import SwiftUI
import Charts
import StopDoingListKit

// 关联分析视图
struct XMomentCorrelationView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var selectedAnalysis: CorrelationType = .timeInterruption
    @State private var animateCharts = false

    // 关联分析类型
    enum CorrelationType: String, CaseIterable, Identifiable {
        // Use keys for raw values
        case timeInterruption = "view.correlation.analysisType.timeInterruption"
        case categoryDuration = "view.correlation.analysisType.categoryDuration"
        case moodDuration = "view.correlation.analysisType.moodDuration"
        case weekdayFocus = "view.correlation.analysisType.weekdayFocus"
        case customCorrelation = "view.correlation.analysisType.customCorrelation"

        var id: String { self.rawValue }

        // Computed property for localized name
        var localizedName: String {
            NSLocalizedString(self.rawValue, comment: "Correlation analysis type name")
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // 分析类型选择器
            Picker(NSLocalizedString("view.correlation.picker.title", comment: "Picker title for correlation analysis type"), selection: $selectedAnalysis) {
                ForEach(CorrelationType.allCases) { type in
                    Text(type.localizedName).tag(type) // Use localizedName
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()

            // 关联分析内容
            ScrollView {
                VStack(spacing: 16) {
                    correlationHeaderView

                    // 根据选择的分析类型显示对应的图表
                    switch selectedAnalysis {
                    case .timeInterruption:
                        timeInterruptionAnalysis
                    case .categoryDuration:
                        categoryDurationAnalysis
                    case .moodDuration:
                        moodDurationAnalysis
                    case .weekdayFocus:
                        weekdayFocusAnalysis
                    case .customCorrelation:
                        customCorrelationAnalysis
                    }

                    // 分析洞察
                    correlationInsightsSection // Renamed from correlationInsights to avoid conflict
                }
                .padding(.horizontal)
                .padding(.bottom)
                .animation(.easeInOut, value: selectedAnalysis)
            }
        }
        .onAppear {
            // 添加延迟以便视图完成加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    animateCharts = true
                }
            }
        }
    }

    // 关联分析头部
    private var correlationHeaderView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("view.correlation.header.title", comment: "Correlation analysis view header title"))
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(NSLocalizedString("view.correlation.header.description", comment: "Correlation analysis view header description"))
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.bottom, 8)
    }

    // MARK: - 时段-中断率分析
    private var timeInterruptionAnalysis: some View {
        let data = getTimeInterruptionData()
        let avgRate = getAverageInterruptionRate()

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.timeInterruption.title", comment: "Time-Interruption analysis chart title"))
                .font(.headline)
                .foregroundColor(.primary)

            if !data.isEmpty {
                Chart {
                    ForEach(data) { item in
                        BarMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.timeSlot", comment: "Chart label: Time Slot"), item.timeSlot), // Time slot is already localized
                            y: .value(NSLocalizedString("view.correlation.chartLabel.interruptionRate", comment: "Chart label: Interruption Rate"), item.interruptionRate)
                        )
                        .foregroundStyle(
                            LinearGradient(colors: [.blue, .purple], startPoint: .bottom, endPoint: .top)
                        )
                        .annotation(position: .top) {
                            Text(String(format: NSLocalizedString("view.correlation.percentageFormat.int", comment: "Percentage format string with integer. Parameter: rate * 100"), item.interruptionRate * 100))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    RuleMark(y: .value(NSLocalizedString("view.correlation.chartLabel.averageInterruptionRate", comment: "Chart label: Average Interruption Rate"), avgRate))
                        .foregroundStyle(.red)
                        .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                        .annotation(position: .top, alignment: .trailing) {
                            Text(NSLocalizedString("view.correlation.chartLabel.average", comment: "Chart annotation: Average"))
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.trailing, 4)
                        }
                }
                .chartXScale(domain: .automatic, range: .plotDimension(padding: 40))
                .chartYScale(domain: 0...1)
                .chartYAxis {
                    AxisMarks(position: .leading, values: [0, 0.25, 0.5, 0.75, 1]) { value in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel {
                            if let doubleValue = value.as(Double.self) {
                                Text(String(format: NSLocalizedString("view.correlation.percentageFormat.axis", comment: "Percentage format string for axis label. Parameter: Int(value * 100)"), Int(doubleValue * 100)))
                            }
                        }
                    }
                }
                .frame(height: 250)
                .opacity(animateCharts ? 1 : 0)
                .scaleEffect(animateCharts ? 1 : 0.8)

                timeInterruptionInsightsView // Renamed
            } else {
                noDataView
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // 时段中断率洞察
    private var timeInterruptionInsightsView: some View { // Renamed
        let data = getTimeInterruptionData()
        let bestTimeSlot = data.min(by: { $0.interruptionRate < $1.interruptionRate })
        let worstTimeSlot = data.max(by: { $0.interruptionRate < $1.interruptionRate })

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.insights.title", comment: "Insights section title"))
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.top, 4)

            VStack(alignment: .leading, spacing: 8) {
                if let best = bestTimeSlot {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)

                        Text(.init(String(format: NSLocalizedString("view.correlation.timeInterruption.insight.best", comment: "Insight text for best focus time slot. Parameters: time slot (string), rate percentage (integer)"), best.timeSlot, Int(best.interruptionRate * 100))))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                if let worst = worstTimeSlot {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "exclamationmark.circle.fill")
                            .foregroundColor(.orange)

                        Text(.init(String(format: NSLocalizedString("view.correlation.timeInterruption.insight.worst", comment: "Insight text for worst focus time slot. Parameters: time slot (string), rate percentage (integer)"), worst.timeSlot, Int(worst.interruptionRate * 100))))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)

                    Text(.init(NSLocalizedString("view.correlation.timeInterruption.insight.suggestion", comment: "Suggestion text for time slot insights")))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
            }
        }
        .opacity(animateCharts ? 1 : 0)
        .offset(y: animateCharts ? 0 : 20)
    }

    // MARK: - 分类-时长分析
    private var categoryDurationAnalysis: some View {
        let data = getCategoryDurationData()
        let unclassifiedKey = "common.category.unclassified"

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.categoryDuration.title", comment: "Category-Duration analysis chart title"))
                .font(.headline)
                .foregroundColor(.primary)

            if !data.isEmpty {
                Chart {
                    ForEach(data) { item in
                        let categoryName = item.category == unclassifiedKey ? NSLocalizedString(unclassifiedKey, comment: "Unclassified category name") : item.category
                        PointMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.avgDurationMinutes", comment: "Chart label: Average Duration (Minutes)"), item.avgDuration / 60),
                            y: .value(NSLocalizedString("view.correlation.chartLabel.category", comment: "Chart label: Category"), NSLocalizedString(categoryName, comment: "Category name"))
                        )
                        .foregroundStyle(Color(hue: 0.3 - min(item.interruptionRate, 1.0) * 0.3, saturation: 0.8, brightness: 0.8))
                        .symbolSize(CGFloat(min(max(item.count * 5, 20), 200)))
                    }
                }
                .chartLegend(position: .bottom) {
                    VStack(alignment: .leading) {
                        HStack {
                            Text(NSLocalizedString("view.correlation.categoryDuration.legend.size", comment: "Chart legend: Point size explanation"))
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            HStack(spacing: 4) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 8, height: 8)
                                Text(NSLocalizedString("view.correlation.categoryDuration.legend.lowInterruption", comment: "Chart legend: Low interruption rate color"))
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 8, height: 8)
                                    .padding(.leading, 4)
                                Text(NSLocalizedString("view.correlation.categoryDuration.legend.highInterruption", comment: "Chart legend: High interruption rate color"))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .frame(height: 300)
                .opacity(animateCharts ? 1 : 0)
                .scaleEffect(animateCharts ? 1 : 0.8)

                categoryDurationInsightsView
            } else {
                noDataView
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // 分类时长洞察
    private var categoryDurationInsightsView: some View {
        let data = getCategoryDurationData()
        let longestCategory = data.max(by: { $0.avgDuration < $1.avgDuration })
        let lowestInterruptionCategory = data.min(by: { $0.interruptionRate < $1.interruptionRate })
        let unclassifiedKey = "common.category.unclassified"

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.insights.title", comment: "Insights section title"))
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.top, 4)

            VStack(alignment: .leading, spacing: 8) {
                if let longest = longestCategory {
                    let categoryName = longest.category == unclassifiedKey ? NSLocalizedString(unclassifiedKey, comment: "Unclassified category name") : longest.category
                    let durationString = formatDuration(longest.avgDuration)
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "clock.fill")
                            .foregroundColor(.blue)

                        Text(.init(String(format: NSLocalizedString("view.correlation.categoryDuration.insight.longest", comment: "Insight text for longest duration category. Parameters: category name (string), formatted duration (string)"), NSLocalizedString(categoryName, comment: "Category name"), durationString)))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                if let lowest = lowestInterruptionCategory {
                    let categoryName = lowest.category == unclassifiedKey ? NSLocalizedString(unclassifiedKey, comment: "Unclassified category name") : lowest.category
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "hand.raised.fill")
                            .foregroundColor(.green)

                        Text(.init(String(format: NSLocalizedString("view.correlation.categoryDuration.insight.lowestInterruption", comment: "Insight text for lowest interruption category. Parameters: category name (string), rate percentage (integer)"), NSLocalizedString(categoryName, comment: "Category name"), Int(lowest.interruptionRate * 100))))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)

                    Text(.init(NSLocalizedString("view.correlation.categoryDuration.insight.suggestion", comment: "Suggestion text for category duration insights")))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
            }
        }
        .opacity(animateCharts ? 1 : 0)
        .offset(y: animateCharts ? 0 : 20)
    }

    // MARK: - 心情-时长分析
    private var moodDurationAnalysis: some View {
        let data = getMoodDurationData()

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.moodDuration.title", comment: "Mood-Duration analysis chart title"))
                .font(.headline)
                .foregroundColor(.primary)

            if !data.isEmpty {
                Chart {
                    ForEach(data) { item in
                        BarMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.avgDurationMinutes", comment: "Chart label: Average Duration (Minutes)"), item.avgDuration / 60),
                            y: .value(NSLocalizedString("view.correlation.chartLabel.mood", comment: "Chart label: Mood"), NSLocalizedString(item.mood, comment: "Mood name"))
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.correlation.chartLabel.moodCategory", comment: "Chart label: Mood Category"), NSLocalizedString(item.mood, comment: "Mood name")))
                        .annotation(position: .trailing) {
                            Text(formatDuration(item.avgDuration))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 250)
                .opacity(animateCharts ? 1 : 0)
                .scaleEffect(animateCharts ? 1 : 0.8)

                Text(String(format: NSLocalizedString("view.correlation.moodDuration.sampleCount", comment: "Sample count text. Parameter: count"), String(data.reduce(0) { $0 + $1.count })))
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .trailing)

                moodDurationInsightsView
            } else {
                noDataView
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // 心情时长洞察
    private var moodDurationInsightsView: some View {
        let data = getMoodDurationData()
        let bestMood = data.max(by: { $0.avgDuration < $1.avgDuration })
        let worstMood = data.min(by: { $0.avgDuration < $1.avgDuration })

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.insights.title", comment: "Insights section title"))
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.top, 4)

            VStack(alignment: .leading, spacing: 8) {
                if let best = bestMood, let worst = worstMood, best.mood != worst.mood {
                    let bestDuration = formatDuration(best.avgDuration)
                    let worstDuration = formatDuration(worst.avgDuration)
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "heart.fill")
                            .foregroundColor(.pink)

                        Text(.init(String(format: NSLocalizedString("view.correlation.moodDuration.insight.comparison", comment: "Insight comparing best and worst mood for duration. Parameters: best mood (string), best duration (string), worst mood (string), worst duration (string)"), NSLocalizedString(best.mood, comment: "Mood name"), bestDuration, NSLocalizedString(worst.mood, comment: "Mood name"), worstDuration)))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)

                    Text(.init(NSLocalizedString("view.correlation.moodDuration.insight.suggestion", comment: "Suggestion text for mood duration insights")))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
            }
        }
        .opacity(animateCharts ? 1 : 0)
        .offset(y: animateCharts ? 0 : 20)
    }

    // MARK: - 星期-专注分析
    private var weekdayFocusAnalysis: some View {
        let countData = getWeekdayFocusCount()
        let durationData = getWeekdayFocusDuration()
        let avgDuration = getWeeklyAverageDuration()

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.weekdayFocus.title", comment: "Weekday-Focus analysis chart title"))
                .font(.headline)
                .foregroundColor(.primary)

            if !countData.isEmpty && !durationData.isEmpty {
                Text(NSLocalizedString("view.correlation.weekdayFocus.countSubtitle", comment: "Focus count distribution subtitle"))
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Chart {
                    ForEach(countData) { item in
                        BarMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.weekday", comment: "Chart label: Weekday"), item.weekday), // Weekday is already localized
                            y: .value(NSLocalizedString("view.correlation.chartLabel.focusCount", comment: "Chart label: Focus Count"), item.count)
                        )
                        .foregroundStyle(.blue.gradient)
                        .annotation(position: .top) {
                            Text("\(item.count)") // Keep raw count
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 150)
                .opacity(animateCharts ? 1 : 0)
                .scaleEffect(animateCharts ? 1 : 0.8)

                Text(NSLocalizedString("view.correlation.weekdayFocus.durationSubtitle", comment: "Average focus duration distribution subtitle"))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.top, 8)

                Chart {
                    ForEach(durationData) { item in
                        LineMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.weekday", comment: "Chart label: Weekday"), item.weekday), // Weekday is already localized
                            y: .value(NSLocalizedString("view.correlation.chartLabel.avgDurationMinutes", comment: "Chart label: Average Duration (Minutes)"), item.avgDuration / 60)
                        )
                        .foregroundStyle(.purple.gradient)
                        .symbol {
                            Circle()
                                .fill(.purple)
                                .frame(width: 8, height: 8)
                        }
                        .interpolationMethod(.catmullRom)

                        AreaMark(
                            x: .value(NSLocalizedString("view.correlation.chartLabel.weekday", comment: "Chart label: Weekday"), item.weekday),
                            y: .value(NSLocalizedString("view.correlation.chartLabel.avgDurationMinutes", comment: "Chart label: Average Duration (Minutes)"), item.avgDuration / 60)
                        )
                        .foregroundStyle(.purple.opacity(0.1).gradient)
                        .interpolationMethod(.catmullRom)
                    }

                    RuleMark(y: .value(NSLocalizedString("view.correlation.chartLabel.weeklyAverage", comment: "Chart label: Weekly Average"), avgDuration / 60))
                        .foregroundStyle(.red)
                        .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                        .annotation(position: .top, alignment: .trailing) {
                            Text(NSLocalizedString("view.correlation.chartLabel.weeklyAverage.short", comment: "Chart annotation: Weekly Average (short)"))
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.trailing, 4)
                        }
                }
                .chartXScale(domain: .automatic, range: .plotDimension(padding: 40))
                .frame(height: 150)
                .opacity(animateCharts ? 1 : 0)
                .scaleEffect(animateCharts ? 1 : 0.8)

                weekdayFocusInsightsView
            } else {
                noDataView
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // 星期专注洞察
    private var weekdayFocusInsightsView: some View {
        let countData = getWeekdayFocusCount()
        let durationData = getWeekdayFocusDuration()

        let mostFrequentDay = countData.max(by: { $0.count < $1.count })
        let longestDurationDay = durationData.max(by: { $0.avgDuration < $1.avgDuration })

        return VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.insights.title", comment: "Insights section title"))
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.top, 4)

            VStack(alignment: .leading, spacing: 8) {
                if let mostFrequent = mostFrequentDay {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "number.circle.fill")
                            .foregroundColor(.blue)

                        Text(.init(String(format: NSLocalizedString("view.correlation.weekdayFocus.insight.mostFrequent", comment: "Insight text for most frequent focus day. Parameters: weekday name (string), count (integer)"), mostFrequent.weekday, mostFrequent.count)))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                if let longest = longestDurationDay {
                    let durationString = formatDuration(longest.avgDuration)
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "clock.fill")
                            .foregroundColor(.purple)

                        Text(.init(String(format: NSLocalizedString("view.correlation.weekdayFocus.insight.longestDuration", comment: "Insight text for longest average duration day. Parameters: weekday name (string), formatted duration (string)"), longest.weekday, durationString)))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }

                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)

                    Text(.init(NSLocalizedString("view.correlation.weekdayFocus.insight.suggestion", comment: "Suggestion text for weekday focus insights")))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
            }
        }
        .opacity(animateCharts ? 1 : 0)
        .offset(y: animateCharts ? 0 : 20)
    }

    // MARK: - 自定义关联分析
    private var customCorrelationAnalysis: some View {
        VStack(alignment: .center, spacing: 20) {
            Image(systemName: "chart.bar.xaxis")
                .font(.system(size: 36))
                .foregroundColor(.accentColor)

            Text(NSLocalizedString("view.correlation.custom.title", comment: "Custom correlation analysis title"))
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(NSLocalizedString("view.correlation.custom.description", comment: "Custom correlation analysis description"))
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding()

            Text(NSLocalizedString("view.correlation.custom.feedbackPrompt", comment: "Custom correlation analysis feedback prompt"))
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(40)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        .opacity(animateCharts ? 1 : 0)
        .scaleEffect(animateCharts ? 1 : 0.9)
    }

    // 关联分析洞察 - Section View
    private var correlationInsightsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("view.correlation.generalInsights.title", comment: "General correlation insights title"))
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                HStack(alignment: .top, spacing: 12) {
                    Image(systemName: "brain")
                        .foregroundColor(.blue)
                        .frame(width: 24)

                    Text(NSLocalizedString("view.correlation.generalInsights.point1", comment: "General insight point 1"))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }

                HStack(alignment: .top, spacing: 12) {
                    Image(systemName: "checklist")
                        .foregroundColor(.green)
                        .frame(width: 24)

                    Text(NSLocalizedString("view.correlation.generalInsights.point2", comment: "General insight point 2"))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }

                HStack(alignment: .top, spacing: 12) {
                    Image(systemName: "chart.xyaxis.line")
                        .foregroundColor(.purple)
                        .frame(width: 24)

                    Text(NSLocalizedString("view.correlation.generalInsights.point3", comment: "General insight point 3"))
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        .padding(.top, 10)
        .opacity(animateCharts ? 1 : 0)
        .offset(y: animateCharts ? 0 : 20)
    }

    // Placeholder for No Data
    private var noDataView: some View {
        Text(NSLocalizedString("view.correlation.noDataAvailable", comment: "Message when not enough data for correlation analysis"))
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity, alignment: .center)
            .padding()
    }

    // MARK: - 数据处理方法

    // 获取时段-中断率数据 - Returns localized time slot names
    private func getTimeInterruptionData() -> [TimeInterruptionData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let formatter = DateFormatter()
        formatter.dateFormat = "HH"

        // 定义时间段键名
        let slotKeys = [
            "view.xmomentInsights.time.timeSlot.morning", // "凌晨 (0-6点)"
            "view.xmomentInsights.time.timeSlot.forenoon", // "上午 (6-12点)"
            "view.xmomentInsights.time.timeSlot.afternoon", // "下午 (12-18点)"
            "view.xmomentInsights.time.timeSlot.evening" // "晚上 (18-24点)"
        ]

        var slotData: [String: (total: Int, interrupted: Int)] = [:]

        // 初始化所有时段
        for key in slotKeys {
            slotData[key] = (total: 0, interrupted: 0)
        }

        // 统计各时段的数据
        for session in sessions {
            let hour = Int(formatter.string(from: session.startTime)) ?? 0

            let slotKey: String
            if hour >= 0 && hour < 6 {
                slotKey = slotKeys[0]
            } else if hour >= 6 && hour < 12 {
                slotKey = slotKeys[1]
            } else if hour >= 12 && hour < 18 {
                slotKey = slotKeys[2]
            } else {
                slotKey = slotKeys[3]
            }

            var data = slotData[slotKey] ?? (total: 0, interrupted: 0)
            data.total += 1
            if session.isInterrupted {
                data.interrupted += 1
            }
            slotData[slotKey] = data
        }

        // 转换为图表数据
        var result: [TimeInterruptionData] = []

        for (key, data) in slotData {
            if data.total >= 3 { // 至少有3个样本才有意义
                let rate = data.total > 0 ? Double(data.interrupted) / Double(data.total) : 0
                result.append(TimeInterruptionData(
                    timeSlot: NSLocalizedString(key, comment: "Time slot name"),
                    timeSlotKey: key,
                    interruptionRate: rate,
                    totalSessions: data.total,
                    interruptedSessions: data.interrupted
                ))
            }
        }

        // 按时段顺序排序 (using keys for stable order)
        return result.sorted { slotKeys.firstIndex(of: $0.timeSlotKey)! < slotKeys.firstIndex(of: $1.timeSlotKey)! }
    }

    // 获取平均中断率
    private func getAverageInterruptionRate() -> Double {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let interruptedCount = sessions.filter { $0.isInterrupted }.count
        return sessions.count > 0 ? Double(interruptedCount) / Double(sessions.count) : 0
    }

    // 获取分类-时长数据 - Returns category name (possibly unlocalized key)
    private func getCategoryDurationData() -> [CategoryDurationData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified"

        var categoryData: [String: (totalDuration: TimeInterval, count: Int, interrupted: Int)] = [:]

        for session in sessions {
            // Use the key or the user-defined category name. Localization happens during display if needed.
            let categoryKeyOrName = session.category ?? unclassifiedKey

            var data = categoryData[categoryKeyOrName] ?? (totalDuration: 0, count: 0, interrupted: 0)
            data.totalDuration += session.duration
            data.count += 1
            if session.isInterrupted {
                data.interrupted += 1
            }
            categoryData[categoryKeyOrName] = data
        }

        // 转换为图表数据
        var result: [CategoryDurationData] = []

        for (categoryKeyOrName, data) in categoryData {
            if data.count >= 3 { // 至少有3个样本才有意义
                // 数据清理和验证
                 let safeCategory = categoryKeyOrName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? unclassifiedKey : categoryKeyOrName

                // 确保平均时长有效
                let avgDuration = data.count > 0 ? max(data.totalDuration / Double(data.count), 0) : 0

                // 确保中断率在有效范围内 (0.0-1.0)
                let interruptionRate = data.count > 0 ? max(min(Double(data.interrupted) / Double(data.count), 1.0), 0.0) : 0.0

                // 确保计数是正整数
                let safeCount = max(data.count, 1)

                result.append(CategoryDurationData(
                    category: safeCategory, // Store key or name
                    avgDuration: avgDuration,
                    interruptionRate: interruptionRate,
                    count: safeCount
                ))
            }
        }

        // 按平均时长排序
        return result.sorted { $0.avgDuration > $1.avgDuration }
    }

    // 获取心情-时长数据 - Returns mood string (user input)
    private func getMoodDurationData() -> [MoodDurationData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.mood != nil && !$0.mood!.isEmpty }

        var moodData: [String: (totalDuration: TimeInterval, count: Int)] = [:]

        for session in sessions {
            if let mood = session.mood, !mood.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                var data = moodData[mood] ?? (totalDuration: 0, count: 0)
                data.totalDuration += session.duration
                data.count += 1
                moodData[mood] = data
            }
        }

        // 转换为图表数据
        var result: [MoodDurationData] = []

        for (mood, data) in moodData {
            if data.count >= 3 { // 至少有3个样本才有意义
                // 确保平均时长有效
                let avgDuration = data.count > 0 ? max(data.totalDuration / Double(data.count), 0) : 0

                // 确保计数是正整数
                let safeCount = max(data.count, 1)

                result.append(MoodDurationData(
                    mood: mood,
                    avgDuration: avgDuration,
                    count: safeCount
                ))
            }
        }

        // 按平均时长排序
        return result.sorted { $0.avgDuration > $1.avgDuration }
    }

    // 获取星期-专注次数数据 - Returns localized weekday names
    private func getWeekdayFocusCount() -> [WeekdayFocusData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        // Use keys for weekdays
        let weekdayKeys = [
            "common.weekday.sun", "common.weekday.mon", "common.weekday.tue",
            "common.weekday.wed", "common.weekday.thu", "common.weekday.fri", "common.weekday.sat"
        ]

        var weekdayCounts: [Int: Int] = [:]

        // 初始化所有星期
        for i in 1...7 {
            weekdayCounts[i] = 0
        }

        // 统计各星期的专注次数
        for session in sessions {
            let weekday = calendar.component(.weekday, from: session.startTime)
            // 确保weekday在有效范围内(1-7)
            if weekday >= 1 && weekday <= 7 {
                weekdayCounts[weekday, default: 0] += 1
            }
        }

        // 转换为图表数据
        var result: [WeekdayFocusData] = []

        for (weekday, count) in weekdayCounts {
            // 确保weekday在有效范围内(1-7)
            if weekday >= 1 && weekday <= 7 {
                // 确保计数是非负数
                let safeCount = max(count, 0)
                let weekdayKey = weekdayKeys[weekday - 1]

                result.append(WeekdayFocusData(
                    weekday: NSLocalizedString(weekdayKey, comment: "Weekday name"),
                    weekdayIndex: weekday,
                    count: safeCount,
                    weekdayKey: weekdayKey // Store key for sorting
                ))
            }
        }

        // 按星期顺序排序
        return result.sorted { $0.weekdayIndex < $1.weekdayIndex }
    }

    // 获取星期-专注时长数据 - Returns localized weekday names
    private func getWeekdayFocusDuration() -> [WeekdayDurationData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        // Use keys for weekdays
        let weekdayKeys = [
            "common.weekday.sun", "common.weekday.mon", "common.weekday.tue",
            "common.weekday.wed", "common.weekday.thu", "common.weekday.fri", "common.weekday.sat"
        ]

        var weekdayData: [Int: (totalDuration: TimeInterval, count: Int)] = [:]

        // 初始化所有星期
        for i in 1...7 {
            weekdayData[i] = (totalDuration: 0, count: 0)
        }

        // 统计各星期的专注数据
        for session in sessions {
            let weekday = calendar.component(.weekday, from: session.startTime)
            // 确保weekday在有效范围内(1-7)
            if weekday >= 1 && weekday <= 7 {
                var data = weekdayData[weekday] ?? (totalDuration: 0, count: 0)
                data.totalDuration += session.duration
                data.count += 1
                weekdayData[weekday] = data
            }
        }

        // 转换为图表数据
        var result: [WeekdayDurationData] = []

        for (weekday, data) in weekdayData {
            // 确保平均时长有效
            let avgDuration = data.count > 0 ? max(data.totalDuration / Double(data.count), 0) : 0

            // 确保计数是非负数
            let safeCount = max(data.count, 0)

            // 确保weekday在有效范围内(1-7)
            if weekday >= 1 && weekday <= 7 {
                 let weekdayKey = weekdayKeys[weekday - 1]
                result.append(WeekdayDurationData(
                    weekday: NSLocalizedString(weekdayKey, comment: "Weekday name"),
                    weekdayIndex: weekday,
                    avgDuration: avgDuration,
                    count: safeCount,
                    weekdayKey: weekdayKey // Store key for sorting
                ))
            }
        }

        // 按星期顺序排序
        return result.sorted { $0.weekdayIndex < $1.weekdayIndex }
    }

    // 获取周平均专注时长
    private func getWeeklyAverageDuration() -> TimeInterval {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let totalDuration = sessions.reduce(0) { $0 + $1.duration }
        return sessions.count > 0 ? totalDuration / Double(sessions.count) : 0
    }

    // MARK: - 辅助方法

    // 格式化持续时间 - Use localized formats
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            // "%@小时%@分钟"
            return String(format: NSLocalizedString("common.durationFormat.hoursMinutesVar", comment: "Duration format: hours and minutes. Parameters: hour count (string), minute count (string)"), String(hours), String(minutes))
        } else {
            // "%@分钟"
            return String(format: NSLocalizedString("common.durationFormat.minutesVar", comment: "Duration format: minutes. Parameter: minute count (string)"), String(minutes))
        }
    }
}

// 数据模型 - Modified to include keys where appropriate
struct TimeInterruptionData: Identifiable {
    var id = UUID()
    var timeSlot: String // Already localized in data generation
    var timeSlotKey: String // Original key for sorting
    var interruptionRate: Double
    var totalSessions: Int
    var interruptedSessions: Int
}

struct CategoryDurationData: Identifiable {
    var id = UUID()
    var category: String // Key or user-defined name
    var avgDuration: TimeInterval
    var interruptionRate: Double
    var count: Int
}

struct MoodDurationData: Identifiable {
    var id = UUID()
    var mood: String // User input, assumed localized or doesn't need it
    var avgDuration: TimeInterval
    var count: Int
}

struct WeekdayFocusData: Identifiable {
    var id = UUID()
    var weekday: String // Localized name
    var weekdayIndex: Int
    var count: Int
    var weekdayKey: String // Original key for sorting
}

struct WeekdayDurationData: Identifiable {
    var id = UUID()
    var weekday: String // Localized name
    var weekdayIndex: Int
    var avgDuration: TimeInterval
    var count: Int
    var weekdayKey: String // Original key for sorting
}

// 预览
struct XMomentCorrelationView_Previews: PreviewProvider {
    static var previews: some View {
        XMomentCorrelationView()
            .environmentObject(DataStore.shared)
            // .environment(\\.locale, .init(identifier: "zh-Hans"))
    }
}