import SwiftUI
import Charts
import StopDoingListKit

struct XMomentInsightsView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var selectedInsight: InsightCategory = .time
    @State private var animateInsights = false

    enum InsightCategory: String, CaseIterable {
        case time = "view.xmomentInsights.categoryEnum.time" // "时间"
        case category = "view.xmomentInsights.categoryEnum.category" // "分类"
        case mood = "view.xmomentInsights.categoryEnum.mood" // "心情"
        case xu = "XU" // No localization needed for "XU"
        case records = "view.xmomentInsights.categoryEnum.records" // "记录"

        var localized: String {
            switch self {
            case .time, .category, .mood, .records:
                return NSLocalizedString(self.rawValue, comment: "Insight category")
            case .xu:
                return self.rawValue // Keep "XU" as is
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // 分类选择器
            Picker(NSLocalizedString("view.xmomentInsights.picker.title", comment: "Picker title for insight category"), selection: $selectedInsight) {
                ForEach(InsightCategory.allCases, id: \.rawValue) { category in
                    Text(category.localized).tag(category)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()

            // 内容区域
            ScrollView {
                VStack(spacing: 20) {
                    switch selectedInsight {
                    case .time:
                        timeInsights
                    case .category:
                        categoryInsights
                    case .mood:
                        moodInsights
                    case .xu:
                        xuInsights
                    case .records:
                        recordInsights
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
                .animation(.easeInOut, value: selectedInsight)
            }
        }
        .onAppear {
            // 添加延迟以便视图完成加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    animateInsights = true
                }
            }
        }
    }

    // MARK: - 时间洞察
    private var timeInsights: some View {
        VStack(spacing: 16) {
            insightHeader(title: NSLocalizedString("view.xmomentInsights.time.header.title", comment: "Header title for time insights"), icon: "clock.fill")

            // 获取时间相关数据
            Group {
                // 专注次数最多的日期
                if let mostFocusedDay = getMostFocusedDay() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.time.mostFocusedDay.title", comment: "Insight card title: Most focused day"),
                        value: formattedDate(mostFocusedDay.date),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.time.mostFocusedDay.detailFormat", comment: "Insight card detail: Count of focus sessions on the most focused day. Parameter: count"), String(mostFocusedDay.count)),
                        icon: "calendar.badge.clock",
                        iconColor: .blue
                    )
                }

                // 专注时长最长的日期
                if let longestDurationDay = getLongestDurationDay() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.time.longestDurationDay.title", comment: "Insight card title: Longest duration day"),
                        value: formattedDate(longestDurationDay.date),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.time.longestDurationDay.detailFormat", comment: "Insight card detail: Total duration on the longest duration day. Parameter: duration string"), formatDuration(longestDurationDay.duration)),
                        icon: "clock.badge.checkmark.fill",
                        iconColor: .green
                    )
                }

                // 专注密度最高的时段
                if let densestTimeSlot = getDensestTimeSlot() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.time.densestTimeSlot.title", comment: "Insight card title: Densest time slot"),
                        value: densestTimeSlot.slot, // Slot string is already localized in getDensestTimeSlot
                        detail: String(format: NSLocalizedString("view.xmomentInsights.time.densestTimeSlot.detailFormat", comment: "Insight card detail: Count of focus sessions in the densest time slot. Parameter: count"), String(densestTimeSlot.count)),
                        icon: "chart.bar.fill",
                        iconColor: .purple
                    )
                }

                // 专注时长最长的时段
                if let longestTimeSlot = getLongestTimeSlot() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.time.longestTimeSlot.title", comment: "Insight card title: Longest duration time slot"),
                        value: longestTimeSlot.slot, // Slot string is already localized in getLongestTimeSlot
                        detail: String(format: NSLocalizedString("view.xmomentInsights.time.longestTimeSlot.detailFormat", comment: "Insight card detail: Total duration in the longest duration time slot. Parameter: duration string"), formatDuration(longestTimeSlot.duration)),
                        icon: "clock.fill",
                        iconColor: .orange
                    )
                }

                // 最近一周无专注的日期
                let noFocusDays = getRecentNoFocusDays(days: 7)
                if !noFocusDays.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "calendar.badge.exclamationmark")
                                .foregroundColor(.red)
                            Text(NSLocalizedString("view.xmomentInsights.time.noFocusDays.title", comment: "Title for section showing days with no focus recently"))
                                .font(.headline)
                                .foregroundColor(.primary)
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(noFocusDays, id: \.self) { date in
                                // The date format itself is handled by formattedDate, but the bullet point needs localization if desired.
                                // Keeping it simple for now, the date format string `MM月dd日` will be localized later.
                                Text("• \(formattedDate(date))")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.leading, 8)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                }
            }
            .opacity(animateInsights ? 1 : 0)
            .offset(y: animateInsights ? 0 : 20)
        }
    }

    // MARK: - 分类洞察
    private var categoryInsights: some View {
        VStack(spacing: 16) {
            insightHeader(title: NSLocalizedString("view.xmomentInsights.category.header.title", comment: "Header title for category insights"), icon: "tag.fill")

            // 获取分类相关数据
            Group {
                // 使用最多的分类
                if let mostUsedCategory = getMostUsedCategory() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.category.mostUsed.title", comment: "Insight card title: Most used category"),
                        value: NSLocalizedString(mostUsedCategory.category, comment: "Category name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.category.mostUsed.detailFormat", comment: "Insight card detail: Count of focus sessions for the most used category. Parameter: count"), String(mostUsedCategory.count)),
                        icon: "tag.fill",
                        iconColor: .blue
                    )
                }

                // 专注时长最长的分类
                if let longestDurationCategory = getLongestDurationCategory() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.category.longestDuration.title", comment: "Insight card title: Longest duration category"),
                        value: NSLocalizedString(longestDurationCategory.category, comment: "Category name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.category.longestDuration.detailFormat", comment: "Insight card detail: Total duration for the longest duration category. Parameter: duration string"), formatDuration(longestDurationCategory.duration)),
                        icon: "clock.fill",
                        iconColor: .green
                    )
                }

                // 平均专注时长最长的分类
                if let highestAvgDurationCategory = getHighestAvgDurationCategory() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.category.highestAvgDuration.title", comment: "Insight card title: Highest average duration category"),
                        value: NSLocalizedString(highestAvgDurationCategory.category, comment: "Category name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.category.highestAvgDuration.detailFormat", comment: "Insight card detail: Average duration for the highest average duration category. Parameter: duration string"), formatDuration(highestAvgDurationCategory.avgDuration)),
                        icon: "chart.bar.fill",
                        iconColor: .purple
                    )
                }

                // 中断率最低的分类
                if let lowestInterruptionCategory = getLowestInterruptionCategory() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.category.lowestInterruption.title", comment: "Insight card title: Lowest interruption rate category"),
                        value: NSLocalizedString(lowestInterruptionCategory.category, comment: "Category name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.category.lowestInterruption.detailFormat", comment: "Insight card detail: Interruption rate percentage. Parameter: rate percentage"), lowestInterruptionCategory.rate * 100),
                        icon: "hand.raised.fill",
                        iconColor: .orange
                    )
                }
            }
            .opacity(animateInsights ? 1 : 0)
            .offset(y: animateInsights ? 0 : 20)
        }
    }

    // MARK: - 心情洞察
    private var moodInsights: some View {
        VStack(spacing: 16) {
            insightHeader(title: NSLocalizedString("view.xmomentInsights.mood.header.title", comment: "Header title for mood insights"), icon: "heart.fill")

            // 获取心情相关数据
            Group {
                // 最常见的心情
                if let mostCommonMood = getMostCommonMood() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.mood.mostCommon.title", comment: "Insight card title: Most common mood"),
                        value: NSLocalizedString(mostCommonMood.mood, comment: "Mood name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.mood.mostCommon.detailFormat", comment: "Insight card detail: Count of occurrences for the most common mood. Parameter: count"), String(mostCommonMood.count)),
                        icon: "face.smiling.fill",
                        iconColor: .pink
                    )
                }

                // 专注时长最长的心情状态
                if let longestDurationMood = getLongestDurationMood() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.mood.longestDuration.title", comment: "Insight card title: Longest duration mood"),
                        value: NSLocalizedString(longestDurationMood.mood, comment: "Mood name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.mood.longestDuration.detailFormat", comment: "Insight card detail: Total duration for the longest duration mood. Parameter: duration string"), formatDuration(longestDurationMood.duration)),
                        icon: "clock.fill",
                        iconColor: .green
                    )
                }

                // 心情分布图
                moodDistributionChart
            }
            .opacity(animateInsights ? 1 : 0)
            .offset(y: animateInsights ? 0 : 20)
        }
    }

    // 心情分布图
    private var moodDistributionChart: some View {
        let moodData = getMoodDistribution()

        return VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("view.xmomentInsights.mood.distributionChart.title", comment: "Title for mood distribution chart"))
                .font(.headline)
                .foregroundColor(.primary)

            if !moodData.isEmpty {
                Chart {
                    ForEach(moodData) { item in
                        SectorMark(
                            angle: .value(NSLocalizedString("view.xmomentInsights.mood.distributionChart.angleValueLabel", comment: "Chart label for angle value (count)"), item.count),
                            innerRadius: .ratio(0.6),
                            angularInset: 1.5
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.xmomentInsights.mood.distributionChart.categoryValueLabel", comment: "Chart label for category value (mood)"), NSLocalizedString(item.mood, comment: "Mood name")))
                        .annotation(position: .overlay) {
                            if item.count > 0 {
                                Text("\(item.count)")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .fontWeight(.bold)
                            }
                        }
                    }
                }
                .frame(height: 200)
                .padding(.vertical, 10)
            } else {
                Text(NSLocalizedString("view.xmomentInsights.mood.distributionChart.noData", comment: "Text shown when there is no mood data"))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // MARK: - XU洞察
    private var xuInsights: some View {
        VStack(spacing: 16) {
            insightHeader(title: NSLocalizedString("view.xmomentInsights.xu.header.title", comment: "Header title for XU insights"), icon: "sparkles")

            // 获取XU相关数据
            Group {
                // XU收益最高的会话
                if let highestXUSession = getHighestXUSession() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.xu.highestXuSession.title", comment: "Insight card title: Highest XU session"),
                        value: String(format: NSLocalizedString("view.xmomentInsights.xu.highestXuSession.valueFormat", comment: "Insight card value format for highest XU session. Parameter: XU value"), highestXUSession.xu),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.xu.highestXuSession.detailFormat", comment: "Insight card detail format for highest XU session. Parameters: date, duration string"), formattedDate(highestXUSession.date), formatDuration(highestXUSession.duration)),
                        icon: "star.fill",
                        iconColor: .yellow
                    )
                }

                // XU效率最高的会话
                if let highestXUEfficiencySession = getHighestXUEfficiencySession() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.xu.highestEfficiencySession.title", comment: "Insight card title: Highest XU efficiency session"),
                        value: String(format: NSLocalizedString("view.xmomentInsights.xu.highestEfficiencySession.valueFormat", comment: "Insight card value format for highest XU efficiency session (XU per hour). Parameter: efficiency value"), highestXUEfficiencySession.efficiency),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.xu.highestEfficiencySession.detailFormat", comment: "Insight card detail format for highest XU efficiency session. Parameters: date, duration string"), formattedDate(highestXUEfficiencySession.date), formatDuration(highestXUEfficiencySession.duration)),
                        icon: "bolt.fill",
                        iconColor: .orange
                    )
                }

                // XU获取最多的日期
                if let mostXUDay = getMostXUDay() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.xu.mostXuDay.title", comment: "Insight card title: Day with most XU earned"),
                        value: formattedDate(mostXUDay.date),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.xu.mostXuDay.detailFormat", comment: "Insight card detail format for day with most XU. Parameter: total XU value"), mostXUDay.xu),
                        icon: "calendar.badge.clock",
                        iconColor: .green
                    )
                }

                // XU获取最多的分类
                if let mostXUCategory = getMostXUCategory() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.xu.mostXuCategory.title", comment: "Insight card title: Category with most XU earned"),
                        value: NSLocalizedString(mostXUCategory.category, comment: "Category name"),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.xu.mostXuCategory.detailFormat", comment: "Insight card detail format for category with most XU. Parameter: total XU value"), mostXUCategory.xu),
                        icon: "tag.fill",
                        iconColor: .blue
                    )
                }
            }
            .opacity(animateInsights ? 1 : 0)
            .offset(y: animateInsights ? 0 : 20)
        }
    }

    // MARK: - 记录洞察
    private var recordInsights: some View {
        VStack(spacing: 16) {
            insightHeader(title: NSLocalizedString("view.xmomentInsights.records.header.title", comment: "Header title for record insights"), icon: "trophy.fill")

            // 获取记录相关数据
            Group {
                // 单次最长专注时间
                if let longestSession = getLongestSession() {
                    let modeString = longestSession.isFreeMode ?
                        NSLocalizedString("view.xmomentInsights.records.longestSession.mode.free", comment: "Mode display string: Free mode") :
                        NSLocalizedString("view.xmomentInsights.records.longestSession.mode.standard", comment: "Mode display string: Standard mode")
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.records.longestSession.title", comment: "Insight card title: Longest single session"),
                        value: formatDuration(longestSession.duration),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.records.longestSession.detailFormat", comment: "Insight card detail format for longest session. Parameters: date, mode string"), formattedDate(longestSession.date), modeString),
                        icon: "clock.fill",
                        iconColor: .blue
                    )
                }

                // 单次最长超频时间
                if let longestOverclock = getLongestOverclock() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.records.longestOverclock.title", comment: "Insight card title: Longest single overclock"),
                        value: formatDuration(longestOverclock.duration),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.records.longestOverclock.detailFormat", comment: "Insight card detail format for longest overclock. Parameters: date, overclock factor"), formattedDate(longestOverclock.date), longestOverclock.factor),
                        icon: "bolt.fill",
                        iconColor: .orange
                    )
                }

                // 最高连续专注天数
                let bestStreak = dataStore.getXMomentUserStatsUnambiguous().bestStreak
                if bestStreak > 0 {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.records.bestStreak.title", comment: "Insight card title: Best focus streak"),
                        value: String(format: NSLocalizedString("view.xmomentInsights.records.bestStreak.valueFormat", comment: "Insight card value format for best streak. Parameter: number of days"), String(bestStreak)),
                        detail: NSLocalizedString("view.xmomentInsights.records.bestStreak.detail", comment: "Insight card detail for best streak"),
                        icon: "flame.fill",
                        iconColor: .red
                    )
                }

                // 单日最多专注次数
                if let mostSessionsDay = getMostSessionsDay() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.records.mostSessionsDay.title", comment: "Insight card title: Day with most focus sessions"),
                        value: String(format: NSLocalizedString("view.xmomentInsights.records.mostSessionsDay.valueFormat", comment: "Insight card value format for most sessions day. Parameter: count"), String(mostSessionsDay.count)),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.records.mostSessionsDay.detailFormat", comment: "Insight card detail format for most sessions day. Parameter: date"), formattedDate(mostSessionsDay.date)),
                        icon: "number.circle.fill",
                        iconColor: .purple
                    )
                }

                // 单日最长专注时间
                if let longestDayDuration = getLongestDayDuration() {
                    insightCard(
                        title: NSLocalizedString("view.xmomentInsights.records.longestDayDuration.title", comment: "Insight card title: Day with longest total focus duration"),
                        value: formatDuration(longestDayDuration.duration),
                        detail: String(format: NSLocalizedString("view.xmomentInsights.records.longestDayDuration.detailFormat", comment: "Insight card detail format for longest duration day. Parameter: date"), formattedDate(longestDayDuration.date)),
                        icon: "hourglass",
                        iconColor: .green
                    )
                }
            }
            .opacity(animateInsights ? 1 : 0)
            .offset(y: animateInsights ? 0 : 20)
        }
    }

    // MARK: - 共享UI组件
    private func insightHeader(title: String, icon: String) -> some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.accentColor)

            Text(title) // Title is now passed in as localized string
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Spacer()
        }
        .padding(.top, 8)
    }

    private func insightCard(title: String, value: String, detail: String, icon: String, iconColor: Color) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(iconColor)

                Text(title) // Title is now passed in as localized string
                    .font(.headline)
                    .foregroundColor(.primary)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value) // Value might be localized (like formatted strings) or not (like category names)
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(detail) // Detail is now passed in as localized string (often formatted)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.leading, 26)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    // MARK: - 数据处理方法

    // 获取专注次数最多的日期
    private func getMostFocusedDay() -> (date: Date, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        var dateCounts: [Date: Int] = [:]

        for session in sessions {
            let date = calendar.startOfDay(for: session.startTime)
            dateCounts[date, default: 0] += 1
        }

        return dateCounts.max(by: { $0.value < $1.value })
            .map { (date: $0.key, count: $0.value) }
    }

    // 获取专注时长最长的日期
    private func getLongestDurationDay() -> (date: Date, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        var dateDurations: [Date: TimeInterval] = [:]

        for session in sessions {
            let date = calendar.startOfDay(for: session.startTime)
            dateDurations[date, default: 0] += session.duration
        }

        return dateDurations.max(by: { $0.value < $1.value })
            .map { (date: $0.key, duration: $0.value) }
    }

    // 获取专注密度最高的时段 - Returns LOCALIZED slot string
    private func getDensestTimeSlot() -> (slot: String, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let formatter = DateFormatter()
        formatter.dateFormat = "HH"

        // 定义时间段键名
        let slotKeys = [
            "view.xmomentInsights.time.timeSlot.morning", // "凌晨 (0-6点)"
            "view.xmomentInsights.time.timeSlot.forenoon", // "上午 (6-12点)"
            "view.xmomentInsights.time.timeSlot.afternoon", // "下午 (12-18点)"
            "view.xmomentInsights.time.timeSlot.evening" // "晚上 (18-24点)"
        ]

        var slotCounts = [String: Int]() // Use key for counting

        for session in sessions {
            let hour = Int(formatter.string(from: session.startTime)) ?? 0

            let slotKey: String
            if hour >= 0 && hour < 6 {
                slotKey = slotKeys[0]
            } else if hour >= 6 && hour < 12 {
                slotKey = slotKeys[1]
            } else if hour >= 12 && hour < 18 {
                slotKey = slotKeys[2]
            } else {
                slotKey = slotKeys[3]
            }

            slotCounts[slotKey, default: 0] += 1
        }

        return slotCounts.max(by: { $0.value < $1.value })
            .map { (slot: NSLocalizedString($0.key, comment: "Time slot description"), count: $0.value) } // Localize here
    }

    // 获取专注时长最长的时段 - Returns LOCALIZED slot string
    private func getLongestTimeSlot() -> (slot: String, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let formatter = DateFormatter()
        formatter.dateFormat = "HH"

        // 定义时间段键名
        let slotKeys = [
             "view.xmomentInsights.time.timeSlot.morning", // "凌晨 (0-6点)"
             "view.xmomentInsights.time.timeSlot.forenoon", // "上午 (6-12点)"
             "view.xmomentInsights.time.timeSlot.afternoon", // "下午 (12-18点)"
             "view.xmomentInsights.time.timeSlot.evening" // "晚上 (18-24点)"
        ]

        var slotDurations = [String: TimeInterval]() // Use key for counting

        for session in sessions {
            let hour = Int(formatter.string(from: session.startTime)) ?? 0

            let slotKey: String
            if hour >= 0 && hour < 6 {
                slotKey = slotKeys[0]
            } else if hour >= 6 && hour < 12 {
                slotKey = slotKeys[1]
            } else if hour >= 12 && hour < 18 {
                slotKey = slotKeys[2]
            } else {
                slotKey = slotKeys[3]
            }

            slotDurations[slotKey, default: 0] += session.duration
        }

        return slotDurations.max(by: { $0.value < $1.value })
            .map { (slot: NSLocalizedString($0.key, comment: "Time slot description"), duration: $0.value) } // Localize here
    }

    // 获取最近无专注的日期
    private func getRecentNoFocusDays(days: Int) -> [Date] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        // 创建最近days天的日期数组
        var daysArray: [Date] = []
        for i in 0..<days {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                daysArray.append(calendar.startOfDay(for: date))
            }
        }

        // 找出有专注记录的日期
        let focusDays = Set(sessions.map { calendar.startOfDay(for: $0.startTime) })

        // 过滤出无专注记录的日期
        return daysArray.filter { !focusDays.contains($0) }
    }

    // 获取使用最多的分类 - Returns category name which might need localization elsewhere if user-defined
    private func getMostUsedCategory() -> (category: String, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified" // "未分类"

        var categoryCounts: [String: Int] = [:]

        for session in sessions {
            let category = session.category ?? NSLocalizedString(unclassifiedKey, comment: "Default category name when none is set")
            categoryCounts[category, default: 0] += 1
        }

        // Determine the category name to return. If it's the localized "Unclassified", return that.
        // Otherwise, return the user's category name (which might need localization separately if it matches a predefined one).
        // For simplicity here, we return the name directly. If the max category IS "Unclassified", its name is already localized.
        return categoryCounts.max(by: { $0.value < $1.value })
            .map { (category: $0.key, count: $0.value) }
    }

    // 获取专注时长最长的分类 - Returns category name
    private func getLongestDurationCategory() -> (category: String, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified" // "未分类"

        var categoryDurations: [String: TimeInterval] = [:]

        for session in sessions {
            let category = session.category ?? NSLocalizedString(unclassifiedKey, comment: "Default category name when none is set")
            categoryDurations[category, default: 0] += session.duration
        }

        return categoryDurations.max(by: { $0.value < $1.value })
            .map { (category: $0.key, duration: $0.value) }
    }

    // 获取平均专注时长最长的分类 - Returns category name
    private func getHighestAvgDurationCategory() -> (category: String, avgDuration: TimeInterval, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified" // "未分类"

        var categoryDurations: [String: TimeInterval] = [:]
        var categoryCounts: [String: Int] = [:]

        for session in sessions {
            let category = session.category ?? NSLocalizedString(unclassifiedKey, comment: "Default category name when none is set")
            categoryDurations[category, default: 0] += session.duration
            categoryCounts[category, default: 0] += 1
        }

        var categoryAvgDurations: [(category: String, avgDuration: TimeInterval, count: Int)] = []

        for (category, duration) in categoryDurations {
            let count = categoryCounts[category] ?? 0
            if count > 0 {
                let avgDuration = duration / Double(count)
                categoryAvgDurations.append((category: category, avgDuration: avgDuration, count: count))
            }
        }

        return categoryAvgDurations.max(by: { $0.avgDuration < $1.avgDuration })
    }

    // 获取中断率最低的分类 - Returns category name
    private func getLowestInterruptionCategory() -> (category: String, rate: Double, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified" // "未分类"

        var categoryTotals: [String: Int] = [:]
        var categoryInterruptions: [String: Int] = [:]

        for session in sessions {
            let category = session.category ?? NSLocalizedString(unclassifiedKey, comment: "Default category name when none is set")
            categoryTotals[category, default: 0] += 1

            if session.isInterrupted {
                categoryInterruptions[category, default: 0] += 1
            }
        }

        var categoryInterruptionRates: [(category: String, rate: Double, count: Int)] = []

        for (category, total) in categoryTotals {
            if total >= 5 { // 至少有5次记录才考虑
                let interruptions = categoryInterruptions[category] ?? 0
                let rate = Double(interruptions) / Double(total)
                categoryInterruptionRates.append((category: category, rate: rate, count: total))
            }
        }

        return categoryInterruptionRates.min(by: { $0.rate < $1.rate })
    }

    // 获取最常见的心情 - Returns mood string (user input)
    private func getMostCommonMood() -> (mood: String, count: Int)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.mood != nil && !$0.mood!.isEmpty }

        var moodCounts: [String: Int] = [:]

        for session in sessions {
            if let mood = session.mood {
                moodCounts[mood, default: 0] += 1
            }
        }

        return moodCounts.max(by: { $0.value < $1.value })
            .map { (mood: $0.key, count: $0.value) }
    }

    // 获取专注时长最长的心情 - Returns mood string (user input)
    private func getLongestDurationMood() -> (mood: String, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.mood != nil && !$0.mood!.isEmpty }

        var moodDurations: [String: TimeInterval] = [:]

        for session in sessions {
            if let mood = session.mood {
                moodDurations[mood, default: 0] += session.duration
            }
        }

        return moodDurations.max(by: { $0.value < $1.value })
            .map { (mood: $0.key, duration: $0.value) }
    }

    // 获取心情分布
    private func getMoodDistribution() -> [MoodData] {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.mood != nil && !$0.mood!.isEmpty }

        var moodCounts: [String: Int] = [:]

        for session in sessions {
            if let mood = session.mood {
                moodCounts[mood, default: 0] += 1
            }
        }

        return moodCounts.map { MoodData(mood: $0.key, count: $0.value) }
            .sorted { $0.count > $1.count }
    }

    // 获取XU收益最高的会话
    private func getHighestXUSession() -> (xu: Double, date: Date, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()

        return sessions.max(by: { $0.xUnits < $1.xUnits })
            .map { (xu: $0.xUnits, date: $0.startTime, duration: $0.duration) }
    }

    // 获取XU效率最高的会话
    private func getHighestXUEfficiencySession() -> (efficiency: Double, date: Date, duration: TimeInterval)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.duration >= 5 * 60 } // 至少5分钟的会话

        var sessionEfficiencies: [(efficiency: Double, date: Date, duration: TimeInterval)] = []

        for session in sessions {
            let hourDuration = session.duration / 3600 // 转换为小时
            if hourDuration > 0 { // Avoid division by zero for very short sessions just in case
               let efficiency = session.xUnits / hourDuration
               sessionEfficiencies.append((efficiency: efficiency, date: session.startTime, duration: session.duration))
            }
        }

        return sessionEfficiencies.max(by: { $0.efficiency < $1.efficiency })
    }

    // 获取XU获取最多的日期
    private func getMostXUDay() -> (date: Date, xu: Double)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        var dateXUs: [Date: Double] = [:]

        for session in sessions {
            let date = calendar.startOfDay(for: session.startTime)
            dateXUs[date, default: 0] += session.xUnits
        }

        return dateXUs.max(by: { $0.value < $1.value })
            .map { (date: $0.key, xu: $0.value) }
    }

    // 获取XU获取最多的分类 - Returns category name
    private func getMostXUCategory() -> (category: String, xu: Double)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        let unclassifiedKey = "common.category.unclassified" // "未分类"

        var categoryXUs: [String: Double] = [:]

        for session in sessions {
            let category = session.category ?? NSLocalizedString(unclassifiedKey, comment: "Default category name when none is set")
            categoryXUs[category, default: 0] += session.xUnits
        }

        return categoryXUs.max(by: { $0.value < $1.value })
            .map { (category: $0.key, xu: $0.value) }
    }

    // 获取单次最长专注时间
    private func getLongestSession() -> (duration: TimeInterval, date: Date, isFreeMode: Bool)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous()

        return sessions.max(by: { $0.duration < $1.duration })
            .map { (duration: $0.duration, date: $0.startTime, isFreeMode: $0.isFreeMode) }
    }

    // 获取单次最长超频时间
    private func getLongestOverclock() -> (duration: TimeInterval, date: Date, factor: Double)? {
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.overclockDuration > 0 }

        return sessions.max(by: { $0.overclockDuration < $1.overclockDuration })
            .map { (duration: $0.overclockDuration, date: $0.startTime, factor: $0.overclockFactor) }
    }

    // 获取单日最多专注次数
    private func getMostSessionsDay() -> (count: Int, date: Date)? {
        return getMostFocusedDay().map { (count: $0.count, date: $0.date) }
    }

    // 获取单日最长专注时间
    private func getLongestDayDuration() -> (duration: TimeInterval, date: Date)? {
        if let result = getLongestDurationDay() {
            return (duration: result.duration, date: result.date)
        }
        return nil
    }

    // MARK: - 辅助方法

    // 格式化持续时间 - Uses localized formats
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
             // "%d小时%d分钟"
            return String(format: NSLocalizedString("common.durationFormat.hoursMinutes", comment: "Duration format: hours and minutes. Parameters: hours, minutes"), hours, minutes)
        } else if minutes > 0 {
             // "%d分钟%d秒"
            return String(format: NSLocalizedString("common.durationFormat.minutesSeconds", comment: "Duration format: minutes and seconds. Parameters: minutes, seconds"), minutes, seconds)
        } else {
             // "%d秒"
            return String(format: NSLocalizedString("common.durationFormat.seconds", comment: "Duration format: seconds. Parameter: seconds"), seconds)
        }
    }

    // 格式化日期 - Uses localized format
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
         // "MM月dd日"
        formatter.dateFormat = NSLocalizedString("common.dateFormat.monthDay", comment: "Date format: Month and day (e.g., 08月15日)")
        return formatter.string(from: date)
    }
}

// 数据模型
struct MoodData: Identifiable {
    var id = UUID()
    var mood: String
    var count: Int
}

// 预览
struct XMomentInsightsView_Previews: PreviewProvider {
    static var previews: some View {
        XMomentInsightsView()
            .environmentObject(DataStore.shared)
            // Add localization environment for preview if needed
            // .environment(\\.locale, .init(identifier: "zh-Hans"))
    }
}