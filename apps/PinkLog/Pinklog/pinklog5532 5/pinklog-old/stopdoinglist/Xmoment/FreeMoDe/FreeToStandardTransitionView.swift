import SwiftUI
import StopDoingListKit

// 按钮样式
fileprivate struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(10)
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.spring(), value: configuration.isPressed)
    }
}

struct FreeToStandardTransitionView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var showingStandardModeInfo = false

    // 检查用户是否准备好过渡到标准模式
    private var isReadyForStandardMode: Bool {
        let stats = dataStore.getXMomentUserStatsUnambiguous()
        return stats.freeModeReached30MinCount >= 5
    }

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text(isReadyForStandardMode ? NSLocalizedString("transition.readyForChallenge", comment: "") : NSLocalizedString("transition.focusProgressTitle", comment: ""))
                .font(.title2.bold())
                .foregroundColor(.primary)

            // 进度指示器
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 10)
                    .frame(width: 150, height: 150)

                Circle()
                    .trim(from: 0, to: progressPercentage())
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [.blue, .purple]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 10, lineCap: .round)
                    )
                    .frame(width: 150, height: 150)
                    .rotationEffect(.degrees(-90))

                VStack {
                    Text("\(Int(progressPercentage() * 100))%")
                        .font(.title.bold())

                    Text(NSLocalizedString("transition.readiness", comment: ""))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()

            // 达成条件列表
            VStack(alignment: .leading, spacing: 15) {
                Text(NSLocalizedString("transition.standardModeReadyTitle", comment: ""))
                    .font(.headline)

                ChecklistItem(
                    text: NSLocalizedString("transition.condition.reach30Min5Times", comment: ""),
                    isCompleted: dataStore.getXMomentUserStatsUnambiguous().freeModeReached30MinCount >= 5
                )

                ChecklistItem(
                    text: NSLocalizedString("transition.condition.longestDuration45Min", comment: ""),
                    isCompleted: dataStore.getXMomentUserStatsUnambiguous().freeModeBestDuration >= 45 * 60
                )

                ChecklistItem(
                    text: NSLocalizedString("transition.condition.complete10FreeMode", comment: ""),
                    isCompleted: dataStore.getXMomentUserStatsUnambiguous().freeModeSessionCount >= 10
                )
            }
            .padding()
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(10)

            // 引导文字
            Text(isReadyForStandardMode ?
                 NSLocalizedString("transition.congratsMessage", comment: "") :
                 NSLocalizedString("transition.continueFreeModeMessage", comment: ""))
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding()

            // 操作按钮
            if isReadyForStandardMode {
                Button(NSLocalizedString("transition.learnStandardModeButton", comment: "")) {
                    showingStandardModeInfo = true
                }
                .buttonStyle(PrimaryButtonStyle())
                .padding()
            }
        }
        .padding()
        .sheet(isPresented: $showingStandardModeInfo) {
            StandardModeInfoView()
        }
    }

    // 计算准备度百分比
    private func progressPercentage() -> Double {
        let stats = dataStore.getXMomentUserStatsUnambiguous()

        // 三个条件的权重
        let weight1 = min(1.0, Double(stats.freeModeReached30MinCount) / 5.0) * 0.5 // 50%权重
        let weight2 = min(1.0, stats.freeModeBestDuration / (45 * 60)) * 0.3 // 30%权重
        let weight3 = min(1.0, Double(stats.freeModeSessionCount) / 10.0) * 0.2 // 20%权重

        return weight1 + weight2 + weight3
    }
}

// 检查项组件
struct ChecklistItem: View {
    let text: String
    let isCompleted: Bool

    var body: some View {
        HStack {
            Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isCompleted ? .green : .gray)

            Text(text) // Note: text in ChecklistItem should be passed already localized
                .strikethrough(isCompleted)
                .foregroundColor(isCompleted ? .secondary : .primary)
        }
    }
}

// 标准模式信息视图
struct StandardModeInfoView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 标题
                    Text(NSLocalizedString("transition.standardModeInfoTitle", comment: ""))
                        .font(.largeTitle.bold())
                        .padding(.bottom, 10)

                    // 特点介绍
                    FeatureSection(
                        title: NSLocalizedString("transition.feature.structuredFocusTitle", comment: ""),
                        description: NSLocalizedString("transition.feature.structuredFocusDesc", comment: ""),
                        iconName: "timer"
                    )

                    FeatureSection(
                        title: NSLocalizedString("transition.feature.overclockRewardTitle", comment: ""),
                        description: NSLocalizedString("transition.feature.overclockRewardDesc", comment: ""),
                        iconName: "bolt.fill"
                    )

                    FeatureSection(
                        title: NSLocalizedString("transition.feature.higherEfficiencyTitle", comment: ""),
                        description: NSLocalizedString("transition.feature.higherEfficiencyDesc", comment: ""),
                        iconName: "chart.bar.fill"
                    )

                    FeatureSection(
                        title: NSLocalizedString("transition.feature.focusStatsTitle", comment: ""),
                        description: NSLocalizedString("transition.feature.focusStatsDesc", comment: ""),
                        iconName: "chart.pie.fill"
                    )

                    // 提示
                    Text(NSLocalizedString("transition.tip.switchModes", comment: ""))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(Color.yellow.opacity(0.1))
                        .cornerRadius(10)

                    Spacer()

                    // 按钮
                    Button(NSLocalizedString("transition.understoodButton", comment: "")) {
                        dismiss()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .frame(maxWidth: .infinity)
                    .padding(.top)
                }
                .padding()
            }
            .navigationBarItems(trailing: Button(NSLocalizedString("transition.closeButton", comment: "")) {
                dismiss()
            })
        }
    }
}

// 特点介绍组件
struct FeatureSection: View {
    let title: String
    let description: String
    let iconName: String

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: iconName)
                .font(.title)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title) // Note: title in FeatureSection should be passed already localized
                    .font(.headline)

                Text(description) // Note: description in FeatureSection should be passed already localized
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.secondary.opacity(0.05))
        .cornerRadius(10)
    }
}



// 预览
struct FreeToStandardTransitionView_Previews: PreviewProvider {
    static var previews: some View {
        FreeToStandardTransitionView()
            .environmentObject(DataStore.shared)
    }
}
