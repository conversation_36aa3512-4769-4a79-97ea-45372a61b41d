import SwiftUI
import Charts
import StopDoingListKit

struct FreeModeTrendView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var timeRange: TimeRange = .month

    enum TimeRange {
        case week, month, year
    }

    // 获取趋势数据
    private func getTrendData() -> [(date: Date, duration: TimeInterval)] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
            .sorted { $0.startTime < $1.startTime }

        let calendar = Calendar.current
        let now = Date()
        
        // 根据时间范围获取起始日期和数据
        let startDate: Date
        var result: [Date: TimeInterval] = [:]
        
        switch timeRange {
        case .week:
            // 获取本周的开始（周日）
            let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
            guard let weekStart = calendar.date(from: components) else {
                startDate = calendar.date(byAdding: .day, value: -6, to: now)!
                break
            }
            
            // 调整为周日开始
            startDate = calendar.date(byAdding: .day, value: -1, to: weekStart)!
            
            // 初始化一周7天的数据点
            var currentDate = startDate
            for _ in 0..<7 {
                result[currentDate] = 0
                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
            }
            
            // 累加每日时长
            let filteredSessions = sessions.filter { $0.startTime >= startDate && $0.startTime <= now }
            for session in filteredSessions {
                let day = calendar.startOfDay(for: session.startTime)
                result[day, default: 0] += session.duration
            }
            
        case .month:
            // 获取本月的开始
            let components = calendar.dateComponents([.year, .month], from: now)
            var comps = components
            comps.day = 1
            startDate = calendar.date(from: comps)!
            
            // 获取本月的天数
            let range = calendar.range(of: .day, in: .month, for: startDate)!
            let daysInMonth = range.count
            
            // 初始化本月每天的数据点
            var currentDate = startDate
            for _ in 0..<daysInMonth {
                result[currentDate] = 0
                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
            }
            
            // 累加每日时长
            let filteredSessions = sessions.filter { $0.startTime >= startDate && $0.startTime <= now }
            for session in filteredSessions {
                let day = calendar.startOfDay(for: session.startTime)
                result[day, default: 0] += session.duration
            }
            
        case .year:
            // 获取今年的开始
            let components = calendar.dateComponents([.year], from: now)
            var comps = components
            comps.month = 1
            comps.day = 1
            startDate = calendar.date(from: comps)!
            
            // 初始化今年每月的数据点
            for month in 1...12 {
                comps.month = month
                if let monthDate = calendar.date(from: comps) {
                    result[monthDate] = 0
                }
            }
            
            // 累加每月时长
            let filteredSessions = sessions.filter { $0.startTime >= startDate && $0.startTime <= now }
            for session in filteredSessions {
                let month = calendar.component(.month, from: session.startTime)
                comps.month = month
                if let monthDate = calendar.date(from: comps) {
                    result[monthDate, default: 0] += session.duration
                }
            }
        }

        // 转换为数组并排序
        return result.map { (date: $0.key, duration: $0.value) }
            .sorted { $0.date < $1.date }
    }

    // 根据时间范围筛选会话
    private func filterSessionsByTimeRange(_ sessions: [XMomentSession]) -> [XMomentSession] {
        let calendar = Calendar.current
        let now = Date()

        switch timeRange {
        case .week:
            // 过去7天
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
            return sessions.filter { $0.startTime >= weekAgo }

        case .month:
            // 过去30天
            let monthAgo = calendar.date(byAdding: .day, value: -30, to: now)!
            return sessions.filter { $0.startTime >= monthAgo }

        case .year:
            // 过去365天
            let yearAgo = calendar.date(byAdding: .day, value: -365, to: now)!
            return sessions.filter { $0.startTime >= yearAgo }
        }
    }

    var body: some View {
        VStack(spacing: 24) {
            // 统计区块
            trendStatsCard

            // 时间范围选择器
            HStack {
                Spacer()
                Picker("", selection: $timeRange) {
                    Text(NSLocalizedString("week", comment: "")).tag(TimeRange.week)
                    Text(NSLocalizedString("month", comment: "")).tag(TimeRange.month)
                    Text(NSLocalizedString("year", comment: "")).tag(TimeRange.year)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                Spacer()
            }
            .padding(.bottom, 2)

            // 趋势图卡片
            ZStack {
                RoundedRectangle(cornerRadius: 24, style: .continuous)
                    .fill(Color(UIColor.systemBackground))
                    .shadow(color: Color.black.opacity(0.06), radius: 8, x: 0, y: 4)
                VStack(spacing: 0) {
                    trendBarChart
                }
                .padding(.vertical, 16)
                .padding(.horizontal, 12)
            }
            .padding(.horizontal)
            .padding(.bottom, 8)
        }
    }

    // 统计区块
    private var trendStatsCard: some View {
        let data = getTrendData()
        let total = data.reduce(0) { $0 + $1.duration / 3600 } // 小时
        let avg = data.isEmpty ? 0 : total / Double(data.count)
        let (title, subtitle) = getStatsTitleAndSubtitle()
        return ZStack {
            RoundedRectangle(cornerRadius: 24, style: .continuous)
                .fill(Color.accentColor.opacity(0.85))
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(.white)
                Text(subtitle)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                HStack(spacing: 24) {
                    VStack(spacing: 2) {
                        Text(String(format: "%.1f", total))
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                        Text(NSLocalizedString("WEEK TOTAL", comment: "统计总数"))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    VStack(spacing: 2) {
                        Text(String(format: "%.2f", avg))
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                        Text(NSLocalizedString("AVERAGE", comment: "平均"))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            .padding(.vertical, 18)
        }
        .padding(.horizontal)
    }

    // 统计区块标题和副标题
    private func getStatsTitleAndSubtitle() -> (String, String) {
        let calendar = Calendar.current
        let now = Date()
        switch timeRange {
        case .week:
            let formatter = DateFormatter()
            formatter.locale = Locale.current
            formatter.setLocalizedDateFormatFromTemplate("MMM d")
            let start = calendar.date(byAdding: .day, value: -6, to: now) ?? now
            let end = now
            let title = String(format: "%@ - %@", formatter.string(from: start), formatter.string(from: end))
            let subtitle = NSLocalizedString("WEEK", comment: "周")
            return (title, subtitle)
        case .month:
            let formatter = DateFormatter()
            formatter.locale = Locale.current
            formatter.setLocalizedDateFormatFromTemplate("MMMM yyyy")
            let title = formatter.string(from: now)
            let subtitle = NSLocalizedString("MONTH", comment: "月")
            return (title, subtitle)
        case .year:
            let formatter = DateFormatter()
            formatter.locale = Locale.current
            formatter.setLocalizedDateFormatFromTemplate("yyyy")
            let title = formatter.string(from: now)
            let subtitle = NSLocalizedString("YEAR", comment: "年")
            return (title, subtitle)
        }
    }

    // 趋势图
    private var trendBarChart: some View {
        let data = getTrendData()
        let maxValue = data.map { $0.duration / 3600 }.max() ?? 1 // 小时
        let barCount = data.count
        let today = Calendar.current.startOfDay(for: Date())
        let selectedIndex = data.firstIndex(where: { Calendar.current.isDate($0.date, inSameDayAs: today) }) ?? -1
        return GeometryReader { geo in
            let barSpacing: CGFloat = barCount <= 12 ? 12 : (barCount <= 31 ? 4 : 2)
            let barWidth = max((geo.size.width - CGFloat(barCount - 1) * barSpacing) / CGFloat(barCount), 6)
            VStack(spacing: 0) {
                // Y轴最大值和0（统一一行显示，左上角对齐）
                HStack(alignment: .firstTextBaseline, spacing: 2) {
                    Text(String(format: "%.1f", maxValue))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("h")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("0")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 4)
                .padding(.bottom, 2)

                // 柱状图
                HStack(alignment: .bottom, spacing: barSpacing) {
                    ForEach(0..<barCount, id: \.self) { idx in
                        let item = data[idx]
                        let isToday = idx == selectedIndex
                        let barHeight = max(6, CGFloat(item.duration / 3600) / CGFloat(maxValue) * 120)
                        RoundedRectangle(cornerRadius: barWidth/2)
                            .fill(isToday ? Color.white : Color.green.opacity(0.7))
                            .frame(width: barWidth, height: barHeight)
                            .overlay(
                                isToday ? RoundedRectangle(cornerRadius: barWidth/2)
                                    .stroke(Color.green, lineWidth: 2) : nil
                            )
                            .animation(.easeInOut, value: item.duration)
                    }
                }
                .frame(height: 120)
                .padding(.vertical, 8)

                // X轴标签
                HStack(spacing: barSpacing) {
                    ForEach(0..<barCount, id: \.self) { idx in
                        let item = data[idx]
                        let label = getXAxisLabel(for: item.date, index: idx, total: barCount)
                        if timeRange == .month && label == "15" {
                            Text(label)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .frame(width: max(barWidth, 24), alignment: .center)
                                .lineLimit(1)
                        } else {
                            Text(label)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .frame(width: barWidth, alignment: .center)
                                .lineLimit(1)
                        }
                    }
                }
                .padding(.top, 2)
            }
        }
        .frame(height: 170)
    }

    // 年视图专用：本地化月份首字母数组
    private var chartMonthSymbols: [String] {
        let locale = Locale.current
        let langCode: String? = {
            if #available(iOS 16.0, *) {
                return locale.language.languageCode?.identifier
            } else {
                return locale.languageCode
            }
        }()
        if langCode?.hasPrefix("zh") == true {
            return (1...12).map { "\($0)" }
        } else {
            return ["J","F","M","A","M","J","J","A","S","O","N","D"]
        }
    }

    // 获取X轴标签
    private func getXAxisLabel(for date: Date, index: Int, total: Int) -> String {
        let calendar = Calendar.current
        switch timeRange {
        case .week:
            // 本地化星期
            let symbols = DateFormatter().shortWeekdaySymbols ?? ["Su","Mo","Tu","We","Th","Fr","Sa"]
            let weekday = calendar.component(.weekday, from: date)
            return symbols[(weekday-1)%7]
        case .month:
            // 只显示15号
            let day = calendar.component(.day, from: date)
            return day == 15 ? "15" : ""
        case .year:
            // 用自定义的月份首字母数组
            let month = calendar.component(.month, from: date)
            return chartMonthSymbols[(month-1)%12]
        }
    }

    // 获取星期几的文字表示
    private func getWeekdayText(for date: Date) -> String {
        let weekday = Calendar.current.component(.weekday, from: date)
        let weekdaySymbols = ["日", "一", "二", "三", "四", "五", "六"]
        return weekdaySymbols[weekday - 1]
    }

    // 获取X轴的时间范围
    private func getXAxisDomain() -> ClosedRange<Date> {
        let calendar = Calendar.current
        let now = Date()
        
        let startDate: Date
        let endDate: Date
        
        switch timeRange {
        case .week:
            // 获取本周的开始（周日）和结束（周六）
            let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
            guard let weekStart = calendar.date(from: components) else {
                startDate = calendar.date(byAdding: .day, value: -6, to: now)!
                endDate = now
                break
            }
            
            // 调整为周日开始
            startDate = calendar.date(byAdding: .day, value: -1, to: weekStart)!
            endDate = calendar.date(byAdding: .day, value: 5, to: weekStart)!
            
        case .month:
            // 获取本月的开始和结束
            let components = calendar.dateComponents([.year, .month], from: now)
            var comps = components
            comps.day = 1
            startDate = calendar.date(from: comps)!
            
            comps.month = comps.month! + 1
            let nextMonth = calendar.date(from: comps)!
            endDate = calendar.date(byAdding: .day, value: -1, to: nextMonth)!
            
        case .year:
            // 获取今年的开始和结束
            let components = calendar.dateComponents([.year], from: now)
            var comps = components
            comps.month = 1
            comps.day = 1
            startDate = calendar.date(from: comps)!
            
            comps.year = comps.year! + 1
            let nextYear = calendar.date(from: comps)!
            endDate = calendar.date(byAdding: .day, value: -1, to: nextYear)!
        }
        
        return startDate...endDate
    }

    // 获取最大持续时间（分钟）
    private func getMaxDuration() -> Double {
        let maxDuration = getTrendData().map { $0.duration / 60 }.max() ?? 30
        return max(maxDuration, 30) // 确保至少显示30分钟的范围
    }
}

// 进步分析组件
struct ProgressAnalysis: View {
    let data: [(date: Date, duration: TimeInterval)]

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text(NSLocalizedString("freeTrend.analysis.title", comment: ""))
                .font(.headline)

            // 进步趋势
            HStack {
                Image(systemName: progressTrend().icon)
                    .foregroundColor(progressTrend().color)
                Text(progressTrend().message) // Message is already localized from the function
            }

            // 专注门槛达成率
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.blue)
                Text(String(format: NSLocalizedString("freeTrend.analysis.thresholdRateFormat", comment: ""), thresholdRate()))
            }

            // 最近一周与上周比较
            if let comparison = weeklyComparison() {
                HStack {
                    Image(systemName: comparison.improved ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                        .foregroundColor(comparison.improved ? .green : .red)
                    Text(comparison.message) // Message is already localized from the function
                }
            }

            // 建议
            Text(getSuggestion()) // Suggestion is already localized from the function
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.top, 5)
        }
        .padding()
        .background(Color.secondary.opacity(0.1))
        .cornerRadius(10)
    }

    // 计算进步趋势 (返回本地化字符串)
    private func progressTrend() -> (icon: String, color: Color, message: String) {
        guard data.count >= 2 else {
            return ("chart.line.flattrend", .gray, NSLocalizedString("freeTrend.analysis.trend.insufficientData", comment: ""))
        }

        // 计算线性回归斜率
        let n = Double(data.count)
        let xValues = Array(0..<data.count).map { Double($0) }
        let yValues = data.map { $0.duration / 60 }

        let sumX = xValues.reduce(0, +)
        let sumY = yValues.reduce(0, +)
        let sumXY = zip(xValues, yValues).map { $0 * $1 }.reduce(0, +)
        let sumXX = xValues.map { $0 * $0 }.reduce(0, +)

        let slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)

        if slope > 0.5 {
            return ("arrow.up.right", .green, NSLocalizedString("freeTrend.analysis.trend.steadyIncrease", comment: ""))
        } else if slope > 0 {
            return ("arrow.up.right", .green, NSLocalizedString("freeTrend.analysis.trend.slightIncrease", comment: ""))
        } else if slope > -0.5 {
            return ("arrow.forward", .orange, NSLocalizedString("freeTrend.analysis.trend.stable", comment: ""))
        } else {
            return ("arrow.down.right", .red, NSLocalizedString("freeTrend.analysis.trend.decrease", comment: ""))
        }
    }

    // 计算专注门槛达成率
    private func thresholdRate() -> Int {
        let thresholdCount = data.filter { $0.duration >= 30 * 60 }.count
        return data.isEmpty ? 0 : Int(Double(thresholdCount) / Double(data.count) * 100)
    }

    // 最近一周与上周比较 (返回本地化字符串)
    private func weeklyComparison() -> (improved: Bool, message: String)? {
        guard data.count >= 14 else {
            return nil
        }

        let calendar = Calendar.current
        let now = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
        let twoWeeksAgo = calendar.date(byAdding: .day, value: -14, to: now)!

        let thisWeekData = data.filter { $0.date >= weekAgo && $0.date <= now }
        let lastWeekData = data.filter { $0.date >= twoWeeksAgo && $0.date < weekAgo }

        guard !thisWeekData.isEmpty && !lastWeekData.isEmpty else {
            return nil
        }

        let thisWeekAvg = thisWeekData.map { $0.duration }.reduce(0, +) / Double(thisWeekData.count) / 60
        let lastWeekAvg = lastWeekData.map { $0.duration }.reduce(0, +) / Double(lastWeekData.count) / 60

        let diff = thisWeekAvg - lastWeekAvg
        let improved = diff > 0

        if abs(diff) < 1 {
            return (improved, NSLocalizedString("freeTrend.analysis.comparison.stable", comment: ""))
        } else {
            let formatKey = improved ? "freeTrend.analysis.comparison.improvedFormat" : "freeTrend.analysis.comparison.decreasedFormat"
            let value = improved ? Int(diff) : Int(abs(diff))
            return (improved, String(format: NSLocalizedString(formatKey, comment: ""), value))
        }
    }

    // 根据数据生成建议 (返回本地化字符串)
    private func getSuggestion() -> String {
        let rate = thresholdRate()

        if rate >= 80 {
            return NSLocalizedString("freeTrend.suggestion.expert", comment: "")
        } else if rate >= 50 {
            return NSLocalizedString("freeTrend.suggestion.good", comment: "")
        } else if rate >= 20 {
            return NSLocalizedString("freeTrend.suggestion.improving", comment: "")
        } else {
            return NSLocalizedString("freeTrend.suggestion.beginner", comment: "")
        }
    }
}

// 预览
struct FreeModeTrendView_Previews: PreviewProvider {
    static var previews: some View {
        FreeModeTrendView()
            .environmentObject(DataStore.shared)
    }
}
