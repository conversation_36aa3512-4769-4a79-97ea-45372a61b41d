import SwiftUI
import StopDoingListKit

struct FreeModeCalendarView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var selectedMonth: Date = Date()
    @State private var selectedDate: Date? = nil
    @State private var selectedDateSessions: [XMomentSession] = []

    // 修改为接收外部传递的导航状态和回调函数
    var onDateSelected: ((Date, [XMomentSession]) -> Void)?

    // 获取月份数据
    private func getMonthData() -> [Date: (count: Int, totalDuration: TimeInterval)] {
        // 修改为包含所有会话，不仅限于自由模式
        let sessions = dataStore.getXMomentSessionsUnambiguous() // 不再过滤isFreeMode
        let calendar = Calendar.current

        var result: [Date: (count: Int, totalDuration: TimeInterval)] = [:]

        for session in sessions {
            let day = calendar.startOfDay(for: session.startTime)
            let currentValue = result[day] ?? (count: 0, totalDuration: 0)

            result[day] = (
                count: currentValue.count + 1,
                totalDuration: currentValue.totalDuration + session.duration
            )
        }

        return result
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 日历卡片
                calendarCard

                // 进步卡片
                progressCard

                // 如果选中了日期，显示查看详情按钮
                if let selectedDate = selectedDate, let data = getMonthData()[selectedDate], data.count > 0 {
                    Button(action: {
                        // 获取选中日期的专注会话
                        let sessions = getSessionsForDate(selectedDate)
                        selectedDateSessions = sessions
                        // 使用外部回调函数处理导航
                        if let onDateSelected = onDateSelected {
                            onDateSelected(selectedDate, sessions)
                        }
                    }) {
                        HStack {
                            Text(String(format: NSLocalizedString("freeCalendar.viewDetailsFormat", comment: ""), formattedDate(selectedDate)))
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.blue, .purple]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                        .shadow(color: Color.blue.opacity(0.3), radius: 5, x: 0, y: 3)
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.bottom)
        }
    }

    // 日历卡片
    private var calendarCard: some View {
        VStack(spacing: 15) {
            // 月份选择器
            HStack {
                Button(action: { moveMonth(by: -1) }) {
                    Image(systemName: "chevron.left")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Circle())
                }

                Spacer()

                Text(monthYearString(from: selectedMonth))
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                Button(action: { moveMonth(by: 1) }) {
                    Image(systemName: "chevron.right")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal)

            // 星期标题
            HStack {
                ForEach(["sun", "mon", "tue", "wed", "thu", "fri", "sat"], id: \.self) { dayKey in
                    Text(NSLocalizedString(dayKey, comment: "Weekday initial"))
                        .font(.footnote)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)

            // 日历网格
            let monthData = getMonthData()
            let daysInMonth = getDaysInMonth()

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 4), count: 7), spacing: 8) {
                ForEach(daysInMonth, id: \.self) { date in
                    if let data = monthData[date] {
                        DayCell(
                            date: date,
                            count: data.count,
                            totalDuration: data.totalDuration,
                            isSelected: isSelected(date)
                        )
                        .onTapGesture {
                            selectDate(date)

                            // 如果用户点击了有专注记录的日期，立即调用回调函数
                            if data.count > 0 {
                                let sessions = getSessionsForDate(date)
                                selectedDateSessions = sessions
                                if let onDateSelected = onDateSelected {
                                    onDateSelected(date, sessions)
                                }
                            }
                        }
                    } else {
                        DayCell(
                            date: date,
                            count: 0,
                            totalDuration: 0,
                            isSelected: isSelected(date)
                        )
                        .onTapGesture {
                            selectDate(date)
                        }
                    }
                }
            }
            .padding(.horizontal, 8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        .padding(.horizontal)
    }

    // 进步卡片
    private var progressCard: some View {
        let monthData = getMonthData()

        return VStack(spacing: 0) {
            ProgressIndicators(monthData: monthData, selectedMonth: selectedMonth)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        .padding(.horizontal)
    }

    // 获取指定日期的会话
    private func getSessionsForDate(_ date: Date) -> [XMomentSession] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) else { return [] }

        // 从所有会话中过滤出指定日期的会话
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        let filteredSessions = allSessions.filter {
            $0.startTime >= startOfDay && $0.startTime < endOfDay
        }.sorted { $0.startTime < $1.startTime } // 按时间排序

        print("获取日期 \(formattedDate(date)) 的会话，共 \(filteredSessions.count) 条")

        return filteredSessions
    }

    // 格式化日期
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = NSLocalizedString("common.dateFormat.monthDay", comment: "Format for month and day")
        return formatter.string(from: date)
    }

    // 获取当前月份的所有日期
    private func getDaysInMonth() -> [Date] {
        let calendar = Calendar.current

        // 获取当前月的第一天
        let components = calendar.dateComponents([.year, .month], from: selectedMonth)
        let startOfMonth = calendar.date(from: components)!

        // 获取当前月的天数
        let range = calendar.range(of: .day, in: .month, for: startOfMonth)!

        // 获取第一天是星期几（0是星期日，1是星期一，以此类推）
        let firstWeekday = calendar.component(.weekday, from: startOfMonth)

        // 创建日期数组
        var days: [Date] = []

        // 添加上个月的日期填充第一周
        let daysToAddBefore = firstWeekday - 1
        if daysToAddBefore > 0 {
            for i in (1...daysToAddBefore).reversed() {
                let day = calendar.date(byAdding: .day, value: -i, to: startOfMonth)!
                days.append(day)
            }
        }

        // 添加当前月的日期
        for i in 1...range.count {
            let day = calendar.date(byAdding: .day, value: i - 1, to: startOfMonth)!
            days.append(day)
        }

        // 添加下个月的日期填充最后一周
        let remainingDays = 7 - (days.count % 7)
        if remainingDays < 7 {
            for i in 1...remainingDays {
                let day = calendar.date(byAdding: .day, value: range.count + i - 1, to: startOfMonth)!
                days.append(day)
            }
        }

        return days
    }

    // 移动月份
    private func moveMonth(by months: Int) {
        let calendar = Calendar.current
        if let newMonth = calendar.date(byAdding: .month, value: months, to: selectedMonth) {
            selectedMonth = newMonth
        }
    }

    // 选择日期
    private func selectDate(_ date: Date) {
        if selectedDate == date {
            selectedDate = nil
        } else {
            selectedDate = date
        }
    }

    // 检查日期是否被选中
    private func isSelected(_ date: Date) -> Bool {
        guard let selected = selectedDate else { return false }
        return Calendar.current.isDate(date, inSameDayAs: selected)
    }

    // 格式化月份和年份
    private func monthYearString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = NSLocalizedString("month_format", comment: "Format for year and month")
        return formatter.string(from: date)
    }

    // 获取日期的天数
    private func dayNumber(from date: Date) -> Int {
        return Calendar.current.component(.day, from: date)
    }
}

// 日历单元格
struct DayCell: View {
    let date: Date
    let count: Int
    let totalDuration: TimeInterval
    let isSelected: Bool

    var body: some View {
        ZStack {
            // 背景
            Circle()
                .fill(backgroundColorForDuration(totalDuration))
                .opacity(count > 0 ? 0.3 : 0)

            // 日期
            Text("\(dayNumber(from: date))")
                .font(.system(size: 12))
                .foregroundColor(count > 0 ? .primary : .secondary)

            // 专注次数指示器
            if count > 0 {
                Text("\(count)")
                    .font(.system(size: 8))
                    .foregroundColor(.white)
                    .padding(4)
                    .background(Circle().fill(Color.purple))
                    .offset(x: 10, y: -10)
            }
        }
        .frame(height: 40)
        .overlay(
            Circle()
                .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
        )
    }

    // 根据专注时长返回不同深浅的颜色
    private func backgroundColorForDuration(_ duration: TimeInterval) -> Color {
        let minutes = duration / 60

        if minutes >= 60 {
            return Color.purple
        } else if minutes >= 30 {
            return Color.blue
        } else if minutes >= 15 {
            return Color.green
        } else if minutes > 0 {
            return Color.yellow
        } else {
            return Color.clear
        }
    }

    // 获取日期的天数
    private func dayNumber(from date: Date) -> Int {
        return Calendar.current.component(.day, from: date)
    }
}

// 进步指标组件
struct ProgressIndicators: View {
    let monthData: [Date: (count: Int, totalDuration: TimeInterval)]
    let selectedMonth: Date
    @EnvironmentObject var dataStore: DataStore

    // Filter data for the selected month
    private var currentMonthData: [(date: Date, data: (count: Int, totalDuration: TimeInterval))] {
        let calendar = Calendar.current
        return monthData
            .filter { calendar.isDate($0.key, equalTo: selectedMonth, toGranularity: .month) }
            .map { (date: $0.key, data: $0.value) } // Convert to array for easier processing
    }

    // Calculate stats for the selected month
    private var totalCountThisMonth: Int {
        currentMonthData.reduce(0) { $0 + $1.data.count }
    }

    private var totalDurationThisMonth: TimeInterval {
        currentMonthData.reduce(0) { $0 + $1.data.totalDuration }
    }

    private var averageDurationThisMonth: TimeInterval {
        if totalCountThisMonth == 0 { return 0 }
        return totalDurationThisMonth / Double(totalCountThisMonth)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Text(NSLocalizedString("freeCalendar.progress.thisMonth", comment: ""))
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                // 月份显示
                Text(monthString(from: selectedMonth))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // 进度卡片
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                // 总专注次数卡片
                progressCard(
                    title: NSLocalizedString("freeCalendar.progress.totalCount", comment: ""),
                    value: "\(totalCountThisMonth)",
                    unit: NSLocalizedString("times_suffix", comment: ""),
                    icon: "number.circle.fill",
                    color: .blue
                )

                // 平均专注时长卡片
                progressCard(
                    title: NSLocalizedString("freeCalendar.progress.averageDuration", comment: ""),
                    value: "\(Int(averageDurationThisMonth / 60))",
                    unit: NSLocalizedString("common.unit.minute", comment: ""),
                    icon: "clock.fill",
                    color: .green
                )

                // 达到专注门槛卡片
                progressCard(
                    title: NSLocalizedString("freeCalendar.progress.thresholdReached", comment: ""),
                    value: "\(reachedThreshold())",
                    unit: NSLocalizedString("times_suffix", comment: ""),
                    icon: "checkmark.seal.fill",
                    color: .purple
                )

                // 最长专注时间卡片
                progressCard(
                    title: NSLocalizedString("freeCalendar.progress.longestDuration", comment: ""),
                    value: "\(maxDuration())",
                    unit: NSLocalizedString("common.unit.minute", comment: ""),
                    icon: "trophy.fill",
                    color: .orange
                )
            }

            // 进步指标 - 上升趋势
            if totalCountThisMonth > 0 {
                HStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .foregroundColor(.blue)
                        .font(.system(size: 16))

                    Text(String(format: NSLocalizedString("freeCalendar.progress.accumulatedKeepGoingFormat", comment: ""), formatDuration(totalDurationThisMonth)))
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 4)
            }
        }
    }

    // 进度卡片组件
    private func progressCard(title: String, value: String, unit: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            // 图标和标题
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14))
                    .foregroundColor(color)

                Text(title)
                    .font(.footnote)
                    .foregroundColor(.secondary)
            }

            // 数值和单位
            HStack(alignment: .lastTextBaseline, spacing: 2) {
                Text(value)
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 2)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }

    // 格式化月份
    private func monthString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = NSLocalizedString("month_format", comment: "Format for year and month")
        return formatter.string(from: date)
    }

    // 格式化持续时间
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            return String(format: NSLocalizedString("common.durationFormat.hoursMinutes", comment: ""), hours, minutes)
        } else {
            return String(format: NSLocalizedString("common.durationFormat.minutes", comment: ""), minutes)
        }
    }

    // 计算达到30分钟门槛的次数
    private func reachedThreshold() -> Int {
        return currentMonthData.filter { $0.data.totalDuration >= 30 * 60 }.count
    }

    // 计算最长专注时间 (修改后的逻辑)
    private func maxDuration() -> Int {
        let calendar = Calendar.current
        guard let monthInterval = calendar.dateInterval(of: .month, for: selectedMonth) else {
            return 0 // 如果无法获取月份区间，返回0
        }

        // 直接获取所有会话，过滤月份和自由模式，然后取最大单次时长
        let maxSingleDuration = dataStore.getXMomentSessionsUnambiguous()
            .filter { session in
                session.isFreeMode && // 确保是自由模式
                session.startTime >= monthInterval.start && // 确保在所选月份内
                session.startTime < monthInterval.end
            }
            .map { $0.duration } // 提取单次会话时长
            .max() ?? 0 // 找到最大值，如果无会话则返回0

        return Int(maxSingleDuration / 60) // 返回分钟数
    }
}

// 预览
struct FreeModeCalendarView_Previews: PreviewProvider {
    static var previews: some View {
        FreeModeCalendarView()
            .environmentObject(DataStore.shared)
    }
}
