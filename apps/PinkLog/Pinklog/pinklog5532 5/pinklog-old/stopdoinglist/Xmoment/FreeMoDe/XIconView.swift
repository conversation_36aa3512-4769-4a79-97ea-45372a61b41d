import SwiftUI

struct XIconView: View {
    var showBreathingEffect: Bool = false
    @State private var breathScale: CGFloat = 1.0
    
    // 冷静紫色
    private let calmPurpleColor = Color(red: 0.55, green: 0.45, blue: 0.85)
    
    var body: some View {
        ZStack {
            // 四个点组成X形状
            Circle()
                .fill(showBreathingEffect ? calmPurpleColor : Color.primary)
                .frame(width: 4, height: 4)
                .offset(x: -4, y: -4)
            
            Circle()
                .fill(showBreathingEffect ? calmPurpleColor : Color.primary)
                .frame(width: 4, height: 4)
                .offset(x: 4, y: -4)
            
            Circle()
                .fill(showBreathingEffect ? calmPurpleColor : Color.primary)
                .frame(width: 4, height: 4)
                .offset(x: -4, y: 4)
            
            Circle()
                .fill(showBreathingEffect ? calmPurpleColor : Color.primary)
                .frame(width: 4, height: 4)
                .offset(x: 4, y: 4)
        }
        .scaleEffect(showBreathingEffect ? breathScale : 1.0)
        .onAppear {
            if showBreathingEffect {
                // 启动呼吸动画
                withAnimation(
                    Animation.easeInOut(duration: 1.5)
                        .repeatForever(autoreverses: true)
                ) {
                    breathScale = 1.2
                }
            }
        }
    }
}

struct XIconView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            XIconView()
                .frame(width: 32, height: 32)
                .background(Color.gray.opacity(0.2))
                .clipShape(Circle())
            
            XIconView(showBreathingEffect: true)
                .frame(width: 32, height: 32)
                .background(Color.gray.opacity(0.2))
                .clipShape(Circle())
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
