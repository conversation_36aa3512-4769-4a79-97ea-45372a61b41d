import SwiftUI
import Charts
import StopDoingListKit

// 统计卡片样式
struct StatCardStyle: ViewModifier {
    var gradient: LinearGradient

    func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(gradient, lineWidth: 2)
            )
    }
}

extension View {
    func statCard(gradient: LinearGradient) -> some View {
        self.modifier(StatCardStyle(gradient: gradient))
    }
}

// 统计仪表盘视图
struct XMomentDashboardView: View {
    @EnvironmentObject var dataStore: DataStore
    @State private var animateStats = false

    // 图表颜色
    private let standardModeColor = Color.blue
    private let freeModeColor = Color.purple
    private let overclockColor = Color.red

    // Localized Keys - Define keys here for clarity or fetch dynamically
    let standardModeKey = "view.xmomentDashboard.type.standardMode"
    let freeModeKey = "view.xmomentDashboard.type.freeMode"
    let overclockIncludedKey = "view.xmomentDashboard.type.overclockIncluded"
    let overclockTimeKey = "view.xmomentDashboard.type.overclockTime"
    let overallAverageKey = "view.xmomentDashboard.type.overallAverage"
    let longestRecordKey = "view.xmomentDashboard.type.longestRecord"
    let shortestRecordKey = "view.xmomentDashboard.type.shortestRecord"
    let overclockRecordKey = "view.xmomentDashboard.type.overclockRecord"

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 顶部总览卡片
                overviewCard

                // 专注次数统计卡片
                focusSessionsCard

                // 专注时长统计卡片
                focusDurationCard

                // 中断统计卡片
                interruptionsCard

                // 平均专注时长卡片
                averageDurationCard

                // 最长/最短专注记录卡片
                extremeDurationsCard
            }
            .padding()
            .onAppear {
                // 添加一点延迟以便视图完成加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        animateStats = true
                    }
                }
            }
        }
    }

    // 顶部统计总览
    private var overviewCard: some View {
        let standardSessions = dataStore.getXMomentSessionsUnambiguous().filter { !$0.isFreeMode }
        let freeSessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.isFreeMode }
        let totalSessions = standardSessions.count + freeSessions.count
        let totalXU = dataStore.getTotalXUnits()

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.overview.title", comment: "Overview card title"))
                .font(.headline)
                .foregroundColor(.primary)

            HStack(spacing: 20) {
                VStack {
                    Text("\(totalSessions)")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    Text(NSLocalizedString("view.xmomentDashboard.overview.totalSessions", comment: "Label for total focus sessions count"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Divider().frame(height: 40)

                VStack {
                    Text(formatTotalDuration(dataStore.getTotalXMomentDuration()))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    Text(NSLocalizedString("view.xmomentDashboard.overview.totalDuration", comment: "Label for total focus duration"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Divider().frame(height: 40)

                VStack {
                    Text(String(format: "%.1f", totalXU)) // Keep XU format as is
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    Text(NSLocalizedString("view.xmomentDashboard.overview.totalXu", comment: "Label for total XU earned"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .opacity(animateStats ? 1 : 0)
            .scaleEffect(animateStats ? 1 : 0.8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // 专注次数统计卡片
    private var focusSessionsCard: some View {
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        let standardSessions = allSessions.filter { !$0.isFreeMode }
        let freeSessions = allSessions.filter { $0.isFreeMode }
        let overclockSessions = allSessions.filter { $0.overclockDuration > 0 }
        let standardCount = standardSessions.count
        let freeCount = freeSessions.count
        let overclockCount = overclockSessions.count
        let totalCount = standardCount + freeCount

        // Use keys for identification
        let data: [SessionTypeCount] = [
            SessionTypeCount(typeKey: standardModeKey, count: standardCount),
            SessionTypeCount(typeKey: freeModeKey, count: freeCount),
            SessionTypeCount(typeKey: overclockIncludedKey, count: overclockCount)
        ]

        let localizedStandardMode = NSLocalizedString(standardModeKey, comment: "Session type: Standard Mode")
        let localizedFreeMode = NSLocalizedString(freeModeKey, comment: "Session type: Free Mode")
        let localizedOverclockIncluded = NSLocalizedString(overclockIncludedKey, comment: "Session type: Included Overclock")

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.focusSessions.title", comment: "Focus sessions card title"))
                .font(.headline)
                .foregroundColor(.primary)

            if totalCount > 0 {
                Chart {
                    ForEach(data) { item in
                        let localizedType = NSLocalizedString(item.typeKey, comment: "Session type")
                        SectorMark(
                            angle: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.count", comment: "Chart label: Count"), item.count),
                            innerRadius: .ratio(0.5),
                            angularInset: 1.5
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.modeType", comment: "Chart label: Mode Type"), localizedType))
                        .annotation(position: .overlay) {
                            if item.count > 0 {
                                Text(String(format: NSLocalizedString("view.xmomentDashboard.focusSessions.annotationFormat", comment: "Annotation format: Count times. Parameter: count"), String(item.count)))
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .fontWeight(.bold)
                            }
                        }
                    }
                }
                .chartForegroundStyleScale([
                    localizedStandardMode: standardModeColor,
                    localizedFreeMode: freeModeColor,
                    localizedOverclockIncluded: overclockColor
                ])
                .chartLegend(.hidden)
                .frame(height: 200)
                .padding(.vertical, 10)
                .opacity(animateStats ? 1 : 0)
                .scaleEffect(animateStats ? 1 : 0.8)

                HStack(spacing: 20) {
                    ForEach(data) { item in
                        let localizedType = NSLocalizedString(item.typeKey, comment: "Session type")
                        HStack(spacing: 4) {
                            Circle()
                                .fill(item.typeKey == standardModeKey ? standardModeColor :
                                      item.typeKey == freeModeKey ? freeModeColor : overclockColor)
                                .frame(width: 10, height: 10)

                            Text(localizedType)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(String(format: NSLocalizedString("view.xmomentDashboard.focusSessions.legendFormat", comment: "Legend format: Count times. Parameter: count"), String(item.count)))
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                }
                .padding(.horizontal)
            } else {
                Text(NSLocalizedString("view.xmomentDashboard.noFocusData", comment: "Message when no focus data exists"))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .statCard(gradient: LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing))
    }

    // 专注时长统计卡片
    private var focusDurationCard: some View {
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        let standardDuration = allSessions.filter { !$0.isFreeMode }.reduce(0) { $0 + $1.duration }
        let freeDuration = allSessions.filter { $0.isFreeMode }.reduce(0) { $0 + $1.duration }
        let overclockDuration = allSessions.reduce(0) { $0 + $1.overclockDuration }
        // 使用 dataStore 获取总时长，以保证与 overviewCard 一致
        let totalDuration = dataStore.getTotalXMomentDuration()

        // 注意：这里的 totalDuration 仅用于判断是否有数据和显示下方的总标签
        // 各个部分的 standardDuration, freeDuration, overclockDuration 仍按原样计算用于图表绘制

        let data: [DurationType] = [
            DurationType(typeKey: standardModeKey, duration: standardDuration),
            DurationType(typeKey: freeModeKey, duration: freeDuration),
            DurationType(typeKey: overclockTimeKey, duration: overclockDuration)
        ]

        let localizedStandardMode = NSLocalizedString(standardModeKey, comment: "Session type: Standard Mode")
        let localizedFreeMode = NSLocalizedString(freeModeKey, comment: "Session type: Free Mode")
        let localizedOverclockTime = NSLocalizedString(overclockTimeKey, comment: "Duration type: Overclock Time")

        // 假设存在此本地化键，用于图表X轴标签
        let durationMinutesKey = "view.xmomentDashboard.chartLabel.durationMinutes"
        let localizedDurationMinutes = NSLocalizedString(durationMinutesKey, comment: "Chart label: Duration (Minutes)")

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.focusDuration.title", comment: "Focus duration card title"))
                .font(.headline)
                .foregroundColor(.primary)

            // 使用从 dataStore 获取的 totalDuration 判断是否有数据
            if totalDuration > 0 {
                Chart {
                    ForEach(data) { item in
                        let localizedType = NSLocalizedString(item.typeKey, comment: "Duration type")
                        BarMark(
                            // X轴使用分钟作为单位
                            x: .value(localizedDurationMinutes, item.duration / 60),
                            y: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.type", comment: "Chart label: Type"), localizedType)
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.modeType", comment: "Chart label: Mode Type"), localizedType))
                        .annotation(position: .trailing) {
                            // 注解仍然使用 formatTotalDuration 格式化各自精确时长
                            Text(formatTotalDuration(item.duration))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .chartForegroundStyleScale([
                    localizedStandardMode: standardModeColor,
                    localizedFreeMode: freeModeColor,
                    localizedOverclockTime: overclockColor
                ])
                .frame(height: 150)
                .padding(.vertical, 10)
                .opacity(animateStats ? 1 : 0)
                .scaleEffect(animateStats ? 1 : 0.8)

                // 下方的总时长标签也使用从 dataStore 获取并格式化的 totalDuration
                Text(String(format: NSLocalizedString("view.xmomentDashboard.focusDuration.totalFormat", comment: "Total focus duration label. Parameter: formatted duration string"), formatTotalDuration(totalDuration)))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
            } else {
                Text(NSLocalizedString("view.xmomentDashboard.noDurationData", comment: "Message when no duration data exists"))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .statCard(gradient: LinearGradient(colors: [.green, .blue], startPoint: .leading, endPoint: .trailing))
    }

    // 中断统计卡片
    private var interruptionsCard: some View {
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        let interruptedSessions = allSessions.filter { $0.isInterrupted }
        let totalSessions = allSessions.count
        let interruptedCount = interruptedSessions.count
        let _ = totalSessions - interruptedCount

        // 计算中断率
        let interruptionRate = totalSessions > 0 ? Double(interruptedCount) / Double(totalSessions) : 0

        // 计算不同模式的中断率
        let standardInterrupted = allSessions.filter { !$0.isFreeMode && $0.isInterrupted }.count
        let standardTotal = allSessions.filter { !$0.isFreeMode }.count
        let standardRate = standardTotal > 0 ? Double(standardInterrupted) / Double(standardTotal) : 0

        let freeInterrupted = allSessions.filter { $0.isFreeMode && $0.isInterrupted }.count
        let freeTotal = allSessions.filter { $0.isFreeMode }.count
        let freeRate = freeTotal > 0 ? Double(freeInterrupted) / Double(freeTotal) : 0

        let localizedStandardMode = NSLocalizedString(standardModeKey, comment: "Session type: Standard Mode")
        let localizedFreeMode = NSLocalizedString(freeModeKey, comment: "Session type: Free Mode")

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.interruptions.title", comment: "Interruptions card title"))
                .font(.headline)
                .foregroundColor(.primary)

            HStack(spacing: 20) {
                VStack {
                    Text("\(interruptedCount)")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    Text(NSLocalizedString("view.xmomentDashboard.interruptions.totalCount", comment: "Label for total interruptions count"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                VStack {
                    Text(String(format: NSLocalizedString("view.xmomentDashboard.interruptions.rateFormat", comment: "Interruption rate format string. Parameter: rate percentage"), interruptionRate * 100))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    Text(NSLocalizedString("view.xmomentDashboard.interruptions.totalRate", comment: "Label for total interruption rate"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 10)
            .opacity(animateStats ? 1 : 0)
            .scaleEffect(animateStats ? 1 : 0.8)

            // 中断率比较
            if standardTotal > 0 || freeTotal > 0 {
                VStack(alignment: .leading, spacing: 8) {
                    Text(NSLocalizedString("view.xmomentDashboard.interruptions.modeRateComparisonTitle", comment: "Title for mode interruption rate comparison section"))
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 15) {
                        // 标准模式中断率
                        VStack(alignment: .leading, spacing: 4) {
                            Text(localizedStandardMode)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            ProgressView(value: standardRate)
                                .progressViewStyle(LinearProgressViewStyle(tint: standardModeColor))
                                .frame(height: 6)

                            Text(String(format: NSLocalizedString("view.xmomentDashboard.interruptions.modeRateDetailFormat", comment: "Mode interruption rate detail format. Parameters: rate percentage, interrupted count, total count"), standardRate * 100, String(standardInterrupted), String(standardTotal)))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        // 自由模式中断率
                        VStack(alignment: .leading, spacing: 4) {
                            Text(localizedFreeMode)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            ProgressView(value: freeRate)
                                .progressViewStyle(LinearProgressViewStyle(tint: freeModeColor))
                                .frame(height: 6)

                            Text(String(format: NSLocalizedString("view.xmomentDashboard.interruptions.modeRateDetailFormat", comment: "Mode interruption rate detail format. Parameters: rate percentage, interrupted count, total count"), freeRate * 100, String(freeInterrupted), String(freeTotal)))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.top, 10)
            }
        }
        .statCard(gradient: LinearGradient(colors: [.orange, .red], startPoint: .leading, endPoint: .trailing))
    }

    // 平均专注时长卡片
    private var averageDurationCard: some View {
        let allSessions = dataStore.getXMomentSessionsUnambiguous()

        // 总体平均
        let totalCount = allSessions.count
        let totalDuration = allSessions.reduce(0) { $0 + $1.duration }
        let averageDuration = totalCount > 0 ? totalDuration / Double(totalCount) : 0

        // 标准模式平均
        let standardSessions = allSessions.filter { !$0.isFreeMode }
        let standardCount = standardSessions.count
        let standardDuration = standardSessions.reduce(0) { $0 + $1.duration }
        let standardAverage = standardCount > 0 ? standardDuration / Double(standardCount) : 0

        // 自由模式平均
        let freeSessions = allSessions.filter { $0.isFreeMode }
        let freeCount = freeSessions.count
        let freeDuration = freeSessions.reduce(0) { $0 + $1.duration }
        let freeAverage = freeCount > 0 ? freeDuration / Double(freeCount) : 0

        let data: [DurationType] = [
            DurationType(typeKey: overallAverageKey, duration: averageDuration),
            DurationType(typeKey: standardModeKey, duration: standardAverage),
            DurationType(typeKey: freeModeKey, duration: freeAverage)
        ]

        let localizedOverallAverage = NSLocalizedString(overallAverageKey, comment: "Average type: Overall")
        let localizedStandardMode = NSLocalizedString(standardModeKey, comment: "Session type: Standard Mode")
        let localizedFreeMode = NSLocalizedString(freeModeKey, comment: "Session type: Free Mode")

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.averageDuration.title", comment: "Average duration card title"))
                .font(.headline)
                .foregroundColor(.primary)

            if totalCount > 0 {
                Chart {
                    ForEach(data) { item in
                        let localizedType = NSLocalizedString(item.typeKey, comment: "Average type")
                        BarMark(
                            x: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.durationMinutes", comment: "Chart label: Duration (Minutes)"), item.duration / 60),
                            y: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.type", comment: "Chart label: Type"), localizedType)
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.modeType", comment: "Chart label: Mode Type"), localizedType))
                        .annotation(position: .trailing) {
                            Text(formatTotalDuration(item.duration)) // Use formatTotalDuration here as well for consistency
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .chartForegroundStyleScale([
                    localizedOverallAverage: Color.gray,
                    localizedStandardMode: standardModeColor,
                    localizedFreeMode: freeModeColor
                ])
                .frame(height: 120)
                .padding(.vertical, 10)
                .opacity(animateStats ? 1 : 0)
                .scaleEffect(animateStats ? 1 : 0.8)
            } else {
                Text(NSLocalizedString("view.xmomentDashboard.noFocusData", comment: "Message when no focus data exists"))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .statCard(gradient: LinearGradient(colors: [.blue, .green], startPoint: .leading, endPoint: .trailing))
    }

    // 最长/最短专注记录
    private var extremeDurationsCard: some View {
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        let maxSession = allSessions.max(by: { $0.duration < $1.duration })
        let minSession = allSessions.filter { $0.duration > 0 }.min(by: { $0.duration < $1.duration })

        // 最长超频记录
        let maxOverclockSession = allSessions.filter { $0.overclockDuration > 0 }.max(by: { $0.overclockDuration < $1.overclockDuration })

        // 创建图表数据
        var chartData: [RecordData] = []

        if let maxSession = maxSession {
            chartData.append(RecordData(typeKey: longestRecordKey, duration: maxSession.duration))
        }

        if let maxOverclockSession = maxOverclockSession {
            chartData.append(RecordData(typeKey: overclockRecordKey, duration: maxOverclockSession.overclockDuration))
        }

        if let minSession = minSession {
            chartData.append(RecordData(typeKey: shortestRecordKey, duration: minSession.duration))
        }

        let localizedLongest = NSLocalizedString(longestRecordKey, comment: "Record type: Longest")
        let localizedShortest = NSLocalizedString(shortestRecordKey, comment: "Record type: Shortest")
        let localizedOverclock = NSLocalizedString(overclockRecordKey, comment: "Record type: Overclock")

        return VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("view.xmomentDashboard.extremeDurations.title", comment: "Extreme durations card title"))
                .font(.headline)
                .foregroundColor(.primary)

            // 添加水平条形图
            if !chartData.isEmpty {
                Chart {
                    ForEach(chartData) { item in
                        let localizedType = NSLocalizedString(item.typeKey, comment: "Record type")
                        BarMark(
                            x: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.durationMinutes", comment: "Chart label: Duration (Minutes)"), item.duration / 60),
                            y: .value(NSLocalizedString("view.xmomentDashboard.chartLabel.type", comment: "Chart label: Type"), localizedType)
                        )
                        .foregroundStyle(by: .value(NSLocalizedString("view.xmomentDashboard.extremeDurations.chartLabel.recordType", comment: "Chart label: Record Type"), localizedType))
                        .annotation(position: .trailing) {
                            Text(formatTotalDuration(item.duration)) // Use formatTotalDuration here too
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .chartForegroundStyleScale([
                    localizedLongest: Color.yellow,
                    localizedOverclock: Color.orange,
                    localizedShortest: Color.green
                ])
                .frame(height: 120)
                .padding(.vertical, 10)
                .opacity(animateStats ? 1 : 0)
                .scaleEffect(animateStats ? 1 : 0.8)
            }

            // 详情卡片区域
            VStack(spacing: 10) {
                if let maxSession = maxSession {
                    let mode = maxSession.isFreeMode ? freeModeKey : standardModeKey
                    recordDetailCard(
                        titleKey: "view.xmomentDashboard.extremeDurations.longestRecordTitle",
                        duration: maxSession.duration,
                        date: maxSession.startTime,
                        subtitleKey: mode,
                        iconName: "trophy.fill",
                        color: .yellow
                    )
                }

                if let maxOverclockSession = maxOverclockSession {
                    recordDetailCard(
                        titleKey: "view.xmomentDashboard.extremeDurations.overclockRecordTitle",
                        duration: maxOverclockSession.overclockDuration,
                        date: maxOverclockSession.startTime,
                        subtitleText: getOverclockFactorText(maxOverclockSession.overclockFactor), // Formatted text directly
                        iconName: "bolt.fill",
                        color: .orange
                    )
                }

                if let minSession = minSession {
                    let mode = minSession.isFreeMode ? freeModeKey : standardModeKey
                    recordDetailCard(
                        titleKey: "view.xmomentDashboard.extremeDurations.shortestRecordTitle",
                        duration: minSession.duration,
                        date: minSession.startTime,
                        subtitleKey: mode,
                        iconName: "stopwatch.fill",
                        color: .green
                    )
                }
            }
        }
        .statCard(gradient: LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing))
    }

    // 记录详情卡片组件 - Modified to accept keys
    private func recordDetailCard(titleKey: String, duration: TimeInterval, date: Date, subtitleKey: String? = nil, subtitleText: String? = nil, iconName: String, color: Color) -> some View {
        let title = NSLocalizedString(titleKey, comment: "Record detail card title")
        let subtitle: String
        if let key = subtitleKey {
            subtitle = NSLocalizedString(key, comment: "Record detail card subtitle")
        } else if let text = subtitleText {
            subtitle = text // Already formatted/localized
        } else {
            subtitle = "" // Should not happen with current usage
        }

        let dateString = formatDate(date)
        let detailText = String(format: NSLocalizedString("view.xmomentDashboard.extremeDurations.detailFormat", comment: "Record detail card detail format. Parameters: date, subtitle"), dateString, subtitle)

        return HStack {
            Image(systemName: iconName)
                .foregroundColor(color)
                .font(.system(size: 16))
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(formatTotalDuration(duration))
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.primary)

                Text(detailText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(color.opacity(0.1))
        .cornerRadius(8)
        .opacity(animateStats ? 1 : 0)
        .offset(y: animateStats ? 0 : 10)
    }

    // 格式化持续时间 - 使用本地化格式
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return String(format: NSLocalizedString("common.durationFormat.hoursMinutes", comment: "Duration format: hours and minutes. Parameters: hours, minutes"), hours, minutes)
        } else if minutes > 0 {
            return String(format: NSLocalizedString("common.durationFormat.minutes", comment: "Duration format: minutes. Parameter: minutes"), minutes)
        } else {
            return String(format: NSLocalizedString("common.durationFormat.seconds", comment: "Duration format: seconds. Parameter: seconds"), seconds)
        }
    }

    // 总专注时长格式化函数 - 使用本地化格式
    private func formatTotalDuration(_ duration: TimeInterval) -> String {
        // 计算总分钟数
        let totalMinutes = duration / 60
        // 四舍五入到最接近的小时
        let roundedHours = Int(round(totalMinutes / 60))

        if roundedHours > 0 {
            return String(format: NSLocalizedString("common.durationFormat.hoursRounded", comment: "Total duration format: rounded hours. Parameter: hours"), roundedHours)
        } else {
            // 如果不足1小时，显示分钟
            return String(format: NSLocalizedString("common.durationFormat.minutesRounded", comment: "Total duration format: rounded minutes. Parameter: minutes"), Int(round(totalMinutes)))
        }
    }

    // 格式化日期 - 使用本地化格式
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = NSLocalizedString("common.dateFormat.monthDayTime", comment: "Date format: Month, day, time (e.g., 08-15 14:30)")
        return formatter.string(from: date)
    }

    // 格式化超频系数 - 使用本地化格式
    private func getOverclockFactorText(_ factor: Double) -> String {
        let formattedFactor = String(format: "%.1f", factor)
        return String(format: NSLocalizedString("view.xmomentDashboard.extremeDurations.overclockFactorFormat", comment: "Overclock factor text format. Parameter: factor value"), formattedFactor)
    }
}

// 数据模型 - Modified to use keys
struct SessionTypeCount: Identifiable {
    var id = UUID()
    var typeKey: String // Store the localization key
    var count: Int
}

struct DurationType: Identifiable {
    var id = UUID()
    var typeKey: String // Store the localization key
    var duration: TimeInterval
}

// 添加记录数据模型 - Modified to use keys
struct RecordData: Identifiable {
    var id = UUID()
    var typeKey: String // Store the localization key
    var duration: TimeInterval
}

// 预览
struct XMomentDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        XMomentDashboardView()
            .environmentObject(DataStore.shared)
            .previewLayout(.sizeThatFits)
            // .environment(\\.locale, .init(identifier: "zh-Hans"))
    }
}