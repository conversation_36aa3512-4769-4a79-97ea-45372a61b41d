import Foundation
import StopDoingListKit

// 这个扩展提供了明确的方法引用，解决了方法歧义问题
extension DataStore {
    // 获取 X Moment 会话
    func getXMomentSessionsUnambiguous() -> [XMomentSession] {
        // 直接访问文件系统来获取会话数据
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentSessionsURL = documentsDirectory.appendingPathComponent("xmoment_sessions.json")
        
        do {
            if fileManager.fileExists(atPath: xMomentSessionsURL.path) {
                let data = try Data(contentsOf: xMomentSessionsURL)
                return try JSONDecoder().decode([XMomentSession].self, from: data)
            }
        } catch {
            print("加载 X Moment 会话数据失败: \(error)")
        }
        return []
    }
    
    // 获取 X Moment 用户统计数据
    func getXMomentUserStatsUnambiguous() -> XMomentUserStats {
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentUserStatsURL = documentsDirectory.appendingPathComponent("xmoment_user_stats.json")
        
        do {
            if fileManager.fileExists(atPath: xMomentUserStatsURL.path) {
                let data = try Data(contentsOf: xMomentUserStatsURL)
                return try JSONDecoder().decode(XMomentUserStats.self, from: data)
            }
        } catch {
            print("加载 X Moment 用户统计数据失败: \(error)")
        }
        return XMomentUserStats()
    }
    
    // 获取 X Moment 总次数
    func getTotalXMomentCountUnambiguous() -> Int {
        let stats = getXMomentUserStatsUnambiguous()
        return stats.totalCompletedCount + stats.totalInterruptedCount
    }
    
    // 获取总 XU 值
    func getTotalXUnitsUnambiguous() -> Double {
        return getXMomentSessionsUnambiguous().reduce(0) { $0 + ($1.progressXU) }
    }
    
    // 获取当前连续专注天数
    func getXMomentStreakDaysUnambiguous() -> Int {
        return getXMomentUserStatsUnambiguous().currentStreak
    }
    
    // 更新里程碑达成状态
    func updateAchievedMilestonesUnambiguous(_ milestoneIDs: Set<String>) {
        var stats = getXMomentUserStatsUnambiguous()
        stats.achievedMilestoneIDs = milestoneIDs
        
        // 保存更新后的用户统计数据
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentUserStatsURL = documentsDirectory.appendingPathComponent("xmoment_user_stats.json")
        
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(stats)
            try data.write(to: xMomentUserStatsURL)
        } catch {
            print("保存 X Moment 用户统计数据失败: \(error)")
        }
    }
    
    // 获取每日提示
    func getDailyTipUnambiguous() -> String {
        let tips = [
            "Tip 1",
            "Tip 2",
            "Tip 3",
            "Tip 4",
            "Tip 5"
        ]
        let randomIndex = Int.random(in: 0..<tips.count)
        return tips[randomIndex]
    }
    
    // 获取自由模式的个人最佳时长，排除当前会话
    func getFreeModePersonalBestDurationUnambiguous(excludingCurrent currentDuration: TimeInterval) -> TimeInterval {
        let sessions = getXMomentSessionsUnambiguous()
        let freeModeSessions = sessions.filter { $0.isFreeMode && $0.duration != currentDuration }
        
        if let bestSession = freeModeSessions.max(by: { $0.duration < $1.duration }) {
            return bestSession.duration
        }
        
        return 0
    }
}
