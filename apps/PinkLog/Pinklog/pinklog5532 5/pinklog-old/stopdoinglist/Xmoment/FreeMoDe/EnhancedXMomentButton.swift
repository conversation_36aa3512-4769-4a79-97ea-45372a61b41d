import SwiftUI

struct EnhancedXMomentButton: View {
    var action: () -> Void

    // 状态变量
    @State private var isPressed = false
    @State private var breathScale: CGFloat = 1.0
    @State private var breathOpacity: Double = 0.7
    @State private var glowOpacity: Double = 0.0
    @State private var rotationAngle: Double = 0
    @State private var innerRotationAngle: Double = 0
    @State private var particleScale: CGFloat = 0.0
    @State private var particleOpacity: Double = 0.0

    // 环境变量
    @Environment(\.colorScheme) private var colorScheme

    // 颜色定义
    private let primaryColor = Color(red: 0.55, green: 0.45, blue: 0.85) // 冷静紫色
    private let secondaryColor = Color(red: 0.65, green: 0.55, blue: 0.95) // 亮紫色
    private let accentColor = Color(red: 0.9, green: 0.5, blue: 0.9) // 粉紫色

    // 动画时间
    private let breathDuration: Double = 3.0

    var body: some View {
        Button(action: {
            // 增强触觉反馈 - 使用更精细的触觉
            let generator = UIImpactFeedbackGenerator(style: .rigid)
            generator.impactOccurred(intensity: 0.8)

            // 触发粒子动画
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                particleScale = 1.5
                particleOpacity = 1.0
                glowOpacity = 1.0
                rotationAngle += 90
            }

            // 延迟执行操作，让动画有时间展示
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                action()
            }

            // 重置粒子动画
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                withAnimation(.easeOut(duration: 0.5)) {
                    particleScale = 0.0
                    particleOpacity = 0.0
                    glowOpacity = 0.0
                }
            }
        }) {
            ZStack {
                // 外部发光效果
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [primaryColor.opacity(0.8), primaryColor.opacity(0.0)]),
                            center: .center,
                            startRadius: 5,
                            endRadius: 25
                        )
                    )
                    .blur(radius: 8)
                    .opacity(glowOpacity)
                    .frame(width: 50, height: 50)

                // 粒子效果
                ZStack {
                    ForEach(0..<6) { i in
                        Circle()
                            .fill(accentColor)
                            .frame(width: 4, height: 4)
                            .offset(
                                x: CGFloat(cos(Double(i) * .pi / 3) * 20),
                                y: CGFloat(sin(Double(i) * .pi / 3) * 20)
                            )
                            .opacity(particleOpacity * 0.7)
                    }
                }
                .scaleEffect(particleScale)

                // 呼吸效果光晕
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                primaryColor.opacity(0.2),
                                primaryColor.opacity(0.05)
                            ]),
                            center: .center,
                            startRadius: 10,
                            endRadius: 25
                        )
                    )
                    .frame(width: 48, height: 48)
                    .scaleEffect(breathScale)
                    .opacity(breathOpacity)

                // 主按钮背景
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                secondaryColor.opacity(colorScheme == .dark ? 0.9 : 0.8),
                                primaryColor.opacity(colorScheme == .dark ? 0.7 : 0.6)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.6),
                                        Color.white.opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                    .frame(width: 42, height: 42)

                // 内部装饰环
                Circle()
                    .stroke(
                        AngularGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.8),
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.8)
                            ]),
                            center: .center
                        ),
                        lineWidth: 1
                    )
                    .frame(width: 30, height: 30)
                    .rotationEffect(.degrees(rotationAngle))

                // 内部X图标 - 使用优雅的线条
                ZStack {
                    // 创建X形状的两条线
                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 2, height: 16)
                        .cornerRadius(1)
                        .rotationEffect(.degrees(45))

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 2, height: 16)
                        .cornerRadius(1)
                        .rotationEffect(.degrees(-45))
                }
                .frame(width: 20, height: 20)
                .rotationEffect(.degrees(innerRotationAngle))

                // 移除了悬停效果，因为iOS不支持悬停交互
            }
            .scaleEffect(isPressed ? 0.92 : 1.0)
            .rotationEffect(.degrees(isPressed ? 5 : 0))
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            // 启动呼吸动画
            withAnimation(
                Animation.easeInOut(duration: breathDuration)
                    .repeatForever(autoreverses: true)
            ) {
                breathScale = 1.15
                breathOpacity = 0.9
            }

            // 启动内部旋转动画
            withAnimation(
                Animation.linear(duration: 20)
                    .repeatForever(autoreverses: false)
            ) {
                innerRotationAngle = 360
            }
        }
        // 注意：在iOS上不使用onHover，因为iOS不支持悬停交互
        // 我们保留isHovered状态变量，但不使用onHover来设置它
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    if !isPressed {
                        let generator = UIImpactFeedbackGenerator(style: .soft)
                        generator.impactOccurred(intensity: 0.5)
                        isPressed = true
                    }
                }
                .onEnded { _ in
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        isPressed = false
                    }
                }
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        // 移除了isHovered的动画绑定，因为我们不再使用onHover
        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: rotationAngle)
    }
}

// 注意：移除了onHover扩展，因为SwiftUI已经提供了这个方法

struct EnhancedXMomentButton_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 亮色模式预览
            EnhancedXMomentButton(action: {})
                .padding()
                .previewLayout(.sizeThatFits)
                .previewDisplayName("Light Mode")

            // 暗色模式预览
            EnhancedXMomentButton(action: {})
                .padding()
                .preferredColorScheme(.dark)
                .previewLayout(.sizeThatFits)
                .previewDisplayName("Dark Mode")
        }
    }
}
