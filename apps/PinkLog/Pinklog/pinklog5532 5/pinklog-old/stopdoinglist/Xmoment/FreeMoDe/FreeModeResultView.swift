import SwiftUI
import StopDoingListKit

// 按钮样式
fileprivate struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(10)
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.spring(), value: configuration.isPressed)
    }
}

fileprivate struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .background(Color.secondary.opacity(0.1))
            .foregroundColor(.primary)
            .cornerRadius(10)
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.spring(), value: configuration.isPressed)
    }
}

struct FreeModeResultView: View {
    let duration: TimeInterval
    var previousBest: TimeInterval
    let isNewRecord: Bool
    let totalXU: Double
    let baseXU: Double
    let bonusXU: Double
    var progressXU: Double
    var onSave: ((XMomentSession) -> Void)? = nil
    var session: XMomentSession? = nil

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var dataStore: DataStore

    // 初始化方法，接收会话和回调函数
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self.duration = session.duration

        // 从 DataStore 获取之前的最佳记录
        self.previousBest = DataStore.shared.getXMomentUserStatsUnambiguous().freeModeBestDuration

        // 如果当前会话就是最佳记录，则使用次佳记录作为比较基准
        if session.isPersonalBest && self.previousBest < session.duration {
            // 使用 DataStore 的方法获取次佳记录，排除当前会话
            self.previousBest = DataStore.shared.getFreeModePersonalBestDurationUnambiguous(excludingCurrent: session.duration)
        }

        self.isNewRecord = session.isPersonalBest

        // 计算XU值
        let (baseXU, bonusXU) = Self.calculateXU(duration: session.duration)
        self.baseXU = baseXU
        self.bonusXU = bonusXU

        // 计算进步奖励
        if self.previousBest > 0 && session.duration > self.previousBest {
            // 进步奖励：每超过5分钟增加0.5XU
            let improvementMinutes = (session.duration - self.previousBest) / 60
            let progressBonus = floor(improvementMinutes / 5) * 0.5
            self.progressXU = progressBonus
        } else {
            self.progressXU = session.progressXU
        }

        self.totalXU = baseXU + bonusXU + self.progressXU
    }

    // 兼容原有代码的初始化方法
    init(duration: TimeInterval, previousBest: TimeInterval, isNewRecord: Bool, totalXU: Double, baseXU: Double, bonusXU: Double, progressXU: Double) {
        self.duration = duration
        self.previousBest = previousBest
        self.isNewRecord = isNewRecord
        self.totalXU = totalXU
        self.baseXU = baseXU
        self.bonusXU = bonusXU
        self.progressXU = progressXU
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 1. 主标题：强调时间而非XU
                Text(NSLocalizedString("freeResult.title", comment: ""))
                    .font(.largeTitle.bold())
                    .foregroundColor(.primary)

                // 2. 专注时间（大字体）
                Text(formatDuration(duration))
                    .font(.system(size: 56, weight: .light))
                    .foregroundColor(.primary)

                // 3. 成就标志（如果有）
                if isNewRecord {
                    HStack {
                        Image(systemName: "trophy.fill")
                            .foregroundColor(.yellow)
                        Text(NSLocalizedString("freeResult.newRecordBanner", comment: ""))
                            .foregroundColor(.yellow)
                            .fontWeight(.bold)
                    }
                    .padding()
                    .background(Color.yellow.opacity(0.1))
                    .cornerRadius(10)
                }

                // 4. 进步指标
                if previousBest > 0 {
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString("freeResult.yourProgress", comment: ""))
                            .font(.headline)
                            .foregroundColor(.secondary)

                        HStack {
                            Image(systemName: "arrow.up.right")
                            Text(compareToPrevious(current: duration, previous: previousBest))
                        }
                        .foregroundColor(.green)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(10)
                }

                // 5. XU奖励（小字体，次要位置）
                VStack(alignment: .leading, spacing: 10) {
                    Text(NSLocalizedString("freeResult.rewardEarned", comment: ""))
                        .font(.headline)
                        .foregroundColor(.secondary)

                    HStack(alignment: .firstTextBaseline) {
                        Text("\(String(format: "%.1f", totalXU))")
                            .font(.title)
                            .foregroundColor(.purple)

                        Text("XU")
                            .font(.body)
                            .foregroundColor(.purple)
                    }

                    // 只有在有进步奖励时才显示详细分类
                    if progressXU > 0 {
                        Divider()

                        VStack(alignment: .leading, spacing: 5) {
                            Text(String(format: NSLocalizedString("freeResult.reward.baseFormat", comment: ""), baseXU))
                                .font(.caption)

                            if bonusXU > 0 {
                                Text(String(format: NSLocalizedString("freeResult.reward.milestoneFormat", comment: ""), bonusXU))
                                    .font(.caption)
                            }

                            Text(String(format: NSLocalizedString("freeResult.reward.progressFormat", comment: ""), progressXU))
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.purple.opacity(0.1))
                .cornerRadius(10)

                // 6. 鼓励性文字
                Text(getEncouragementMessage(duration: duration, isNewRecord: isNewRecord))
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()

                Spacer()

                // 7. 操作按钮
                HStack(spacing: 20) {
                    Button(NSLocalizedString("freeResult.button.again", comment: "")) {
                        // 重新开始自由模式
                        dismiss()
                        // 这里需要添加导航到新的自由模式会话的逻辑
                    }
                    .buttonStyle(SecondaryButtonStyle())

                    Button(NSLocalizedString("common.done", comment: "")) {
                        // 如果有会话和回调函数，则调用回调函数
                        if let session = session, let onSave = onSave {
                            var modifiedSession = session
                            modifiedSession.progressXU = progressXU
                            onSave(modifiedSession)
                        }
                        dismiss()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                }
                .padding()
            }
            .padding()
        }
        .navigationBarBackButtonHidden(true)
    }

    // 静态方法计算XU
    static func calculateXU(duration: TimeInterval) -> (baseXU: Double, bonusXU: Double) {
        var baseXU: Double = 0.0
        var bonusXU: Double = 0.0

        if duration < 30 * 60 {
            // 30分钟以下：每10分钟0.2XU
            baseXU = floor(duration / (10 * 60)) * 0.2

            // 小里程碑奖励
            if duration >= 5 * 60 { bonusXU += 0.1 }
            if duration >= 10 * 60 { bonusXU += 0.1 }
            if duration >= 15 * 60 { bonusXU += 0.2 }
            if duration >= 20 * 60 { bonusXU += 0.2 }
        } else {
            // 30分钟及以上：与标准模式相同，每15分钟1XU
            baseXU = floor(duration / (15 * 60))
            // 确保30分钟至少有2XU
            if baseXU < 2 {
                baseXU = 2
            }
        }

        return (baseXU, bonusXU)
    }

    // 静态方法创建结果视图
    static func create(from session: XMomentSession, previousBest: TimeInterval, progressXU: Double, onSave: @escaping (XMomentSession) -> Void) -> FreeModeResultView {
        var modifiedSession = session
        modifiedSession.progressXU = progressXU

        // 创建结果视图并设置之前的最佳记录
        var resultView = FreeModeResultView(session: modifiedSession, onSave: onSave)
        resultView.previousBest = previousBest

        return resultView
    }

    // 格式化时间
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // 简化的时间格式化（只显示分钟）
    private func formatDurationSimple(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        if minutes < 1 {
            return NSLocalizedString("common.duration.underMinute", comment: "")
        } else {
            return String(format: NSLocalizedString("common.duration.minutesFormat", comment: ""), minutes)
        }
    }

    // 与上次记录比较
    private func compareToPrevious(current: TimeInterval, previous: TimeInterval) -> String {
        let diff = current - previous
        let formattedCurrent = formatDurationSimple(current)
        let formattedDiff = formatDurationSimple(diff)

        if diff > 0 {
            let improvementPercent = (diff / previous) * 100
            if improvementPercent >= 100 {
                return String(format: NSLocalizedString("freeResult.comparison.improvedPercentFormat", comment: ""), formattedDiff, Int(improvementPercent))
            } else if improvementPercent >= 50 {
                return String(format: NSLocalizedString("freeResult.comparison.improvedSignificantFormat", comment: ""), formattedDiff)
            } else if improvementPercent >= 20 {
                return String(format: NSLocalizedString("freeResult.comparison.improvedNoticeableFormat", comment: ""), formattedDiff)
            } else {
                return String(format: NSLocalizedString("freeResult.comparison.improvedKeepGoingFormat", comment: ""), formattedDiff)
            }
        } else if diff < 0 {
            return String(format: NSLocalizedString("freeResult.comparison.lessKeepTryingFormat", comment: ""), formattedCurrent)
        } else {
            return NSLocalizedString("freeResult.comparison.sameStable", comment: "")
        }
    }

    // 鼓励性文字
    private func getEncouragementMessage(duration: TimeInterval, isNewRecord: Bool) -> String {
        let minutes = Int(duration / 60)
        let keys: [String]

        // 新记录的特殊鼓励
        if isNewRecord {
            keys = [
                "freeResult.encourage.newRecord.awesome",
                "freeResult.encourage.newRecord.breakthrough",
                "freeResult.encourage.newRecord.newHeight"
            ]
        } else {
            // 基于时长的鼓励
            if minutes < 5 {
                keys = [
                    "freeResult.encourage.under5.everyMinute",
                    "freeResult.encourage.under5.practice",
                    "freeResult.encourage.under5.goodStart"
                ]
            } else if minutes < 15 {
                keys = [
                    "freeResult.encourage.under15.goodTry",
                    "freeResult.encourage.under15.keepPracticing",
                    "freeResult.encourage.under15.strengthenWillpower"
                ]
            } else if minutes < 30 {
                keys = [
                    "freeResult.encourage.under30.nearThreshold",
                    "freeResult.encourage.under30.improving",
                    "freeResult.encourage.under30.goodFocus"
                ]
            } else if minutes < 45 {
                keys = [
                    "freeResult.encourage.under45.congratsThreshold",
                    "freeResult.encourage.under45.valuable",
                    "freeResult.encourage.under45.tryStandard"
                ]
            } else {
                keys = [
                    "freeResult.encourage.over45.deepFocus",
                    "freeResult.encourage.over45.outstanding",
                    "freeResult.encourage.over45.readyForStandard"
                ]
            }
        }
        return NSLocalizedString(keys.randomElement()!, comment: "Encouragement message")
    }
}

// 预览
struct FreeModeResultView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建模拟会话
        let mockSession = XMomentSession(
            id: UUID(),
            startTime: Date().addingTimeInterval(-35 * 60),
            endTime: Date(),
            duration: 35 * 60,
            mood: "高效",
            category: "工作",
            overclockFactor: 1.0,
            overclockDuration: 0,
            isInterrupted: false,
            isFreeMode: true,
            initialDuration: 60,
            isPersonalBest: true,
            progressXU: 0.5
        )

        FreeModeResultView(session: mockSession) { _ in
            // 预览中不需要实际保存
            print("Would save session in real app")
        }
        .environmentObject(DataStore.shared)
    }
}
