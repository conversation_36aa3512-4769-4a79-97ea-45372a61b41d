import SwiftUI
import Charts
import StopDoingListKit

// 主题系统
struct StatsViewTheme {
    // 主色系
    static let primary = Color.blue // 使用系统已有的强调色
    static let secondary = Color.gray.opacity(0.1)

    // 图表色阶（数据渐变）
    static let chartGradient = LinearGradient(
        colors: [Color.blue.opacity(0.7), Color.purple.opacity(0.9)],
        startPoint: .bottom,
        endPoint: .top
    )

    // 文字
    static let titleColor = Color.primary
    static let subtitleColor = Color.secondary
    static let labelColor = Color.secondary

    // 卡片
    static let cardBackground = Color(.systemBackground)
    static let cardShadow = Color.black.opacity(0.1)

    // 动态颜色 - 依据数值高低
    static func dynamicColor(for value: Double, range: ClosedRange<Double> = 0...1) -> Color {
        let normalized = min(max((value - range.lowerBound) / (range.upperBound - range.lowerBound), 0), 1)
        return Color(
            hue: 0.3 + 0.3 * (1 - normalized), // 从蓝到绿到红
            saturation: 0.7 + 0.3 * normalized,
            brightness: 0.9
        )
    }
}

// XMoment统计视图
struct XMomentStatsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataStore: DataStore
    @State private var selectedXMomentTimeRange: XMomentTimeRange = .month
    @State private var animateCharts = false
    @State private var selectedCategory: String? = nil
    @State private var calendarDisplayMonth: Date = Date()

    // 添加标签页选择状态
    @State private var selectedTab = 0

    // 添加导航相关状态
    @State private var selectedCalendarDate: Date? = nil
    @State private var selectedDateSessions: [XMomentSession] = []
    @State private var selectedDataPointDate: Date? = nil

    // Moved struct definition outside ViewBuilder
    struct DatedDay: Identifiable {
        let id = UUID()
        let date: Date
    }

    // 时间范围枚举
    enum XMomentTimeRange: String, CaseIterable, Identifiable {
        case week = "week"
        case month = "month"
        case year = "year"
        case all = "all"

        var id: String { self.rawValue }

        var days: Int {
            switch self {
            case .week: return 7
            case .month: return 30
            case .year: return 365
            case .all: return Int.max
            }
        }
    }

    // 获取时间范围的开始日期
    private var startDate: Date {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        switch selectedXMomentTimeRange {
        case .week:
            return calendar.date(byAdding: .day, value: -7, to: today)!
        case .month:
            return calendar.date(byAdding: .day, value: -30, to: today)!
        case .year:
            return calendar.date(byAdding: .day, value: -365, to: today)!
        case .all:
            return dataStore.getXMomentUserStatsUnambiguous().firstSessionDate ?? today
        }
    }

    // 卡片组件
    private func AnalyticsCard<Content: View>(
        title: String,
        subtitle: String? = nil,
        height: CGFloat? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(StatsViewTheme.titleColor)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(StatsViewTheme.subtitleColor)
                }
            }
            .padding(.horizontal)

            content()
                .if(height != nil) { view in
                    view.frame(height: height)
                }
        }
        .padding(.vertical)
        .background(StatsViewTheme.cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        .shadow(color: StatsViewTheme.cardShadow, radius: 10, x: 0, y: 5)
        .padding(.horizontal)
    }

    // 图表加载占位符
    private struct ChartPlaceholder: View {
        @State private var isAnimating = false

        var body: some View {
            VStack {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 48))
                    .foregroundColor(Color.gray.opacity(0.3))
                    .opacity(isAnimating ? 0.3 : 0.7)
                    .scaleEffect(isAnimating ? 1.1 : 1.0)

                Text("加载图表中...")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .onAppear {
                withAnimation(Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                    isAnimating = true
                }
            }
        }
    }

    // 视图过渡效果
    private func animatedTransition() -> AnyTransition {
        AnyTransition.opacity
            .combined(with: .move(edge: .bottom))
            .combined(with: .scale(scale: 0.95, anchor: .center))
            .animation(.spring(response: 0.3, dampingFraction: 0.7))
    }

    var body: some View {
        NavigationStack {
            ZStack { // 使用ZStack包裹所有内容
                VStack(spacing: 0) {
                    // 顶部标签选择器 - 修改为下拉菜单样式
                    HStack {
                        // 使用 Text 显示当前选中的标签
                        Text(selectedTabTitle)
                            .font(.title2.bold())
                            .foregroundColor(StatsViewTheme.primary)
                            .frame(maxWidth: .infinity, alignment: .leading) // 左对齐标题

                        Spacer() // 将 Picker 推到右侧

                        Picker("选择视图", selection: $selectedTab) {
                            Label(NSLocalizedString("view.stats.tab.dashboard", comment: "Dashboard tab label"), systemImage: "chart.pie.fill").tag(0)
                            Label(NSLocalizedString("view.stats.tab.insights", comment: "Insights tab label"), systemImage: "lightbulb.fill").tag(1)
                            Label(NSLocalizedString("view.stats.tab.correlation", comment: "Correlation tab label"), systemImage: "link.circle.fill").tag(2)
                            Label(NSLocalizedString("view.stats.tab.calendar", comment: "Calendar tab label"), systemImage: "calendar").tag(3)
                            Label(NSLocalizedString("view.stats.tab.trend", comment: "Trend tab label"), systemImage: "chart.line.uptrend.xyaxis").tag(4)
                            Label(NSLocalizedString("view.stats.tab.guide", comment: "Guide tab label"), systemImage: "figure.walk").tag(5)
                        }
                        .pickerStyle(.menu) // 改为下拉菜单样式
                        .accentColor(StatsViewTheme.primary) // 设置下拉箭头颜色
                        // 可以给 Picker 加点背景和圆角让它更像一个按钮
                        .padding(.horizontal, 10)
                        .padding(.vertical, 5)
                        .background(StatsViewTheme.secondary)
                        .clipShape(Capsule())

                    }
                    .padding(.horizontal)
                    .padding(.top)
                    .padding(.bottom, 8) // 增加一点底部间距

                    // 内容区域 - 保持不变
                    TabView(selection: $selectedTab) {
                        // 使用修改后的标签页
                        dashboardTab
                            .tag(0)

                        insightsTab
                            .tag(1)

                        correlationTab
                            .tag(2)

                        // 日历视图标签页
                        FreeModeCalendarView(onDateSelected: { date, sessions in
                            // 设置选中的日期和会话列表，以触发导航
                            selectedCalendarDate = date
                            selectedDateSessions = sessions
                        })
                        .environmentObject(dataStore)
                        .tag(3)

                        // 趋势分析标签页
                        FreeModeTrendView()
                            .environmentObject(dataStore)
                            .tag(4)

                        // 成长指南标签页
                        FreeToStandardTransitionView()
                            .environmentObject(dataStore)
                            .tag(5)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    // 添加保留视图状态的修饰符
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                }
                .navigationTitle(NSLocalizedString("xmoment_stats", comment: "XMoment Stats"))
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .topBarTrailing) {
                        Button(NSLocalizedString("done", comment: "Done")) {
                            dismiss()
                        }
                        .foregroundColor(StatsViewTheme.primary)
                    }
                }
                .onAppear {
                    // 延迟一点时间开始动画，让视图先加载
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        withAnimation(.easeInOut(duration: 0.8)) {
                            animateCharts = true
                        }
                    }
                }
            }
            .navigationDestination(isPresented: Binding(
                get: { selectedCalendarDate != nil },
                set: { if !$0 { selectedCalendarDate = nil } }
            )) {
                if let date = selectedCalendarDate {
                    DailyFocusDetailView(date: date, sessions: selectedDateSessions)
                        .environmentObject(dataStore)
                        .onAppear {
                            print("显示日期详情：\(date)，共\(selectedDateSessions.count)条记录")
                        }
                }
            }
        }
    }

    // MARK: - 各标签页内容

    // 仪表盘标签页
    private var dashboardTab: some View {
        // 直接使用XMomentDashboardView替代之前的自定义内容
        XMomentDashboardView()
            .environmentObject(dataStore)
    }

    // 数据洞察标签页
    private var insightsTab: some View {
        // 直接使用XMomentInsightsView替代之前的自定义内容
        XMomentInsightsView()
            .environmentObject(dataStore)
    }

    // 关联分析标签页
    private var correlationTab: some View {
        XMomentCorrelationView()
            .environmentObject(dataStore)
    }

    // 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(spacing: 8) {
            HStack {
                Text("统计周期")
                    .font(.title2.bold())
                    .foregroundColor(StatsViewTheme.titleColor)

                Spacer()

                Menu {
                    ForEach(XMomentTimeRange.allCases) { range in
                        Button {
                            withAnimation(.spring()) {
                                selectedXMomentTimeRange = range
                            }
                        } label: {
                            HStack {
                                Text(range.rawValue)
                                if selectedXMomentTimeRange == range {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(selectedXMomentTimeRange.rawValue)
                            .fontWeight(.semibold)
                        Image(systemName: "chevron.down")
                            .font(.caption.bold())
                    }
                    .foregroundColor(StatsViewTheme.primary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(StatsViewTheme.primary.opacity(0.1))
                    .clipShape(Capsule())
                }
            }

            // 滑动选择器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    ForEach(XMomentTimeRange.allCases) { range in
                        Text(range.rawValue)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedXMomentTimeRange == range ?
                                    StatsViewTheme.primary :
                                    StatsViewTheme.secondary
                            )
                            .foregroundColor(
                                selectedXMomentTimeRange == range ?
                                    .white :
                                    StatsViewTheme.titleColor
                            )
                            .clipShape(Capsule())
                            .onTapGesture {
                                withAnimation(.spring()) {
                                    selectedXMomentTimeRange = range
                                }
                            }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.horizontal)
        .padding(.top)
    }

    // 主要统计卡片
    private var mainStatsCards: some View {
        VStack(spacing: 20) {
            // 总览卡片 - 合并主要指标
            AnalyticsCard(title: NSLocalizedString("stats_overview", comment: "Focus overview"), 
                         subtitle: NSLocalizedString("key_metrics_period", comment: "Key metrics for this period")) {
                VStack(spacing: 0) {
                    HStack(spacing: 0) {
                        // 专注次数
                        statItem(
                            icon: "timer",
                            value: "\(filteredSessions.count)",
                            label: NSLocalizedString("focus_count", comment: "Focus count"),
                            color: .blue
                        )

                        // 总专注时长
                        statItem(
                            icon: "clock.fill",
                            value: formatTotalDuration(totalDuration),
                            label: NSLocalizedString("total_duration", comment: "Total duration"),
                            color: .purple
                        )
                    }

                    Divider()
                        .padding(.horizontal)

                    HStack(spacing: 0) {
                        // 平均每次时长
                        statItem(
                            icon: "chart.bar.fill",
                            value: formatDuration(averageDuration),
                            label: NSLocalizedString("average_duration", comment: "Average duration"),
                            color: .green
                        )

                        // 完成率
                        statItem(
                            icon: "checkmark.circle.fill",
                            value: "\(Int(completionRate * 100))%",
                            label: NSLocalizedString("completion_rate", comment: "Completion rate"),
                            color: .orange
                        )
                    }
                }
            }

            // 中断与超频卡片
            AnalyticsCard(title: NSLocalizedString("focus_quality", comment: "Focus quality"), 
                         subtitle: NSLocalizedString("interruption_quality", comment: "Interruption and overclock status")) {
                HStack(spacing: 12) {
                    // 中断次数
                    qualityStatItem(
                        icon: "xmark.octagon.fill",
                        value: "\(interruptionCount)",
                        label: NSLocalizedString("interruption_count", comment: "Interruption count"),
                        color: .red,
                        percentage: interruptionCount > 0 ? Double(interruptionCount) / Double(max(filteredSessions.count, 1)) : 0
                    )

                    Divider()

                    // 超频次数与倍率
                    qualityStatItem(
                        icon: "bolt.fill",
                        value: "\(overclockCount)",
                        label: NSLocalizedString("overclock_count", comment: "Overclock count"),
                        subtext: String(format: NSLocalizedString("average_multiplier", comment: "Average multiplier: %@x"), 
                                      String(format: "%.1f", averageOverclockFactor)),
                        color: .orange,
                        percentage: Double(overclockCount) / Double(max(filteredSessions.count, 1))
                    )
                }
                .padding()
            }
        }
    }

    // 统计项目组件
    private func statItem(icon: String, value: String, label: String, color: Color) -> some View {
        VStack(alignment: .center, spacing: 6) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14))
                    .foregroundColor(color)

                Text(label)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Text(value)
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundColor(.primary)
                .minimumScaleFactor(0.7)
                .lineLimit(1)
        }
        .padding()
        .frame(maxWidth: .infinity)
    }

    // 质量统计项目（带进度指示）
    private func qualityStatItem(
        icon: String,
        value: String,
        label: String,
        subtext: String? = nil,
        color: Color,
        percentage: Double
    ) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(color)

                Text(label)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text(value)
                    .font(.system(size: 22, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
            }

            if let subtext = subtext {
                Text(subtext)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 8)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * CGFloat(min(percentage, 1.0)), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
        }
        .frame(maxWidth: .infinity)
    }

    // 专注时长分布图
    private var durationDistributionChart: some View {
        AnalyticsCard(title: NSLocalizedString("duration_distribution", comment: "Duration distribution"), 
                     subtitle: NSLocalizedString("duration_intervals", comment: "Duration intervals")) {
            if animateCharts {
                Chart {
                    ForEach(durationDistribution, id: \.range) { item in
                        BarMark(
                            x: .value("时长范围", item.label),
                            y: .value("次数", item.count)
                        )
                        .cornerRadius(8)
                        .foregroundStyle(StatsViewTheme.chartGradient)
                        .annotation(position: .top) {
                            if item.count > 0 {
                                Text("\(item.count)")
                                    .font(.system(.caption, design: .rounded).bold())
                                    .foregroundColor(StatsViewTheme.primary)
                                    .padding(.vertical, 4)
                                    .padding(.horizontal, 8)
                                    .background(
                                        Capsule()
                                            .fill(Color.white)
                                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    )
                            }
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .leading) { value in
                        AxisValueLabel()
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [5, 5]))
                    }
                }
                .chartXAxis {
                    AxisMarks(preset: .aligned) { value in
                        AxisValueLabel()
                            .font(.caption)
                    }
                }
                .padding()
            } else {
                ChartPlaceholder()
            }
        }
    }

    // 分类统计图
    private var categoryDistributionChart: some View {
        AnalyticsCard(title: NSLocalizedString("category_distribution", comment: "Category distribution"), 
                     subtitle: NSLocalizedString("category_time_ratio", comment: "Category time ratio")) {
            if animateCharts {
                VStack(spacing: 16) {
                    // 饼图部分
                    Chart {
                        ForEach(categoryDistribution) { item in
                            SectorMark(
                                angle: .value("时长", item.duration),
                                innerRadius: .ratio(0.6),
                                angularInset: 1.5
                            )
                            .foregroundStyle(categoryColor(for: item.category))
                            .cornerRadius(4)
                            .annotation(position: .overlay) {
                                if item.duration > totalDuration * 0.15 { // 只在较大扇区显示标签
                                    Text(item.category)
                                        .font(.caption2.bold())
                                        .foregroundColor(.white)
                                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                                }
                            }
                        }
                    }
                    .frame(height: 220)
                    .id("categoryChart") // 使用固定ID代替categoryDistribution

                    // 分类图例（改进版）
                    VStack(spacing: 8) {
                        ForEach(categoryDistribution.prefix(5)) { item in
                            HStack(spacing: 8) {
                                Circle()
                                    .fill(categoryColor(for: item.category))
                                    .frame(width: 12, height: 12)

                                Text(item.category)
                                    .font(.subheadline)
                                    .lineLimit(1)

                                Spacer()

                                Text(formatDuration(item.duration))
                                    .font(.caption.bold())
                                    .foregroundColor(.secondary)

                                Text("(\(Int(item.duration / totalDuration * 100))%)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 4)
                        }

                        if categoryDistribution.count > 5 {
                            Text("+ \(categoryDistribution.count - 5) 其他类别")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .trailing)
                        }
                    }
                    .padding(.horizontal)
                }
            } else {
                ChartPlaceholder()
            }
        }
    }

    // 心情分布图
    private var moodDistributionChart: some View {
        AnalyticsCard(title: NSLocalizedString("mood_distribution", comment: "Mood distribution"), 
                     subtitle: NSLocalizedString("mood_count_distribution", comment: "Mood count distribution")) {
            if animateCharts {
                Chart {
                    ForEach(moodDistribution, id: \.mood) { item in
                        BarMark(
                            x: .value("心情", item.mood),
                            y: .value("次数", item.count)
                        )
                        .foregroundStyle(moodColor(for: item.mood))
                        .cornerRadius(6)
                        .annotation(position: .top) {
                            if item.count > 0 {
                                Text("\(item.count)")
                                    .font(.caption.bold())
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .leading) { value in
                        AxisValueLabel()
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [5, 5]))
                    }
                }
                .padding()
            } else {
                ChartPlaceholder()
            }
        }
    }

    // 每日专注时长趋势图
    private var dailyDurationTrendChart: some View {
        AnalyticsCard(title: NSLocalizedString("duration_trend", comment: "Duration trend"), 
                     subtitle: NSLocalizedString("daily_duration_change", comment: "Daily duration change")) {
            if animateCharts {
                VStack(spacing: 0) {
                    ZStack(alignment: .topLeading) {
                        Chart {
                            averageReferenceRule

                            dailyDurationDataMarks

                            selectedPointMark
                        }
                        .chartXAxis {
                            AxisMarks(values: .stride(by: .day, count: getXAxisStride())) { value in
                                if let date = value.as(Date.self) {
                                    AxisGridLine()
                                    AxisTick()
                                    AxisValueLabel {
                                        Text(date, format: .dateTime.month().day())
                                            .font(.caption)
                                    }
                                }
                            }
                        }
                        .chartYAxis {
                            AxisMarks(position: .leading) { value in
                                AxisValueLabel {
                                    if let doubleValue = value.as(Double.self) {
                                        Text("\(Int(doubleValue))分钟")
                                            .font(.caption)
                                    }
                                }
                                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [5, 5]))
                            }
                        }
                        .chartXScale(domain: startDate...Date())
                        .chartOverlay { proxy in
                            ChartGestureOverlay(
                                proxy: proxy,
                                onDateSelected: { date in
                                    selectedDataPointDate = date
                                },
                                onEnded: {
                                    selectedDataPointDate = nil
                                },
                                dailyData: dailyDurationData
                            )
                        }
                        .frame(height: 200)
                        .padding()

                        dataPointInfoOverlay
                    }
                }
            } else {
                ChartPlaceholder()
            }
        }
    }

    // 提取Chart内容为单独视图
    private var dailyDurationChartContent: some View {
        Chart {
            averageReferenceRule

            dailyDurationDataMarks

            selectedPointMark
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: .day, count: getXAxisStride())) { value in
                if let date = value.as(Date.self) {
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel {
                        Text(date, format: .dateTime.month().day())
                            .font(.caption)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading) { value in
                AxisValueLabel {
                    if let doubleValue = value.as(Double.self) {
                        Text("\(Int(doubleValue))分钟")
                            .font(.caption)
                    }
                }
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [5, 5]))
            }
        }
        .chartXScale(domain: startDate...Date())
        .chartOverlay { proxy in
            ChartGestureOverlay(
                proxy: proxy,
                onDateSelected: { date in
                    selectedDataPointDate = date
                },
                onEnded: {
                    selectedDataPointDate = nil
                },
                dailyData: dailyDurationData
            )
        }
        .frame(height: 200)
        .padding()
    }

    // 平均值参考线
    private var averageReferenceRule: some ChartContent {
        RuleMark(
            y: .value("平均", averageDailyDuration)
        )
        .foregroundStyle(.red.opacity(0.5))
        .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
        .annotation(position: .top, alignment: .trailing) {
            Text("平均: \(Int(averageDailyDuration))分钟")
                .font(.caption)
                .foregroundColor(.red.opacity(0.8))
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color.white.opacity(0.8))
                        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                )
        }
    }

    // 主线数据标记
    private var dailyDurationDataMarks: some ChartContent {
        ForEach(dailyDurationData) { item in
            LineMark(
                x: .value("日期", item.date, unit: .day),
                y: .value("时长(分钟)", item.durationMinutes)
            )
            .foregroundStyle(
                LinearGradient(
                    colors: [.blue, .purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round))
            .interpolationMethod(.catmullRom)
            .symbol {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 5, height: 5)
                    .opacity(selectedDataPointDate == item.date ? 1 : 0.6)
                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
            }
            .symbolSize(selectedDataPointDate == item.date ? 140 : 100)
        }
    }

    // 选中点标记 - 修复可选值处理
    private var selectedPointMark: some ChartContent {
        // 创建一个基于选中日期的数据点数组
        let dataPoints: [DailyDurationData] = {
            if let date = selectedDataPointDate,
               let item = getMatchingDurationItem(for: date) {
                return [item]
            } else {
                return []
            }
        }()

        return ForEach(dataPoints) { item in
            PointMark(
                x: .value("日期", item.date, unit: .day),
                y: .value("时长", item.durationMinutes)
            )
            .foregroundStyle(Color.blue)
            .symbolSize(200)
        }
    }

    // 图表手势处理封装 - 完全重写
    private struct ChartGestureOverlay: View {
        let proxy: ChartProxy
        let onDateSelected: (Date) -> Void
        let onEnded: () -> Void
        let dailyData: [DailyDurationData]

        var body: some View {
            GeometryReader { geometry in
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle()) // 确保整个区域可交互
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                // 计算坐标
                                let xPosition = value.location.x

                                // 找到最近的数据点
                                if let closestDate = findClosestDate(at: xPosition, in: geometry) {
                                    onDateSelected(closestDate)
                                }
                            }
                            .onEnded { _ in
                                withAnimation(.easeOut(duration: 0.5)) {
                                    onEnded()
                                }
                            }
                    )
            }
        }

        // 查找最近的日期点 - 根据X坐标位置
        private func findClosestDate(at xPosition: CGFloat, in geometry: GeometryProxy) -> Date? {
            if dailyData.isEmpty { return nil }

            // 计算图表内的相对位置
            let relativeXPosition = xPosition - geometry.frame(in: .local).minX
            let chartWidth = geometry.size.width

            // 数据范围
            if let minDate = dailyData.map({ $0.date }).min(),
               let maxDate = dailyData.map({ $0.date }).max() {

                // 计算相对日期位置 (线性插值)
                let dateRange = maxDate.timeIntervalSince(minDate)
                let relativePosition = Double(relativeXPosition / chartWidth)
                let approximateDate = minDate.addingTimeInterval(dateRange * relativePosition)

                // 查找最接近的实际数据点
                return dailyData
                    .sorted(by: {
                        abs($0.date.timeIntervalSince(approximateDate)) <
                        abs($1.date.timeIntervalSince(approximateDate))
                    })
                    .first?
                    .date
            }

            return nil
        }
    }

    // 选中数据点信息浮层
    private var dataPointInfoOverlay: some View {
        Group {
            if let selectedDate = selectedDataPointDate,
               let selectedItem = getMatchingDurationItem(for: selectedDate) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(selectedDate, style: .date)
                        .font(.headline)

                    HStack {
                        Text("专注时长:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(Int(selectedItem.durationMinutes))分钟")
                            .font(.subheadline.bold())
                    }

                    HStack {
                        Text("专注次数:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(getSessionsCountForDate(selectedDate))次")
                            .font(.subheadline.bold())
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.95))
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 5)
                )
                .padding()
                .transition(.scale.combined(with: .opacity))
            }
        }
    }

    // 专注时段分析图表
    private var timeOfDayAnalysisChart: some View {
        AnalyticsCard(title: NSLocalizedString("timeblock_analysis", comment: "Timeblock analysis"), 
                     subtitle: NSLocalizedString("timeblock_distribution", comment: "Timeblock distribution")) {
            if animateCharts {
                VStack(spacing: 16) {
                    // 指标选择器
                    HStack {
                        Text("指标:")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        HStack(spacing: 16) {
                            Text("次数")
                                .font(.caption.bold())
                                .padding(.horizontal, 10)
                                .padding(.vertical, 4)
                                .background(StatsViewTheme.primary)
                                .foregroundColor(.white)
                                .clipShape(Capsule())

                            Text("时长")
                                .font(.caption)
                                .padding(.horizontal, 10)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.1))
                                .foregroundColor(.secondary)
                                .clipShape(Capsule())

                            Text("中断")
                                .font(.caption)
                                .padding(.horizontal, 10)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.1))
                                .foregroundColor(.secondary)
                                .clipShape(Capsule())
                        }
                    }
                    .padding(.horizontal)

                    Chart {
                        ForEach(timeOfDayStats) { stat in
                            BarMark(
                                x: .value("时段", stat.timeRangeLabel),
                                y: .value("次数", stat.focusCount)
                            )
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.cyan, .indigo],
                                    startPoint: .bottom,
                                    endPoint: .top
                                )
                            )
                            .cornerRadius(6)
                            .annotation(position: .top) {
                                if stat.focusCount > 0 {
                                    Text("\(stat.focusCount)")
                                        .font(.caption.bold())
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel()
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [5, 5]))
                        }
                    }
                    .chartXAxis {
                        AxisMarks { value in
                            AxisValueLabel()
                                .font(.caption)
                        }
                    }
                    .frame(height: 180)
                    .padding(.horizontal)

                    // 使用提取的视图方法
                    bestTimeRangeView()
                }
            } else {
                ChartPlaceholder()
            }
        }
    }

    // 提取最佳专注时段显示为单独视图方法
    private func bestTimeRangeView() -> some View {
        Group {
            let bestTimeRange = timeOfDayStats.isEmpty ? nil : timeOfDayStats.max(by: { $0.focusCount < $1.focusCount })

            if let bestRange = bestTimeRange, bestRange.focusCount > 0 {
                let averageDuration = Int(bestRange.averageDurationPerSession / 60)

                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)

                    Text("你的最佳专注时段是 \(bestRange.timeRangeLabel)，共 \(bestRange.focusCount) 次专注，平均每次 \(averageDuration) 分钟")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                .padding(.bottom, 4)
            }
        }
    }

    // 专注日历 Section - 重构版
    private var focusCalendarSection: some View {
        AnalyticsCard(title: NSLocalizedString("focus_calendar", comment: "Focus calendar"), 
                     subtitle: NSLocalizedString("view_daily_records", comment: "View daily records")) {
            VStack(spacing: 16) {
                // 月份导航
                calendarMonthNavigator

                // 日历内容
                calendarContentView
            }
        }
    }

    // 提取月份导航组件
    private var calendarMonthNavigator: some View {
        HStack {
            Button {
                withAnimation {
                    changeMonth(by: -1)
                }
            } label: {
                Image(systemName: "chevron.left")
                    .font(.body.weight(.semibold))
                    .foregroundColor(StatsViewTheme.primary)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .clipShape(Circle())
            }

            Spacer()

            Text(calendarDisplayMonth, style: .date)
                .font(.headline)
                .id(calendarDisplayMonth)

            Spacer()

            Button {
                withAnimation {
                    changeMonth(by: 1)
                }
            } label: {
                Image(systemName: "chevron.right")
                    .font(.body.weight(.semibold))
                    .foregroundColor(StatsViewTheme.primary)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .clipShape(Circle())
            }
        }
        .padding(.horizontal)
    }

    // 提取日历内容组件
    private var calendarContentView: some View {
        // 获取数据但不在View Builder中使用let
        Group {
            // 先计算当前状态数据
            let focusCounts = getFocusCountsForMonth(displayMonth: calendarDisplayMonth)
            let datesWithSessions = focusCounts.keys.sorted()

            if datesWithSessions.isEmpty {
                emptyCalendarView
            } else {
                calendarSessionsList(focusCounts: focusCounts, datesWithSessions: datesWithSessions)
            }
        }
    }

    // 空日历视图
    private var emptyCalendarView: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Text(NSLocalizedString("no_focus_records", comment: "No focus records this month"))
                    .foregroundColor(.secondary)
                    .padding()
                Spacer()
            }
            Spacer()
        }
        .frame(height: 200)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .padding(.horizontal)
    }

    // 日历会话列表 - 接收参数而不是使用let声明
    private func calendarSessionsList(focusCounts: [Date: Int], datesWithSessions: [Date]) -> some View {
        ScrollView {
            VStack(spacing: 8) {
                ForEach(datesWithSessions, id: \.self) { date in
                    calendarDayItem(date: date, count: focusCounts[date] ?? 0)
                }
            }
            .padding(.horizontal)
        }
        .frame(height: 280)
    }

    // 单个日历日期项
    private func calendarDayItem(date: Date, count: Int) -> some View {
        let isToday = Calendar.current.isDateInToday(date)

        return Button {
            selectedDateSessions = getSessionsForDate(date)
            selectedCalendarDate = date
        } label: {
            HStack(spacing: 12) {
                // 日期指示
                VStack(spacing: 2) {
                    Text(formatDayOfWeek(from: date))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)

                    Text("\(dayOfMonth(date))")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(isToday ? StatsViewTheme.primary : .primary)
                }
                .frame(width: 40)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isToday ? StatsViewTheme.primary.opacity(0.1) : Color.clear)
                )

                // 专注指示
                calendarDayDetails(date: date, count: count)

                Spacer()

                // 箭头指示
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 12)
            .padding(.horizontal)
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .shadow(color: Color.black.opacity(0.03), radius: 3, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 日历日期详情
    private func calendarDayDetails(date: Date, count: Int) -> some View {
        let sessions = getSessionsForDate(date)
        let totalDuration = sessions.reduce(0) { $0 + $1.duration }
        let totalSeconds = Int(totalDuration)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let durationText = hours > 0 ? 
            String(format: NSLocalizedString("hours_minutes_format", comment: "%d hours %d minutes"), hours, minutes) :
            String(format: NSLocalizedString("minutes_format", comment: "%d minutes"), minutes)

        return VStack(alignment: .leading, spacing: 4) {
            Text("\(count) \(NSLocalizedString("times_suffix", comment: "times"))")
                .font(.system(size: 15, weight: .semibold))
                .foregroundColor(.primary)

            Text(durationText)
                .font(.system(size: 12))
                .foregroundColor(.secondary)

            activityIndicator(for: count)
        }
    }

    // 活跃度指示条
    private func activityIndicator(for count: Int) -> some View {
        HStack(spacing: 2) {
            ForEach(0..<5, id: \.self) { i in
                let threshold = Double(i) * 0.8
                let isActive = Double(count) > threshold
                let fillColor = isActive ?
                    StatsViewTheme.dynamicColor(for: Double(i) / 5.0) :
                    Color.gray.opacity(0.2)

                Rectangle()
                    .fill(fillColor)
                    .frame(height: 3)
                    .cornerRadius(1.5)
            }
        }
        .frame(width: 50)
    }

    // MARK: - 数据计算方法

    // 根据选择的时间范围过滤会话
    private var filteredSessions: [XMomentSession] {
        let sessions = dataStore.getXMomentSessionsUnambiguous()
        return sessions.filter { $0.startTime >= startDate }
    }

    // 计算总专注时长
    private var totalDuration: TimeInterval {
        filteredSessions.reduce(0) { $0 + $1.duration }
    }

    // 计算中断次数
    private var interruptionCount: Int {
        filteredSessions.filter { $0.isInterrupted }.count
    }

    // 计算平均每次专注时长
    private var averageDuration: TimeInterval {
        filteredSessions.isEmpty ? 0 : totalDuration / Double(filteredSessions.count)
    }

    // 计算完成率
    private var completionRate: Double {
        let total = filteredSessions.count
        if total == 0 { return 0 }

        let completed = filteredSessions.filter { !$0.isInterrupted }.count
        return Double(completed) / Double(total)
    }

    // 计算专注时长分布
    private var durationDistribution: [(range: ClosedRange<TimeInterval>, label: String, count: Int)] {
        let ranges: [(range: ClosedRange<TimeInterval>, label: String)] = [
            (0...5*60, NSLocalizedString("less_than_5min", comment: "Less than 5 minutes")),
            (5*60...15*60, NSLocalizedString("5_to_15min", comment: "5-15 minutes")),
            (15*60...25*60, NSLocalizedString("15_to_25min", comment: "15-25 minutes")),
            (25*60...40*60, NSLocalizedString("25_to_40min", comment: "25-40 minutes")),
            (40*60...60*60, NSLocalizedString("40_to_60min", comment: "40-60 minutes")),
            (60*60...Double.infinity, NSLocalizedString("more_than_60min", comment: "More than 60 minutes"))
        ]

        return ranges.map { range, label in
            let count = filteredSessions.filter { range.contains($0.duration) }.count
            return (range, label, count)
        }
    }

    // 分类分布项目结构体
    private struct CategoryDistributionItem: Identifiable, Hashable {
        let id = UUID()
        let category: String
        let duration: TimeInterval

        func hash(into hasher: inout Hasher) {
            hasher.combine(id)
        }

        static func == (lhs: CategoryDistributionItem, rhs: CategoryDistributionItem) -> Bool {
            lhs.id == rhs.id
        }
    }

    // 计算分类分布
    private var categoryDistribution: [CategoryDistributionItem] {
        var result: [String: TimeInterval] = [:]

        for session in filteredSessions {
            let category = session.category ?? NSLocalizedString("uncategorized", comment: "Uncategorized")
            result[category, default: 0] += session.duration
        }

        return result.map { CategoryDistributionItem(category: $0.key, duration: $0.value) }
            .sorted { $0.duration > $1.duration }
    }

    // 计算心情分布
    private var moodDistribution: [(mood: String, count: Int)] {
        var result: [String: Int] = [:]

        for session in filteredSessions {
            let mood = session.mood ?? NSLocalizedString("unrecorded", comment: "Unrecorded")
            result[mood, default: 0] += 1
        }

        return result.map { (mood: $0.key, count: $0.value) }
            .sorted { $0.count > $1.count }
    }

    // MARK: - 时段分析
    // 时段分析数据结构
    struct TimeOfDayFocusStat: Identifiable {
        let id = UUID()
        let timeRangeLabel: String
        let hourRange: Range<Int>
        var focusCount: Int = 0
        var totalDuration: TimeInterval = 0
        var interruptionCount: Int = 0
        var totalOverclockFactorSum: Double = 0
        var overclockSessionCount: Int = 0

        var averageOverclockFactor: Double {
            overclockSessionCount == 0 ? 1.0 : totalOverclockFactorSum / Double(overclockSessionCount)
        }
        var averageDurationPerSession: TimeInterval {
            focusCount == 0 ? 0 : totalDuration / Double(focusCount)
        }
    }

    // 计算时段分析统计
    private var timeOfDayStats: [TimeOfDayFocusStat] {
        let calendar = Calendar.current
        // 定义时间段
        let ranges: [(String, Range<Int>)] = [
            (NSLocalizedString("time_0_6", comment: "0-6 AM"), 0..<6),
            (NSLocalizedString("time_6_12", comment: "6-12 AM"), 6..<12),
            (NSLocalizedString("time_12_18", comment: "12-18 PM"), 12..<18),
            (NSLocalizedString("time_18_24", comment: "18-24 PM"), 18..<24)
        ]

        var stats = ranges.map { TimeOfDayFocusStat(timeRangeLabel: $0.0, hourRange: $0.1) }

        for session in filteredSessions {
            // 修改：calendar.component 返回 Int 而非可选类型，直接使用
            let hour = calendar.component(.hour, from: session.startTime)

            // 找到对应的时段索引
            if let index = stats.firstIndex(where: { $0.hourRange.contains(hour) }) {
                stats[index].focusCount += 1
                stats[index].totalDuration += session.duration
                if session.isInterrupted {
                    stats[index].interruptionCount += 1
                }
                if session.overclockFactor > 1.0 {
                    stats[index].totalOverclockFactorSum += session.overclockFactor
                    stats[index].overclockSessionCount += 1
                }
            }
        }
        return stats
    }

    // 每日专注时长数据
    private struct DailyDurationData: Identifiable {
        let id = UUID()
        let date: Date
        let durationMinutes: Double
    }

    // 计算每日专注时长数据
    private var dailyDurationData: [DailyDurationData] {
        let calendar = Calendar.current
        var result: [Date: TimeInterval] = [:]

        // 初始化日期范围内的所有日期
        var currentDate = calendar.startOfDay(for: startDate)
        let endDate = calendar.startOfDay(for: Date())

        while currentDate <= endDate {
            result[currentDate] = 0
            guard let nextDate = calendar.date(byAdding: .day, value: 1, to: currentDate) else { break }
            currentDate = nextDate
        }

        // 累加每个会话的时长到对应日期
        for session in filteredSessions {
            let day = calendar.startOfDay(for: session.startTime)
            if result[day] != nil { // 只累加在时间范围内的会话
                result[day]? += session.duration
            }
        }

        // 转换为数组并排序
        return result.map { date, duration in
            DailyDurationData(
                date: date,
                durationMinutes: duration / 60.0
            )
        }.sorted { $0.date < $1.date }
    }

    // 计算平均每日专注时长（分钟）
    private var averageDailyDuration: Double {
        if dailyDurationData.isEmpty { return 0 }
        let totalMinutes = dailyDurationData.reduce(0) { $0 + $1.durationMinutes }
        
        // 计算时间范围内的总天数
        let calendar = Calendar.current
        let dayCount = calendar.dateComponents([.day], from: startDate, to: Date()).day ?? 1
        let totalDays = max(dayCount, 1) // 确保至少为1天
        
        return totalMinutes / Double(totalDays)
    }

    // 超频相关统计
    private var overclockCount: Int {
        filteredSessions.filter { $0.overclockFactor > 1.0 }.count
    }

    private var averageOverclockFactor: Double {
        let overclockSessions = filteredSessions.filter { $0.overclockFactor > 1.0 }
        if overclockSessions.isEmpty { return 1.0 }

        let totalFactor = overclockSessions.reduce(0.0) { $0 + $1.overclockFactor }
        return totalFactor / Double(overclockSessions.count)
    }

    private var totalOverclockDuration: TimeInterval {
        filteredSessions.reduce(0) { $0 + $1.overclockDuration }
    }

    // 连续专注记录
    private var currentStreak: Int {
        dataStore.getXMomentUserStatsUnambiguous().currentStreak
    }

    private var bestStreak: Int {
        dataStore.getXMomentUserStatsUnambiguous().bestStreak
    }

    // 自由模式相关数据
    private var freeModeSessionCount: Int {
        return dataStore.getXMomentUserStatsUnambiguous().freeModeSessionCount
    }

    private var freeModeBestDuration: TimeInterval {
        return dataStore.getXMomentUserStatsUnambiguous().freeModeBestDuration
    }

    private var freeModeReached30MinCount: Int {
        return dataStore.getXMomentUserStatsUnambiguous().freeModeReached30MinCount
    }

    private var freeModeThresholdRate: Double {
        if freeModeSessionCount == 0 {
            return 0
        }
        return Double(freeModeReached30MinCount) / Double(freeModeSessionCount)
    }

    // MARK: - 日历相关计算

    // 获取指定月份的所有日期及其专注次数
    private func getFocusCountsForMonth(displayMonth: Date) -> [Date: Int] {
        let calendar = Calendar.current
        // 获取月份的开始和结束，并确保安全
        guard let monthInterval = calendar.dateInterval(of: .month, for: displayMonth) else {
            return [:]
        }

        // 初始化结果字典
        var counts: [Date: Int] = [:]

        // 初始化该月每天的计数为 0
        var currentDate = monthInterval.start
        let endOfMonth = monthInterval.end

        // 使用安全的日期迭代
        while currentDate < endOfMonth {
            let dayStart = calendar.startOfDay(for: currentDate)
            counts[dayStart] = 0

            // 安全地计算下一天
            if let nextDay = calendar.date(byAdding: .day, value: 1, to: currentDate) {
                currentDate = nextDay
            } else {
                // 防止无限循环
                break
            }
        }

        // 获取所有会话并按条件过滤
        let allSessions = dataStore.getXMomentSessionsUnambiguous()

        // 筛选当前时间范围内的会话
        for session in allSessions {
            // 检查会话是否在当前月份内
            let sessionInMonth = session.startTime >= monthInterval.start &&
                                 session.startTime < monthInterval.end

            // 检查会话是否在全局时间范围内
            let sessionInTimeRange = session.startTime >= startDate

            // 只处理满足两个条件的会话
            if sessionInMonth && sessionInTimeRange {
                let dayStart = calendar.startOfDay(for: session.startTime)
                counts[dayStart, default: 0] += 1
            }
        }

        return counts
    }

    // 获取指定日期的会话 (用于点击日历日期后的详情页)
    private func getSessionsForDate(_ date: Date) -> [XMomentSession] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) else { return [] }

        // 应该过滤所有会话，而不是 filteredSessions，因为日历点击不应受全局时间范围影响
        let allSessions = dataStore.getXMomentSessionsUnambiguous()
        return allSessions.filter {
            $0.startTime >= startOfDay && $0.startTime < endOfDay
        }.sorted { $0.startTime < $1.startTime } // 按时间排序
    }

    // MARK: - 辅助方法

    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        // 仅显示到分钟（四舍五入）
        if hours > 0 {
            return String(format: NSLocalizedString("hours_minutes_format", comment: "%d hours %d minutes"), hours, minutes)
        } else {
            return String(format: NSLocalizedString("minutes_format", comment: "%d minutes"), minutes)
        }
    }

    // 总专注时长格式化函数 - 只显示到小时（四舍五入）
    private func formatTotalDuration(_ duration: TimeInterval) -> String {
        // 计算总分钟数
        let totalMinutes = duration / 60
        // 四舍五入到最接近的小时
        let roundedHours = Int(round(totalMinutes / 60))

        if roundedHours > 0 {
            return String(format: NSLocalizedString("hours_format", comment: "%d hours"), roundedHours)
        } else {
            // 如果不足1小时，显示分钟
            return String(format: NSLocalizedString("minutes_format", comment: "%d minutes"), Int(round(totalMinutes)))
        }
    }

    // 获取分类颜色
    private func categoryColor(for category: String) -> Color {
        let colors: [Color] = [.blue, .green, .orange, .purple, .pink, .yellow, .red, .cyan]
        let index = abs(category.hashValue) % colors.count
        return colors[index]
    }

    // 获取心情颜色
    private func moodColor(for mood: String) -> Color {
        switch mood.lowercased() {
        case NSLocalizedString("focused", comment: "Focused").lowercased(),
             NSLocalizedString("efficient", comment: "Efficient").lowercased():
            return .green
        case NSLocalizedString("calm", comment: "Calm").lowercased(),
             NSLocalizedString("relaxed", comment: "Relaxed").lowercased():
            return .blue
        case NSLocalizedString("tired", comment: "Tired").lowercased():
            return .orange
        case NSLocalizedString("distracted", comment: "Distracted").lowercased(),
             NSLocalizedString("interrupted", comment: "Interrupted").lowercased():
            return .red
        default:
            return .gray
        }
    }

    // 根据时间范围确定X轴刻度间隔
    private func getXAxisStride() -> Int {
        switch selectedXMomentTimeRange {
        case .week:
            return 1
        case .month:
            return 3
        case .year:
            return 30
        case .all:
            let dayCount = Calendar.current.dateComponents([.day], from: startDate, to: Date()).day ?? 0
            return max(dayCount / 8, 1) // 调整为更合适的间隔
        }
    }

    // MARK: - 日历辅助方法

    // 获取月份中的所有日期
    private func daysInMonth(for date: Date) -> [Date] {
        let calendar = Calendar.current
        guard
            let monthInterval = calendar.dateInterval(of: .month, for: date),
            let range = calendar.range(of: .day, in: .month, for: date)
        else { return [] }

        return range.compactMap { day -> Date? in
            calendar.date(byAdding: .day, value: day - 1, to: monthInterval.start)
        }
    }

    // 获取月份第一天是星期几 (0=周日, 1=周一, ... 6=周六)
    private func firstWeekdayOfMonth(for date: Date) -> Int {
        let calendar = Calendar.current
        guard let startOfMonth = calendar.dateInterval(of: .month, for: date)?.start else { return 0 }
        return (calendar.component(.weekday, from: startOfMonth) + 6 - calendar.firstWeekday) % 7 // Adjust to make Monday = 0 if needed, here Sunday=0
    }

    // 获取日期是几号
    private func dayOfMonth(_ date: Date) -> Int {
        Calendar.current.component(.day, from: date)
    }

    // 更改日历显示的月份
    private func changeMonth(by amount: Int) {
        if let newMonth = Calendar.current.date(byAdding: .month, value: amount, to: calendarDisplayMonth) {
            calendarDisplayMonth = newMonth
        }
    }

    // 修正函数返回类型
    private func getMatchingDurationItem(for date: Date) -> DailyDurationData? {
        return dailyDurationData.first { item in
            Calendar.current.isDate(item.date, inSameDayAs: date)
        }
    }

    private func getSessionsCountForDate(_ date: Date) -> Int {
        return filteredSessions.filter { session in
            Calendar.current.isDate(session.startTime, inSameDayAs: date)
        }.count
    }

    // 获取星期几的中文表示
    private func formatDayOfWeek(from date: Date) -> String {
        let weekday = Calendar.current.component(.weekday, from: date)
        let weekdaySymbols = ["日", "一", "二", "三", "四", "五", "六"]
        return "周" + weekdaySymbols[weekday - 1]
    }

    // 超频统计部分
    private var overclockStatsSection: some View {
        AnalyticsCard(title: NSLocalizedString("overclock_stats", comment: "Overclock stats"), 
                     subtitle: NSLocalizedString("overclock_performance", comment: "Overclock performance")) {
            VStack(spacing: 16) {
                HStack(spacing: 20) {
                    // 超频次数
                    overclockStatItem(
                        value: "\(overclockCount)",
                        label: NSLocalizedString("overclock_count", comment: "Overclock count"),
                        icon: "bolt.circle.fill"
                    )

                    // 平均超频倍率
                    overclockStatItem(
                        value: String(format: "%.1fx", averageOverclockFactor),
                        label: NSLocalizedString("average_multiplier", comment: "Average multiplier"),
                        icon: "dial.medium.fill"
                    )

                    // 超频总时长
                    overclockStatItem(
                        value: formatDuration(totalOverclockDuration),
                        label: NSLocalizedString("overclock_duration", comment: "Overclock duration"),
                        icon: "clock.arrow.2.circlepath"
                    )
                }
                .padding()

                // 超频提示
                if overclockCount > 0 {
                    HStack(alignment: .top, spacing: 12) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 16))

                        Text("通过超频，你已额外获得 \(formatDuration(totalOverclockDuration)) 的专注时间，相当于提升了 \(Int((averageOverclockFactor - 1) * 100))% 的专注效率。")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                }
            }
        }
    }

    // 超频统计项目
    private func overclockStatItem(value: String, label: String, icon: String) -> some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.orange)

            Text(value)
                .font(.system(size: 20, weight: .bold, design: .rounded))
                .foregroundColor(.primary)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.orange.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .strokeBorder(Color.orange.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // 自由模式统计卡片
    private var freeModeSectionCard: some View {
        AnalyticsCard(title: NSLocalizedString("free_mode_stats", comment: "Free mode stats"), 
                     subtitle: NSLocalizedString("focus_training_progress", comment: "Focus training progress")) {
            VStack(spacing: 16) {
                // 自由模式概览
                HStack(spacing: 20) {
                    // 自由模式会话数
                    VStack(alignment: .leading, spacing: 4) {
                        Text(NSLocalizedString("free_mode_count", comment: "Free mode count"))
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(freeModeSessionCount)")
                            .font(.title2.bold())
                            .foregroundColor(.primary)
                    }

                    Divider()

                    // 最佳记录
                    VStack(alignment: .leading, spacing: 4) {
                        Text(NSLocalizedString("best_record", comment: "Best record"))
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text(formatDuration(freeModeBestDuration))
                            .font(.title2.bold())
                            .foregroundColor(.primary)
                    }
                }
                .padding()
                .background(Color.secondary.opacity(0.1))
                .cornerRadius(10)

                // 专注门槛达成率
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(NSLocalizedString("focus_threshold_rate", comment: "Focus threshold rate"))
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text("\(Int(freeModeThresholdRate * 100))%")
                            .font(.subheadline.bold())
                            .foregroundColor(.purple)
                    }

                    // 进度条
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 8)
                                .cornerRadius(4)

                            Rectangle()
                                .fill(LinearGradient(
                                    gradient: Gradient(colors: [.blue, .purple]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ))
                                .frame(width: geometry.size.width * CGFloat(freeModeThresholdRate), height: 8)
                                .cornerRadius(4)
                        }
                    }
                    .frame(height: 8)
                }
                .padding()

                // 查看详细统计按钮
                Button {
                    // 处理查看详细统计的操作
                } label: {
                    HStack {
                        Text(NSLocalizedString("view_detailed_stats", comment: "View detailed stats"))
                            .font(.subheadline.bold())

                        Image(systemName: "chevron.right")
                            .font(.caption.bold())
                    }
                    .foregroundColor(StatsViewTheme.primary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(StatsViewTheme.primary.opacity(0.1))
                    .cornerRadius(10)
                }
            }
        }
    }

    // 连续专注记录部分
    private var streakStatsSection: some View {
        AnalyticsCard(title: NSLocalizedString("streak_stats", comment: "Streak stats"), 
                     subtitle: NSLocalizedString("streak_achievements", comment: "Streak achievements")) {
            VStack(spacing: 16) {
                HStack(spacing: 20) {
                    // 当前连续天数
                    VStack(spacing: 2) {
                        Text("\(currentStreak)")
                            .font(.system(size: 28, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)

                        Text(NSLocalizedString("days_suffix", comment: "Days"))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.green.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .strokeBorder(Color.green.opacity(0.1), lineWidth: 1)
                            )
                    )

                    // 最佳连续天数
                    VStack(spacing: 8) {
                        Text("\(bestStreak)")
                            .font(.system(size: 28, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)

                        Text(NSLocalizedString("best_streak_days", comment: "Best streak days"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.yellow.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .strokeBorder(Color.yellow.opacity(0.1), lineWidth: 1)
                            )
                    )
                }
                .padding(.horizontal)

                // 连续专注激励语
                if currentStreak > 0 {
                    HStack(alignment: .top, spacing: 12) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 16))

                        Text(getStreakMessage())
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                }
            }
        }
    }

    // 根据连续天数生成激励消息
    private func getStreakMessage() -> String {
        if currentStreak >= bestStreak && currentStreak > 7 {
            return NSLocalizedString("streak_new_record", comment: "New streak record message")
        } else if currentStreak > 21 {
            return String(format: NSLocalizedString("streak_21_days", comment: "21 days streak message"), currentStreak)
        } else if currentStreak > 14 {
            return NSLocalizedString("streak_14_days", comment: "14 days streak message")
        } else if currentStreak > 7 {
            return NSLocalizedString("streak_7_days", comment: "7 days streak message")
        } else if currentStreak > 3 {
            return String(format: NSLocalizedString("streak_3_days", comment: "3 days streak message"), currentStreak)
        } else {
            return String(format: NSLocalizedString("streak_continue", comment: "Continue streak message"), currentStreak)
        }
    }

    // 新增计算属性，获取当前选中标签的标题
    private var selectedTabTitle: String {
        switch selectedTab {
        case 0: return NSLocalizedString("dashboard", comment: "Dashboard")
        case 1: return NSLocalizedString("data_insights", comment: "Data insights")
        case 2: return NSLocalizedString("correlation_analysis", comment: "Correlation analysis")
        case 3: return NSLocalizedString("calendar_view", comment: "Calendar view")
        case 4: return NSLocalizedString("trend_analysis", comment: "Trend analysis")
        case 5: return NSLocalizedString("growth_guide", comment: "Growth guide")
        default: return ""
        }
    }
}

extension View {
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// 预览
struct XMomentStatsView_Previews: PreviewProvider {
    static var previews: some View {
        XMomentStatsView()
            .environmentObject(DataStore.shared)
    }
}
