import SwiftUI
import SpriteKit

// 旅程风格的计时视图 - 基于出发-到达理念
struct JourneyTimingView: View {
    var progress: Double // 输入进度 0.0 到 1.0

    // 是否已开始计时，默认为true（兼容现有代码）
    var isTimingStarted: Bool = true
    
    // 自由模式标记
    var isFreeMode: Bool = false

    // 用于跟踪上一次的计时状态，实现丰富的动画效果
    @State private var previousTimingState: Bool = true

    // 控制场景元素的透明度
    @State private var sceneOpacity: Double = 0.0

    // 超频状态和倍率的回调
    var onOverclockStatusUpdated: ((Bool, Double, TimeInterval) -> Void)? = nil

    // 创建并持有 SKScene 实例
    @StateObject private var scene: JourneyScene = {
        let scene = JourneyScene(size: CGSize(width: 600, height: 200))
        scene.scaleMode = .aspectFill
        scene.anchorPoint = CGPoint(x: 0.5, y: 0.5) // 设置锚点为中心
        scene.backgroundColor = .clear
        return scene
    }()

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景卡片 - 使用与图片相同的亮黄色
                RoundedRectangle(cornerRadius: 16) // 减小圆角半径，与容器保持一致
                    .fill(Color(red: 1.0, green: 0.95, blue: 0.0))
                    .frame(width: geometry.size.width, height: geometry.size.height)

                // SpriteKit 场景视图 - 始终存在，但通过透明度控制显示
                SpriteView(scene: scene, options: [.allowsTransparency])
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .opacity(sceneOpacity) // 使用透明度控制显示/隐藏
                    .onAppear {
                        // 设置初始大小
                        scene.sceneSize = geometry.size

                        // 设置初始透明度
                        sceneOpacity = isTimingStarted ? 1.0 : 0.0
                        previousTimingState = isTimingStarted
                    }
                    .onChange(of: geometry.size) { oldSize, newSize in
                        // 更新场景大小
                        scene.sceneSize = newSize
                    }
                    .onChange(of: progress) { oldProgress, newProgress in
                        // 将进度值传递给场景
                        scene.progress = newProgress

                        // 检查进度是否达到100%并设置超频状态
                        if newProgress >= 1.0 && !scene.isOverclocked {
                            scene.isOverclocked = true
                        }
                    }
                    .onChange(of: scene.hasMinimumOverclockTime) { _, hasMinimum in
                        // 当达到最小超频时间时，通知父视图
                        if hasMinimum {
                            print("已达到最小超频时间要求 (5分钟)")
                        }
                        // 调用回调，传递超频状态、奖励倍率和累计超频时间
                        onOverclockStatusUpdated?(
                            scene.isOverclocked,
                            scene.getOverclockBonusFactor(),
                            scene.overclockDuration
                        )
                    }
                    .onChange(of: isTimingStarted) { oldValue, newValue in
                        // 当计时状态变化时，添加丰富的动画效果
                        if newValue != oldValue {
                            if newValue { // 从纯色背景切换到完整界面
                                // 使用弹性动画，增加丰富的视觉效果
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    sceneOpacity = 1.0
                                }
                            } else { // 从完整界面切换到纯色背景
                                // 使用快速淡出动画
                                withAnimation(.easeOut(duration: 0.3)) {
                                    sceneOpacity = 0.0
                                }
                            }
                            previousTimingState = newValue
                        }
                    }
                    .onDisappear {
                        // 在视图消失时，确保回调最终状态
                        onOverclockStatusUpdated?(
                            scene.isOverclocked,
                            scene.getOverclockBonusFactor(),
                            scene.overclockDuration
                        )
                        // 重置超频状态
                        scene.resetOverclockState()
                    }
            }
        }
        .clipped()
    }
}

// MARK: - 旅程场景实现
class JourneyScene: SKScene, ObservableObject {

    // MARK: - 属性
    @Published var progress: Double = 0.0
    @Published var isOverclocked: Bool = false
    @Published var overclockDuration: TimeInterval = 0
    @Published var hasMinimumOverclockTime: Bool = false

    var sceneSize: CGSize = .zero {
        didSet {
            guard sceneSize != oldValue, sceneSize != .zero else { return }
            self.size = sceneSize
            setupScene()
        }
    }

    // 场景元素
    private var departureLabel: SKLabelNode?
    private var arrivalLabel: SKLabelNode?
    private var departureTimeLabel: SKLabelNode?
    private var arrivalTimeLabel: SKLabelNode?
    private var landingInfoLabel: SKLabelNode?
    private var airlineLabel: SKLabelNode?
    private var planeNode: SKSpriteNode?
    private var pathNode: SKNode?
    private var dotNode: SKShapeNode?
    private var overclockEffects: [SKNode] = []

    // 时间跟踪
    private var startTime: TimeInterval = 0
    private var lastUpdateTime: TimeInterval = 0
    private var elapsedTime: TimeInterval = 0

    // 实际开始时间
    private var actualStartTime: Date = Date()

    // 中国国内机场代码列表
    private let chineseAirports = [
        "PEK": "Beijing Capital",
        "SHA": "Shanghai Hongqiao",
        "PVG": "Shanghai Pudong",
        "CAN": "Guangzhou Baiyun",
        "SZX": "Shenzhen Bao'an",
        "CTU": "Chengdu Shuangliu",
        "KMG": "Kunming Changshui",
        "XIY": "Xi'an Xianyang",
        "CKG": "Chongqing Jiangbei",
        "HGH": "Hangzhou Xiaoshan",
        "TAO": "Qingdao Liuting",
        "CSX": "Changsha Huanghua",
        "NKG": "Nanjing Lukou",
        "XMN": "Xiamen Gaoqi",
        "WUH": "Wuhan Tianhe",
        "TNA": "Jinan Yaoqiang",
        "CGO": "Zhengzhou Xinzheng",
        "SYX": "Sanya Phoenix",
        "HRB": "Harbin Taiping",
        "DLC": "Dalian Zhoushuizi"
    ]

    // 当前选择的机场
    private var departureAirport: String = "PEK"
    private var arrivalAirport: String = "SHA"

    // 起飞点和着陆点标记
    private var takeoffMarker: SKNode?
    private var landingMarker: SKNode?
    private var breathingAction: SKAction?

    // 超频相关
    private var overclockStartTime: TimeInterval = 0
    private var minimumOverclockTime: TimeInterval = 5 * 60 // 5分钟
    private var maxOverclockBonus: Double = 1.5 // 最大超频奖励倍率

    // 记录上次添加效果的时间
    private var lastEffectTime: TimeInterval = 0

    // MARK: - 初始化和设置

    override func didMove(to view: SKView) {
        backgroundColor = .clear

        // 记录实际开始时间
        actualStartTime = Date()

        setupScene()
    }

    private func setupScene() {
        // 清除现有节点
        removeAllChildren()
        overclockEffects.removeAll()

        // 随机选择机场
        randomizeAirports()

        // 设置场景元素
        setupLabels()
        setupPath()
        setupPlane()

        // 更新出发时间标签为实际开始时间
        updateDepartureTime()
    }

    // 随机选择机场
    private func randomizeAirports() {
        // 获取所有机场代码
        let airportCodes = Array(chineseAirports.keys)

        // 确保出发机场和到达机场不同
        let newDepartureIndex = Int.random(in: 0..<airportCodes.count)
        var newArrivalIndex = Int.random(in: 0..<airportCodes.count)

        // 确保出发机场和到达机场不同
        while newArrivalIndex == newDepartureIndex {
            newArrivalIndex = Int.random(in: 0..<airportCodes.count)
        }

        // 设置新的机场
        departureAirport = airportCodes[newDepartureIndex]
        arrivalAirport = airportCodes[newArrivalIndex]
    }

    // 更新出发时间标签
    private func updateDepartureTime() {
        guard let departureTimeLabel = departureTimeLabel else { return }

        // 格式化实际开始时间
        let dateFormatter = DateFormatter()
        dateFormatter.locale = Locale(identifier: "en_US") // 强制使用英文区域设置，显示AM/PM
        dateFormatter.dateFormat = "h:mm a" // 例如：11:04 AM
        let timeString = dateFormatter.string(from: actualStartTime)

        // 更新出发时间标签，使用简化文字
        departureTimeLabel.text = "Dept \(timeString)"
    }

    private func setupLabels() {
        // 定义布局参数
        let leftMargin = -sceneSize.width * 0.45 // 左边距
        let rightMargin = sceneSize.width * 0.45 // 右边距
        let topMargin = sceneSize.height * 0.25 // 顶部边距
        let airportY = -sceneSize.height * 0.2 // 机场标签的Y坐标
        let timeY = -sceneSize.height * 0.3 // 时间标签的Y坐标

        // 设置航空公司标签（左上角）
        airlineLabel = createLabel(text: "X AirJet", fontSize: 18, position: CGPoint(x: leftMargin, y: topMargin))
        if let airlineLabel = airlineLabel {
            airlineLabel.horizontalAlignmentMode = .left // 靠左对齐
            addChild(airlineLabel)
        }

        // 设置飞行阶段信息标签（右上角）
        landingInfoLabel = createLabel(text: "Engine start-up", fontSize: 16, position: CGPoint(x: rightMargin, y: topMargin))
        if let landingInfoLabel = landingInfoLabel {
            landingInfoLabel.horizontalAlignmentMode = .right // 靠右对齐
            landingInfoLabel.preferredMaxLayoutWidth = sceneSize.width * 0.5 // 限制最大宽度
            landingInfoLabel.numberOfLines = 2 // 允许换行
            landingInfoLabel.fontName = "HelveticaNeue-Medium" // 使用中等粗字体
            addChild(landingInfoLabel)
        }

        // 设置出发地标签（左下方）
        departureLabel = createLabel(text: departureAirport, fontSize: 36, position: CGPoint(x: leftMargin, y: airportY))
        if let departureLabel = departureLabel {
            departureLabel.fontName = "HelveticaNeue-Bold" // 粗体
            departureLabel.horizontalAlignmentMode = .left // 靠左对齐
            departureLabel.verticalAlignmentMode = .bottom // 底部对齐
            addChild(departureLabel)
        }

        // 设置到达地标签（右下方）
        arrivalLabel = createLabel(text: arrivalAirport, fontSize: 36, position: CGPoint(x: rightMargin, y: airportY))
        if let arrivalLabel = arrivalLabel {
            arrivalLabel.fontName = "HelveticaNeue-Bold" // 粗体
            arrivalLabel.horizontalAlignmentMode = .right // 靠右对齐
            arrivalLabel.verticalAlignmentMode = .bottom // 底部对齐
            addChild(arrivalLabel)
        }

        // 设置出发时间标签（左下方，机场标签下方）
        departureTimeLabel = createLabel(text: "Dept 11:04 AM", fontSize: 18, position: CGPoint(x: leftMargin, y: timeY))
        if let departureTimeLabel = departureTimeLabel {
            departureTimeLabel.horizontalAlignmentMode = .left // 靠左对齐
            departureTimeLabel.verticalAlignmentMode = .top // 顶部对齐
            departureTimeLabel.preferredMaxLayoutWidth = sceneSize.width * 0.3 // 限制最大宽度
            addChild(departureTimeLabel)
        }

        // 设置到达时间标签（右下方，机场标签下方）
        arrivalTimeLabel = createLabel(text: "Estimated arrival", fontSize: 18, position: CGPoint(x: rightMargin, y: timeY))
        if let arrivalTimeLabel = arrivalTimeLabel {
            arrivalTimeLabel.horizontalAlignmentMode = .right // 靠右对齐
            arrivalTimeLabel.verticalAlignmentMode = .top // 顶部对齐
            arrivalTimeLabel.preferredMaxLayoutWidth = sceneSize.width * 0.3 // 限制最大宽度
            addChild(arrivalTimeLabel)
        }
    }

    private func createLabel(text: String, fontSize: CGFloat, position: CGPoint) -> SKLabelNode {
        let label = SKLabelNode(fontNamed: "HelveticaNeue")
        label.text = text
        label.fontSize = fontSize
        label.fontColor = .black
        label.position = position
        label.verticalAlignmentMode = .center
        label.horizontalAlignmentMode = .center
        return label
    }

    // 存储路径段节点，以便更新飞机经过的路径
    private var pathSegments: [SKShapeNode] = []

    private func setupPath() {
        // 创建完整的飞行路径，包括起飞和降落滑行
        // 起飞滑行起点（在屏幕左侧外）
        let runwayStartPoint = CGPoint(x: -sceneSize.width * 0.6, y: 0)
        // 起飞点（屏幕左侧）
        let takeoffPoint = CGPoint(x: -sceneSize.width * 0.35, y: 0)
        // 巡航中间点（屏幕中间偏上）
        let cruisingPoint = CGPoint(x: 0, y: sceneSize.height * 0.3) // 增大弧度
        // 降落点（屏幕右侧）
        let landingPoint = CGPoint(x: sceneSize.width * 0.35, y: 0)
        // 降落滑行终点（屏幕右侧外）
        let runwayEndPoint = CGPoint(x: sceneSize.width * 0.6, y: 0)

        // 创建一个组合节点来存放所有的路径段
        let pathContainer = SKNode()
        pathContainer.alpha = 0.5
        addChild(pathContainer)
        pathNode = pathContainer

        // 清除现有路径段
        pathSegments.removeAll()

        // 创建起飞滑行路径（直线）- 陆地部分
        createPathSegment(from: runwayStartPoint, to: takeoffPoint, container: pathContainer, segments: 10, isGround: true)

        // 创建主要飞行路径（弧形）- 空中部分
        createCurvedPathSegment(from: takeoffPoint, through: cruisingPoint, to: landingPoint, container: pathContainer, segments: 30, isGround: false)

        // 创建降落滑行路径（直线）- 陆地部分
        createPathSegment(from: landingPoint, to: runwayEndPoint, container: pathContainer, segments: 10, isGround: true)

        // 创建终点圆点
        dotNode = SKShapeNode(circleOfRadius: 5)
        if let dotNode = dotNode {
            dotNode.fillColor = .black
            dotNode.strokeColor = .clear
            dotNode.position = runwayEndPoint
            dotNode.alpha = 0.5
            addChild(dotNode)
        }

        // 创建起飞点标记
        takeoffMarker = createLocationMarker(at: takeoffPoint, color: .blue)
        if let takeoffMarker = takeoffMarker {
            addChild(takeoffMarker)
        }

        // 创建着陆点标记
        landingMarker = createLocationMarker(at: landingPoint, color: .red)
        if let landingMarker = landingMarker {
            // 创建呼吸效果
            breathingAction = SKAction.sequence([
                SKAction.scale(to: 1.2, duration: 1.0),
                SKAction.scale(to: 1.0, duration: 1.0)
            ])

            if let breathingAction = breathingAction {
                landingMarker.run(SKAction.repeatForever(breathingAction))
            }

            addChild(landingMarker)
        }
    }

    // 创建简洁的定位标记
    private func createLocationMarker(at position: CGPoint, color: UIColor) -> SKNode {
        let markerNode = SKNode()
        markerNode.position = position

        // 创建一个小圆点
        let dot = SKShapeNode(circleOfRadius: 3)
        dot.fillColor = color
        dot.strokeColor = color
        dot.alpha = 0.8
        markerNode.addChild(dot)

        return markerNode
    }

    // 创建直线路径段
    private func createPathSegment(from startPoint: CGPoint, to endPoint: CGPoint, container: SKNode, segments: Int, isGround: Bool = false) {
        var lastPoint = startPoint

        for i in 1...segments {
            let t = CGFloat(i) / CGFloat(segments)
            let x = startPoint.x + (endPoint.x - startPoint.x) * t
            let y = startPoint.y + (endPoint.y - startPoint.y) * t
            let currentPoint = CGPoint(x: x, y: y)

            // 创建路径段
            let segmentPath = CGMutablePath()
            segmentPath.move(to: lastPoint)
            segmentPath.addLine(to: currentPoint)

            let segmentNode = SKShapeNode(path: segmentPath)

            // 使用统一的碳黑色不区分陆地和空中
            segmentNode.strokeColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0) // 碳黑色
            segmentNode.lineWidth = 2.0 // 加粗路径

            // 初始状态下设置虚线效果
            if i % 2 == 0 {
                // 偶数段显示，实现虚线效果
                segmentNode.alpha = 0.5
            } else {
                // 奇数段不显示，实现虚线效果
                segmentNode.alpha = 0.0
            }

            container.addChild(segmentNode)
            pathSegments.append(segmentNode)

            lastPoint = currentPoint
        }
    }

    // 创建弧形路径段（二次贝塞尔曲线）
    private func createCurvedPathSegment(from startPoint: CGPoint, through controlPoint: CGPoint, to endPoint: CGPoint, container: SKNode, segments: Int, isGround: Bool = false) {
        var lastPoint = startPoint

        for i in 1...segments {
            let t = CGFloat(i) / CGFloat(segments)
            let mt = 1 - t

            // 二次贝塞尔曲线公式
            let x = mt * mt * startPoint.x + 2 * mt * t * controlPoint.x + t * t * endPoint.x
            let y = mt * mt * startPoint.y + 2 * mt * t * controlPoint.y + t * t * endPoint.y
            let currentPoint = CGPoint(x: x, y: y)

            // 创建路径段
            let segmentPath = CGMutablePath()
            segmentPath.move(to: lastPoint)
            segmentPath.addLine(to: currentPoint)

            let segmentNode = SKShapeNode(path: segmentPath)

            // 使用统一的碳黑色不区分陆地和空中
            segmentNode.strokeColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0) // 碳黑色
            segmentNode.lineWidth = 2.0 // 加粗路径

            // 初始状态下设置虚线效果
            if i % 2 == 0 {
                // 偶数段显示，实现虚线效果
                segmentNode.alpha = 0.5
            } else {
                // 奇数段不显示，实现虚线效果
                segmentNode.alpha = 0.0
            }

            container.addChild(segmentNode)
            pathSegments.append(segmentNode)

            lastPoint = currentPoint
        }
    }

    private func setupPlane() {
        // 使用与模板选择器中完全一样的飞机图标
        let planeImage = createSystemAirplaneImage()
        let planeTexture = SKTexture(image: planeImage)
        planeNode = SKSpriteNode(texture: planeTexture)

        if let planeNode = planeNode {
            // 调整飞机大小
            planeNode.size = CGSize(width: 24, height: 24)

            // 设置初始位置（起飞滑行起点）
            planeNode.position = CGPoint(x: -sceneSize.width * 0.6, y: 0)
            addChild(planeNode)
        }
    }

    // 创建系统飞机图标图像
    private func createSystemAirplaneImage() -> UIImage {
        // 使用SF Symbols的飞机图标，与模板选择器中的图标完全一致
        let config = UIImage.SymbolConfiguration(pointSize: 24, weight: .regular)
        if let image = UIImage(systemName: "airplane", withConfiguration: config) {
            // 创建一个新的图像上下文来绘制图标
            let renderer = UIGraphicsImageRenderer(size: CGSize(width: 24, height: 24))

            let renderedImage = renderer.image { context in
                // 填充透明背景
                UIColor.clear.setFill()
                context.fill(CGRect(x: 0, y: 0, width: 24, height: 24))

                // 绘制飞机图标，使用黑色
                UIColor.black.setFill()
                image.draw(in: CGRect(x: 0, y: 0, width: 24, height: 24))
            }

            return renderedImage
        }

        // 如果无法加载系统图标，使用备用的飞机纹理
        return createFallbackPlaneImage()
    }

    // 创建备用的飞机图像
    private func createFallbackPlaneImage() -> UIImage {
        let size = CGSize(width: 24, height: 24)
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 绘制一个简单的飞机形状
            let path = UIBezierPath()

            // 飞机主体
            path.move(to: CGPoint(x: 4, y: 12))
            path.addLine(to: CGPoint(x: 20, y: 12))
            path.addLine(to: CGPoint(x: 20, y: 8))
            path.addLine(to: CGPoint(x: 4, y: 8))
            path.close()

            // 飞机机头
            path.move(to: CGPoint(x: 20, y: 10))
            path.addLine(to: CGPoint(x: 24, y: 10))
            path.addLine(to: CGPoint(x: 20, y: 10))

            // 飞机机翼
            path.move(to: CGPoint(x: 12, y: 8))
            path.addLine(to: CGPoint(x: 16, y: 4))
            path.addLine(to: CGPoint(x: 8, y: 4))
            path.addLine(to: CGPoint(x: 12, y: 8))

            UIColor.black.setFill()
            path.fill()
        }
    }

    // MARK: - 更新逻辑

    override func update(_ currentTime: TimeInterval) {
        super.update(currentTime)
        if startTime == 0 { startTime = currentTime; lastUpdateTime = currentTime; return }
        let deltaTime = currentTime - lastUpdateTime
        elapsedTime = currentTime - startTime
        lastUpdateTime = currentTime

        // 更新超频时间计算
        updateOverclockTime(deltaTime: deltaTime)

        // 更新场景元素
        updatePlanePosition()

        // 更新着陆信息
        updateLandingInfo()

        // 超频状态下的特殊效果
        if isOverclocked {
            updateOverclockEffects()
        }
    }

    private func updateOverclockTime(deltaTime: TimeInterval) {
        if isOverclocked {
            if overclockStartTime == 0 {
                overclockStartTime = elapsedTime
            }

            overclockDuration += deltaTime

            // 检查是否达到最小超频时间
            if overclockDuration >= minimumOverclockTime && !hasMinimumOverclockTime {
                hasMinimumOverclockTime = true
            }
        } else {
            overclockStartTime = 0
            overclockDuration = 0
            hasMinimumOverclockTime = false
        }
    }

    private func updatePlanePosition() {
        guard let planeNode = planeNode else { return }

        // 根据进度计算飞机在完整路径上的位置
        let t = CGFloat(progress)

        // 定义各个路径点
        let runwayStartPoint = CGPoint(x: -sceneSize.width * 0.6, y: 0)
        let takeoffPoint = CGPoint(x: -sceneSize.width * 0.35, y: 0)
        let cruisingPoint = CGPoint(x: 0, y: sceneSize.height * 0.3) // 增大弧度
        let landingPoint = CGPoint(x: sceneSize.width * 0.35, y: 0)
        let runwayEndPoint = CGPoint(x: sceneSize.width * 0.6, y: 0)

        // 计算飞机当前位置和旋转角度
        var position = CGPoint.zero
        var angle: CGFloat = 0

        // 根据进度分段计算位置
        if t < 0.2 { // 起飞滑行阶段
            let segmentT = t / 0.2 // 当前段内的进度

            // 直线插值
            position = CGPoint(
                x: runwayStartPoint.x + (takeoffPoint.x - runwayStartPoint.x) * segmentT,
                y: runwayStartPoint.y + (takeoffPoint.y - runwayStartPoint.y) * segmentT
            )

            // 计算旋转角度
            let dx = takeoffPoint.x - runwayStartPoint.x
            let dy = takeoffPoint.y - runwayStartPoint.y
            angle = atan2(dy, dx)

        } else if t < 0.8 { // 主要飞行阶段（弧形）
            let segmentT = (t - 0.2) / 0.6 // 当前段内的进度

            // 二次贝塞尔曲线公式
            let mt = 1 - segmentT
            position = CGPoint(
                x: mt * mt * takeoffPoint.x + 2 * mt * segmentT * cruisingPoint.x + segmentT * segmentT * landingPoint.x,
                y: mt * mt * takeoffPoint.y + 2 * mt * segmentT * cruisingPoint.y + segmentT * segmentT * landingPoint.y
            )

            // 计算切线方向以确定飞机朝向
            let dx = 2 * (1 - segmentT) * (cruisingPoint.x - takeoffPoint.x) + 2 * segmentT * (landingPoint.x - cruisingPoint.x)
            let dy = 2 * (1 - segmentT) * (cruisingPoint.y - takeoffPoint.y) + 2 * segmentT * (landingPoint.y - cruisingPoint.y)
            angle = atan2(dy, dx)

        } else { // 降落滑行阶段
            let segmentT = (t - 0.8) / 0.2 // 当前段内的进度

            // 直线插值
            position = CGPoint(
                x: landingPoint.x + (runwayEndPoint.x - landingPoint.x) * segmentT,
                y: landingPoint.y + (runwayEndPoint.y - landingPoint.y) * segmentT
            )

            // 计算旋转角度
            let dx = runwayEndPoint.x - landingPoint.x
            let dy = runwayEndPoint.y - landingPoint.y
            angle = atan2(dy, dx)
        }

        // 设置飞机位置和旋转
        planeNode.position = position
        planeNode.zRotation = angle

        // 更新路径段的显示状态 - 飞机经过的路径变为实线
        updatePathSegments(progress: progress)

        // 更新起飞点和着陆点标记的可见性
        updateMarkers(progress: progress)

        // 超频状态下的飞机效果
        if isOverclocked {
            // 飞机轻微放大和发光效果
            planeNode.setScale(1.2)
            planeNode.alpha = 1.0

            // 添加发光效果
            if planeNode.children.isEmpty {
                let glow = SKSpriteNode(color: .yellow, size: CGSize(width: 30, height: 30))
                glow.alpha = 0.3
                glow.blendMode = .add
                planeNode.addChild(glow)
            }
        } else {
            planeNode.setScale(1.0)
            planeNode.alpha = 1.0

            // 移除发光效果
            planeNode.removeAllChildren()
        }
    }

    // 更新路径段的显示状态 - 飞机经过的路径变为实线，未经过的路径显示为虚线
    private func updatePathSegments(progress: Double) {
        guard !pathSegments.isEmpty else { return }

        let totalSegments = pathSegments.count
        let progressSegment = Int(progress * Double(totalSegments))

        // 更新每个路径段的显示状态
        for (index, segment) in pathSegments.enumerated() {
            if index <= progressSegment {
                // 飞机已经经过的路径段，显示为实线
                segment.alpha = 1.0
            } else {
                // 飞机未经过的路径段
                if index % 2 == 0 {
                    // 偶数段显示，实现虚线效果
                    segment.alpha = 0.5
                } else {
                    // 奇数段不显示，实现虚线效果
                    segment.alpha = 0.0
                }
            }
        }
    }

    // 更新起飞点和着陆点标记的可见性
    private func updateMarkers(progress: Double) {
        guard let takeoffMarker = takeoffMarker, let landingMarker = landingMarker else { return }

        // 根据飞机位置控制起飞点和着陆点标记的可见性
        if progress < 0.2 {
            // 起飞滑行阶段，显示起飞点，隐藏着陆点
            takeoffMarker.isHidden = false
            landingMarker.isHidden = false

            // 起飞点不需要呼吸效果
            takeoffMarker.removeAllActions()
            takeoffMarker.setScale(1.0)

            // 着陆点需要呼吸效果
            if let breathingAction = breathingAction {
                landingMarker.run(SKAction.repeatForever(breathingAction))
            }
        } else if progress < 0.8 {
            // 主要飞行阶段，隐藏起飞点，显示着陆点
            takeoffMarker.isHidden = true
            landingMarker.isHidden = false

            // 着陆点需要呼吸效果
            if let breathingAction = breathingAction {
                landingMarker.run(SKAction.repeatForever(breathingAction))
            }
        } else if progress < 0.85 {
            // 进近阶段，隐藏起飞点，显示着陆点
            takeoffMarker.isHidden = true
            landingMarker.isHidden = false

            // 着陆点不需要呼吸效果
            landingMarker.removeAllActions()
            landingMarker.setScale(1.0)
        } else {
            // 降落滑行后期，飞机已经经过着陆点，隐藏所有标记
            takeoffMarker.isHidden = true
            landingMarker.isHidden = true
        }
    }

    // 飞行阶段信息
    private enum FlightPhase: String {
        // 起飞前准备
        case engineStartup = "Engine start-up"

        // 起飞
        case takeoffRoll = "Takeoff roll"
        case rotation = "Rotation"
        case liftOff = "Lift-off"
        case initialClimb = "Initial climb"
        case gearRetraction = "Gear retraction"

        // 巡航
        case climbToCruising = "Climb to cruising altitude"
        case enRoute = "En route"

        // 降落准备
        case descent = "Descent"
        case approach = "Approach"

        // 降落
        case touchdown = "Touchdown"
        case landingRoll = "Landing roll"
        case landed = "Landed"

        // 超频状态 - 降落后
        case deboarding = "Deboarding"
        case aircraftShutdown = "Aircraft shutdown"
        case postFlightInspection = "Post-flight inspection"
        case aircraftMaintenance = "Aircraft maintenance"
    }

    private func updateLandingInfo() {
        // 注意：文本更新已经在getFlightPhase方法中处理
        _ = getFlightPhase(progress: progress)

        // 更新到达时间信息
        if let arrivalTimeLabel = arrivalTimeLabel {
            if progress >= 1.0 {
                // 已经完成专注
                let dateFormatter = DateFormatter()
                dateFormatter.locale = Locale(identifier: "en_US") // 强制使用英文区域设置，显示AM/PM
                dateFormatter.dateFormat = "h:mm a" // 例如：4:55 PM

                // 计算实际完成时间
                let completionTime = actualStartTime.addingTimeInterval(elapsedTime)
                let timeString = dateFormatter.string(from: completionTime)

                // 设置到达时间，使用简化文字
                arrivalTimeLabel.text = "Arr \(timeString)"

                // 如果处于超频状态，添加呼吸效果
                if isOverclocked {
                    // 添加呼吸效果
                    // 先移除之前的所有动作，避免重复添加
                    arrivalTimeLabel.removeAllActions()

                    // 创建一个缓慢的呼吸效果
                    let pulseAction = SKAction.sequence([
                        SKAction.fadeAlpha(to: 0.5, duration: 1.2),
                        SKAction.fadeAlpha(to: 1.0, duration: 1.2)
                    ])
                    arrivalTimeLabel.run(SKAction.repeatForever(pulseAction))

                    // 添加简化的超频文字
                    arrivalTimeLabel.text = "Arrived \(timeString)\nOC"
                } else {
                    // 正常完成，移除所有动画
                    arrivalTimeLabel.removeAllActions()
                }
            } else {
                // 还未到达
                arrivalTimeLabel.text = "Estimated arrival"
            }
        }
    }

    // 当前飞行阶段和上一个飞行阶段
    private var currentPhase: FlightPhase = .engineStartup
    private var lastPhaseChangeTime: TimeInterval = 0
    private var phaseTransitionDuration: TimeInterval = 2.0 // 状态过渡时间（2秒）

    // 根据进度获取当前飞行阶段
    private func getFlightPhase(progress: Double) -> FlightPhase {
        // 计算目标阶段
        let targetPhase: FlightPhase

        // 定义各个阶段的进度范围
        switch progress {
        case 0.0..<0.05:
            targetPhase = .engineStartup
        case 0.05..<0.10:
            targetPhase = .takeoffRoll
        case 0.10..<0.15:
            targetPhase = .rotation
        case 0.15..<0.20:
            targetPhase = .liftOff
        case 0.20..<0.25:
            targetPhase = .initialClimb
        case 0.25..<0.30:
            targetPhase = .gearRetraction
        case 0.30..<0.40:
            targetPhase = .climbToCruising
        case 0.40..<0.70:
            // 巡航阶段占据最长时间，只使用"En route"状态
            targetPhase = .enRoute
        case 0.70..<0.80:
            targetPhase = .descent
        case 0.80..<0.85:
            targetPhase = .approach
        case 0.85..<0.95: // 当飞机接近着陆点时，显示接地状态
            targetPhase = .touchdown
        case 0.95..<1.0:
            targetPhase = .landingRoll
        case 1.0:
            targetPhase = .landed
        case _ where progress > 1.0 && isOverclocked:
            // 超频状态 - 降落后的阶段
            let overclockTime = overclockDuration

            if overclockTime < 30 { // 前30秒
                targetPhase = .deboarding
            } else if overclockTime < 60 { // 30-60秒
                targetPhase = .aircraftShutdown
            } else if overclockTime < 120 { // 1-2分钟
                targetPhase = .postFlightInspection
            } else { // 2分钟以上
                targetPhase = .aircraftMaintenance
            }
        default:
            targetPhase = .enRoute
        }

        // 如果目标阶段与当前阶段不同，则平滑过渡
        if targetPhase != currentPhase {
            // 记录阶段变化时间
            lastPhaseChangeTime = elapsedTime
            currentPhase = targetPhase

            // 平滑过渡文本变化
            if let landingInfoLabel = landingInfoLabel {
                // 创建淡出淡入动画
                let fadeOut = SKAction.fadeAlpha(to: 0.0, duration: 0.5)
                let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 0.5)
                let updateText = SKAction.run {
                    landingInfoLabel.text = targetPhase.rawValue
                }

                // 执行动画序列
                landingInfoLabel.run(SKAction.sequence([fadeOut, updateText, fadeIn]))
            }
        }

        return currentPhase
    }

    private func updateOverclockEffects() {
        // 控制效果频率，使其更加稀疏
        let currentTime = elapsedTime
        let minTimeBetweenEffects: TimeInterval = 3.0 // 至少3秒才添加一个新效果

        if currentTime - lastEffectTime < minTimeBetweenEffects {
            return // 如果时间间隔太短，则不添加新效果
        }

        // 更新最后效果时间
        lastEffectTime = currentTime

        // 添加一定的随机性，使效果不那么频繁
        if Double.random(in: 0...1) > 0.8 { // 只有20%的几率添加效果
            addJourneyEffect()
        }
    }

    private func addJourneyEffect() {
        guard let planeNode = planeNode else { return }

        // 创建一个轨迹效果
        let trailEffect = SKSpriteNode(texture: createGlowTexture())
        trailEffect.size = CGSize(width: 16, height: 16)
        trailEffect.position = planeNode.position
        trailEffect.zPosition = planeNode.zPosition - 1
        trailEffect.alpha = 0.0

        // 使用柔和的颜色
        let trailColor = UIColor(red: 1.0, green: 0.9, blue: 0.4, alpha: 0.6)
        trailEffect.color = trailColor
        trailEffect.colorBlendFactor = 0.8

        addChild(trailEffect)
        overclockEffects.append(trailEffect)

        // 创建非常缓慢的动画
        let duration = Double.random(in: 2.0...3.0)

        // 淡入淡出动画
        let fadeAction = SKAction.sequence([
            SKAction.fadeAlpha(to: 0.5, duration: 0.5),
            SKAction.wait(forDuration: duration - 1.0),
            SKAction.fadeAlpha(to: 0, duration: 0.5),
            SKAction.removeFromParent()
        ])

        // 缩小动画
        let scaleAction = SKAction.scale(to: 0.5, duration: duration)

        // 组合动画
        let combinedAction = SKAction.group([fadeAction, scaleAction])

        trailEffect.run(combinedAction) { [weak self, weak trailEffect] in
            if let trailEffect = trailEffect, let index = self?.overclockEffects.firstIndex(of: trailEffect) {
                self?.overclockEffects.remove(at: index)
            }
        }
    }

    // 创建柔和的光晕纹理
    private func createGlowTexture() -> SKTexture {
        let size = CGSize(width: 32, height: 32)
        let renderer = UIGraphicsImageRenderer(size: size)

        let texture = renderer.image { context in
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            let maxRadius = min(size.width, size.height) / 2

            // 创建径向渐变
            let colors = [UIColor.white.cgColor, UIColor.clear.cgColor]
            let colorLocations: [CGFloat] = [0.0, 1.0]

            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(),
                                      colors: colors as CFArray,
                                      locations: colorLocations)!

            context.cgContext.drawRadialGradient(gradient,
                                               startCenter: center,
                                               startRadius: 0,
                                               endCenter: center,
                                               endRadius: maxRadius,
                                               options: [])
        }

        return SKTexture(image: texture)
    }

    // MARK: - 公共方法

    // 获取超频奖励倍率
    func getOverclockBonusFactor() -> Double {
        if !isOverclocked || overclockDuration < minimumOverclockTime {
            return 1.0
        }

        // 基础奖励
        let baseBonus = 1.2

        // 额外奖励，基于超频时间，最多增加0.3
        let extraBonus = min(0.3, overclockDuration / (20 * 60) * 0.3)

        return min(maxOverclockBonus, baseBonus + extraBonus)
    }

    // 重置超频状态
    func resetOverclockState() {
        isOverclocked = false
        overclockDuration = 0
        overclockStartTime = 0
        hasMinimumOverclockTime = false
    }
}

// MARK: - 预览
struct JourneyTimingView_Previews: PreviewProvider {
    static var previews: some View {
        JourneyTimingView_PreviewWrapper()
            .preferredColorScheme(.dark)
    }
}

struct JourneyTimingView_PreviewWrapper: View {
    @State private var progress: Double = 0.0
    @State private var isTimingStarted: Bool = false // 默认为未开始计时状态，便于测试过渡效果
    var body: some View {
        VStack {
            JourneyTimingView(progress: progress, isTimingStarted: isTimingStarted)
                .frame(height: 250)
                .padding()

            Slider(value: $progress, in: 0...1, step: 0.01)
                .padding()
            Text("Progress: \(progress, specifier: "%.2f")")
                .foregroundColor(.white)

            Button("切换计时状态") {
                // 使用按钮替代Toggle，更直观地测试过渡效果
                withAnimation {
                    isTimingStarted.toggle()
                }
            }
            .padding()
            .foregroundColor(.white)
            .background(Color.blue.opacity(0.6))
            .cornerRadius(8)
        }
        .background(Color.black.ignoresSafeArea())
    }
}
