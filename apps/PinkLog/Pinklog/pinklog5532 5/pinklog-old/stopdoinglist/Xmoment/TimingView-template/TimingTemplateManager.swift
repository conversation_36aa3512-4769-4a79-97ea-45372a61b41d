import SwiftUI
import Combine

// 支持的计时模板
enum TimingTemplate: String, CaseIterable, Identifiable, Codable {
    case mindAura = "mind_aura"
    case lofi = "lofi"
    case journey = "journey"

    var id: String { self.rawValue }

    // 获取当前模板的图标
    var icon: String {
        switch self {
        case .mindAura: return "waveform.path.ecg"
        case .lofi: return "music.note"
        case .journey: return "airplane"
        }
    }

    // 模板描述
    var description: String {
        switch self {
        case .mindAura:
            return NSLocalizedString("mind_aura_description", comment: "Mind aura template description")
        case .lofi:
            return NSLocalizedString("lofi_description", comment: "Lofi template description")
        case .journey:
            return NSLocalizedString("journey_description", comment: "Journey template description")
        }
    }

    // 本地化显示名称
    var localizedName: String {
        switch self {
        case .mindAura: return NSLocalizedString("mind_aura", comment: "Mind aura template name")
        case .lofi: return NSLocalizedString("lofi", comment: "Lofi template name")
        case .journey: return NSLocalizedString("journey", comment: "Journey template name")
        }
    }
}

// 计时模板管理器
class TimingTemplateManager: ObservableObject {
    @Published var currentTemplate: TimingTemplate

    // 用户默认键
    private let templateKey = "XMomentTimingTemplate"

    init() {
        // 从用户默认值加载模板
        if let savedTemplateData = UserDefaults.standard.data(forKey: templateKey),
           let savedTemplate = try? JSONDecoder().decode(TimingTemplate.self, from: savedTemplateData) {
            // 检查已保存的模板是否仍然是有效 case
            if TimingTemplate.allCases.contains(savedTemplate) {
                self.currentTemplate = savedTemplate
            } else {
                self.currentTemplate = .mindAura // 如果无效，则重置为默认模板
                UserDefaults.standard.removeObject(forKey: templateKey) // 清除无效设置
            }
        } else {
            // 默认模板
            self.currentTemplate = .mindAura
        }
    }

    // 设置模板并保存到UserDefaults
    func setTemplate(_ template: TimingTemplate) {
        self.currentTemplate = template

        // 保存设置
        if let templateData = try? JSONEncoder().encode(template) {
            UserDefaults.standard.set(templateData, forKey: templateKey)
        }
    }

    // 获取适合当前模板的计时视图
    func timingView(progress: Double, onOverclockStatusUpdated: ((Bool, Double, TimeInterval) -> Void)? = nil, isTimingStarted: Bool = true, isFreeMode: Bool = false) -> AnyView {
        switch currentTemplate {
        case .mindAura:
            return AnyView(Focus_MindAuraView(progress: progress, isFreeMode: isFreeMode, onOverclockStatusUpdated: onOverclockStatusUpdated))
        case .lofi:
            return AnyView(LofiTimingView(progress: progress, onOverclockStatusUpdated: onOverclockStatusUpdated, isFreeMode: isFreeMode))
        case .journey:
            return AnyView(JourneyTimingView(progress: progress, isTimingStarted: isTimingStarted, isFreeMode: isFreeMode, onOverclockStatusUpdated: onOverclockStatusUpdated))
        }
    }
}

// 计时模板选择视图
struct TimingTemplateSelectionView: View {
    @EnvironmentObject private var templateManager: TimingTemplateManager
    @Environment(\.dismiss) private var dismiss

    @State private var selectedTemplate: TimingTemplate

    init() {
        // 使用 EnvironmentObject 初始化 selectedTemplate
        _selectedTemplate = State(initialValue: TimingTemplateManager().currentTemplate)
    }

    var body: some View {
        NavigationStack {
            List {
                // 模板选择区
                Section(header: Text(NSLocalizedString("timing_template_title", comment: "Timing animation templates"))) {
                    ForEach(TimingTemplate.allCases) { template in
                        templateOptionRow(template)
                    }
                }

                // 模板预览区
                Section(header: Text(NSLocalizedString("template_preview", comment: "Template preview"))) {
                    VStack(alignment: .center, spacing: 10) {
                        ZStack {
                            // 背景矩形
                            Rectangle()
                                .fill(Color.secondary.opacity(0.15))
                                .frame(height: 200)
                                .clipShape(RoundedRectangle(cornerRadius: 12))

                            // 模板名称提示
                            VStack(spacing: 12) {
                                Image(systemName: selectedTemplate.icon)
                                    .font(.system(size: 40))
                                    .foregroundColor(.blue)

                                Text(selectedTemplate.localizedName)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                            }
                        }

                        // 模板描述
                        Text(selectedTemplate.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.vertical, 10)
                    .frame(maxWidth: .infinity)
                }
            }
            .navigationTitle(NSLocalizedString("timing_template_title", comment: "Timing animation templates"))
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(NSLocalizedString("done", comment: "Done")) {
                        // 应用选择的模板
                        templateManager.setTemplate(selectedTemplate)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }

                ToolbarItem(placement: .navigationBarLeading) {
                    Button(NSLocalizedString("cancel", comment: "Cancel")) {
                        dismiss()
                    }
                }
            }
            // 在视图出现时同步 EnvironmentObject 的当前模板
            .onAppear {
                selectedTemplate = templateManager.currentTemplate
            }
        }
    }

    // 模板选项行
    private func templateOptionRow(_ template: TimingTemplate) -> some View {
        HStack {
            Image(systemName: template.icon)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading) {
                Text(template.localizedName)
                    .font(.system(size: 17, weight: .medium))
            }

            Spacer()

            if selectedTemplate == template {
                Image(systemName: "checkmark")
                    .foregroundColor(.blue)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            selectedTemplate = template
        }
        .padding(.vertical, 4)
    }
}
