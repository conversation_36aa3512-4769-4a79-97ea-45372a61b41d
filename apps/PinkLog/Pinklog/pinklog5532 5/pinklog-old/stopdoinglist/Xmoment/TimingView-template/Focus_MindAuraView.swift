// Focus_MindAuraView.swift
// Integrates the MindAuraScene using SpriteView.

import SwiftUI
import SpriteKit // Import SpriteKit

struct Focus_MindAuraView: View {
    var progress: Double // Input progress 0.0 to 1.0
    var isFreeMode: Bool = false // 新增：是否自由模式
    // 新增：添加获取超频状态和倍率的回调
    var onOverclockStatusUpdated: ((Bool, Double, TimeInterval) -> Void)? = nil

    @Environment(\.scenePhase) private var scenePhase // 新增：监听生命周期
    // Create and hold the SKScene instance
    // Initialize with a temporary size, it will be updated by GeometryReader
    @StateObject private var scene: MindAuraScene = {
        let scene = MindAuraScene(size: CGSize(width: 300, height: 200)) // Initial placeholder size
        scene.scaleMode = .resizeFill // Ensure scale mode is set
        return scene
    }()

    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            GeometryReader { geometry in
                SpriteView(scene: scene, options: [.allowsTransparency])
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .onAppear {
                        // Set the initial size when the view appears
                        print("GeometryReader appeared, size: \(geometry.size)")
                        scene.sceneSize = geometry.size
                        scene.isFreeMode = isFreeMode // 新增：同步 isFreeMode
                    }
                    .onChange(of: geometry.size) { oldSize, newSize in
                        // Update the scene size if the view layout changes
                         print("!!! Focus_MindAuraView GeometryReader size changed: \(newSize)")
                        scene.sceneSize = newSize
                    }
                    .onChange(of: progress) { oldProgress, newProgress in
                        // Pass the progress value to the scene
                        scene.progress = newProgress
                        
                        // Check if progress reaches 100% and set overclocked state if not already set
                        // Note: This assumes the view remains visible after progress hits 1.0.
                        // Handling user interaction to *stop* overclocking likely happens in a parent view.
                        if newProgress >= 1.0 && !scene.isOverclocked {
                            print("Progress reached 1.0, entering Overclocked state.")
                            scene.isOverclocked = true
                        }
                    }
                    .onChange(of: isFreeMode) { _, newValue in
                        scene.isFreeMode = newValue // 新增：支持动态切换
                    }
                    .onChange(of: scene.hasMinimumOverclockTime) { _, hasMinimum in
                        // 当达到最小超频时间时，通知父视图
                        if hasMinimum {
                            print("已达到最小超频时间要求 (5分钟)")
                        }
                        // 调用回调，传递超频状态、奖励倍率和累计超频时间
                        onOverclockStatusUpdated?(
                            scene.isOverclocked,
                            scene.getOverclockBonusFactor(),
                            scene.overclockDuration
                        )
                    }
                    .onDisappear {
                        // 在视图消失时，确保回调最终状态
                        onOverclockStatusUpdated?(
                            scene.isOverclocked,
                            scene.getOverclockBonusFactor(),
                            scene.overclockDuration
                        )
                        // 重置超频状态
                        scene.resetOverclockState()
                    }
            }
            .clipped() // Clip the SpriteView to its bounds
        }
        .onChange(of: scenePhase) { _, newPhase in
            if newPhase == .background {
                scene.isPaused = true // 进入后台暂停动画
            } else if newPhase == .active {
                scene.isPaused = false // 回到前台恢复动画
            }
        }
    }
}

// MARK: - Preview (Optional, but recommended)
struct Focus_MindAuraView_Previews: PreviewProvider {
    static var previews: some View {
        Focus_MindAuraView_PreviewWrapper()
            .preferredColorScheme(.dark)
    }
}

struct Focus_MindAuraView_PreviewWrapper: View {
    @State private var progress: Double = 0.0
    var body: some View {
        VStack {
            Focus_MindAuraView(progress: progress)
                .frame(height: 250)
                .border(Color.gray.opacity(0.5))

            Slider(value: $progress, in: 0...1, step: 0.01)
                .padding()
            Text("Progress: \(progress, specifier: "%.2f")")
                .foregroundColor(.white)
        }
        .background(Color.black.ignoresSafeArea())
    }
}

// MARK: - Extensions (Make sure these are accessible)
// If not defined globally, include these extensions here or ensure they are in scope.

extension Double {
    func clamped(to range: ClosedRange<Double>) -> Double {
        return Swift.min(Swift.max(self, range.lowerBound), range.upperBound)
    }
}
extension CGFloat {
    func clamped(to range: ClosedRange<CGFloat>) -> CGFloat {
        return Swift.min(Swift.max(self, range.lowerBound), range.upperBound)
    }
}
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default: (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(.sRGB, red: Double(r) / 255, green: Double(g) / 255, blue: Double(b) / 255, opacity: Double(a) / 255)
    }
}

