import SwiftUI
import SpriteKit
import UIKit
import ImageIO

// Lofi风格的计时视图，使用GIF文件
struct LofiTimingView: View {
    var progress: Double // 输入进度 0.0 到 1.0

    // 超频状态和倍率的回调
    var onOverclockStatusUpdated: ((Bool, Double, TimeInterval) -> Void)? = nil
    
    // 自由模式标记
    var isFreeMode: Bool = false

    // 超频状态
    @State private var isOverclocked: Bool = false
    @State private var overclockDuration: TimeInterval = 0
    @State private var overclockStartTime: TimeInterval = 0
    @State private var hasMinimumOverclockTime: Bool = false
    @State private var lastUpdateTime: Date = Date()
    
    // 每次应用启动时随机选择的GIF索引
    private static let randomGifIndex: Int = {
        let randomIndex = Int.random(in: 1...3)
        // 保存随机选择的索引，供预览使用
        UserDefaults.standard.set(randomIndex, forKey: "selectedLofiGifIndex")
        return randomIndex
    }()
    
    // 使用静态属性确保每次应用启动只随机一次
    private let gifIndex: Int = LofiTimingView.randomGifIndex
    
    // 超频相关常量
    private let minimumOverclockTime: TimeInterval = 5 * 60 // 5分钟
    private let maxOverclockBonus: Double = 1.5 // 最大超频奖励倍率
    
    // 生命周期计时器
    @State private var timer: Timer? = nil

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 黑色背景
                Color.black.edgesIgnoringSafeArea(.all)
                
                // 使用GIF播放器
                AnimatedGifView(gifIndex: gifIndex)
                .frame(width: geometry.size.width, height: geometry.size.height)
                    .id("gifView")
            }
                .onAppear {
                // 设置计时器，用于更新超频状态
                timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                    updateOverclockTime()
                }
                }
                .onChange(of: progress) { oldProgress, newProgress in
                    // 检查进度是否达到100%并设置超频状态
                if newProgress >= 1.0 && !isOverclocked {
                        print("Progress reached 1.0, entering Overclocked state.")
                    isOverclocked = true
                }
            }
                .onDisappear {
                    // 在视图消失时，确保回调最终状态
                    onOverclockStatusUpdated?(
                    isOverclocked,
                    getOverclockBonusFactor(),
                    overclockDuration
                )
                
                // 停止所有计时器
                timer?.invalidate()
                timer = nil
                
                // 重置超频状态
                resetOverclockState()
            }
        }
    }
    
    // 更新超频时间
    private func updateOverclockTime() {
        if isOverclocked {
            let now = Date()
            let deltaTime = now.timeIntervalSince(lastUpdateTime)
            lastUpdateTime = now
            
            if overclockStartTime == 0 {
                overclockStartTime = Date().timeIntervalSinceReferenceDate
            }

            overclockDuration += deltaTime

            // 检查是否达到最小超频时间
            if overclockDuration >= minimumOverclockTime && !hasMinimumOverclockTime {
                hasMinimumOverclockTime = true
                
                // 通知回调
                onOverclockStatusUpdated?(
                    isOverclocked,
                    getOverclockBonusFactor(),
                    overclockDuration
                )
            } else if overclockDuration.truncatingRemainder(dividingBy: 1.0) < 0.1 {
                // 每秒更新一次回调
                onOverclockStatusUpdated?(
                    isOverclocked,
                    getOverclockBonusFactor(),
                    overclockDuration
                )
            }
        }
    }
    
    // 获取超频奖励倍率
    private func getOverclockBonusFactor() -> Double {
        if !isOverclocked || overclockDuration < minimumOverclockTime {
            return 1.0
        }
        
        // 基础奖励
        let baseBonus = 1.2
        
        // 额外奖励，基于超频时间，最多增加0.3
        let extraBonus = min(0.3, overclockDuration / (20 * 60) * 0.3)
        
        return min(maxOverclockBonus, baseBonus + extraBonus)
    }
    
    // 重置超频状态
    private func resetOverclockState() {
        isOverclocked = false
        overclockDuration = 0
        overclockStartTime = 0
        hasMinimumOverclockTime = false
    }
}

// LofiGIF预览视图
struct LofiPreviewView: View {
    // 使用AppStorage直接绑定到UserDefaults
    @AppStorage("selectedLofiGifIndex") private var selectedGifIndex: Int = 1
    @State private var showingSelector: Bool = false
    
    var body: some View {
        VStack {
            // GIF预览
            AnimatedGifView(gifIndex: selectedGifIndex)
                .aspectRatio(contentMode: .fill)
                .frame(height: 200)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
                .onTapGesture {
                    showingSelector = true
                }
            
            // 提示文本
            Text("当前计时背景")
                .font(.caption)
                .foregroundColor(.gray)
                .padding(.top, 4)
                
            if showingSelector {
                // GIF选择器
                HStack(spacing: 16) {
                    ForEach(1...3, id: \.self) { index in
                        GifThumbnail(index: index, isSelected: selectedGifIndex == index) {
                            selectedGifIndex = index
                            showingSelector = false
                        }
                    }
                }
                .padding(.top, 10)
                .transition(.opacity)
                .animation(.easeInOut, value: showingSelector)
            }
        }
        .padding(.vertical)
    }
}

// GIF缩略图组件
struct GifThumbnail: View {
    let index: Int
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            AnimatedGifView(gifIndex: index)
                .aspectRatio(contentMode: .fill)
                .frame(width: 80, height: 80)
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                )
                .shadow(color: isSelected ? Color.blue.opacity(0.3) : Color.clear, radius: 4, x: 0, y: 0)
        }
    }
}

// GIF动画播放器实现
struct AnimatedGifView: UIViewRepresentable {
    var gifIndex: Int
    
    // 静态缓存，用于存储已加载的GIF图像
    private static var imageCache: [Int: [UIImage]] = [:]
    private static var durationCache: [Int: TimeInterval] = [:]
    
    func makeUIView(context: Context) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .black
        
        // 加载动画
        if let gifImageView = createGifImageView() {
            containerView.addSubview(gifImageView)
            
            // 使GifImageView填充整个容器
            gifImageView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                gifImageView.topAnchor.constraint(equalTo: containerView.topAnchor),
                gifImageView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
                gifImageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                gifImageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor)
            ])
            
            gifImageView.contentMode = .scaleAspectFill
        }
        
        return containerView
    }
    
    // 禁用更新，防止闪烁
    func updateUIView(_ uiView: UIView, context: Context) {
        // 不做任何更新，保持稳定
    }
    
    private func createGifImageView() -> UIImageView? {
        print("尝试加载GIF图像，索引: \(gifIndex)")
        
        // 检查缓存中是否已有该索引的GIF
        if let cachedImages = AnimatedGifView.imageCache[gifIndex],
           let cachedDuration = AnimatedGifView.durationCache[gifIndex],
           !cachedImages.isEmpty {
            // 使用缓存的图像
            print("使用缓存的GIF: 索引\(gifIndex), \(cachedImages.count)帧")
            let imageView = UIImageView()
            imageView.animationImages = cachedImages
            imageView.animationDuration = cachedDuration
            imageView.animationRepeatCount = 0 // 无限循环
            imageView.startAnimating()
            return imageView
        }
        
        // 否则从文件加载
        let gifName = "\(gifIndex).gif"
        print("尝试加载的GIF文件名: \(gifName)")
        
        // 显示搜索路径
        let paths = Bundle.main.paths(forResourcesOfType: "gif", inDirectory: nil)
        print("Bundle中所有GIF文件路径: \(paths)")
        
        // 从主bundle加载GIF文件
        if let path = Bundle.main.path(forResource: String(gifIndex), ofType: "gif") {
            print("从主bundle加载GIF: \(path)")
            
            if let gifData = try? Data(contentsOf: URL(fileURLWithPath: path)) {
                print("成功加载GIF数据，大小: \(gifData.count)字节")
                
                // 使用CoreGraphics解析GIF
                if let source = CGImageSourceCreateWithData(gifData as CFData, nil) {
                    let frameCount = CGImageSourceGetCount(source)
                    print("GIF帧数: \(frameCount)")
                    
                    if frameCount > 1 {
                        var images = [UIImage]()
                        var delays = [Double]()
                        
                        // 解析所有帧
                        for i in 0..<frameCount {
                            if let cgImage = CGImageSourceCreateImageAtIndex(source, i, nil) {
                                let uiImage = UIImage(cgImage: cgImage)
                                images.append(uiImage)
                                
                                // 获取帧延迟时间
                                if let properties = CGImageSourceCopyPropertiesAtIndex(source, i, nil) as? [String: Any],
                                   let gifDict = properties[kCGImagePropertyGIFDictionary as String] as? [String: Any],
                                   let delayTime = gifDict[kCGImagePropertyGIFDelayTime as String] as? Double {
                                    delays.append(delayTime)
            } else {
                                    delays.append(0.1) // 默认0.1秒
                                }
                            }
                        }
                        
                        if !images.isEmpty {
                            // 计算总动画时长
                            let totalDuration = delays.reduce(0, +)
                            
                            // 保存到缓存
                            AnimatedGifView.imageCache[gifIndex] = images
                            AnimatedGifView.durationCache[gifIndex] = totalDuration
                            
                            let imageView = UIImageView()
                            imageView.animationImages = images
                            imageView.animationDuration = totalDuration
                            imageView.animationRepeatCount = 0 // 无限循环
                            imageView.startAnimating()
                            
                            print("开始播放GIF动画，总时长: \(totalDuration)秒")
                            return imageView
                        }
                    } else if let cgImage = CGImageSourceCreateImageAtIndex(source, 0, nil) {
                        // 只有一帧，显示为静态图片
                        print("GIF只有一帧，显示为静态图片")
                        let image = UIImage(cgImage: cgImage)
                        AnimatedGifView.imageCache[gifIndex] = [image]
                        AnimatedGifView.durationCache[gifIndex] = 0
                        return UIImageView(image: image)
                    }
                }
            }
        }
        
        // 如果GIF加载失败，创建渐变背景
        print("GIF加载失败，使用渐变背景")
        let gradientLayer = CAGradientLayer()
        let colors: [[CGColor]] = [
            // 针对索引1的颜色方案
            [
                UIColor(red: 0.4, green: 0.2, blue: 0.5, alpha: 1.0).cgColor,
                UIColor(red: 0.1, green: 0.0, blue: 0.2, alpha: 1.0).cgColor
            ],
            // 针对索引2的颜色方案
            [
                UIColor(red: 0.1, green: 0.2, blue: 0.4, alpha: 1.0).cgColor,
                UIColor(red: 0.0, green: 0.0, blue: 0.2, alpha: 1.0).cgColor
            ],
            // 针对索引3的颜色方案
            [
                UIColor(red: 0.4, green: 0.1, blue: 0.3, alpha: 1.0).cgColor,
                UIColor(red: 0.1, green: 0.0, blue: 0.1, alpha: 1.0).cgColor
            ]
        ]
        
        // 确保索引在有效范围内
        let colorIndex = max(0, min(gifIndex - 1, colors.count - 1))
        gradientLayer.colors = colors[colorIndex]
        gradientLayer.locations = [0.0, 1.0]
        
        let size = CGSize(width: 400, height: 300)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        if let context = UIGraphicsGetCurrentContext() {
            gradientLayer.render(in: context)
            if let image = UIGraphicsGetImageFromCurrentImageContext() {
                UIGraphicsEndImageContext()
                // 缓存生成的渐变图像
                AnimatedGifView.imageCache[gifIndex] = [image]
                AnimatedGifView.durationCache[gifIndex] = 0
                return UIImageView(image: image)
            }
        }
        UIGraphicsEndImageContext()
        
        return UIImageView()
    }
}

// MARK: - 预览
struct LofiTimingView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            // 显示预览选择视图
            LofiPreviewView()
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(16)
                .padding()
            
            // 显示计时视图预览
        LofiTimingView_PreviewWrapper()
                .frame(height: 300)
        }
            .preferredColorScheme(.dark)
    }
}

struct LofiTimingView_PreviewWrapper: View {
    @State private var progress: Double = 0.0
    var body: some View {
        VStack {
            LofiTimingView(progress: progress)
                .frame(height: 250)
                .border(Color.gray.opacity(0.5))

            Slider(value: $progress, in: 0...1, step: 0.01)
                .padding()
            Text("Progress: \(progress, specifier: "%.2f")")
                .foregroundColor(.white)
        }
        .background(Color.black.ignoresSafeArea())
    }
}
