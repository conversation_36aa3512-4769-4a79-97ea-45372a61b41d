import SwiftUI

// 这个文件提供了一个飞机图标的SwiftUI视图，可以用于生成图像资源
struct AirplaneIcon: View {
    var body: some View {
        Image(systemName: "airplane")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 24, height: 24)
            .rotationEffect(.degrees(45))
            .foregroundColor(.black)
    }
}

#Preview {
    AirplaneIcon()
        .padding()
        .background(Color.yellow)
}
