// MindAuraScene.swift
// Hybrid approach: Particle compression -> Crossfade -> Smooth line rendering

import SwiftUI
import SpriteKit
import Combine

// MARK: - Particle Data Structure (Still needed for initial phase)
private class Particle {
    let id = UUID()
    var node: SKSpriteNode // Use SKSpriteNode for initial phase
    var initialPosition: CGPoint
    var position: CGPoint { get { node.position } set { node.position = newValue } }
    var targetY: CGFloat = 0.0
    var baseColor: UIColor
    var targetColor: UIColor = .white
    var baseSize: CGFloat
    var targetSize: CGFloat = 1.0
    var baseOpacity: CGFloat
    var targetOpacity: CGFloat = 1.0
    var currentOverallAlpha: CGFloat = 1.0 // Store modulated alpha

    var xJitterMagnitude: CGFloat = 1.0
    var yJitterMagnitude: CGFloat = 1.0
    var phaseOffset: Double = Double.random(in: 0...(2 * .pi))

    init(node: SKSpriteNode, initialPosition: CGPoint, baseColor: UIColor, baseSize: CGFloat, baseOpacity: CGFloat) {
        self.node = node
        self.initialPosition = initialPosition
        self.baseColor = baseColor
        self.baseSize = baseSize
        self.baseOpacity = baseOpacity
        node.position = initialPosition
        node.alpha = 0 // Start invisible, fade in later
    }
}

// MARK: - Scene Implementation
class MindAuraScene: SKScene, ObservableObject {

    // MARK: - Properties
    @Published var progress: Double = 0.0
    @Published var isOverclocked: Bool = false // <-- 添加超频状态标志
    @Published var overclockDuration: TimeInterval = 0 // 添加：跟踪总超频时间
    @Published var hasMinimumOverclockTime: Bool = false // 添加：是否达到最小5分钟超频时间要求
    
    var isFreeMode: Bool = false // 新增：是否自由模式
    
    var sceneSize: CGSize = .zero {
        didSet {
            guard sceneSize != oldValue, sceneSize != .zero else { return }
            self.size = sceneSize
            setupNodesAndParticles() // Re-initialize everything
        }
    }
    private var startTime: TimeInterval = 0
    private var lastUpdateTime: TimeInterval = 0
    private var elapsedTime: TimeInterval = 0
    private var currentTotalOffset: Double = 0.0 // <-- 新增：增量计算的总偏移量
    private var overclockStartTime: TimeInterval = -1 // <-- 记录超频开始时间
    private let overclockTransitionDuration: TimeInterval = 10.0 // <-- NEW: 延长过渡时间到 10 秒
    private let minimumOverclockTimeRequired: TimeInterval = 300.0 // 添加：最小超频时间要求 (5分钟 = 300秒)

    // --- Configurable Parameters ---
    private let particleCount: Int = 1500
    private let initialParticleSizeRange: ClosedRange<CGFloat> = 0.8...2.0
    private let initialParticleOpacityRange: ClosedRange<CGFloat> = 0.4...0.9
    // private let particleNodeRadius: CGFloat = 1.0 // Not needed if using texture directly
    // @Published var targetBPM: Double = 70.0 // <-- Keep this as a potential baseline or future input, but we'll use a range now
    private let minTargetBPM: Double = 65.0 // <-- Minimum BPM for fluctuation
    private let maxTargetBPM: Double = 75.0 // <-- Maximum BPM for fluctuation
    private let bpmFluctuationPeriod: Double = 15.0 // <-- Seconds for one BPM fluctuation cycle
    private let finalECGLineThickness: CGFloat = 1.5
    private let finalECGColor: UIColor = UIColor(Color(hex: "90e0f0")) // Base cyan color
    // private let overclockedECGColor: UIColor = UIColor(hue: 0.52, saturation: 0.4, brightness: 1.0, alpha: 1.0) // <-- Previous brighter cyan
    private let overclockedECGColor: UIColor = UIColor(hue: 0.75, saturation: 0.45, brightness: 0.75, alpha: 1.0) // <-- Calm Purple for overclocked state
    private let initialNoisyLineThickness: CGFloat = 6.0 // Renamed from noisyLineThicknessStart
    private let noisyLineColorStart: UIColor = UIColor(Color(hex: "605080")) // Initial noisy color
    // --- Transition Control ---
    private let compressionThreshold: Double = 0.65 // Point where particles *start* behaving like a line
    private let lineAppearanceStart: Double = 0.60 // Start color/size transition
    private let ecgEmergenceStart: Double = 0.75 // ECG wave influence starts
    private let crossfadeStartProgress: Double = 0.78 // Start fading particles out, line in
    private let crossfadeEndProgress: Double = 0.90   // Finish fading particles out, line fully in
    private let finalSmoothnessStart: Double = 0.85 // Jitter reduction start point

    private let initialXJitter: CGFloat = 0.7
    private let initialYJitter: CGFloat = 0.7
    private let breathingEffectMagnitude: Double = 0.03
    private let breathCycleDuration: Double = 5.0
    private let convergenceLerpFactor: CGFloat = 0.15

    // --- Scan Light Parameters ---
    private let scanLightWidth: CGFloat = 100.0 // Width of the scan effect
    private let scanLightIntensity: Double = 2.5 // Increased for debugging
    // private let scanLightSpeed: Double = 150.0  // Speed pixels/sec - Keep for potential scan light effect, but not directly ECG speed
    private let overclockedScrollSpeedFactor: Double = 0.8 // <-- Set to 80% speed for calmer focus

    // --- Overclocked Effect Parameters ---
    private let overclockEffectFrequency: Double = 25.0 // 频率 (Hz)
    private let overclockEffectAmplitude: CGFloat = 0.4  // 振幅 (points)

    // --- ECG Visual Scaling Factors --- 
    private let bpmCalculationWidthFactor: CGFloat = 0.125 // Factor for calculating speed from BPM (makes speed ~50 for 70BPM)
    private let drawingCycleWidthFactor: CGFloat = 1.5   // Factor for phase calculation (makes cycle wider than screen)
    private let shapeCompactnessFactor: CGFloat = 0.4    // NEW: Make shape occupy even less of the cycle, leaving more baseline

    // --- Nodes & Particles ---
    private var particles: [Particle] = []
    private var particleContainer = SKNode()
    private var lineNode: SKShapeNode? // Reintroduce the line node
    private var sharedParticleTexture: SKTexture? // Hold the shared texture

    // MARK: - Scene Lifecycle
    override func didMove(to view: SKView) {
        super.didMove(to: view)
        self.scaleMode = .resizeFill
        self.anchorPoint = CGPoint(x: 0.5, y: 0.5)
        self.backgroundColor = .black
        addChild(particleContainer)

        startTime = 0; lastUpdateTime = 0; elapsedTime = 0

        if sceneSize != .zero {
            setupNodesAndParticles()
        } else {
            let viewBoundsSize = view.bounds.size
            if viewBoundsSize != .zero {
                 DispatchQueue.main.async { self.sceneSize = viewBoundsSize }
            } else { print("Warning: Scene/View size zero.") }
        }
    }

    override func update(_ currentTime: TimeInterval) {
        super.update(currentTime)
        if startTime == 0 { startTime = currentTime; lastUpdateTime = currentTime; return }
        let deltaTime = currentTime - lastUpdateTime // <-- 计算时间增量
        elapsedTime = currentTime - startTime
        lastUpdateTime = currentTime

        // 更新超频时间计算
        updateOverclockTime(deltaTime: deltaTime)

        // Update states based on progress and time, passing deltaTime
        updateSceneElements(elapsedTime: elapsedTime, deltaTime: deltaTime) // <-- 传递 deltaTime
    }

    // MARK: - Setup
    private func setupNodesAndParticles() {
        guard sceneSize != .zero else { return }

        // Clear existing
        particleContainer.removeAllChildren()
        particles = []
        lineNode?.removeFromParent()
        lineNode = nil

        // --- Create Shared Texture ---
        let textureSize = CGSize(width: 8, height: 8)
        sharedParticleTexture = SKTexture(image: createCircleTexture(size: textureSize, color: .white))

        // --- Initialize Particles ---
        particles.reserveCapacity(particleCount)
        let widthRadius = sceneSize.width / 2, heightRadius = sceneSize.height / 2
        for _ in 0..<particleCount {
            let x = CGFloat.random(in: -widthRadius...widthRadius)
            let y = CGFloat.random(in: -heightRadius...heightRadius)
            let initialPos = CGPoint(x: x, y: y)

            guard let texture = sharedParticleTexture else { continue } // Should exist
            let node = SKSpriteNode(texture: texture)
            // Set initial size based on average particle size? Or fixed small size? Let's use base size.
            let baseSize = CGFloat.random(in: initialParticleSizeRange)
            node.size = CGSize(width: baseSize, height: baseSize) // Start with base size
            node.colorBlendFactor = 1.0
            node.zPosition = 0
            node.alpha = 0 // Start invisible

            let brightnessNoise = Double.random(in: 0.8...1.2); let baseGray = 0.8 * brightnessNoise
            let rNoise = Double.random(in: -0.1...0.1), gNoise = Double.random(in: -0.1...0.1), bNoise = Double.random(in: -0.1...0.1)
            let baseColor = UIColor(red: (baseGray+rNoise).c(), green: (baseGray+gNoise).c(), blue: (baseGray+bNoise).c(), alpha: 1.0)
            let baseOpacity = CGFloat.random(in: initialParticleOpacityRange)

            let particle = Particle(node: node, initialPosition: initialPos, baseColor: baseColor, baseSize: baseSize, baseOpacity: baseOpacity)
            particleContainer.addChild(node)
            particles.append(particle)
        }

        // --- Setup Line Node ---
        lineNode = SKShapeNode()
        if let line = lineNode {
            line.lineWidth = finalECGLineThickness // Start thin? Or noisy thick? Let's start noisy.
            line.strokeColor = noisyLineColorStart.withAlphaComponent(0) // Start transparent
            line.lineCap = .round
            line.lineJoin = .round
            line.zPosition = 10 // Ensure line is drawn above particles if needed
            line.isHidden = true // Hidden until crossfade starts
            addChild(line)
        }
         print("Setup complete: \(particles.count) particles, line node added.")
         // Reset incremental offset on setup
         currentTotalOffset = 0.0 // <-- 重置偏移量
          // Perform initial update
           updateSceneElements(elapsedTime: 0, deltaTime: 0) // <-- 传递 deltaTime = 0
    }

    // MARK: - Update Logic

    private func updateSceneElements(elapsedTime: TimeInterval, deltaTime: TimeInterval) {
        guard sceneSize.height > 0, sceneSize.width > 0, (lineNode != nil || !particles.isEmpty) else { return }

        // --- Handle Overclock Start Time --- 
        if isOverclocked && overclockStartTime == -1 {
            overclockStartTime = elapsedTime // Record the time when overclocking starts
            print("DEBUG: Overclock transition started at \(elapsedTime)")
        } else if !isOverclocked && overclockStartTime != -1 {
            overclockStartTime = -1 // Reset if overclocking stops
        }
        
        // --- Calculate Overclock Transition Factor --- 
        var overclockTransitionFactor: Double = 0.0
        if isOverclocked {
            if overclockStartTime != -1 {
                let transitionElapsed = elapsedTime - overclockStartTime
                overclockTransitionFactor = (transitionElapsed / overclockTransitionDuration).clamped(to: 0...1)
            } else {
                // Should not happen if logic above is correct, but fallback to fully transitioned
                overclockTransitionFactor = 1.0 
            }
        } 
        // else: factor remains 0.0

        // --- Global Progress Factors ---
        let overallAlpha = smoothstep(0.0, 0.15, progress)
        let compressionProgress = easeInOutCubic(progress)
        let compressionFactor = 1.0 - compressionProgress
        let lineAppearanceProgress = smoothstep(lineAppearanceStart, finalSmoothnessStart, progress)
        let ecgInfluenceProgress = smoothstep(ecgEmergenceStart, 1.0, progress)
        let jitterReductionProgress = smoothstep(lineAppearanceStart, finalSmoothnessStart, progress)
        let crossfadeProgress = smoothstep(crossfadeStartProgress, crossfadeEndProgress, progress) // 0 -> 1 during fade

        // --- ECG Parameters ---
        // Width for BPM -> Speed calculation
        let bpmCalculationWidth = sceneSize.width * bpmCalculationWidthFactor
        // Width for drawing the waveform phase across the screen
        let drawingCycleWidth = sceneSize.width * drawingCycleWidthFactor

        // --- Calculate Fluctuating Simulated BPM --- 
        let centerBPM = (maxTargetBPM + minTargetBPM) / 2.0
        let amplitudeBPM = (maxTargetBPM - minTargetBPM) / 2.0
        let sineValue = sin(elapsedTime * 2 * .pi / bpmFluctuationPeriod)
        let currentSimulatedBPM = centerBPM + sineValue * amplitudeBPM

        // Calculate required base scroll speed based on simulated BPM and the *BPM calculation width*
        var calculatedBaseScrollSpeed: Double = 50.0 // Default fallback speed
        if bpmCalculationWidth > 0 && currentSimulatedBPM > 0 { // Use bpmCalculationWidth
            calculatedBaseScrollSpeed = (Double(bpmCalculationWidth) * currentSimulatedBPM) / 60.0 // Use bpmCalculationWidth
        }

        // Determine effective scroll speed based on calculated base, overclocked state and transition
        let currentScrollSpeed = lerp(start: calculatedBaseScrollSpeed, end: calculatedBaseScrollSpeed * overclockedScrollSpeedFactor, t: overclockTransitionFactor) // <-- Use calculated base speed
        
        // Incrementally update total offset
        currentTotalOffset += deltaTime * currentScrollSpeed // <-- 使用 deltaTime 和当前速度更新 totalOffset
        let totalOffset = currentTotalOffset // <-- 使用增量计算的 totalOffset
        let breathingShift = sin(elapsedTime * (2 * .pi / breathCycleDuration)) * sceneSize.height * breathingEffectMagnitude

        // --- Particle Updates (Run until crossfade ends) ---
        if progress < crossfadeEndProgress && !particles.isEmpty {
             updateParticlesLogic(
                elapsedTime: elapsedTime,
                overallAlpha: overallAlpha,
                compressionFactor: compressionFactor,
                lineAppearanceProgress: lineAppearanceProgress,
                ecgInfluenceProgress: ecgInfluenceProgress,
                jitterReductionProgress: jitterReductionProgress,
                crossfadeProgress: crossfadeProgress,
                scrollSpeed: currentScrollSpeed,
                totalOffset: totalOffset,
                drawingCycleWidth: drawingCycleWidth, // <-- Pass drawing width
                breathingShift: breathingShift
             )
             particleContainer.isHidden = false
        } else {
            // After crossfade, hide the particle container entirely for performance
            particleContainer.isHidden = true
             // Consider removing particles from array and container here if memory is a concern
             // if !particles.isEmpty { particles = []; particleContainer.removeAllChildren() }
        }

        // --- Line Node Updates (Run from crossfade start) ---
         if let line = lineNode {
            if progress > crossfadeStartProgress {
                updateLineNodeLogic(
                    line: line,
                    elapsedTime: elapsedTime,
                    overallAlpha: overallAlpha,
                    lineAppearanceProgress: lineAppearanceProgress,
                    ecgInfluenceProgress: ecgInfluenceProgress,
                    jitterReductionProgress: jitterReductionProgress,
                    crossfadeProgress: crossfadeProgress,
                    scrollSpeed: currentScrollSpeed,
                    totalOffset: totalOffset, // Pass the incrementally calculated offset
                    drawingCycleWidth: drawingCycleWidth, // <-- Pass drawing width
                    breathingShift: breathingShift,
                    overclockTransitionFactor: overclockTransitionFactor // <-- 传递过渡因子
                )
            line.isHidden = false
            } else {
                line.isHidden = true
            }
         }
    }


    // --- Particle Update Sub-Function ---
    private func updateParticlesLogic(elapsedTime: TimeInterval, overallAlpha: Double, compressionFactor: CGFloat,
                                      lineAppearanceProgress: Double, ecgInfluenceProgress: Double, jitterReductionProgress: Double,
                                      crossfadeProgress: Double, // Receive crossfade progress
                                      scrollSpeed: Double, // Receive the potentially interpolated speed
                                      totalOffset: Double, // <-- Receive the incrementally calculated offset
                                      drawingCycleWidth: CGFloat, // <-- Receive drawing width
                                      breathingShift: Double)
    {
        let currentXJitterMag = lerp(start: initialXJitter, end: 0.0, t: jitterReductionProgress)
        let particleTargetAlpha = 1.0 - crossfadeProgress // Fade out particles

        for particle in particles {
             // 1. Calculate Base Target Y (Compression)
             let compressedTargetY = particle.initialPosition.y * compressionFactor

             // 2. Calculate ECG Target Y (if applicable)
             var ecgTargetY: CGFloat = 0.0
             if ecgInfluenceProgress > 0 {
                  ecgTargetY = calculateECGOffsetForParticle(particle, totalOffset: totalOffset, drawingCycleWidth: drawingCycleWidth) // <-- Pass drawing width
             }

             // 3. Combine Target Ys & Breathing
             let combinedTargetY = lerp(start: compressedTargetY, end: ecgTargetY, t: ecgInfluenceProgress)
             particle.targetY = combinedTargetY + CGFloat(breathingShift)

             // 4. Interpolate Y Position
             let currentConvergenceFactor = lerp(start: 0.05, end: convergenceLerpFactor, t: ecgInfluenceProgress)
             particle.position.y = lerp(start: particle.position.y, end: particle.targetY, t: currentConvergenceFactor)

             // 5. Update X Position (Jitter & Wrapping)
             // 减少X方向的抖动，尤其是当进入ECG阶段时
             let stabilityFactor = Swift.min(1.0, ecgInfluenceProgress * 2.5) // 增加ECG阶段的稳定性
             let reducedJitterX = CGFloat.random(in: -currentXJitterMag...currentXJitterMag) * particle.phaseOffset * (1.0 - CGFloat(stabilityFactor))
             particle.position.x += reducedJitterX
             let widthRadius = sceneSize.width / 2
             if particle.position.x > widthRadius { particle.position.x -= sceneSize.width }
             if particle.position.x < -widthRadius { particle.position.x += sceneSize.width }

             // 6. Update Appearance (Color, Size, Opacity)
             particle.targetColor = ColorLerp(from: particle.baseColor, to: self.finalECGColor, t: lineAppearanceProgress)
             // Size transitions from base random size towards the final line thickness value
             let targetParticleSize = lerp(start: self.initialNoisyLineThickness, end: self.finalECGLineThickness, t: lineAppearanceProgress) // Corrected target size
             particle.targetSize = lerp(start: particle.baseSize, end: targetParticleSize, t: lineAppearanceProgress)
             particle.targetOpacity = lerp(start: particle.baseOpacity, end: 1.0, t: lineAppearanceProgress)

             // Apply to node, modulating final alpha by overall fade and crossfade fade-out
             particle.currentOverallAlpha = particle.targetOpacity * CGFloat(overallAlpha) * CGFloat(particleTargetAlpha)
             particle.node.color = particle.targetColor
             particle.node.alpha = particle.currentOverallAlpha
             particle.node.size = CGSize(width: particle.targetSize, height: particle.targetSize)

             // 应用渲染抖动（仅Y方向），但在ECG阶段大幅减少
             let currentYJitterMag = lerp(start: self.initialYJitter, end: 0.0, t: jitterReductionProgress)
             let reducedYJitter = currentYJitterMag * (1.0 - CGFloat(stabilityFactor))
             let jitterY = CGFloat.random(in: -reducedYJitter...reducedYJitter)
             particle.node.position.y = particle.position.y + jitterY
        }
    }

     // --- Line Node Update Sub-Function ---
     private func updateLineNodeLogic(line: SKShapeNode, elapsedTime: TimeInterval, overallAlpha: Double,
                                       lineAppearanceProgress: Double, ecgInfluenceProgress: Double, jitterReductionProgress: Double,
                                       crossfadeProgress: Double, // Receive crossfade progress
                                       scrollSpeed: Double, // Receive the potentially interpolated speed
                                       totalOffset: Double, // <-- Receive the incrementally calculated offset
                                       drawingCycleWidth: CGFloat, // <-- Receive drawing width
                                       breathingShift: Double,
                                       overclockTransitionFactor: Double) // <-- 接收过渡因子
     {
         // Style Transition (already calculated based on lineAppearanceProgress)
         let currentThickness = lerp(start: self.initialNoisyLineThickness, end: self.finalECGLineThickness, t: lineAppearanceProgress)

         // Calculate the target base color based on overclock transition
         let currentTargetColor = ColorLerp(from: self.finalECGColor, to: self.overclockedECGColor, t: overclockTransitionFactor) // <-- 平滑过渡目标颜色
         let baseLineColor = ColorLerp(from: self.noisyLineColorStart, to: currentTargetColor, t: lineAppearanceProgress) // Lerp towards the interpolated target color

         let lineTargetAlpha = crossfadeProgress // Fade in line
         
         // --- 扫光效果 - 现在只影响颜色亮度，不影响波形振幅 ---
         let scanLightEffectiveSpeed = 150.0 // Keep scan light speed potentially independent
         let scanLightScreenX = (elapsedTime * scanLightEffectiveSpeed).truncatingRemainder(dividingBy: sceneSize.width) - sceneSize.width / 2
         let distanceFromCenter = abs(scanLightScreenX)
         // Adjust scan light sensitivity - make it less pronounced at the final stage or during overclock maybe? Let's keep it for now.
         let scanFactor = smoothstep(scanLightWidth / 2, 0.0, Double(distanceFromCenter))
         
         // 使用扫光因子调整颜色亮度，并基于 isOverclocked 状态选择基础颜色
         let enhancedColor = enhanceColor(baseLineColor, brightnessFactor: 1.0 + Double(scanFactor) * 0.3) // Enhance the interpolated base color

         // DEBUG LOGGING (adjusted):
         /* <-- Remove or comment out debug log
         if isOverclocked {
             print("DEBUG: Overclock active (Transition: \(String(format: "%.2f", overclockTransitionFactor))). Speed: \(String(format: "%.1f", scrollSpeed)), Target Base Color: \(currentTargetColor), Final Stroke Color (before alpha): \(enhancedColor)")
         }
         */

         line.strokeColor = enhancedColor.withAlphaComponent(CGFloat(overallAlpha) * CGFloat(lineTargetAlpha))
         line.lineWidth = currentThickness

         // Path Generation (Includes breathing, ECG, jitter)
         line.path = generateLineCGPath(
             elapsedTime: elapsedTime,
             ecgInfluenceFactor: ecgInfluenceProgress, // Use correct progress factor
             jitterMagnitudeFactor: jitterReductionProgress, // Use jitter reduction for path jitter
             scrollSpeed: scrollSpeed,
             totalOffset: totalOffset, // <-- Pass incremental offset
             drawingCycleWidth: drawingCycleWidth, // <-- Pass drawing width
             baseHeartbeatSpacing: 0.0, // No longer needed directly here
             breathingShift: breathingShift,
             overclockTransitionFactor: overclockTransitionFactor // <-- 传递过渡因子
         )
     }

     // 增强颜色亮度的辅助函数
     private func enhanceColor(_ color: UIColor, brightnessFactor: Double) -> UIColor {
         var h: CGFloat = 0, s: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
         color.getHue(&h, saturation: &s, brightness: &b, alpha: &a)
         
         // 仅增加亮度，保留色相和饱和度
         let newBrightness = min(b * CGFloat(brightnessFactor), 1.0)
         return UIColor(hue: h, saturation: s, brightness: newBrightness, alpha: a)
     }

    // MARK: - Calculation Helpers

    // Calculates ECG offset Y for a given particle's X position
    private func calculateECGOffsetForParticle(_ particle: Particle, totalOffset: Double, drawingCycleWidth: CGFloat) -> CGFloat { // <-- Receive drawingCycleWidth
        let canvasX = particle.position.x
        // Use drawingCycleWidth for phase calculation
        // let calculationCycleWidth = baseHeartbeatWidth + baseHeartbeatSpacing // OLD
        guard drawingCycleWidth > 0 else { return 0.0 } // Avoid division by zero

        // --- Calculate parameters for the CURRENT beat index (using drawing width) ---
        let currentBeatIndex = floor(totalOffset / Double(drawingCycleWidth))
        let uniqueBeatSeed = currentBeatIndex * 3.14159 + 1.618

        // --- Width variation is disabled, keep it simple for now --- 
        let _ = drawingCycleWidth // 使用下划线替换未使用的变量
        let _ = 1.0 // 使用下划线替换未使用的变量

        // --- Calculate Position based on Phase (using drawing width)--- 
        let initialPhaseOffsetCycles = 1.0 // Offset by one cycle
        let effectiveTotalOffset = totalOffset - initialPhaseOffsetCycles * Double(drawingCycleWidth)
        let effectiveX = canvasX - CGFloat(effectiveTotalOffset)

        // Calculate phase based on DRAWING cycle width (smooth 0-1 loop)
        let normalizedPhase = (effectiveX / drawingCycleWidth).truncatingRemainder(dividingBy: 1.0)
        let phase = normalizedPhase < 0 ? normalizedPhase + 1.0 : normalizedPhase

        // --- Map Phase & Calculate Actual ECG Shape --- 
        var rawECGY: Double = 0.0
        if phase < shapeCompactnessFactor { // Only draw shape within the compacted factor
             // Map phase [0, shapeCompactnessFactor) to tInCycle [0, 1) for generateEnhancedECG
             let tInCycle = phase / shapeCompactnessFactor

             // --- Reintroduce subtle variations for ECG shape per beat --- 
             let beatVariation = 1.0 + abs(sin(uniqueBeatSeed * 1.7)) * 0.05 // NEW: 0% to +5% variation (preserves S-wave depth)
             let pWaveScale = 1.0 + cos(uniqueBeatSeed * 2.3) * 0.1  // +/- 10% variation in P-wave scale (Keep)
             let tWaveScale = 1.0 + sin(uniqueBeatSeed * 0.9) * 0.05  // NEW: +/- 5% variation in T-wave scale

             rawECGY = generateEnhancedECG(
                 t: tInCycle, // Use remapped tInCycle
                 pWaveScale: pWaveScale, // Use calculated variation
                 tWaveScale: tWaveScale, // Use calculated variation
                 beatVariation: beatVariation // Use calculated variation
             )
         } // else: rawECGY remains 0.0 for the elongated baseline part

         // --- Visibility Factor based on overall phase --- 
          let visEntryFactor = smoothstep(0.0, 0.1, phase) // Fade in early in the fixed cycle phase
          let visibilityFactor = visEntryFactor // <-- Use only entry factor

         let finalECGAmplitude: Double = Double(sceneSize.height * 0.35)
         let ecgComponentFinal = rawECGY * finalECGAmplitude * visibilityFactor
         
         // 移除扫光对振幅的影响
         return CGFloat(ecgComponentFinal)
    }

    // Generates CGPath for the line node
    private func generateLineCGPath(elapsedTime: TimeInterval, ecgInfluenceFactor: Double, jitterMagnitudeFactor: Double,
                                   scrollSpeed: Double, // Receive the potentially interpolated speed
                                   totalOffset: Double, // <-- Receive the incrementally calculated offset
                                   drawingCycleWidth: CGFloat, // <-- Receive drawing width
                                   baseHeartbeatSpacing: CGFloat, breathingShift: Double,
                                   overclockTransitionFactor: Double) -> CGPath {
         guard sceneSize.width > 0 else { return CGMutablePath() }
        let path = CGMutablePath()
         // 增加采样点数，使线条更平滑
         let steps = Int(sceneSize.width)
         
         // 减少抖动，特别是在ECG区域
         let stabilityFactor = Swift.min(1.0, ecgInfluenceFactor * 2.5) // 增加ECG阶段的稳定性
         let currentYJitterMagnitude = lerp(start: initialYJitter, end: 0.0, t: jitterMagnitudeFactor) * (1.0 - CGFloat(stabilityFactor))
         
         // 不再使用扫光位置计算影响振幅的因子
         // let calculationCycleWidth = baseHeartbeatWidth + baseHeartbeatSpacing // OLD
         guard drawingCycleWidth > 0 else { return CGMutablePath() }

         // Offset by one cycle for start position fix (using drawing width)
         let initialPhaseOffsetCycles = 1.0
         let effectiveTotalOffset = totalOffset - initialPhaseOffsetCycles * Double(drawingCycleWidth)

        var _ = true // 将firstPoint改为下划线
         let startX = -sceneSize.width / 2, endX = sceneSize.width / 2
         
         // 创建平滑点数组以应用平滑
         var points: [CGPoint] = []
         points.reserveCapacity(steps + 1)

        for i in 0...steps {
            let canvasX = startX + (endX - startX) * CGFloat(i) / CGFloat(steps)
             var currentY = CGFloat(breathingShift)

            if ecgInfluenceFactor > 0 {
                  // --- Calculate parameters for the CURRENT beat index (using drawing width) ---
                  let currentBeatIndex = floor(totalOffset / Double(drawingCycleWidth))
                  let uniqueBeatSeed = currentBeatIndex * 3.14159 + 1.618

                  // --- Width variation is disabled, keep it simple for now --- 
                  let _ = drawingCycleWidth // 使用下划线替换未使用的变量
                  let _ = 1.0 // 使用下划线替换未使用的变量

                  // --- Calculate Phase (using drawing width) --- 
                  let effectiveX = canvasX - CGFloat(effectiveTotalOffset)
                  let normalizedPhase = (effectiveX / drawingCycleWidth).truncatingRemainder(dividingBy: 1.0)
                  let phase = normalizedPhase < 0 ? normalizedPhase + 1.0 : normalizedPhase

                  // --- Map Phase & Calculate Actual ECG Shape --- 
                  var rawECGY: Double = 0.0
                  if phase < shapeCompactnessFactor { // Only draw shape within the compacted factor
                      // Map phase [0, shapeCompactnessFactor) to tInCycle [0, 1) for generateEnhancedECG
                      let tInCycle = phase / shapeCompactnessFactor

                      // --- Reintroduce subtle variations for ECG shape per beat --- 
                      let beatVariation = 1.0 + abs(sin(uniqueBeatSeed * 1.7)) * 0.05 // NEW: 0% to +5% variation (preserves S-wave depth)
                      let pWaveScale = 1.0 + cos(uniqueBeatSeed * 2.3) * 0.1  // +/- 10% variation in P-wave scale (Keep)
                      let tWaveScale = 1.0 + sin(uniqueBeatSeed * 0.9) * 0.05  // NEW: +/- 5% variation in T-wave scale

                       rawECGY = generateEnhancedECG(
                           t: tInCycle,
                           pWaveScale: pWaveScale, // Use calculated variation
                           tWaveScale: tWaveScale, // Use calculated variation
                           beatVariation: beatVariation // Use calculated variation
                       )
                  } // else: rawECGY remains 0.0 for the elongated baseline part

                  // --- Visibility Factor --- 
                  let visEntryFactor = smoothstep(0.0, 0.1, phase)
                  let visibilityFactor = visEntryFactor // <-- Use only entry factor

                  let finalECGAmp = Double(sceneSize.height * 0.35)
                  // 直接应用基础振幅，不再乘以扫光增益系数
                  let ecgComponentFinal = rawECGY * finalECGAmp * visibilityFactor
                  currentY += CGFloat(ecgComponentFinal * ecgInfluenceFactor)
              }

             // 加入抖动并收集点
            let yJitter = CGFloat.random(in: -currentYJitterMagnitude...currentYJitterMagnitude)
            currentY += yJitter

            // --- 加入超频状态下的额外能量发散效果 --- 
            if overclockTransitionFactor >= 1.0 { // 仅在过渡完成后应用
                 let overclockWave = sin(elapsedTime * 2 * .pi * overclockEffectFrequency) * overclockEffectAmplitude
                 currentY += CGFloat(overclockWave)
            }

            points.append(CGPoint(x: canvasX, y: currentY))
         }
         
         // 应用平滑算法，特别是在心电图尖峰区域
         /* // DEBUG: Ensure smoothing remains disabled to observe R-peak changes
         if ecgInfluenceFactor > 0.5 && points.count > 3 {
             // 对R波区域应用更平滑的处理
             let smoothedPoints = applyAdaptiveSmoothing(points: points, ecgInfluenceFactor: ecgInfluenceFactor)
             
             // 构建路径
             if !smoothedPoints.isEmpty {
                 path.move(to: smoothedPoints[0])
                 for i in 1..<smoothedPoints.count {
                     path.addLine(to: smoothedPoints[i])
                 }
             }
            } else { // END DEBUG BLOCK
            */
             // 直接使用原始点 (Make sure this block is active)
             if !points.isEmpty {
                 path.move(to: points[0])
                 for i in 1..<points.count {
                     path.addLine(to: points[i])
                 }
            }
         // } // DEBUG: Ensure this marks end of commented block

        return path
    }

    // 自适应平滑算法 - 在R波区域保持特征，同时平滑尖锐变化
    private func applyAdaptiveSmoothing(points: [CGPoint], ecgInfluenceFactor: Double) -> [CGPoint] {
        guard points.count > 3 else { return points }
        
        var result = [CGPoint]()
        result.append(points[0]) // 保留起始点
        
        // 找出可能的R波位置（最高点）
        var maxY: CGFloat = -CGFloat.greatestFiniteMagnitude
        var maxYIndex = 0
        
        for i in 0..<points.count {
            if points[i].y > maxY {
                maxY = points[i].y
                maxYIndex = i
            }
        }
        
        // 平滑窗口大小
        let baseWindowSize = 3
        
        for i in 1..<(points.count-1) {
            // 靠近R波峰时减少平滑
            let distanceToRPeak = abs(i - maxYIndex)
            let isNearRPeak = distanceToRPeak < 5
            
            if isNearRPeak {
                // R波峰附近，保留原始点以保持特征
                result.append(points[i])
            } else {
                // 其他区域应用平滑
                let windowSize = baseWindowSize
                let start = max(0, i - windowSize/2)
                let end = min(points.count - 1, i + windowSize/2)
                
                var sumX: CGFloat = 0
                var sumY: CGFloat = 0
                var count: CGFloat = 0
                
                for j in start...end {
                    sumX += points[j].x
                    sumY += points[j].y
                    count += 1
                }
                
                if count > 0 {
                    result.append(CGPoint(x: sumX/count, y: sumY/count))
                } else {
                    result.append(points[i])
                }
            }
        }
        
        result.append(points[points.count - 1]) // 保留结束点
        return result
    }

    // MARK: - Texture Generation Helper
    private func createCircleTexture(size: CGSize, color: UIColor) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { ctx in
            let rect = CGRect(origin: .zero, size: size).insetBy(dx: 1, dy: 1)
            ctx.cgContext.setFillColor(color.cgColor)
            ctx.cgContext.fillEllipse(in: rect)
        }
    }

    // MARK: - Math Helpers
    // ... (lerp, ColorLerp, smoothstep, easing functions, generateEnhancedECG) ...
    // Ensure CGFloat.clamped is available or implement it
    // Ensure UIColor lerp is correct
    private func ColorLerp(from startColor: UIColor, to endColor: UIColor, t: Double) -> UIColor {
         let tClamped = t.clamped(to: 0...1); var r1: CGFloat = 0, g1: CGFloat = 0, b1: CGFloat = 0, a1: CGFloat = 0; var r2: CGFloat = 0, g2: CGFloat = 0, b2: CGFloat = 0, a2: CGFloat = 0
         startColor.getRed(&r1, green: &g1, blue: &b1, alpha: &a1); endColor.getRed(&r2, green: &g2, blue: &b2, alpha: &a2)
         return UIColor(red: lerp(start: r1, end: r2, t: tClamped), green: lerp(start: g1, end: g2, t: tClamped), blue: lerp(start: b1, end: b2, t: tClamped), alpha: 1.0) // Ignore input alpha for lerp
     }
     private func lerp(start: CGFloat, end: CGFloat, t: Double) -> CGFloat { return start + (end - start) * CGFloat(t.clamped(to: 0...1)) }
     private func lerp(start: CGFloat, end: CGFloat, t: CGFloat) -> CGFloat { return start + (end - start) * CGFloat(t.clamped(to: 0...1)) }
     private func lerp(start: Double, end: Double, t: Double) -> Double { return start + (end - start) * t.clamped(to: 0...1) }
     private func smoothstep(_ e0: Double, _ e1: Double, _ x: Double) -> Double { let t=((x-e0)/(e1-e0)).c(); return t*t*(3.0-2.0*t) }
     private func easeInOutCubic(_ x: Double) -> Double { let t=x.c(); return t<0.5 ? 4*t*t*t : 1-pow(-2*t+2,3)/2 }
     private func easeOutQuad(_ x: Double) -> Double { let t=x.c(); return 1-(1-t)*(1-t) }
     private func easeOutCubic(_ x: Double) -> Double { let t=x.c(); return 1-pow(1-t, 3) }
     private func easeInCubic(_ x: Double) -> Double { let t=x.c(); return t*t*t }
     private func easeOutElastic(_ x: Double) -> Double { let t=x.c(); if t==0||t==1{return t}; let c4=(2*Double.pi)/3; return pow(2,-10*t)*sin((t*10-0.75)*c4)+1 }
     private func easeInQuad(_ x: Double) -> Double { let t=x.c(); return t * t }
     // --- Decompressed and corrected generateEnhancedECG ---
    private func generateEnhancedECG(t: Double, pWaveScale: Double, tWaveScale: Double, beatVariation: Double) -> Double {
         let tClamped = max(0.0, min(1.0, t)) // Clamp t to [0, 1]
 
         // ECG Timing Parameters (relative durations within one cycle) - Refined for realism
         let pDuration: Double = 0.07 // NEW: Shorter P Wave duration
         let prInterval: Double = 0.07 // PR Interval (isoelectric)
         let qrsDuration: Double = 0.09 // NEW: Slightly shorter QRS Complex duration for sharper R peak
         let stSegment: Double = 0.10  // ST Segment (isoelectric)
         let tDuration: Double = 0.16  // T Wave duration
         // Calculated rest period
         let _ = pDuration + prInterval + qrsDuration + stSegment + tDuration
 
         // ECG Amplitude Parameters (relative heights) - Refined for realism
         let pAmp = 0.22 * pWaveScale   // NEW: Taller P wave amplitude again
         let qAmp = -0.10 * beatVariation // Q wave amplitude (small negative)
         let rAmp = 1.0 * beatVariation   // R wave amplitude (dominant positive)
         let sAmp = -0.30 * beatVariation // NEW: Deeper S wave amplitude
         let tAmp = 0.20 * tWaveScale   // T wave amplitude (moderate positive, smaller than R)
 
         // --- Waveform Generation --- 
 
         // P wave (More rounded shape)
         if tClamped < pDuration {
             let pPhase = tClamped / pDuration
             // Use a sinusoidal shape for a rounded P wave
             return pAmp * sin(pPhase * Double.pi)
         }
         // PR Interval (Baseline)
         else if tClamped < pDuration + prInterval { /* PR */ return 0.0 }
         // QRS Complex
         else if tClamped < pDuration + prInterval + qrsDuration {
             let qrsPhase = (tClamped - pDuration - prInterval) / qrsDuration // Normalized phase [0, 1) within QRS
             // Subdivide QRS for more control - Adjusted for sharper R peak
             let qDurationRel: Double = 0.15 // Q duration relative to QRS
             let rPeakTimeRel: Double = 0.45 // NEW: Earlier R peak time relative to QRS
             let sDipEndTimeRel: Double = 0.75 // NEW: Define S dip end time relative to QRS start
 
             if qrsPhase < qDurationRel { // Q wave (sharper dip)
                 let qPhase = qrsPhase / qDurationRel // Normalize phase [0, 1) for Q
                 return lerp(start: 0.0, end: qAmp, t: easeInQuad(qPhase)) // Faster dip
             }
             else if qrsPhase < rPeakTimeRel { // R wave upstroke (sharp but slightly eased)
                 // Normalize phase [0, 1) for R upstroke segment
                 let rUpPhase = (qrsPhase - qDurationRel) / (rPeakTimeRel - qDurationRel)
                 return lerp(start: qAmp, end: rAmp, t: easeOutQuad(rUpPhase)) // Fast rise, eased peak
             }
             else if qrsPhase < sDipEndTimeRel { // NEW: R wave downstroke to S dip end
                 // Normalize phase [0, 1) for R downstroke/S dip segment
                 let sDipPhase = (qrsPhase - rPeakTimeRel) / (sDipEndTimeRel - rPeakTimeRel)
                 return lerp(start: rAmp, end: sAmp, t: easeInQuad(sDipPhase)) // Sharp drop towards S
             }
             else { // NEW: Return to baseline from S
                 // Normalize phase [0, 1) for S return segment
                 let sReturnPhase = (qrsPhase - sDipEndTimeRel) / (1.0 - sDipEndTimeRel)
                 return lerp(start: sAmp, end: 0.0, t: easeOutQuad(sReturnPhase)) // Eased return
             }
         }
         // ST Segment (Baseline)
         else if tClamped < pDuration + prInterval + qrsDuration + stSegment { /* ST */ return 0.0 }
         // T wave (Asymmetrical and rounded)
         else if tClamped < pDuration + prInterval + qrsDuration + stSegment + tDuration {
             let tPhase = (tClamped - pDuration - prInterval - qrsDuration - stSegment) / tDuration
             let tPeakTime: Double = 0.4 // Peak of T wave occurs earlier
             
             if tPhase < tPeakTime { // Slower rise to T peak
                 return tAmp * easeOutCubic(tPhase / tPeakTime)
             } else { // Faster fall from T peak
                 return tAmp * (1.0 - easeInCubic((tPhase - tPeakTime) / (1.0 - tPeakTime)))
             }
         }
         // Rest Period (Baseline)
         else { /* Rest */ return 0.0 }
     }

    // 添加：更新超频时间的方法
    private func updateOverclockTime(deltaTime: TimeInterval) {
        if isOverclocked {
            // 累加超频时间
            overclockDuration += deltaTime
            
            // 检查是否达到最小超频时间
            hasMinimumOverclockTime = overclockDuration >= minimumOverclockTimeRequired
            
            if hasMinimumOverclockTime && !hasMinimumOverclockTime {
                print("已达到最小超频时间: \(overclockDuration)秒")
            }
        }
    }
    
    // 添加：获取超频奖励倍率
    func getOverclockBonusFactor() -> Double {
        return hasMinimumOverclockTime ? 1.5 : 1.0
    }
    
    // 添加：重置超频状态和计时
    func resetOverclockState() {
        isOverclocked = false
        overclockDuration = 0
        hasMinimumOverclockTime = false
        overclockStartTime = -1
    }
}

// MARK: - Helper Extensions
// Need extensions for Double/CGFloat .c() -> clamped(to: 0...1)
extension Double { func c() -> Double { self.clamped(to: 0...1) } }
extension CGFloat { func c() -> CGFloat { self.clamped(to: 0...1) } }
// Ensure Color(hex:) is accessible or defined here.

