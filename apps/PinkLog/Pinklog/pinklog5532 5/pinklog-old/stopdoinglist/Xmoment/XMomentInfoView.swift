import SwiftUI

struct XMomentInfoView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var themeManager = XMomentThemeManager()
    @State private var showingThemeSettings = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 30) {
                    
                    // 引言
                    Text(NSLocalizedString("xmoment_title", comment: "X Moment - Immersive focus, reshape time value"))
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top)

                    // Why & What
                    SectionView(title: NSLocalizedString("why_choose_xmoment", comment: "Why choose X Moment?")) {
                        Text(NSLocalizedString("xmoment_description", comment: "X Moment description"))
                    }

                    // How & Where
                    SectionView(title: NSLocalizedString("how_to_start", comment: "How to start and experience?")) {
                        Text(NSLocalizedString("how_to_use", comment: "How to use X Moment"))
                            .lineSpacing(5)
                    }

                    // What & Why - 量化与价值
                    SectionView(title: NSLocalizedString("focus_value", comment: "Focus value: X Units")) {
                        Text(NSLocalizedString("xu_description", comment: "XU description"))
                    }
                    
                    // 新增：超频奖励信息
                    SectionView(title: NSLocalizedString("overclock_reward", comment: "Overclock reward mechanism")) {
                        Text(NSLocalizedString("overclock_description", comment: "Overclock description"))
                            .lineSpacing(5)
                        
                        HStack(spacing: 12) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(NSLocalizedString("overclock_multiplier", comment: "Overclock multiplier"))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text("1.5 \(NSLocalizedString("xu_multiplier", comment: "XU multiplier"))")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                                    .fontWeight(.bold)
                            }
                            .padding(.trailing)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text(NSLocalizedString("minimum_time", comment: "Minimum time requirement"))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text("5 \(NSLocalizedString("minutes", comment: "Minutes"))")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                                    .fontWeight(.bold)
                            }
                        }
                        .padding(.top, 4)
                    }
                    
                    // 新增：自由模式说明
                    SectionView(title: NSLocalizedString("free_mode", comment: "Free mode")) {
                        Text(NSLocalizedString("free_mode_description", comment: "Free mode description"))
                            .lineSpacing(5)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text(NSLocalizedString("core_features", comment: "Core features"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(NSLocalizedString("no_interruption", comment: "No interruption mechanism"))
                                .font(.caption)
                            Text(NSLocalizedString("milestone_incentive", comment: "Milestone incentive"))
                                .font(.caption)
                            Text(NSLocalizedString("progress_oriented", comment: "Progress oriented"))
                                .font(.caption)
                        }
                        .padding(.top, 2)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text(NSLocalizedString("xu_calculation", comment: "XU calculation principle"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(NSLocalizedString("xu_under_30", comment: "XU calculation under 30 minutes"))
                                .font(.caption)
                            Text(NSLocalizedString("xu_over_30", comment: "XU calculation over 30 minutes"))
                                .font(.caption)
                            Text(NSLocalizedString("personal_best", comment: "Personal best reward"))
                                .font(.caption)
                        }
                        .padding(.top, 2)
                        
                        Text(NSLocalizedString("free_mode_bridge", comment: "Free mode as a bridge"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 4)
                    }

                    // 新增：主题设置区域
                    SectionView(title: NSLocalizedString("focus_theme", comment: "Focus interface theme")) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text(NSLocalizedString("theme_description", comment: "Theme selection description"))
                                .font(.body)
                            
                            Button(action: {
                                showingThemeSettings = true
                            }) {
                                HStack {
                                    Image(themeManager.currentTheme.previewImage)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 60, height: 40)
                                        .cornerRadius(8)
                                    
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text(themeManager.currentTheme.rawValue)
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Text(NSLocalizedString("change_theme", comment: "Click to change theme"))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(.secondary)
                                }
                                .padding(12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.secondary.opacity(0.1))
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }

                    // Who & 最终价值
                    SectionView(title: NSLocalizedString("who_for", comment: "Who is it designed for?")) {
                        Text(NSLocalizedString("target_audience", comment: "Target audience description"))
                    }
                    
                    // 核心亮点
                    SectionView(title: NSLocalizedString("core_highlights", comment: "Core highlights")) {
                        HighlightView(icon: "timer.slash", text: NSLocalizedString("no_timer", comment: "No digital timer"))
                        HighlightView(icon: "waveform.path.ecg", text: NSLocalizedString("sensory_experience", comment: "Ultimate sensory experience"))
                        HighlightView(icon: "target", text: NSLocalizedString("quantify_willpower", comment: "Quantify willpower"))
                        HighlightView(icon: "figure.mind.and.body", text: NSLocalizedString("being_philosophy", comment: "Being philosophy"))
                    }
                }
                .padding()
            }
            .navigationTitle(NSLocalizedString("about_xmoment", comment: "About X Moment"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button(NSLocalizedString("menu_done", comment: "Done")) {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingThemeSettings) {
                XMomentThemeSettingsView()
                    .environmentObject(themeManager)
            }
        }
    }

    // 辅助视图：段落
    struct SectionView<Content: View>: View {
        let title: String
        @ViewBuilder let content: Content

        var body: some View {
            VStack(alignment: .leading, spacing: 12) {
                Text(title)
                    .font(.headline)
                    .foregroundStyle(.blue)
                content
                    .font(.body)
                    .foregroundStyle(.primary)
            }
        }
    }

    // 辅助视图：高亮项
    struct HighlightView: View {
        let icon: String
        let text: String

        var body: some View {
            HStack(alignment: .top, spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundStyle(.purple)
                    .frame(width: 25, alignment: .center)
                    .padding(.top, 2)
                Text(text)
                    .font(.subheadline)
                    .foregroundStyle(.primary)
            }
            .padding(.vertical, 4)
        }
    }
}
