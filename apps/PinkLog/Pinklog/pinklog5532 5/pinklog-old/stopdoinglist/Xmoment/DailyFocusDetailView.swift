import SwiftUI
import StopDoingListKit

// 当日专注详情视图
struct DailyFocusDetailView: View {
    let date: Date
    let sessions: [XMomentSession]
    @EnvironmentObject private var dataStore: DataStore // Needed for formatting functions or other context

    // 当日统计
    private var dailyTotalDuration: TimeInterval {
        sessions.reduce(0) { $0 + $1.duration }
    }
    private var dailyInterruptionCount: Int {
        sessions.filter { $0.isInterrupted }.count
    }
    private var dailyAverageDuration: TimeInterval {
        sessions.isEmpty ? 0 : dailyTotalDuration / Double(sessions.count)
    }
    private var dailyOverclockCount: Int {
        sessions.filter { $0.overclockFactor > 1.0 }.count
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 1. 当日统计摘要
                dailyStatsSummary

                // 2. 当日会话列表
                dailySessionList
            }
            .padding()
        }
        .navigationTitle("\(date, formatter: dateFormatter)")
        .navigationBarTitleDisplayMode(.inline)
    }

    // 当日统计摘要视图
    private var dailyStatsSummary: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("daily_summary", comment: "Daily summary"))
                .font(.headline)

            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                summaryCard(title: NSLocalizedString("focus_count", comment: "Focus count"), value: "\(sessions.count)", color: .blue)
                summaryCard(title: NSLocalizedString("total_duration", comment: "Total duration"), value: formatDuration(dailyTotalDuration), color: .purple)
                summaryCard(title: NSLocalizedString("average_duration", comment: "Average duration"), value: formatDuration(dailyAverageDuration), color: .green)
                summaryCard(title: NSLocalizedString("interruption_count", comment: "Interruption count"), value: "\(dailyInterruptionCount)", color: .red)
                summaryCard(title: NSLocalizedString("completion_rate", comment: "Completion rate"), value: "\(calculateCompletionRate())%", color: .orange)
                summaryCard(title: NSLocalizedString("overclock_count", comment: "Overclock count"), value: "\(dailyOverclockCount)", color: .cyan)
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 当日会话列表视图
    private var dailySessionList: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("session_records", comment: "Session records"))
                .font(.headline)

            if sessions.isEmpty {
                Text(NSLocalizedString("no_focus_today", comment: "No focus records today"))
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity)
            } else {
                ForEach(sessions) { session in
                    sessionRow(session: session)
                        .padding(.vertical, 8)
                }
                .padding(.horizontal)
                .background(Color(.secondarySystemBackground))
                .cornerRadius(12)
            }
        }
    }

    // 单个会话行视图
    private func sessionRow(session: XMomentSession) -> some View {
        NavigationLink(destination: XMomentSessionDetailView(session: session)) {
            HStack(spacing: 15) {
                // 时间和状态图标
                VStack(alignment: .leading) {
                    Text(session.startTime, style: .time)
                        .font(.subheadline).bold()
                    if session.isInterrupted {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                            .font(.caption)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                    }
                }
                .frame(width: 60, alignment: .leading)

                // 详细信息
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("\(NSLocalizedString("duration_prefix", comment: "Duration")): \(formatDuration(session.duration))")
                        if session.overclockFactor > 1.0 {
                            Text("x\(String(format: "%.1f", session.overclockFactor))")
                                .font(.caption)
                                .padding(.horizontal, 4)
                                .background(Color.orange.opacity(0.2))
                                .foregroundColor(.orange)
                                .cornerRadius(4)
                        }
                    }
                    .font(.body)

                    if let category = session.category, !category.isEmpty {
                        Text("\(NSLocalizedString("category_prefix", comment: "Category")): \(NSLocalizedString(category, comment: "Category name"))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    if let mood = session.mood, !mood.isEmpty {
                        Text("\(NSLocalizedString("mood_prefix", comment: "Mood")): \(NSLocalizedString(mood, comment: "Mood name"))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    if let notes = session.notes, !notes.isEmpty {
                        Text("\(NSLocalizedString("notes_prefix", comment: "Notes")): \(notes)")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .lineLimit(1)
                    }
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle()) // 使用PlainButtonStyle避免整行高亮
    }

    // 摘要卡片辅助视图
    private func summaryCard(title: String, value: String, color: Color) -> some View {
        VStack {
            Text(value)
                .font(.title3).bold()
                .foregroundColor(color)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }

    // MARK: - 辅助计算和格式化

    private func calculateCompletionRate() -> Int {
        guard !sessions.isEmpty else { return 0 }
        let completed = sessions.filter { !$0.isInterrupted }.count
        return Int((Double(completed) / Double(sessions.count)) * 100)
    }

    // 格式化时长 (复用或从公共地方获取)
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        // 只显示小时和分钟
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated // e.g., "1h 15m", "25m"
        // 移除 zeroFormattingBehavior，让其自动处理
        // formatter.zeroFormattingBehavior = .pad
        // 如果格式化失败或时长为0，显示 0m
        return formatter.string(from: duration) ?? "0m"
    }

    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
}

// MARK: - Preview

struct DailyFocusDetailView_Previews: PreviewProvider {
    static let sampleDate = Calendar.current.date(byAdding: .day, value: -1, to: Date())!
    static let sampleSessions: [XMomentSession] = [
        XMomentSession(id: UUID(), startTime: Calendar.current.date(bySettingHour: 9, minute: 0, second: 0, of: sampleDate)!, endTime: Calendar.current.date(bySettingHour: 9, minute: 25, second: 0, of: sampleDate)!, duration: 25 * 60, mood: NSLocalizedString("focused", comment: "Focused mood"), category: NSLocalizedString("work", comment: "Work category"), notes: NSLocalizedString("write_report", comment: "Write report"), overclockFactor: 1.0, isInterrupted: false),
        XMomentSession(id: UUID(), startTime: Calendar.current.date(bySettingHour: 10, minute: 30, second: 0, of: sampleDate)!, endTime: Calendar.current.date(bySettingHour: 10, minute: 45, second: 0, of: sampleDate)!, duration: 15 * 60, mood: NSLocalizedString("tired", comment: "Tired mood"), category: NSLocalizedString("study", comment: "Study category"), notes: NSLocalizedString("read_docs", comment: "Read documentation"), overclockFactor: 1.0, isInterrupted: true),
        XMomentSession(id: UUID(), startTime: Calendar.current.date(bySettingHour: 14, minute: 0, second: 0, of: sampleDate)!, endTime: Calendar.current.date(bySettingHour: 14, minute: 45, second: 0, of: sampleDate)!, duration: 45 * 60, mood: NSLocalizedString("efficient", comment: "Efficient mood"), category: NSLocalizedString("work", comment: "Work category"), notes: NSLocalizedString("code_review", comment: "Code review"), overclockFactor: 1.5, isInterrupted: false),
    ]

    static var previews: some View {
        NavigationStack {
            DailyFocusDetailView(date: sampleDate, sessions: sampleSessions)
                .environmentObject(DataStore.shared) // Provide DataStore for preview
        }
    }
} 