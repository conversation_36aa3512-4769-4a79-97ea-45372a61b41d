import SwiftUI
import StopDoingListKit
// X Moment 会话数据模型
struct XMomentSession: Identifiable, Codable, Equatable {
    var id: UUID
    var startTime: Date
    var endTime: Date
    var duration: TimeInterval
    var mood: String?
    var category: String?
    var notes: String?
    var overclockFactor: Double = 1.0 // 新增：超频奖励倍率
    var overclockDuration: TimeInterval = 0 // 新增：超频持续时间
    var isInterrupted: Bool = false // 新增：是否被中断
    var isFreeMode: Bool = false // 新增：是否为自由模式
    var initialDuration: TimeInterval = 0 // 新增：初始设定时长
    var isPersonalBest: Bool = false // 新增：是否为个人最佳记录
    var progressXU: Double = 0.0 // 新增：进步奖励XU

    // 计算 X Units - 根据模式不同采用不同的计算方式
    var xUnits: Double {
        if isFreeMode {
            return calculateFreeModeXU()
        } else {
            // 标准模式：每15分钟1XU，应用超频倍率
            let baseXU = round(duration / (15 * 60))
            return baseXU * overclockFactor
        }
    }

    // 自由模式的XU计算
    private func calculateFreeModeXU() -> Double {
        var totalXU = 0.0

        if duration < 30 * 60 {
            // 30分钟以下：每10分钟0.2XU + 小额里程碑奖励
            let baseXU = floor(duration / (10 * 60)) * 0.2

            // 小里程碑奖励
            var bonusXU = 0.0

            if duration >= 5 * 60 { bonusXU += 0.1 }
            if duration >= 10 * 60 { bonusXU += 0.1 }
            if duration >= 15 * 60 { bonusXU += 0.2 }
            if duration >= 20 * 60 { bonusXU += 0.2 }

            totalXU = baseXU + bonusXU
        } else {
            // 30分钟及以上：与标准模式相同，每15分钟1XU
            totalXU = floor(duration / (15 * 60))

            // 30分钟达成奖励：确保至少2XU（与标准模式30分钟相同）
            if totalXU < 2 && duration >= 30 * 60 {
                totalXU = 2
            }
        }

        // 添加进步奖励
        totalXU += progressXU

        return totalXU
    }

    // 静态方法创建会话
    static func create(
        duration: TimeInterval,
        category: String? = nil,
        mood: String? = nil,
        notes: String? = nil,
        overclockFactor: Double = 1.0,
        overclockDuration: TimeInterval = 0,
        isInterrupted: Bool = false,
        isFreeMode: Bool = false,
        initialDuration: TimeInterval = 0,
        isPersonalBest: Bool = false,
        progressXU: Double = 0.0
    ) -> XMomentSession {
        let endTime = Date()
        let startTime = endTime.addingTimeInterval(-duration)

        return XMomentSession(
            id: UUID(),
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            mood: mood,
            category: category,
            notes: notes,
            overclockFactor: overclockFactor,
            overclockDuration: overclockDuration,
            isInterrupted: isInterrupted,
            isFreeMode: isFreeMode,
            initialDuration: initialDuration,
            isPersonalBest: isPersonalBest,
            progressXU: progressXU
        )
    }

    // Equatable 实现
    static func == (lhs: XMomentSession, rhs: XMomentSession) -> Bool {
        return lhs.id == rhs.id
    }
}

// X Moment 用户统计数据模型
struct XMomentUserStats: Codable {
    // 用户首选项设置
    var lastUsedDuration: TimeInterval = 25 * 60 // 默认25分钟

    // 统计数据
    var totalInterruptedCount: Int = 0 // 总中断次数
    var totalCompletedCount: Int = 0 // 总完成次数
    var bestStreak: Int = 0 // 最佳连续专注天数
    var currentStreak: Int = 0 // 当前连续专注天数
    var firstSessionDate: Date? = nil // 首次专注日期
    var lastSessionDate: Date? = nil // 最后一次专注日期

    // 里程碑相关
    var achievedMilestoneIDs: Set<String> = [] // 已达成的里程碑ID

    // 自由模式相关统计
    var freeModeBestDuration: TimeInterval = 0 // 自由模式最佳时长
    var freeModeLastDuration: TimeInterval = 0 // 上次自由模式时长
    var freeModeSessionCount: Int = 0 // 自由模式总次数
    var freeModeReached30MinCount: Int = 0 // 达到30分钟门槛的次数
    var freeModeConsecutiveImprovement: Int = 0 // 连续进步次数
    var freeModeWeeklyDurations: [String: TimeInterval] = [:] // 每日最长时间记录，使用"YYYY-MM-DD"格式的字符串作为键
    var freeModeAchievedMilestoneIDs: Set<String> = [] // 已达成的自由模式里程碑ID

    // 其他可能的统计/设置
    var sharingCount: Int = 0 // 分享次数
    var customCategories: [String] = [] // 用户自定义的专注类别

    // 初始化方法
    init() {}

    // 从UserDefaults迁移数据的初始化方法
    init(migrateFromUserDefaults: Bool) {
        if migrateFromUserDefaults {
            // 迁移设置
            if let savedDuration = UserDefaults.standard.object(forKey: "XMomentLastDuration") as? TimeInterval {
                self.lastUsedDuration = savedDuration
            }

            // 迁移分享计数
            self.sharingCount = UserDefaults.standard.integer(forKey: "sharingCount")

            // 迁移里程碑数据
            if let data = UserDefaults.standard.data(forKey: "achievedMilestoneIDs_v1"),
               let decoded = try? JSONDecoder().decode(Set<String>.self, from: data) {
                self.achievedMilestoneIDs = decoded
            }
        }
    }
}

// DataStore 扩展 - 添加 X Moment 相关方法
extension DataStore {
    // MARK: - X Moment 文件路径

    // X Moment 会话存储路径
    internal var xMomentSessionsURL: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent("xmoment_sessions.json")
    }

    // X Moment 用户统计数据存储路径
    internal var xMomentUserStatsURL: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent("xmoment_user_stats.json")
    }

    // MARK: - X Moment 持久化方法

    // 保存 X Moment 会话
    func saveXMomentSession(_ session: XMomentSession) {
        // 读取已有会话
        var sessions = getXMomentSessionsUnambiguous()

        // 添加新会话
        sessions.append(session)

        // 保存更新后的会话列表到 JSON 文件
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(sessions)
            try data.write(to: xMomentSessionsURL)

            // 更新用户统计数据
            updateUserStatsAfterNewSession(session)

            objectWillChange.send() // 通知视图更新
        } catch {
            print(NSLocalizedString("save_xmoment_session_failed", comment: "Failed to save XMoment session data: \(error)"))
        }
    }

    // 获取所有 X Moment 会话
    func getXMomentSessions() -> [XMomentSession] {
        do {
            if FileManager.default.fileExists(atPath: xMomentSessionsURL.path) {
                let data = try Data(contentsOf: xMomentSessionsURL)
                return try JSONDecoder().decode([XMomentSession].self, from: data)
            }
        } catch {
            print(NSLocalizedString("load_xmoment_session_failed", comment: "Failed to load XMoment session data: \(error)"))

            // 错误恢复：尝试从 UserDefaults 迁移旧数据（兼容性）
            if let data = UserDefaults.standard.data(forKey: "XMomentSessions"),
               let sessions = try? JSONDecoder().decode([XMomentSession].self, from: data) {
                print(NSLocalizedString("recovered_sessions", comment: "Recovered sessions from UserDefaults"))

                // 异步保存到新的 JSON 文件
                DispatchQueue.global(qos: .background).async { [weak self] in
                    guard let self = self else { return }
                    do {
                        let encoder = JSONEncoder()
                        encoder.outputFormatting = .prettyPrinted
                        let data = try encoder.encode(sessions)
                        try data.write(to: self.xMomentSessionsURL)
                        print(NSLocalizedString("migrated_to_json", comment: "Migrated session data from UserDefaults to JSON file"))

                        // 迁移完成后清除 UserDefaults 中的旧数据
                        DispatchQueue.main.async {
                            UserDefaults.standard.removeObject(forKey: "XMomentSessions")
                        }
                    } catch {
                        print(NSLocalizedString("migration_failed", comment: "Failed to migrate XMoment session data: \(error)"))
                    }
                }

                return sessions
            }
        }
        return []
    }

    // 删除 X Moment 会话
    func deleteXMomentSession(id: UUID) {
        var sessions = getXMomentSessionsUnambiguous()
        sessions.removeAll { $0.id == id }

        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(sessions)
            try data.write(to: xMomentSessionsURL)
            objectWillChange.send()
        } catch {
            print(NSLocalizedString("delete_xmoment_session_failed", comment: "Failed to delete XMoment session data: \(error)"))
        }
    }

    // 获取用户统计数据
    func getXMomentUserStats() -> XMomentUserStats {
        do {
            if FileManager.default.fileExists(atPath: xMomentUserStatsURL.path) {
                let data = try Data(contentsOf: xMomentUserStatsURL)
                let decodedStats = try JSONDecoder().decode(XMomentUserStats.self, from: data)
                return decodedStats
            }
        } catch {
            print(" ERROR loading XMomentUserStats: \(error). File might be corrupted or incompatible.")
        }

        // 如果没有找到文件或解码失败，尝试从 UserDefaults 迁移数据
        print(" INFO: xmoment_user_stats.json not found or failed to load. Creating new/migrating.")
        let stats = XMomentUserStats(migrateFromUserDefaults: true)
        saveXMomentUserStats(stats) // This save might overwrite if loading failed
        print(" INFO: Returning new/migrated XMomentUserStats. freeModeSessionCount: \(stats.freeModeSessionCount)")
        return stats
    }

    // 保存用户统计数据
    func saveXMomentUserStats(_ stats: XMomentUserStats) {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(stats)
            try data.write(to: xMomentUserStatsURL)
            objectWillChange.send()
        } catch {
            print(NSLocalizedString("save_xmoment_stats_failed", comment: "Failed to save XMoment user stats data: \(error)"))
        }
    }

    // 更新用户设置 - 最后使用的计时时长
    func updateLastUsedXMomentDuration(_ duration: TimeInterval) {
        var stats = getXMomentUserStatsUnambiguous()
        stats.lastUsedDuration = duration
        saveXMomentUserStats(stats)
    }

    // 更新里程碑达成状态
    func updateAchievedMilestones(_ milestoneIDs: Set<String>) {
        var stats = getXMomentUserStatsUnambiguous()
        stats.achievedMilestoneIDs = milestoneIDs
        saveXMomentUserStats(stats)
    }

    private func updateUserStatsAfterNewSession(_ session: XMomentSession) {
        var stats = getXMomentUserStatsUnambiguous()

        // 更新首次/最后会话日期
        if stats.firstSessionDate == nil || (stats.firstSessionDate! > session.startTime) {
            stats.firstSessionDate = session.startTime
        }
        stats.lastSessionDate = session.endTime

        // 更新中断/完成次数
        if session.isInterrupted {
            stats.totalInterruptedCount += 1
        } else {
            stats.totalCompletedCount += 1
        }

        // 更新专注天数统计
        updateStreakStats(&stats)

        // 如果是自由模式会话，更新自由模式统计
        if session.isFreeMode {
            updateFreeModeStats(after: session, stats: &stats)
        }

        saveXMomentUserStats(stats)
    }

    // 更新连续专注天数
    private func updateStreakStats(_ stats: inout XMomentUserStats) {
        let sessions = getXMomentSessionsUnambiguous()
        let calendar = Calendar.current

        // 将日期映射到天数
        let dateStrings = sessions.map {
            let components = calendar.dateComponents([.year, .month, .day], from: $0.startTime)
            return "\(components.year!)-\(components.month!)-\(components.day!)"
        }

        // 去重并排序
        let uniqueDates = Array(Set(dateStrings)).sorted(by: >)

        // 如果没有记录，重置统计
        if uniqueDates.isEmpty {
            stats.currentStreak = 0
            return
        }

        // 检查今天是否有记录
        let todayString = {
            let components = calendar.dateComponents([.year, .month, .day], from: Date())
            return "\(components.year!)-\(components.month!)-\(components.day!)"
        }()

        // 如果今天没有记录，则连续天数为0
        if uniqueDates[0] != todayString {
            stats.currentStreak = 0
            return
        }

        // 计算连续天数
        var streak = 1
        let currentDate = Date()

        while streak < uniqueDates.count {
            // 检查前一天
            let previousDate = calendar.date(byAdding: .day, value: -streak, to: currentDate)!
            let previousDateString = {
                let components = calendar.dateComponents([.year, .month, .day], from: previousDate)
                return "\(components.year!)-\(components.month!)-\(components.day!)"
            }()

            // 如果前一天有记录，则连续天数+1
            if uniqueDates.contains(previousDateString) {
                streak += 1
            } else {
                break
            }
        }

        stats.currentStreak = streak
        if streak > stats.bestStreak {
            stats.bestStreak = streak
        }
    }

    // MARK: - 其他 X Moment 相关的辅助方法

    // 获取今日 X Units 总数
    func getTodayXUnits() -> Double {
        let sessions = getXMomentSessionsUnambiguous()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        return sessions
            .filter { calendar.isDate($0.startTime, inSameDayAs: today) }
            .reduce(0) { $0 + $1.xUnits }
    }

    // 获取本周 X Units 总数
    func getWeekXUnits() -> Double {
        let sessions = getXMomentSessionsUnambiguous()
        let calendar = Calendar.current
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: Date())!

        return sessions
            .filter { $0.startTime >= weekAgo }
            .reduce(0) { $0 + $1.xUnits }
    }

    // 获取总 X Units
    func getTotalXUnits() -> Double {
        return getXMomentSessionsUnambiguous().reduce(0) { $0 + $1.xUnits }
    }

    // 获取按分类统计的 X Units
    func getXUnitsByCategory() -> [(category: String, xUnits: Double)] {
        let sessions = getXMomentSessionsUnambiguous()
        var result: [String: Double] = [:]

        // 按分类统计
        for session in sessions {
            let category = session.category ?? NSLocalizedString("uncategorized", comment: "Uncategorized category")
            let currentValue = result[category] ?? 0
            result[category] = currentValue + session.xUnits
        }

        // 转换为数组并排序
        return result.map { (category: $0.key, xUnits: $0.value) }
            .sorted { $0.xUnits > $1.xUnits }
    }

    // 获取连续专注天数
    func getXMomentStreakDays() -> Int {
        return getXMomentUserStatsUnambiguous().currentStreak
    }

    // 获取总 X Moment 完成次数
    func getTotalXMomentCount() -> Int {
        return getXMomentSessionsUnambiguous().count
    }

    // 获取总 X Moment 专注时长（秒）
    func getTotalXMomentDuration() -> TimeInterval {
        return getXMomentSessionsUnambiguous().reduce(0) { $0 + $1.duration }
    }

    // 获取本周 X Moment 专注时长（秒）
    func getWeekXMomentDuration() -> TimeInterval {
        let sessions = getXMomentSessionsUnambiguous()
        let calendar = Calendar.current
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: Date())!

        return sessions
            .filter { $0.startTime >= weekAgo }
            .reduce(0) { $0 + $1.duration }
    }

    // 获取所有中断的 X Moment 会话
    func getPersonalBestDuration(excludingCurrent currentDuration: TimeInterval? = nil) -> TimeInterval {
        let sessions = getXMomentSessionsUnambiguous()
        var maxDuration: TimeInterval = 0

        for session in sessions {
            // 如果需要排除当前会话，并且当前会话时长与此会话时长非常接近（允许微小误差），则跳过
            if let durationToExclude = currentDuration, abs(session.duration - durationToExclude) < 1.0 {
                continue
            }
            if session.duration > maxDuration {
                maxDuration = session.duration
            }
        }
        return maxDuration
    }

    // 获取自由模式的个人最佳时长
    func getFreeModePersonalBestDuration(excludingCurrent currentDuration: TimeInterval? = nil) -> TimeInterval {
        let sessions = getXMomentSessionsUnambiguous().filter { $0.isFreeMode }
        var maxDuration: TimeInterval = 0

        for session in sessions {
            // 如果需要排除当前会话，并且当前会话时长与此会话时长非常接近（允许微小误差），则跳过
            if let durationToExclude = currentDuration, abs(session.duration - durationToExclude) < 1.0 {
                continue
            }
            if session.duration > maxDuration {
                maxDuration = session.duration
            }
        }
        return maxDuration
    }

    // 更新自由模式统计
    func updateFreeModeStats(after session: XMomentSession, stats: inout XMomentUserStats) {
        // 更新自由模式会话计数
        stats.freeModeSessionCount += 1

        // 更新上次自由模式时长
        stats.freeModeLastDuration = session.duration

        // 检查是否达到30分钟门槛
        if session.duration >= 30 * 60 {
            stats.freeModeReached30MinCount += 1
        }

        // 检查是否是新的最佳记录
        if session.duration > stats.freeModeBestDuration {
            stats.freeModeBestDuration = session.duration

            // 更新连续进步次数
            stats.freeModeConsecutiveImprovement += 1
        } else {
            // 重置连续进步次数
            stats.freeModeConsecutiveImprovement = 0
        }

        // 更新每日最长时间记录
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateKey = dateFormatter.string(from: session.startTime)

        let currentBest = stats.freeModeWeeklyDurations[dateKey] ?? 0
        if session.duration > currentBest {
            stats.freeModeWeeklyDurations[dateKey] = session.duration
        }

        // 检查并更新自由模式里程碑
        updateFreeModeMilestones(stats: &stats)
    }

    // 检查并更新自由模式里程碑
    func updateFreeModeMilestones(stats: inout XMomentUserStats) {
        var newlyAchievedMilestones: [FreeModeMilestone] = []

        // 检查每个里程碑
        for milestone in FreeModeMilestone.allCases {
            let milestoneID = "free_mode_" + milestone.id.description

            // 如果这个里程碑已经达成，跳过
            if stats.freeModeAchievedMilestoneIDs.contains(milestoneID) {
                continue
            }

            // 检查是否达成这个里程碑
            if isMilestoneAchieved(milestone, stats: stats) {
                stats.freeModeAchievedMilestoneIDs.insert(milestoneID)
                newlyAchievedMilestones.append(milestone)
            }
        }

        // 保存更新后的统计数据
        if !newlyAchievedMilestones.isEmpty {
            saveXMomentUserStats(stats)
        }
    }

    // 检查自由模式里程碑是否达成
    private func isMilestoneAchieved(_ milestone: FreeModeMilestone, stats: XMomentUserStats) -> Bool {
        switch milestone {
        case .firstSession:
            return stats.freeModeSessionCount >= 1
        case .reach5Minutes:
            return stats.freeModeBestDuration >= 5 * 60
        case .reach15Minutes:
            return stats.freeModeBestDuration >= 15 * 60
        case .reach30Minutes:
            return stats.freeModeBestDuration >= 30 * 60
        case .reach60Minutes:
            return stats.freeModeBestDuration >= 60 * 60
        case .threeConsecutiveImprovements:
            return stats.freeModeConsecutiveImprovement >= 3
        case .fiveConsecutiveImprovements:
            return stats.freeModeConsecutiveImprovement >= 5
        case .reach30MinutesFiveTimes:
            return stats.freeModeReached30MinCount >= 5
        case .reach30MinutesTenTimes:
            return stats.freeModeReached30MinCount >= 10
        }
    }
}

// 自由模式里程碑
enum FreeModeMilestone: Int, CaseIterable, Identifiable {
    case firstSession = 0 // 首次尝试
    case reach5Minutes = 1 // 达到5分钟
    case reach15Minutes = 2 // 达到15分钟
    case reach30Minutes = 3 // 达到30分钟门槛
    case reach60Minutes = 4 // 达到60分钟
    case threeConsecutiveImprovements = 5 // 连续3次进步
    case fiveConsecutiveImprovements = 6 // 连续5次进步
    case reach30MinutesFiveTimes = 7 // 5次达到30分钟
    case reach30MinutesTenTimes = 8 // 10次达到30分钟

    var id: Int { self.rawValue }

    var name: String {
        switch self {
        case .firstSession: return NSLocalizedString("first_experience", comment: "First experience")
        case .reach5Minutes: return NSLocalizedString("focus_start", comment: "Focus start")
        case .reach15Minutes: return NSLocalizedString("focus_intermediate", comment: "Focus intermediate")
        case .reach30Minutes: return NSLocalizedString("focus_threshold", comment: "Focus threshold")
        case .reach60Minutes: return NSLocalizedString("deep_focus", comment: "Deep focus")
        case .threeConsecutiveImprovements: return NSLocalizedString("continuous_improvement", comment: "Continuous improvement")
        case .fiveConsecutiveImprovements: return NSLocalizedString("steady_progress", comment: "Steady progress")
        case .reach30MinutesFiveTimes: return NSLocalizedString("focus_habit_formation", comment: "Focus habit formation")
        case .reach30MinutesTenTimes: return NSLocalizedString("focus_master", comment: "Focus master")
        }
    }

    var description: String {
        switch self {
        case .firstSession: return NSLocalizedString("complete_first_free_mode", comment: "Complete first free mode session")
        case .reach5Minutes: return NSLocalizedString("reach_5_minutes", comment: "Reach 5 minutes in free mode")
        case .reach15Minutes: return NSLocalizedString("reach_15_minutes", comment: "Reach 15 minutes in free mode")
        case .reach30Minutes: return NSLocalizedString("reach_30_minutes", comment: "Reach 30 minutes threshold in free mode")
        case .reach60Minutes: return NSLocalizedString("reach_60_minutes", comment: "Reach 60 minutes in free mode")
        case .threeConsecutiveImprovements: return NSLocalizedString("improve_3_times", comment: "Improve focus duration 3 times consecutively")
        case .fiveConsecutiveImprovements: return NSLocalizedString("improve_5_times", comment: "Improve focus duration 5 times consecutively")
        case .reach30MinutesFiveTimes: return NSLocalizedString("reach_30_minutes_5_times", comment: "Reach 30 minutes 5 times in free mode")
        case .reach30MinutesTenTimes: return NSLocalizedString("reach_30_minutes_10_times", comment: "Reach 30 minutes 10 times in free mode")
        }
    }

    var iconName: String {
        switch self {
        case .firstSession: return "1.circle.fill"
        case .reach5Minutes: return "5.circle.fill"
        case .reach15Minutes: return "15.circle.fill"
        case .reach30Minutes: return "30.circle.fill"
        case .reach60Minutes: return "60.circle.fill"
        case .threeConsecutiveImprovements: return "arrow.up.forward.circle.fill"
        case .fiveConsecutiveImprovements: return "arrow.up.forward.app.fill"
        case .reach30MinutesFiveTimes: return "hand.thumbsup.fill"
        case .reach30MinutesTenTimes: return "crown.fill"
        }
    }

    var rewardXU: Double {
        switch self {
        case .firstSession: return 0.2
        case .reach5Minutes: return 0.3
        case .reach15Minutes: return 0.5
        case .reach30Minutes: return 1.0
        case .reach60Minutes: return 1.5
        case .threeConsecutiveImprovements: return 0.5
        case .fiveConsecutiveImprovements: return 1.0
        case .reach30MinutesFiveTimes: return 1.5
        case .reach30MinutesTenTimes: return 3.0
        }
    }
}

// 新增：嵌入式专注时间选择器
struct EmbeddedTimeSelector: View {
    @Binding var selectedDuration: TimeInterval
    var onSelect: (Int) -> Void

    // Define the time options. '1' will represent the 'Free/Custom' icon button.
    let timeOptions = [1, 15, 30, 55]
    @State private var showOptions = false

    // Define flat colors for buttons (Example Palette)
    private let buttonColors: [Int: Color] = [
        1: Color.gray.opacity(0.8), // Free/Custom button color
        15: Color.blue.opacity(0.8),
        30: Color.green.opacity(0.8),
        55: Color.orange.opacity(0.8)
    ]
    private let foregroundColor: Color = .white // Color for numbers and icon

    // Method to create each button view
    private func createTimeOption(for minutes: Int, index: Int) -> some View {
        let delay = 0.1 * Double(index)
        let buttonColor = buttonColors[minutes] ?? Color.gray // Fallback color

        return EmbeddedTimeOptionView(
            minutes: minutes,
            buttonColor: buttonColor,
            foregroundColor: foregroundColor, // Pass foreground color
            onTap: {
                // Haptic feedback
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                withAnimation(.easeOut(duration: 0.3)) {
                    selectedDuration = TimeInterval(minutes * 60)
                    onSelect(minutes)
                }
            }
        )
        .opacity(showOptions ? 1 : 0)
        .offset(y: showOptions ? 0 : 30)
        .animation(.spring(response: 0.4, dampingFraction: 0.6).delay(delay), value: showOptions)
    }

    var body: some View {
        VStack {
            Spacer() // Push options to the bottom

            // Row of time option buttons
            timeOptionsRow
                .padding(.bottom, 50) // Adjust bottom padding as needed
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(backgroundGradient.ignoresSafeArea()) // Keep the subtle gradient overlay
        .onAppear {
            withAnimation(.easeOut(duration: 0.3)) {
                showOptions = true
            }
        }
    }

    // Extracted property for the row of buttons
    private var timeOptionsRow: some View {
        HStack(spacing: 20) { // Adjust spacing as needed
            ForEach(Array(timeOptions.enumerated()), id: \.element) { index, minutes in
                createTimeOption(for: minutes, index: index)
            }
        }
    }

    // Extracted property for the background gradient overlay
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [.clear, .black.opacity(showOptions ? 0.2 : 0)]),
            startPoint: .center,
            endPoint: .bottom
        )
    }
}

// New: Embedded time option button view (Flat Design)
struct EmbeddedTimeOptionView: View {
    let minutes: Int
    let buttonColor: Color
    let foregroundColor: Color // Use passed foreground color
    var onTap: (() -> Void)? = nil

    @State private var isPressed = false // For subtle press effect

    var body: some View {
        VStack(spacing: 0) { // Reduced spacing
            if minutes == 1 {
                // Free/Custom button: Simple circle icon
                Image(systemName: "circle") // Use circle icon
                    .font(.system(size: 24, weight: .semibold)) // Adjust size/weight
                    .foregroundColor(foregroundColor)
            } else {
                // Standard time buttons: Just the number
                Text("\(minutes)")
                    .font(.system(size: 20, weight: .bold, design: .rounded)) // Clear font
                    .foregroundColor(foregroundColor)
            }
        }
        .frame(width: 60, height: 60) // Keep frame size consistent
        .background(
            Circle()
                .fill(buttonColor) // Use solid flat color
                // Removed material overlay and shadow
        )
        // Subtle press effect (optional, can be removed for perfect flatness)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in isPressed = true }
                .onEnded { _ in
                    // Execute tap action on ended
                    onTap?()
                    // Animate back immediately
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.6)) {
                         isPressed = false
                    }
                }
        )
        // Removed brightness effect
         // Removed complex animation logic for isPressed
    }
}

// 新增：低干扰庆祝视图
struct MilestoneCelebrationView: View {
    let milestone: Milestone?
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0

    var body: some View {
        VStack(spacing: 15) {
            if let ms = milestone {
                Image(systemName: ms.iconName ?? "star.fill") // 使用里程碑图标或默认星星
                    .font(.system(size: 50))
                    .foregroundColor(.yellow) // 庆祝颜色
                    .shadow(color: .yellow.opacity(0.5), radius: 10)
                    .scaleEffect(scale)
                    .opacity(opacity)

                Text(ms.name)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 2)
                    .scaleEffect(scale * 0.9)
                    .opacity(opacity)

                 Text(NSLocalizedString("milestone_achieved", comment: "Milestone achieved"))
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .shadow(color: .black.opacity(0.3), radius: 1)
                    .scaleEffect(scale * 0.8)
                    .opacity(opacity)
            } else {
                // 理论上不应显示，但加个占位
                EmptyView()
            }
        }
        .padding(30)
        .background(
            Circle()
                .fill(Color.black.opacity(0.4))
                .background(
                    Rectangle()
                        .fill(Material.ultraThinMaterial)
                )
                .clipShape(Circle())
                .scaleEffect(scale * 1.2)
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity) // 覆盖整个区域
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.5)) {
                scale = 1.0
                opacity = 1.0
            }
        }
        .allowsHitTesting(false) // 不阻挡下方交互
    }
}