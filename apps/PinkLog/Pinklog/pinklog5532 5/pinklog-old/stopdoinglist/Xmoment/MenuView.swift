import SwiftUI
import StopDoingListKit

struct MenuView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject private var dataStore: DataStore
    
    // 添加主题设置状态
    @State private var showingThemeSettings = false
    
    var body: some View {
        NavigationStack {
            List {
                Section {
                    Button {
                        // 显示主题设置
                        showingThemeSettings = true
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: "gamecontroller")
                                .frame(width: 30, alignment: .leading)
                                .foregroundColor(.blue)
                            Text(NSLocalizedString("focus_record_theme", comment: "Focus record theme"))
                        }
                    }
                } header: {
                    Text(NSLocalizedString("xmoment_section", comment: "X Moment section"))
                }
                
                Section {
                    Button {
                        // 显示统计视图
                        NotificationCenter.default.post(name: NSNotification.Name("ShowStatistics"), object: nil)
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: "chart.bar")
                                .frame(width: 30, alignment: .leading)
                                .foregroundColor(.orange)
                            Text(NSLocalizedString("menu_statistics", comment: "Statistics"))
                        }
                    }
                    
                    Button {
                        // 显示设置视图
                        NotificationCenter.default.post(name: NSNotification.Name("ShowSettings"), object: nil)
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: "gear")
                                .frame(width: 30, alignment: .leading)
                                .foregroundColor(.gray)
                            Text(NSLocalizedString("menu_settings", comment: "Settings"))
                        }
                    }
                    
                    Button {
                        // 显示关于视图
                        NotificationCenter.default.post(name: NSNotification.Name("ShowAbout"), object: nil)
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: "info.circle")
                                .frame(width: 30, alignment: .leading)
                                .foregroundColor(.blue)
                            Text(NSLocalizedString("menu_about", comment: "About"))
                        }
                    }
                } header: {
                    Text(NSLocalizedString("menu_app", comment: "App"))
                }
            }
            .navigationTitle(NSLocalizedString("menu_title", comment: "Menu"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(NSLocalizedString("menu_done", comment: "Done")) {
                        dismiss()
                    }
                }
            }
        }
        // 添加主题设置Sheet
        .sheet(isPresented: $showingThemeSettings) {
            XMomentThemeSettingsView()
        }
    }
} 