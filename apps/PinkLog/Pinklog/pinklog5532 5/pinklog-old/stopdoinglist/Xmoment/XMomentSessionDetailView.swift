import SwiftUI
import StopDoingListKit

// 单个专注会话详情视图
struct XMomentSessionDetailView: View {
    let session: XMomentSession
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataStore: DataStore
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 会话状态和基本信息
                sessionStatusHeader
                
                // 详细信息卡片
                sessionDetailsCard
                
                // 时间信息卡片
                timeDetailsCard
                
                // 笔记卡片（如果有）
                if let notes = session.notes, !notes.isEmpty {
                    notesCard(notes)
                }
                
                // 超频信息卡片（如果有）
                if session.overclockFactor > 1.0 {
                    overclockDetailsCard
                }
                
                // 其他会话统计信息
                sessionStatsCard
            }
            .padding()
        }
        .navigationTitle(NSLocalizedString("session_details", comment: "Session details"))
        .navigationBarTitleDisplayMode(.inline)
    }
    
    // 会话状态头部
    private var sessionStatusHeader: some View {
        HStack(spacing: 20) {
            // 状态图标
            ZStack {
                Circle()
                    .fill(session.isInterrupted ? Color.red.opacity(0.1) : Color.green.opacity(0.1))
                    .frame(width: 80, height: 80)
                
                Image(systemName: session.isInterrupted ? "xmark.circle.fill" : "checkmark.circle.fill")
                    .font(.system(size: 32))
                    .foregroundColor(session.isInterrupted ? .red : .green)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(session.isInterrupted ? NSLocalizedString("interrupted", comment: "Session interrupted") : NSLocalizedString("completed", comment: "Session completed"))
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(session.isInterrupted ? .red : .green)
                
                Text("\(NSLocalizedString("started_at_prefix", comment: "Started at prefix")) \(formattedTime(session.startTime))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("\(NSLocalizedString("duration_prefix", comment: "Duration prefix")) \(formatDuration(session.duration))")
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 会话详细信息卡片
    private var sessionDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(NSLocalizedString("session_info", comment: "Session information"))
                .font(.headline)
                .foregroundColor(.primary)
            
            detailRow(
                title: NSLocalizedString("session_type", comment: "Session type"),
                value: session.isFreeMode ? NSLocalizedString("free_mode", comment: "Free mode") : NSLocalizedString("standard_mode", comment: "Standard mode"),
                icon: "timer",
                color: .blue
            )
            
            if let category = session.category, !category.isEmpty {
                detailRow(
                    title: NSLocalizedString("category", comment: "Category"),
                    value: NSLocalizedString(category, comment: "Category name"),
                    icon: "tag.fill",
                    color: .purple
                )
            }
            
            if let mood = session.mood, !mood.isEmpty {
                detailRow(
                    title: NSLocalizedString("mood", comment: "Mood"),
                    value: NSLocalizedString(mood, comment: "Mood name"),
                    icon: "face.smiling.fill",
                    color: .orange
                )
            }
            
            detailRow(
                title: NSLocalizedString("session_id", comment: "Session ID"),
                value: session.id.uuidString,
                icon: "number",
                color: .gray,
                allowCopy: true
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 时间详情卡片
    private var timeDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(NSLocalizedString("time_info", comment: "Time information"))
                .font(.headline)
                .foregroundColor(.primary)
            
            detailRow(
                title: NSLocalizedString("start_time", comment: "Start time"),
                value: formattedDateTime(session.startTime),
                icon: "clock.fill",
                color: .green
            )
            
            detailRow(
                title: NSLocalizedString("end_time", comment: "End time"),
                value: formattedDateTime(session.endTime),
                icon: "clock.badge.checkmark.fill",
                color: .red
            )
            
            detailRow(
                title: NSLocalizedString("duration_time", comment: "Duration time"),
                value: formatDuration(session.duration),
                icon: "stopwatch.fill",
                color: .blue
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 笔记卡片
    private func notesCard(_ notes: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(NSLocalizedString("notes", comment: "Notes"))
                .font(.headline)
                .foregroundColor(.primary)
            
            Text(notes)
                .font(.body)
                .foregroundColor(.primary)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 超频详情卡片
    private var overclockDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(NSLocalizedString("overclock_info", comment: "Overclock information"))
                .font(.headline)
                .foregroundColor(.primary)
            
            detailRow(
                title: NSLocalizedString("overclock_factor", comment: "Overclock factor"),
                value: String(format: "%.1f%@", session.overclockFactor, NSLocalizedString("overclock_multiplier", comment: "Multiplier")),
                icon: "bolt.fill",
                color: .orange
            )
            
            detailRow(
                title: NSLocalizedString("overclock_duration", comment: "Overclock duration"),
                value: formatDuration(session.overclockDuration),
                icon: "clock.arrow.2.circlepath",
                color: .orange
            )
            
            detailRow(
                title: NSLocalizedString("actual_duration", comment: "Actual duration"),
                value: formatDuration(session.duration - session.overclockDuration),
                icon: "timer",
                color: .blue
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 会话统计信息卡片
    private var sessionStatsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(NSLocalizedString("stats_info", comment: "Statistics information"))
                .font(.headline)
                .foregroundColor(.primary)
            
            detailRow(
                title: NSLocalizedString("xu_earned", comment: "XU earned"),
                value: String(format: "%.1f", session.xUnits),
                icon: "star.fill",
                color: .yellow
            )
            
            // 这里可以添加更多与该会话相关的统计信息
            // 例如该会话在所有会话中的排名、与平均时长的比较等
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
    }
    
    // 详情行视图组件
    private func detailRow(title: String, value: String, icon: String, color: Color, allowCopy: Bool = false) -> some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            if allowCopy {
                Button(action: {
                    UIPasteboard.general.string = value
                }) {
                    Image(systemName: "doc.on.doc")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(BorderlessButtonStyle())
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 格式化日期时间
    private func formattedDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    // 格式化时间
    private func formattedTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // 格式化持续时间
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? "0s"
    }
}

// MARK: - 预览
struct XMomentSessionDetailView_Previews: PreviewProvider {
    static let exampleDate = Date()
    static let exampleSession = XMomentSession(
        id: UUID(),
        startTime: exampleDate.addingTimeInterval(-45 * 60),
        endTime: exampleDate,
        duration: 45 * 60,
        mood: NSLocalizedString("focused", comment: "Focused mood"),
        category: NSLocalizedString("work", comment: "Work category"),
        notes: NSLocalizedString("example_note", comment: "Example note content"),
        overclockFactor: 1.5,
        isInterrupted: false
    )
    
    static var previews: some View {
        NavigationStack {
            XMomentSessionDetailView(session: exampleSession)
                .environmentObject(DataStore.shared)
        }
    }
} 