import SwiftUI
import UIKit
import Foundation

// 手绘素描风格的专注完成记录视图
struct XMomentRecordSketch: View {
    var session: XMomentSession
    var onSave: (XMomentSession) -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 会话状态
    @State private var editedSession: XMomentSession
    @State private var selectedCategory: String?
    @State private var selectedMood: String?
    @State private var notesText: String = ""

    // 动画状态
    @State private var isAppearing = false
    @State private var savingInProgress = false
    @State private var showCelebration = false
    @State private var drawingProgress: CGFloat = 0
    @State private var hoveringSkip = false
    @State private var hoveringSave = false
    @State private var pageRotation: Double = 0
    @State private var stars: [StarParticle] = []

    // 键盘相关
    @State private var keyboardHeight: CGFloat = 0
    @State private var isKeyboardVisible = false
    @State private var scrollOffset: CGFloat = 0

    // 键盘焦点状态
    @FocusState private var focusedField: FocusField?

    // 颜色定义
    private var paperColor: Color {
        colorScheme == .dark ? Color(red: 0.12, green: 0.12, blue: 0.14) : Color(red: 0.96, green: 0.95, blue: 0.92)
    }

    private var inkColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.9) : Color(red: 0.2, green: 0.2, blue: 0.2)
    }

    private var secondaryInkColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.6) : Color(red: 0.4, green: 0.4, blue: 0.4)
    }

    private var accentColor: Color {
        colorScheme == .dark ? Color(red: 0.8, green: 0.3, blue: 0.3) : Color(red: 0.8, green: 0.2, blue: 0.2)
    }

    private var lineColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.1) : Color.black.opacity(0.1)
    }

    // 类别选项 - 使用键名
    private let categoryKeys = ["category_work", "category_study", "category_creative", "category_personal"]

    // 心情数据 - 使用键名
    private let moodData: [(key: String, icon: String)] = [
        ("mood_focused", "brain.head.profile"),
        ("mood_efficient", "bolt.fill"),
        ("mood_tired", "cloud.rain.fill"),
        ("mood_distracted", "tornado")
    ]

    // 初始化
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self._editedSession = State(initialValue: session)
        self._selectedCategory = State(initialValue: session.category)
        self._selectedMood = State(initialValue: session.mood)
        self._notesText = State(initialValue: session.notes ?? "")
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 纸张背景
                paperBackground

                // 主要内容
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 25) {
                        // 顶部标题
                        titleSection

                        // 时间信息
                        timeInfoSection

                        // XU奖励
                        xuRewardSection

                        // 类别选择
                        categorySection

                        // 状态选择
                        moodSection

                        // 备注输入
                        notesSection

                        // 底部按钮
                        buttonSection

                        // 键盘显示时的额外空间
                        if isKeyboardVisible {
                            Spacer()
                                .frame(height: keyboardHeight)
                        }
                    }
                    .padding(.horizontal, 30) // 笔记本风格的边距
                    .padding(.vertical, 25)
                }
                .opacity(isAppearing ? 1 : 0)
                .scaleEffect(isAppearing ? 1 : 0.95)
                .rotation3DEffect(
                    .degrees(pageRotation),
                    axis: (x: 0.0, y: 1.0, z: 0.0),
                    anchor: .leading
                )

                // 庆祝动画
                if showCelebration {
                    celebrationView
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(paperColor)
            .dismissKeyboardOnTap(focused: $focusedField)
            .onAppear {
                animateAppearance()
                generateStars()
                setupKeyboardObservers()
            }
            .onDisappear {
                removeKeyboardObservers()
            }
        }

    }

    // 纸张背景
    private var paperBackground: some View {
        ZStack {
            // 纸张纹理
            Image("paper_texture")
                .resizable()
                .opacity(0.1)
                .blendMode(.multiply)

            // 横线
            VStack(spacing: 20) {
                ForEach(0..<20) { _ in
                    Rectangle()
                        .fill(lineColor)
                        .frame(height: 1)
                }
            }
            .padding(.horizontal, 20)

            // 装饰元素
            ForEach(0..<5) { i in
                let position = CGPoint(
                    x: CGFloat.random(in: 50...UIScreen.main.bounds.width-50),
                    y: CGFloat.random(in: 50...UIScreen.main.bounds.height-50)
                )

                if i % 2 == 0 {
                    // 小星星
                    StarShape()
                        .stroke(secondaryInkColor.opacity(0.5), lineWidth: 1)
                        .frame(width: CGFloat.random(in: 10...20), height: CGFloat.random(in: 10...20))
                        .position(position)
                } else {
                    // 小箭头
                    ArrowShape()
                        .stroke(secondaryInkColor.opacity(0.5), lineWidth: 1)
                        .frame(width: CGFloat.random(in: 15...25), height: CGFloat.random(in: 8...15))
                        .rotationEffect(.degrees(Double.random(in: 0...360)))
                        .position(position)
                }
            }
        }
    }

    // 顶部标题
    private var titleSection: some View {
        Text(NSLocalizedString("title_focus_completed", comment: ""))
            .font(.custom("Marker Felt", size: 28))
            .foregroundColor(inkColor)
            .frame(maxWidth: .infinity, alignment: .center)
            .drawingAnimation(progress: drawingProgress, delay: 0.1)
    }

    // 时间信息
    private var timeInfoSection: some View {
        VStack(spacing: 15) {
            HStack(spacing: 20) {
                // 开始时间
                VStack(alignment: .leading, spacing: 5) {
                    Text(NSLocalizedString("label_start_time", comment: ""))
                        .font(.custom("Marker Felt", size: 16))
                        .foregroundColor(secondaryInkColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.2)

                    Text(formatTimeOnly(editedSession.startTime))
                        .font(.custom("Marker Felt", size: 22))
                        .foregroundColor(inkColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.25)
                }

                Spacer()

                // 结束时间
                VStack(alignment: .trailing, spacing: 5) {
                    Text(NSLocalizedString("label_end_time", comment: ""))
                        .font(.custom("Marker Felt", size: 16))
                        .foregroundColor(secondaryInkColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.3)

                    Text(formatTimeOnly(editedSession.endTime))
                        .font(.custom("Marker Felt", size: 22))
                        .foregroundColor(inkColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.35)
                }
            }

            // 专注时长
            VStack(spacing: 5) {
                Text(NSLocalizedString("label_focus_duration", comment: ""))
                    .font(.custom("Marker Felt", size: 16))
                    .foregroundColor(secondaryInkColor)
                    .drawingAnimation(progress: drawingProgress, delay: 0.4)

                Text(formatDuration(editedSession.duration))
                    .font(.custom("Marker Felt", size: 28))
                    .foregroundColor(inkColor)
                    .drawingAnimation(progress: drawingProgress, delay: 0.45)
            }
            .padding(.top, 5)
        }
        .padding(20)
        .background(
            HandDrawnRectangle()
                .stroke(inkColor, lineWidth: 2)
                .drawingAnimation(progress: drawingProgress, delay: 0.15)
        )
    }

    // XU奖励
    private var xuRewardSection: some View {
        HStack(spacing: 15) {
            // XU值
            VStack(alignment: .leading, spacing: 5) {
                Text(NSLocalizedString("label_reward_earned", comment: ""))
                    .font(.custom("Marker Felt", size: 16))
                    .foregroundColor(secondaryInkColor)
                    .drawingAnimation(progress: drawingProgress, delay: 0.5)

                HStack(alignment: .firstTextBaseline, spacing: 5) {
                    Text("\(Int(editedSession.xUnits))")
                        .font(.custom("Marker Felt", size: 32))
                        .foregroundColor(accentColor)

                    Text("XU")
                        .font(.custom("Marker Felt", size: 20))
                        .foregroundColor(accentColor)
                }
                .drawingAnimation(progress: drawingProgress, delay: 0.55)
            }

            Spacer()

            // 超频奖励
            if editedSession.overclockFactor > 1.0 {
                VStack(alignment: .trailing, spacing: 5) {
                    Text(NSLocalizedString("label_overclock_bonus", comment: ""))
                        .font(.custom("Marker Felt", size: 16))
                        .foregroundColor(secondaryInkColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.6)

                    Text("+\(Int((editedSession.overclockFactor - 1.0) * 100))%")
                        .font(.custom("Marker Felt", size: 24))
                        .foregroundColor(accentColor)
                        .drawingAnimation(progress: drawingProgress, delay: 0.65)
                }
                .onAppear {
                    if !showCelebration {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
                            withAnimation {
                                showCelebration = true
                            }

                            // 3秒后隐藏庆祝动画
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                withAnimation {
                                    showCelebration = false
                                }
                            }
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            HandDrawnRectangle()
                .stroke(accentColor, lineWidth: 2)
                .drawingAnimation(progress: drawingProgress, delay: 0.5)
        )
    }

    // 类别选择
    private var categorySection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("label_category", comment: ""))
                .font(.custom("Marker Felt", size: 18))
                .foregroundColor(inkColor)
                .drawingAnimation(progress: drawingProgress, delay: 0.7)

            HStack(spacing: 15) {
                ForEach(categoryKeys, id: \.self) { key in
                    categoryButton(categoryKey: key)
                }
            }
            .drawingAnimation(progress: drawingProgress, delay: 0.75)
        }
    }

    // 状态选择
    private var moodSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("label_status", comment: ""))
                .font(.custom("Marker Felt", size: 18))
                .foregroundColor(inkColor)
                .drawingAnimation(progress: drawingProgress, delay: 0.8)

            HStack(spacing: 15) {
                ForEach(moodData, id: \.key) { data in
                   moodButton(moodKey: data.key, icon: data.icon)
                }
            }
            .drawingAnimation(progress: drawingProgress, delay: 0.85)
        }
    }

    // 备注输入
    private var notesSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString("label_notes", comment: ""))
                .font(.custom("Marker Felt", size: 18))
                .foregroundColor(inkColor)
                .drawingAnimation(progress: drawingProgress, delay: 0.9)

            TextField(NSLocalizedString("placeholder_add_notes", comment: ""), text: $notesText)
                .font(.custom("Marker Felt", size: 16))
                .foregroundColor(inkColor)
                .padding(12)
                .background(
                    HandDrawnRectangle()
                        .stroke(secondaryInkColor, lineWidth: 1)
                        .drawingAnimation(progress: drawingProgress, delay: 0.95)
                )
                .focused($focusedField, equals: .notes)
                .onChange(of: notesText) { _, _ in
                    editedSession.notes = notesText.isEmpty ? nil : notesText
                }
        }
    }

    // 底部按钮
    private var buttonSection: some View {
        HStack(spacing: 30) {
            // 跳过按钮
            Button {
                dismissWithAnimation()
            } label: {
                Text(NSLocalizedString("button_skip", comment: ""))
                    .font(.custom("Marker Felt", size: 18))
                    .foregroundColor(secondaryInkColor)
                    .padding(.horizontal, 25)
                    .padding(.vertical, 12)
                    .background(
                        HandDrawnRectangle()
                            .stroke(secondaryInkColor, lineWidth: hoveringSkip ? 2 : 1)
                            .drawingAnimation(progress: drawingProgress, delay: 1.0)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .onHover { hovering in
                withAnimation(.easeInOut(duration: 0.2)) {
                    hoveringSkip = hovering
                }
            }

            // 保存按钮
            Button {
                saveRecord()
            } label: {
                Text(NSLocalizedString("button_save", comment: ""))
                    .font(.custom("Marker Felt", size: 18))
                    .foregroundColor(colorScheme == .dark ? .black : .white)
                    .padding(.horizontal, 25)
                    .padding(.vertical, 12)
                    .background(
                        ZStack {
                            HandDrawnRectangle()
                                .fill(accentColor)

                            if hoveringSave {
                                HandDrawnRectangle()
                                    .stroke(inkColor, lineWidth: 2)
                            }
                        }
                        .drawingAnimation(progress: drawingProgress, delay: 1.05)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .onHover { hovering in
                withAnimation(.easeInOut(duration: 0.2)) {
                    hoveringSave = hovering
                }
            }
        }
        .padding(.top, 10)
    }

    // 庆祝动画视图
    private var celebrationView: some View {
        ZStack {
            ForEach(stars) { star in
                StarShape()
                    .fill(star.color)
                    .frame(width: star.size, height: star.size)
                    .position(star.position)
                    .opacity(star.opacity)
                    .rotationEffect(.degrees(star.rotation))
            }
        }
    }

    // 类别按钮 - 接受 categoryKey
    private func categoryButton(categoryKey: String) -> some View {
        let isSelected = selectedCategory == categoryKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                selectedCategory = (selectedCategory == categoryKey) ? nil : categoryKey
                editedSession.category = selectedCategory
            }
            playHapticFeedback()
        } label: {
            Text(NSLocalizedString(categoryKey, comment: ""))
                .font(.custom("Marker Felt", size: 16))
                .foregroundColor(isSelected ? (colorScheme == .dark ? .black : .white) : inkColor)
                .padding(.horizontal, 15)
                .padding(.vertical, 8)
                .background(
                    ZStack {
                        if isSelected {
                            HandDrawnRectangle()
                                .fill(accentColor)
                        } else {
                            HandDrawnRectangle()
                                .stroke(secondaryInkColor, lineWidth: 1)
                        }
                    }
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 心情按钮 - 接受 moodKey
    private func moodButton(moodKey: String, icon: String) -> some View {
        let isSelected = selectedMood == moodKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                selectedMood = (selectedMood == moodKey) ? nil : moodKey
                editedSession.mood = selectedMood
            }
            playHapticFeedback()
        } label: {
            VStack(spacing: 5) {
                Image(systemName: icon)
                    .font(.system(size: 16))

                Text(NSLocalizedString(moodKey, comment: ""))
                    .font(.custom("Marker Felt", size: 14))
            }
            .foregroundColor(isSelected ? (colorScheme == .dark ? .black : .white) : inkColor)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                ZStack {
                    if isSelected {
                        HandDrawnRectangle()
                            .fill(accentColor)
                    } else {
                        HandDrawnRectangle()
                            .stroke(secondaryInkColor, lineWidth: 1)
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 辅助函数

    // 触感反馈
    private func playHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // 出现动画
    private func animateAppearance() {
        // 绘制进度动画
        withAnimation(.easeOut(duration: 1.5)) {
            drawingProgress = 1.0
        }

        // 整体出现动画
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.2)) {
            isAppearing = true
        }
    }

    // 关闭视图
    private func dismissWithAnimation() {
        // 页面卷曲效果
        withAnimation(.easeInOut(duration: 0.5)) {
            pageRotation = 30
            isAppearing = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismiss()
        }
    }

    // 保存记录
    private func saveRecord() {
        // 触感反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 创建一个新的 Session 对象来传递，确保数据最新
        var finalSession = session // 从原始 session 复制基础数据

        // 更新为当前 UI 的状态
        finalSession.mood = selectedMood
        finalSession.category = selectedCategory
        finalSession.notes = notesText.isEmpty ? nil : notesText

        // 保留原始的超频信息
        finalSession.overclockFactor = session.overclockFactor
        finalSession.overclockDuration = session.overclockDuration

        // 回调 - 传递新创建的 finalSession
        onSave(finalSession)

        // 关闭视图
        dismissWithAnimation()
    }

    // 生成星星粒子
    private func generateStars() {
        stars = (0..<30).map { _ in
            let size = CGFloat.random(in: 10...30)
            let screenWidth = UIScreen.main.bounds.width
            let screenHeight = UIScreen.main.bounds.height

            return StarParticle(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...screenWidth),
                    y: CGFloat.random(in: 0...screenHeight)
                ),
                size: size,
                color: [accentColor, inkColor, secondaryInkColor].randomElement()!,
                opacity: Double.random(in: 0.3...0.8),
                rotation: Double.random(in: 0...360)
            )
        }
    }

    // 仅格式化时间
    private func formatTimeOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - 辅助视图和形状

// 手绘矩形
struct HandDrawnRectangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 添加轻微的不规则性
        let jitter: (CGFloat) -> CGFloat = { max in
            return CGFloat.random(in: -max...max)
        }

        // 起点（左上角）
        let startX = rect.minX + jitter(2)
        let startY = rect.minY + jitter(2)
        path.move(to: CGPoint(x: startX, y: startY))

        // 右上角
        path.addLine(to: CGPoint(x: rect.maxX + jitter(2), y: rect.minY + jitter(2)))

        // 右下角
        path.addLine(to: CGPoint(x: rect.maxX + jitter(2), y: rect.maxY + jitter(2)))

        // 左下角
        path.addLine(to: CGPoint(x: rect.minX + jitter(2), y: rect.maxY + jitter(2)))

        // 闭合路径
        path.closeSubpath()

        return path
    }
}

// 星星形状
struct StarShape: Shape {
    func path(in rect: CGRect) -> Path {
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let outerRadius = min(rect.width, rect.height) / 2
        let innerRadius = outerRadius * 0.4

        var path = Path()
        let jitter: (CGFloat) -> CGFloat = { max in
            return CGFloat.random(in: -max...max)
        }

        for i in 0..<5 {
            let outerAngle = Double(i) * 2 * .pi / 5 - .pi / 2
            let innerAngle = outerAngle + .pi / 5

            let outerPoint = CGPoint(
                x: center.x + Darwin.cos(outerAngle) * (outerRadius + jitter(2)),
                y: center.y + Darwin.sin(outerAngle) * (outerRadius + jitter(2))
            )

            let innerPoint = CGPoint(
                x: center.x + Darwin.cos(innerAngle) * (innerRadius + jitter(1)),
                y: center.y + Darwin.sin(innerAngle) * (innerRadius + jitter(1))
            )

            if i == 0 {
                path.move(to: outerPoint)
            } else {
                path.addLine(to: outerPoint)
            }

            path.addLine(to: innerPoint)
        }

        path.closeSubpath()
        return path
    }
}

// 箭头形状
struct ArrowShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 添加轻微的不规则性
        let jitter: (CGFloat) -> CGFloat = { max in
            return CGFloat.random(in: -max...max)
        }

        // 箭杆
        path.move(to: CGPoint(x: rect.minX, y: rect.midY + jitter(1)))
        path.addLine(to: CGPoint(x: rect.maxX - rect.width * 0.3, y: rect.midY + jitter(1)))

        // 箭头
        path.move(to: CGPoint(x: rect.maxX - rect.width * 0.3, y: rect.minY + jitter(1)))
        path.addLine(to: CGPoint(x: rect.maxX + jitter(1), y: rect.midY + jitter(1)))
        path.addLine(to: CGPoint(x: rect.maxX - rect.width * 0.3, y: rect.maxY + jitter(1)))

        return path
    }
}

// 绘制动画修饰符
struct DrawingAnimationModifier: ViewModifier {
    var progress: CGFloat
    var delay: Double

    @State private var localProgress: CGFloat = 0

    func body(content: Content) -> some View {
        content
            .opacity(localProgress)
            .onAppear {
                withAnimation(.easeOut(duration: 0.5).delay(delay)) {
                    localProgress = progress
                }
            }
            .onChange(of: progress) { _, newValue in
                withAnimation(.easeOut(duration: 0.5).delay(delay)) {
                    localProgress = newValue
                }
            }
    }
}

// 星星粒子
struct StarParticle: Identifiable {
    let id: UUID
    var position: CGPoint
    var size: CGFloat
    var color: Color
    var opacity: Double
    var rotation: Double
}

// MARK: - 扩展

extension View {
    func drawingAnimation(progress: CGFloat, delay: Double) -> some View {
        self.modifier(DrawingAnimationModifier(progress: progress, delay: delay))
    }

    // 移除旧的 hideKeyboardOnTap
    /*
    func hideKeyboardOnTap() -> some View {
// ... existing code ...
    }
    */
}

// MARK: - XMomentRecordSketch 扩展

extension XMomentRecordSketch {
    // 设置键盘观察者
    func setupKeyboardObservers() {
        NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
            guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }

            let keyboardHeight = keyboardFrame.height
            self.keyboardHeight = keyboardHeight
            self.isKeyboardVisible = true
        }

        NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
            self.isKeyboardVisible = false
            self.keyboardHeight = 0
        }
    }

    // 移除键盘观察者
    func removeKeyboardObservers() {
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
    }
}

// 焦点字段枚举
enum FocusField: Hashable {
    case notes
}

// MARK: - 预览
struct XMomentRecordSketch_Previews: PreviewProvider {
    static var previews: some View {
        XMomentRecordSketch(
            session: XMomentSession.create(
                duration: 25 * 60,
                category: "category_work",
                mood: "mood_focused",
                notes: NSLocalizedString("preview_notes_project_planning_done", comment: ""),
                overclockFactor: 1.2,
                overclockDuration: 5 * 60
            ),
            onSave: { _ in }
        )
        .environment(\..locale, .init(identifier: "zh-Hans"))
    }
}
