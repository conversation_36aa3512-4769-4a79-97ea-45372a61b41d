import SwiftUI
import UIKit

// 支持的主题
enum XMomentTheme: String, CaseIterable, Identifiable, Codable {
    case apple = "theme_apple"
    case elegant = "theme_elegant"
    case neonCyber = "theme_neon_cyber"
    case sketch = "theme_sketch"
    case boardingPass = "theme_boarding_pass"

    var id: String { self.rawValue }

    // 获取当前主题的预览图
    var previewImage: String {
        switch self {
        case .apple: return "theme_apple"
        case .elegant: return "theme_elegant"
        case .neonCyber: return "theme_neon"
        case .sketch: return "theme_sketch"
        case .boardingPass: return "theme_boarding_pass"
        }
    }

    // 主题描述
    var description: String {
        switch self {
        case .apple:
            return NSLocalizedString("theme_apple_description", comment: "Apple style theme description")
        case .elegant:
            return NSLocalizedString("theme_elegant_description", comment: "Elegant modern theme description")
        case .neonCyber:
            return NSLocalizedString("theme_neon_cyber_description", comment: "Neon cyber theme description")
        case .sketch:
            return NSLocalizedString("theme_sketch_description", comment: "Sketch theme description")
        case .boardingPass:
            return NSLocalizedString("theme_boarding_pass_description", comment: "Boarding pass theme description")
        }
    }

    // 本地化显示名称
    var localizedName: String {
        switch self {
        case .apple: return NSLocalizedString("theme_apple", comment: "Apple style theme")
        case .elegant: return NSLocalizedString("theme_elegant", comment: "Elegant modern theme")
        case .neonCyber: return NSLocalizedString("theme_neon_cyber", comment: "Neon cyber theme")
        case .sketch: return NSLocalizedString("theme_sketch", comment: "Sketch theme")
        case .boardingPass: return NSLocalizedString("theme_boarding_pass", comment: "Boarding pass theme")
        }
    }
}

// 主题管理器
class XMomentThemeManager: ObservableObject {
    @Published var currentTheme: XMomentTheme

    // 用户默认键
    private let themeKey = "XMomentPreferredTheme"

    init() {
        // 从用户默认值加载主题
        if let savedThemeData = UserDefaults.standard.data(forKey: themeKey),
           let savedTheme = try? JSONDecoder().decode(XMomentTheme.self, from: savedThemeData) {
            // 检查已保存的主题是否仍然是有效 case（防止移除主题后崩溃）
            if XMomentTheme.allCases.contains(savedTheme) {
                self.currentTheme = savedTheme
            } else {
                self.currentTheme = .elegant // 如果无效，则重置为优雅主题
                UserDefaults.standard.removeObject(forKey: themeKey) // 清除无效设置
            }
        } else {
            // 默认主题
            self.currentTheme = .elegant
        }
    }

    // 设置主题并保存到UserDefaults
    func setTheme(_ theme: XMomentTheme) {
        self.currentTheme = theme

        // 保存设置
        if let themeData = try? JSONEncoder().encode(theme) {
            UserDefaults.standard.set(themeData, forKey: themeKey)
        }
    }

    // 获取适合当前主题的记录视图
    func recordView(for session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) -> AnyView {
        switch currentTheme {
        case .apple:
            return AnyView(XMomentRecordApple(session: session, onSave: onSave))
        case .elegant:
            return AnyView(XMomentRecordElegant(session: session, onSave: onSave))
        case .neonCyber:
            return AnyView(XMomentRecordNeonCyber(session: session, onSave: onSave))
        case .sketch:
            return AnyView(XMomentRecordSketch(session: session, onSave: onSave))
        case .boardingPass:
            return AnyView(XMomentRecordBoardingPass(session: session, onSave: onSave))
        }
    }

    // 临时占位符视图，直到具体主题视图被创建
    private func placeholderView(theme: XMomentTheme) -> some View {
        VStack {
            Spacer()
            Text("\(theme.localizedName) \(NSLocalizedString("theme", comment: "Theme"))")
                .font(.title)
            Text(NSLocalizedString("theme_in_development", comment: "Theme view is under development"))
                .font(.subheadline)
                .foregroundColor(.gray)
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(uiColor: .systemBackground))
    }
}

// 主题设置视图
struct XMomentThemeSettingsView: View {
    @EnvironmentObject private var themeManager: XMomentThemeManager
    @Environment(\.dismiss) private var dismiss

    @State private var selectedTheme: XMomentTheme

    init() {
        // 使用 EnvironmentObject 初始化 selectedTheme
        _selectedTheme = State(initialValue: XMomentThemeManager().currentTheme)
    }

    var body: some View {
        NavigationStack {
            List {
                // 主题选择区
                Section(header: Text(NSLocalizedString("focus_record_theme", comment: "Focus record theme"))) {
                    ForEach(XMomentTheme.allCases) { theme in
                        themeOptionRow(theme)
                    }
                }

                // 主题预览区
                Section(header: Text(NSLocalizedString("theme_preview", comment: "Theme preview"))) {
                    VStack(alignment: .center, spacing: 10) {
                        // 使用临时解决方案，直到图像资源准备好
                        ZStack {
                            // 背景矩形（占位符）
                            Rectangle()
                                .fill(Color.secondary.opacity(0.15))
                                .frame(height: 200)
                                .clipShape(RoundedRectangle(cornerRadius: 12))

                            // 主题名称提示
                            VStack(spacing: 12) {
                                Image(systemName: themeIcon(for: selectedTheme))
                                    .font(.system(size: 40))
                                    .foregroundColor(themeColor(for: selectedTheme))

                                Text(selectedTheme.localizedName)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(themeColor(for: selectedTheme))
                            }
                        }

                        // 主题描述
                        Text(selectedTheme.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.vertical, 10)
                    .frame(maxWidth: .infinity)
                }
            }
            .navigationTitle(NSLocalizedString("focus_record_theme", comment: "Focus record theme"))
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(NSLocalizedString("done", comment: "Done")) {
                        // 应用选择的主题
                        themeManager.setTheme(selectedTheme)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }

                ToolbarItem(placement: .navigationBarLeading) {
                    Button(NSLocalizedString("cancel", comment: "Cancel")) {
                        dismiss()
                    }
                }
            }
            // 在视图出现时同步 EnvironmentObject 的当前主题
            .onAppear {
                selectedTheme = themeManager.currentTheme
            }
        }
    }

    // 主题选项行
    private func themeOptionRow(_ theme: XMomentTheme) -> some View {
        HStack {
            VStack(alignment: .leading) {
                Text(theme.localizedName)
                    .font(.system(size: 17, weight: .medium))
            }

            Spacer()

            if selectedTheme == theme {
                Image(systemName: "checkmark")
                    .foregroundColor(.blue)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            selectedTheme = theme
        }
        .padding(.vertical, 4)
    }

    // 主题图标
    private func themeIcon(for theme: XMomentTheme) -> String {
        switch theme {
        case .apple:
            return "apple.logo"
        case .elegant:
            return "sparkles"
        case .neonCyber:
            return "bolt.fill"
        case .sketch:
            return "pencil.and.ruler.fill"
        case .boardingPass:
            return "airplane.circle.fill"
        }
    }

    // 主题颜色
    private func themeColor(for theme: XMomentTheme) -> Color {
        switch theme {
        case .apple:
            return Color(red: 0.2, green: 0.8, blue: 0.8) // 青绿色
        case .elegant:
            return Color(red: 0.4, green: 0.4, blue: 0.8) // 优雅紫色
        case .neonCyber:
            return Color(red: 0.9, green: 0.2, blue: 0.8) // 霓虎粉色
        case .sketch:
            return Color(red: 0.8, green: 0.2, blue: 0.2) // 手绘红色
        case .boardingPass:
            return Color(red: 1.0, green: 0.9, blue: 0.0) // 航空黄色
        }
    }
}