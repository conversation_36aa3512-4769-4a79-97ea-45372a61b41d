import SwiftUI
import UIKit

// Apple风格的专注完成记录视图 - 参考iOS设计语言
struct XMomentRecordApple: View {
    var session: XMomentSession
    var onSave: (XMomentSession) -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 会话状态
    @State private var editedSession: XMomentSession
    @State private var selectedCategory: String?
    @State private var selectedMood: String?
    @State private var notesText: String = ""

    // 动画状态
    @State private var isAppearing = false
    @State private var savingInProgress = false
    @State private var showCheckmarks = false

    // 键盘焦点状态
    @FocusState private var focusedField: FocusField?

    // 颜色定义
    private var mainColor: Color {
        colorScheme == .dark ?
            Color(red: 0.1, green: 0.6, blue: 0.6) : // 深色模式下的深青绿色
            Color(red: 0.2, green: 0.8, blue: 0.8)   // 浅色模式下的青绿色
    }

    private var accentColor: Color {
        colorScheme == .dark ?
            Color(red: 0.9, green: 0.7, blue: 0.2) : // 深色模式下的金黄色
            Color(red: 1.0, green: 0.8, blue: 0.3)   // 浅色模式下的金黄色
    }

    private var cardColor: Color {
        colorScheme == .dark ?
            Color(red: 0.15, green: 0.15, blue: 0.15) : // 深色模式下的深灰色
            Color.white                               // 浅色模式下的白色
    }

    private var backgroundColor: Color {
        colorScheme == .dark ?
            Color(red: 0.1, green: 0.5, blue: 0.5) : // 深色模式下的深青绿色背景
            Color(red: 0.2, green: 0.8, blue: 0.8)   // 浅色模式下的青绿色背景
    }

    // 文字颜色
    private var primaryTextColor: Color {
        colorScheme == .dark ? Color.white : Color.black
    }

    private var secondaryTextColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.7) : Color.black.opacity(0.6)
    }

    // 心情选项 - 使用键名
    private let moodData: [(key: String, icon: String)] = [
        ("mood_focused", "brain.head.profile"),
        ("mood_efficient", "bolt.fill"),
        ("mood_tired", "cloud.rain.fill"),
        ("mood_distracted", "tornado")
    ]

    // 分类选项 - 使用键名
    private let categoryKeys = ["category_study", "category_work", "category_exercise", "category_other"]

    // 初始化
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self._editedSession = State(initialValue: session)
        self._selectedCategory = State(initialValue: session.category) // 存储键名
        self._selectedMood = State(initialValue: session.mood) // 存储键名
        self._notesText = State(initialValue: session.notes ?? "")
    }

    var body: some View {
        ZStack {
            // 背景
            backgroundColor.ignoresSafeArea()

            // 主卡片
            VStack(spacing: 0) {
                // 顶部时间信息区域
                VStack(spacing: 4) {
                    // 专注时间 - 大字体粗体
                    Text(formatDuration(editedSession.duration))
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(primaryTextColor)

                    // 开始-结束时间 - 小字体淡色
                    Text("\(formatTimeOnly(editedSession.startTime)) - \(formatTimeOnly(editedSession.endTime))")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(secondaryTextColor)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(cardColor)
                        .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 2)
                )
                .overlay(
                    // 顶部圆形指示器
                    Circle()
                        .fill(accentColor)
                        .frame(width: 40, height: 40)
                        .overlay(
                            Circle()
                                .stroke(Color.black, lineWidth: 3)
                        )
                        .overlay(
                            Circle()
                                .fill(Color(white: 0.9))
                                .frame(width: 20, height: 20)
                        )
                        .offset(y: -20),
                    alignment: .top
                )
                .padding(.bottom, 10) // 增加与下方黄色卡片的间距

                // 主内容区域
                VStack(spacing: 12) {
                    // 奖励信息区域 - 移到顶部
                    rewardInfoArea

                    // 心情选择区
                    moodSelectionArea

                    // 类别选择区
                    categorySelectionArea

                    // 备注输入框 - 对齐圆形图标
                    notesInputArea

                    // 底部按钮区域
                    buttonArea
                }
                .padding(.vertical, 20)
                .padding(.horizontal, 15)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(red: 1.0, green: 0.85, blue: 0.7)) // 橙色背景
                        .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 2)
                )
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 40)
            .scaleEffect(isAppearing ? 1 : 0.9)
            .opacity(isAppearing ? 1 : 0)
        }
        .dismissKeyboardOnTap(focused: $focusedField) // 应用修饰符

        .onAppear {
            animateAppearance()

            // 延迟显示勾选标记
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    showCheckmarks = true
                }
            }
        }
    }

    // 心情选择区域
    private var moodSelectionArea: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("label_feeling", comment: ""))
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(secondaryTextColor)
                .padding(.leading, 5)
                .padding(.bottom, 2)

            HStack(spacing: 8) {
                // 使用 moodData
                ForEach(moodData, id: \.key) { data in
                    moodButton(moodKey: data.key, icon: data.icon)
                }
            }
        }
    }

    // 心情按钮 - 接受 moodKey
    private func moodButton(moodKey: String, icon: String) -> some View {
        let isSelected = selectedMood == moodKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedMood == moodKey {
                    selectedMood = nil
                } else {
                    selectedMood = moodKey
                }
                editedSession.mood = selectedMood
            }
            playHapticFeedback()
        } label: {
            VStack(spacing: 6) {
                ZStack {
                    Circle()
                        .fill(isSelected ? mainColor : cardColor)
                        .frame(width: 50, height: 50)
                        .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.1), radius: 2, x: 0, y: 1)

                    Image(systemName: icon)
                        .font(.system(size: 20))
                        .foregroundColor(isSelected ? .white : primaryTextColor)
                }

                // 显示本地化字符串
                Text(NSLocalizedString(moodKey, comment: ""))
                    .font(.system(size: 12, weight: isSelected ? .bold : .medium))
                    .foregroundColor(primaryTextColor)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 类别选择区域
    private var categorySelectionArea: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("label_category", comment: ""))
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(secondaryTextColor)
                .padding(.leading, 5)
                .padding(.bottom, 2)

            HStack(spacing: 8) {
                // 使用 categoryKeys
                ForEach(categoryKeys, id: \.self) { key in
                    categoryButton(categoryKey: key)
                }
            }
        }
    }

    // 类别按钮 - 接受 categoryKey
    private func categoryButton(categoryKey: String) -> some View {
        let isSelected = selectedCategory == categoryKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedCategory == categoryKey {
                    selectedCategory = nil
                } else {
                    selectedCategory = categoryKey
                }
                editedSession.category = selectedCategory
            }
            playHapticFeedback()
        } label: {
            VStack(spacing: 6) {
                ZStack {
                    Circle()
                        .fill(isSelected ? mainColor : cardColor)
                        .frame(width: 50, height: 50)
                        .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.1), radius: 2, x: 0, y: 1)

                    Image(systemName: iconForCategory(categoryKey))
                        .font(.system(size: 20))
                        .foregroundColor(isSelected ? .white : primaryTextColor)
                }

                // 显示本地化字符串
                Text(NSLocalizedString(categoryKey, comment: ""))
                    .font(.system(size: 12, weight: isSelected ? .bold : .medium))
                    .foregroundColor(primaryTextColor)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 奖励信息区域 - 移到顶部
    private var rewardInfoArea: some View {
        HStack(spacing: 10) {
            // XU奖励
            VStack(alignment: .leading, spacing: 2) {
                Text("\(Int(editedSession.xUnits))")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(primaryTextColor)

                Text("XU EARNED")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(secondaryTextColor)
            }

            Spacer()

            // 超频奖励
            if editedSession.overclockFactor > 1.0 {
                VStack(alignment: .trailing, spacing: 2) {
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        Text("+")
                            .font(.system(size: 20, weight: .bold))
                        Text("\(Int((editedSession.overclockFactor - 1.0) * 100) / 50)")
                            .font(.system(size: 36, weight: .bold, design: .rounded))
                    }
                    .foregroundColor(primaryTextColor)

                    Text("OVERCLOCK POINTS")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(secondaryTextColor)
                }
            }
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 5)
    }

    // 备注输入区域 - 对齐圆形图标左右边缘
    private var notesInputArea: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 与类别标题完全相同的格式
            Text(NSLocalizedString("label_notes", comment: ""))
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(secondaryTextColor)
                .padding(.leading, 5)
                .padding(.bottom, 2)

            // 使用与类别选择区域相同的布局和间距
            HStack(spacing: 8) {
                // 输入框与类别按钮保持相同的对齐方式
                TextField(NSLocalizedString("placeholder_add_short_notes", comment: ""), text: $notesText)
                    .font(.system(size: 15))
                    .foregroundColor(primaryTextColor)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(cardColor)
                            .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.1), radius: 2, x: 0, y: 1)
                    )
                    .focused($focusedField, equals: .notes) // 添加焦点绑定
                    .onChange(of: notesText) { _, _ in
                        editedSession.notes = notesText
                    }
            }
            .frame(height: 50) // 与圆形图标区域高度一致
        }
    }

    // 底部按钮区域
    private var buttonArea: some View {
        // 只保留保存按钮
        Button {
            saveRecord()
        } label: {
            Text(NSLocalizedString("button_save", comment: ""))
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(mainColor)
                        .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.4 : 0.2), radius: 3, x: 0, y: 2)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.top, 5)
    }

    // MARK: - 辅助函数

    // 根据类别获取图标 - 参数改为 key
    private func iconForCategory(_ categoryKey: String) -> String {
        switch categoryKey {
        case "category_study": return "book.fill"
        case "category_work": return "briefcase.fill"
        case "category_exercise": return "figure.run"
        case "category_other": return "ellipsis.circle.fill"
        default: return "circle"
        }
    }

    // 触感反馈
    private func playHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // 出现动画
    private func animateAppearance() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            isAppearing = true
        }
    }

    // 关闭视图
    private func dismissWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isAppearing = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }

    // 保存记录
    private func saveRecord() {
        // 触感反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 创建一个新的 Session 对象来传递，确保数据最新
        var finalSession = session // 从原始 session 复制基础数据 (ID, times, etc.)

        // 更新为当前 UI 的状态
        finalSession.mood = selectedMood
        finalSession.category = selectedCategory
        finalSession.notes = notesText.isEmpty ? nil : notesText

        // 保留原始的超频信息 (如果需要的话，这部分逻辑不变)
        finalSession.overclockFactor = session.overclockFactor
        finalSession.overclockDuration = session.overclockDuration

        // 回调 - 传递新创建的 finalSession
        onSave(finalSession)

        // 关闭视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismissWithAnimation()
        }
    }

    // 格式化日期（短格式）
    private func formatDateShort(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "202≈ MM/dd HH:mm"
        return formatter.string(from: date)
    }

    // 仅格式化时间
    private func formatTimeOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    // 焦点字段枚举
    enum FocusField: Hashable {
        case notes
    }
}

// MARK: - 预览
struct XMomentRecordApple_Previews: PreviewProvider {
    static var previews: some View {
        XMomentRecordApple(
            session: XMomentSession.create(
                duration: 25 * 60,
                category: "category_work", // 使用键名
                mood: "mood_focused", // 使用键名
                notes: NSLocalizedString("preview_notes_project_planning_done", comment: ""),
                overclockFactor: 1.2,
                overclockDuration: 5 * 60
            ),
            onSave: { _ in }
        )
        .environment(\..locale, .init(identifier: "zh-Hans")) // 添加环境设置以在预览中显示中文
    }
}
