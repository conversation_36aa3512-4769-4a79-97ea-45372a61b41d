import SwiftUI
import UIKit

// 霓虎赛博风格的专注完成记录视图
struct XMomentRecordNeonCyber: View {
    var session: XMomentSession
    var onSave: (XMomentSession) -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 会话状态
    @State private var editedSession: XMomentSession
    @State private var selectedCategory: String?
    @State private var selectedMood: String?
    @State private var notesText: String = ""

    // 动画状态
    @State private var isAppearing = false
    @State private var savingInProgress = false
    @State private var glowIntensity: Double = 0.5
    @State private var scanlineOffset: CGFloat = 0
    @State private var showPowerUp = false

    // 键盘焦点状态
    @FocusState private var focusedField: FocusField?

    // 颜色定义
    private let backgroundColor = Color.black // 黑色背景
    private let primaryColor = Color(red: 0.9, green: 0.2, blue: 0.8) // 霓虹粉色
    private let secondaryColor = Color(red: 0.2, green: 0.8, blue: 0.9) // 霓虹蓝色
    private let accentColor = Color(red: 0.9, green: 0.8, blue: 0.2) // 霓虹黄色

    // 心情选项及其对应颜色 - 使用键名
    private let moodData: [(key: String, icon: String, color: Color)] = [
        ("mood_focused", "brain.head.profile", Color(red: 0.9, green: 0.2, blue: 0.8)), // 粉色
        ("mood_efficient", "bolt.fill", Color(red: 0.2, green: 0.8, blue: 0.9)), // 蓝色
        ("mood_tired", "cloud.rain.fill", Color(red: 0.9, green: 0.4, blue: 0.4)), // 红色
        ("mood_distracted", "tornado", Color(red: 0.9, green: 0.8, blue: 0.2)) // 黄色
    ]

    // 分类选项 - 使用键名
    private let categoryKeys = ["category_study", "category_work", "category_exercise", "category_other"]

    // 初始化
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self._editedSession = State(initialValue: session)
        self._selectedCategory = State(initialValue: session.category) // 存储键名
        self._selectedMood = State(initialValue: session.mood) // 存储键名
        self._notesText = State(initialValue: session.notes ?? "")
    }

    var body: some View {
        ZStack {
            // 背景
            backgroundColor.ignoresSafeArea()

            // 网格背景
            gridBackground

            // 扫描线效果
            scanlineEffect

            // 主内容
            VStack(spacing: 25) {
                // 顶部信息区域
                topInfoSection

                // 状态选择区域
                moodSelectionSection

                // 分类选择区域
                categorySelectionSection

                // 备注输入区域
                notesInputSection

                // 底部按钮区域
                bottomButtonSection
            }
            .padding(.horizontal, 25)
            .padding(.vertical, 30)
            .opacity(isAppearing ? 1 : 0)
            .offset(y: isAppearing ? 0 : 20)

            // 能量提升动画
            if showPowerUp {
                powerUpAnimation
            }
        }
        .dismissKeyboardOnTap(focused: $focusedField)
        .onAppear {
            animateAppearance()
            startAnimations()
        }
    }

    // 网格背景
    private var gridBackground: some View {
        ZStack {
            // 水平线
            ForEach(0..<20) { i in
                Path { path in
                    let y = CGFloat(i) * 40
                    path.move(to: CGPoint(x: 0, y: y))
                    path.addLine(to: CGPoint(x: UIScreen.main.bounds.width, y: y))
                }
                .stroke(primaryColor.opacity(0.2), lineWidth: 1)
            }

            // 垂直线
            ForEach(0..<15) { i in
                Path { path in
                    let x = CGFloat(i) * 40
                    path.move(to: CGPoint(x: x, y: 0))
                    path.addLine(to: CGPoint(x: x, y: UIScreen.main.bounds.height))
                }
                .stroke(secondaryColor.opacity(0.2), lineWidth: 1)
            }
        }
    }

    // 扫描线效果
    private var scanlineEffect: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [
                        Color.clear,
                        primaryColor.opacity(0.2),
                        secondaryColor.opacity(0.2),
                        Color.clear
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .frame(height: 100)
            .offset(y: scanlineOffset)
            .opacity(0.5)
            .blendMode(.screen)
    }

    // 顶部信息区域
    private var topInfoSection: some View {
        VStack(spacing: 15) {
            // XU值
            HStack(alignment: .firstTextBaseline) {
                Text("\(Int(editedSession.xUnits))")
                    .font(.system(size: 60, weight: .bold, design: .monospaced))
                    .foregroundColor(primaryColor)
                    .shadow(color: primaryColor.opacity(glowIntensity), radius: 10, x: 0, y: 0)

                Text("XU")
                    .font(.system(size: 20, weight: .bold, design: .monospaced))
                    .foregroundColor(primaryColor)
                    .shadow(color: primaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
                    .padding(.leading, -5)
            }

            // 超频奖励
            if editedSession.overclockFactor > 1.0 {
                HStack(spacing: 5) {
                    Image(systemName: "bolt.fill")
                        .font(.system(size: 16))
                        .foregroundColor(accentColor)

                    Text("OVERCLOCK +\(Int((editedSession.overclockFactor - 1.0) * 100))%")
                        .font(.system(size: 16, weight: .bold, design: .monospaced))
                        .foregroundColor(accentColor)
                }
                .shadow(color: accentColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
            }

            // 时间信息
            HStack(spacing: 15) {
                VStack(alignment: .leading, spacing: 5) {
                    Text("DURATION")
                        .font(.system(size: 12, weight: .medium, design: .monospaced))
                        .foregroundColor(secondaryColor)

                    Text(formatDuration(editedSession.duration))
                        .font(.system(size: 18, weight: .bold, design: .monospaced))
                        .foregroundColor(.white)
                }

                Rectangle()
                    .fill(secondaryColor.opacity(0.5))
                    .frame(width: 1, height: 30)

                VStack(alignment: .leading, spacing: 5) {
                    Text("TIME")
                        .font(.system(size: 12, weight: .medium, design: .monospaced))
                        .foregroundColor(secondaryColor)

                    Text("\(formatTimeOnly(editedSession.startTime)) - \(formatTimeOnly(editedSession.endTime))")
                        .font(.system(size: 18, weight: .bold, design: .monospaced))
                        .foregroundColor(.white)
                }
            }
            .padding(15)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(secondaryColor, lineWidth: 1)
                    )
            )
        }
    }

    // 状态选择区域
    private var moodSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("STATUS") // 保留英文，或根据需要本地化
                .font(.system(size: 16, weight: .bold, design: .monospaced))
                .foregroundColor(secondaryColor)
                .shadow(color: secondaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
                .padding(.leading, 5)

            HStack(spacing: 10) {
                // 使用 moodData
                ForEach(moodData, id: \.key) { data in
                    moodButton(moodKey: data.key, icon: data.icon, color: data.color)
                }
            }
        }
    }

    // 分类选择区域
    private var categorySelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("CATEGORY") // 保留英文，或根据需要本地化
                .font(.system(size: 16, weight: .bold, design: .monospaced))
                .foregroundColor(secondaryColor)
                .shadow(color: secondaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
                .padding(.leading, 5)

            HStack(spacing: 10) {
                // 使用 categoryKeys
                ForEach(categoryKeys, id: \.self) { key in
                    categoryButton(categoryKey: key)
                }
            }
        }
    }

    // 备注输入区域
    private var notesInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("NOTES") // 保留英文，或根据需要本地化
                .font(.system(size: 16, weight: .bold, design: .monospaced))
                .foregroundColor(secondaryColor)
                .shadow(color: secondaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
                .padding(.leading, 5)

            TextField("Add notes...", text: $notesText) // 保留英文 placeholder，或根据需要本地化
                .font(.system(size: 16, design: .monospaced))
                .foregroundColor(.white)
                .padding(15)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color(red: 0.15, green: 0.15, blue: 0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(secondaryColor, lineWidth: 1)
                        )
                )
                .focused($focusedField, equals: .notes)
                .onChange(of: notesText) { _, _ in
                    editedSession.notes = notesText.isEmpty ? nil : notesText
                }
        }
    }

    // 底部按钮区域
    private var bottomButtonSection: some View {
        HStack {
            // 取消按钮
            Button {
                dismissWithAnimation()
            } label: {
                Text("CANCEL")
                    .font(.system(size: 16, weight: .bold, design: .monospaced))
                    .foregroundColor(primaryColor)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(primaryColor, lineWidth: 1)
                    )
                    .shadow(color: primaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
            }
            .buttonStyle(ScaleButtonStyle())

            Spacer()

            // 保存按钮
            Button {
                withAnimation {
                    savingInProgress = true
                    showPowerUp = true
                }

                // 延迟保存，先显示能量提升动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    saveRecord()
                }
            } label: {
                Group {
                    if savingInProgress {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .black))
                            .scaleEffect(1.2)
                    } else {
                        Text("SAVE")
                            .font(.system(size: 16, weight: .bold, design: .monospaced))
                            .foregroundColor(.black)
                    }
                }
                .frame(width: 100)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(secondaryColor)
                )
                .shadow(color: secondaryColor.opacity(glowIntensity), radius: 5, x: 0, y: 0)
            }
            .buttonStyle(ScaleButtonStyle())
            .disabled(savingInProgress)
        }
    }

    // 能量线
    private var energyLines: some View {
        ForEach(0..<20, id: \.self) { i in
            EnergyLine(index: i, primaryColor: primaryColor, secondaryColor: secondaryColor)
        }
    }

    // 单个能量线
    private struct EnergyLine: View {
        let index: Int
        let primaryColor: Color
        let secondaryColor: Color

        var body: some View {
            let angle = Double(index) * 18
            let length = CGFloat.random(in: 100...200)

            Path { path in
                let centerX = UIScreen.main.bounds.width / 2
                let centerY = UIScreen.main.bounds.height / 2

                path.move(to: CGPoint(x: centerX, y: centerY))
                path.addLine(to: CGPoint(
                    x: centerX + cos(angle * .pi / 180) * length,
                    y: centerY + sin(angle * .pi / 180) * length
                ))
            }
            .stroke(
                LinearGradient(
                    colors: [primaryColor, secondaryColor],
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                lineWidth: 2
            )
            .shadow(color: primaryColor, radius: 5, x: 0, y: 0)
        }
    }

    // 能量提升动画
    private var powerUpAnimation: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.8)
                .ignoresSafeArea()

            // 能量线
            energyLines

            // 中心圆
            Circle()
                .fill(
                    RadialGradient(
                        colors: [primaryColor, secondaryColor.opacity(0.5), Color.clear],
                        center: .center,
                        startRadius: 0,
                        endRadius: 100
                    )
                )
                .frame(width: 100, height: 100)
                .shadow(color: primaryColor, radius: 20, x: 0, y: 0)

            // XU值
            Text("+\(Int(editedSession.xUnits)) XU")
                .font(.system(size: 36, weight: .bold, design: .monospaced))
                .foregroundColor(.white)
                .shadow(color: primaryColor, radius: 10, x: 0, y: 0)
                .offset(y: 120)
        }
        .transition(.opacity)
    }

    // 心情按钮 - 接受 moodKey
    private func moodButton(moodKey: String, icon: String, color: Color) -> some View {
        let isSelected = selectedMood == moodKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                selectedMood = (selectedMood == moodKey) ? nil : moodKey
                editedSession.mood = selectedMood
            }
            playHapticFeedback()
        } label: {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .black : .white)

                // 显示本地化字符串
                Text(NSLocalizedString(moodKey, comment: ""))
                    .font(.system(size: 12, design: .monospaced))
                    .foregroundColor(isSelected ? .black : .white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 15)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? color : Color(red: 0.15, green: 0.15, blue: 0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(color, lineWidth: 1)
                    )
            )
            .shadow(color: isSelected ? color.opacity(glowIntensity) : color.opacity(0.3), radius: 5, x: 0, y: 0)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 分类按钮 - 接受 categoryKey
    private func categoryButton(categoryKey: String) -> some View {
        let isSelected = selectedCategory == categoryKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                selectedCategory = (selectedCategory == categoryKey) ? nil : categoryKey
                editedSession.category = selectedCategory
            }
            playHapticFeedback()
        } label: {
            HStack(spacing: 8) {
                Image(systemName: iconForCategory(categoryKey))
                    .font(.system(size: 16))

                // 显示本地化字符串
                Text(NSLocalizedString(categoryKey, comment: ""))
                    .font(.system(size: 14, design: .monospaced))
            }
            .foregroundColor(isSelected ? .black : .white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? secondaryColor : Color(red: 0.15, green: 0.15, blue: 0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(secondaryColor, lineWidth: 1)
                    )
            )
            .shadow(color: isSelected ? secondaryColor.opacity(glowIntensity) : secondaryColor.opacity(0.3), radius: 5, x: 0, y: 0)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 辅助函数

    // 根据类别获取图标 - 参数改为 key
    private func iconForCategory(_ categoryKey: String) -> String {
        switch categoryKey {
        case "category_study": return "book.fill"
        case "category_work": return "briefcase.fill"
        case "category_exercise": return "figure.run"
        case "category_other": return "ellipsis.circle.fill"
        default: return "circle"
        }
    }

    // 触感反馈
    private func playHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // 出现动画
    private func animateAppearance() {
        withAnimation(.easeOut(duration: 0.8)) {
            isAppearing = true
        }
    }

    // 开始动画
    private func startAnimations() {
        // 霓虹灯闪烁动画
        withAnimation(Animation.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            glowIntensity = 1.0
        }

        // 扫描线动画
        withAnimation(Animation.linear(duration: 8).repeatForever(autoreverses: false)) {
            scanlineOffset = UIScreen.main.bounds.height
        }
    }

    // 关闭视图
    private func dismissWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isAppearing = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }

    // 保存记录
    private func saveRecord() {
        // 触感反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 创建一个新的 Session 对象来传递，确保数据最新
        var finalSession = session // 从原始 session 复制基础数据

        // 更新为当前 UI 的状态
        finalSession.mood = selectedMood
        finalSession.category = selectedCategory
        finalSession.notes = notesText.isEmpty ? nil : notesText

        // 保留原始的超频信息
        finalSession.overclockFactor = session.overclockFactor
        finalSession.overclockDuration = session.overclockDuration

        // 回调 - 传递新创建的 finalSession
        onSave(finalSession)

        // 关闭视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismissWithAnimation()
        }
    }

    // 仅格式化时间
    private func formatTimeOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // 焦点字段枚举
    enum FocusField: Hashable {
        case notes
    }
}

// MARK: - 预览
struct XMomentRecordNeonCyber_Previews: PreviewProvider {
    static var previews: some View {
        XMomentRecordNeonCyber(
            session: XMomentSession.create(
                duration: 25 * 60,
                category: "category_work", // 使用键名
                mood: "mood_focused", // 使用键名
                notes: NSLocalizedString("preview_notes_project_planning_done", comment: ""),
                overclockFactor: 1.2,
                overclockDuration: 5 * 60
            ),
            onSave: { _ in }
        )
        .environment(\..locale, .init(identifier: "zh-Hans")) // 添加环境设置以在预览中显示中文
    }
}
