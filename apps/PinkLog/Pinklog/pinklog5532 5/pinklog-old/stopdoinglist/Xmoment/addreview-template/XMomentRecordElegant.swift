import SwiftUI
import UIKit
// 引入ButtonStyles以使用ScaleButtonStyle

// 优雅现代风格的专注完成记录视图
struct XMomentRecordElegant: View {
    var session: XMomentSession
    var onSave: (XMomentSession) -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 会话状态
    @State private var editedSession: XMomentSession
    @State private var selectedCategory: String?
    @State private var selectedMood: String?
    @State private var notesText: String = ""

    // 动画状态
    @State private var isAppearing = false
    @State private var savingInProgress = false
    @State private var cardOffset: CGFloat = 20

    // 键盘焦点状态
    @FocusState private var focusedField: FocusField?

    // 颜色定义
    private var backgroundColor: Color {
        colorScheme == .dark ?
            Color(red: 0.1, green: 0.1, blue: 0.15) : // 深色模式下的深蓝色背景
            Color(red: 0.98, green: 0.98, blue: 1.0)  // 浅色模式下的浅灰蓝色背景
    }

    private var cardColor: Color {
        colorScheme == .dark ?
            Color(red: 0.18, green: 0.18, blue: 0.22) : // 深色模式下的深灰色卡片
            Color.white                               // 浅色模式下的白色卡片
    }

    private var accentColor: Color {
        colorScheme == .dark ?
            Color(red: 0.5, green: 0.5, blue: 0.9) : // 深色模式下的亮紫色
            Color(red: 0.4, green: 0.4, blue: 0.8)   // 浅色模式下的优雅紫色
    }

    private var secondaryAccentColor: Color {
        colorScheme == .dark ?
            Color(red: 0.3, green: 0.3, blue: 0.4) : // 深色模式下的深紫色
            Color(red: 0.85, green: 0.85, blue: 0.95) // 浅色模式下的浅紫色
    }

    // 文字颜色
    private var primaryTextColor: Color {
        colorScheme == .dark ? Color.white : Color.black
    }

    private var secondaryTextColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.7) : Color.black.opacity(0.6)
    }

    // 心情选项及其对应颜色 - 使用键名
    private let moodData: [(key: String, icon: String, color: Color)] = [
        ("mood_focused", "brain.head.profile", Color(red: 0.4, green: 0.4, blue: 0.8)), // 紫色
        ("mood_efficient", "bolt.fill", Color(red: 0.3, green: 0.7, blue: 0.4)), // 绿色
        ("mood_tired", "cloud.rain.fill", Color(red: 0.7, green: 0.3, blue: 0.3)), // 红色
        ("mood_distracted", "tornado", Color(red: 0.9, green: 0.6, blue: 0.3)) // 橙色
    ]

    // 分类选项 - 使用键名
    private let categoryKeys = ["category_study", "category_work", "category_exercise", "category_other"]

    // 初始化
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self._editedSession = State(initialValue: session)
        self._selectedCategory = State(initialValue: session.category) // 存储键名
        self._selectedMood = State(initialValue: session.mood) // 存储键名
        self._notesText = State(initialValue: session.notes ?? "")
    }

    var body: some View {
        ZStack {
            // 背景
            backgroundColor.ignoresSafeArea()

            // 主内容
            VStack(spacing: 16) {
                // 顶部卡片 - XU和超频奖励
                topCard

                // 中部卡片 - 状态和分类选择
                mainCard

                // 底部卡片 - 时间信息和保存按钮
                bottomCard
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 30)
            .offset(y: isAppearing ? 0 : 50)
            .opacity(isAppearing ? 1 : 0)
        }
        .dismissKeyboardOnTap(focused: $focusedField)

        .onAppear {
            animateAppearance()
        }
    }

    // 顶部卡片 - XU和超频奖励
    private var topCard: some View {
        VStack(spacing: 0) {
            HStack(alignment: .bottom, spacing: 16) {
                // XU值
                VStack(alignment: .leading, spacing: 4) {
                    Text(NSLocalizedString("label_this_focus_session", comment: ""))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    HStack(alignment: .firstTextBaseline, spacing: 4) {
                        Text("\(Int(editedSession.xUnits))")
                            .font(.system(size: 36, weight: .bold, design: .rounded))
                            .foregroundColor(accentColor)

                        Text("XU")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(accentColor.opacity(0.8))
                    }
                }

                Spacer()

                // 超频奖励
                if editedSession.overclockFactor > 1.0 {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(NSLocalizedString("label_overclock_bonus", comment: ""))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(secondaryTextColor)

                        HStack(alignment: .firstTextBaseline, spacing: 2) {
                            Image(systemName: "bolt.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.orange)

                            Text("+\(Int((editedSession.overclockFactor - 1.0) * 100))%")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.orange)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(cardColor)
                .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        )
        .offset(y: isAppearing ? 0 : cardOffset)
        .animation(Animation.spring(response: 0.5, dampingFraction: 0.8).delay(0.1), value: isAppearing)
    }

    // 中部卡片 - 状态和分类选择
    private var mainCard: some View {
        VStack(spacing: 20) {
            // 状态选择区域
            VStack(alignment: .leading, spacing: 12) {
                Text(NSLocalizedString("label_status", comment: ""))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(primaryTextColor)
                    .padding(.leading, 4)

                // 状态选择按钮组
                HStack(spacing: 10) {
                    ForEach(moodData, id: \.key) { data in
                        moodButton(moodKey: data.key, icon: data.icon, color: data.color)
                    }
                }
            }

            // 分类选择区域
            VStack(alignment: .leading, spacing: 12) {
                Text(NSLocalizedString("label_category", comment: ""))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(primaryTextColor)
                    .padding(.leading, 4)

                // 分类选择按钮组
                HStack(spacing: 10) {
                    ForEach(categoryKeys, id: \.self) { key in
                        categoryButton(categoryKey: key)
                    }
                }
            }

            // 备注输入区域
            VStack(alignment: .leading, spacing: 12) {
                Text(NSLocalizedString("label_notes", comment: ""))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(primaryTextColor)
                    .padding(.leading, 4)

                // 备注输入框
                TextField(NSLocalizedString("placeholder_add_short_notes", comment: ""), text: $notesText)
                    .font(.system(size: 15))
                    .foregroundColor(primaryTextColor)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(secondaryAccentColor.opacity(colorScheme == .dark ? 0.2 : 0.3))
                    )
                    .focused($focusedField, equals: .notes)
                    .onChange(of: notesText) { _, _ in
                        editedSession.notes = notesText.isEmpty ? nil : notesText
                    }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(cardColor)
                .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.05), radius: 10, x: 0, y: 5)
        )
        .offset(y: isAppearing ? 0 : cardOffset)
        .animation(Animation.spring(response: 0.5, dampingFraction: 0.8).delay(0.2), value: isAppearing)
    }

    // 底部卡片 - 时间信息和保存按钮
    private var bottomCard: some View {
        VStack(spacing: 16) {
            // 时间信息
            HStack(alignment: .bottom) {
                // 开始-结束时间
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(formatTimeOnly(editedSession.startTime)) - \(formatTimeOnly(editedSession.endTime))")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Text(formatDuration(editedSession.duration))
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(primaryTextColor)
                }

                Spacer()

                // 保存按钮
                Button {
                    saveRecord()
                } label: {
                    Text(NSLocalizedString("button_save", comment: ""))
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(accentColor)
                                .shadow(color: accentColor.opacity(colorScheme == .dark ? 0.5 : 0.3), radius: 5, x: 0, y: 2)
                        )
                }
                .buttonStyle(ScaleButtonStyle())
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(cardColor)
                .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        )
        .offset(y: isAppearing ? 0 : cardOffset)
        .animation(Animation.spring(response: 0.5, dampingFraction: 0.8).delay(0.3), value: isAppearing)
    }

    // 心情按钮 - 接受 moodKey
    private func moodButton(moodKey: String, icon: String, color: Color) -> some View {
        let isSelected = selectedMood == moodKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedMood == moodKey {
                    selectedMood = nil
                } else {
                    selectedMood = moodKey
                }
                editedSession.mood = selectedMood
            }
            playHapticFeedback()
        } label: {
            VStack(spacing: 8) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? color : secondaryAccentColor.opacity(colorScheme == .dark ? 0.2 : 0.3))
                        .frame(height: 50)
                        .shadow(color: isSelected ? color.opacity(colorScheme == .dark ? 0.5 : 0.3) : Color.clear, radius: 5, x: 0, y: 2)

                    HStack(spacing: 8) {
                        Image(systemName: icon)
                            .font(.system(size: 16))
                            .foregroundColor(isSelected ? .white : color)

                        Text(NSLocalizedString(moodKey, comment: ""))
                            .font(.system(size: 15, weight: isSelected ? .semibold : .medium))
                            .foregroundColor(isSelected ? .white : primaryTextColor)
                    }
                    .padding(.horizontal, 12)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 类别按钮 - 接受 categoryKey
    private func categoryButton(categoryKey: String) -> some View {
        let isSelected = selectedCategory == categoryKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedCategory == categoryKey {
                    selectedCategory = nil
                } else {
                    selectedCategory = categoryKey
                }
                editedSession.category = selectedCategory
            }
            playHapticFeedback()
        } label: {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? accentColor : secondaryAccentColor.opacity(colorScheme == .dark ? 0.2 : 0.3))
                    .frame(height: 50)
                    .shadow(color: isSelected ? accentColor.opacity(colorScheme == .dark ? 0.5 : 0.3) : Color.clear, radius: 5, x: 0, y: 2)

                HStack(spacing: 8) {
                    Image(systemName: iconForCategory(categoryKey))
                        .font(.system(size: 16))
                        .foregroundColor(isSelected ? .white : accentColor)

                    Text(NSLocalizedString(categoryKey, comment: ""))
                        .font(.system(size: 15, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .white : primaryTextColor)
                }
                .padding(.horizontal, 12)
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 辅助函数

    // 根据类别获取图标 - 参数改为 key
    private func iconForCategory(_ categoryKey: String) -> String {
        switch categoryKey {
        case "category_study": return "book.fill"
        case "category_work": return "briefcase.fill"
        case "category_exercise": return "figure.run"
        case "category_other": return "ellipsis.circle.fill"
        default: return "circle"
        }
    }

    // 触感反馈
    private func playHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // 出现动画
    private func animateAppearance() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            isAppearing = true
        }
    }

    // 关闭视图
    private func dismissWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isAppearing = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }

    // 保存记录
    private func saveRecord() {
        // 触感反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 创建一个新的 Session 对象来传递，确保数据最新
        var finalSession = session // 从原始 session 复制基础数据

        // 更新为当前 UI 的状态
        finalSession.mood = selectedMood
        finalSession.category = selectedCategory
        finalSession.notes = notesText.isEmpty ? nil : notesText

        // 保留原始的超频信息
        finalSession.overclockFactor = session.overclockFactor
        finalSession.overclockDuration = session.overclockDuration

        // 回调 - 传递新创建的 finalSession
        onSave(finalSession)

        // 关闭视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismissWithAnimation()
        }
    }

    // 仅格式化时间
    private func formatTimeOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    // 格式化时长 - 使用本地化格式字符串
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: NSLocalizedString("duration_format_elegant", comment: ""), minutes, seconds)
    }

    // 焦点字段枚举
    enum FocusField: Hashable {
        case notes
    }
}

// MARK: - 预览
struct XMomentRecordElegant_Previews: PreviewProvider {
    static var previews: some View {
        XMomentRecordElegant(
            session: XMomentSession.create(
                duration: 25 * 60,
                category: "category_work", // 使用键名
                mood: "mood_focused", // 使用键名
                notes: NSLocalizedString("preview_notes_project_planning_done", comment: ""),
                overclockFactor: 1.2,
                overclockDuration: 5 * 60
            ),
            onSave: { _ in }
        )
        .environment(\..locale, .init(identifier: "zh-Hans")) // 添加环境设置以在预览中显示中文
    }
}
