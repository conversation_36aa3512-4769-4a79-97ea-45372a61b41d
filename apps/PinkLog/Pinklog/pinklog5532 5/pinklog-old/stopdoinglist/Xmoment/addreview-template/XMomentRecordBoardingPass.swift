import SwiftUI
import UIKit

// 登机牌风格的专注完成记录视图
struct XMomentRecordBoardingPass: View {
    var session: XMomentSession
    var onSave: (XMomentSession) -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 会话状态
    @State private var editedSession: XMomentSession
    @State private var selectedCategory: String?
    @State private var selectedMood: String?
    @State private var notesText: String = ""

    // 动画状态
    @State private var isAppearing = false
    @State private var savingInProgress = false
    @State private var showCheckmarks = false

    // 随机生成的航班号和座位号
    @State private var flightNumber: String
    @State private var seatNumber: String

    // 键盘焦点状态
    @FocusState private var focusedField: FocusField?

    // 颜色定义 - 使用与计时模板相同的黄色
    private var mainColor: Color {
        colorScheme == .dark ?
            Color(red: 1.0, green: 0.9, blue: 0.0) : // 深色模式下的亮黄色
            Color(red: 1.0, green: 0.95, blue: 0.0)  // 浅色模式下的亮黄色
    }

    private var accentColor: Color {
        colorScheme == .dark ?
            Color(red: 1.0, green: 0.8, blue: 0.0) : // 深色模式下的金黄色
            Color(red: 1.0, green: 0.9, blue: 0.0)   // 浅色模式下的金黄色
    }

    private var cardColor: Color {
        colorScheme == .dark ?
            Color(red: 0.15, green: 0.15, blue: 0.15) : // 深色模式下的深灰色
            Color.white                               // 浅色模式下的白色
    }

    private var backgroundColor: Color {
        colorScheme == .dark ?
            Color(red: 0.2, green: 0.2, blue: 0.1) : // 深色模式下的深色背景，带有黄色色调
            Color(red: 0.98, green: 0.98, blue: 0.9)   // 浅色模式下的浅色背景，带有黄色色调
    }

    // 文字颜色
    private var primaryTextColor: Color {
        colorScheme == .dark ? Color.white : Color.black
    }

    private var secondaryTextColor: Color {
        colorScheme == .dark ? Color.white.opacity(0.7) : Color.black.opacity(0.6)
    }

    // 心情选项 - 使用键名
    private let moodData: [(key: String, icon: String)] = [
        ("mood_focused", "brain.head.profile"),
        ("mood_efficient", "bolt.fill"),
        ("mood_tired", "cloud.rain.fill"),
        ("mood_distracted", "tornado")
    ]

    // 分类选项 - 使用键名
    private let categoryKeys = ["category_study", "category_work", "category_exercise", "category_other"]

    // 初始化
    init(session: XMomentSession, onSave: @escaping (XMomentSession) -> Void) {
        self.session = session
        self.onSave = onSave
        self._editedSession = State(initialValue: session)
        self._selectedCategory = State(initialValue: session.category) // 存储键名
        self._selectedMood = State(initialValue: session.mood) // 存储键名
        self._notesText = State(initialValue: session.notes ?? "")

        // 生成随机航班号和座位号
        let airlines = ["XM", "FO", "PM", "TM"]
        let randomAirline = airlines.randomElement() ?? "XM"
        let randomNumber = Int.random(in: 1000...9999)
        self._flightNumber = State(initialValue: "\(randomAirline)\(randomNumber)")

        let rows = ["A", "B", "C", "D"]
        let randomRow = rows.randomElement() ?? "A"
        let randomSeat = Int.random(in: 1...30)
        self._seatNumber = State(initialValue: "\(randomSeat)\(randomRow)")
    }

    var body: some View {
        ZStack {
            // 背景
            backgroundColor.ignoresSafeArea()

            // 主卡片 - 登机牌设计
            VStack(spacing: 0) {
                // 登机牌顶部
                boardingPassHeader

                // 登机牌中部 - 主要信息
                boardingPassBody

                // 登机牌底部 - 虚线分隔和条形码
                boardingPassFooter
            }
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(cardColor)
                    .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(mainColor.opacity(0.3), lineWidth: 1)
            )
            .padding(.horizontal, 20)
            .padding(.vertical, 40)
            .scaleEffect(isAppearing ? 1 : 0.9)
            .opacity(isAppearing ? 1 : 0)
        }
        .dismissKeyboardOnTap(focused: $focusedField) // 应用修饰符

        .onAppear {
            animateAppearance()

            // 延迟显示勾选标记
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    showCheckmarks = true
                }
            }
        }
    }

    // 登机牌顶部
    private var boardingPassHeader: some View {
        VStack(spacing: 0) {
            HStack {
                // 航空公司标志和名称
                HStack(spacing: 8) {
                    Image(systemName: "airplane")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(mainColor)

                    Text("XMOMENT AIRLINES")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(mainColor)
                }

                Spacer()

                // 航班号
                Text("FLIGHT \(flightNumber)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(secondaryTextColor)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 15)

            // 分隔线
            Rectangle()
                .fill(mainColor.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 10)
        }
    }

    // 登机牌中部 - 主要信息
    private var boardingPassBody: some View {
        VStack(spacing: 0) {
            // 出发和到达信息
            HStack(alignment: .top, spacing: 0) {
                // 出发信息
                VStack(alignment: .leading, spacing: 4) {
                    Text("FROM")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Text("FOCUS")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(primaryTextColor)

                    Text(formatTimeOnly(editedSession.startTime))
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(primaryTextColor)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 中间飞机图标和虚线
                VStack(spacing: 5) {
                    Image(systemName: "airplane")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(mainColor)
                        .rotationEffect(.degrees(90))

                    // 虚线路径
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: 0))
                        path.addLine(to: CGPoint(x: 0, y: 50))
                    }
                    .stroke(style: StrokeStyle(lineWidth: 1.5, dash: [3]))
                    .foregroundColor(mainColor.opacity(0.8))
                }
                .padding(.horizontal, 20)

                // 到达信息
                VStack(alignment: .trailing, spacing: 4) {
                    Text("TO")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Text("COMPLETE")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(primaryTextColor)

                    Text(formatTimeOnly(editedSession.endTime))
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(primaryTextColor)
                }
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)

            // 分隔线
            Rectangle()
                .fill(mainColor.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 10)

            // 专注时间和奖励信息
            HStack(alignment: .center) {
                // 专注时间
                VStack(alignment: .leading, spacing: 4) {
                    Text("DURATION")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Text(formatDuration(editedSession.duration))
                        .font(.system(size: 24, weight: .bold, design: .monospaced))
                        .foregroundColor(primaryTextColor)
                }

                Spacer()

                // XU奖励
                VStack(alignment: .trailing, spacing: 4) {
                    Text("XU EARNED")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Text("\(Int(editedSession.xUnits))")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(primaryTextColor)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)

            // 超频奖励（如果有）
            if editedSession.overclockFactor > 1.0 {
                Rectangle()
                    .fill(mainColor.opacity(0.2))
                    .frame(height: 1)
                    .padding(.horizontal, 10)

                HStack {
                    Text("OVERCLOCK BONUS")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    Spacer()

                    Text("+\(Int((editedSession.overclockFactor - 1.0) * 100) / 50)")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(accentColor)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
            }

            // 分隔线
            Rectangle()
                .fill(mainColor.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 10)

            // 座位和登机口信息（类别和心情）
            HStack(alignment: .top) {
                // 座位信息（类别）
                VStack(alignment: .leading, spacing: 10) {
                    Text("CATEGORY") // 保留英文或本地化
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    categorySelectionArea
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 登机口信息（心情）
                VStack(alignment: .trailing, spacing: 10) {
                    Text("MOOD") // 保留英文或本地化
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(secondaryTextColor)

                    moodSelectionArea
                }
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)

            // 分隔线
            Rectangle()
                .fill(mainColor.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 10)

            // 备注区域
            VStack(alignment: .leading, spacing: 10) {
                Text("NOTES") // 保留英文或本地化
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(secondaryTextColor)

                TextField("Add a short note...", text: $notesText) // 保留英文或本地化
                    .font(.system(size: 15))
                    .foregroundColor(primaryTextColor)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
                    )
                    .focused($focusedField, equals: .notes) // 添加焦点绑定
                    .onChange(of: notesText) { _, _ in
                        editedSession.notes = notesText
                    }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)
        }
    }

    // 登机牌底部 - 虚线分隔和条形码
    private var boardingPassFooter: some View {
        VStack(spacing: 0) {
            // 虚线分隔
            HStack(spacing: 0) {
                ForEach(0..<30, id: \.self) { _ in
                    Rectangle()
                        .fill(mainColor.opacity(0.5))
                        .frame(width: 6, height: 2)
                        .padding(.horizontal, 2)
                }
            }
            .padding(.vertical, 10)

            // 条形码和保存按钮
            VStack(spacing: 15) {
                // 模拟条形码
                HStack(spacing: 1) {
                    ForEach(0..<40, id: \.self) { index in
                        Rectangle()
                            .fill(primaryTextColor.opacity(Double.random(in: 0.3...1.0)))
                            .frame(width: Double.random(in: 1...3), height: 40)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 40)

                // 座位号和日期
                HStack {
                    Text("SEAT \(seatNumber)")
                        .font(.system(size: 14, weight: .bold, design: .monospaced))
                        .foregroundColor(primaryTextColor)

                    Spacer()

                    Text(formatDateShort(editedSession.startTime))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(secondaryTextColor)
                }
                .padding(.horizontal, 20)

                // 保存按钮
                Button {
                    saveRecord()
                } label: {
                    Text("SAVE")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(colorScheme == .dark ? .black : .black)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(mainColor)
                                .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 2)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
    }

    // 心情选择区域
    private var moodSelectionArea: some View {
        HStack(spacing: 8) {
            // 使用 moodData
            ForEach(moodData, id: \.key) { data in
                moodButton(moodKey: data.key, icon: data.icon)
            }
        }
    }

    // 心情按钮 - 接受 moodKey
    private func moodButton(moodKey: String, icon: String) -> some View {
        let isSelected = selectedMood == moodKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedMood == moodKey {
                    selectedMood = nil
                } else {
                    selectedMood = moodKey
                }
                editedSession.mood = selectedMood
            }
            playHapticFeedback()
        } label: {
            ZStack {
                Circle()
                    .fill(isSelected ? mainColor : colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
                    .frame(width: 36, height: 36)

                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? (colorScheme == .dark ? Color.black : Color.black) : primaryTextColor)
            }
            // Tooltip or accessibility label should use NSLocalizedString(moodKey, comment: "")
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 类别选择区域
    private var categorySelectionArea: some View {
        HStack(spacing: 8) {
            // 使用 categoryKeys
            ForEach(categoryKeys, id: \.self) { key in
                categoryButton(categoryKey: key)
            }
        }
    }

    // 类别按钮 - 接受 categoryKey
    private func categoryButton(categoryKey: String) -> some View {
        let isSelected = selectedCategory == categoryKey

        return Button {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                if selectedCategory == categoryKey {
                    selectedCategory = nil
                } else {
                    selectedCategory = categoryKey
                }
                editedSession.category = selectedCategory
            }
            playHapticFeedback()
        } label: {
            ZStack {
                Circle()
                    .fill(isSelected ? mainColor : colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
                    .frame(width: 36, height: 36)

                Image(systemName: iconForCategory(categoryKey))
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? (colorScheme == .dark ? Color.black : Color.black) : primaryTextColor)
            }
             // Tooltip or accessibility label should use NSLocalizedString(categoryKey, comment: "")
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 辅助函数

    // 根据类别获取图标 - 参数改为 key
    private func iconForCategory(_ categoryKey: String) -> String {
        switch categoryKey {
        case "category_study": return "book.fill"
        case "category_work": return "briefcase.fill"
        case "category_exercise": return "figure.run"
        case "category_other": return "ellipsis.circle.fill"
        default: return "circle"
        }
    }

    // 触感反馈
    private func playHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // 出现动画
    private func animateAppearance() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            isAppearing = true
        }
    }

    // 关闭视图
    private func dismissWithAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isAppearing = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }

    // 保存记录
    private func saveRecord() {
        // 触感反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 创建一个新的 Session 对象来传递，确保数据最新
        var finalSession = session // 从原始 session 复制基础数据

        // 更新为当前 UI 的状态
        finalSession.mood = selectedMood
        finalSession.category = selectedCategory
        finalSession.notes = notesText.isEmpty ? nil : notesText

        // 保留原始的超频信息
        finalSession.overclockFactor = session.overclockFactor
        finalSession.overclockDuration = session.overclockDuration

        // 回调 - 传递新创建的 finalSession
        onSave(finalSession)

        // 关闭视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismissWithAnimation()
        }
    }

    // 格式化日期（短格式）
    private func formatDateShort(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd MMM yyyy"
        return formatter.string(from: date)
    }

    // 仅格式化时间
    private func formatTimeOnly(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }

    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    // 焦点字段枚举
    enum FocusField: Hashable {
        case notes
    }
}

// MARK: - 预览
struct XMomentRecordBoardingPass_Previews: PreviewProvider {
    static var previews: some View {
        XMomentRecordBoardingPass(
            session: XMomentSession.create(
                duration: 25 * 60,
                category: "category_work", // 使用键名
                mood: "mood_focused", // 使用键名
                notes: NSLocalizedString("preview_notes_project_planning_done", comment: ""),
                overclockFactor: 1.2,
                overclockDuration: 5 * 60
            ),
            onSave: { _ in }
        )
        .environment(\..locale, .init(identifier: "zh-Hans")) // 添加环境设置以在预览中显示中文
    }
}
