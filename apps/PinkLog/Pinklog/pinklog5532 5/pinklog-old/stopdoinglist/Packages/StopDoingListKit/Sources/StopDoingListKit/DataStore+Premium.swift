import Foundation
import SwiftUI

// DataStore的扩展，用于处理高级功能检查
extension DataStore {
    // MARK: - 高级功能检查
    
    // 检查是否可以添加更多习惯
    public func canAddMoreHabits() -> Bool {
        // 如果是高级用户，可以无限添加
        if StoreManager.shared.isPremium {
            return true
        }
        
        // 免费用户限制3个习惯
        return habits.count < 1
    }
    
    // 检查是否可以使用iCloud备份功能
    public func canUseCloudBackup() -> Bool {
        // 只有高级用户可以使用iCloud备份
        return StoreManager.shared.isPremium
    }
    
    // 检查是否可以使用XMoment
    public func canUseXMoment() -> Bool {
        // 如果是高级用户，可以无限使用
        if StoreManager.shared.isPremium {
            return true
        }
        
        // 检查今天的使用次数
        let dailyUses = StoreManager.shared.getXMomentDailyUses()
        
        // 免费用户每天限制3次
        return dailyUses < 3
    }
    
    // 记录XMoment使用
    public func recordXMomentUse() {
        StoreManager.shared.recordXMomentUse()
    }
}
