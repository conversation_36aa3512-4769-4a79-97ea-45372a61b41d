import Foundation
import UIKit
import SwiftUI

/// 集中管理图片操作的类，处理所有图片的保存、加载和删除
public class ImageManager: ObservableObject {
    public static let shared = ImageManager()
    
    // 诊断信息
    @Published public var lastOperationFailed = false
    @Published public var lastErrorMessage = ""
    @Published public var debugMessages: [String] = []
    
    // 图片缓存
    private let imageCache = NSCache<NSString, UIImage>()
    private let fileManager = FileManager.default
    
    private init() {
        // 设置缓存限制
        imageCache.countLimit = 100
        imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB
        
        addDebugMessage("ImageManager 初始化完成")
        
        // 确保图片目录存在
        do {
            try fileManager.createDirectoryIfNeeded(at: FileManager.getImagesDirectory())
        } catch {
            addDebugMessage("创建图片目录失败: \(error)")
        }
    }
    
    deinit {
        addDebugMessage("ImageManager 已释放")
    }
    
    // MARK: - 公共方法
    
    /// 保存图片到文件系统
    /// - Parameters:
    ///   - image: 要保存的UIImage
    ///   - identifier: 可选的标识符（如果提供将用于文件名）
    ///   - compressionQuality: JPEG压缩质量，默认0.7
    /// - Returns: 保存的文件名，失败时返回nil
    public func saveImage(_ image: UIImage, identifier: String? = nil, compressionQuality: CGFloat = 0.7) -> String? {
        resetErrors()
        
        // 1. 转换图片为JPEG数据
        guard let imageData = image.jpegData(compressionQuality: compressionQuality) else {
            reportError("无法从图片生成JPEG数据")
            return nil
        }
        
        // 2. 创建文件名
        let fileName: String
        if let safeIdentifier = identifier?.replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: ":", with: "_")
            .replacingOccurrences(of: " ", with: "_") {
            fileName = "image_\(safeIdentifier).jpg"
        } else {
            fileName = "image_\(UUID().uuidString).jpg"
        }
        
        addDebugMessage("准备保存图片: \(fileName)")
        
        do {
            // 3. 获取存储目录
            let storageURL = FileManager.getImagesDirectory()
            
            // 4. 创建文件 URL
            let fileURL = storageURL.appendingPathComponent(fileName)
            addDebugMessage("文件路径: \(fileURL.path)")
            
            // 5. 写入文件
            try fileManager.safeWriteData(imageData, to: fileURL)
            
            // 6. 添加到缓存
            imageCache.setObject(image, forKey: fileName as NSString)
            
            addDebugMessage("图片保存成功: \(fileName)")
            return fileName
        } catch {
            reportError("保存图片失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 从文件系统加载图片
    /// - Parameter fileName: 文件名或文件URL字符串
    /// - Returns: 加载的UIImage，失败时返回nil
    public func loadImage(from fileName: String) -> UIImage? {
        resetErrors()
        addDebugMessage("尝试加载图片: \(fileName)")
        
        // 1. 检查缓存
        if let cachedImage = imageCache.object(forKey: fileName as NSString) {
            addDebugMessage("从缓存加载图片成功")
            return cachedImage
        }
        
        // 2. 清理输入字符串
        let cleanFileName = fileName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 3. 从文件名或URL中提取文件名
        let extractedFileName: String
        if cleanFileName.hasPrefix("file://") {
            extractedFileName = URL(string: cleanFileName)?.lastPathComponent ?? cleanFileName
        } else {
            extractedFileName = cleanFileName.components(separatedBy: "/").last ?? cleanFileName
        }
        
        addDebugMessage("提取的文件名: \(extractedFileName)")
        
        do {
            // 4. 获取存储目录
            let imagesURL = FileManager.getImagesDirectory()
            let fileURL = imagesURL.appendingPathComponent(extractedFileName)
            addDebugMessage("查找文件路径: \(fileURL.path)")
            
            // 5. 尝试读取文件
            let data = try fileManager.safeReadData(from: fileURL)
            if let image = UIImage(data: data) {
                // 添加到缓存
                imageCache.setObject(image, forKey: extractedFileName as NSString)
                addDebugMessage("图片加载成功")
                return image
            } else {
                reportError("无法从数据创建图片")
                return nil
            }
        } catch {
            reportError("读取文件数据失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 删除图片文件
    /// - Parameter fileName: 文件名
    /// - Returns: 是否成功删除
    public func deleteImage(_ fileName: String) -> Bool {
        resetErrors()
        addDebugMessage("尝试删除图片: \(fileName)")
        
        // 从缓存中移除
        imageCache.removeObject(forKey: fileName as NSString)
        
        // 清理输入字符串
        let cleanFileName = fileName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 从文件名或URL中提取文件名
        let extractedFileName: String
        if cleanFileName.hasPrefix("file://") {
            extractedFileName = URL(string: cleanFileName)?.lastPathComponent ?? cleanFileName
        } else {
            extractedFileName = cleanFileName.components(separatedBy: "/").last ?? cleanFileName
        }
        
        do {
            // 获取文件URL
            let imagesURL = FileManager.getImagesDirectory()
            let fileURL = imagesURL.appendingPathComponent(extractedFileName)
            
            try fileManager.safeRemoveFile(at: fileURL)
            addDebugMessage("图片删除成功")
            return true
        } catch {
            reportError("删除图片失败: \(error.localizedDescription)")
            return false
        }
    }
    
    /// 清除图片缓存
    public func clearCache() {
        imageCache.removeAllObjects()
        addDebugMessage("图片缓存已清除")
    }
    
    // MARK: - 私有辅助方法
    
    private func resetErrors() {
        lastOperationFailed = false
        lastErrorMessage = ""
    }
    
    private func reportError(_ message: String) {
        lastOperationFailed = true
        lastErrorMessage = message
        addDebugMessage("错误: \(message)")
    }
    
    private func addDebugMessage(_ message: String) {
        debugMessages.append("[\(Date())] \(message)")
        if debugMessages.count > 100 {
            debugMessages.removeFirst(debugMessages.count - 100)
        }
    }
}

// MARK: - SwiftUI扩展
public extension Image {
    /// 从文件名加载图片
    /// - Parameter fileName: 图片文件名
    /// - Returns: Image视图
    static func fromFileName(_ fileName: String) -> Image {
        if let uiImage = ImageManager.shared.loadImage(from: fileName) {
            return Image(uiImage: uiImage)
        } else {
            return Image(systemName: "photo")
        }
    }
}

// MARK: - UIImage扩展
public extension UIImage {
    /// 保存图片并返回文件名
    /// - Parameters:
    ///   - identifier: 可选标识符
    ///   - quality: 压缩质量
    /// - Returns: 保存的文件名
    func saveToFile(identifier: String? = nil, quality: CGFloat = 0.7) -> String? {
        return ImageManager.shared.saveImage(self, identifier: identifier, compressionQuality: quality)
    }
} 