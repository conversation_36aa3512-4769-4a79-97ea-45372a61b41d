import SwiftUI

public struct WaveformView: View {
    let samples: [CGFloat]
    let color: Color
    
    public init(samples: [CGFloat], color: Color) {
        self.samples = samples
        self.color = color
    }
    
    public var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 2) {
                ForEach(samples.indices, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(color)
                        .frame(width: 3, height: max(samples[index] * geometry.size.height, 3))
                }
            }
            .frame(maxHeight: .infinity)
            .animation(.easeInOut(duration: 0.1), value: samples)
        }
    }
} 