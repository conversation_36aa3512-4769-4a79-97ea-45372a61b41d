//
//  Item.swift
//  stopdoinglist
//
//  Created by thr33 on 2025/3/6.
//

import Foundation
import SwiftUI

// 确保Double和String类型符合NSSecureCoding协议
extension Double: @unchecked Sendable {}
extension Array: @unchecked Sendable where Element: Sendable {}

public final class Habit: Identifiable, Codable, Equatable {
    public var id: UUID = UUID()
    public var name: String
    public var description: String?
    public var createdAt: Date = Date()
    public var lastReset: Date?
    public var resetIds: [UUID] = []
    public var triggerIds: [UUID] = []
    public var reviewIds: [UUID] = []
    public var tags: [String] = []
    public var goal: Int = 1
    public var goalUnit: String = "天"
    public var currentStreak: Int = 0
    public var bestStreak: Int = 0
    public var totalResets: Int = 0
    public var lastChecked: Date = Date()
    public var notes: String?
    public var isArchived: Bool = false
    public var color: String = "blue"
    public var icon: String = "🚫"
    public var notificationEnabled: Bool = false
    public var notificationTime: Date?
    public var notificationDays: [Int] = []
    public var notificationMessage: String?
    
    public var currentStreakDays: Int {
        guard let lastReset = lastReset else {
            return Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        }
        return Calendar.current.dateComponents([.day], from: lastReset, to: Date()).day ?? 0
    }
    
    public init(id: UUID = UUID(), name: String) {
        self.id = id
        self.name = name
    }
    
    // Equatable 协议实现
    public static func == (lhs: Habit, rhs: Habit) -> Bool {
        return lhs.id == rhs.id
    }
}
