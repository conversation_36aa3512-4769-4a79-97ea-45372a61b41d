// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "StopDoingListKit",
    platforms: [
        .iOS(.v17),
        .macOS(.v14)
    ],
    products: [
        .library(
            name: "StopDoingListKit",
            targets: ["StopDoingListKit"]),
    ],
    targets: [
        .target(
            name: "StopDoingListKit",
            dependencies: []),
        .testTarget(
            name: "StopDoingListKitTests",
            dependencies: ["StopDoingListKit"]),
    ]
) 