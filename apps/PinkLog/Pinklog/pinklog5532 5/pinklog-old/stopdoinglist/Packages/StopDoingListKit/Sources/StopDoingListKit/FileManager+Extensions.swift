import Foundation

public extension FileManager {
    static func getAudioDirectory() -> URL {
        let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
        return paths[0].appendingPathComponent("Audios")
    }
    
    static func getImagesDirectory() -> URL {
        let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
        return paths[0].appendingPathComponent("Images")
    }
    
    static func directorySize(_ url: URL) -> Int64 {
        let fileManager = FileManager.default
        guard let enumerator = fileManager.enumerator(at: url, includingPropertiesForKeys: [.fileSizeKey], options: [], errorHandler: nil) else {
            return 0
        }
        
        var size: Int64 = 0
        for case let fileURL as URL in enumerator {
            do {
                let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                if let fileSize = attributes[.size] as? Int64 {
                    size += fileSize
                }
            } catch {
                print("Error getting size of \(fileURL): \(error)")
            }
        }
        return size
    }
    
    func createDirectoryIfNeeded(at url: URL) throws {
        guard !fileExists(atPath: url.path) else { return }
        try createDirectory(at: url, withIntermediateDirectories: true)
    }
    
    func safeWriteData(_ data: Data, to url: URL) throws {
        let tempURL = url.appendingPathExtension("temp")
        try data.write(to: tempURL)
        if fileExists(atPath: url.path) {
            try removeItem(at: url)
        }
        try moveItem(at: tempURL, to: url)
    }
    
    func safeReadData(from url: URL) throws -> Data {
        return try Data(contentsOf: url)
    }
    
    func safeRemoveFile(at url: URL) throws {
        guard fileExists(atPath: url.path) else { return }
        try removeItem(at: url)
    }
} 