import Foundation

public final class Trigger: Identifiable, Codable {
    public var id: UUID = UUID()
    public var triggerType: TriggerType
    public var triggerDesc: String
    public var frequency: Int = 1
    public var lastOccurred: Date?
    public var notes: String?
    public var mood: MoodType?
    public var location: String?
    public var alternativeBehaviors: [String] = []
    public var audioRecordingURL: String?
    public var audioDuration: Double?
    public var audioWaveformData: [Double]?
    public var images: [String] = []
    public var tags: [String] = []
    public var habitId: UUID?
    
    // Support for enhanced categorization system
    public var mainCategory: TriggerMainCategory?
    public var subCategory: TriggerSubCategory?
    public var specificOptions: [String] = []
    
    public enum TriggerType: String, Codable, CaseIterable {
        case emotion = "emotion"
        case environment = "environment"
        case time = "time"
        case social = "social"
        case physical = "physical"
        
        public var localizedString: String {
            return NSLocalizedString(self.rawValue, comment: "Trigger type")
        }
        
        // Backward compatibility initialization
        public init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            let rawValue = try container.decode(String.self)
            
            // Handle legacy Chinese rawValue
            switch rawValue {
            case "情绪": self = .emotion
            case "环境": self = .environment
            case "时间": self = .time
            case "社交": self = .social
            case "生理": self = .physical
            default:
                if let type = TriggerType(rawValue: rawValue) {
                    self = type
                } else {
                    self = .emotion // Default value
                }
            }
        }
    }
    
    public enum MoodType: String, Codable, CaseIterable {
        case great = "great"
        case good = "good"
        case neutral = "neutral"
        case bad = "bad"
        case terrible = "terrible"
        
        public var localizedString: String {
            return NSLocalizedString(self.rawValue, comment: "Mood type")
        }
        
        // Backward compatibility initialization
        public init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            let rawValue = try container.decode(String.self)
            
            // Handle legacy Chinese rawValue
            switch rawValue {
            case NSLocalizedString("great_mood_zh", comment: "很好"): self = .great
            case NSLocalizedString("good_mood_zh", comment: "好"): self = .good
            case NSLocalizedString("neutral_mood_zh", comment: "一般"): self = .neutral
            case NSLocalizedString("bad_mood_zh", comment: "不好"): self = .bad
            case NSLocalizedString("terrible_mood_zh", comment: "很差"): self = .terrible
            default:
                if let type = MoodType(rawValue: rawValue) {
                    self = type
                } else {
                    self = .neutral // Default value
                }
            }
        }
        
        public var icon: String {
            switch self {
            case .great: return "😊"
            case .good: return "🙂"
            case .neutral: return "😐"
            case .bad: return "😟"
            case .terrible: return "😢"
            }
        }
    }
    
    // Main category enumeration
    public enum TriggerMainCategory: String, Codable, CaseIterable {
        case emotional = "emotional"
        case environmental = "environmental"
        case physical = "physical"
        case events = "events"
        
        public var localizedString: String {
            return NSLocalizedString(self.rawValue, comment: "Main trigger category")
        }
        
        // Backward compatibility initialization
        public init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            let rawValue = try container.decode(String.self)
            
            // Handle legacy Chinese rawValue
            switch rawValue {
            case NSLocalizedString("emotional_category_zh", comment: "情绪心理"): self = .emotional
            case NSLocalizedString("environmental_category_zh", comment: "环境社交"): self = .environmental
            case NSLocalizedString("physical_category_zh", comment: "生理因素"): self = .physical
            case NSLocalizedString("events_category_zh", comment: "特定事件"): self = .events
            default:
                if let category = TriggerMainCategory(rawValue: rawValue) {
                    self = category
                } else {
                    self = .emotional // Default value
                }
            }
        }
        
        public var icon: String {
            switch self {
            case .emotional: return "brain.head.profile"
            case .environmental: return "globe"
            case .physical: return "figure.walk"
            case .events: return "calendar.badge.exclamationmark"
            }
        }
    }
    
    // Sub-category enumeration
    public enum TriggerSubCategory: String, Codable, CaseIterable {
        // Emotional & Psychological
        case negativeEmotions = "negative_emotions"
        case positiveEmotions = "positive_emotions"
        case mentalStates = "mental_states"
        
        // Environmental & Social
        case locations = "locations"
        case times = "times"
        case socialContexts = "social_contexts"
        case mediaInfluence = "media_influence"
        
        // Physical Factors
        case withdrawalSymptoms = "withdrawal_symptoms"
        case bodyConditions = "body_conditions"
        case physiologicalNeeds = "physiological_needs"
        
        // Specific Events
        case conflicts = "conflicts"
        case failures = "failures"
        case lifeEvents = "life_events"
        case noObviousTrigger = "no_obvious_trigger"
        
        public var localizedString: String {
            return NSLocalizedString(self.rawValue, comment: "Sub trigger category")
        }
        
        public var icon: String {
            switch self {
            // Emotional & Psychological
            case .negativeEmotions: return "face.smiling.inverse"
            case .positiveEmotions: return "face.smiling"
            case .mentalStates: return "brain"
                
            // Environmental & Social
            case .locations: return "mappin.and.ellipse"
            case .times: return "clock"
            case .socialContexts: return "person.2"
            case .mediaInfluence: return "tv"
                
            // Physical Factors
            case .withdrawalSymptoms: return "waveform.path.ecg"
            case .bodyConditions: return "heart"
            case .physiologicalNeeds: return "bed.double"
                
            // Specific Events
            case .conflicts: return "bolt"
            case .failures: return "xmark.circle"
            case .lifeEvents: return "calendar"
            case .noObviousTrigger: return "questionmark"
            }
        }
        
        // Backward compatibility initialization
        public init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            let rawValue = try container.decode(String.self)
            
            // Handle legacy Chinese rawValue
            switch rawValue {
            case NSLocalizedString("negative_emotions_zh", comment: "负面情绪"): self = .negativeEmotions
            case NSLocalizedString("positive_emotions_zh", comment: "正面情绪"): self = .positiveEmotions
            case NSLocalizedString("mental_states_zh", comment: "心理状态"): self = .mentalStates
            case NSLocalizedString("specific_locations_zh", comment: "特定地点"): self = .locations
            case NSLocalizedString("specific_times_zh", comment: "特定时间"): self = .times
            case NSLocalizedString("social_occasions_zh", comment: "社交场合"): self = .socialContexts
            case NSLocalizedString("media_influence_zh", comment: "媒体影响"): self = .mediaInfluence
            case NSLocalizedString("withdrawal_symptoms_zh", comment: "戒断症状"): self = .withdrawalSymptoms
            case NSLocalizedString("physical_conditions_zh", comment: "身体状况"): self = .bodyConditions
            case NSLocalizedString("physiological_needs_zh", comment: "生理需求"): self = .physiologicalNeeds
            case NSLocalizedString("conflicts_zh", comment: "冲突争吵"): self = .conflicts
            case NSLocalizedString("failures_setbacks_zh", comment: "失败挫折"): self = .failures
            case NSLocalizedString("major_events_zh", comment: "重大事件"): self = .lifeEvents
            case NSLocalizedString("no_obvious_reason_zh", comment: "无明确原因"): self = .noObviousTrigger
            default:
                if let category = TriggerSubCategory(rawValue: rawValue) {
                    self = category
                } else {
                    self = .noObviousTrigger // Default value
                }
            }
        }
        
        // Map to legacy TriggerType
        public func toTriggerType() -> TriggerType {
            switch self {
            case .negativeEmotions, .positiveEmotions, .mentalStates:
                return .emotion
            case .locations, .mediaInfluence:
                return .environment
            case .times:
                return .time
            case .socialContexts:
                return .social
            case .withdrawalSymptoms, .bodyConditions, .physiologicalNeeds:
                return .physical
            case .conflicts, .failures, .lifeEvents, .noObviousTrigger:
                return .environment
            }
        }
        
        // Get parent main category
        public var mainCategory: TriggerMainCategory {
            switch self {
            case .negativeEmotions, .positiveEmotions, .mentalStates:
                return .emotional
            case .locations, .times, .socialContexts, .mediaInfluence:
                return .environmental
            case .withdrawalSymptoms, .bodyConditions, .physiologicalNeeds:
                return .physical
            case .conflicts, .failures, .lifeEvents, .noObviousTrigger:
                return .events
            }
        }
        
        // Map legacy Chinese options to new keys
        public func mapOldChineseOption(_ option: String) -> String {
            let oldToNewMapping: [String: String] = [
                // Negative Emotions
                NSLocalizedString("stress_zh", comment: "压力"): "stress",
                NSLocalizedString("anxiety_zh", comment: "焦虑"): "anxiety",
                NSLocalizedString("depression_zh", comment: "抑郁"): "depression",
                NSLocalizedString("anger_zh", comment: "愤怒"): "anger",
                NSLocalizedString("loneliness_zh", comment: "孤独"): "loneliness",
                NSLocalizedString("boredom_zh", comment: "无聊"): "boredom",
                NSLocalizedString("guilt_zh", comment: "内疚"): "guilt",
                NSLocalizedString("jealousy_zh", comment: "嫉妒"): "jealousy",
                NSLocalizedString("fear_zh", comment: "恐惧"): "fear",
                
                // Positive Emotions
                NSLocalizedString("excitement_zh", comment: "兴奋"): "excitement",
                NSLocalizedString("joy_zh", comment: "喜悦"): "joy",
                NSLocalizedString("relaxation_zh", comment: "放松"): "relaxation",
                NSLocalizedString("comfort_zh", comment: "舒适"): "comfort",
                NSLocalizedString("confidence_zh", comment: "自信"): "confidence",
                NSLocalizedString("satisfaction_zh", comment: "满足"): "satisfaction",
                
                // Mental States
                NSLocalizedString("fatigue_zh", comment: "疲劳"): "fatigue",
                NSLocalizedString("drowsiness_zh", comment: "困倦"): "drowsiness",
                NSLocalizedString("impulsiveness_zh", comment: "冲动"): "impulsiveness",
                NSLocalizedString("lack_of_control_zh", comment: "缺乏自控"): "lack_of_control",
                NSLocalizedString("rationalization_zh", comment: "合理化"): "rationalization",
                NSLocalizedString("nostalgia_zh", comment: "怀旧"): "nostalgia",
                NSLocalizedString("craving_zh", comment: "渴望"): "craving",
                NSLocalizedString("temptation_zh", comment: "诱惑"): "temptation",
                
                // Locations
                NSLocalizedString("bar_zh", comment: "酒吧"): "bar",
                NSLocalizedString("ktv_zh", comment: "KTV"): "ktv",
                NSLocalizedString("restaurant_zh", comment: "餐厅"): "restaurant",
                NSLocalizedString("smoking_area_zh", comment: "吸烟区"): "smoking_area",
                NSLocalizedString("convenience_store_zh", comment: "便利店"): "convenience_store",
                NSLocalizedString("supermarket_zh", comment: "超市"): "supermarket",
                NSLocalizedString("home_zh", comment: "家里"): "home",
                NSLocalizedString("office_zh", comment: "办公室"): "office",
                NSLocalizedString("usual_places_zh", comment: "习惯场所"): "usual_places",
                
                // Times
                NSLocalizedString("morning_zh", comment: "早晨"): "morning",
                NSLocalizedString("noon_zh", comment: "中午"): "noon",
                NSLocalizedString("evening_zh", comment: "晚上"): "evening",
                NSLocalizedString("late_night_zh", comment: "深夜"): "late_night",
                NSLocalizedString("weekend_zh", comment: "周末"): "weekend",
                NSLocalizedString("holidays_zh", comment: "节假日"): "holidays",
                NSLocalizedString("work_break_zh", comment: "工作间隙"): "work_break",
                NSLocalizedString("after_meals_zh", comment: "饭后"): "after_meals",
                NSLocalizedString("before_bed_zh", comment: "睡前"): "before_bed",
                
                // Social Contexts
                NSLocalizedString("friend_gathering_zh", comment: "朋友聚会"): "friend_gathering",
                NSLocalizedString("family_dinner_zh", comment: "家庭聚餐"): "family_dinner",
                NSLocalizedString("social_pressure_zh", comment: "社交压力"): "social_pressure",
                NSLocalizedString("peer_influence_zh", comment: "他人影响"): "peer_influence",
                NSLocalizedString("colleague_influence_zh", comment: "同事影响"): "colleague_influence",
                NSLocalizedString("unfamiliar_environment_zh", comment: "陌生环境"): "unfamiliar_environment",
                
                // Media Influence
                NSLocalizedString("advertising_zh", comment: "广告"): "advertising",
                NSLocalizedString("movies_zh", comment: "电影"): "movies",
                NSLocalizedString("tv_shows_zh", comment: "电视节目"): "tv_shows",
                NSLocalizedString("social_media_zh", comment: "社交媒体"): "social_media",
                NSLocalizedString("easily_accessible_zh", comment: "触手可得"): "easily_accessible",
                
                // Withdrawal Symptoms
                NSLocalizedString("headache_zh", comment: "头痛"): "headache",
                NSLocalizedString("nausea_zh", comment: "恶心"): "nausea",
                NSLocalizedString("insomnia_zh", comment: "失眠"): "insomnia",
                NSLocalizedString("irritability_zh", comment: "焦躁"): "irritability",
                NSLocalizedString("poor_concentration_zh", comment: "注意力不集中"): "poor_concentration",
                NSLocalizedString("strong_urge_zh", comment: "强烈渴望"): "strong_urge",
                
                // Body Conditions
                NSLocalizedString("illness_zh", comment: "疾病"): "illness",
                NSLocalizedString("pain_zh", comment: "疼痛"): "pain",
                NSLocalizedString("hormonal_changes_zh", comment: "激素变化"): "hormonal_changes",
                NSLocalizedString("menstruation_zh", comment: "经期"): "menstruation",
                NSLocalizedString("weight_changes_zh", comment: "体重变化"): "weight_changes",
                
                // Physiological Needs
                NSLocalizedString("hunger_zh", comment: "饥饿"): "hunger",
                NSLocalizedString("thirst_zh", comment: "口渴"): "thirst",
                NSLocalizedString("sleep_deprivation_zh", comment: "睡眠不足"): "sleep_deprivation",
                NSLocalizedString("physical_exhaustion_zh", comment: "体力透支"): "physical_exhaustion",
                
                // Conflicts
                NSLocalizedString("family_conflict_zh", comment: "家庭争吵"): "family_conflict",
                NSLocalizedString("work_conflict_zh", comment: "工作冲突"): "work_conflict",
                NSLocalizedString("interpersonal_conflict_zh", comment: "人际矛盾"): "interpersonal_conflict",
                NSLocalizedString("disagreement_zh", comment: "意见分歧"): "disagreement",
                
                // Failures
                NSLocalizedString("work_failure_zh", comment: "工作失败"): "work_failure",
                NSLocalizedString("study_setback_zh", comment: "学习挫折"): "study_setback",
                NSLocalizedString("plan_disruption_zh", comment: "计划受阻"): "plan_disruption",
                NSLocalizedString("unmet_expectations_zh", comment: "期望落空"): "unmet_expectations",
                
                // Life Events
                NSLocalizedString("unemployment_zh", comment: "失业"): "unemployment",
                NSLocalizedString("breakup_zh", comment: "失恋"): "breakup",
                NSLocalizedString("moving_zh", comment: "搬家"): "moving",
                NSLocalizedString("bereavement_zh", comment: "亲人去世"): "bereavement",
                NSLocalizedString("major_changes_zh", comment: "重大变故"): "major_changes",
                
                // No Obvious Trigger
                NSLocalizedString("habitual_behavior_zh", comment: "习惯性行为"): "habitual_behavior",
                NSLocalizedString("inner_desire_zh", comment: "内在渴望"): "inner_desire",
                NSLocalizedString("no_obvious_reason_zh", comment: "无明显原因"): "no_obvious_reason",
                NSLocalizedString("custom_reason_zh", comment: "自定义"): "custom_reason"
            ]
            return oldToNewMapping[option] ?? option
        }
        
        // Get specific options
        public var specificOptions: [String] {
            let options = self.getSpecificOptions()
            return options.map { option in
                // Map legacy Chinese options to new keys
                let key = mapOldChineseOption(option)
                return NSLocalizedString(key, comment: "Specific trigger option")
            }
        }
        
        private func getSpecificOptions() -> [String] {
            switch self {
            case .negativeEmotions:
                return ["stress", "anxiety", "depression", "anger", "loneliness", "boredom", "guilt", "jealousy", "fear"]
            case .positiveEmotions:
                return ["excitement", "joy", "relaxation", "comfort", "confidence", "satisfaction"]
            case .mentalStates:
                return ["fatigue", "drowsiness", "impulsiveness", "lack_of_control", "rationalization", "nostalgia", "craving", "temptation"]
            case .locations:
                return ["bar", "ktv", "restaurant", "smoking_area", "convenience_store", "supermarket", "home", "office", "usual_places"]
            case .times:
                return ["morning", "noon", "evening", "late_night", "weekend", "holidays", "work_break", "after_meals", "before_bed"]
            case .socialContexts:
                return ["friend_gathering", "family_dinner", "social_pressure", "peer_influence", "colleague_influence", "unfamiliar_environment"]
            case .mediaInfluence:
                return ["advertising", "movies", "tv_shows", "social_media", "easily_accessible"]
            case .withdrawalSymptoms:
                return ["headache", "nausea", "insomnia", "irritability", "poor_concentration", "strong_urge"]
            case .bodyConditions:
                return ["illness", "pain", "hormonal_changes", "menstruation", "weight_changes"]
            case .physiologicalNeeds:
                return ["hunger", "thirst", "sleep_deprivation", "physical_exhaustion"]
            case .conflicts:
                return ["family_conflict", "work_conflict", "interpersonal_conflict", "disagreement"]
            case .failures:
                return ["work_failure", "study_setback", "plan_disruption", "unmet_expectations"]
            case .lifeEvents:
                return ["unemployment", "breakup", "moving", "bereavement", "major_changes"]
            case .noObviousTrigger:
                return ["habitual_behavior", "inner_desire", "no_obvious_reason", "custom_reason"]
            }
        }
    }
    
    public init(id: UUID = UUID(), type: TriggerType, desc: String, habitId: UUID? = nil) {
        self.id = id
        self.triggerType = type
        self.triggerDesc = desc
        self.habitId = habitId
    }
    
    // 新增：使用新分类系统的初始化器
    public init(id: UUID = UUID(), 
                mainCategory: TriggerMainCategory, 
                subCategory: TriggerSubCategory, 
                specificOptions: [String],
                desc: String, 
                habitId: UUID? = nil) {
        self.id = id
        self.triggerType = subCategory.toTriggerType() // 映射到旧的类型
        self.mainCategory = mainCategory
        self.subCategory = subCategory
        self.specificOptions = specificOptions
        self.triggerDesc = desc
        self.habitId = habitId
    }
    
    // 添加辅助方法，用于获取具体选项的描述
    public var specificOptionsDescription: String {
        return specificOptions.isEmpty ? "" : specificOptions.joined(separator: "、")
    }
    
    // 添加辅助方法，用于兼容旧版数据
    public func migrateToNewStructure() {
        if mainCategory == nil {
            // 根据旧的triggerType设置推断的mainCategory和subCategory
            switch triggerType {
            case .emotion:
                mainCategory = .emotional
                subCategory = .negativeEmotions
            case .environment:
                mainCategory = .environmental
                subCategory = .locations
            case .time:
                mainCategory = .environmental
                subCategory = .times
            case .social:
                mainCategory = .environmental
                subCategory = .socialContexts
            case .physical:
                mainCategory = .physical
                subCategory = .physiologicalNeeds
            }
        }
    }
    
    // 添加数据迁移方法
    public func migrateToNewFormat() {
        // 已经在 init(from decoder:) 中处理了枚举的迁移
        // 这里处理其他可能需要迁移的数据
        if let oldOptions = self.specificOptions as? [String] {
            let newOptions = oldOptions.map { option in
                // 如果是旧的中文选项，转换为新的键
                if let subCategory = self.subCategory {
                    return subCategory.mapOldChineseOption(option)
                }
                return option
            }
            self.specificOptions = newOptions
        }
    }
    
    // 编码和解码支持
    public required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        triggerType = try container.decode(TriggerType.self, forKey: .triggerType)
        triggerDesc = try container.decode(String.self, forKey: .triggerDesc)
        frequency = try container.decodeIfPresent(Int.self, forKey: .frequency) ?? 1
        lastOccurred = try container.decodeIfPresent(Date.self, forKey: .lastOccurred) ?? Date()
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        mood = try container.decodeIfPresent(MoodType.self, forKey: .mood)
        location = try container.decodeIfPresent(String.self, forKey: .location)
        alternativeBehaviors = try container.decodeIfPresent([String].self, forKey: .alternativeBehaviors) ?? []
        audioRecordingURL = try container.decodeIfPresent(String.self, forKey: .audioRecordingURL)
        audioDuration = try container.decodeIfPresent(Double.self, forKey: .audioDuration)
        audioWaveformData = try container.decodeIfPresent([Double].self, forKey: .audioWaveformData)
        images = try container.decodeIfPresent([String].self, forKey: .images) ?? []
        tags = try container.decodeIfPresent([String].self, forKey: .tags) ?? []
        habitId = try container.decodeIfPresent(UUID.self, forKey: .habitId)
        
        // 新增字段
        mainCategory = try container.decodeIfPresent(TriggerMainCategory.self, forKey: .mainCategory)
        subCategory = try container.decodeIfPresent(TriggerSubCategory.self, forKey: .subCategory)
        specificOptions = try container.decodeIfPresent([String].self, forKey: .specificOptions) ?? []
        
        // 如果新字段为空，尝试迁移
        if mainCategory == nil {
            migrateToNewStructure()
        }
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(triggerType, forKey: .triggerType)
        try container.encode(triggerDesc, forKey: .triggerDesc)
        try container.encode(frequency, forKey: .frequency)
        try container.encode(lastOccurred, forKey: .lastOccurred)
        try container.encodeIfPresent(notes, forKey: .notes)
        try container.encodeIfPresent(mood, forKey: .mood)
        try container.encodeIfPresent(location, forKey: .location)
        try container.encode(alternativeBehaviors, forKey: .alternativeBehaviors)
        try container.encodeIfPresent(audioRecordingURL, forKey: .audioRecordingURL)
        try container.encodeIfPresent(audioDuration, forKey: .audioDuration)
        try container.encodeIfPresent(audioWaveformData, forKey: .audioWaveformData)
        try container.encode(images, forKey: .images)
        try container.encode(tags, forKey: .tags)
        try container.encodeIfPresent(habitId, forKey: .habitId)
        
        // 编码新字段
        try container.encodeIfPresent(mainCategory, forKey: .mainCategory)
        try container.encodeIfPresent(subCategory, forKey: .subCategory)
        try container.encode(specificOptions, forKey: .specificOptions)
    }
    
    private enum CodingKeys: String, CodingKey {
        case id, triggerType, triggerDesc, frequency, lastOccurred, notes, mood
        case location, alternativeBehaviors, audioRecordingURL, audioDuration
        case audioWaveformData, images, tags, habitId
        // 新字段的键
        case mainCategory, subCategory, specificOptions
    }
} 