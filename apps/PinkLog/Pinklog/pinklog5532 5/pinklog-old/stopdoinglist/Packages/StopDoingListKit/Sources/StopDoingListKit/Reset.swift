import Foundation

public final class Reset: Identifiable, Codable {
    public var id: UUID = UUID()
    public var date: Date
    public var streakDays: Int = 0
    public var notes: String?
    public var mood: MoodType?
    public var location: String?
    public var audioRecordingURL: String?
    public var audioDuration: Double?
    public var audioWaveformData: [Double]?
    public var images: [String] = []
    public var tags: [String] = []
    public var habitId: UUID?
    
    public enum MoodType: String, Codable, CaseIterable {
        case great = "很好"
        case good = "好"
        case neutral = "一般"
        case bad = "不好"
        case terrible = "很差"
        
        public var icon: String {
            switch self {
            case .great: return "😊"
            case .good: return "🙂"
            case .neutral: return "😐"
            case .bad: return "😟"
            case .terrible: return "😢"
            }
        }
    }
    
    public init(id: UUID = UUID(), date: Date = Date(), streakDays: Int = 0, habitId: UUID? = nil) {
        self.id = id
        self.date = date
        self.streakDays = streakDays
        self.habitId = habitId
    }
} 