import Foundation
import StoreKit
import SwiftUI

// StoreManager类管理应用内购买
public class StoreManager: NSObject, ObservableObject {
    public static let shared = StoreManager()

    // 发布属性，UI将观察这些属性以更新状态
    @Published public var products: [Product] = []
    @Published public var isLoading = false
    @Published public var errorMessage: String?
    @Published public var isPurchaseInProgress = false

    // 购买状态
    @Published public var isPremium = false

    // XMoment使用次数跟踪
    private let xmomentDailyUsesKey = "xmoment_daily_uses"
    private let xmomentLastUseDateKey = "xmoment_last_use_date"
    private let isPremiumKey = "is_premium_purchased"

    // 产品ID
    private let premiumProductID = "top.trysapp.xmoment.premium"

    // 初始化时请求产品并开始观察交易
    override private init() {
        super.init()

        // 从UserDefaults加载状态
        isPremium = UserDefaults.standard.bool(forKey: isPremiumKey)

        // 请求产品信息
        Task {
            await requestProducts()
            await updatePurchaseStatus()
        }

        // 设置交易监听器
        Task {
            await listenForTransactions()
        }
    }

    // MARK: - 产品请求

    @MainActor
    public func requestProducts() async {
        isLoading = true
        errorMessage = nil

        do {
            let storeProducts = try await Product.products(for: [premiumProductID])
            self.products = storeProducts
            isLoading = false
        } catch {
            errorMessage = "无法加载产品信息: \(error.localizedDescription)"
            isLoading = false
        }
    }

    // MARK: - 购买功能

    @MainActor
    public func purchasePremium() async {
        guard let product = products.first(where: { $0.id == premiumProductID }) else {
            errorMessage = "无法找到产品"
            return
        }

        isPurchaseInProgress = true

        do {
            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                // 验证购买
                switch verification {
                case .verified(let transaction):
                    // 购买成功，更新状态
                    await transaction.finish()
                    isPremium = true
                    UserDefaults.standard.set(true, forKey: isPremiumKey)
                    isPurchaseInProgress = false
                case .unverified(_, let error):
                    // 购买验证失败
                    errorMessage = "购买验证失败: \(error.localizedDescription)"
                    isPurchaseInProgress = false
                }
            case .userCancelled:
                // 用户取消购买
                errorMessage = "购买已取消"
                isPurchaseInProgress = false
            case .pending:
                // 购买待处理
                errorMessage = "购买正在处理中"
                isPurchaseInProgress = false
            @unknown default:
                // 未知状态
                errorMessage = "购买状态未知"
                isPurchaseInProgress = false
            }
        } catch {
            // 购买过程中出错
            errorMessage = "购买过程中出错: \(error.localizedDescription)"
            isPurchaseInProgress = false
        }
    }

    // MARK: - 恢复购买

    @MainActor
    public func restorePurchases() async {
        isLoading = true

        do {
            try await AppStore.sync()
            await updatePurchaseStatus()
            isLoading = false
        } catch {
            errorMessage = "恢复购买失败: \(error.localizedDescription)"
            isLoading = false
        }
    }

    // MARK: - 交易监听

    @MainActor
    private func listenForTransactions() async {
        // 持续监听交易更新
        for await result in Transaction.updates {
            do {
                let transaction = try checkVerified(result)

                // 处理交易
                if transaction.productID == premiumProductID {
                    isPremium = true
                    UserDefaults.standard.set(true, forKey: isPremiumKey)
                }

                // 完成交易
                await transaction.finish()
            } catch {
                // 处理验证错误
                print("交易验证失败: \(error)")
            }
        }
    }

    // 验证交易
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .verified(let safe):
            return safe
        case .unverified(_, let error):
            throw error
        }
    }

    // 更新购买状态
    @MainActor
    public func updatePurchaseStatus() async {
        // 检查当前有效的购买
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)

                // 如果找到有效的高级版购买
                if transaction.productID == premiumProductID {
                    isPremium = true
                    UserDefaults.standard.set(true, forKey: isPremiumKey)
                }
            } catch {
                print("验证当前权益失败: \(error)")
            }
        }
    }

    // MARK: - XMoment使用次数管理

    // 获取今天的XMoment使用次数
    public func getXMomentDailyUses() -> Int {
        let lastUseDate = Date(timeIntervalSince1970: UserDefaults.standard.double(forKey: xmomentLastUseDateKey))
        let calendar = Calendar.current

        if !calendar.isDate(lastUseDate, inSameDayAs: Date()) {
            // 新的一天，重置计数
            resetXMomentDailyUse()
            return 0
        }

        return UserDefaults.standard.integer(forKey: xmomentDailyUsesKey)
    }

    // 记录XMoment使用
    public func recordXMomentUse() {
        let lastUseDate = Date(timeIntervalSince1970: UserDefaults.standard.double(forKey: xmomentLastUseDateKey))
        let calendar = Calendar.current

        if !calendar.isDate(lastUseDate, inSameDayAs: Date()) {
            // 新的一天，重置计数
            resetXMomentDailyUse()
        }

        let currentUses = UserDefaults.standard.integer(forKey: xmomentDailyUsesKey)
        UserDefaults.standard.set(currentUses + 1, forKey: xmomentDailyUsesKey)
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: xmomentLastUseDateKey)
    }

    // 重置XMoment每日使用次数
    public func resetXMomentDailyUse() {
        UserDefaults.standard.set(0, forKey: xmomentDailyUsesKey)
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: xmomentLastUseDateKey)
    }
}
