import Foundation

public struct HabitReport: Codable {
    public var habitId: UUID
    public var startDate: Date
    public var endDate: Date
    public var triggerCount: Int
    public var commonLocations: [String: Int]
    public var commonTriggerTimes: [Int: Int]
    
    var durationInDays: Int {
        Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
    }
    
    var successRate: Double {
        guard durationInDays > 0 else { return 0 }
        return Double(durationInDays - triggerCount) / Double(durationInDays)
    }
    
    public init(habitId: UUID, startDate: Date, endDate: Date, triggerCount: Int, commonLocations: [String: Int], commonTriggerTimes: [Int: Int]) {
        self.habitId = habitId
        self.startDate = startDate
        self.endDate = endDate
        self.triggerCount = triggerCount
        self.commonLocations = commonLocations
        self.commonTriggerTimes = commonTriggerTimes
    }
} 