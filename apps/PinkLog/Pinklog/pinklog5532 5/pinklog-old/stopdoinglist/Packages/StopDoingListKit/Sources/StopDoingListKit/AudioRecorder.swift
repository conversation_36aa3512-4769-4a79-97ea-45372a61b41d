import Foundation
import AVFoundation
import SwiftUI
import AVFAudio

public class AudioRecorder: NSObject, ObservableObject {
    // 录音状态
    public enum RecordingState {
        case idle
        case recording
        case playing
    }
    
    // 录音会话 - 延迟初始化
    private lazy var audioSession: AVAudioSession = {
        print("延迟初始化 AVAudioSession")
        return AVAudioSession.sharedInstance()
    }()
    
    // 录音器和播放器
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    
    // 录音文件URL
    @Published public var audioFileURL: URL?
    
    // 波形数据
    @Published public var waveformSamples: [CGFloat] = []
    
    // 录音状态
    @Published public var recordingState: RecordingState = .idle
    
    // 录音时长
    @Published public var recordingDuration: TimeInterval = 0
    private var recordingTimer: Timer?
    
    // 播放时间
    @Published public var currentPlayTime: TimeInterval = 0
    private var playbackTimer: Timer?
    
    // 调试信息
    @Published public var debugMessage: String = ""
    
    // 标记音频会话是否已设置
    private var isAudioSessionSetup = false
    
    // 添加错误处理标志
    private var lastOperationFailed = false
    private var lastErrorMessage = ""
    
    public override init() {
        super.init()
        
        #if targetEnvironment(simulator)
        print("AudioRecorder 初始化完成 (模拟器环境)")
        #else
        print("AudioRecorder 初始化完成，未设置音频会话")
        #endif
        
        // 预先设置音频会话，避免首次使用时的延迟
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            if self?.setupAudioSession() == true {
                #if targetEnvironment(simulator)
                // 模拟器中忽略硬件初始化错误
                print("模拟器音频会话已准备就绪 (忽略硬件错误)")
                #endif
            }
        }
    }
    
    deinit {
        // 确保资源被正确释放
        cleanUp()
        print("AudioRecorder 已释放")
    }
    
    // 添加清理方法，确保资源正确释放
    private func cleanUp() {
        // 停止所有计时器
        recordingTimer?.invalidate()
        recordingTimer = nil
        
        playbackTimer?.invalidate()
        playbackTimer = nil
        
        // 停止录音并释放录音机
        if let recorder = audioRecorder, recorder.isRecording {
            recorder.stop()
        }
        audioRecorder = nil
        
        // 停止播放并释放播放器
        if let player = audioPlayer, player.isPlaying {
            player.stop()
        }
        audioPlayer = nil
        
        // 重置音频会话
        if isAudioSessionSetup {
            do {
                try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
                print("音频会话已停用")
            } catch {
                print("停用音频会话失败: \(error.localizedDescription)")
            }
            isAudioSessionSetup = false
        }
    }
    
    // 设置音频会话
    private func setupAudioSession() -> Bool {
        // 如果已经设置过，不再重复设置
        if isAudioSessionSetup {
            return true
        }
        
        do {
            // 在模拟器中需要特殊处理
            #if targetEnvironment(simulator)
            // 模拟器使用更简单的配置
            try audioSession.setCategory(.playAndRecord, mode: .default)
            #else
            // 真机使用完整配置
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            #endif
            
            // 添加音频会话配置通知观察
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioSessionInterruption),
                name: AVAudioSession.interruptionNotification,
                object: audioSession
            )
            
            // 激活音频会话
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            
            // 标记音频会话已设置
            isAudioSessionSetup = true
            print("音频会话设置成功")
            return true
        } catch {
            print("设置音频会话失败: \(error.localizedDescription)")
            
            // 添加带延迟的重试逻辑
            DispatchQueue.global(qos: .userInitiated).asyncAfter(deadline: .now() + 0.5) { [weak self] in
                print("尝试重新设置音频会话...")
                _ = self?.setupAudioSession()
            }
            
            return false
        }
    }
    
    // 处理音频中断
    @objc private func handleAudioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            // 中断开始，停止录音/播放
            if recordingState == .recording {
                _ = stopRecording()
            } else if recordingState == .playing {
                stopPlayback()
            }
            
        case .ended:
            // 中断结束，尝试恢复
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    // 重新激活音频会话
                    _ = setupAudioSession()
                }
            }
            
        @unknown default:
            break
        }
    }
    
    // 请求麦克风权限
    public func requestMicrophonePermission(completion: @escaping (Bool) -> Void) {
        // 根据iOS版本使用不同的API
        if #available(iOS 17.0, *) {
            switch AVAudioApplication.shared.recordPermission {
            case .granted:
                debugMessage = "麦克风权限已授予"
                completion(true)
            case .denied:
                debugMessage = "麦克风权限被拒绝"
                completion(false)
            case .undetermined:
                debugMessage = "请求麦克风权限..."
                AVAudioApplication.requestRecordPermission { granted in
                    DispatchQueue.main.async {
                        self.debugMessage = granted ? "麦克风权限已授予" : "麦克风权限被拒绝"
                        completion(granted)
                    }
                }
            @unknown default:
                debugMessage = "未知的麦克风权限状态"
                completion(false)
            }
        } else {
            // 旧版iOS使用旧API
            switch AVAudioSession.sharedInstance().recordPermission {
            case .granted:
                debugMessage = "麦克风权限已授予"
                completion(true)
            case .denied:
                debugMessage = "麦克风权限被拒绝"
                completion(false)
            case .undetermined:
                debugMessage = "请求麦克风权限..."
                AVAudioSession.sharedInstance().requestRecordPermission { granted in
                    DispatchQueue.main.async {
                        self.debugMessage = granted ? "麦克风权限已授予" : "麦克风权限被拒绝"
                        completion(granted)
                    }
                }
            @unknown default:
                debugMessage = "未知的麦克风权限状态"
                completion(false)
            }
        }
    }
    
    // 开始录音
    public func startRecording() {
        // 确保当前不在录音状态
        if recordingState == .recording {
            debugMessage = "已经在录音中，忽略开始录音请求"
            return
        }
        
        // 请求麦克风权限
        requestMicrophonePermission { [weak self] granted in
            guard let self = self, granted else {
                DispatchQueue.main.async {
                    self?.debugMessage = "麦克风权限被拒绝，无法开始录音"
                }
                return
            }
            
            // 确保音频会话已设置
            if !self.setupAudioSession() {
                DispatchQueue.main.async {
                    self.debugMessage = "无法设置音频会话，取消录音"
                }
                return
            }
            
            // 创建录音文件URL - 使用持久化存储
            let fileName = "recording_\(UUID().uuidString).m4a"
            
            // 使用guard确保目录可以被创建
            guard let storageURL = self.getAudioDirectory() else {
                DispatchQueue.main.async {
                    self.debugMessage = "无法创建音频存储目录，取消录音"
                }
                return
            }
            
            let fileURL = storageURL.appendingPathComponent(fileName)
            
            // 在主线程更新URL
            DispatchQueue.main.async {
                self.audioFileURL = fileURL
            }
            
            // 录音设置
            let settings: [String: Any] = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100.0,
                AVNumberOfChannelsKey: 1,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]
            
            // 创建录音器
            do {
                self.audioRecorder = try AVAudioRecorder(url: fileURL, settings: settings)
                self.audioRecorder?.delegate = self
                self.audioRecorder?.isMeteringEnabled = true
                self.audioRecorder?.prepareToRecord()
                
                // 开始录音
                if self.audioRecorder?.record() == true {
                    // 更新状态
                    DispatchQueue.main.async {
                        self.recordingState = .recording
                        self.recordingDuration = 0
                        self.waveformSamples = []
                        self.debugMessage = "录音已开始"
                    }
                    
                    // 启动计时器
                    self.startRecordingTimer()
                } else {
                    DispatchQueue.main.async {
                        self.debugMessage = "开始录音失败"
                    }
                    print(self.debugMessage)
                }
            } catch {
                DispatchQueue.main.async {
                    self.debugMessage = "录音失败: \(error.localizedDescription)"
                }
                print(self.debugMessage)
            }
        }
    }
    
    // 获取音频目录
    private func getAudioDirectory() -> URL? {
        let audioDirectory = FileManager.getAudioDirectory()
        do {
            try FileManager.default.createDirectoryIfNeeded(at: audioDirectory)
            return audioDirectory
        } catch {
            debugMessage = "创建音频目录失败: \(error.localizedDescription)"
            return nil
        }
    }
    
    // 停止录音
    public func stopRecording() -> URL? {
        // 检查是否正在录音，如果不是，则直接返回
        if recordingState != .recording {
            DispatchQueue.main.async {
                self.debugMessage = "没有正在进行的录音，忽略停止录音请求"
            }
            return nil
        }
        
        // 保存当前URL
        let currentURL = audioFileURL
        
        // 安全地停止录音
        audioRecorder?.stop()
        
        // 停止计时器
        DispatchQueue.main.async {
            self.recordingTimer?.invalidate()
            self.recordingTimer = nil
        }
        
        // 更新状态
        DispatchQueue.main.async {
            self.recordingState = .idle
            self.debugMessage = "录音已停止，时长: \(self.formatTime(self.recordingDuration))"
        }
        
        // 停用音频会话
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            DispatchQueue.main.async {
                self.debugMessage = "停用音频会话失败: \(error.localizedDescription)"
            }
            print(debugMessage)
        }
        
        return currentURL
    }
    
    // 播放录音
    public func playRecording(from url: URL) {
        // 如果正在播放，先停止
        if recordingState == .playing {
            stopPlayback()
        }
        
        // 确保文件存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            debugMessage = "要播放的音频文件不存在"
            print("音频文件不存在: \(url.path)")
            return
        }
        
        // 确保音频会话已设置
        if !setupPlaybackSession() {
            DispatchQueue.main.async {
                self.debugMessage = "无法设置播放音频会话，取消播放"
            }
            return
        }
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
            
            // 更新状态
            DispatchQueue.main.async {
                self.recordingState = .playing
                self.currentPlayTime = 0
                self.debugMessage = "开始播放录音"
            }
            
            // 启动播放计时器
            startPlaybackTimer()
        } catch {
            debugMessage = "播放失败: \(error.localizedDescription)"
            print(debugMessage)
        }
    }
    
    // 设置播放音频会话
    private func setupPlaybackSession() -> Bool {
        do {
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            debugMessage = "播放音频会话已激活"
            return true
        } catch {
            debugMessage = "设置播放音频会话失败: \(error.localizedDescription)"
            print(debugMessage)
            return false
        }
    }
    
    // 带错误处理的播放方法
    public func startPlayback(url: URL) throws {
        // 如果正在播放，先停止
        if recordingState == .playing {
            stopPlayback()
        }
        
        // 确保文件存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            let error = NSError(
                domain: "AudioRecorderErrorDomain",
                code: 2,
                userInfo: [NSLocalizedDescriptionKey: "要播放的音频文件不存在: \(url.path)"]
            )
            throw error
        }
        
        // 确保音频会话已设置
        if !setupPlaybackSession() {
            throw NSError(
                domain: "AudioRecorderErrorDomain",
                code: 3,
                userInfo: [NSLocalizedDescriptionKey: "无法设置播放音频会话"]
            )
        }
        
        // 创建播放器
        audioPlayer = try AVAudioPlayer(contentsOf: url)
        audioPlayer?.delegate = self
        audioPlayer?.prepareToPlay()
        
        // 开始播放
        if audioPlayer?.play() == true {
            // 更新状态
            DispatchQueue.main.async {
                self.recordingState = .playing
                self.currentPlayTime = 0
                self.debugMessage = "开始播放录音"
            }
            
            // 启动播放计时器
            startPlaybackTimer()
        } else {
            throw NSError(domain: "AudioRecorderErrorDomain", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法开始播放"])
        }
    }
    
    // 停止播放
    public func stopPlayback() {
        // 检查是否正在播放，如果不是，则直接返回
        if recordingState != .playing {
            debugMessage = "没有正在播放的录音，忽略停止播放请求"
            return
        }
        
        // 安全地停止播放
        audioPlayer?.stop()
        
        // 停止计时器
        DispatchQueue.main.async {
            self.playbackTimer?.invalidate()
            self.playbackTimer = nil
        }
        
        // 更新状态
        DispatchQueue.main.async {
            self.recordingState = .idle
            self.currentPlayTime = 0
            self.debugMessage = "播放已停止"
        }
        
        // 停用音频会话，注意捕获可能的错误
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            debugMessage = "停用音频会话失败: \(error.localizedDescription)"
            print(debugMessage)
        }
    }
    
    // 启动录音计时器
    private func startRecordingTimer() {
        // 确保在主线程上创建定时器
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 停止现有定时器
            self.recordingTimer?.invalidate()
            self.recordingTimer = nil
            
            // 使用RunLoop.main确保在主线程上执行
            self.recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
                guard let self = self, 
                      let recorder = self.audioRecorder,
                      recorder.isRecording else { 
                    // 如果录音器不存在或不在录音，停止定时器
                    DispatchQueue.main.async {
                        self?.recordingTimer?.invalidate()
                        self?.recordingTimer = nil
                    }
                    return 
                }
                
                // 更新录音时长
                let currentTime = recorder.currentTime
                
                // 更新波形数据 - 降低采样频率
                // 这个操作可能比较耗时，可以考虑在后台线程执行
                DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                    guard let self = self else { return }
                    
                    recorder.updateMeters()
                    let power = recorder.averagePower(forChannel: 0)
                    let normalizedPower = self.normalizePower(power)
                    
                    // 在主线程上更新UI相关数据
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        
                        self.recordingDuration = currentTime
                        
                        // 添加波形样本 - 减少存储的样本数量
                        // 使用更高效的数组操作
                        var samples = self.waveformSamples
                        if samples.count >= 30 {
                            samples.removeFirst()
                        }
                        samples.append(normalizedPower)
                        self.waveformSamples = samples
                    }
                }
            }
            
            // 确保定时器添加到主运行循环
            RunLoop.main.add(self.recordingTimer!, forMode: .common)
        }
    }
    
    // 启动播放计时器
    private func startPlaybackTimer() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 停止现有计时器
            self.playbackTimer?.invalidate()
            self.playbackTimer = nil
            
            // 创建新计时器
            self.playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
                guard let self = self,
                      let player = self.audioPlayer else {
                    // 如果播放器不存在，停止计时器
                    DispatchQueue.main.async {
                        self?.playbackTimer?.invalidate()
                        self?.playbackTimer = nil
                    }
                    return
                }
                
                // 更新播放时间
                DispatchQueue.main.async {
                    self.currentPlayTime = player.currentTime
                }
            }
            
            // 确保定时器添加到主运行循环
            RunLoop.main.add(self.playbackTimer!, forMode: .common)
        }
    }
    
    private func normalizePower(_ power: Float) -> CGFloat {
        // power范围是-160到0，我们将其映射到0-1
        let minDb: Float = -60.0
        let normalizedPower = max(0.0, (power - minDb) / abs(minDb))
        return CGFloat(normalizedPower)
    }
    
    // 格式化时间为分:秒
    public func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - AVAudioRecorderDelegate
extension AudioRecorder: AVAudioRecorderDelegate {
    public func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            print("录音未成功完成")
        }
        
        DispatchQueue.main.async {
            self.recordingState = .idle
        }
    }
    
    public func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            print("录音编码错误: \(error.localizedDescription)")
        }
        DispatchQueue.main.async {
            self.recordingState = .idle
        }
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioRecorder: AVAudioPlayerDelegate {
    public func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        DispatchQueue.main.async {
            self.recordingState = .idle
            self.currentPlayTime = 0
            
            self.playbackTimer?.invalidate()
            self.playbackTimer = nil
        }
    }
    
    public func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            print("播放解码错误: \(error.localizedDescription)")
        }
        DispatchQueue.main.async {
            self.recordingState = .idle
            self.currentPlayTime = 0
            
            self.playbackTimer?.invalidate()
            self.playbackTimer = nil
        }
    }
} 