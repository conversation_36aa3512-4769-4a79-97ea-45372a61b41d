import SwiftUI

public struct RecordButton: View {
    @ObservedObject var audioRecorder: AudioRecorder
    @Binding var isRecording: Bool
    let onRecordingComplete: (URL, TimeInterval, [CGFloat]) -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 1.0
    @State private var showPermissionAlert = false
    @State private var permissionDenied = false
    
    public init(audioRecorder: AudioRecorder, isRecording: Binding<Bool>, onRecordingComplete: @escaping (URL, TimeInterval, [CGFloat]) -> Void) {
        self.audioRecorder = audioRecorder
        self._isRecording = isRecording
        self.onRecordingComplete = onRecordingComplete
    }
    
    public var body: some View {
        Button(action: {
            // 点击按钮时切换录音状态
            if isRecording {
                stopRecording()
            } else {
                startRecording()
            }
        }) {
            Image(systemName: "mic.circle.fill")
                .font(.system(size: 36))
                .foregroundColor(isRecording ? .red : .blue)
                .scaleEffect(scale)
                .opacity(opacity)
        }
        .buttonStyle(PlainButtonStyle())
        .alert("需要麦克风权限", isPresented: $showPermissionAlert) {
            Button("取消", role: .cancel) { }
            Button("去设置") {
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(url)
                }
            }
        } message: {
            Text("请在设置中允许此应用使用麦克风，以便录制语音。")
        }
        .onChange(of: audioRecorder.recordingState) { oldValue, newValue in
            // 在主线程上更新UI状态
            DispatchQueue.main.async {
                // 同步录音状态
                isRecording = (newValue == .recording)
                
                // 更新动画
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = isRecording ? 1.2 : 1.0
                    opacity = isRecording ? 0.8 : 1.0
                }
                
                // 如果录音停止，检查是否有录音文件
                if oldValue == .recording && newValue == .idle {
                    if let url = audioRecorder.audioFileURL, 
                       audioRecorder.recordingDuration > 0.5 { // 只处理超过0.5秒的录音
                        onRecordingComplete(
                            url,
                            audioRecorder.recordingDuration,
                            audioRecorder.waveformSamples
                        )
                    }
                }
            }
        }
    }
    
    private func startRecording() {
        // 先更新UI状态，避免阻塞
        withAnimation(.easeInOut(duration: 0.2)) {
            scale = 1.2
            opacity = 0.8
        }
        
        // 检查麦克风权限
        audioRecorder.requestMicrophonePermission { granted in
            if granted {
                // 直接在主线程开始录音，避免线程问题
                audioRecorder.startRecording()
            } else {
                DispatchQueue.main.async {
                    permissionDenied = true
                    showPermissionAlert = true
                }
            }
        }
    }
    
    private func stopRecording() {
        // 先更新UI状态，避免阻塞
        withAnimation(.easeInOut(duration: 0.2)) {
            scale = 1.0
            opacity = 1.0
        }
        
        // 停止录音
        audioRecorder.stopRecording()
    }
} 