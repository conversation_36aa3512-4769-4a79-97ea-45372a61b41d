import SwiftUI

public struct AudioBubbleView: View {
    let audioURL: URL
    let duration: TimeInterval
    let waveformSamples: [CGFloat]
    let onPlay: (URL) -> Void
    let isPlaying: Bool
    
    public init(audioURL: URL, duration: TimeInterval, waveformSamples: [CGFloat], onPlay: @escaping (URL) -> Void, isPlaying: Bool) {
        self.audioURL = audioURL
        self.duration = duration
        self.waveformSamples = waveformSamples
        self.onPlay = onPlay
        self.isPlaying = isPlaying
    }
    
    public var body: some View {
        Button(action: {
            onPlay(audioURL)
        }) {
            HStack(spacing: 10) {
                // 播放/暂停图标
                Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.blue)
                
                // 波形图
                WaveformView(samples: waveformSamples, color: isPlaying ? .blue : .gray)
                    .frame(height: 30)
                
                // 时长
                Text(formatTime(duration))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(Color(.systemGray6))
            .cornerRadius(16)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
} 