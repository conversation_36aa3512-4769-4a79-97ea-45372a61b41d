import Foundation
import SwiftUI

// 音频记录模型
public struct AudioRecordingModel: Identifiable, Codable {
    public var id = UUID()
    public let url: URL
    public let duration: TimeInterval
    public let waveformSamples: [CGFloat]
    public let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, duration, waveformSamples, createdAt, urlString
    }

    public init(url: URL, duration: TimeInterval, waveformSamples: [CGFloat]) {
        self.url = url
        self.duration = duration
        self.waveformSamples = waveformSamples
        self.createdAt = Date()
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        duration = try container.decode(TimeInterval.self, forKey: .duration)
        waveformSamples = try container.decode([CGFloat].self, forKey: .waveformSamples)
        createdAt = try container.decode(Date.self, forKey: .createdAt)

        let urlString = try container.decode(String.self, forKey: .urlString)
        url = URL(fileURLWithPath: urlString)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(duration, forKey: .duration)
        try container.encode(waveformSamples, forKey: .waveformSamples)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(url.path, forKey: .urlString)
    }
}

// X Moment 会话数据模型
public struct XMomentSession: Identifiable, Codable, Equatable {
    public var id: UUID
    public var startTime: Date
    public var endTime: Date
    public var duration: TimeInterval
    public var mood: String?
    public var category: String?
    public var notes: String?
    public var overclockFactor: Double = 1.0 // 新增：超频奖励倍率
    public var overclockDuration: TimeInterval = 0 // 新增：超频持续时间
    public var isInterrupted: Bool = false // 新增：是否被中断
    public var isFreeMode: Bool = false // 新增：是否为自由模式
    public var initialDuration: TimeInterval = 0 // 新增：初始设定时长
    public var isPersonalBest: Bool = false // 新增：是否为个人最佳记录
    public var progressXU: Double = 0.0 // 新增：进步奖励XU

    // 静态方法创建会话
    public static func create(
        duration: TimeInterval,
        category: String? = nil,
        mood: String? = nil,
        notes: String? = nil,
        overclockFactor: Double = 1.0,
        overclockDuration: TimeInterval = 0,
        isInterrupted: Bool = false,
        isFreeMode: Bool = false,
        initialDuration: TimeInterval = 0,
        isPersonalBest: Bool = false,
        progressXU: Double = 0.0
    ) -> XMomentSession {
        let endTime = Date()
        let startTime = endTime.addingTimeInterval(-duration)

        return XMomentSession(
            id: UUID(),
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            mood: mood,
            category: category,
            notes: notes,
            overclockFactor: overclockFactor,
            overclockDuration: overclockDuration,
            isInterrupted: isInterrupted,
            isFreeMode: isFreeMode,
            initialDuration: initialDuration,
            isPersonalBest: isPersonalBest,
            progressXU: progressXU
        )
    }

    // Equatable 实现
    public static func == (lhs: XMomentSession, rhs: XMomentSession) -> Bool {
        return lhs.id == rhs.id
    }
}

// X Moment 用户统计数据模型
public struct XMomentUserStats: Codable {
    // 用户首选项设置
    public var lastUsedDuration: TimeInterval = 25 * 60 // 默认25分钟

    // 统计数据
    public var totalInterruptedCount: Int = 0 // 总中断次数
    public var totalCompletedCount: Int = 0 // 总完成次数
    public var bestStreak: Int = 0 // 最佳连续专注天数
    public var currentStreak: Int = 0 // 当前连续专注天数
    public var firstSessionDate: Date? = nil // 首次专注日期
    public var lastSessionDate: Date? = nil // 最后一次专注日期

    // 里程碑相关
    public var achievedMilestoneIDs: Set<String> = [] // 已达成的里程碑ID

    // 自由模式统计
    public var freeModeSessionCount: Int = 0 // 自由模式使用次数
    public var freeModeTotalDuration: TimeInterval = 0 // 自由模式总时长
    public var freeModeBestDuration: TimeInterval = 0 // 自由模式最佳时长

    public init() {}

    public init(migrateFromUserDefaults: Bool) {
        if migrateFromUserDefaults {
            // 从 UserDefaults 读取旧数据
            let defaults = UserDefaults.standard
            lastUsedDuration = defaults.double(forKey: "xmoment_last_duration") > 0 ? defaults.double(forKey: "xmoment_last_duration") : 25 * 60
            totalCompletedCount = defaults.integer(forKey: "xmoment_total_count")
            bestStreak = defaults.integer(forKey: "xmoment_best_streak")
            currentStreak = defaults.integer(forKey: "xmoment_current_streak")

            if let firstDateTimestamp = defaults.object(forKey: "xmoment_first_date") as? TimeInterval, firstDateTimestamp > 0 {
                firstSessionDate = Date(timeIntervalSince1970: firstDateTimestamp)
            }

            if let lastDateTimestamp = defaults.object(forKey: "xmoment_last_date") as? TimeInterval, lastDateTimestamp > 0 {
                lastSessionDate = Date(timeIntervalSince1970: lastDateTimestamp)
            }

            // 自由模式数据
            freeModeSessionCount = defaults.integer(forKey: "xmoment_free_mode_count")
            freeModeTotalDuration = defaults.double(forKey: "xmoment_free_mode_total_duration")
            freeModeBestDuration = defaults.double(forKey: "xmoment_free_mode_best_duration")
        }
    }

    // 添加自定义解码器，处理字段缺失的情况
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 解码基本字段，如果缺失则使用默认值
        lastUsedDuration = try container.decodeIfPresent(TimeInterval.self, forKey: .lastUsedDuration) ?? 25 * 60
        totalInterruptedCount = try container.decodeIfPresent(Int.self, forKey: .totalInterruptedCount) ?? 0
        totalCompletedCount = try container.decodeIfPresent(Int.self, forKey: .totalCompletedCount) ?? 0
        bestStreak = try container.decodeIfPresent(Int.self, forKey: .bestStreak) ?? 0
        currentStreak = try container.decodeIfPresent(Int.self, forKey: .currentStreak) ?? 0
        firstSessionDate = try container.decodeIfPresent(Date.self, forKey: .firstSessionDate)
        lastSessionDate = try container.decodeIfPresent(Date.self, forKey: .lastSessionDate)

        // 尝试解码新添加的字段，如果缺失则使用默认值
        if let milestoneArray = try? container.decodeIfPresent([String].self, forKey: .achievedMilestoneIDs) {
            achievedMilestoneIDs = Set(milestoneArray)
        } else {
            achievedMilestoneIDs = []
        }

        freeModeSessionCount = try container.decodeIfPresent(Int.self, forKey: .freeModeSessionCount) ?? 0
        freeModeTotalDuration = try container.decodeIfPresent(TimeInterval.self, forKey: .freeModeTotalDuration) ?? 0
        freeModeBestDuration = try container.decodeIfPresent(TimeInterval.self, forKey: .freeModeBestDuration) ?? 0
    }

    // 添加自定义编码器，确保所有字段都被正确编码
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(lastUsedDuration, forKey: .lastUsedDuration)
        try container.encode(totalInterruptedCount, forKey: .totalInterruptedCount)
        try container.encode(totalCompletedCount, forKey: .totalCompletedCount)
        try container.encode(bestStreak, forKey: .bestStreak)
        try container.encode(currentStreak, forKey: .currentStreak)
        try container.encodeIfPresent(firstSessionDate, forKey: .firstSessionDate)
        try container.encodeIfPresent(lastSessionDate, forKey: .lastSessionDate)

        // 将 Set 转换为 Array 再编码
        try container.encode(Array(achievedMilestoneIDs), forKey: .achievedMilestoneIDs)

        try container.encode(freeModeSessionCount, forKey: .freeModeSessionCount)
        try container.encode(freeModeTotalDuration, forKey: .freeModeTotalDuration)
        try container.encode(freeModeBestDuration, forKey: .freeModeBestDuration)
    }

    // 定义编码键
    private enum CodingKeys: String, CodingKey {
        case lastUsedDuration
        case totalInterruptedCount
        case totalCompletedCount
        case bestStreak
        case currentStreak
        case firstSessionDate
        case lastSessionDate
        case achievedMilestoneIDs
        case freeModeSessionCount
        case freeModeTotalDuration
        case freeModeBestDuration
    }
}

public class DataStore: ObservableObject {
    public static let shared = DataStore()
    private let fileManager = FileManager.default

    // 发布属性，用于通知视图更新
    @Published public var habits: [Habit] = []
    @Published public var triggers: [Trigger] = []
    @Published public var resets: [Reset] = []
    @Published public var reviews: [Review] = []
    @Published public var habitReports: [HabitReport] = []
    @Published public var audioRecordings: [AudioRecordingModel] = []

    // 文件路径
    private var documentsDirectory: URL {
        fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    private var habitsURL: URL { documentsDirectory.appendingPathComponent("habits.json") }
    private var triggersURL: URL { documentsDirectory.appendingPathComponent("triggers.json") }
    private var resetsURL: URL { documentsDirectory.appendingPathComponent("resets.json") }
    private var reviewsURL: URL { documentsDirectory.appendingPathComponent("reviews.json") }
    private var habitReportsURL: URL { documentsDirectory.appendingPathComponent("habit_reports.json") }
    private var audioRecordingsURL: URL { documentsDirectory.appendingPathComponent("audio_recordings.json") }

    // X Moment 相关文件路径
    public var xMomentSessionsURL: URL { documentsDirectory.appendingPathComponent("xmoment_sessions.json") }
    public var xMomentUserStatsURL: URL { documentsDirectory.appendingPathComponent("xmoment_user_stats.json") }

    // 添加其他 JSON 文件的路径
    private var reasonsURL: URL { documentsDirectory.appendingPathComponent("reasons.json") }
    private var unavailableReasonsURL: URL { documentsDirectory.appendingPathComponent("unavailable_reasons.json") }
    private var disabledUseCasesURL: URL { documentsDirectory.appendingPathComponent("disabled_use_cases.json") }

    // MARK: - 缓存系统
    private var habitCache: [UUID: Habit] = [:]
    private var habitTriggersCache: [UUID: [Trigger]] = [:]
    private var habitResetsCache: [UUID: [Reset]] = [:]
    private var habitReviewsCache: [UUID: [Review]] = [:]

    // 添加其他 JSON 数据的缓存 (如果需要发布更新，也要用 @Published)
    private var reasonsCache: [String] = [] // 示例类型，根据实际需要调整
    private var unavailableReasonsCache: [String] = [] // 示例类型
    private var disabledUseCasesCache: [String] = [] // 示例类型

    // 重置所有缓存
    private func resetAllCaches() {
        habitCache = [:]
        habitTriggersCache = [:]
        habitResetsCache = [:]
        habitReviewsCache = [:]
        reasonsCache = []
        unavailableReasonsCache = []
        disabledUseCasesCache = []
    }

    // 添加加载完成状态和回调管理
    private var initialLoadComplete = false
    private let loadCompletionQueue = DispatchQueue(label: "com.xdolist.loadCompletionQueue")
    private var initialLoadCompletionHandlers: [() -> Void] = []

    private init() {
        // 在后台线程加载数据
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 先在后台线程加载所有数据
            let habits = self.loadHabitsData()
            let triggers = self.loadTriggersData()
            let resets = self.loadResetsData()
            let reviews = self.loadReviewsData()
            let habitReports = self.loadHabitReportsData()
            let audioRecordings = self.loadAudioRecordingsData()
            // 加载其他 JSON 文件 - 恢复加载
            let reasons = self.loadGenericJsonData(from: self.reasonsURL, type: [String].self, defaultValue: [])
            let unavailableReasons = self.loadGenericJsonData(from: self.unavailableReasonsURL, type: [String].self, defaultValue: [])
            let disabledUseCases = self.loadGenericJsonData(from: self.disabledUseCasesURL, type: [String].self, defaultValue: [])

            // 然后在主线程更新UI和缓存
            DispatchQueue.main.async {
                self.habits = habits
                self.triggers = triggers
                self.resets = resets
                self.reviews = reviews
                self.habitReports = habitReports
                self.audioRecordings = audioRecordings
                // 更新其他缓存 - 恢复更新
                self.reasonsCache = reasons
                self.unavailableReasonsCache = unavailableReasons
                self.disabledUseCasesCache = disabledUseCases

                // 重置依赖这些数据的其他缓存
                self.resetDependentCaches() // 可能需要一个单独的方法来重置依赖缓存

                // 标记并触发完成回调
                self.signalInitialLoadComplete()
            }
        }
    }

    // MARK: - 数据加载方法

    // 重置依赖于主要数据的缓存（不包括主要数据本身和简单JSON缓存）
    private func resetDependentCaches() {
        habitCache = [:]
        habitTriggersCache = [:]
        habitResetsCache = [:]
        habitReviewsCache = [:]
    }

    // 通用 JSON 加载方法
    private func loadGenericJsonData<T: Decodable>(from url: URL, type: T.Type, defaultValue: T) -> T {
        do {
            // 检查文件是否存在，这很重要，因为FileSystemErrorHandler可能刚创建它
            guard fileManager.fileExists(atPath: url.path) else {
                return defaultValue
            }
            let data = try Data(contentsOf: url)
            // 如果文件是空的（刚被创建），解码会失败，返回默认值
            guard !data.isEmpty else {
                 return defaultValue
            }
            return try JSONDecoder().decode(T.self, from: data)
        } catch {
            // 打印更详细的错误信息
            print("*** ERROR during loadGenericJsonData for \(url.lastPathComponent) (Thread: \(Thread.current)): \(error). Returning default value. ***")
        }
        return defaultValue
    }

    private func loadHabitsData() -> [Habit] { loadGenericJsonData(from: habitsURL, type: [Habit].self, defaultValue: []) }
    private func loadTriggersData() -> [Trigger] { loadGenericJsonData(from: triggersURL, type: [Trigger].self, defaultValue: []) }
    private func loadResetsData() -> [Reset] { loadGenericJsonData(from: resetsURL, type: [Reset].self, defaultValue: []) }
    private func loadReviewsData() -> [Review] { loadGenericJsonData(from: reviewsURL, type: [Review].self, defaultValue: []) }
    private func loadHabitReportsData() -> [HabitReport] { loadGenericJsonData(from: habitReportsURL, type: [HabitReport].self, defaultValue: []) }
    private func loadAudioRecordingsData() -> [AudioRecordingModel] { loadGenericJsonData(from: audioRecordingsURL, type: [AudioRecordingModel].self, defaultValue: []) }

    // MARK: - 数据保存方法

    public func saveHabits() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(habits)
            try data.write(to: habitsURL)
        } catch {
            print("保存习惯数据失败: \(error)")
        }
    }

    public func saveTriggers() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(triggers)
            try data.write(to: triggersURL)
        } catch {
            print("保存触发因素数据失败: \(error)")
        }
    }

    public func saveResets() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(resets)
            try data.write(to: resetsURL)
        } catch {
            print("保存重置数据失败: \(error)")
        }
    }

    public func saveReviews() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(reviews)
            try data.write(to: reviewsURL)
        } catch {
            print("保存回顾数据失败: \(error)")
        }
    }

    public func saveAllData() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            self?.saveHabits()
            self?.saveTriggers()
            self?.saveResets()
            self?.saveReviews()
            self?.saveHabitReports()
            self?.saveAudioRecordings()
            // Save other JSON data if modified
        }
    }

    public func saveHabitReports() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(habitReports)
            try data.write(to: habitReportsURL)
        } catch {
            print("保存习惯报告数据失败: \(error)")
        }
    }

    public func saveAudioRecordings() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(audioRecordings)
            try data.write(to: audioRecordingsURL)
        } catch {
            print("保存音频记录数据失败: \(error)")
        }
    }

    // MARK: - 数据操作方法

    public func getHabit(id: UUID) -> Habit? {
        // 先尝试从缓存中获取 - 提高性能
        if let cachedHabit = habitCache[id] {
            return cachedHabit
        }

        // 针对数组边界情况的快速检查
        if let firstHabit = habits.first, firstHabit.id == id {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitCache[id] = firstHabit
            }
            return firstHabit
        }

        if let lastHabit = habits.last, lastHabit.id == id {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitCache[id] = lastHabit
            }
            return lastHabit
        }

        // 常规查找
        if let habit = habits.first(where: { $0.id == id }) {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitCache[id] = habit
            }
            return habit
        }

        return nil
    }

    public func getTriggersForHabit(habitId: UUID) -> [Trigger] {
        // 先尝试从缓存获取
        if let cachedTriggers = habitTriggersCache[habitId] {
            return cachedTriggers
        }

        // 缓存未命中，执行过滤，正确处理可选类型
        let filteredTriggers = triggers.filter { trigger in
            if let triggerHabitId = trigger.habitId {
                return triggerHabitId == habitId
            }
            return false
        }

        // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
        DispatchQueue.main.async {
            self.habitTriggersCache[habitId] = filteredTriggers
        }
        return filteredTriggers
    }

    public func getResetsForHabit(habitId: UUID) -> [Reset] {
        // 先尝试从缓存获取
        if let cachedResets = habitResetsCache[habitId] {
            return cachedResets
        }

        // 缓存未命中，执行过滤，正确处理可选类型
        let filteredResets = resets.filter { reset in
            if let resetHabitId = reset.habitId {
                return resetHabitId == habitId
            }
            return false
        }

        // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
        DispatchQueue.main.async {
            self.habitResetsCache[habitId] = filteredResets
        }
        return filteredResets
    }

    public func getReviewsForHabit(habitId: UUID) -> [Review] {
        // 先尝试从缓存获取
        if let cachedReviews = habitReviewsCache[habitId] {
            return cachedReviews
        }

        // 缓存未命中，执行过滤，正确处理可选类型
        let filteredReviews = reviews.filter { review in
            if let reviewHabitId = review.habitId {
                return reviewHabitId == habitId
            }
            return false
        }

        // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
        DispatchQueue.main.async {
            self.habitReviewsCache[habitId] = filteredReviews
        }
        return filteredReviews
    }

    public func addHabit(_ habit: Habit) {
        if let index = habits.firstIndex(where: { $0.id == habit.id }) {
            habits[index] = habit
        } else {
            habits.append(habit)
        }

        // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
        DispatchQueue.main.async {
            self.habitCache[habit.id] = habit
        }

        saveHabits()
    }

    public func addTrigger(_ trigger: Trigger) {
        if let index = triggers.firstIndex(where: { $0.id == trigger.id }) {
            triggers[index] = trigger
        } else {
            triggers.append(trigger)
        }

        // 清除相关缓存 - 添加可选值检查
        if let habitId = trigger.habitId {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitTriggersCache[habitId] = nil
            }
        }

        saveTriggers()
    }

    public func addReset(_ reset: Reset) {
        if let index = resets.firstIndex(where: { $0.id == reset.id }) {
            resets[index] = reset
        } else {
            resets.append(reset)
        }

        // 清除相关缓存 - 添加可选值检查
        if let habitId = reset.habitId {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitResetsCache[habitId] = nil
            }
        }

        saveResets()
    }

    public func addReview(_ review: Review) {
        if let index = reviews.firstIndex(where: { $0.id == review.id }) {
            reviews[index] = review
        } else {
            reviews.append(review)
        }

        // 清除相关缓存 - 添加可选值检查
        if let habitId = review.habitId {
            // 使用主线程异步更新缓存，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitReviewsCache[habitId] = nil
            }
        }

        saveReviews()
    }

    public func deleteHabit(id: UUID) {
        if let index = habits.firstIndex(where: { $0.id == id }) {
            habits.remove(at: index)

            // 从缓存中异步移除，避免在视图更新过程中修改状态
            DispatchQueue.main.async {
                self.habitCache[id] = nil
                self.habitTriggersCache[id] = nil
                self.habitResetsCache[id] = nil
                self.habitReviewsCache[id] = nil
            }

            // 删除相关数据 - 正确处理可选类型
            triggers.removeAll { trigger in
                if let triggerHabitId = trigger.habitId {
                    return triggerHabitId == id
                }
                return false
            }

            resets.removeAll { reset in
                if let resetHabitId = reset.habitId {
                    return resetHabitId == id
                }
                return false
            }

            reviews.removeAll { review in
                if let reviewHabitId = review.habitId {
                    return reviewHabitId == id
                }
                return false
            }

            // 保存所有相关数据
            saveHabits()
            saveTriggers()
            saveResets()
            saveReviews()
        }
    }

    public func deleteTrigger(id: UUID) {
        triggers.removeAll(where: { $0.id == id })
        saveTriggers()
    }

    // MARK: - 扩展功能

    // 获取习惯的平均坚持天数
    public func getAverageStreakDays(for habit: Habit) -> Double {
        let habitResets = getResetsForHabit(habitId: habit.id)
        guard !habitResets.isEmpty else { return Double(habit.currentStreakDays) }

        let totalDays = Double(habitResets.reduce(0) { $0 + $1.streakDays })
        return totalDays / Double(habitResets.count)
    }

    // 生成习惯报告
    public func generateReport(for habit: Habit, from startDate: Date, to endDate: Date) -> HabitReport {
        let habitTriggers = getTriggersForHabit(habitId: habit.id)
        let filteredTriggers = habitTriggers.filter {
            guard let lastOccurred = $0.lastOccurred else { return false }
            return lastOccurred >= startDate && lastOccurred <= endDate
        }

        return HabitReport(
            habitId: habit.id,
            startDate: startDate,
            endDate: endDate,
            triggerCount: filteredTriggers.count,
            commonLocations: Dictionary(
                filteredTriggers.compactMap { $0.location }.map { ($0, 1) },
                uniquingKeysWith: +
            ),
            commonTriggerTimes: Dictionary(
                filteredTriggers.compactMap { $0.lastOccurred }
                    .map { Calendar.current.component(.hour, from: $0) }
                    .map { ($0, 1) },
                uniquingKeysWith: +
            )
        )
    }

    // 重置相关触发器缓存
    private func resetTriggerCache(for habitId: UUID) {
        habitTriggersCache[habitId] = nil
    }

    // 重置相关重置缓存
    private func resetResetCache(for habitId: UUID) {
        habitResetsCache[habitId] = nil
    }

    // 重置相关回顾缓存
    private func resetReviewCache(for habitId: UUID) {
        habitReviewsCache[habitId] = nil
    }

    // 获取特定日期前的坚持天数
    public func getStreakCount(for habit: Habit, upTo date: Date) -> Int {
        let resets = getResetsForHabit(habitId: habit.id)
            .filter { $0.date <= date }
            .sorted { $0.date > $1.date }

        guard let lastReset = resets.first else {
            return habit.currentStreakDays
        }

        return lastReset.streakDays
    }

    // MARK: - 加载完成处理

    // Method to register completion handlers
    public func onInitialLoadComplete(perform action: @escaping () -> Void) {
        loadCompletionQueue.sync {
            if initialLoadComplete {
                // Already complete, execute immediately on main thread
                DispatchQueue.main.async {
                    action()
                }
            } else {
                // Not yet complete, add to handlers
                initialLoadCompletionHandlers.append(action)
            }
        }
    }

    // Private method to signal completion and execute handlers
    private func signalInitialLoadComplete() {
        loadCompletionQueue.sync {
            guard !initialLoadComplete else { return }
            initialLoadComplete = true
            let handlers = initialLoadCompletionHandlers
            initialLoadCompletionHandlers = [] // Clear handlers
            // Execute handlers on main thread
            DispatchQueue.main.async {
                print("DataStore: Initial load signalled complete. Executing \(handlers.count) handlers.")
                handlers.forEach { $0() }
            }
        }
    }

    // MARK: - 获取其他 JSON 数据的方法 (示例)
    // 提供访问 reasons 等数据的方法，它们将从缓存读取
    public func getReasons() -> [String] {
        return reasonsCache
    }

    // MARK: - X Moment 相关方法

    public func saveXMomentSession(_ session: XMomentSession) {
        var sessions = getXMomentSessions()
        sessions.append(session)

        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(sessions)
            try data.write(to: xMomentSessionsURL)
            updateUserStatsAfterNewSession(session)
            objectWillChange.send()
        } catch {
            print("保存 X Moment 会话数据失败: \(error)")
        }
    }

    // 获取X Moment总次数
    public func getTotalXMomentCount() -> Int {
        let stats = getXMomentUserStats()
        return stats.totalCompletedCount + stats.totalInterruptedCount
    }

    // 获取总专注时长
    public func getTotalXMomentDuration() -> TimeInterval {
        return getXMomentSessions().reduce(0) { $0 + $1.duration }
    }

    // 获取总XU值
    public func getTotalXUnits() -> Double {
        return getXMomentSessions().reduce(0) { $0 + ($1.progressXU) }
    }

    // 获取当前连续专注天数
    public func getXMomentStreakDays() -> Int {
        return getXMomentUserStats().currentStreak
    }

    // 更新里程碑达成状态
    public func updateAchievedMilestones(_ milestoneIDs: Set<String>) {
        var stats = getXMomentUserStats()
        stats.achievedMilestoneIDs = milestoneIDs
        saveXMomentUserStats(stats)
    }

    // 获取每日提示
    public func getDailyTip() -> String {
        let tips = [
            "Tip 1",
            "Tip 2",
            "Tip 3",
            "Tip 4",
            "Tip 5"
        ]
        let randomIndex = Int.random(in: 0..<tips.count)
        return tips[randomIndex]
    }

    public func getXMomentSessions() -> [XMomentSession] {
        do {
            if FileManager.default.fileExists(atPath: xMomentSessionsURL.path) {
                let data = try Data(contentsOf: xMomentSessionsURL)
                return try JSONDecoder().decode([XMomentSession].self, from: data)
            } else {
                // 文件不存在时创建目录和空文件
                print("INFO: xmoment_sessions.json 不存在，正在创建新文件")
                let directory = xMomentSessionsURL.deletingLastPathComponent()
                if !FileManager.default.fileExists(atPath: directory.path) {
                    try FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true, attributes: nil)
                    print("已创建目录: \(directory.path)")
                }

                // 创建并保存空的会话列表
                let emptySessions: [XMomentSession] = []
                let encoder = JSONEncoder()
                encoder.outputFormatting = .prettyPrinted
                let emptyData = try encoder.encode(emptySessions)
                try emptyData.write(to: xMomentSessionsURL)
                print("已创建空的 xmoment_sessions.json 文件")
                return emptySessions
            }
        } catch {
            print("加载 X Moment 会话数据失败: \(error)")
        }
        return []
    }

    public func getXMomentUserStats() -> XMomentUserStats {
        do {
            if FileManager.default.fileExists(atPath: xMomentUserStatsURL.path) {
                let data = try Data(contentsOf: xMomentUserStatsURL)
                return try JSONDecoder().decode(XMomentUserStats.self, from: data)
            } else {
                // 文件不存在时创建目录和默认文件
                print("INFO: xmoment_user_stats.json 不存在，正在创建新文件")
                let directory = xMomentUserStatsURL.deletingLastPathComponent()
                if !FileManager.default.fileExists(atPath: directory.path) {
                    try FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true, attributes: nil)
                    print("已创建目录: \(directory.path)")
                }

                // 创建并保存默认的用户统计数据
                let newStats = XMomentUserStats(migrateFromUserDefaults: true)
                let encoder = JSONEncoder()
                encoder.outputFormatting = .prettyPrinted
                let newStatsData = try encoder.encode(newStats)
                try newStatsData.write(to: xMomentUserStatsURL)
                print("已创建默认的 xmoment_user_stats.json 文件")
                return newStats
            }
        } catch {
            print("加载 X Moment 用户统计数据失败: \(error)")
        }
        return XMomentUserStats()
    }

    public func saveXMomentUserStats(_ stats: XMomentUserStats) {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(stats)
            try data.write(to: xMomentUserStatsURL)
            objectWillChange.send()
        } catch {
            print("保存 X Moment 用户统计数据失败: \(error)")
        }
    }

    private func updateUserStatsAfterNewSession(_ session: XMomentSession) {
        var stats = getXMomentUserStats()

        // 更新首次/最后会话日期
        if stats.firstSessionDate == nil || (stats.firstSessionDate! > session.startTime) {
            stats.firstSessionDate = session.startTime
        }
        stats.lastSessionDate = session.endTime

        // 更新中断/完成次数
        if session.isInterrupted {
            stats.totalInterruptedCount += 1
        } else {
            stats.totalCompletedCount += 1
        }

        saveXMomentUserStats(stats)
    }

    // 获取自由模式的个人最佳时长，排除当前会话
    public func getFreeModePersonalBestDuration(excludingCurrent currentDuration: TimeInterval) -> TimeInterval {
        let sessions = getXMomentSessions()
        let freeModeSessions = sessions.filter { $0.isFreeMode && $0.duration != currentDuration }

        if let bestSession = freeModeSessions.max(by: { $0.duration < $1.duration }) {
            return bestSession.duration
        }

        return 0
    }

    // 添加习惯报告
    public func addHabitReport(_ report: HabitReport) {
        DispatchQueue.main.async {
            self.habitReports.append(report)
            self.saveHabitReports()
            self.objectWillChange.send()
        }
    }

    // 添加音频记录
    public func addAudioRecording(_ recording: AudioRecordingModel) {
        DispatchQueue.main.async {
            self.audioRecordings.append(recording)
            self.saveAudioRecordings()
            self.objectWillChange.send()
        }
    }

    // MARK: - iCloud备份与恢复支持

    /// 重新加载所有数据（用于iCloud恢复后）
    public func reloadAllData() {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // 先在后台线程加载所有数据
            let habits = self.loadHabitsData()
            let triggers = self.loadTriggersData()
            let resets = self.loadResetsData()
            let reviews = self.loadReviewsData()
            let habitReports = self.loadHabitReportsData()
            let audioRecordings = self.loadAudioRecordingsData()
            // 加载其他 JSON 文件 - 恢复加载
            let reasons = self.loadGenericJsonData(from: self.reasonsURL, type: [String].self, defaultValue: [])
            let unavailableReasons = self.loadGenericJsonData(from: self.unavailableReasonsURL, type: [String].self, defaultValue: [])
            let disabledUseCases = self.loadGenericJsonData(from: self.disabledUseCasesURL, type: [String].self, defaultValue: [])
            
            // 然后在主线程更新UI和缓存
            DispatchQueue.main.async {
                self.habits = habits
                self.triggers = triggers
                self.resets = resets
                self.reviews = reviews
                self.habitReports = habitReports
                self.audioRecordings = audioRecordings
                // 更新其他缓存 - 恢复更新
                self.reasonsCache = reasons
                self.unavailableReasonsCache = unavailableReasons
                self.disabledUseCasesCache = disabledUseCases
                
                // 重置所有缓存
                self.resetAllCaches()
                
                // 通知UI刷新
                self.objectWillChange.send()
                
                // 发送通知，数据已重新加载
                NotificationCenter.default.post(name: NSNotification.Name("DataStoreReloadCompleted"), object: nil)
            }
        }
    }
}