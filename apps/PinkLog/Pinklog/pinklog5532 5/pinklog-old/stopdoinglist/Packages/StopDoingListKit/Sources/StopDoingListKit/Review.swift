import Foundation

// 定义 MoodType 枚举，避免依赖 Trigger 模型
public enum ReviewMoodType: String, Codable, CaseIterable {
    case terrible = "非常糟糕"
    case bad = "糟糕"
    case neutral = "一般"
    case good = "不错"
    case great = "非常好"
}

public final class Review: Identifiable, Codable {
    public var id: UUID
    public var date: Date
    public var content: String
    public var mood: ReviewMoodType
    public var location: String?
    public var audioRecordingURL: String?
    public var audioDuration: Double?
    public var habitId: UUID?
    
    public init() {
        self.id = UUID()
        self.date = Date()
        self.content = ""
        self.mood = .neutral
    }
    
    public init(content: String, mood: ReviewMoodType = .neutral) {
        self.id = UUID()
        self.date = Date()
        self.content = content
        self.mood = mood
    }
    
    public init(id: UUID = UUID(), date: Date = Date(), content: String, mood: ReviewMoodType = .neutral, location: String? = nil, audioRecordingURL: String? = nil, audioDuration: Double? = nil, habitId: UUID? = nil) {
        self.id = id
        self.date = date
        self.content = content
        self.mood = mood
        self.location = location
        self.audioRecordingURL = audioRecordingURL
        self.audioDuration = audioDuration
        self.habitId = habitId
    }
}
