import SwiftUI
import PhotosUI
import UniformTypeIdentifiers

/// 图片选择处理类，提供更健壮的图片选择和处理功能
public class ImagePickerHandler: ObservableObject {
    @Published public var selectedItems: [PhotosPickerItem] = []
    @Published public var isLoading = false
    @Published public var loadedImages: [String] = []
    @Published public var errorMessage: String?
    
    private var loadingQueue = DispatchQueue(label: "com.stopdoinglist.imageloading", qos: .userInitiated, attributes: .concurrent)
    private var loadingTasks: [Task<Void, Never>] = []
    
    public init() {}
    
    deinit {
        // 取消所有加载任务
        for task in loadingTasks {
            task.cancel()
        }
    }
    
    /// 处理选择的图片项
    /// - Parameters:
    ///   - items: 选择的图片项数组
    ///   - maxSize: 最大图片尺寸（用于缩放）
    ///   - completion: 处理完成回调
    public func processItems(_ items: [PhotosPickerItem], maxSize: CGFloat = 1200, completion: (([String]) -> Void)? = nil) {
        // 取消之前的所有任务
        for task in loadingTasks {
            task.cancel()
        }
        loadingTasks.removeAll()
        
        guard !items.isEmpty else {
            completion?([])
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        let newTask = Task {
            var loadedFileNames: [String] = []
            var errors: [String] = []
            
            // 为了防止 PHPickerViewControllerDelegate 错误，我们处理一个项目后稍微延迟
            for (index, item) in items.enumerated() {
                if Task.isCancelled { break }
                
                do {
                    // 错误处理: 使用 try-catch 加载图片数据
                    if let imageData = try await loadImageData(from: item) {
                        if let uiImage = UIImage(data: imageData) {
                            // 调整图片大小
                            let resizedImage = self.resizeImageIfNeeded(uiImage, maxSize: maxSize)
                            
                            // 使用 ImageManager 保存图片
                            if let fileName = ImageManager.shared.saveImage(resizedImage) {
                                await MainActor.run {
                                    loadedFileNames.append(fileName)
                                }
                            }
                        }
                    }
                    
                    // 在项目之间添加小延迟，减轻 PhotosUI 框架压力
                    if index < items.count - 1 {
                        try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒延迟
                    }
                } catch {
                    print("图片加载错误: \(error.localizedDescription)")
                    errors.append(error.localizedDescription)
                    
                    // 如果错误包含 "PHPickerViewControllerDelegate"，添加额外延迟
                    if error.localizedDescription.contains("PHPicker") || 
                       error.localizedDescription.contains("connection") {
                        print("检测到 PhotosUI 相关错误，添加额外延迟")
                        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒额外延迟
                    }
                }
            }
            
            // 完成加载，更新状态
            await MainActor.run {
                self.isLoading = false
                self.loadedImages.append(contentsOf: loadedFileNames)
                
                if !errors.isEmpty {
                    // 只显示一个综合错误信息，包含失败的项目数量
                    self.errorMessage = "无法加载 \(errors.count) 张图片"
                }
                
                completion?(loadedFileNames)
            }
        }
        
        loadingTasks.append(newTask)
    }
    
    /// 取消当前所有加载
    public func cancelLoading() {
        for task in loadingTasks {
            task.cancel()
        }
        loadingTasks.removeAll()
        
        DispatchQueue.main.async {
            self.isLoading = false
        }
    }
    
    /// 删除所有已加载的图片
    public func deleteAllLoadedImages() {
        // 在后台线程删除文件
        DispatchQueue.global(qos: .utility).async {
            // 使用 ImageManager 删除所有图片
            for fileName in self.loadedImages {
                let _ = ImageManager.shared.deleteImage(fileName)
            }
            
            // 更新状态
            DispatchQueue.main.async {
                self.loadedImages.removeAll()
            }
        }
    }
    
    /// 调整图片尺寸
    /// - Parameters:
    ///   - image: 原始图片
    ///   - maxSize: 最大尺寸
    /// - Returns: 调整后的图片
    private func resizeImageIfNeeded(_ image: UIImage, maxSize: CGFloat) -> UIImage {
        let size = image.size
        
        // 如果图片尺寸已经小于最大尺寸，不需要调整
        if size.width <= maxSize && size.height <= maxSize {
            return image
        }
        
        // 计算缩放比例
        let widthRatio = maxSize / size.width
        let heightRatio = maxSize / size.height
        let scaleFactor = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: size.width * scaleFactor, height: size.height * scaleFactor)
        
        // 创建新的图片上下文
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: newSize))
        
        // 获取调整后的图片
        guard let resizedImage = UIGraphicsGetImageFromCurrentImageContext() else {
            return image // 如果调整失败，返回原始图片
        }
        
        return resizedImage
    }
    
    /// 从 PhotosPickerItem 加载图片数据
    /// - Parameter item: PhotosPickerItem
    /// - Returns: 图片数据
    private func loadImageData(from item: PhotosPickerItem) async throws -> Data? {
        // 尝试加载图片数据
        for attempt in 1...3 {
            do {
                if let data = try await item.loadTransferable(type: Data.self) {
                    return data
                }
                
                // 如果返回是 nil 但没有错误，等待一会再试
                try await Task.sleep(nanoseconds: 300_000_000) // 0.3秒
            } catch {
                print("图片加载尝试 \(attempt) 失败: \(error.localizedDescription)")
                
                if attempt == 3 {
                    throw error // 最后一次尝试失败，抛出错误
                }
                
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            }
        }
        
        return nil
    }
}

// MARK: - SwiftUI Previews

public struct PhotoPickerWithHandler: View {
    @StateObject private var pickerHandler = ImagePickerHandler()
    @State private var selectedItems: [PhotosPickerItem] = []
    
    public init() {}
    
    public var body: some View {
        VStack {
            // 图片选择器
            PhotosPicker(selection: $selectedItems, matching: .images) {
                Label("选择图片", systemImage: "photo.fill")
                    .padding()
                    .background(RoundedRectangle(cornerRadius: 8).stroke(Color.blue))
            }
            .onChange(of: selectedItems) { _, newItems in
                if !newItems.isEmpty {
                    // 处理选择的图片
                    pickerHandler.processItems(newItems) { fileNames in
                        print("加载完成: \(fileNames)")
                    }
                }
            }
            
            if pickerHandler.isLoading {
                ProgressView("加载图片中...")
                    .padding()
            }
            
            if let error = pickerHandler.errorMessage {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding()
            }
            
            // 图片预览
            ScrollView(.horizontal) {
                HStack(spacing: 12) {
                    ForEach(pickerHandler.loadedImages, id: \.self) { fileName in
                        if let image = ImageManager.shared.loadImage(from: fileName) {
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFill()
                                .frame(width: 100, height: 100)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                                )
                        }
                    }
                }
                .padding()
            }
            
            // 清除按钮
            if !pickerHandler.loadedImages.isEmpty {
                Button("清除所有图片") {
                    pickerHandler.deleteAllLoadedImages()
                    selectedItems = []
                }
                .foregroundColor(.red)
                .padding()
            }
        }
    }
}

#Preview {
    PhotoPickerWithHandler()
} 