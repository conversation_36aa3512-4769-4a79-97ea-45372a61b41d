// 这是一个空文件，用于解决构建冲突问题
import SwiftUI

#if canImport(UIKit)
import UIKit

// 定义一个空的协议，避免与主项目中的UIViewControllerRepresentable冲突
public protocol ShareSheetCompatible {}

// 简单的空结构体，避免与主项目代码冲突
public struct ShareSheetPlaceholder: ShareSheetCompatible {
    public let items: [Any]
    
    public init(items: [Any]) {
        self.items = items
        print("警告：正在使用已弃用的Utilities.ShareSheet。请改用主项目中的ActivityViewController")
    }
}

// 使用类型别名使旧代码能继续编译
public typealias ShareSheet = ShareSheetPlaceholder
#endif 