//
//  ContentView.swift
//  stopdoinglist
//
//  Created by thr33 on 2025/3/6.
//

import SwiftUI
import Charts
import StopDoingListKit


// 定义卡片颜色选项
struct CardColorOption {
    let name: String
    let color: Color
    let textColor: Color

    static let options: [CardColorOption] = [
        CardColorOption(name: "默认", color: Color.clear, textColor: .primary), // 使用Color.clear作为标记，实际显示时会替换为系统背景色
        CardColorOption(name: "雾蓝", color: Color(hex: "7d8bae"), textColor: .white),
        CardColorOption(name: "珊瑚橙", color: Color(hex: "e5857b"), textColor: .white),
        CardColorOption(name: "晴空蓝", color: Color(hex: "9ac5e5"), textColor: .black),
        CardColorOption(name: "暖阳金", color: Color(hex: "edce7a"), textColor: .black),
        CardColorOption(name: "玫瑰粉", color: Color(hex: "c98c9a"), textColor: .white),
        CardColorOption(name: "薰衣紫", color: Color(hex: "c6b4d8"), textColor: .black),
        CardColorOption(name: "浅云蓝", color: Color(hex: "cee0e6"), textColor: .black),
        CardColorOption(name: "奶油白", color: Color(hex: "f0eae0"), textColor: .black),
        CardColorOption(name: "蜜桃粉", color: Color(hex: "f5c0bf"), textColor: .black),
        CardColorOption(name: "云雾灰", color: Color(hex: "ead4d4"), textColor: .black),
        CardColorOption(name: "赤焰红", color: Color(hex: "e36255"), textColor: .white),
        CardColorOption(name: "橘霞", color: Color(hex: "ec9a86"), textColor: .black),
        CardColorOption(name: "湖水青", color: Color(hex: "a2c5c9"), textColor: .black),
        CardColorOption(name: "碧海青", color: Color(hex: "0b9b8a"), textColor: .white),
        CardColorOption(name: "樱花粉", color: Color(hex: "f596a1"), textColor: .black),
        CardColorOption(name: "晴空浅蓝", color: Color(hex: "c4e1f6"), textColor: .black),
        CardColorOption(name: "柠檬金", color: Color(hex: "f9c975"), textColor: .black),
        CardColorOption(name: "暮紫", color: Color(hex: "8e65ab"), textColor: .white),
        CardColorOption(name: "蔷薇紫", color: Color(hex: "dc94b0"), textColor: .black),
        CardColorOption(name: "浅紫粉", color: Color(hex: "e8c6de"), textColor: .black),
        CardColorOption(name: "麦田金", color: Color(hex: "d3ba83"), textColor: .black),
        CardColorOption(name: "晨雾白", color: Color(hex: "ebedd4"), textColor: .black),
        CardColorOption(name: "青柠绿", color: Color(hex: "bee24f"), textColor: .black),
        CardColorOption(name: "碧波青", color: Color(hex: "28baa6"), textColor: .white),
        CardColorOption(name: "蔷薇红", color: Color(hex: "bf2132"), textColor: .white),
        CardColorOption(name: "薰衣草紫", color: Color(hex: "c6b4d8"), textColor: .black),
        CardColorOption(name: "向日金", color: Color(hex: "e0c323"), textColor: .black),
        CardColorOption(name: "夜幕黑", color: Color(hex: "05060b"), textColor: .white),
        CardColorOption(name: "青草绿", color: Color(hex: "7c9f24"), textColor: .white),
        CardColorOption(name: "深邃蓝", color: Color(hex: "5b5897"), textColor: .white)
    ]
}

// Color extension for hex support - REMOVED from here
/* // MARK: - Removed Hex Color Extension
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
*/

struct ContentView: View {
    @StateObject private var dataStore = DataStore.shared
    @AppStorage("colorSchemeValue") private var colorSchemeValue: Int = 0
    @Environment(\.colorScheme) private var systemColorScheme

    var body: some View {
        HabitListView()
            .environmentObject(dataStore)
            .preferredColorScheme(getPreferredColorScheme())
            .onAppear {
                // 使用配置工具确保背景不透明
                UIConfigurationHelper.configureTabBarAppearance()
            }
    }

    private func getPreferredColorScheme() -> ColorScheme? {
        switch colorSchemeValue {
        case 1: return .light
        case 2: return .dark
        default: return nil
        }
    }
}

// 添加一个自定义修饰符来处理卡片背景和边框
struct CardBackgroundModifier: ViewModifier {
    let isTransparent: Bool
    let cardColor: Color
    let colorScheme: ColorScheme

    func body(content: Content) -> some View {
        return content
            .background(cardColor)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isTransparent ?
                            (colorScheme == .dark ? Color.white.opacity(0.15) : Color.black.opacity(0.1)) :
                            Color.clear,
                        lineWidth: 0.5
                    )
            )
    }
}

// 添加 apply 扩展方法
extension View {
    @ViewBuilder
    func apply<T: View>(@ViewBuilder transform: (Self) -> T) -> some View {
        transform(self)
    }
}

struct HabitRowView: View {
    let habit: Habit
    @State private var timeInterval: (days: String, hours: String, minutes: String, seconds: String) = ("000", "00", "00", "00")
    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(habit.name)
                .font(.headline)
            HStack(spacing: 4) {
                // Use the TimeUnitCard and ResetCountCard directly
                TimeUnitCard(value: timeInterval.days, unit: "days_unit_card")
                TimeUnitCard(value: timeInterval.hours, unit: "hours_unit_card")
                TimeUnitCard(value: timeInterval.minutes, unit: "minutes_unit_card")
                TimeUnitCard(value: timeInterval.seconds, unit: "seconds_unit_card")
                Spacer()
                    .frame(width: 16)
                ResetCountCard(count: habit.resetIds.count)
                Spacer(minLength: 0)
            }
        }
        .padding(.vertical, 4)
        .onAppear {
            updateTimeInterval()
        }
        .onReceive(timer) { _ in
            updateTimeInterval()
        }
    }

    private func updateTimeInterval() {
        let now = Date()
        let interval = now.timeIntervalSince(habit.lastReset ?? habit.createdAt)

        let totalDays = Int(interval) / (3600 * 24)
        let hours = Int(interval) / 3600 % 24
        let minutes = Int(interval) / 60 % 60
        let seconds = Int(interval) % 60

        timeInterval = (
            String(format: "%03d", totalDays),
            String(format: "%02d", hours),
            String(format: "%02d", minutes),
            String(format: "%02d", seconds)
        )
    }
}

// Define the TimeUnitCard and ResetCountCard components here
struct TimeUnitCard: View {
    let value: String
    let unit: String

    var body: some View {
        VStack(spacing: 1) {
            Text(NSLocalizedString(unit, comment: "Time unit"))
                .font(.caption2)
                .foregroundStyle(.secondary)
            Text(value)
                .font(.system(.callout, design: .monospaced))
                .fontWeight(.medium)
        }
        .frame(width: 45, height: 45)
        .background(Color(UIColor.systemGray6))
        .cornerRadius(8)
    }
}

struct ResetCountCard: View {
    let count: Int

    var body: some View {
        VStack(spacing: 1) {
            Text(NSLocalizedString("reset_text", comment: "Reset label"))
                .font(.caption2)
                .foregroundStyle(.white)
            Text(String(format: NSLocalizedString("times_count", comment: "Times count"), count))
                .font(.system(.callout, design: .monospaced))
                .fontWeight(.medium)
                .foregroundStyle(.white)
        }
        .frame(width: 45, height: 45)
        .background(count == 0 ? Color.green.opacity(0.8) : Color.red.opacity(0.8))
        .cornerRadius(8)
    }
}

#Preview {
    ContentView()
}
