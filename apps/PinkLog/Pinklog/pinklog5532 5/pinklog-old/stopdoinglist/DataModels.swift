import Foundation
import SwiftUI

// MARK: - 主要数据模型

struct Habit: Codable, Identifiable {
    var id: UUID
    var name: String
    var desc: String
    var startDate: Date
    var lastResetDate: Date
    var targetDays: Int
    var category: String
    var reminderTime: Date?
    var sortOrder: Int
    var colorStyle: Int
    var colorIndex: Int
    
    var resetIds: [UUID] = []
    var triggerIds: [UUID] = []
    var reviewIds: [UUID] = []
    
    // 计算属性
    var currentStreakDays: Int {
        Calendar.current.dateComponents([.day], from: lastResetDate, to: Date()).day ?? 0
    }
    
    var currentStreakTimeInterval: String {
        let now = Date()
        let interval = now.timeIntervalSince(lastResetDate)
        
        let hours = Int(interval) / 3600
        let minutes = Int(interval) / 60 % 60
        let seconds = Int(interval) % 60
        let milliseconds = Int((interval.truncatingRemainder(dividingBy: 1)) * 1000)
        
        return String(format: "%02d:%02d:%02d.%03d", hours, minutes, seconds, milliseconds)
    }
    
    var averageStreakDays: Double {
        guard resetIds.count > 0 else { return Double(currentStreakDays) }
        // 注意：这里需要通过 DataStore 获取 resets
        // 在实际使用时需要通过 DataStore 计算
        return 0.0
    }
    
    var progress: Double {
        guard targetDays > 0 else { return 0 }
        return min(Double(currentStreakDays) / Double(targetDays), 1.0)
    }
}

struct Reset: Codable, Identifiable {
    var id: UUID
    var habitId: UUID
    var date: Date
    var streakDays: Int
    var note: String?
}

struct Trigger: Codable, Identifiable {
    var id: UUID
    var habitId: UUID
    var triggerType: TriggerType
    var triggerDesc: String
    var frequency: Int
    var lastOccurred: Date
    var notes: String?
    var mood: MoodType?
    var location: String?
    var alternativeBehaviors: [String] = []
    var audioRecordingPath: String?
    var audioDuration: Double?
    var audioWaveformData: [Double]?
    var imagePaths: [String] = []
    var tags: [String] = []
    
    enum TriggerType: String, Codable, CaseIterable {
        case emotion = "trigger_type_emotion"
        case environment = "trigger_type_environment"
        case time = "trigger_type_time"
        case social = "trigger_type_social"
        case physical = "trigger_type_physical"
        
        var localizedName: String {
            return NSLocalizedString(self.rawValue, comment: "Trigger type")
        }
    }
    
    enum MoodType: String, Codable, CaseIterable {
        case great = "mood_type_great"
        case good = "mood_type_good"
        case neutral = "mood_type_neutral"
        case bad = "mood_type_bad"
        case terrible = "mood_type_terrible"
        
        var localizedName: String {
            return NSLocalizedString(self.rawValue, comment: "Mood type")
        }
        
        var icon: String {
            switch self {
            case .great: return "😊"
            case .good: return "🙂"
            case .neutral: return "😐"
            case .bad: return "😟"
            case .terrible: return "😢"
            }
        }
    }
}

struct Review: Codable, Identifiable {
    var id: UUID
    var habitId: UUID
    var date: Date
    var content: String
}

struct HabitReport: Codable {
    let habit: Habit
    let startDate: Date
    let endDate: Date
    let triggerCount: Int
    let commonLocations: [String: Int]
    let commonTriggerTimes: [Int: Int]
    
    var durationInDays: Int {
        Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
    }
    
    var successRate: Double {
        guard durationInDays > 0 else { return 0 }
        return Double(durationInDays - triggerCount) / Double(durationInDays)
    }
} 