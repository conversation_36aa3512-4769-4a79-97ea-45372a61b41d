import SwiftUI
import StopDoingListKit
import AudioToolbox
import UIKit
import AVFoundation
import Combine
import SpriteKit
import StoreKit




// Add wrapper to make UUID identifiable
struct IdentifiableUUID: Identifiable {
    let id: UUID

    init(_ uuid: UUID) {
        self.id = uuid
    }
}

// Define Layout options
enum LayoutOption { // <-- Add enum
    case grid, list
}

struct HabitListView: View {
    @EnvironmentObject private var dataStore: DataStore
    @State private var showingMenu = false
    @Environment(\.colorScheme) private var colorScheme
    @State private var sortOption: SortOption = .successRate
    @State private var layoutOption: LayoutOption = .grid
    @State private var searchText: String = ""
    @State private var isSearching: Bool = false
    @State private var searchFieldOpacity: Double = 0
    @State private var searchFieldScale: CGFloat = 0.96
    @State private var searchButtonRotation: Double = 0
    @State private var searchButtonScale: CGFloat = 1.0
    @FocusState private var isSearchFieldFocused: Bool
    @State private var showingCompletionOptions = false

    // X Moment 相关状态
    @State private var xMomentState: WillpowerTugOfWar.XMomentState = .inactive
    @State private var isChoosingTime: Bool = false
    @State private var isOverclocked: Bool = false
    @State private var xMomentIsFreeMode: Bool = false
    @State private var showingPurchasePrompt = false

    // 控制背景元素效果的状态
    @State private var backgroundElementsOpacity: Double = 1.0
    @State private var backgroundElementsBlurRadius: CGFloat = 0
    @State private var showOverclockEffect: Bool = false

    // 用于缓存排序结果
    @State private var cachedHabits: [Habit] = []
    @State private var lastSortOption: SortOption = .successRate
    @State private var lastHabitsCount: Int = 0

    // Sort options
    enum SortOption: String, CaseIterable {
        case successRate = "success_rate"
        case dateAdded = "date_added"
        case customOrder = "custom_order"
    }

    // 使用计算属性来获取排序后的习惯列表
    private var sortedHabits: [Habit] {
        // 如果缓存有效，则使用缓存
        if lastSortOption == sortOption && lastHabitsCount == dataStore.habits.count && !cachedHabits.isEmpty {
            return cachedHabits
        }

        // 如果缓存无效，则返回一个临时排序结果
        switch sortOption {
        case .successRate:
            return dataStore.habits.sorted {
                dataStore.getAverageStreakDays(for: $0) > dataStore.getAverageStreakDays(for: $1)
            }
        case .dateAdded:
            return dataStore.habits.sorted { $0.createdAt > $1.createdAt }
        case .customOrder:
            return dataStore.habits
        }
    }

    // 过滤搜索结果
    private var filteredHabits: [Habit] {
        if searchText.isEmpty {
            return sortedHabits
        } else {
            return sortedHabits.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }

    private var displayHabits: [Habit] {
        return filteredHabits
    }

    // 判断是否显示搜索框
    private var shouldShowSearch: Bool {
        return dataStore.habits.count > 19
    }

    private func updateSortOrders() {
        for habit in sortedHabits {
            dataStore.addHabit(habit)
        }
    }

    // 预加载习惯详情数据
    private func preloadHabitDetails(for habit: Habit) {
        DispatchQueue.global(qos: .userInitiated).async {
            _ = dataStore.getResetsForHabit(habitId: habit.id)
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                Color.black.ignoresSafeArea()
                
                Color(UIColor.systemBackground)
                    .ignoresSafeArea()

                backgroundEffectsView

                mainScrollView
            }
                .sheet(isPresented: $showingMenu) {
                    HabitListMenuView()
                }
                .sheet(isPresented: $showingPurchasePrompt) {
                    PurchasePromptView()
                }
                .onAppear {
                    NotificationCenter.default.addObserver(forName: NSNotification.Name("ShowPurchasePrompt"), object: nil, queue: .main) { _ in
                        showingPurchasePrompt = true
                    }
                }
                .onDisappear {
                    NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
                }
        }
    }

    // 主要内容视图
    private var mainContentView: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            Color(UIColor.systemBackground)
                .ignoresSafeArea()

            backgroundEffectsView

            mainScrollView
        }
    }

    // 背景效果视图
    private var backgroundEffectsView: some View {
        Group {
            if xMomentState != .inactive {
                // 始终使用黑色背景作为基础层，确保不会闪烁
                Color.black
                    .ignoresSafeArea()

                // 如果是超频状态，在黑色背景上叠加深空效果
                if showOverclockEffect {
                    XDoOverclockedDeepSpaceView()
                }
            }
        }
    }

    // 主要滚动视图内容
    private var mainScrollContent: some View {
        VStack(spacing: 24) {
            // Top section - Now controlled by opacity/interaction
            headerSection
                .opacity(backgroundElementsOpacity)
                .allowsHitTesting(xMomentState == .inactive)
                .padding(.horizontal)

            // WillpowerTugOfWar Card
            WillpowerTugOfWar(
                showingCompletionOptions: $showingCompletionOptions,
                xMomentState: $xMomentState,
                isChoosingTime: $isChoosingTime,
                isOverclocked: $isOverclocked,
                xMomentIsFreeMode: $xMomentIsFreeMode
            )
            .onTapGesture {
                if !dataStore.canUseXMoment() {
                    NotificationCenter.default.post(name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray5) : Color(UIColor.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
            )
            .padding(.horizontal)

            // 当日会话记录区域 - 只在非活动状态显示
            if xMomentState == .inactive {
                DailyXMomentSessionsView()
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray5) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                    )
                    .padding(.horizontal)
            }
        }
        .padding(.bottom, 30)
    }

    // 主要滚动视图
    private var mainScrollView: some View {
        ScrollView(.vertical, showsIndicators: true) {
            mainScrollContent
        }
        .scrollBounceBehavior(.automatic)
        .simultaneousGesture(
            DragGesture().onChanged { _ in
                if isSearchFieldFocused {
                    isSearchFieldFocused = false
                }
            }
        )
        .onAppear {
            for habit in sortedHabits.prefix(4) {
                preloadHabitDetails(for: habit)
            }
        }
        .onChange(of: dataStore.habits.count) { _, _ in
        }
        .onChange(of: sortOption) { _, newValue in
            if newValue == .customOrder {
                updateSortOrders()
            }
        }
        .background(stateChangeListeners)
    }

    // 应用状态变化监听器
    private var stateChangeListeners: some View {
        EmptyView()
            .onChange(of: xMomentState) { _, newState in
                updateBackgroundEffects(newState: newState, choosingTime: isChoosingTime, overclocked: isOverclocked)
            }
            .onChange(of: isChoosingTime) { _, newChoosingTime in
                updateBackgroundEffects(newState: xMomentState, choosingTime: newChoosingTime, overclocked: isOverclocked)
            }
            .onChange(of: isOverclocked) { _, newOverclocked in
                updateBackgroundEffects(newState: xMomentState, choosingTime: isChoosingTime, overclocked: newOverclocked)
            }
    }



    // Extract the header section
    private var headerSection: some View {
        VStack(spacing: 10) {
        HStack {
            Text("x_do_list")
                .font(.system(size: 36, weight: .bold, design: .rounded))
                .foregroundColor(.primary)
            Spacer()
                Button(action: { showingMenu = true }) {
                        ZStack {
                            Circle()
                                .fill(Color.secondary.opacity(0.15))
                                .frame(width: 28, height: 28)
                                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 0)
                    Image(systemName: "ellipsis")
                                .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.secondary)
                        }
                    }
                    .buttonStyle(ScaleButtonStyle())
        }
        .padding(.horizontal)
        .padding(.top, 16)
        }
    }

    // 切换搜索状态的方法
    private func toggleSearch() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            searchButtonRotation = isSearching ? 0 : 90
        }
        searchButtonScale = 0.9
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            searchButtonScale = 1.0
        }
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        if isSearching {
            isSearchFieldFocused = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    isSearching = false
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    searchText = ""
                }
            }
        } else {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                isSearching = true
            }
        }
    }

   



    // 重写：统一处理背景效果的函数，包含新的深空效果逻辑
    private func updateBackgroundEffects(newState: WillpowerTugOfWar.XMomentState, choosingTime: Bool, overclocked: Bool) {
        // 当xmoment活动时，始终隐藏普通元素
        let targetOpacity: Double = (newState != .inactive) ? 0.0 : 1.0

        // 只有在超频状态下才显示深空效果，但始终保持黑色背景
        let showDeepSpace: Bool = (newState != .inactive) && overclocked

        // 使用更长的动画时间，确保过渡更平滑
        withAnimation(.linear(duration: 0.8)) {
            backgroundElementsOpacity = targetOpacity
            backgroundElementsBlurRadius = 0.0 // 始终不使用模糊效果
        }

        // 单独处理深空效果的显示，使用稍微延迟的动画，确保黑色背景先显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.linear(duration: 0.7)) {
                showOverclockEffect = showDeepSpace
            }
        }
    }

 
}





// 菜单视图
struct HabitListMenuView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("colorSchemeValue") private var colorSchemeValue: Int = 0
    @AppStorage("languageCode") private var languageCode: String = "zh-Hans" // Add language storage
    @State private var showingAboutXDo = false

    // 添加iCloud备份视图导航状态
    @State private var showingCloudBackupView = false
    // 添加购买提示状态
    @State private var showingPurchasePrompt = false

    // 菜单相关状态



    var body: some View {
        NavigationStack {
            List {
                // 专业版升级部分 - 在顶部突出位置添加
                if !StoreManager.shared.isPremium {
                    Section {
                        Button(action: {
                            showingPurchasePrompt = true
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(NSLocalizedString("upgrade_to_pro", comment: "升级到专业版"))
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                    
                                    Text(NSLocalizedString("unlock_premium_features", comment: "解锁所有高级功能"))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                ZStack {
                                    Circle()
                                        .fill(LinearGradient(
                                            gradient: Gradient(colors: [.yellow, .orange]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ))
                                        .frame(width: 36, height: 36)
                                    
                                    Image(systemName: "crown.fill")
                                        .foregroundColor(.white)
                                        .font(.system(size: 16, weight: .semibold))
                                }
                            }
                            .padding(.vertical, 6)
                            .listRowBackground(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color(UIColor.systemBackground), Color.blue.opacity(0.1)]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                        }
                    }
                }

                // 设置选项
                Section(NSLocalizedString("settings", comment: "Settings section")) {
                    Picker(NSLocalizedString("appearance", comment: "Appearance mode"), selection: $colorSchemeValue) {
                        Text(NSLocalizedString("system", comment: "System appearance")).tag(0)
                        Text(NSLocalizedString("light", comment: "Light mode")).tag(1)
                        Text(NSLocalizedString("dark", comment: "Dark mode")).tag(2)
                    }

                    // Add language picker
                    Picker(NSLocalizedString("language", comment: "Language selection"), selection: $languageCode) {
                        Text(NSLocalizedString("simplified_chinese", comment: "")).tag("zh-Hans")
                        Text(NSLocalizedString("traditional_chinese", comment: "")).tag("zh-Hant")
                        Text(NSLocalizedString("english", comment: "")).tag("en")
                    }
                    .onChange(of: languageCode) { oldValue, newValue in
                        if oldValue != newValue {
                            // Set the new language
                            UserDefaults.standard.set([newValue], forKey: "AppleLanguages")
                            UserDefaults.standard.synchronize()

                            // Show alert about app restart
                            // In a real app, you might want to restart the app programmatically
                            // or show a more sophisticated UI for language change
                        }
                    }

                    Button(action: {}) {
                        Label(NSLocalizedString("notification_settings", comment: "Notification settings"), systemImage: "bell")
                    }

                    NavigationLink(destination: PrivacyPolicyContent().navigationTitle(NSLocalizedString("privacy_policy", comment: "Privacy Policy"))) {
                        Label(NSLocalizedString("privacy_policy", comment: "Privacy Policy"), systemImage: "lock")
                    }
                }

                // Data management section
                Section(NSLocalizedString("data_management", comment: "Data management section")) {
                    // 添加iCloud备份选项
                    Group {
                        if StoreManager.shared.isPremium {
                            NavigationLink(destination: CloudBackupView().navigationTitle(NSLocalizedString("icloud_backup", comment: "iCloud Backup"))) {
                                Label(NSLocalizedString("icloud_backup", comment: "iCloud Backup"), systemImage: "icloud")
                            }
                        } else {
                            Button(action: {
                                // 直接显示购买页面，而不是发送通知
                                showingPurchasePrompt = true
                            }) {
                                HStack {
                                    Label(NSLocalizedString("icloud_backup", comment: "iCloud Backup"), systemImage: "icloud")
                                    Spacer()
                                    Image(systemName: "crown.fill")
                                        .foregroundColor(.yellow)
                                        .font(.caption)
                                }
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }



                
            }
            .navigationTitle(NSLocalizedString("settings_and_options", comment: "Settings and options"))
            .navigationBarTitleDisplayMode(.inline)
            .safeAreaInset(edge: .top) {
                HStack {
                    Button(NSLocalizedString("done", comment: "Done button")) {
                        dismiss()
                    }
                }
                .padding(.trailing)
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
        .sheet(isPresented: $showingAboutXDo) {
            AboutXDoView()
        }
        .sheet(isPresented: $showingPurchasePrompt) {
            PurchasePromptView()
        }


    }
}

// 意志力平衡组件 - 动态可视化用户的意志力与诱惑之间的角力
struct WillpowerTugOfWar: View {
    @EnvironmentObject private var dataStore: DataStore
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var themeManager = XMomentThemeManager()
    @StateObject private var templateManager = TimingTemplateManager()
    @Binding var showingCompletionOptions: Bool
    @Binding var xMomentState: XMomentState
    @Binding var isChoosingTime: Bool
    @Binding var isOverclocked: Bool
    @Binding var xMomentIsFreeMode: Bool

    // 状态变量
    @State private var balancePosition: Double = 0 // 平衡点位置，-1到1之间
    @State private var showDetail: Bool = false
    @State private var isAnimating: Bool = false // 控制动画状态
    @State private var ringRotation: Double = 0 // 环形旋转角度
    @State private var pulseScale: CGFloat = 1.0 // 脉冲缩放

    // 新增：控制卡片区域偏移量
    @State private var cardAreaOffset: CGFloat = 0

    // 其他内部状态保持不变
    @State private var xMomentDuration: TimeInterval = 25 * 60 // 默认25分钟
    @State private var xMomentMenuScale: CGFloat = 1.0 // 新增：控制菜单按钮缩放动画
    @State private var xMomentMenuRotation: Double = 0 // 新增：旋转状态
    @State private var showingXMomentHorizontalMenu: Bool = false // 重命名状态变量
    @State private var menuScale: CGFloat = 0.96 // 新增：菜单缩放动画
    @State private var menuOpacity: Double = 0 // 新增：菜单透明度动画
    @State private var menuOffset: CGFloat = -10 // 新增：菜单偏移量

    // 新增：控制菜单项的交错出现效果
    @State private var menuItemsAppeared = false
    @State private var menuItem1Opacity: Double = 0
    @State private var menuItem2Opacity: Double = 0
    @State private var menuItem3Opacity: Double = 0
    @State private var menuItem4Opacity: Double = 0
    @State private var lastUsedXMomentDuration: TimeInterval = 25 * 60 // 记住上次使用的时长
    @State private var xMomentStartTime: Date? = nil
    @State private var xMomentProgress: Double = 0.0
    @State private var showingXMomentInfo: Bool = false
    @State private var showingCancelConfirm: Bool = false
    @State private var sessionToRecord: XMomentSession? = nil
    @State private var showSaveFeedback: Bool = false
    @State private var showingThemeSettings: Bool = false // 主题设置显示状态
    @State private var showingTemplateSettings: Bool = false // 计时模板设置显示状态
    @State private var showingXMomentStats: Bool = false // XMoment统计显示状态

    // 超频状态相关变量
    @State private var overclockBonusFactor: Double = 1.0
    @State private var overclockDuration: TimeInterval = 0

    // 自由模式里程碑相关变量
    // @State private var reachedMilestones: Set<Int> = [] // 已达成的分钟里程碑
    // @State private var showingMilestoneMessage: Bool = false // 是否显示里程碑消息
    // @State private var currentMilestoneMessage: String = "" // 当前里程碑消息
    // @State private var milestoneOpacity: Double = 0 // 里程碑消息透明度
    // @State private var lastCheckedMinute: Int = 0 // 上次检查的分钟数
    // @State private var milestoneCheckTimer: Timer? = nil // 里程碑检测定时器

    // 新增：Tips 管理器
    @StateObject private var tipsManager = TipsManager()

    // 新增：控制庆祝动画的状态
    @State private var showCelebration = false

    // 枚举定义 X Moment 状态 (保持在这里，因为父视图也需要引用它)
    enum XMomentState {
        case inactive         // 未激活
        case inProgress       // 正在进行中
        case completed        // 已完成 (计时结束，等待用户交互)
    }

    // 新的意志力分数算法（仅基于最近一周XMoment数据）
    private var willpowerScore: Double {
        // 以专注次数、总时长、平均时长、超频次数、超频时长、中断次数为基础
        let stats = weekXMomentStats
        // 你可以根据实际需求调整权重和算法
        // 这里给出一个简单的线性加权示例
        let focusScore = min(Double(stats.count) * 8, 40) // 一周专注次数，每次8分，最多40分
        let durationScore = min(stats.totalDuration / 3600.0 * 2, 20) // 总时长每小时2分，最多20分
        let avgScore = min(stats.avgDuration / 1800.0 * 10, 10) // 平均时长每30分钟10分，最多10分
        let overclockScore = min(Double(stats.overclockCount) * 3, 12) // 超频次数每次3分，最多12分
        let overclockDurationScore = min(stats.overclockDuration / 1800.0 * 8, 8) // 超频时长每30分钟8分，最多8分
        let interruptionPenalty = min(Double(stats.interruptedCount) * 5, 25) // 每次中断扣5分，最多扣25分
        let raw = focusScore + durationScore + avgScore + overclockScore + overclockDurationScore - interruptionPenalty
        return max(0, min(88, raw))
    }

    // 新的状态描述
    private var statusDescription: String {
        let score = willpowerScore
        if weekXMomentStats.count == 0 {
            return NSLocalizedString("start_journey", comment: "")
        }
        if score >= 70 {
            return NSLocalizedString("exceptional_willpower", comment: "")
        } else if score >= 55 {
            return NSLocalizedString("strong_willpower", comment: "")
        } else if score >= 40 {
            return NSLocalizedString("growing_willpower", comment: "")
        } else if score >= 25 {
            return NSLocalizedString("balanced_energy", comment: "")
        } else if score >= 10 {
            return NSLocalizedString("increasing_temptation", comment: "")
        } else {
            return NSLocalizedString("critical_imbalance", comment: "")
        }
    }

    // 新的可视化分数（-1~1）
    private var forceBalance: Double {
        // 直接用新分数线性映射
        return (willpowerScore - 44) / 44 // 44为中点
    }

    // 获取状态颜色 - 更细致的色彩过渡
    private var statusColor: Color {
        let score = willpowerScore

        if score >= 75 { return .green }
        if score >= 60 { return .mint }
        if score >= 45 { return .yellow }
        if score >= 30 { return .orange }
        return .red
    }

    // 获取意志力百分比
    private var willpowerPercentage: Double {
        // 直接使用计算的意志力得分作为百分比
        return willpowerScore / 100
    }

    // 获取诱惑力百分比
    private var temptationPercentage: Double {
        // 直接使用计算的诱惑力得分作为百分比
        return willpowerScore / 100
    }

    // 新增：最近一周XMoment统计
    private var weekXMomentStats: (count: Int, totalDuration: TimeInterval, avgDuration: TimeInterval, overclockCount: Int, overclockDuration: TimeInterval, interruptedCount: Int) {
        let calendar = Calendar.current
        let now = Date()
        guard let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) else {
            return (0, 0, 0, 0, 0, 0)
        }
        let sessions = dataStore.getXMomentSessionsUnambiguous().filter { $0.startTime >= weekAgo && $0.startTime <= now }
        let count = sessions.count
        let totalDuration = sessions.reduce(0) { $0 + $1.duration }
        let avgDuration = count > 0 ? totalDuration / Double(count) : 0
        let overclockSessions = sessions.filter { $0.overclockFactor > 1.0 }
        let overclockCount = overclockSessions.count
        let overclockDuration = overclockSessions.reduce(0) { $0 + $1.overclockDuration }
        let interruptedCount = sessions.filter { $0.isInterrupted }.count
        return (count, totalDuration, avgDuration, overclockCount, overclockDuration, interruptedCount)
    }

    // 添加缺失的body属性实现
    var body: some View {
        VStack(spacing: 0) {
            // 标题部分 - 重构为VStack包含菜单
            VStack(spacing: 0) {
                // 标题行
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack(spacing: 6) {
                            // 根据 xMomentState 动态显示标题
                            if xMomentState == .inactive {
                                Text("willpower_tug_of_war")
                                    .font(.system(size: 18, weight: .bold, design: .rounded))
                                    .foregroundColor(.primary)
                            } else {
                                Text("X MOMENT")
                                    .font(.system(size: 18, weight: .bold, design: .rounded))
                                    .foregroundColor(.primary)
                            }

                            // 脉冲圆点指示器 (仅在 inactive 状态下显示)
                            if xMomentState == .inactive {
                                Circle()
                                    .fill(statusColor)
                                    .frame(width: 8, height: 8)
                                    .scaleEffect(pulseScale)
                                    .animation(
                                        Animation.easeInOut(duration: 1.0)
                                            .repeatForever(autoreverses: true),
                                        value: pulseScale
                                    )
                            }
                        }

                        // 在标题下方显示 Tips
                        if xMomentState != .inactive {
                            // X Moment 模式下：显示 Tip
                            if let tip = tipsManager.currentTip {
                                Text(tip)
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.secondary)
                                    .lineLimit(2)
                                    .fixedSize(horizontal: false, vertical: true) // 允许多行
                                    .transition(.opacity.animation(.easeInOut))
                                    .padding(.top, 2)
                            } else {
                                // 如果没有 tip，可以显示一个占位符或空视图
                                 Spacer().frame(height: 15) // 保持高度一致性
                            }
                        } else {
                            // 非 X Moment 模式下：显示状态描述
                            Text(statusDescription)
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(statusColor)
                                .lineLimit(1)
                                .transition(.opacity.animation(.easeInOut))
                        }
                    }

                    Spacer()

                    // 根据状态显示不同的右上角按钮
                    if xMomentState == .inactive {
                        // --- 将两个按钮放在 HStack 中，设置间距 --- START ---
                        HStack(spacing: 12) { // 设置合适的间距，例如 12
                            // X Moment 启动按钮 (无独立背景)
                            let willpowerScore = willpowerScore
                            let isHighWillpower = willpowerScore > 85
                            let opacityLevel = isHighWillpower ? 1.0 : 0.7
                            let scaleEffect = isHighWillpower ? 1.05 : 1.0

                            Button(action: {
                                handleLongPressStart()
                            }) {
                                XIconView(showBreathingEffect: isHighWillpower)
                                    .font(.system(size: 12))
                                    .foregroundColor(.primary.opacity(opacityLevel))
                                    .frame(width: 32, height: 32)
                                    .contentShape(Rectangle())
                            }
                            .buttonStyle(ScaleButtonStyle(scaleFactor: 0.95))
                            .scaleEffect(scaleEffect)
                            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isHighWillpower)

                            // 信息按钮 (可以选择恢复独立背景，或者保持无背景)
                            Button(action: { showDetail = true }) {
                                Image(systemName: "info.circle")
                                    .font(.system(size: 16))
                                    .foregroundColor(.secondary)
                                    .frame(width: 32, height: 32)
                                    .contentShape(Rectangle())
                                // 可选: 添加背景让它更像之前的独立按钮
                                // .background(
                                //     Circle()
                                //         .fill(Color.secondary.opacity(0.15))
                                // )
                            }
                            .buttonStyle(ScaleButtonStyle(scaleFactor: 0.95))
                        }
                        // --- HStack 结束 ---
                    } else {
                        // X Moment 模式下的按钮 (替换 Menu)
                        Button {
                            // 触觉反馈 - 提升触觉反馈强度
                            let generator = UIImpactFeedbackGenerator(style: .soft)
                            generator.impactOccurred(intensity: 1.0)

                            // 切换菜单状态
                            showingXMomentHorizontalMenu.toggle()

                            // 图标旋转动画 - 使用更优雅的旋转速度
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                                xMomentMenuRotation += showingXMomentHorizontalMenu ? 45 : -45
                            }

                            // 图标缩放动画 - 更加精细的缩放效果
                            xMomentMenuScale = 0.85

                            // 使用带延迟的弹性动画恢复原始大小
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                                withAnimation(.spring(response: 0.5, dampingFraction: 0.5)) {
                                    xMomentMenuScale = 1.0
                                }
                            }
                        } label: {
                            ZStack {
                                Circle()
                                    .fill(Color.secondary.opacity(0.15))
                                    .frame(width: 32, height: 32)

                                XIconView(showBreathingEffect: isOverclocked) // 使用新的 X 图标，并在超频模式下显示吸气效果
                                    .foregroundColor(.secondary)
                                    .font(.system(size: 12)) // 参考大小
                                    .rotationEffect(.degrees(xMomentMenuRotation)) // 应用旋转
                            }
                            .scaleEffect(xMomentMenuScale)
                            .animation(.spring(response: 0.35, dampingFraction: 0.7, blendDuration: 0), value: xMomentMenuScale)
                        }
                        .buttonStyle(ScaleButtonStyle()) // 应用点击缩放效果
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)

                // 在 X MOMENT 模式下，显示横向菜单
                if xMomentState != .inactive && showingXMomentHorizontalMenu {
                    HStack(spacing: 15) { // 调整按钮间距
                        // 使用不同的透明度来实现交错出现效果
                        // Stats Button
                        Button {
                            // 触觉反馈 - 提升触觉反馈强度
                            let generator = UIImpactFeedbackGenerator(style: .soft)
                            generator.impactOccurred(intensity: 1.0)

                            // 立即显示统计视图，不等待菜单动画完成
                            showingXMomentStats = true

                            // 图标旋转和菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                                xMomentMenuRotation -= 45
                            }

                            // 菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                                menuOpacity = 0
                                menuScale = 0.95
                                menuOffset = -15
                                cardAreaOffset = 0
                                showingXMomentHorizontalMenu = false
                                menuItemsAppeared = false
                                menuItem1Opacity = 0
                                menuItem2Opacity = 0
                                menuItem3Opacity = 0
                                menuItem4Opacity = 0
                            }
                        } label: {
                             Image(systemName: "chart.bar.fill")
                        }
                        .buttonStyle(XMomentHorizontalButtonStyle())
                        .opacity(menuItem1Opacity)

                        // Template Button
                        Button {
                            // 触觉反馈 - 提升触觉反馈强度
                            let generator = UIImpactFeedbackGenerator(style: .soft)
                            generator.impactOccurred(intensity: 1.0)

                            // 立即显示模板设置，不等待菜单动画完成
                            showingTemplateSettings = true

                            // 图标旋转和菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                                xMomentMenuRotation -= 45
                            }

                            // 菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                                menuOpacity = 0
                                menuScale = 0.95
                                menuOffset = -15
                                cardAreaOffset = 0
                                showingXMomentHorizontalMenu = false
                                menuItemsAppeared = false
                                menuItem1Opacity = 0
                                menuItem2Opacity = 0
                                menuItem3Opacity = 0
                                menuItem4Opacity = 0
                            }
                        } label: {
                             Image(systemName: templateManager.currentTemplate.icon)
                        }
                        .buttonStyle(XMomentHorizontalButtonStyle())
                        .opacity(menuItem2Opacity)

                        // Theme Button
                        Button {
                            // 触觉反馈 - 提升触觉反馈强度
                            let generator = UIImpactFeedbackGenerator(style: .soft)
                            generator.impactOccurred(intensity: 1.0)

                            // 立即显示主题设置，不等待菜单动画完成
                            showingThemeSettings = true

                            // 图标旋转和菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                                xMomentMenuRotation -= 45
                            }

                            // 菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                                menuOpacity = 0
                                menuScale = 0.95
                                menuOffset = -15
                                cardAreaOffset = 0
                                showingXMomentHorizontalMenu = false
                                menuItemsAppeared = false
                                menuItem1Opacity = 0
                                menuItem2Opacity = 0
                                menuItem3Opacity = 0
                                menuItem4Opacity = 0
                            }
                        } label: {
                             Image(systemName: "paintbrush")
                        }
                        .buttonStyle(XMomentHorizontalButtonStyle())
                        .opacity(menuItem3Opacity)

                        // Info Button
                        Button {
                            // 触觉反馈 - 提升触觉反馈强度
                            let generator = UIImpactFeedbackGenerator(style: .soft)
                            generator.impactOccurred(intensity: 1.0)

                            // 立即显示信息页面，不等待菜单动画完成
                            showingXMomentInfo = true

                            // 图标旋转和菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                                xMomentMenuRotation -= 45
                            }

                            // 菜单收起动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                                menuOpacity = 0
                                menuScale = 0.95
                                menuOffset = -15
                                cardAreaOffset = 0
                                showingXMomentHorizontalMenu = false
                                menuItemsAppeared = false
                                menuItem1Opacity = 0
                                menuItem2Opacity = 0
                                menuItem3Opacity = 0
                                menuItem4Opacity = 0
                            }
                        } label: {
                             Image(systemName: "info.circle")
                        }
                        .buttonStyle(XMomentHorizontalButtonStyle())
                        .opacity(menuItem4Opacity)
                    }
                    .padding(.vertical, 6)
                    .padding(.horizontal, 20)
                    .background(Color.black.opacity(0.85)) // <-- Always use black background
                    .clipShape(Capsule())
                    .shadow(color: Color.black.opacity(0.15), radius: 6, y: 3)
                    .padding(.horizontal, 16) // 与标题行对齐
                    .padding(.top, 8) // 与标题的间距
                    .scaleEffect(menuScale) // 使用绑定变量控制缩放
                    .opacity(menuOpacity) // 使用绑定变量控制透明度
                    .offset(y: menuOffset) // 使用绑定变量控制偏移
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.95).combined(with: .opacity).combined(with: .offset(y: -5)),
                        removal: .scale(scale: 0.95).combined(with: .opacity).combined(with: .offset(y: -5))
                    ))
                }
            }
            // 监听菜单状态变化，应用优雅的动画效果
            .onChange(of: showingXMomentHorizontalMenu) { _, isShowing in
                if isShowing {
                    // 显示菜单时的动画 - 使用更优雅的节奏

                    // 全部动画效果在一个动画块中完成，确保完全同步
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                        cardAreaOffset = 1.5 // 将菜单与卡片区域的边距调整为6点
                        menuOpacity = 1 // 同步控制菜单透明度
                        menuScale = 1.0 // 同步控制菜单缩放
                        menuOffset = 0  // 同步控制菜单偏移
                    }

                    // 菜单项交错出现效果
                    if !menuItemsAppeared {
                        menuItemsAppeared = true

                        // 第一个菜单项
                        withAnimation(.easeInOut(duration: 0.4).delay(0.1)) {
                            menuItem1Opacity = 1
                        }

                        // 第二个菜单项
                        withAnimation(.easeInOut(duration: 0.4).delay(0.2)) {
                            menuItem2Opacity = 1
                        }

                        // 第三个菜单项
                        withAnimation(.easeInOut(duration: 0.4).delay(0.3)) {
                            menuItem3Opacity = 1
                        }

                        // 第四个菜单项
                        withAnimation(.easeInOut(duration: 0.4).delay(0.4)) {
                            menuItem4Opacity = 1
                        }
                    }

                } else {
                    // 隐藏菜单时的动画 - 使用优雅的退场方式

                    // 重置菜单项透明度状态
                    menuItem1Opacity = 0
                    menuItem2Opacity = 0
                    menuItem3Opacity = 0
                    menuItem4Opacity = 0
                    menuItemsAppeared = false

                    // 所有退场动画在一个动画块中完成，确保完全同步
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                        menuOpacity = 0     // 菜单透明度变为0
                        menuScale = 0.95    // 菜单缩小
                        menuOffset = -15    // 菜单上移
                        cardAreaOffset = 0  // 卡片区域恢复原位
                    }
                }
            }

            // 力量平衡可视化区域 - 添加偏移动画
            ZStack {
                // 背景层
                RoundedRectangle(cornerRadius: 16)
                    .fill(colorScheme == .dark ?
                          Color(UIColor.systemGray6) :
                          Color(UIColor.systemGray6).opacity(0.3))

                // 基于当前状态显示不同的内容
                ZStack {
                    // 常规状态：显示 ForceFieldView
                    if xMomentState == .inactive {
                        ForceFieldView(
                            balance: forceBalance,
                            isAnimating: isAnimating,
                            itemCount: weekXMomentStats.count
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .transition(.opacity.animation(.easeOut(duration: 0.3)))
                        .overlay( // Gesture for starting
                        Color.clear
                            .contentShape(Rectangle())
                            .gesture(
                                LongPressGesture(minimumDuration: 0.8)
                                        .onEnded { _ in handleLongPressStart() }
                                )
                        )
                    }

                    // 进行中 或 完成后等待交互 状态：显示进度/最终视觉
                    if xMomentState == .inProgress || (xMomentState == .completed && !showingCompletionOptions) {
                        xMomentProgressView // 这个视图现在内部处理了完成后的 Tap 手势
                            .transition(.opacity.animation(.easeOut(duration: 0.3))) // 使用淡入淡出避免闪烁
                    }

                    // 完成后且已点击，显示选项
                    if xMomentState == .completed && showingCompletionOptions {
                        completionOptionsView
                            .transition(.opacity.animation(.easeInOut(duration: 0.3)))
                    }

                    // 选择时间状态：显示时间选择器（确保它在最上层）
                    if isChoosingTime {
                        timeSelectionOverlay
                    }
                }
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(8)
                .contentShape(Rectangle()) // Gesture for cancellation
                .gesture(
                    LongPressGesture(minimumDuration: 0.8)
                        .onEnded { _ in handleMainAreaLongPress() }
                )
                .simultaneousGesture( // <-- ADDED SIMULTANEOUS TAP GESTURE
                    TapGesture()
                        .onEnded { _ in
                            if xMomentIsFreeMode {
                                completeFreeModeSession()
                            }
                        }
                )

                // 庆祝动画层 (覆盖在 ZStack 内容之上)
                if showCelebration {
                    MilestoneCelebrationView(milestone: tipsManager.lastAchievedMilestone)
                        .transition(.scale.combined(with: .opacity))
                        .onAppear {
                            // 动画显示一段时间后自动消失
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                withAnimation {
                                    showCelebration = false
                                    tipsManager.lastAchievedMilestone = nil // 清除状态以防重复触发
                                }
                            }
                        }
                }

                // 自由模式里程碑消息层
                // if showingMilestoneMessage && xMomentIsFreeMode { ... }
            }
            .frame(height: 200)
            .padding(.top, 8)
            .offset(y: cardAreaOffset) // 应用卡片区域偏移动画

            // 数据指标区域 - 根据状态动态显示
            WillpowerMetricsView(xMomentState: xMomentState)

            // 意志力建议
            HStack(alignment: .center, spacing: 12) {
                Image(systemName: getDailyTipIcon())
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(getDailyTipColor())
                    .frame(width: 26, height: 26)
                    .background(
                        Circle()
                            .fill(getDailyTipColor().opacity(0.15))
                    )

                // 使用原有的TypingText
                TypingText(text: getDailyTip())
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
            .offset(y: cardAreaOffset) // 应用卡片区域偏移动画
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(colorScheme == .dark ?
                      Color(UIColor.systemGray5) :
                      Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .onAppear {
            // 激活动画
            withAnimation(.easeInOut(duration: 0.8)) {
                isAnimating = true
            }

            // 启动脉冲缩放
            withAnimation {
                pulseScale = 1.4
            }

            // 设置初始平衡位置 - 异步执行
            DispatchQueue.main.async {
                self.balancePosition = self.forceBalance
            }

            // 从用户统计数据读取上次使用的 X Moment 时长设置
            let userStats = dataStore.getXMomentUserStatsUnambiguous()
            lastUsedXMomentDuration = userStats.lastUsedDuration
            xMomentDuration = userStats.lastUsedDuration

            // 初始化图标和颜色
            updateTipIconAndColor()

            // 首次出现时更新 Tip - 添加延迟
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                updateTipWithRandomMessage()
            }
        }
        .onDisappear {
            // 清理里程碑检测定时器
            // stopMilestoneCheckTimer()
        }
        .onChange(of: dataStore.habits.count) { _, _ in
            // 当习惯数据变化时，更新动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                balancePosition = forceBalance
            }
        }
        .sheet(isPresented: $showDetail) {
            WillpowerDetailView()
        }
        .sheet(isPresented: $showingXMomentInfo) { // 新增：弹出 X Moment 信息视图
            XMomentInfoView()
        }
        .sheet(isPresented: $showingThemeSettings) { // 弹出主题设置视图
            XMomentThemeSettingsView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingTemplateSettings) { // 弹出计时模板设置视图
            TimingTemplateSelectionView()
                .environmentObject(templateManager)
        }
        .sheet(isPresented: $showingXMomentStats) { // 弹出XMoment统计视图
            XMomentStatsView()
                .environmentObject(dataStore)
        }
        // 添加取消确认弹窗
        .alert(NSLocalizedString("stop_x_moment_title", comment: ""), isPresented: $showingCancelConfirm) {
            Button(NSLocalizedString("stop", comment: ""), role: .destructive) {
                cancelXMoment()
            }
            Button(NSLocalizedString("continue_focus", comment: ""), role: .cancel) {}
        } message: {
            Text(NSLocalizedString("stop_x_moment_message", comment: ""))
        }
        // 添加记录表单 Sheet
        .sheet(item: $sessionToRecord, onDismiss: {
            // 如果用户从记录表单取消，也重置状态 <-- MODIFY THIS BLOCK
            print("🔵 .sheet onDismiss triggered. sessionToRecord is currently \(sessionToRecord == nil ? "nil" : "NOT nil")")
            // Only call reset if the sheet is dismissing while the state still holds the item.
            // This handles cases like swipe-to-dismiss.
            if sessionToRecord != nil {
                print("🔵 .sheet onDismiss: sessionToRecord != nil, calling resetToInactiveState.")
                resetToInactiveState()
            } else {
                print("🔵 .sheet onDismiss: sessionToRecord is already nil, reset likely handled by sheet content's completion.")
            }
        }) { session in
            if session.isFreeMode {
                // 自由模式使用专门的结果视图
                FreeModeResultView(session: session) { modifiedSession in
                    // 保存逻辑
                    dataStore.saveXMomentSession(modifiedSession)

                    // 使用成功完成的提示消息
                    DispatchQueue.main.async {
                        let successTips = [
                            NSLocalizedString("free_mode_recorded_tip_1", comment: ""),
                            NSLocalizedString("free_mode_recorded_tip_2", comment: ""),
                            NSLocalizedString("free_mode_recorded_tip_3", comment: "")
                        ]
                        tipsManager.currentTip = successTips.randomElement()
                    }

                    // 触发反馈动画
                    triggerSaveFeedback()

                    // 重置状态
                    isOverclocked = false
                    resetToInactiveState()
                }
                .environmentObject(dataStore)
            } else {
                // 标准模式使用主题管理器选择合适的记录视图
                themeManager.recordView(for: session) { modifiedSession in
                    // 保存逻辑
                    dataStore.saveXMomentSession(modifiedSession)

                    // !!! 关键：保存后检查里程碑并触发提示更新 !!!
                    // 使用成功完成的提示消息
                    DispatchQueue.main.async {
                        let successTips = [
                            NSLocalizedString("standard_mode_recorded_tip_1", comment: ""),
                            NSLocalizedString("standard_mode_recorded_tip_2", comment: ""),
                            NSLocalizedString("standard_mode_recorded_tip_3", comment: "")
                        ]
                        tipsManager.currentTip = successTips.randomElement()
                    }

                    // 触发反馈动画
                    triggerSaveFeedback()

                    // 先重置超频状态，确保超频数据已经被正确记录
                    isOverclocked = false

                    // 然后再重置其他状态
                    resetToInactiveState()
                }
            }
        }
        // 应用保存反馈动画效果
         .scaleEffect(showSaveFeedback ? 1.03 : 1.0)
         .opacity(showSaveFeedback ? 0.8 : 1.0)
         .animation(.spring(response: 0.3, dampingFraction: 0.4), value: showSaveFeedback)
        // 监听里程碑达成，触发庆祝动画
        .onChange(of: tipsManager.lastAchievedMilestone) { _, newMilestone in
            if newMilestone != nil {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
                    showCelebration = true
                }
                // 播放成功的触觉反馈
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.success)
            }
        }
    }

    // MARK: - 子视图提取

    // 提取的 X Moment 进行中/完成 视图
    private var xMomentProgressView: some View {
        ZStack {
            // 时间线视图用于动画更新
            timelineProgressView
        }
        // 添加 Tap 手势，仅在完成后触发选项显示
        .overlay(Color.clear.contentShape(Rectangle())) // 确保整个区域可点击
        .onTapGesture {
            if xMomentState == .completed {
                withAnimation(.easeInOut) {
                    showingCompletionOptions = true
                }
                // 轻触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()
            }
        }
    }

    // 提取的时间线进度视图
    private var timelineProgressView: some View {
        TimelineView(.animation(minimumInterval: 1/60)) { timeline in
            // 计算当前进度 - 修改：直接传递 currentTime
            let currentTime = timeline.date
            // let progress = isChoosingTime ? 0.0 : calculateXMomentProgress(currentTime: currentTime) // 移除此行

            // 显示进度视觉效果 - 修改：传递 currentTime
            mindAuraProgressView(currentTime: currentTime)
        }
    }

    // 提取的计时进度视图 - 修改：接收 currentTime 并计算 progress
    private func mindAuraProgressView(currentTime: Date) -> some View {
        // 在这里根据状态计算进度
        let progress: Double
        let elapsedTime: TimeInterval
        let isFreeMode = xMomentIsFreeMode // 新增：直接传递

        if isChoosingTime {
            progress = 0.0
            elapsedTime = 0
        } else if let startTime = xMomentStartTime {
            elapsedTime = currentTime.timeIntervalSince(startTime)

            // 自由模式特殊处理：使用预设的30分钟作为完成时间
            if isFreeMode {
                let freeModeDuration: TimeInterval = 30 * 60 // 30分钟
                progress = min(elapsedTime / freeModeDuration, 0.98)
            } else {
                progress = min(elapsedTime / xMomentDuration, 1.0)
                if progress >= 1.0 && xMomentProgress < 1.0 {
                    DispatchQueue.main.async {
                        self.xMomentProgress = progress
                        self.handleXMomentComplete()
                    }
                } else if progress != xMomentProgress {
                    DispatchQueue.main.async {
                        self.xMomentProgress = progress
                    }
                }
            }
        } else {
            progress = 0.0
            elapsedTime = 0
        }

        // 使用templateManager.timingView方法根据当前选择的模板返回相应的视图
        return ZStack {
            templateManager.timingView(
                progress: progress,
                onOverclockStatusUpdated: { isActive, bonusFactor, duration in
                    // 更新超频状态 - 使用绑定变量
                    self.isOverclocked = isActive
                    self.overclockBonusFactor = bonusFactor
                    self.overclockDuration = duration
                    if bonusFactor > 1.0 {
                        print("X Moment 超频中：倍率 \(bonusFactor)x，累计时间 \(Int(duration/60)) 分钟")
                    }
                },
                isTimingStarted: true,
                isFreeMode: isFreeMode
            )
        }
        .onTapGesture {
            if isFreeMode {
                completeFreeModeSession()
            } else if progress >= 1.0 && xMomentState == .inProgress {
                handleXMomentComplete()
            }
        }
    }

    // 提取的时间选择覆盖层
    private var timeSelectionOverlay: some View {
        // 定义过渡效果（简化表达式）
        let transition = AnyTransition.opacity.combined(with: .scale(scale: 0.9))

        // 返回时间选择器组件
        return EmbeddedTimeSelector(selectedDuration: $xMomentDuration) { selectedMinutes in
            handleTimeSelection(minutes: selectedMinutes)
        }
        .transition(transition)
        .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(0.2), value: isChoosingTime)
    }



    // 提取的时间选择处理逻辑
    private func handleTimeSelection(minutes: Int) {
        // 检查是否可以使用XMoment
        if !dataStore.canUseXMoment() {
            // 使用通知中心发送通知，而不是直接设置状态变量
            NotificationCenter.default.post(name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
            isChoosingTime = false
            return
        }

        // 记录XMoment使用
        dataStore.recordXMomentUse()

        // 设置时长
        xMomentDuration = TimeInterval(minutes * 60)

        // 先设置开始时间
        xMomentStartTime = Date()

        // 重置进度
        xMomentProgress = 0.0

        // 保存选择的时长以便下次使用
        dataStore.updateLastUsedXMomentDuration(xMomentDuration)
        lastUsedXMomentDuration = xMomentDuration

        // 判断是否为自由模式（1分钟选项）
        let isFreeMode = (minutes == 1)

        // 如果是自由模式基准时间设置，实际使用预设的30分钟作为内部计算基准
        if isFreeMode {
            xMomentDuration = 1 * 60 // 内部使用预设的30分钟
        }

        // 打印日志
        if isFreeMode {
            print("🔵 启动自由模式")
        }

        // 设置自由模式标记
        xMomentIsFreeMode = isFreeMode

        // 重置里程碑相关状态
        if isFreeMode {
            // reachedMilestones = []
            // lastCheckedMinute = 0
            // startMilestoneCheckTimer() // <-- Directly start the timer here
        } else {
            // 如果不是自由模式，确保定时器已停止
            // stopMilestoneCheckTimer()
        }

        // 确保状态是 .inProgress
        xMomentState = .inProgress

        // 启动核心计时逻辑
        startXMomentCoreLogic()

        // 最后再隐藏选择器，此时所有状态都已准备就绪
        withAnimation(.easeInOut(duration: 0.5)) {
            isChoosingTime = false
        }
    }

    // MARK: - X Moment 相关方法

    // 处理主区域长按事件（启动或返回）
    private func handleMainAreaLongPress() {
        if xMomentState == .inactive {
            // 非 X Moment 状态：启动 X Moment 流程
            handleLongPressStart()
        } else if xMomentState == .inProgress || xMomentState == .completed {
            // X Moment 进行中或完成后：长按直接返回意志力角力场
            print("🔵 长按触发返回意志力角力场")

            // 提供明确的触觉反馈
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.warning) // 使用警告反馈表示中断/返回

            // 检查是否需要记录中断会话（超过5分钟）
            if let startTime = xMomentStartTime {
                let currentDuration = Date().timeIntervalSince(startTime)

                // 如果持续时间超过5分钟（300秒），记录为中断会话
                if currentDuration >= 300 {
                    print("🟠 中断会话记录：持续时间 \(Int(currentDuration/60)) 分钟")

                    // 判断是否为自由模式
                    let isFreeMode = xMomentIsFreeMode

                    // 创建带有中断标记的会话
                    let interruptedSession = XMomentSession(
                        id: UUID(),
                        startTime: startTime,
                        endTime: Date(),
                        duration: currentDuration,
                        mood: NSLocalizedString("interrupted", comment: ""), // 标记心情为"中断"
                        category: NSLocalizedString("interrupted", comment: ""), // 标记类别为"中断"
                        notes: NSLocalizedString("interrupted_by_long_press", comment: "通过长按中断"), // 添加说明
                        overclockFactor: overclockBonusFactor,
                        overclockDuration: overclockDuration,
                        isInterrupted: true, // 标记为中断
                        isFreeMode: isFreeMode,
                        initialDuration: xMomentDuration,
                        isPersonalBest: false,
                        progressXU: 0.0
                    )

                    // 保存中断会话记录
                    dataStore.saveXMomentSession(interruptedSession)

                    // 在保存会话后重置超频状态
                    isOverclocked = false
                } else {
                    print("⚪️ 中断的会话持续时间不足5分钟，不记录：\(Int(currentDuration)) 秒")

                    // 即使不记录会话，也需要重置超频状态
                    isOverclocked = false
                }
            } else {
                // 如果没有开始时间，也确保重置超频状态
                isOverclocked = false
            }

            // 重置到非活动状态
            resetToInactiveState()
        }
        // 在 isChoosingTime 状态下长按，不执行任何操作
    }

    // X Moment 启动逻辑 (原 handleLongPress)
    private func handleLongPressStart() {
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.warning) // 使用警告反馈表示中断/返回

        // 如果 X Moment 已经在进行中，忽略（此检查现在由 handleMainAreaLongPress 处理，但保留无妨）
        if xMomentState != .inactive { return }

        // 检查是否可以使用XMoment
        if !dataStore.canUseXMoment() {
            // 使用通知中心发送通知，而不是直接设置状态变量
            NotificationCenter.default.post(name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
            return
        }

        // 平滑切换到进行中状态，并标记为正在选择时间
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            xMomentState = .inProgress
            isChoosingTime = true // 显示时间选择器
            xMomentDuration = lastUsedXMomentDuration // 预设为上次时长
        }
    }

    // 添加核心计时逻辑函数实现
    private func startXMomentCoreLogic() {
        // 记录开始时间并重置进度
        xMomentStartTime = Date()
        xMomentProgress = 0.0

        // 确保状态是 .inProgress (虽然调用时应该已经是)
        if xMomentState != .inProgress {
            xMomentState = .inProgress
        }
    }

    private func handleXMomentComplete() {
        print("🔵 handleXMomentComplete: X Moment 完成")
        // 确保只执行一次完成逻辑
        guard xMomentState == .inProgress else { return }

        // X Moment 完成后，平滑切换到完成状态
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            xMomentState = .completed
            isChoosingTime = false // 确保时间选择器不在
            showingCompletionOptions = false // 初始时不显示选项
        }

        // 修改触觉反馈为轻微
        let generator = UIImpactFeedbackGenerator(style: .light) // 改为 .light
        generator.impactOccurred() // 触发震动

        // 不再清理开始时间，记录时需要
        // xMomentStartTime = nil

        // 停止音频 (交给 onDisappear 处理，或在此处明确停止)
        // cleanupResources() // 或者保留让 onDisappear 处理
    }



    // 新增：完成状态的交互选项视图
    private var completionOptionsView: some View {
        VStack(spacing: 20) {
            Text(NSLocalizedString("willpower.completion.title", comment: "Focus completed title"))
                .font(.system(size: 20, weight: .semibold, design: .rounded))
                .foregroundColor(.white)

            HStack(spacing: 25) {
                // 跳过按钮
                Button {
                    // 触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()

                    // 先重置超频状态
                    isOverclocked = false

                    // 然后重置其他状态
                    withAnimation {
                        resetToInactiveState()
                    }
                } label: {
                    Label(NSLocalizedString("willpower.completion.skip", comment: "Skip button label"), systemImage: "xmark")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 20)
                        .padding(.vertical, 6)
                        .background(.ultraThinMaterial)
                        .clipShape(Capsule())
                }
                .buttonStyle(ScaleButtonStyle(scaleFactor: 0.95))

                // 记录按钮
                Button {
                    // 触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .heavy)
                    generator.impactOccurred()

                    // 准备会话数据并弹出表单
                    guard let startTime = xMomentStartTime else {
                        print("⚠️ 无法记录：开始时间丢失")
                        resetToInactiveState() // 即使出错也重置
                        return
                    }
        let endTime = Date()
                    let actualDuration = endTime.timeIntervalSince(startTime)

        // 判断是否为自由模式
        let isFreeMode = xMomentIsFreeMode

        let session = XMomentSession(
            id: UUID(),
            startTime: startTime,
            endTime: endTime,
            duration: actualDuration,
            mood: nil, // 让用户在表单中选择
            category: nil, // 让用户在表单中选择
            overclockFactor: overclockBonusFactor, // 应用超频倍率
            overclockDuration: overclockDuration, // 记录超频时间
            isInterrupted: false,
            isFreeMode: isFreeMode,
            initialDuration: xMomentDuration,
            isPersonalBest: false,
            progressXU: 0.0 // 进步奖励将在表单中计算
        )
                    sessionToRecord = session
                    showingCompletionOptions = false // 隐藏选项，触发 sheet

                } label: {
                    Label(NSLocalizedString("willpower.completion.record", comment: "Record focus button label"), systemImage: "square.and.pencil")
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(.black)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 6)
                        .background(Color.white)
                        .clipShape(Capsule())
                }
                 .buttonStyle(ScaleButtonStyle(scaleFactor: 0.95))
            }
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.black.opacity(0.3))
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 20))

        )
        .shadow(color: .black.opacity(0.2), radius: 8, y: 4)
    }

    // 新增：取消 X Moment 的逻辑
    private func cancelXMoment() {
        // 检查是否需要记录中断会话（超过5分钟）
        if let startTime = xMomentStartTime {
            let currentDuration = Date().timeIntervalSince(startTime)

            // 如果持续时间超过5分钟（300秒），记录为中断会话
            if currentDuration >= 300 {
                print("🟠 取消确认中断会话记录：持续时间 \(Int(currentDuration/60)) 分钟")

                // 判断是否为自由模式
                let isFreeMode = xMomentIsFreeMode

                // 创建带有中断标记的会话
                let interruptedSession = XMomentSession(
                    id: UUID(),
                    startTime: startTime,
                    endTime: Date(),
                    duration: currentDuration,
                    mood: NSLocalizedString("interrupted", comment: ""), // 标记心情为"中断"
                    category: NSLocalizedString("interrupted", comment: ""), // 标记类别为"中断"
                    notes: NSLocalizedString("interrupted_by_cancel_button", comment: ""), // 添加说明
                    overclockFactor: overclockBonusFactor,
                    overclockDuration: overclockDuration,
                    isInterrupted: true, // 标记为中断
                    isFreeMode: isFreeMode,
                    initialDuration: xMomentDuration,
                    isPersonalBest: false,
                    progressXU: 0.0
                )

                // 保存中断会话记录
                dataStore.saveXMomentSession(interruptedSession)

                // 在保存会话后重置超频状态
                isOverclocked = false
            } else {
                print("⚪️ 取消的会话持续时间不足5分钟，不记录中断：\(Int(currentDuration)) 秒")

                // 即使不记录会话，也需要重置超频状态
                isOverclocked = false
            }
        } else {
            // 如果没有开始时间，也确保重置超频状态
            isOverclocked = false
        }

        resetToInactiveState() // 使用通用重置函数

        // 提供反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.warning) // 使用警告反馈表示取消
        print("🟡 X Moment 已取消")

        // 取消后更新提示（使用随机提示）
        updateTipWithRandomMessage()
    }

    // 新增：重置到非活动状态的通用函数
    private func resetToInactiveState() {
        // print("🔵 resetToInactiveState called.") // <-- REMOVED log
        // 停止里程碑检测定时器
        // stopMilestoneCheckTimer()

        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            xMomentState = .inactive
            isChoosingTime = false
            showSaveFeedback = false // 确保反馈动画重置
        }
        xMomentStartTime = nil
        xMomentProgress = 0.0
        sessionToRecord = nil // 清空待记录项
        xMomentIsFreeMode = false // <-- 添加重置自由模式状态
        // isOverclocked = false // <-- 移除此行：不再在这里重置超频状态

        // 重置里程碑相关状态
        // reachedMilestones = [] // 清除里程碑记录
        // lastCheckedMinute = 0
        // showingMilestoneMessage = false

        // 重置状态后更新提示（使用随机提示）
        updateTipWithRandomMessage()

        // 重置 TabBar 外观
        let isLightMode = colorScheme == .light
        // 直接使用UIConfigurationHelper的方法设置TabBar外观
        UIConfigurationHelper.updateTabBarForXMoment(isActive: false, isLightMode: isLightMode)
    }

    // 新增: 触发保存反馈动画
    private func triggerSaveFeedback() {
        showSaveFeedback = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
             withAnimation {
                 showSaveFeedback = false
             }
        }
    }

    // 新增: 完成自由模式会话
    private func completeFreeModeSession() {
        // stopMilestoneCheckTimer() // <-- Ensure timer is stopped

        // 确保有开始时间
        guard let startTime = xMomentStartTime else {
            print("⚠️ 无法完成自由模式：开始时间丢失")
            // resetToInactiveState() // Don't reset here if start time is missing, let the UI stay
            return
        }

        // 计算实际持续时间
        let endTime = Date()
        let actualDuration = endTime.timeIntervalSince(startTime)

        // 警告时间过短，但继续
        if actualDuration < 5 {
             print("⚠️ 自由模式时间过短，但仍将显示记录表单。")
        }

        // 检查是否为个人最佳记录
        let previousBest = dataStore.getXMomentUserStatsUnambiguous().freeModeBestDuration
        let isPersonalBest = actualDuration > previousBest

        // 创建会话对象
        let session = XMomentSession(
            id: UUID(),
            startTime: startTime,
            endTime: endTime,
            duration: actualDuration,
            mood: nil, // 让用户在表单中选择
            category: nil, // 让用户在表单中选择
            overclockFactor: 1.0, // 自由模式不使用超频
            overclockDuration: 0, // 自由模式不使用超频
            isInterrupted: false,
            isFreeMode: true,
            initialDuration: 30 * 60, // 自由模式预设时长为30分钟 - NOTE: Should this be 1*60 based on previous edit? Check handleTimeSelection. Let's assume 30*60 is correct intent.
            isPersonalBest: isPersonalBest,
            progressXU: 0.0 // 进步奖励将在表单中计算
        )

        // FIRST: Change state to completed within animation <-- RESTORED
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            xMomentState = .completed
            isChoosingTime = false
            showingCompletionOptions = true // Let the UI momentarily reflect completion
        }

        // THEN: After a short delay, set the data to trigger the sheet <-- ADDED DELAY
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            sessionToRecord = session
        }

        // 触觉反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        print("🟢 完成自由模式会话: 持续时间 \(Int(actualDuration/60)) 分钟 (Sheet trigger delayed)")
    }

    // 更新提示信息（使用随机消息）
    private func updateTipWithRandomMessage() {
        DispatchQueue.main.async {
            let tipKeys = [
                "willpower.tip.dialogue",
                "willpower.tip.less_is_more",
                "willpower.tip.invest_time",
                "willpower.tip.polish_focus",
                "willpower.tip.feel_power",
                "common.dailyTip" // Re-use the key for the dataStore daily tip if desired
            ]
            // Use dataStore.getDailyTip() directly or use a key for it
            // Let's assume we want to use keys for all tips for consistency
            let randomKey = tipKeys.randomElement() ?? "willpower.tip.default"

            // If the selected key is the common daily tip, fetch it from dataStore
            let localizedTip: String
            if randomKey == "common.dailyTip" {
                localizedTip = dataStore.getDailyTip() // Assuming getDailyTip returns a localized string
            } else {
                localizedTip = NSLocalizedString(randomKey, comment: "Random willpower tip")
            }

            self.tipsManager.currentTip = localizedTip

            // 当提示更新时，同时更新图标和颜色
            updateTipIconAndColor()
        }
    }

    // 格式化数据
    private func formatPercent(_ value: Double) -> String {
        return String(format: "%.0f%%", min(value * 100, 100))
    }

    // 格式化时间为小时
    private func formatDurationToHours(_ duration: TimeInterval) -> String {
        let hours = duration / 3600.0
        return String(format: "%.1f", hours)
    }

    // 修改格式化方法
    private func formatDetail(count: Int, key: String) -> String {
        return String(format: "%d %@", count, NSLocalizedString(key, comment: ""))
    }

    // 获取每日建议
    private func getDailyTip() -> String {
        let tips = [
            NSLocalizedString("daily_tip_1", comment: ""),
            NSLocalizedString("daily_tip_2", comment: ""),
            NSLocalizedString("daily_tip_3", comment: ""),
            NSLocalizedString("daily_tip_4", comment: ""),
            NSLocalizedString("daily_tip_5", comment: ""),
            NSLocalizedString("daily_tip_6", comment: ""),
            NSLocalizedString("daily_tip_7", comment: ""),
            NSLocalizedString("daily_tip_8", comment: ""),
            NSLocalizedString("daily_tip_9", comment: ""),
            NSLocalizedString("daily_tip_10", comment: ""),
            NSLocalizedString("daily_tip_11", comment: ""),
            NSLocalizedString("daily_tip_12", comment: ""),
            NSLocalizedString("daily_tip_13", comment: ""),
            NSLocalizedString("daily_tip_14", comment: ""),
            NSLocalizedString("daily_tip_15", comment: ""),
            NSLocalizedString("daily_tip_16", comment: ""),
            NSLocalizedString("daily_tip_17", comment: ""),
            NSLocalizedString("daily_tip_18", comment: ""),
            NSLocalizedString("daily_tip_19", comment: ""),
            NSLocalizedString("daily_tip_20", comment: "")
        ]

        // 使用随机数生成器，每次打开应用都随机显示一条建议
        let randomIndex = Int.random(in: 0..<tips.count)
        return tips[randomIndex]
    }

    // 存储当前图标和颜色的状态变量
    @State private var currentTipIcon: String = "brain"
    @State private var currentTipColor: Color = .blue

    // 获取每日建议图标 - 返回当前固定图标
    private func getDailyTipIcon() -> String {
        return currentTipIcon
    }

    // 获取每日建议颜色 - 返回当前固定颜色
    private func getDailyTipColor() -> Color {
        return currentTipColor
    }

    // 更新建议图标和颜色 - 只在需要时调用
    private func updateTipIconAndColor() {
        let icons = ["brain", "sparkles", "book.fill", "graduationcap.fill", "lightbulb.fill", "atom", "infinity", "puzzlepiece.fill"]
        let colors: [Color] = [.blue, .purple, .orange, .green, .pink, .teal, .yellow, .indigo]

        currentTipIcon = icons.randomElement() ?? "brain"
        currentTipColor = colors.randomElement() ?? .blue
    }

    // 新增：计算 X Moment 进度
    private func calculateXMomentProgress(currentTime: Date) -> Double {
        // 如果正在选择时间，直接返回0
        if isChoosingTime {
            print("🟡 calculateXMomentProgress: 选择时间中，返回0")
            return 0.0
        }

        // 确保有开始时间
        guard let startTime = xMomentStartTime else {
            print("⚠️ calculateXMomentProgress: xMomentStartTime 为空")
            return 0.0
        }

        // 确保持续时间有效
        guard xMomentDuration > 0 else {
            print("⚠️ calculateXMomentProgress: 无效的持续时间 (\(xMomentDuration))")
            return 0.0
        }

        // 计算进度
        let elapsedTime = currentTime.timeIntervalSince(startTime)

        // 自由模式特殊处理
        let progress: Double
        if xMomentIsFreeMode {
            // 自由模式下，使用预设的30分钟作为分母
            let freeModeDuration: TimeInterval = 30 * 60 // 30分钟
            progress = min(elapsedTime / freeModeDuration, 0.9) // 最大只到90%，保留一些空间给超频动画

            // 检查里程碑
            // checkFreeModeMillestones(elapsedTime: elapsedTime)

            // 在自由模式下，每分钟打印一次日志
            let minutes = Int(elapsedTime / 60)
            if elapsedTime.truncatingRemainder(dividingBy: 60) < 0.1 {
                print("🟢 自由模式进行中: \(minutes) 分钟")
            }
        } else {
            // 标准模式正常计算进度
            progress = min(elapsedTime / xMomentDuration, 1.0)

            // 只在进度变化较大时打印日志
            if (progress * 100).truncatingRemainder(dividingBy: 1) < 0.1 { // 每1%打印一次
                print("🟢 calculateXMomentProgress: 进度 - \(String(format: "%.2f%%", progress * 100))")
            }
        }

        return progress
    }

    // 新增：初始化里程碑检测定时器
    // private func startMilestoneCheckTimer() {
    //     // 清除现有定时器
    //     stopMilestoneCheckTimer()

    //     // 创建新定时器，每秒检查一次
    //     let timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
    //         Task { @MainActor in
    //             await self.processMilestoneCheck(timer)
    //         }
    //     }
    //     milestoneCheckTimer = timer

    //     print("🗓️ 里程碑检测定时器已启动")
    // }

    // 新增：停止里程碑检测定时器
    // private func stopMilestoneCheckTimer() {
    //     milestoneCheckTimer?.invalidate()
    //     milestoneCheckTimer = nil
    // }

    // 新增：处理里程碑检测
    // @MainActor
    // private func processMilestoneCheck(_ timer: Timer) async {
    //     // 确保在自由模式下才检测
    //     guard xMomentIsFreeMode && xMomentState == .inProgress else {
    //         stopMilestoneCheckTimer()
    //         return
    //     }
    //     // Safely get start time and calculate elapsed time
    //     guard let startTime = xMomentStartTime else {
    //         stopMilestoneCheckTimer()
    //         return
    //     }
    //     let elapsedTime = Date().timeIntervalSince(startTime)

    //     // 计算当前分钟数
    //     let minutes = Int(elapsedTime / 60) // <-- Use calculated elapsedTime

    //     // 如果分钟数变化了，并且这个分钟数还没有达到过
    //     if minutes > lastCheckedMinute && !reachedMilestones.contains(minutes) {
    //         // 更新上次检测的分钟数
    //         lastCheckedMinute = minutes

    //         // 添加到已达到的里程碑集合
    //         reachedMilestones.insert(minutes)
    //         print("🏆 自由模式达到里程碑: \(minutes) 分钟")

    //         // 只显示特定里程碑的消息
    //         if [5, 10, 15, 20, 30, 45, 60].contains(minutes) || (minutes > 60 && minutes % 15 == 0) {
    //             // 在主线程上显示里程碑消息
    //             showMilestoneMessage(minute: minutes)
    //         }
    //     }
    // }

    // 新增：显示里程碑消息 (修改为处理键名和格式化)
    // @MainActor
    // private func showMilestoneMessage(minute: Int) {
    //     let messageKey = getMilestoneMessageKey(minute: minute)
    //     let localizedMessage: String

    //     // 处理需要格式化的消息
    //     if messageKey.hasSuffix("Format") {
    //         localizedMessage = String(format: NSLocalizedString(messageKey, comment: "Formatted milestone message. Param: minutes"), minute)
    //     } else {
    //         localizedMessage = NSLocalizedString(messageKey, comment: "Fixed milestone message")
    //     }

    //     currentMilestoneMessage = localizedMessage
    //     print("📢 显示里程碑消息: \(currentMilestoneMessage)")

    //     // 触觉反馈
    //     let generator = UIImpactFeedbackGenerator(style: .medium)
    //     generator.impactOccurred(intensity: 0.8)

    //     // 显示消息 - 使用主线程确保UI更新
    //     withAnimation(.easeIn(duration: 0.3)) {
    //         showingMilestoneMessage = true
    //         milestoneOpacity = 1.0
    //     }

    //     // 通知TipsManager更新里程碑
    //     if let milestone = createMilestoneForMinute(minute) {
    //         tipsManager.lastAchievedMilestone = milestone
    //     }

    //     // 3秒后隐藏消息
    //     DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
    //         withAnimation(.easeOut(duration: 0.5)) {
    //             self.milestoneOpacity = 0.0
    //         }

    //         DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
    //             self.showingMilestoneMessage = false
    //         }
    //     }
    // }

    // 创建里程碑对象用于TipsManager (修改以使用格式化后的消息)
    // private func createMilestoneForMinute(_ minute: Int) -> Milestone? {
    //     let category: MilestoneCategory = .deepWork
    //     let id = "free_mode_\(minute)_min"
    //     let name = String(format: NSLocalizedString("willpower.milestone.nameFormat", comment: "Milestone name format. Param: minutes"), minute)

    //     // 获取格式化后的描述
    //     let messageKey = getMilestoneMessageKey(minute: minute)
    //     let description: String
    //     if messageKey.hasSuffix("Format") {
    //         description = String(format: NSLocalizedString(messageKey, comment: "Formatted milestone message. Param: minutes"), minute)
    //     } else {
    //         description = NSLocalizedString(messageKey, comment: "Fixed milestone message")
    //     }

    //     let iconName = "timer"

    //     // 创建一个简单的条件闭包，始终返回true
    //     let condition: (DataStore) -> Bool = { _ in return true }

    //     return Milestone(id: id, name: name, description: description, category: category, iconName: iconName, condition: condition)
    // }

    // 新增：获取里程碑消息 (修改为返回键名)
    // private func getMilestoneMessageKey(minute: Int) -> String {
    //     switch minute {
    //     case 5:
    //         return "willpower.milestone.5min"
    //     case 10:
    //         return "willpower.milestone.10min"
    //     case 15:
    //         return "willpower.milestone.15min"
    //     case 20:
    //         return "willpower.milestone.20min"
    //     case 30:
    //         return "willpower.milestone.30min"
    //     case 45:
    //         return "willpower.milestone.45min"
    //     case 60:
    //         return "willpower.milestone.60min"
    //     default:
    //         if minute > 60 && minute % 15 == 0 {
    //             return "willpower.milestone.multipleOf15minFormat" // 需要格式化的键
    //         }
    //         return "willpower.milestone.genericFormat" // 需要格式化的键
    //     }
    // }

    // 获取个人最长单次专注时长
    // excludingCurrent: 用于检查是否打破记录，需要排除当前刚完成的会话（如果传入）
}

// 坚持指标数据结构
struct StreakMetrics {
    var totalDays: Int = 0
    var maxStreak: Int = 0
    var minStreak: Int = 0
    var avgStreak: Double = 0
    var consistencyScore: Double = 0 // 一致性得分(0-100)
}

// 重置指标数据结构
struct ResetMetrics {
    var totalResets: Int = 0
    var maxResetCount: Int = 0 // 单一习惯最大重置次数
    var avgResetInterval: TimeInterval = 0 // 平均重置间隔
    var maxResetInterval: TimeInterval = 0 // 最长重置间隔
    var minResetInterval: TimeInterval = 0 // 最短重置间隔
    var detailedResetRatio: Double = 0 // 有详细记录的重置比例
}

// 参与度指标数据结构
struct EngagementMetrics {
    var habitCount: Int = 0
    var reflectionCount: Int = 0
    var sharingCount: Int = 0
    var triggerCount: Int = 0  // 新增：触发记录数量
}


// 在DataStore中添加辅助方法
extension DataStore {
    // 获取某个习惯的反思记录数量 - 更新为使用实际的回顾记录
    func getReflectionsForHabit(habitId: UUID) -> [Reflection] {
        // 获取实际的回顾记录（Review）
        let reviews = self.getReviewsForHabit(habitId: habitId)
        var reflections: [Reflection] = []

        // 将回顾记录转换为Reflection对象
        for review in reviews {
            let reflection = Reflection(
                id: review.id,
                habitId: habitId,
                timestamp: review.date,
                content: review.content
            )
            reflections.append(reflection)
        }

        // 仅作为备用：如果回顾系统尚未实现，则使用有笔记的重置记录作为反思
        if reflections.isEmpty {
            let resets = self.getResetsForHabit(habitId: habitId)
            for reset in resets {
                // 只有带有笔记的重置才被视为反思
                if let notes = reset.notes, !notes.isEmpty {
                    let reflection = Reflection(
                        id: UUID(),
                        habitId: habitId,
                        timestamp: reset.date,
                        content: notes
                    )
                    reflections.append(reflection)
                }
            }
        }

        return reflections
    }

    // 获取用户的分享次数
    func getSharingCount() -> Int {
        return getXMomentUserStats().sharingCount
    }

    // 增加分享计数
    func incrementSharingCount() {
        var stats = getXMomentUserStatsUnambiguous()
        stats.sharingCount += 1
        saveXMomentUserStats(stats)

        // 发送objectWillChange通知，以便更新依赖此值的UI
        objectWillChange.send()

        print("分享计数增加: \(stats.sharingCount)")
    }

    // 重置分享计数（用于测试）
    func resetSharingCount() {
        var stats = getXMomentUserStatsUnambiguous()
        stats.sharingCount = 0
        saveXMomentUserStats(stats)
        objectWillChange.send()
        print("分享计数已重置")
    }

    // 新增：获取所有中断的 X Moment 会话
    func getInterruptedXMomentSessions() -> [XMomentSession] {
        return getXMomentSessionsUnambiguous().filter { $0.isInterrupted }
    }

    // 新增：获取特定时间段内的中断会话数量
    func getInterruptedSessionsCount(from startDate: Date? = nil, to endDate: Date? = nil) -> Int {
        let sessions = getXMomentSessionsUnambiguous()

        return sessions.filter { session in
            // 首先检查是否为中断会话
            guard session.isInterrupted else { return false }

            // 如果指定了开始日期，检查会话是否在该日期之后
            if let start = startDate, session.startTime < start {
                return false
            }

            // 如果指定了结束日期，检查会话是否在该日期之前
            if let end = endDate, session.endTime > end {
                return false
            }

            return true
        }.count
    }

    // 新增：获取中断率（中断会话数 / 总会话数）
    func getInterruptionRate(from startDate: Date? = nil, to endDate: Date? = nil) -> Double {
        let sessions = getXMomentSessionsUnambiguous()

        // 根据日期筛选会话
        let filteredSessions = sessions.filter { session in
            if let start = startDate, session.startTime < start {
                return false
            }

            if let end = endDate, session.endTime > end {
                return false
            }

            return true
        }

        // 计算总会话数和中断会话数
        let totalCount = filteredSessions.count
        let interruptedCount = filteredSessions.filter { $0.isInterrupted }.count

        // 避免除以零
        if totalCount == 0 {
            return 0.0
        }

        return Double(interruptedCount) / Double(totalCount)
    }

    // 公开访问的每日提示方法，供TipsManager使用
    func getDailyTip() -> String {
        let tips = [
            NSLocalizedString("daily_tip_1", comment: ""),
            NSLocalizedString("daily_tip_2", comment: ""),
            NSLocalizedString("daily_tip_3", comment: ""),
            NSLocalizedString("daily_tip_4", comment: ""),
            NSLocalizedString("daily_tip_5", comment: ""),
            NSLocalizedString("daily_tip_6", comment: ""),
            NSLocalizedString("daily_tip_7", comment: ""),
            NSLocalizedString("daily_tip_8", comment: ""),
            NSLocalizedString("daily_tip_9", comment: ""),
            NSLocalizedString("daily_tip_10", comment: ""),
            NSLocalizedString("daily_tip_11", comment: ""),
            NSLocalizedString("daily_tip_12", comment: ""),
            NSLocalizedString("daily_tip_13", comment: ""),
            NSLocalizedString("daily_tip_14", comment: ""),
            NSLocalizedString("daily_tip_15", comment: ""),
            NSLocalizedString("daily_tip_16", comment: ""),
            NSLocalizedString("daily_tip_17", comment: ""),
            NSLocalizedString("daily_tip_18", comment: ""),
            NSLocalizedString("daily_tip_19", comment: ""),
            NSLocalizedString("daily_tip_20", comment: "")
        ]

        // 使用随机数生成器，每次打开应用都随机显示一条建议
        let randomIndex = Int.random(in: 0..<tips.count)
        return tips[randomIndex]
    }
}

// Reflection结构体定义 - 兼容Review
struct Reflection: Identifiable, Codable {
    var id: UUID
    var habitId: UUID
    var timestamp: Date
    var content: String
}

struct WillpowerDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 标题与简介
                    VStack(alignment: .leading, spacing: 16) {
                        Text("willpower_balance_science")
                            .font(.title)
                            .fontWeight(.bold)
                            .padding(.top, 16)

                        Text("willpower_balance_description")
                            .font(.body)
                            .foregroundColor(.primary)
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 数据解读
                    VStack(alignment: .leading, spacing: 16) {
                        Text("data_interpretation")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        dataSection(
                            title: "willpower_persistence",
                            description: "willpower_description",
                            items: [
                                NSLocalizedString("accumulated_days", comment: ""),
                                NSLocalizedString("habit_consistency", comment: ""),
                                NSLocalizedString("challenge_difficulty", comment: ""),
                                NSLocalizedString("time_weighting", comment: "")
                            ],
                            iconName: "brain.head.profile",
                            color: .green
                        )

                        dataSection(
                            title: "temptation_and_failure",
                            description: "temptation_description",
                            items: [
                                NSLocalizedString("reset_frequency", comment: ""),
                                NSLocalizedString("cyclic_pattern", comment: ""),
                                NSLocalizedString("environmental_impact", comment: ""),
                                NSLocalizedString("recovery_speed", comment: "")
                            ],
                            iconName: "exclamationmark.octagon",
                            color: .red
                        )

                        // 新增：触发记录与反思指标部分
                        dataSection(
                            title: "trigger_and_reflection",
                            description: "trigger_reflection_description",
                            items: [
                                NSLocalizedString("trigger_awareness", comment: ""),
                                NSLocalizedString("reflection_depth", comment: ""),
                                NSLocalizedString("sharing_engagement", comment: ""),
                                NSLocalizedString("behavioral_insight", comment: "")
                            ],
                            iconName: "brain.fill",
                            color: .purple
                        )

                        dataSection(
                            title: "balance_index",
                            description: "balance_description",
                            items: [
                                NSLocalizedString("sensitivity", comment: ""),
                                NSLocalizedString("stability", comment: ""),
                                NSLocalizedString("progression", comment: ""),
                                NSLocalizedString("motivation", comment: "")
                            ],
                            iconName: "scale.3d",
                            color: .blue
                        )
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 科学依据
                    VStack(alignment: .leading, spacing: 16) {
                        Text("scientific_basis")
                            .font(.title2)
                            .fontWeight(.bold)

                        scienceSection(
                            title: "prefrontal_cortex",
                            description: "neuroscience_description",
                            reference: "Heatherton & Wagner, 2011"
                        )

                        scienceSection(
                            title: "willpower_resource_model",
                            description: "psychology_research",
                            reference: "Baumeister et al., 2018"
                        )

                        scienceSection(
                            title: "visualization_behavior_change",
                            description: "behavioral_science",
                            reference: "Fogg Behavior Model, 2020"
                        )
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 专家建议
                    VStack(alignment: .leading, spacing: 16) {
                        Text("expert_advice")
                            .font(.title2)
                            .fontWeight(.bold)

                        expertTipSection(
                            role: "neuroscientist",
                            tip: "neuroscientist_tip"
                        )

                        expertTipSection(
                            role: "behavioral_psychologist",
                            tip: "psychologist_tip"
                        )

                        expertTipSection(
                            role: "habit_research_expert",
                            tip: "habit_expert_tip"
                        )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 30)
            }
            .navigationTitle("willpower_science")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("close") {
                        dismiss()
                    }
                }
            }
        }
    }

    // 数据部分组件
    private func dataSection(title: String, description: String, items: [String], iconName: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: iconName)
                    .font(.system(size: 22))
                    .foregroundColor(color)
                    .frame(width: 30, height: 30)

                Text(NSLocalizedString(title, comment: ""))
                    .font(.headline)
                    .foregroundColor(.primary)
            }

            Text(NSLocalizedString(description, comment: ""))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.leading, 42)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.self) { item in
                    HStack(alignment: .top, spacing: 8) {
                        Text("•")
                            .foregroundColor(color)

                        Text(item)
                            .font(.system(size: 14))
                            .foregroundColor(.primary)
                    }
                }
            }
            .padding(.leading, 42)
        }
        .padding(.vertical, 8)
    }

    // 科学部分组件
    private func scienceSection(title: String, description: String, reference: String) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString(title, comment: ""))
                .font(.headline)
                .foregroundColor(.primary)

            Text(NSLocalizedString(description, comment: ""))
                .font(.subheadline)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)

            Text(reference)
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
        .padding(.vertical, 4)
    }

    // 专家建议组件
    private func expertTipSection(role: String, tip: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            ZStack {
                Circle()
                    .fill(Color.blue.opacity(0.1))
                    .frame(width: 44, height: 44)

                Image(systemName: "person.fill")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.blue)
            }

            VStack(alignment: .leading, spacing: 6) {
                Text(NSLocalizedString(role, comment: ""))
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(NSLocalizedString(tip, comment: ""))
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 4)
    }
}



// 自定义的按钮视图，确保即时反馈效果，完全复制DashboardPod的动画体验
struct InstantFeedbackButton<Content: View>: View {
    let action: () -> Void
    let content: Content

    @State private var isPressed = false
    @State private var dragStartTime: Date? = nil
    @State private var dragDistance: CGFloat = 0

    init(action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.action = action
        self.content = content()
    }

    var body: some View {
        content
            .scaleEffect(isPressed ? 0.96 : 1.0)  // 调整缩放比例为 0.96
            .animation(
                isPressed ?
                    .easeInOut(duration: 0.2) :
                    .interpolatingSpring(mass: 1.0, stiffness: 170, damping: 15, initialVelocity: 5),
                value: isPressed
            )
            .contentShape(Rectangle())
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        if dragStartTime == nil {
                            dragStartTime = Date()
                            // 手指按下时立即缩放
                            if dragDistance < 10 {
                                isPressed = true
                            }
                        }

                        dragDistance = sqrt(
                            pow(value.translation.width, 2) +
                            pow(value.translation.height, 2)
                        )

                        // 如果滑动超过阈值，取消缩放
                        if dragDistance >= 10 {
                            isPressed = false
                        }
                    }
                    .onEnded { value in
                        let dragDuration = Date().timeIntervalSince(dragStartTime ?? Date())

                        if dragDistance < 10 && dragDuration < 0.3 {
                            // 只在手指离开时执行动作
                            action()
                        }

                        // 手指离开时回弹
                        isPressed = false
                        dragStartTime = nil
                        dragDistance = 0
                    }
            )
    }
}

struct AboutXDoView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 标题
                    Text("about_xdo")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.top, 20)

                    // 核心理念
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Spacer()
                            VStack(spacing: 8) {
                                Text("less_to_do")
                                    .font(.system(size: 28, weight: .bold, design: .rounded))
                                    .foregroundColor(.blue)

                                Text("more_to_be")
                                    .font(.system(size: 28, weight: .bold, design: .rounded))
                                    .foregroundColor(.red)
                            }
                            Spacer()
                        }
                        .padding(.vertical, 8)

                        Divider()
                            .padding(.vertical, 8)

                        Text("busy_life_message")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Text("better_self_message")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .padding(.bottom, 8)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                    )

                    // 现代人的困境
                    VStack(alignment: .leading, spacing: 20) {
                        Text("why_need_xdo")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        VStack(alignment: .leading, spacing: 12) {
                            painPointRow(icon: "hourglass", text: "time_waste")
                            painPointRow(icon: "heart.fill", text: "health_crisis")
                            painPointRow(icon: "creditcard", text: "financial_pressure")
                            painPointRow(icon: "hand.thumbsdown", text: "self_doubt")
                        }
                        .padding(.bottom, 12)

                        Text("data_shows")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 8) {
                            dataPointRow(text: "habit_failure_rate")
                            dataPointRow(text: "daily_time_waste")
                            dataPointRow(text: "resolution_abandon")
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                    )

                    // X Do的独特价值
                    VStack(alignment: .leading, spacing: 16) {
                        Text("xdo_unique_value")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        featureRow(icon: "atom", color: .blue, title: "scientific_method", description: "scientific_description")
                        featureRow(icon: "hand.tap.fill", color: .green, title: "easy_to_use", description: "easy_description")
                        featureRow(icon: "chart.line.uptrend.xyaxis", color: .orange, title: "visual_progress", description: "visual_description")
                        featureRow(icon: "lock.shield.fill", color: .purple, title: "privacy_protection", description: "privacy_description")
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                    )

                    // 用户见证
                    VStack(alignment: .leading, spacing: 16) {
                        Text("user_testimonials")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        testimonialRow(name: NSLocalizedString("programmer_testimonial", comment: ""),
                                     text: NSLocalizedString("programmer_testimonial", comment: ""))

                        testimonialRow(name: NSLocalizedString("mother_testimonial", comment: ""),
                                     text: NSLocalizedString("mother_testimonial", comment: ""))
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                    )

                    // 版本信息
                    HStack {
                        Spacer()
                        VStack {
                            Text("X Do")
                                .font(.headline)
                            Text("version_number")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                    }
                    .padding(.top, 30)
                }
                .padding()
            }
            .navigationTitle("about_xdo")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("done") {
                        dismiss()
                    }
                }
            }
        }
    }

    // 痛点行视图
    private func painPointRow(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(.red)
                .frame(width: 24, height: 24)

            Text(NSLocalizedString(text, comment: ""))
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    // 数据点行视图
    private func dataPointRow(text: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: "chart.bar.fill")
                .font(.system(size: 16))
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)

            Text(NSLocalizedString(text, comment: ""))
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    // 用户见证行视图
    private func testimonialRow(name: String, text: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("\"\(text)\"")
                .font(.body)
                .foregroundColor(.primary)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )

            Text("- \(name)")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.leading, 8)
        }
    }

    // 功能行视图
    private func featureRow(icon: String, color: Color, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 36, height: 36)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color.opacity(0.15))
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(NSLocalizedString(title, comment: ""))
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(NSLocalizedString(description, comment: ""))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 8)
    }
}



// Privacy Policy Content View (used within navigation)
struct PrivacyPolicyContent: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text(NSLocalizedString("privacy_policy_title", comment: "隐私政策标题"))
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.bottom, 10)

                Text(NSLocalizedString("last_updated", comment: "最后更新时间"))
                    .font(.subheadline)
                    .foregroundStyle(.secondary)

                Group {
                    Text(NSLocalizedString("overview", comment: "概述标题"))
                        .font(.headline)

                    Text(NSLocalizedString("privacy_overview", comment: "隐私概述内容"))
                }

                Group {
                    Text(NSLocalizedString("information_collection", comment: "信息收集标题"))
                        .font(.headline)

                    Text(NSLocalizedString("collection_details", comment: "信息收集详情"))
                }

                Group {
                    Text(NSLocalizedString("information_usage", comment: "信息使用标题"))
                        .font(.headline)

                    Text(NSLocalizedString("usage_details", comment: "信息使用详情"))
                }

                Group {
                    Text(NSLocalizedString("data_storage_security", comment: "数据存储与安全标题"))
                        .font(.headline)

                    Text(NSLocalizedString("storage_details", comment: "数据存储详情"))
                }

                Group {
                    Text(NSLocalizedString("information_sharing", comment: "信息共享标题"))
                        .font(.headline)

                    Text(NSLocalizedString("sharing_details", comment: "信息共享详情"))
                }

                Group {
                    Text(NSLocalizedString("your_rights", comment: "用户权利标题"))
                        .font(.headline)

                    Text(NSLocalizedString("rights_details", comment: "用户权利详情"))
                }

                Group {
                    Text(NSLocalizedString("childrens_privacy", comment: "儿童隐私标题"))
                        .font(.headline)

                    Text(NSLocalizedString("children_details", comment: "儿童隐私详情"))
                }

                Group {
                    Text(NSLocalizedString("privacy_updates", comment: "隐私政策更新标题"))
                        .font(.headline)

                    Text(NSLocalizedString("updates_details", comment: "隐私政策更新详情"))
                }

                Group {
                    Text(NSLocalizedString("contact_us", comment: "联系我们标题"))
                        .font(.headline)

                    Text(NSLocalizedString("contact_details", comment: "联系方式详情"))
                }

                Text(NSLocalizedString("privacy_agreement", comment: "隐私协议同意声明"))
                    .italic()
                    .padding(.top, 20)
            }
            .padding()
        }
    }
}

// Note: PrivacyPolicyView is defined in SettingsView.swift
// The redeclaration has been removed to fix the duplicate declaration error

// Coming Soon页面
struct ComingSoonView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    let featureName: String
    let iconName: String
    let featureDescription: String
    let estimatedTime: String

    // 动画状态
    @State private var showIcon = false
    @State private var showTitle = false
    @State private var showDescription = false
    @State private var showTimeline = false
    @State private var showButton = false
    @State private var pulseEffect = false
    @State private var rotationAngle: Double = 0

    var body: some View {
        VStack(spacing: 0) {
            // 顶部区域 - 包含关闭按钮
            HStack {
                Spacer()
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 28))
                        .foregroundStyle(Color.secondary.opacity(0.7))
                }
                .padding(.trailing, 20)
                .padding(.top, 20)
            }

            // 主要内容区域
            ScrollView {
                VStack(spacing: 40) {
                    // 图标
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [.blue.opacity(0.6), .purple.opacity(0.6)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 120, height: 120)
                            .shadow(color: .blue.opacity(0.3), radius: 15, x: 0, y: 8)
                            .scaleEffect(showIcon ? 1 : 0.5)
                            .opacity(showIcon ? 1 : 0)

                        Image(systemName: iconName)
                            .font(.system(size: 50))
                            .foregroundStyle(.white)
                            .scaleEffect(pulseEffect ? 1.1 : 1.0)
                            .rotationEffect(.degrees(rotationAngle))
                    }
                    .padding(.top, 30)

                    // 标题和描述
                    VStack(spacing: 20) {
                        Text(NSLocalizedString(featureName, comment: ""))
                            .font(.system(size: 28, weight: .bold))
                            .foregroundStyle(.primary)
                            .opacity(showTitle ? 1 : 0)
                            .offset(y: showTitle ? 0 : 20)

                        Text("coming_soon")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundStyle(
                                LinearGradient(
                                    gradient: Gradient(colors: [.blue, .purple]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .opacity(showTitle ? 1 : 0)
                            .offset(y: showTitle ? 0 : 20)
                            .blur(radius: showTitle ? 0 : 5)

                        Text(NSLocalizedString(featureDescription, comment: ""))
                            .font(.system(size: 16))
                            .multilineTextAlignment(.center)
                            .foregroundStyle(.secondary)
                            .frame(maxWidth: 300)
                            .padding(.top, 10)
                            .opacity(showDescription ? 1 : 0)
                            .offset(y: showDescription ? 0 : 15)
                    }

                    // 时间线
                    VStack(spacing: 15) {
                        Text("estimated_release_time")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundStyle(.secondary)

                        Text(NSLocalizedString(estimatedTime, comment: ""))
                            .font(.system(size: 20, weight: .bold))
                            .foregroundStyle(.primary)

                        // 进度指示器
                        ProgressView(value: 0.7)
                            .progressViewStyle(LinearProgressViewStyle(tint: Color.blue))
                            .frame(width: 200)
                    }
                    .padding(.vertical)
                    .padding(.horizontal, 30)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(colorScheme == .dark ? Color(.systemGray6) : Color(.systemGray6).opacity(0.3))
                    )
                    .opacity(showTimeline ? 1 : 0)
                    .offset(y: showTimeline ? 0 : 30)

                    // 订阅通知按钮
                    Button(action: {
                        // 这里可以添加订阅通知的逻辑
                        withAnimation(.spring(duration: 0.5, bounce: 0.4)) {
                            pulseEffect.toggle()
                        }
                    }) {
                        HStack {
                            Image(systemName: "bell.fill")
                            Text("notify_feature_available")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundStyle(.white)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 30)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.blue, .purple.opacity(0.8)]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .clipShape(Capsule())
                        .shadow(color: .blue.opacity(0.3), radius: 5, x: 0, y: 3)
                    }
                    .scaleEffect(showButton ? 1 : 0.8)
                    .opacity(showButton ? 1 : 0)

                    Spacer()
                }
                .padding(.horizontal)
            }
        }
        .background(
            colorScheme == .dark ?
                Color(UIColor.systemBackground) :
                Color(UIColor.systemBackground)
        )
        .onAppear {
            // 顺序播放动画
            withAnimation(.spring(duration: 0.6, bounce: 0.4)) {
                showIcon = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showTitle = true
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
                withAnimation(.easeOut(duration: 0.6)) {
                    showDescription = true
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.spring(duration: 0.6)) {
                    showTimeline = true
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.3) {
                withAnimation(.spring(duration: 0.7, bounce: 0.3)) {
                    showButton = true
                }
            }

            // 图标缓慢旋转
            withAnimation(.linear(duration: 20).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }

            // 脉冲效果
            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                pulseEffect = true
            }
        }
    }
}

// 数据指标视图
struct DataMetricView: View {
    enum TrendDirection {
        case up, down, neutral
    }

    let title: String
    let value: String
    let detail: String
    let icon: String
    let color: Color
    let trend: TrendDirection

    // 获取趋势箭头
    private var trendIcon: String {
        switch trend {
        case .up: return "arrow.up"
        case .down: return "arrow.down"
        case .neutral: return "minus"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            // 标题行
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 12))
                    .foregroundColor(color)

                Text(NSLocalizedString(title, comment: ""))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                Spacer()

                // 趋势指示
                if trend != .neutral {
                    Image(systemName: trendIcon)
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(trend == .up ? .green : .red)
                }
            }

            // 数值行
            HStack(alignment: .firstTextBaseline) {
                Text(value)
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Spacer()

                Text(NSLocalizedString(detail, comment: ""))
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// 力量场可视化
struct ForceFieldView: View {
    var balance: Double // -1到1之间，负值表示诱惑强，正值表示意志力强
    var isAnimating: Bool
    var itemCount: Int = 0 // 项目数量，用于调整海浪高度

    // 用于动画的状态
    @State private var pulsePhase: CGFloat = 0.4
    @State private var touchLocation: CGPoint? = nil
    @State private var rippleStart: Date? = nil
    @State private var rippleStrength: CGFloat = 0
    @State private var feedbackGenerator = UIImpactFeedbackGenerator(style: .medium)

    // 配色
    private var leftColor: Color { Color.red }
    private var rightColor: Color { Color.green }
    private var centerColor: Color { Color.white }

    // 计算平衡点位置 (0-1范围)
    private var balancePoint: CGFloat {
        // 将balance从-1...1映射到0...1，当balance为-1时balancePoint为1（红色区域最大）
        return CGFloat(1 - (balance + 1) / 2)
    }

    // 根据项目数量计算波浪高度百分比 (0.1-0.618)
    private var waveHeightPercentage: CGFloat {
        if itemCount <= 0 {
            return 0.10 // 0 个会话时 10%
        }
        if itemCount <= 10 {
            // 1~10 个会话：10%~30%
            let progress = CGFloat(itemCount) / 10.0
            return 0.10 + progress * 0.20
        } else if itemCount <= 25 {
            // 11~25 个会话：30%~50%
            let progress = CGFloat(itemCount - 10) / 15.0
            return 0.30 + progress * 0.20
        } else {
            // 26~50 个会话：50%~61.8%
            let maxItems: CGFloat = 50.0
            let cappedCount = min(CGFloat(itemCount), maxItems)
            let progress = (cappedCount - 25.0) / 25.0
            return min(0.50 + progress * 0.118, 0.618)
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变 - 修改为系统背景色
                Color(UIColor.systemBackground) // 使用系统背景色

                // 波形层
                TimelineView(.animation(minimumInterval: 1/60)) { timeline in
                    ZStack {
                        // 波形Canvas
                        Canvas { context, size in
                            // 计算平衡点
                            let centerX = size.width * balancePoint

                            // 计算当前动画相位 - 直接使用时间戳
                            let speed = 0.618 // 控制波浪移动速度 - 使用黄金比例
                            let phase = isAnimating ? timeline.date.timeIntervalSince1970 * speed : 0

                            // 绘制波形
                            drawWaveFill(context: context, size: size, centerX: centerX, phase: phase)
                            drawWaveStroke(context: context, size: size, centerX: centerX, isLeftSide: true, phase: phase)
                            drawWaveStroke(context: context, size: size, centerX: centerX, isLeftSide: false, phase: phase)

                            // 绘制触摸波纹效果
                            if let touchPoint = touchLocation, let rippleTime = rippleStart {
                                let elapsed = timeline.date.timeIntervalSince(rippleTime)
                                if elapsed < 1.5 { // 1.5秒内显示波纹
                                    drawTouchRipple(context: context, size: size, point: touchPoint, elapsed: elapsed)
                                }
                            }
                        }
                        .drawingGroup() // 启用Metal加速渲染

                        // 平衡点指示器和光晕
                        // 这部分从Canvas中分离出来，成为独立的视图
                        let buoyY = calculateBuoyY(
                            size: geometry.size,
                            phase: isAnimating ? timeline.date.timeIntervalSince1970 * 0.618 : 0
                        )

                        // 平衡点光晕
                        RadialGradient(
                            gradient: Gradient(colors: [
                                centerColor.opacity(0.6),
                                centerColor.opacity(0.0)
                            ]),
                            center: .center,
                            startRadius: 0,
                            endRadius: 40
                        )
                        .frame(width: 80, height: 80)
                        .position(
                            x: geometry.size.width * balancePoint,
                            y: buoyY
                        )
                        .opacity(pulsePhase)

                        // 平衡点指示器
                        Circle()
                            .fill(Color.white)
                            .frame(width: 12, height: 12)
                            .shadow(color: Color.black.opacity(0.3), radius: 2)
                            .position(
                                x: geometry.size.width * balancePoint,
                                y: buoyY
                            )
                            .overlay(
                                Circle()
                                    .stroke(Color.black.opacity(0.2), lineWidth: 1)
                                    .frame(width: 12, height: 12)
                                    .position(
                                        x: geometry.size.width * balancePoint,
                                        y: buoyY
                                    )
                            )
                    }
                }

                // 力场标签
                HStack {
                    // 左侧标签 - 诱惑力场
                    Text("temptation_field")
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(leftColor.opacity(0.7))
                        )
                        .padding(.leading, 12)

                    Spacer()

                    // 右侧标签 - 意志力场
                    Text("willpower_field")
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(rightColor.opacity(0.7))
                        )
                        .padding(.trailing, 12)
                }
                .frame(maxHeight: .infinity, alignment: .top)
                .padding(.top, 12)
            }
            
            .onAppear {
                // 启动脉冲动画
                withAnimation(.easeInOut(duration: 3).repeatForever(autoreverses: true)) {
                    pulsePhase = 0.7
                }

                // 预加载触觉反馈生成器
                feedbackGenerator.prepare()
            }
        }
    }

    // 绘制触摸波纹效果
    private func drawTouchRipple(
        context: GraphicsContext,
        size: CGSize,
        point: CGPoint,
        elapsed: TimeInterval
    ) {
        let maxRadius: CGFloat = min(size.width, size.height) * 0.3
        let progress = min(1.0, CGFloat(elapsed) / 1.5)
        let radius = maxRadius * progress
        let opacity = (1.0 - progress) * 0.5 * rippleStrength

        let circle = Path(ellipseIn: CGRect(
            x: point.x - radius,
            y: point.y - radius,
            width: radius * 2,
            height: radius * 2
        ))

        // 中心光晕
        context.fill(
            Path(ellipseIn: CGRect(
                x: point.x - radius * 0.2,
                y: point.y - radius * 0.2,
                width: radius * 0.4,
                height: radius * 0.4
            )),
            with: .color(Color.white.opacity(opacity * 2))
        )

        // 扩散圆环
        context.stroke(
            circle,
            with: .color(Color.white.opacity(opacity)),
            lineWidth: 2 - progress
        )
    }

    // 计算浮标的Y位置
    private func calculateBuoyY(size: CGSize, phase: Double) -> CGFloat {
        // 直接从波浪绘制函数获取交界处的实际Y位置
        let centerX = size.width * balancePoint

        // 计算左右波浪在交界处的Y值
        let leftY = waveYPosition(
            at: centerX,
            centerX: centerX,
            size: size,
            phase: phase,
            isLeftSide: true
        )

        let rightY = waveYPosition(
            at: centerX,
            centerX: centerX,
            size: size,
            phase: phase,
            isLeftSide: false
        )

        // 使用两侧计算的平均值以确保平滑过渡
        // 由于交界处左右两侧的计算可能略有差异，取平均值更准确
        let avgY = (leftY + rightY) / 2

        // 不再使用固定偏移，而是让浮标精确地位于波峰上
        // 只上移一点点（3像素）确保浮标看起来是浮在水面上，而不是半沉在水中
        return avgY - 3
    }

    // 绘制波形填充部分
    private func drawWaveFill(
        context: GraphicsContext,
        size: CGSize,
        centerX: CGFloat,
        phase: Double
    ) {
        // 绘制左侧波形填充
        let leftPath = createWavePath(
            size: size,
            centerX: centerX,
            isLeftSide: true,
            phase: phase
        )

        // 绘制右侧波形填充
        let rightPath = createWavePath(
            size: size,
            centerX: centerX,
            isLeftSide: false,
            phase: phase
        )

        // 应用左侧填充渐变
        context.fill(
            leftPath,
            with: .linearGradient(
                Gradient(colors: [
                    leftColor.opacity(0.7),
                    leftColor.opacity(0.3)
                ]),
                startPoint: CGPoint(x: 0, y: size.height/2),
                endPoint: CGPoint(x: centerX, y: size.height/2)
            )
        )

        // 应用右侧填充渐变
        context.fill(
            rightPath,
            with: .linearGradient(
                Gradient(colors: [
                    rightColor.opacity(0.3),
                    rightColor.opacity(0.7)
                ]),
                startPoint: CGPoint(x: centerX, y: size.height/2),
                endPoint: CGPoint(x: size.width, y: size.height/2)
            )
        )
    }

    // 绘制波形描边部分
    private func drawWaveStroke(
        context: GraphicsContext,
        size: CGSize,
        centerX: CGFloat,
        isLeftSide: Bool,
        phase: Double
    ) {
        let startX = isLeftSide ? 0 : centerX
        let endX = isLeftSide ? centerX : size.width
        let strokeColor = isLeftSide ? leftColor.opacity(0.8) : rightColor.opacity(0.8)

        // 波形密度
        let steps = 100

        // 调整步骤范围，避免中心处重叠
        let startStep = isLeftSide ? 0 : 2
        let endStep = isLeftSide ? steps - 2 : steps

        // 创建波形路径
        var path = Path()

        // 获取第一个点
        let firstX = startX + (endX - startX) * CGFloat(startStep) / CGFloat(steps)
        let firstY = waveYPosition(at: firstX, centerX: centerX, size: size, phase: phase, isLeftSide: isLeftSide)

        // 开始路径
        path.move(to: CGPoint(x: firstX, y: firstY))

        // 绘制波形路径
        for i in (startStep+1)...endStep {
            let x = startX + (endX - startX) * CGFloat(i) / CGFloat(steps)
            let y = waveYPosition(at: x, centerX: centerX, size: size, phase: phase, isLeftSide: isLeftSide)
            path.addLine(to: CGPoint(x: x, y: y))
        }

        // 绘制描边
        context.stroke(path, with: .color(strokeColor), lineWidth: 1.0)
    }

    // 创建波形路径
    private func createWavePath(
        size: CGSize,
        centerX: CGFloat,
        isLeftSide: Bool,
        phase: Double
    ) -> Path {
        let startX = isLeftSide ? 0 : centerX
        let endX = isLeftSide ? centerX : size.width

        var path = Path()
        path.move(to: CGPoint(x: startX, y: size.height/2))

        // 波形密度
        let steps = 100

        // 绘制波形路径
        for i in 0...steps {
            let x = startX + (endX - startX) * CGFloat(i) / CGFloat(steps)
            let y = waveYPosition(at: x, centerX: centerX, size: size, phase: phase, isLeftSide: isLeftSide)
            path.addLine(to: CGPoint(x: x, y: y))
        }

        // 封闭路径以便填充
        path.addLine(to: CGPoint(x: endX, y: size.height))
        path.addLine(to: CGPoint(x: startX, y: size.height))
        path.closeSubpath()

        return path
    }

    // 计算波形在指定位置的Y坐标
    private func waveYPosition(
        at x: CGFloat,
        centerX: CGFloat,
        size: CGSize,
        phase: Double,
        isLeftSide: Bool
    ) -> CGFloat {
        // 计算归一化的X坐标 (0到1)
        let normalizedX = isLeftSide ? 1.0 - (x / centerX) : (x - centerX) / (size.width - centerX)

        // 根据项目数量调整波形参数
        let baseAmplitude: CGFloat = 10.0
        let frequency: CGFloat = 15.0

        // 计算海浪基准位置 (从底部向上计算)
        // waveHeightPercentage为0.1表示海浪位于距离底部10%的高度
        // waveHeightPercentage为1.0表示海浪位于画布顶部
        let waveBaselineY = size.height * (1.0 - waveHeightPercentage)

        // 波浪振幅 (根据高度稍微调整，使波浪在较高位置时更加明显)
        let adjustedAmplitude = baseAmplitude * (1.0 + waveHeightPercentage * 0.3)

        // 添加更复杂的波纹效果，使用多个不同频率的正弦波叠加
        let waveHeight = sin(normalizedX * frequency + CGFloat(phase)) * adjustedAmplitude
                       + sin(normalizedX * frequency * 1.5 + CGFloat(phase * 0.8)) * adjustedAmplitude * 0.3
                       + sin(normalizedX * frequency * 0.5 + CGFloat(phase * 1.2)) * adjustedAmplitude * 0.2

        // 中心扰动
        let centerDisturbPhase = phase * 1.2
        let centerDisturb = sin(normalizedX * 30 + CGFloat(centerDisturbPhase)) * 6.0
        let centerEffect = (1.0 - abs(normalizedX - 0.5) * 2) * centerDisturb

        // 添加触摸点波纹影响
        var touchEffect: CGFloat = 0
        if let touchPoint = touchLocation, rippleStrength > 0 {
            let touchX = isLeftSide ?
                (touchPoint.x < centerX ? touchPoint.x : centerX) :
                (touchPoint.x > centerX ? touchPoint.x : centerX)

            let distanceX = abs(touchX - x)
            let maxInfluenceRadius = size.width * 0.2

            if distanceX < maxInfluenceRadius {
                let influenceFactor = (1 - distanceX / maxInfluenceRadius) * 10 * rippleStrength
                touchEffect = sin(distanceX * 0.1 + CGFloat(phase * 3)) * influenceFactor
            }
        }

        // 最终Y坐标
        // 确保波浪不会超出画布边界
        return max(10.0, min(size.height - 10.0, waveBaselineY + waveHeight + centerEffect + touchEffect))
    }
}

// 打字机效果文本组件
struct TypingText: View {
    let text: String
    @State private var displayedText: String = ""
    @State private var currentIndex: Int = 0

    // 控制打字速度与延迟
    private let typingSpeed: Double = 0.05
    private let initialDelay: Double = 0.8  // 初始延迟0.5秒

    var body: some View {
        Text(displayedText)
            .onAppear {
                startTypingAnimation()
            }
    }

    private func startTypingAnimation() {
        // 重置显示状态
        displayedText = ""
        currentIndex = 0

        // 添加初始延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + initialDelay) {
            // 延迟后启动定时器实现打字效果
            Timer.scheduledTimer(withTimeInterval: typingSpeed, repeats: true) { timer in
                if currentIndex < text.count {
                    let index = text.index(text.startIndex, offsetBy: currentIndex)
                    displayedText.append(text[index])
                    currentIndex += 1
                } else {
                    timer.invalidate()
                }
            }
        }
    }
}

// 搜索字段视图组件
struct SearchFieldView: View {
    @Binding var searchText: String
    @Binding var isSearching: Bool
    @Binding var opacity: Double
    @Binding var scale: CGFloat
    @FocusState var isFocused: Bool

    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)

                TextField(NSLocalizedString("search_habits", comment: "搜索习惯的占位文字"), text: $searchText)
                    .font(.system(size: 15))
                    .padding(.vertical, 9)
                    .accentColor(.blue) // 设置光标颜色
                    .submitLabel(.search) // 设置键盘上的回车键为搜索
                    .focused($isFocused) // 将TextField与FocusState绑定
                    .overlay(
                        ZStack {
                            if !searchText.isEmpty {
                                HStack {
                                    Spacer()
                                    Button(action: {
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            searchText = ""
                                        }
                                    }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(.secondary)
                                            .padding(.trailing, 8)
                                    }
                                    .transition(.opacity)
                                }
                            }
                        }
                    )
            }
            .padding(.horizontal, 2)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(UIColor.secondarySystemBackground))
                    .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
            )
        }
        .opacity(opacity)
        .scaleEffect(scale)
        .transition(
            .asymmetric(
                insertion: .opacity.combined(with: .scale(scale: 0.96)).combined(with: .offset(y: -5)),
                removal: .opacity.combined(with: .scale(scale: 0.96)).combined(with: .offset(y: -5))
            )
        )
    }
}




// 新增：X Moment 菜单 Popover 内容视图
struct XMomentMenuView: View {
    @Environment(\.dismiss) private var dismiss
    // 接收必要的绑定和对象
    @Binding var showingXMomentStats: Bool
    @Binding var showingTemplateSettings: Bool
    @Binding var showingThemeSettings: Bool
    @Binding var showingXMomentInfo: Bool
    @ObservedObject var templateManager: TimingTemplateManager // 使用 ObservedObject

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 1. 统计按钮选项
            Button {
                showingXMomentStats = true
                dismiss() // 关闭 popover
            } label: {
                Label(NSLocalizedString("willpower.menu.stats", comment: "Stats menu item"), systemImage: "chart.bar.fill")
            }
            .padding(.vertical, 12)
            .padding(.horizontal)

            Divider()

            // 2. 计时模板选择按钮选项
            Button {
                showingTemplateSettings = true
                dismiss()
            } label: {
                Label(NSLocalizedString("willpower.menu.template", comment: "Template menu item"), systemImage: templateManager.currentTemplate.icon)
            }
            .padding(.vertical, 12)
            .padding(.horizontal)

            Divider()

            // 3. 主题设置按钮选项
            Button {
                showingThemeSettings = true
                dismiss()
            } label: {
                Label(NSLocalizedString("willpower.menu.theme", comment: "Theme menu item"), systemImage: "paintbrush")
            }
            .padding(.vertical, 12)
            .padding(.horizontal)

            Divider()

            // 4. 信息按钮选项
            Button {
                showingXMomentInfo = true
                dismiss()
            } label: {
                Label(NSLocalizedString("willpower.menu.about", comment: "About X Moment menu item"), systemImage: "info.circle")
            }
            .padding(.vertical, 12)
            .padding(.horizontal)
        }
        .padding(.vertical, 5) // 给VStack整体加一点垂直padding
        .background(.ultraThinMaterial) // 使用毛玻璃背景
        .cornerRadius(10) // 添加圆角
        .frame(minWidth: 180) // 设置最小宽度
    }
}

// 新增：横向菜单按钮样式
struct XMomentHorizontalButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 14)) // 图标大小
            .foregroundColor(.white.opacity(0.9)) // <-- Change to white
            .padding(6) // 内边距
            .background(configuration.isPressed ? Color.secondary.opacity(0.2) : Color.clear) // 按下背景反馈
            .clipShape(Circle())
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0) // 按下缩放反馈
            .animation(.spring(response: 0.2, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

// ... rest of the file ...

// 当日 X Moment 会话列表视图
struct DailyXMomentSessionsView: View {
    @EnvironmentObject private var dataStore: DataStore
    @Environment(\.colorScheme) private var colorScheme
    // 新增：用于导航到详情页
    @State private var selectedSession: XMomentSession? = nil
    @State private var showingSessionDetail: Bool = false
    
    // 获取今日会话
    private var todaySessions: [XMomentSession] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        return dataStore.getXMomentSessionsUnambiguous().filter { session in
            calendar.isDate(session.startTime, inSameDayAs: today)
        }.sorted { $0.startTime > $1.startTime }
    }
    
    // 当日统计
    private var dailyStats: (totalDuration: TimeInterval, completedCount: Int, interruptedCount: Int, overclockCount: Int) {
        var stats = (totalDuration: TimeInterval(0), completedCount: 0, interruptedCount: 0, overclockCount: 0)
        
        for session in todaySessions {
            stats.totalDuration += session.duration
            if session.isInterrupted {
                stats.interruptedCount += 1
            } else {
                stats.completedCount += 1
            }
            if session.overclockFactor > 1.0 {
                stats.overclockCount += 1
            }
        }
        
        return stats
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题栏
            HStack {
                Text(NSLocalizedString("today_sessions", comment: "今日会话"))
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                
                Spacer()
                
                NavigationLink(destination: DailyFocusDetailView(date: Date(), sessions: todaySessions)) {
                    Text(NSLocalizedString("view_details", comment: "查看详情"))
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                }
                .simultaneousGesture(TapGesture().onEnded {
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()
                })
            }
            
            if todaySessions.isEmpty {
                // 空状态视图
                VStack(spacing: 8) {
                    Image(systemName: "timer.circle")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .padding(.bottom, 4)
                    
                    Text(NSLocalizedString("no_sessions_today", comment: "今日暂无专注记录"))
                        .font(.system(size: 15))
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 30)
                .background(Color(.secondarySystemBackground))
                .cornerRadius(12)
            } else {
                // 会话列表
                VStack(spacing: 12) {
                    // 统计数据
                    HStack(spacing: 20) {
                        StatCard(
                            title: NSLocalizedString("total_duration", comment: "总时长"),
                            value: formatDuration(dailyStats.totalDuration),
                            icon: "clock.fill",
                            color: .blue
                        )
                        
                        StatCard(
                            title: NSLocalizedString("completed", comment: "已完成"),
                            value: "\(dailyStats.completedCount)",
                            icon: "checkmark.circle.fill",
                            color: .green
                        )
                        
                        StatCard(
                            title: NSLocalizedString("interrupted", comment: "已中断"),
                            value: "\(dailyStats.interruptedCount)",
                            icon: "xmark.circle.fill",
                            color: .red
                        )
                        
                        StatCard(
                            title: NSLocalizedString("overclock", comment: "超频"),
                            value: "\(dailyStats.overclockCount)",
                            icon: "bolt.fill",
                            color: .orange
                        )
                    }
                    .padding(.vertical, 8)
                    
                    // 最新2条会话记录
                    VStack(spacing: 12) {
                        ForEach(todaySessions.prefix(2)) { session in
                            Button(action: {
                                let generator = UIImpactFeedbackGenerator(style: .light)
                                generator.impactOccurred()
                                selectedSession = session
                                showingSessionDetail = true
                            }) {
                                VerticalSessionCard(session: session)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
            }
        }
        .padding(16)
        // 新写法：导航到详情页
        .navigationDestination(isPresented: $showingSessionDetail) {
            if let session = selectedSession {
                XMomentSessionDetailView(session: session)
            }
        }
    }
    
    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = duration / 3600.0
        // Use NSLocalizedString for "hours_unit_detailed" to allow for different language support.
        // Ensure "hours_unit_detailed" = "小時"; is added to your Localizable.strings for zh-Hant.
        return String(format: "%.1f %@", hours, NSLocalizedString("hours_unit_detailed", comment: "小时单位"))
    }
}

// 新的垂直会话卡片组件
struct VerticalSessionCard: View {
    let session: XMomentSession
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack(spacing: 16) {
            // 左侧时间和状态
            VStack(alignment: .leading, spacing: 4) {
                Text(session.startTime, style: .time)
                    .font(.system(size: 15, weight: .medium))
                
                HStack(spacing: 4) {
                    Image(systemName: session.isInterrupted ? "xmark.circle.fill" : "checkmark.circle.fill")
                        .foregroundColor(session.isInterrupted ? .red : .green)
                        .font(.system(size: 12))
                    
                    Text(session.isInterrupted ? NSLocalizedString("session_status_interrupted", comment: "Session status: Interrupted") : NSLocalizedString("session_status_completed", comment: "Session status: Completed"))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 右侧持续时间和超频信息
            VStack(alignment: .trailing, spacing: 4) {
                HStack(spacing: 4) {
                    Text(formatDuration(session.duration))
                        .font(.system(size: 15, weight: .semibold))
                    
                    if session.overclockFactor > 1.0 {
                        Text("x\(String(format: "%.1f", session.overclockFactor))")
                            .font(.system(size: 11))
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.2))
                            .foregroundColor(.orange)
                            .cornerRadius(4)
                    }
                }
                
                if let category = session.category, !category.isEmpty {
                    Text(NSLocalizedString(category, comment: "分类名称"))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(12)
        .background(colorScheme == .dark ? Color(.systemGray6) : Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? "0m"
    }
}

// 统计卡片组件
struct StatCard: View {
    let title: String // 保留但不显示
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 12))
                .foregroundColor(color)
            
            Text(value)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
                .fixedSize(horizontal: true, vertical: false) // Prevent wrapping for horizontal layout
                .lineLimit(1) // Ensure it stays on one line
        }
        .frame(maxWidth: .infinity)
    }
}

// 意志力数据指标视图
struct WillpowerMetricsView: View {
    @EnvironmentObject private var dataStore: DataStore
    let xMomentState: WillpowerTugOfWar.XMomentState
    
    private func formatDurationToHours(_ duration: TimeInterval) -> String {
        let hours = duration / 3600.0
        return String(format: "%.1f", hours)
    }
    
    var body: some View {
        HStack(spacing: 12) {
            if xMomentState == .inactive {
                // 中断次数指标
                DataMetricView(
                    title: "interrupted_sessions",
                    value: String(dataStore.getInterruptedSessionsCount()),
                    detail: NSLocalizedString("total_interruptions", comment: "总中断次数"),
                    icon: "xmark.circle.fill",
                    color: .red,
                    trend: .neutral
                )

                // 分隔线
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 1)
                    .padding(.vertical, 8)

                // 总专注时长指标
                DataMetricView(
                    title: "total_focus_hours",
                    value: formatDurationToHours(dataStore.getTotalXMomentDuration()),
                    detail: NSLocalizedString("total_focus_time", comment: "总专注时长"),
                    icon: "clock.fill",
                    color: .green,
                    trend: .up
                )
            } else {
                // X Moment 模式下的指标
                // 左侧：XU 指标
                DataMetricView(
                    title: NSLocalizedString("willpower.metric.totalXu", comment: "Total XU metric title"),
                    value: String(format: "%.0f", dataStore.getTotalXUnits()),
                    detail: String(format: NSLocalizedString("willpower.metric.todayFormat", comment: "Today value format. Param: Value"), String(format: "%.0f", dataStore.getTodayXUnits())),
                    icon: "target",
                    color: .blue,
                    trend: .neutral
                )

                // 分隔线
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 1)
                    .padding(.vertical, 8)

                // 右侧：专注时长指标
                DataMetricView(
                    title: NSLocalizedString("willpower.metric.totalFocusHours", comment: "Total focus hours metric title"),
                    value: formatDurationToHours(dataStore.getTotalXMomentDuration()),
                    detail: String(format: NSLocalizedString("willpower.metric.thisWeekFormat", comment: "This week value format. Param: Value"), formatDurationToHours(dataStore.getWeekXMomentDuration())),
                    icon: "timer",
                    color: .purple,
                    trend: .neutral
                )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
}
