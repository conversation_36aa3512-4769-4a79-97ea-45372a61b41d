<!-- Generated by SpecStory -->

# 解决StatisticsView.swift中的图表闪烁问题 (2025-04-28 22:42:24)

_**User**_

@StatisticsView.swift 这个图表下在各个标签切换的时候，屏幕滑到一半图表会刷新闪一下 体验极其糟糕

---

_**Assistant**_

[MODE: RESEARCH]

让我分析一下问题。我看到在 `StatisticsView.swift` 中，标签切换时的闪烁问题主要有以下几个原因：

1. 使用了过多的动画和过渡效果
2. 图表数据的重新计算和刷新时机不合适
3. TabView 的切换动画与内容动画冲突

我已经在代码中看到了一些相关的动画代码，让我们进一步研究解决方案。

[MODE: PLAN]

我建议按以下步骤修复这个问题：

1. 移除 TabView 的默认动画，使用自定义的平滑过渡
2. 优化图表数据的加载时机，避免不必要的重新计算
3. 移除多余的动画效果，只保留必要的动画
4. 优化状态管理，减少不必要的视图刷新

请确认是否同意这个计划？

[MODE: EXECUTE]

收到确认后，我将开始实施修复。首先我会修改 TabView 的动画处理和图表刷新逻辑。

让我知道是否可以继续执行这个计划。

---

