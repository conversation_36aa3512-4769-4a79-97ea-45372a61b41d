<!-- Generated by SpecStory -->

# Localization of Sub-category Enumeration (2025-05-04 07:11:25)

_**User**_

@TriggerAnalysisView.swift 这个图表只有【子分类分布】不能调用本地字符串而是显示键名 其他部分都是正常显示的 帮我排查增么回事



---

_**Assistant**_

[MODE: RESEARCH]
[MEMORY BANK: INACTIVE]

让我帮您分析这个问题。我会先查看相关的代码部分。

首先，我看到图表相关的代码在 `TriggerChartView` 结构体中。让我检查显示子分类名称的相关代码：

```12:15:app/components/Todo.tsx
// 获取更短的显示名称，适合图表显示
private func getShortDisplayName(_ trigger: Trigger) -> String {
    if let subCategory = trigger.subCategory {
        // 使用子分类名称，更短
        return subCategory.rawValue
    }
    // 名称太长时进行截断
    let desc = trigger.triggerDesc
    return desc.isEmpty ? trigger.triggerType.rawValue : 
           (desc.count > 10 ? String(desc.prefix(10)) + "..." : desc)
}
```

我发现问题所在了。在 `getShortDisplayName` 函数中，当显示子分类时，直接返回了 `subCategory.rawValue`，而没有使用 `NSLocalizedString` 进行本地化。

这就是为什么图表中子分类显示的是键名而不是本地化后的字符串。让我帮您修复这个问题。

我将编写一个修复：

---

```diff
      private func getShortDisplayName(_ trigger: Trigger) -> String {
          if let subCategory = trigger.subCategory {
-             // 使用子分类名称，更简短
-             return subCategory.rawValue
+             // 使用子分类名称，更短，添加本地化
+             return NSLocalizedString(subCategory.rawValue, comment: "Sub trigger category")
          }
          // 名称太长时进行截断
```

---

这个修改应该解决了图表中子分类显示键名的问题。现在，子分类名称将会正确地使用本地化字符串显示，就像其他部分一样。

修改的内容：
1. 在返回子分类名称时，添加了 `NSLocalizedString` 包装
2. 使用 `subCategory.rawValue` 作为本地化的键
3. 添加了适当的注释说明本地化

这样修改后，图表中的子分类名称应该会正确显示本地化后的文字，而不是原始的键名。您可以测试一下，看看是否解决了问题。如果还有其他问题，请随时告诉我。

---

_**User**_

还是显示键名 会不会是我们改错文件了 你看看是不是这个文件才对@EnhancedTriggerAnalysisSection.swift 

---

_**Assistant**_

[MODE: RESEARCH]
[MEMORY BANK: INACTIVE]

是的，我发现问题了。在 `EnhancedTriggerAnalysisSection.swift` 文件中，子分类分布图表的问题出在这里：

```swift
private var subCategoryDistribution: [(category: String, mainCategory: Trigger.TriggerMainCategory, count: Int)] {
    let triggers = dataStore.getTriggersForHabit(habitId: habit.id)
    var distribution: [String: (Trigger.TriggerMainCategory, Int)] = [:]
    
    for trigger in triggers {
        if let subCategory = trigger.subCategory {
            let categoryName = subCategory.rawValue  // 这里直接使用了 rawValue
            let mainCategory = subCategory.mainCategory
            distribution[categoryName, default: (mainCategory, 0)].1 += 1
        }
    }
    
    return distribution.map { (category: $0.key, mainCategory: $0.value.0, count: $0.value.1) }
        .sorted { $0.count > $1.count }
}
```

让我修复这个问题：

---

```diff
          for trigger in triggers {
              if let subCategory = trigger.subCategory {
-                 let categoryName = subCategory.rawValue
+                 let categoryName = NSLocalizedString(subCategory.rawValue, comment: "Sub trigger category")
                  let mainCategory = subCategory.mainCategory
                  distribution[categoryName, default: (mainCategory, 0)].1 += 1
```

---

我已经修改了代码，主要变更是：
1. 将 `subCategory.rawValue` 改为 `NSLocalizedString(subCategory.rawValue, comment: "Sub trigger category")`
2. 这样子分类名称会在显示时使用本地化的字符串，而不是直接显示键名

这个修改应该能解决子分类分布图表显示键名的问题。现在图表应该会显示正确的本地化文本了。请您测试一下看看是否解决了问题。如果还有其他问题，请告诉我。

---

_**User**_

是的 解决了

---

_**Assistant**_

[MODE: RESEARCH]
[MEMORY BANK: INACTIVE]

很高兴问题解决了！让我总结一下刚才的修复：

1. 问题所在：子分类分布图表中直接使用了 `subCategory.rawValue`，导致显示了原始键名而不是本地化的文本
2. 解决方案：在 `subCategoryDistribution` 计算属性中，使用 `NSLocalizedString(subCategory.rawValue, comment: "Sub trigger category")` 来获取本地化的文本
3. 修复效果：现在子分类分布图表可以正确显示本地化后的中文文本，与其他部分保持一致

这个问题的修复也提醒我们在处理需要显示的文本时，要始终记得使用本地化函数，特别是在处理枚举的 `rawValue` 时。

如果您还有其他需要帮助的地方，请随时告诉我。

---

