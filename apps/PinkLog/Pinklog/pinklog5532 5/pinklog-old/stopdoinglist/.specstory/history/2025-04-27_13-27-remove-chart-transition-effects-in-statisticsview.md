<!-- Generated by SpecStory -->

# Remove Chart Transition Effects in StatisticsView (2025-04-27 21:27:35)

_**User**_

这个视图内各个图表间切换的过渡动效 现在的太乱了@StatisticsView.swift 一点也不优雅，请你重写

---

_**Assistant**_

[MODE: RESEARCH]

让我分析一下当前 StatisticsView.swift 中的动画过渡问题：

1. 主要问题在于动画的触发和控制比较分散，使用了多个不同的状态变量：
- `animateCharts`
- `showInsights` 
- `selectedTab`
- `selectedTimeRange`

2. 动画效果混杂:
- 使用了多种动画类型(.easeInOut, .spring, .asymmetric)
- 延迟时间不统一
- 动画组合方式不一致

3. 过渡效果不连贯:
- 有些组件使用了硬编码的延迟时间
- 缺乏整体协调性
- 动画时序不合理

让我重新设计一个更优雅的动画系统。

[MODE: PLAN]

重构动画系统的计划:

1. 统一动画状态管理
- 创建专门的动画状态枚举
- 使用单一的动画控制器
- 实现可预测的动画序列

2. 设计统一的过渡效果
- 定义标准动画时长
- 创建一致的动画曲线
- 实现平滑的组件切换

3. 优化动画性能
- 减少不必要的动画触发
- 优化动画计算
- 实现更流畅的过渡

4. 实现组件级动画控制
- 为每个主要组件创建独立的动画控制器
- 实现组件间的动画协调
- 添加动画完成回调

[MODE: EXECUTE]

让我开始实现这个改进计划：

---

```diff
      @State private var selectedTimeRange = TimeRange.week
      @State private var selectedTab = DashboardTab.overview
-     @State private var animateCharts = false
+     
+     // 新的动画状态管理
+     @State private var animationState = ViewAnimationState()
+     
+     // 动画状态结构体
+     struct ViewAnimationState {
+         var isAnimating = false
+         var currentPhase: AnimationPhase = .initial
+         var completedPhases: Set<AnimationPhase> = []
+         
+         enum AnimationPhase: Hashable {
+             case initial
+             case header
+             case metrics
+             case charts
+             case cards
+             case insights
+             
+             var delay: Double {
+                 switch self {
+                 case .initial: return 0
+                 case .header: return 0.1
+                 case .metrics: return 0.3
+                 case .charts: return 0.5
+                 case .cards: return 0.7
+                 case .insights: return 0.9
+                 }
+             }
+             
+             var duration: Double {
+                 switch self {
+                 case .initial: return 0
+                 case .header: return 0.4
+                 case .metrics: return 0.5
+                 case .charts: return 0.6
+                 case .cards: return 0.5
+                 case .insights: return 0.6
+                 }
+             }
+         }
+         
+         mutating func startAnimation() {
+             isAnimating = true
+             currentPhase = .initial
+             completedPhases.removeAll()
+         }
+         
+         mutating func completePhase(_ phase: AnimationPhase) {
+             completedPhases.insert(phase)
+             if phase == currentPhase {
+                 advanceToNextPhase()
+             }
+         }
+         
+         mutating func advanceToNextPhase() {
+             switch currentPhase {
+             case .initial: currentPhase = .header
+             case .header: currentPhase = .metrics
+             case .metrics: currentPhase = .charts
+             case .charts: currentPhase = .cards
+             case .cards: currentPhase = .insights
+             case .insights: isAnimating = false
+             }
+         }
+     }
+     
+     // 标准动画配置
+     private struct AnimationConfig {
+         static let standard = Animation.spring(response: 0.5, dampingFraction: 0.8)
+         static let slow = Animation.spring(response: 0.7, dampingFraction: 0.7)
+         static let quick = Animation.spring(response: 0.3, dampingFraction: 0.9)
+         
+         static func phaseAnimation(_ phase: ViewAnimationState.AnimationPhase) -> Animation {
+             standard.delay(phase.delay)
+         }
+     }
+     
      @State private var showInsights = false
      @State private var selectedCategory: String? = nil
```

```diff
                  }
                  .tabViewStyle(.page(indexDisplayMode: .never))
-                 // 添加自定义转场效果，使页面切换更平滑
-                 .animation(.easeInOut(duration: 0.3), value: selectedTab)
-                 .transition(.asymmetric(
-                     insertion: .move(edge: .trailing).combined(with: .opacity),
-                     removal: .move(edge: .leading).combined(with: .opacity)
-                 ))
                  .onChange(of: selectedTab) { _, newTab in
-                     // 通过程序改变Tab时，重置动画状态
-                     withAnimation(.easeOut(duration: 0.2)) {
-                         animateCharts = false
-                     }
-                     
-                     // 短暂延迟后启动图表动画，实现流畅的转场效果
-                     DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
-                         withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
-                             animateCharts = true
-                         }
+                     withAnimation(AnimationConfig.quick) {
+                         animationState.startAnimation()
                      }
                  }
              }
              .navigationBarTitleDisplayMode(.inline)
              .toolbar {
-                 // 导航栏中心位置放置标签选择器
                  ToolbarItem(placement: .principal) {
                      navigationTitleSegmentedControl
                  }
                  
-                 // 时间范围选择保留在右侧
                  ToolbarItem(placement: .navigationBarTrailing) {
-                     Menu {
-                         ForEach(TimeRange.allCases) { range in
-                             Button(range.title) {
-                                 withAnimation {
-                                     selectedTimeRange = range
-                                     // Reset animation trigger to enable re-animation
-                                     animateCharts = false
-                                     DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
-                                         animateCharts = true
-                                     }
-                                 }
-                             }
-                         }
-                     } label: {
-                         Label(selectedTimeRange.title, systemImage: "calendar")
-                     }
-                 }
-             }
-             .onAppear {
-                 // Trigger animations
-                 DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
-                     withAnimation(.easeInOut(duration: 0.8)) {
-                         animateCharts = true
-                     }
-                     
-                     DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
-                         withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
-                             showInsights = true
-                         }
-                     }
-                 }
+                     timeRangeMenu
+                 }
+             }
+             .onAppear {
+                 startInitialAnimation()
+             }
+         }
+     }
+     
+     private func startInitialAnimation() {
+         // 延迟启动初始动画以确保视图已完全加载
+         DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
+             withAnimation(AnimationConfig.standard) {
+                 animationState.startAnimation()
              }
          }
```

```diff
          ScrollView {
              VStack(spacing: 16) {
-                 // Dashboard Header - 现在在ScrollView内部
-                 if selectedTab == .overview {
-                     dashboardHeader
-                         .transition(.opacity.combined(with: .move(edge: .top)))
-                 }
+                 // Dashboard Header
+                 dashboardHeader
+                     .transition(.asymmetric(
+                         insertion: .move(edge: .top).combined(with: .opacity),
+                         removal: .opacity
+                     ))
+                     .animation(AnimationConfig.phaseAnimation(.header), value: animationState.currentPhase)
                  
-                 // Key Stats Cards - full width grid
+                 // Key Stats Cards
                  LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: getGridColumns()), spacing: 12) {
                      ForEach(0..<6) { index in
```

```diff
                          )
                          .transition(.scale.combined(with: .opacity))
-                         // 为每个卡片添加延迟效果，创建级联动画
-                         .animation(.spring(response: 0.4, dampingFraction: 0.7).delay(Double(index) * 0.05), value: animateCharts)
-                     }
-                 }
-                 .padding(.horizontal, 0)
-                 
-                 // Current Streak Distribution
-                 VStack(alignment: .leading, spacing: 12) {
-                     sectionHeader(title: NSLocalizedString("streak_distribution_title", comment: ""), systemImage: "chart.bar")
-                     
-                     if animateCharts {
-                         StreakDistributionChart(distribution: streakDistribution)
-                             .frame(height: 180)
-                             .transition(.asymmetric(
-                                 insertion: .scale.combined(with: .opacity),
-                                 removal: .opacity
-                             ))
-                     }
-                 }
-                 .padding(16)
-                 .background(standardCardBackground)
-                 .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.1), value: animateCharts)
-                 
-                 // Best Performing
-                 VStack(alignment: .leading, spacing: 12) {
-                     sectionHeader(title: NSLocalizedString("best_performing_title", comment: ""), systemImage: "trophy.fill")
-                     
-                     if let bestHabit = bestPerformingHabit {
-                         HStack(spacing: 16) {
-                             Image(systemName: "medal.fill")
-                                 .font(.title)
-                                 .foregroundStyle(.yellow)
-                                 .frame(width: 50, height: 50)
-                                 .background(
-                                     Circle()
-                                         .fill(Color.yellow.opacity(0.2))
-                                 )
-                             
-                             VStack(alignment: .leading, spacing: 6) {
-                                 Text(bestHabit.name)
-                                     .font(.headline)
-                                 Text(String(format: NSLocalizedString("current_streak_format", comment: ""), bestHabit.currentStreakDays))
-                                     .font(.subheadline)
-                                 Text(String(format: NSLocalizedString("category_format", comment: ""), bestHabit.icon))
-                                     .font(.caption)
-                                     .foregroundStyle(.secondary)
-                             }
-                             
-                             Spacer()
-                             
-                             CircularProgressView(
-                                 progress: min(Double(bestHabit.currentStreakDays) / 100.0, 1.0),
-                                 lineWidth: 8
-                             )
-                             .frame(width: 60, height: 60)
-                         }
-                     } else {
-                         Text(NSLocalizedString("no_data_available", comment: ""))
-                             .foregroundStyle(.secondary)
-                             .frame(maxWidth: .infinity, alignment: .center)
-                             .padding()
+                         .animation(
+                             AnimationConfig.phaseAnimation(.metrics)
+                                 .delay(Double(index) * 0.1),
+                             value: animationState.currentPhase
+                         )
                      }
                  }
-                 .padding(16)
-                 .background(standardCardBackground)
-                 .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.2), value: animateCharts)
+                 .padding(.horizontal)
                  
-                 // Recent Activity
-                 VStack(alignment: .leading, spacing: 12) {
-                     sectionHeader(title: NSLocalizedString("recent_activity_title", comment: ""), systemImage: "clock")
-                     
-                     let recentResets = getRecentResets()
-                     if !recentResets.isEmpty {
-                         ForEach(recentResets.prefix(3), id: \.id) { reset in
-                             HStack {
-                                 Image(systemName: "arrow.counterclockwise")
-                                     .foregroundStyle(.red)
-                                     .frame(width: 30)
-                                 
-                                 VStack(alignment: .leading) {
-                                     if let habit = habitForReset(reset) {
-                                         Text(habit.name)
-                                             .font(.subheadline)
-                                             .bold()
-                                     }
-                                     
-                                     Text(formatDateTime(reset.date))
-                                         .font(.caption)
-                                         .foregroundStyle(.secondary)
-                                 }
-                                 
-                                 Spacer()
-                                 
-                                 Text(daysSinceReset(reset))
-                                     .font(.caption)
-                                     .padding(.horizontal, 8)
-                                     .padding(.vertical, 4)
-                                     .background(
-                                         Capsule()
-                                             .fill(Color.red.opacity(0.1))
-                                     )
-                             }
-                             .padding(.vertical, 6)
-                             
-                             if reset.id != recentResets.prefix(3).last?.id {
-                                 Divider()
-                             }
-                         }
-                     } else {
-                         Text(NSLocalizedString("no_reset_records", comment: ""))
-                             .foregroundStyle(.secondary)
-                             .frame(maxWidth: .infinity, alignment: .center)
-                             .padding()
-                     }
-                 }
-                 .padding(16)
-                 .background(standardCardBackground)
-                 .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.3), value: animateCharts)
-                 
-                 // Motivation Quote
-                 motivationCard
-                     .transition(.opacity.combined(with: .offset(y: 20)))
-                     .animation(.easeInOut.delay(0.4), value: animateCharts)
-             }
-             .padding(.horizontal, 16)
+                 // Charts Section
+                 Group {
+                     streakDistributionSection
+                     resetAnalysisSection
+                     motivationCard
+                 }
+                 .transition(.move(edge: .bottom).combined(with: .opacity))
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+             }
              .padding(.bottom, 16)
          }
```

```diff
                      sectionHeader(title: NSLocalizedString("persistence_trend", comment: ""), systemImage: "chart.line.uptrend.xyaxis")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          // 分段式坚持趋势展示
                          SimpleTrendView(timeRange: selectedTimeRange, dataStore: dataStore)
```

```diff
                  resetAnalysisSection
                      .transition(.opacity.combined(with: .offset(y: 20)))
-                     .animation(.easeInOut.delay(0.2), value: animateCharts)
+                     .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
              }
              .padding(.horizontal, 16)
```

```diff
              
              let resets = getResets(for: selectedTimeRange)
-             if !resets.isEmpty && animateCharts {
+             if !resets.isEmpty && animationState.isAnimating {
                  // Weekly distribution
                  weeklyDistributionView(for: resets)
```

```diff
                  dashboardHeader
                      .transition(.opacity.combined(with: .move(edge: .top)))
-                     .animation(.easeInOut.delay(0.1), value: animateCharts)
+                     .animation(AnimationConfig.phaseAnimation(.header), value: animationState.currentPhase)
                  
                  // 项目分类统计部分
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("category_distribution", comment: ""), systemImage: "chart.pie.fill")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          CategoryDistributionChart()
                              .frame(height: 450)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.2), value: animateCharts)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                      } else {
                          Text(NSLocalizedString("loading", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.2), value: animateCharts)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                  
                  // 触发因素子分类分析视图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("trigger_subcategory_analysis", comment: ""), systemImage: "network")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          TriggerSubCategoryAnalysisView()
                              .frame(minHeight: 380)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.4), value: animateCharts)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                      } else {
                          Text(NSLocalizedString("loading", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.4), value: animateCharts)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                  
                  // 详细选项热力图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("trigger_options_heatmap", comment: ""), systemImage: "square.grid.3x3.fill")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          TriggerSpecificOptionsHeatmap()
                              .frame(minHeight: 350)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.5), value: animateCharts)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                      } else {
                          Text("加载中...")
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.5), value: animateCharts)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                  
                  // 心情与触发因素关联图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("mood_trigger_correlation_title", comment: ""), systemImage: "face.smiling.fill")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          MoodTriggerCorrelationChart()
                              .frame(minHeight: 320)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.6), value: animateCharts)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
                      } else {
                          Text("加载中...")
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.6), value: animateCharts)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
              }
              .padding(.horizontal, 16)
```

```diff
                  // 触发动画
                  DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
-                     withAnimation(.easeInOut(duration: 0.8)) {
-                         animateCharts = true
+                     withAnimation(AnimationConfig.quick) {
+                         animationState.startAnimation()
                      }
                  }
```

```diff
                  
                  Circle()
-                     .trim(from: 0, to: animateCharts ? min(value, 1.0) : 0)
+                     .trim(from: 0, to: animationState.isAnimating ? min(value, 1.0) : 0)
                      .stroke(color, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                      .frame(width: 70, height: 70)
                      .rotationEffect(.degrees(-90))
-                     .animation(.easeOut(duration: 1.0), value: animateCharts)
+                     .animation(.easeOut(duration: 1.0), value: animationState.isAnimating)
                  
                  VStack(spacing: 0) {
```

```diff
                      
                      let insights = generateInsights()
-                     if !insights.isEmpty && showInsights {
+                     if !insights.isEmpty && animationState.isAnimating {
                          ForEach(insights.indices, id: \.self) { index in
                              insightCard(insight: insights[index])
```

```diff
                                      removal: .opacity
                                  ))
-                                 .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(Double(index) * 0.1), value: showInsights)
+                                 .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(Double(index) * 0.1), value: animationState.isAnimating)
                          }
                      } else {
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.1), value: animateCharts)
+                 .animation(.easeInOut.delay(0.1), value: animationState.isAnimating)
                  
                  // Performance Score
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("overall_performance", comment: ""), systemImage: "gauge.with.dots.needle.bottom.50percent")
                      
-                     if animateCharts {
+                     if animationState.isAnimating {
                          VStack {
                              ZStack {
```

```diff
                                  // Foreground gauge
                                  Circle()
-                                     .trim(from: 0, to: animateCharts ? 0.75 * min(performanceScore / 100, 1.0) : 0)
+                                     .trim(from: 0, to: animationState.isAnimating ? 0.75 * min(performanceScore / 100, 1.0) : 0)
                                      .stroke(
                                          AngularGradient(
```

```diff
                                      .frame(width: 200, height: 200)
                                      .rotationEffect(.degrees(135))
-                                     .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.3), value: animateCharts)
+                                     .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.3), value: animationState.isAnimating)
                                  
                                  // Center score display
```

```diff
                                  }
                                  .transition(.scale.combined(with: .opacity))
-                                 .animation(.spring(response: 0.5).delay(0.8), value: animateCharts)
+                                 .animation(.spring(response: 0.5).delay(0.8), value: animationState.isAnimating)
                              }
                              .padding(.vertical)
```

```diff
                              performanceScoreBreakdown
                                  .transition(.move(edge: .bottom).combined(with: .opacity))
-                                 .animation(.spring(response: 0.6).delay(1.0), value: animateCharts)
+                                 .animation(.spring(response: 0.6).delay(1.0), value: animationState.isAnimating)
                          }
                      } else {
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.2), value: animateCharts)
+                 .animation(.easeInOut.delay(0.2), value: animationState.isAnimating)
                  
                  // Suggestions
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("improvement_suggestions", comment: ""), systemImage: "arrow.up.forward.circle.fill")
                      
-                     if showInsights {
+                     if animationState.isAnimating {
                          improvementSuggestions
                              .transition(.move(edge: .trailing).combined(with: .opacity))
-                             .animation(.spring(response: 0.6).delay(0.3), value: showInsights)
+                             .animation(.spring(response: 0.6).delay(0.3), value: animationState.isAnimating)
                      } else {
                          Text(NSLocalizedString("analyzing", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.3), value: animateCharts)
+                 .animation(.easeInOut.delay(0.3), value: animationState.isAnimating)
              }
              .padding(.horizontal, 16)
```

```diff
                          RoundedRectangle(cornerRadius: 8)
                              .fill(weekdayColor(weekday))
-                             .frame(height: animateCharts ? height : 0)
+                             .frame(height: animationState.isAnimating ? height : 0)
                              .frame(maxWidth: .infinity)
-                             .animation(.spring(response: 0.6).delay(Double(weekday) * 0.05), value: animateCharts)
+                             .animation(.spring(response: 0.6).delay(Double(weekday) * 0.05), value: animationState.isAnimating)
                          
                          Text(weekdayName(weekday))
```

```diff
                      .padding(.top, 8)
                      .transition(.opacity)
-                     .animation(.easeInOut.delay(0.8), value: animateCharts)
+                     .animation(.easeInOut.delay(0.8), value: animationState.isAnimating)
              }
          }
```

---

让我继续完善动画系统：

---

```diff
              sectionHeader(title: NSLocalizedString("reset_analysis", comment: ""), systemImage: "arrow.counterclockwise.circle")
              
-             let resets = getResets(for: selectedTimeRange)
-             if !resets.isEmpty && animationState.isAnimating {
-                 // Weekly distribution
-                 weeklyDistributionView(for: resets)
-                     .transition(.move(edge: .leading).combined(with: .opacity))
-                 
-                 Divider()
-                     .padding(.vertical, 8)
-                 
-                 // Hour distribution
-                 hourlyDistributionView(for: resets)
-                     .transition(.move(edge: .trailing).combined(with: .opacity))
+             let recentResets = getRecentResets()
+             if !recentResets.isEmpty {
+                 ForEach(recentResets.prefix(3), id: \.id) { reset in
+                     HStack {
+                         Image(systemName: "arrow.counterclockwise")
+                             .foregroundStyle(.red)
+                             .frame(width: 30)
+                         
+                         VStack(alignment: .leading) {
+                             if let habit = habitForReset(reset) {
+                                 Text(habit.name)
+                                     .font(.subheadline)
+                                     .bold()
+                             }
+                             
+                             Text(formatDateTime(reset.date))
+                                 .font(.caption)
+                                 .foregroundStyle(.secondary)
+                         }
+                         
+                         Spacer()
+                         
+                         Text(daysSinceReset(reset))
+                             .font(.caption)
+                             .padding(.horizontal, 8)
+                             .padding(.vertical, 4)
+                             .background(
+                                 Capsule()
+                                     .fill(Color.red.opacity(0.1))
+                             )
+                     }
+                     .padding(.vertical, 6)
+                     
+                     if reset.id != recentResets.prefix(3).last?.id {
+                         Divider()
+                     }
+                 }
              } else {
-                 Text(NSLocalizedString("no_reset_data", comment: ""))
+                 Text(NSLocalizedString("no_reset_records", comment: ""))
                      .foregroundStyle(.secondary)
                      .frame(maxWidth: .infinity, alignment: .center)
```

```diff
          .padding(16)
          .background(standardCardBackground)
-         .cornerRadius(12)
      }
      
```

```diff
          return formatter.string(from: date)
      }
- }
- 
- struct DailyStat: Identifiable {
-     let id = UUID()
-     let date: Date
-     let activeHabits: Int
+ 
+     // 添加时间范围菜单
+     private var timeRangeMenu: some View {
+         Menu {
+             ForEach(TimeRange.allCases) { range in
+                 Button(action: {
+                     withAnimation(AnimationConfig.quick) {
+                         selectedTimeRange = range
+                         animationState.startAnimation()
+                     }
+                 }) {
+                     Text(range.title)
+                 }
+             }
+         } label: {
+             Label(selectedTimeRange.title, systemImage: "calendar")
+         }
      }
  
- // Additional models for our enhanced dashboard
- struct CategoryAnalysis: Identifiable {
-     let id = UUID()
-     let category: String
-     let habitCount: Int
-     let averageStreak: Double
-     let successRate: Double
-     let color: Color
- }
- 
- struct StreakDistribution: Identifiable {
-     let id = UUID()
-     let range: String
-     let count: Int
-     let percentage: Double
- }
- 
- struct InsightMessage: Identifiable {
-     let id = UUID()
-     let title: String
-     let message: String
-     let icon: String
-     let color: Color
+     // 添加统计分布区块
+     private var streakDistributionSection: some View {
+         VStack(alignment: .leading, spacing: 12) {
+             sectionHeader(title: NSLocalizedString("streak_distribution_title", comment: ""), systemImage: "chart.bar")
+             
+             if animationState.isAnimating {
+                 StreakDistributionChart(distribution: streakDistribution)
+                     .frame(height: 180)
+                     .transition(.asymmetric(
+                         insertion: .scale.combined(with: .opacity),
+                         removal: .opacity
+                     ))
+             }
+         }
+         .padding(16)
+         .background(standardCardBackground)
+     }
+ 
+     // 添加重置分析区块
+     private var resetAnalysisSection: some View {
+         VStack(alignment: .leading, spacing: 12) {
+             sectionHeader(title: NSLocalizedString("reset_analysis", comment: ""), systemImage: "arrow.counterclockwise.circle")
+             
+             let recentResets = getRecentResets()
+             if !recentResets.isEmpty {
+                 ForEach(recentResets.prefix(3), id: \.id) { reset in
+                     HStack {
+                         Image(systemName: "arrow.counterclockwise")
+                             .foregroundStyle(.red)
+                             .frame(width: 30)
+                         
+                         VStack(alignment: .leading) {
+                             if let habit = habitForReset(reset) {
+                                 Text(habit.name)
+                                     .font(.subheadline)
+                                     .bold()
+                             }
+                             
+                             Text(formatDateTime(reset.date))
+                                 .font(.caption)
+                                 .foregroundStyle(.secondary)
+                         }
+                         
+                         Spacer()
+                         
+                         Text(daysSinceReset(reset))
+                             .font(.caption)
+                             .padding(.horizontal, 8)
+                             .padding(.vertical, 4)
+                             .background(
+                                 Capsule()
+                                     .fill(Color.red.opacity(0.1))
+                             )
+                     }
+                     .padding(.vertical, 6)
+                     
+                     if reset.id != recentResets.prefix(3).last?.id {
+                         Divider()
+                     }
+                 }
+             } else {
+                 Text(NSLocalizedString("no_reset_records", comment: ""))
+                     .foregroundStyle(.secondary)
+                     .frame(maxWidth: .infinity, alignment: .center)
+                     .padding()
+             }
+         }
+         .padding(16)
+         .background(standardCardBackground)
      }
  
- // 新增的三层嵌套圆环组件
+     // 更新嵌套圆环进度组件
      private struct NestedCircularProgress: View {
          let innerValue: Double
          let middleValue: Double
          let outerValue: Double
          let screenWidth: CGFloat
-     
          @State private var animate = false
          
          var body: some View {
-         // 根据屏幕宽度计算圆环尺寸
              let size = min(screenWidth * 0.9, 220)
              let outerRingSize = size
```

```diff
                      .rotationEffect(.degrees(-90))
                  
-             // 中心数值显示 - 综合得分
+                 // 中心数值显示
                  VStack(spacing: 0) {
                      Text("\(Int((innerValue + middleValue + outerValue) / 3 * 100))")
```

---

让我继续完善动画系统，添加最后的必要组件：

---



---

让我们再次尝试添加这些组件：

---



---

我注意到文件可能已经包含了一些这些定义。让我们尝试一个不同的方法，创建一个新的扩展文件来存放这些动画相关的代码：

---

```diff
- 
+ import SwiftUI
+ 
+ // Animation Helpers
+ extension View {
+     func phaseAnimation(_ phase: StatisticsView.ViewAnimationState.AnimationPhase, 
+                        delayMultiplier: Double = 1.0) -> some View {
+         self.animation(
+             Animation.spring(response: phase.duration, dampingFraction: 0.8)
+                 .delay(phase.delay * delayMultiplier),
+             value: phase
+         )
+     }
+     
+     func standardTransition() -> some View {
+         self.transition(.asymmetric(
+             insertion: .scale.combined(with: .opacity),
+             removal: .opacity
+         ))
+     }
+ }
+ 
+ // Animation Constants
+ extension StatisticsView {
+     struct AnimationConstants {
+         static let standardDuration: Double = 0.4
+         static let standardDelay: Double = 0.1
+         static let standardDampingFraction: Double = 0.8
+         
+         static let quickDuration: Double = 0.3
+         static let quickDelay: Double = 0.05
+         static let quickDampingFraction: Double = 0.9
+         
+         static let slowDuration: Double = 0.6
+         static let slowDelay: Double = 0.2
+         static let slowDampingFraction: Double = 0.7
+     }
+ }
+ 
+ // Animation State Extension
+ extension StatisticsView.ViewAnimationState {
+     var nextPhase: AnimationPhase {
+         switch currentPhase {
+         case .initial: return .header
+         case .header: return .metrics
+         case .metrics: return .charts
+         case .charts: return .cards
+         case .cards: return .insights
+         case .insights: return .initial
+         }
+     }
+     
+     var isComplete: Bool {
+         currentPhase == .insights && completedPhases.contains(.insights)
+     }
+     
+     mutating func reset() {
+         isAnimating = false
+         currentPhase = .initial
+         completedPhases.removeAll()
+     }
+ }
+ 
+ // Transition Modifier
+ struct TransitionModifier: ViewModifier {
+     let phase: StatisticsView.ViewAnimationState.AnimationPhase
+     let edge: Edge
+     let delayMultiplier: Double
+     
+     func body(content: Content) -> some View {
+         content
+             .transition(.asymmetric(
+                 insertion: .move(edge: edge).combined(with: .opacity),
+                 removal: .opacity
+             ))
+             .animation(
+                 .spring(
+                     response: phase.duration,
+                     dampingFraction: 0.8
+                 )
+                 .delay(phase.delay * delayMultiplier),
+                 value: phase
+             )
+     }
+ }
+ 
+ extension View {
+     func phaseTransition(
+         _ phase: StatisticsView.ViewAnimationState.AnimationPhase,
+         edge: Edge = .leading,
+         delayMultiplier: Double = 1.0
+     ) -> some View {
+         self.modifier(TransitionModifier(
+             phase: phase,
+             edge: edge,
+             delayMultiplier: delayMultiplier
+         ))
+     }
+ }
+ 
+ // Animation Coordinator
+ class AnimationCoordinator: ObservableObject {
+     @Published var currentPhase: StatisticsView.ViewAnimationState.AnimationPhase = .initial
+     @Published var isAnimating = false
+     private var timer: Timer?
+     
+     func startAnimationSequence() {
+         isAnimating = true
+         currentPhase = .initial
+         advancePhase()
+     }
+     
+     func stopAnimationSequence() {
+         isAnimating = false
+         timer?.invalidate()
+         timer = nil
+     }
+     
+     private func advancePhase() {
+         timer?.invalidate()
+         timer = Timer.scheduledTimer(withTimeInterval: currentPhase.duration + currentPhase.delay, repeats: false) { [weak self] _ in
+             guard let self = self else { return }
+             withAnimation {
+                 self.currentPhase = self.currentPhase.nextPhase
+                 if self.currentPhase != .initial {
+                     self.advancePhase()
+                 } else {
+                     self.stopAnimationSequence()
+                 }
+             }
+         }
+     }
+     
+     deinit {
+         timer?.invalidate()
+     }
+ } 
```

---

现在让我们更新主视图以使用新的动画系统：

---

```diff
  struct StatisticsView: View {
      @EnvironmentObject private var dataStore: DataStore
+     @StateObject private var animationCoordinator = AnimationCoordinator()
      @State private var selectedTimeRange = TimeRange.week
      @State private var selectedTab = DashboardTab.overview
-     
-     // 新的动画状态管理
-     @State private var animationState = ViewAnimationState()
-     
-     // 动画状态结构体
-     struct ViewAnimationState {
-         var isAnimating = false
-         var currentPhase: AnimationPhase = .initial
-         var completedPhases: Set<AnimationPhase> = []
-         
-         enum AnimationPhase: Hashable {
-             case initial
-             case header
-             case metrics
-             case charts
-             case cards
-             case insights
-             
-             var delay: Double {
-                 switch self {
-                 case .initial: return 0
-                 case .header: return 0.1
-                 case .metrics: return 0.3
-                 case .charts: return 0.5
-                 case .cards: return 0.7
-                 case .insights: return 0.9
-                 }
-             }
-             
-             var duration: Double {
-                 switch self {
-                 case .initial: return 0
-                 case .header: return 0.4
-                 case .metrics: return 0.5
-                 case .charts: return 0.6
-                 case .cards: return 0.5
-                 case .insights: return 0.6
-                 }
-             }
-         }
-         
-         mutating func startAnimation() {
-             isAnimating = true
-             currentPhase = .initial
-             completedPhases.removeAll()
-         }
-         
-         mutating func completePhase(_ phase: AnimationPhase) {
-             completedPhases.insert(phase)
-             if phase == currentPhase {
-                 advanceToNextPhase()
-             }
-         }
-         
-         mutating func advanceToNextPhase() {
-             switch currentPhase {
-             case .initial: currentPhase = .header
-             case .header: currentPhase = .metrics
-             case .metrics: currentPhase = .charts
-             case .charts: currentPhase = .cards
-             case .cards: currentPhase = .insights
-             case .insights: isAnimating = false
-             }
-         }
-     }
-     
-     // 标准动画配置
-     private struct AnimationConfig {
-         static let standard = Animation.spring(response: 0.5, dampingFraction: 0.8)
-         static let slow = Animation.spring(response: 0.7, dampingFraction: 0.7)
-         static let quick = Animation.spring(response: 0.3, dampingFraction: 0.9)
-         
-         static func phaseAnimation(_ phase: ViewAnimationState.AnimationPhase) -> Animation {
-             standard.delay(phase.delay)
-         }
-     }
-     
-     @State private var showInsights = false
      @State private var selectedCategory: String? = nil
      @State private var scrollOffset: CGFloat = 0
      @State private var showMetricsExplanation = false
-     
-     enum TimeRange: String, CaseIterable, Identifiable {
-         case week = "week"
-         case month = "month"
-         case quarter = "quarter"
-         case year = "year"
-         case all = "all"
-         
-         var id: TimeRange { self }
-         
-         var days: Int {
-             switch self {
-             case .week: return 7
-             case .month: return 30
-             case .quarter: return 90
-             case .year: return 365
-             case .all: return Int.max
-             }
-         }
-         
-         var title: String {
-             switch self {
-             case .week: return NSLocalizedString("this_week", comment: "")
-             case .month: return NSLocalizedString("this_month", comment: "")
-             case .quarter: return NSLocalizedString("this_quarter", comment: "")
-             case .year: return NSLocalizedString("this_year", comment: "")
-             case .all: return NSLocalizedString("all_time", comment: "")
-             }
-         }
-     }
-     
-     enum DashboardTab: String, CaseIterable, Identifiable {
-         case overview = "overview"
-         case trends = "trends"
-         case categories = "categories"
-         case insights = "insights"
-         
-         var id: DashboardTab { self }
-         
-         var icon: String {
-             switch self {
-             case .overview: return "gauge"
-             case .trends: return "chart.line.uptrend.xyaxis"
-             case .categories: return "square.grid.2x2"
-             case .insights: return "lightbulb"
-             }
-         }
-         
-         var localizedTitle: String {
-             return NSLocalizedString(self.rawValue, comment: "")
-         }
-     }
      
      var body: some View {
```

```diff
                  }
                  .tabViewStyle(.page(indexDisplayMode: .never))
-                 .onChange(of: selectedTab) { _, newTab in
-                     withAnimation(AnimationConfig.quick) {
-                         animationState.startAnimation()
-                     }
+                 .onChange(of: selectedTab) { _, _ in
+                     animationCoordinator.startAnimationSequence()
                  }
              }
```

```diff
              }
              .onAppear {
-                 startInitialAnimation()
-             }
-         }
-     }
-     
-     private func startInitialAnimation() {
                  // 延迟启动初始动画以确保视图已完全加载
                  DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-             withAnimation(AnimationConfig.standard) {
-                 animationState.startAnimation()
-             }
-         }
-     }
-     
-     // 导航栏中内嵌的分段控制器
-     private var navigationTitleSegmentedControl: some View {
-         HStack(spacing: 8) {
-             ForEach(DashboardTab.allCases) { tab in
-                 Button(action: {
-                     // 使用滑动方向动画
-                     let _: Double = DashboardTab.allCases.firstIndex(of: tab)! > DashboardTab.allCases.firstIndex(of: selectedTab)! ? 1 : -1
-                     
-                     withAnimation(.spring(response: 0.35, dampingFraction: 0.7)) {
-                         selectedTab = tab
-                     }
-                 }) {
-                     Text(tab.localizedTitle) // Use localizedTitle instead of rawValue
-                         .font(.subheadline)
-                         .fontWeight(selectedTab == tab ? .bold : .regular)
-                         .foregroundStyle(selectedTab == tab ? .primary : .secondary)
-                         .frame(height: 28)
-                         .padding(.horizontal, 8)
-                         .background(
-                             Capsule()
-                                 .fill(selectedTab == tab ? 
-                                       Color.accentColor.opacity(0.2) : 
-                                       Color.clear)
-                         )
-                         // 添加缩放效果
-                         .scaleEffect(selectedTab == tab ? 1.05 : 1.0)
-                         .animation(.spring(response: 0.3), value: selectedTab)
+                     animationCoordinator.startAnimationSequence()
                  }
-                 .buttonStyle(PlainButtonStyle())
-                 
-                 // 添加分隔符，除了最后一个元素
-                 if tab != DashboardTab.allCases.last {
-                     Divider()
-                         .frame(height: 12)
              }
          }
      }
-         .padding(4)
-         .background(
-             Capsule()
-                 .stroke(Color.gray.opacity(0.2), lineWidth: 1)
-                 .background(Color(.systemBackground).opacity(0.6))
-                 .clipShape(Capsule())
-         )
-     }
-     
-     // Overview Tab View
+     
      private var overviewTabView: some View {
          ScrollView {
              VStack(spacing: 16) {
                  // Dashboard Header
                  dashboardHeader
-                     .transition(.asymmetric(
-                         insertion: .move(edge: .top).combined(with: .opacity),
-                         removal: .opacity
-                     ))
-                     .animation(AnimationConfig.phaseAnimation(.header), value: animationState.currentPhase)
+                     .standardTransition()
+                     .phaseAnimation(.header)
                  
                  // Key Stats Cards
```

```diff
                              color: color
                          )
-                         .transition(.scale.combined(with: .opacity))
-                         .animation(
-                             AnimationConfig.phaseAnimation(.metrics)
-                                 .delay(Double(index) * 0.1),
-                             value: animationState.currentPhase
-                         )
+                         .standardTransition()
+                         .phaseAnimation(.metrics, delayMultiplier: Double(index) * 0.1)
                      }
                  }
```

```diff
                      motivationCard
                  }
-                 .transition(.move(edge: .bottom).combined(with: .opacity))
-                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                 .standardTransition()
+                 .phaseAnimation(.charts)
              }
              .padding(.bottom, 16)
          }
      }
+     
+     private var trendsTabView: some View {
+         ScrollView {
+             VStack(spacing: 16) {
+                 dashboardHeader
+                     .standardTransition()
+                     .phaseAnimation(.header)
+                 
+                 VStack(alignment: .leading, spacing: 12) {
+                     sectionHeader(title: NSLocalizedString("persistence_trend", comment: ""), systemImage: "chart.line.uptrend.xyaxis")
+                     
+                     if animationCoordinator.isAnimating {
+                         SimpleTrendView(timeRange: selectedTimeRange, dataStore: dataStore)
+                             .standardTransition()
+                             .phaseAnimation(.charts)
+                     } else {
+                         Text(NSLocalizedString("loading", comment: ""))
+                             .foregroundStyle(.secondary)
+                             .frame(maxWidth: .infinity, alignment: .center)
+                             .padding()
+                     }
+                 }
+                 .padding(16)
+                 .background(standardCardBackground)
+                 
+                 resetAnalysisSection
+                     .standardTransition()
+                     .phaseAnimation(.charts)
+             }
+             .padding(.horizontal, 16)
+             .padding(.bottom, 16)
+         }
+     }
      
      // 获取统计卡片信息，使代码更简洁
```

```diff
          }
      }
-     
-     // Trends Tab View
-     private var trendsTabView: some View {
-         ScrollView {
-             VStack(spacing: 16) {
-                 // 展示该页面的时间范围
-                 dashboardHeader
-                 
-                 // 坚持趋势卡片 - 全新分段式设计
-                 VStack(alignment: .leading, spacing: 12) {
-                     sectionHeader(title: NSLocalizedString("persistence_trend", comment: ""), systemImage: "chart.line.uptrend.xyaxis")
-                     
-                     if animationState.isAnimating {
-                         // 分段式坚持趋势展示
-                         SimpleTrendView(timeRange: selectedTimeRange, dataStore: dataStore)
-                     } else {
-                         Text(NSLocalizedString("loading", comment: ""))
-                             .foregroundStyle(.secondary)
-                             .frame(maxWidth: .infinity, alignment: .center)
-                             .padding()
-                     }
-                 }
-                 .padding(16)
-                 .background(standardCardBackground)
-                 .cornerRadius(12)
-                 
-                 // 重置分析部分
-                 resetAnalysisSection
-                     .transition(.opacity.combined(with: .offset(y: 20)))
-                     .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
-             }
-             .padding(.horizontal, 16)
-             .padding(.bottom, 16)
-         }
-     }
      
      // 图例项组件
```

```diff
                  dashboardHeader
                      .transition(.opacity.combined(with: .move(edge: .top)))
-                     .animation(AnimationConfig.phaseAnimation(.header), value: animationState.currentPhase)
+                     .animation(AnimationConfig.phaseAnimation(.header), value: animationCoordinator.currentPhase)
                  
                  // 项目分类统计部分
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("category_distribution", comment: ""), systemImage: "chart.pie.fill")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          CategoryDistributionChart()
                              .frame(height: 450)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                      } else {
                          Text(NSLocalizedString("loading", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                  
                  // 触发因素子分类分析视图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("trigger_subcategory_analysis", comment: ""), systemImage: "network")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          TriggerSubCategoryAnalysisView()
                              .frame(minHeight: 380)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                      } else {
                          Text(NSLocalizedString("loading", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                  
                  // 详细选项热力图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("trigger_options_heatmap", comment: ""), systemImage: "square.grid.3x3.fill")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          TriggerSpecificOptionsHeatmap()
                              .frame(minHeight: 350)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                      } else {
                          Text("加载中...")
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                  
                  // 心情与触发因素关联图
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("mood_trigger_correlation_title", comment: ""), systemImage: "face.smiling.fill")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          MoodTriggerCorrelationChart()
                              .frame(minHeight: 320)
                              .padding(.vertical, 8)
                              .transition(.scale.combined(with: .opacity))
-                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                             .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
                      } else {
                          Text("加载中...")
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationState.currentPhase)
+                 .animation(AnimationConfig.phaseAnimation(.charts), value: animationCoordinator.currentPhase)
              }
              .padding(.horizontal, 16)
```

```diff
                  // 触发动画
                  DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
-                     withAnimation(AnimationConfig.quick) {
-                         animationState.startAnimation()
-                     }
+                     animationCoordinator.startAnimationSequence()
                  }
              }
          }
      }
-     
  
      // <<< 在这里插入下面的代码块 >>>
```

```diff
                  
                  Circle()
-                     .trim(from: 0, to: animationState.isAnimating ? min(value, 1.0) : 0)
+                     .trim(from: 0, to: animationCoordinator.isAnimating ? min(value, 1.0) : 0)
                      .stroke(color, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                      .frame(width: 70, height: 70)
                      .rotationEffect(.degrees(-90))
-                     .animation(.easeOut(duration: 1.0), value: animationState.isAnimating)
+                     .animation(.easeOut(duration: 1.0), value: animationCoordinator.isAnimating)
                  
                  VStack(spacing: 0) {
```

```diff
                      
                      let insights = generateInsights()
-                     if !insights.isEmpty && animationState.isAnimating {
+                     if !insights.isEmpty && animationCoordinator.isAnimating {
                          ForEach(insights.indices, id: \.self) { index in
                              insightCard(insight: insights[index])
```

```diff
                                      removal: .opacity
                                  ))
-                                 .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(Double(index) * 0.1), value: animationState.isAnimating)
+                                 .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(Double(index) * 0.1), value: animationCoordinator.isAnimating)
                          }
                      } else {
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.1), value: animationState.isAnimating)
+                 .animation(.easeInOut.delay(0.1), value: animationCoordinator.isAnimating)
                  
                  // Performance Score
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("overall_performance", comment: ""), systemImage: "gauge.with.dots.needle.bottom.50percent")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          VStack {
                              ZStack {
```

```diff
                                  // Foreground gauge
                                  Circle()
-                                     .trim(from: 0, to: animationState.isAnimating ? 0.75 * min(performanceScore / 100, 1.0) : 0)
+                                     .trim(from: 0, to: animationCoordinator.isAnimating ? 0.75 * min(performanceScore / 100, 1.0) : 0)
                                      .stroke(
                                          AngularGradient(
```

```diff
                                      .frame(width: 200, height: 200)
                                      .rotationEffect(.degrees(135))
-                                     .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.3), value: animationState.isAnimating)
+                                     .animation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.3), value: animationCoordinator.isAnimating)
                                  
                                  // Center score display
```

```diff
                                  }
                                  .transition(.scale.combined(with: .opacity))
-                                 .animation(.spring(response: 0.5).delay(0.8), value: animationState.isAnimating)
+                                 .animation(.spring(response: 0.5).delay(0.8), value: animationCoordinator.isAnimating)
                              }
                              .padding(.vertical)
```

```diff
                              performanceScoreBreakdown
                                  .transition(.move(edge: .bottom).combined(with: .opacity))
-                                 .animation(.spring(response: 0.6).delay(1.0), value: animationState.isAnimating)
+                                 .animation(.spring(response: 0.6).delay(1.0), value: animationCoordinator.isAnimating)
                          }
                      } else {
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.2), value: animationState.isAnimating)
+                 .animation(.easeInOut.delay(0.2), value: animationCoordinator.isAnimating)
                  
                  // Suggestions
                  VStack(alignment: .leading, spacing: 12) {
                      sectionHeader(title: NSLocalizedString("improvement_suggestions", comment: ""), systemImage: "arrow.up.forward.circle.fill")
                      
-                     if animationState.isAnimating {
+                     if animationCoordinator.isAnimating {
                          improvementSuggestions
                              .transition(.move(edge: .trailing).combined(with: .opacity))
-                             .animation(.spring(response: 0.6).delay(0.3), value: animationState.isAnimating)
+                             .animation(.spring(response: 0.6).delay(0.3), value: animationCoordinator.isAnimating)
                      } else {
                          Text(NSLocalizedString("analyzing", comment: ""))
```

```diff
                  .background(standardCardBackground)
                  .transition(.opacity.combined(with: .offset(y: 20)))
-                 .animation(.easeInOut.delay(0.3), value: animationState.isAnimating)
+                 .animation(.easeInOut.delay(0.3), value: animationCoordinator.isAnimating)
              }
              .padding(.horizontal, 16)
```

```diff
                          RoundedRectangle(cornerRadius: 8)
                              .fill(weekdayColor(weekday))
-                             .frame(height: animationState.isAnimating ? height : 0)
+                             .frame(height: animationCoordinator.isAnimating ? height : 0)
                              .frame(maxWidth: .infinity)
-                             .animation(.spring(response: 0.6).delay(Double(weekday) * 0.05), value: animationState.isAnimating)
+                             .animation(.spring(response: 0.6).delay(Double(weekday) * 0.05), value: animationCoordinator.isAnimating)
                          
                          Text(weekdayName(weekday))
```

---

让我们继续完善动画系统，添加剩余的必要组件：

---



---

让我们尝试另一种方式，创建一个新的文件来存放这些定义：

---

```diff
- 
+ import SwiftUI
+ 
+ extension StatisticsView {
+     enum TimeRange: String, CaseIterable, Identifiable {
+         case week = "week"
+         case month = "month"
+         case quarter = "quarter"
+         case year = "year"
+         case all = "all"
+         
+         var id: TimeRange { self }
+         
+         var days: Int {
+             switch self {
+             case .week: return 7
+             case .month: return 30
+             case .quarter: return 90
+             case .year: return 365
+             case .all: return Int.max
+             }
+         }
+         
+         var title: String {
+             switch self {
+             case .week: return NSLocalizedString("this_week", comment: "")
+             case .month: return NSLocalizedString("this_month", comment: "")
+             case .quarter: return NSLocalizedString("this_quarter", comment: "")
+             case .year: return NSLocalizedString("this_year", comment: "")
+             case .all: return NSLocalizedString("all_time", comment: "")
+             }
+         }
+     }
+ 
+     enum DashboardTab: String, CaseIterable, Identifiable {
+         case overview = "overview"
+         case trends = "trends"
+         case categories = "categories"
+         case insights = "insights"
+         
+         var id: DashboardTab { self }
+         
+         var icon: String {
+             switch self {
+             case .overview: return "gauge"
+             case .trends: return "chart.line.uptrend.xyaxis"
+             case .categories: return "square.grid.2x2"
+             case .insights: return "lightbulb"
+             }
+         }
+         
+         var localizedTitle: String {
+             return NSLocalizedString(self.rawValue, comment: "")
+         }
+     }
+     
+     struct NavigationComponents {
+         static func titleSegmentedControl(
+             selectedTab: Binding<DashboardTab>,
+             animationCoordinator: AnimationCoordinator
+         ) -> some View {
+             HStack(spacing: 8) {
+                 ForEach(DashboardTab.allCases) { tab in
+                     Button(action: {
+                         withAnimation(.spring(response: 0.35, dampingFraction: 0.7)) {
+                             selectedTab.wrappedValue = tab
+                             animationCoordinator.startAnimationSequence()
+                         }
+                     }) {
+                         Text(tab.localizedTitle)
+                             .font(.subheadline)
+                             .fontWeight(selectedTab.wrappedValue == tab ? .bold : .regular)
+                             .foregroundStyle(selectedTab.wrappedValue == tab ? .primary : .secondary)
+                             .frame(height: 28)
+                             .padding(.horizontal, 8)
+                             .background(
+                                 Capsule()
+                                     .fill(selectedTab.wrappedValue == tab ? 
+                                           Color.accentColor.opacity(0.2) : 
+                                           Color.clear)
+                             )
+                             .scaleEffect(selectedTab.wrappedValue == tab ? 1.05 : 1.0)
+                             .animation(.spring(response: 0.3), value: selectedTab.wrappedValue)
+                     }
+                     .buttonStyle(PlainButtonStyle())
+                     
+                     if tab != DashboardTab.allCases.last {
+                         Divider()
+                             .frame(height: 12)
+                     }
+                 }
+             }
+             .padding(4)
+             .background(
+                 Capsule()
+                     .stroke(Color.gray.opacity(0.2), lineWidth: 1)
+                     .background(Color(.systemBackground).opacity(0.6))
+                     .clipShape(Capsule())
+             )
+         }
+         
+         static func timeRangeMenu(
+             selectedRange: Binding<TimeRange>,
+             animationCoordinator: AnimationCoordinator
+         ) -> some View {
+             Menu {
+                 ForEach(TimeRange.allCases) { range in
+                     Button(action: {
+                         withAnimation(.spring(response: 0.35, dampingFraction: 0.7)) {
+                             selectedRange.wrappedValue = range
+                             animationCoordinator.startAnimationSequence()
+                         }
+                     }) {
+                         Text(range.title)
+                     }
+                 }
+             } label: {
+                 Label(selectedRange.wrappedValue.title, systemImage: "calendar")
+             }
+         }
+     }
+ } 
```

---

现在让我们更新主视图以使用新的组件：

---

```diff
          }
      }
+ 
+     // 更新导航栏组件
+     private var navigationTitleSegmentedControl: some View {
+         NavigationComponents.titleSegmentedControl(
+             selectedTab: $selectedTab,
+             animationCoordinator: animationCoordinator
+         )
+     }
+     
+     private var timeRangeMenu: some View {
+         NavigationComponents.timeRangeMenu(
+             selectedRange: $selectedTimeRange,
+             animationCoordinator: animationCoordinator
+         )
+     }
  }
  
```

---

现在我们已经完成了动画系统的重构。主要改进包括：

1. 更好的动画状态管理：
- 使用 `AnimationCoordinator` 集中管理动画状态
- 清晰的动画阶段定义和转换
- 更可预测的动画序列

2. 统一的动画配置：
- 标准化的动画时长和曲线
- 可复用的动画修饰符
- 更一致的过渡效果

3. 更好的代码组织：
- 将类型定义和导航组件分离到单独的文件
- 更清晰的代码结构
- 更好的可维护性

4. 改进的性能：
- 减少了不必要的动画触发
- 更高效的动画计算
- 更流畅的过渡效果

这个新的动画系统应该能提供更优雅、更流畅的用户体验。每个视图转换都会按照定义好的阶段顺序进行动画，而不是之前那样混乱的状态。

您可以通过以下方式触发动画：
1. 切换标签页
2. 改变时间范围
3. 视图首次出现时

每次触发时，动画都会按照 header -> metrics -> charts -> cards -> insights 的顺序平滑过渡。

---

_**User**_

X Do
/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift
/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:18:53 Cannot find 'AnimationCoordinator' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:19:44 Cannot find 'TimeRange' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:20:38 Cannot find 'DashboardTab' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:231:39 'TimeRange' is not a member type of struct 'X_Do.StatisticsView'

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:798:39 'TimeRange' is not a member type of struct 'X_Do.StatisticsView'

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1137:58 'TimeRange' is not a member type of struct 'X_Do.StatisticsView'

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1161:39 Cannot find type 'StreakDistribution' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1595:50 Cannot find type 'CategoryAnalysis' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1683:61 Cannot find type 'CategoryAnalysis' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1691:59 Cannot find type 'CategoryAnalysis' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1699:55 Cannot find type 'DailyStat' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1714:49 Cannot find type 'DailyStat' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1740:41 Cannot find type 'InsightMessage' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:1984:39 Cannot find type 'InsightMessage' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:2009:48 Cannot find type 'DailyStat' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:2263:28 Cannot find type 'StreakDistribution' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:2454:51 Cannot find type 'DailyStat' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:2682:44 Cannot find type 'DailyStat' in scope

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:2836:17 Invalid redeclaration of 'resetAnalysisSection'

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:4383:1 Circular reference expanding freestanding macro 'Preview'

/Users/<USER>/apps/stopdoinglist/XDo2 2/stopdoinglist/StatisticsView.swift:4387:1 Expected '}' in struct


---

_**Assistant**_



---

