<!-- Generated by SpecStory -->

# 可用工具讨论 (2025-04-23 20:14:01)

_**User**_

你有哪些工具可以用？

---

_**Assistant**_



---

[MODE: ORCHESTRATE][Memory Bank: Inactive]

我可以使用以下工具帮助您：

1. 代码库搜索（codebase_search）- 在代码库中查找相关代码片段
2. 读取文件（read_file）- 查看文件内容
3. 运行终端命令（run_terminal_cmd）- 执行终端命令
4. 列出目录（list_dir）- 显示目录中的文件和文件夹
5. 文本搜索（grep_search）- 使用正则表达式在文件中搜索
6. 编辑文件（edit_file）- 修改或创建文件
7. 文件搜索（file_search）- 根据文件名模糊搜索文件
8. 删除文件（delete_file）- 删除指定文件
9. 重新应用编辑（reapply）- 重新应用最后一次编辑
10. 库ID解析（mcp_context7_resolve_library_id）- 解析库名称为兼容ID
11. 获取库文档（mcp_context7_get_library_docs）- 获取库的文档

需要我帮您执行什么任务吗？

---



---

