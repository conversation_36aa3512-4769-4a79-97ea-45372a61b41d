import Foundation
import CloudKit
import Combine
import StopDoingListKit  // 添加这一行以使用FileManager扩展

// CloudKit管理器 - 处理媒体文件和数据备份的核心类
public class CloudKitManager: ObservableObject {
    // 单例实例
    public static let shared = CloudKitManager()
    
    // CloudKit容器和数据库
    private let container: CKContainer
    private let privateDB: CKDatabase
    
    // 记录类型常量
    private struct RecordTypes {
        static let backup = "Backup"
        static let mediaFile = "MediaFile"
        static let appSettings = "AppSettings"
    }
    
    // 发布属性 - 用于UI显示
    @Published public var isOperationInProgress = false
    @Published public var lastBackupDate: Date?
    @Published public var lastSyncError: String?
    @Published public var backupExists: Bool = false
    
    // 添加媒体记录数组
    private var mediaRecords = [CKRecord]()
    
    // 操作队列
    private let operationQueue = OperationQueue()
    
    // 上传进度回调
    public var progressHandler: ((Double) -> Void)?
    
    // 初始化CloudKit容器和数据库
    private init() {
        // 使用指定的容器标识符
        container = CKContainer(identifier: "iCloud.top.trysapp.xmoment")
        privateDB = container.privateCloudDatabase
        
        // 设置操作队列
        operationQueue.maxConcurrentOperationCount = 1
        
        // 加载上次备份日期
        fetchLastBackupDate()
        
        // 检查是否存在备份
        checkIfBackupExists()
        
        print("[CloudKitManager] 初始化完成")
    }
    
    // MARK: - 公共方法
    
    // 检查是否存在备份（无论是否有lastBackupDate）
    private func checkIfBackupExists() {
        print("[CloudKitManager] 检查是否存在备份...")
        
        // 创建一个查询，检查是否有任何备份记录
        let query = CKQuery(recordType: Self.RecordTypes.backup, predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "lastUpdated", ascending: false)]
        
        let operation = CKQueryOperation(query: query)
        operation.resultsLimit = 1  // 只需要找到一条记录即可
        
        operation.recordMatchedBlock = { [weak self] (recordID, result) in
            guard let self = self else { return }
            
            switch result {
            case .success(_):
                // 找到了至少一条记录
                DispatchQueue.main.async {
                    self.backupExists = true
                    print("[CloudKitManager] 发现备份记录存在")
                }
            case .failure(let error):
                print("[CloudKitManager] 检查备份记录时出错: \(error.localizedDescription)")
            }
        }
        
        operation.queryResultBlock = { [weak self] result in
            guard let self = self else { return }
            
            // 如果没有记录找到，确保设置backupExists = false
            if case .success(_) = result, !self.backupExists {
                DispatchQueue.main.async {
                    print("[CloudKitManager] 未找到备份记录")
                }
            }
        }
        
        privateDB.add(operation)
        
        // 同时检查是否有媒体文件备份
        let mediaQuery = CKQuery(recordType: Self.RecordTypes.mediaFile, predicate: NSPredicate(value: true))
        let mediaOperation = CKQueryOperation(query: mediaQuery)
        mediaOperation.resultsLimit = 1
        
        mediaOperation.recordMatchedBlock = { [weak self] (recordID, result) in
            guard let self = self else { return }
            
            switch result {
            case .success(_):
                // 找到了至少一条媒体记录
                DispatchQueue.main.async {
                    self.backupExists = true
                    print("[CloudKitManager] 发现媒体备份记录存在")
                }
            case .failure(let error):
                print("[CloudKitManager] 检查媒体备份记录时出错: \(error.localizedDescription)")
            }
        }
        
        privateDB.add(mediaOperation)
    }
    
    // 检查CloudKit服务可用性，并刷新备份状态
    public func checkCloudKitAvailability(completion: @escaping (Bool, String?) -> Void) {
        container.accountStatus { [weak self] (status, error) in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                switch status {
                case .available:
                    print("[CloudKitManager] iCloud账户可用")
                    // 当确认iCloud可用时，检查是否存在备份
                    self.checkIfBackupExists()
                    // 刷新备份日期
                    self.fetchLastBackupDate()
                    completion(true, nil)
                case .noAccount:
                    print("[CloudKitManager] 用户未登录iCloud")
                    completion(false, "请在设置中登录您的iCloud账户")
                case .restricted:
                    print("[CloudKitManager] iCloud账户受限")
                    completion(false, "您的iCloud账户受限，无法使用备份功能")
                case .couldNotDetermine:
                    print("[CloudKitManager] 无法确定iCloud状态")
                    completion(false, "无法确定iCloud状态，请检查网络连接")
                case .temporarilyUnavailable:
                    print("[CloudKitManager] iCloud暂时不可用")
                    completion(false, "iCloud暂时不可用，请稍后再试")
                @unknown default:
                    print("[CloudKitManager] 未知iCloud状态")
                    completion(false, "无法访问iCloud，请稍后再试")
                }
            }
        }
    }
    
    // 执行完整备份 - 包括数据文件和媒体文件
    public func performFullBackup(completion: @escaping (Bool, String?) -> Void) {
        // 避免多个备份操作同时进行
        guard !isOperationInProgress else {
            completion(false, "备份操作已在进行中")
            return
        }
        
        // 检查CloudKit可用性
        checkCloudKitAvailability { [weak self] (available, error) in
            guard let self = self else { return }
            
            guard available else {
                completion(false, error)
                return
            }
            
            DispatchQueue.main.async {
                self.isOperationInProgress = true
                print("[CloudKitManager] 开始执行完整备份...")
                // 初始化进度
                self.progressHandler?(0.0)
            }
            
            // 创建操作组
            let backupGroup = DispatchGroup()
            
            // 执行JSON数据备份
            backupGroup.enter()
            self.backupJSONData { (success, error) in
                if !success {
                    print("[CloudKitManager] JSON数据备份失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.33)
                }
                backupGroup.leave()
            }
            
            // 执行媒体文件备份
            backupGroup.enter()
            self.backupMediaFiles { (success, error) in
                if !success {
                    print("[CloudKitManager] 媒体文件备份失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.66)
                }
                backupGroup.leave()
            }
            
            // 备份设置和元数据
            backupGroup.enter()
            self.backupSettings { (success, error) in
                if !success {
                    print("[CloudKitManager] 设置备份失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.9)
                }
                backupGroup.leave()
            }
            
            // 所有备份操作完成后
            backupGroup.notify(queue: .main) {
                let now = Date()
                // 在主线程上更新发布属性
                self.lastBackupDate = now
                
                // 保存备份时间戳
                self.saveBackupTimestamp(date: now) { (timestampSaved, timestampError) in
                    // 在主线程上更新状态
                    DispatchQueue.main.async {
                        self.isOperationInProgress = false
                        // 完成进度
                        self.progressHandler?(1.0)
                        
                        if timestampSaved {
                            print("[CloudKitManager] 完整备份成功完成")
                            completion(true, nil)
                        } else {
                            print("[CloudKitManager] 备份时间戳保存失败: \(timestampError ?? "未知错误")")
                            completion(false, "备份完成但无法更新备份时间")
                        }
                    }
                }
            }
        }
    }
    
    // 执行恢复操作 - 包括数据文件和媒体文件
    public func performFullRestore(completion: @escaping (Bool, String?) -> Void) {
        // 避免多个恢复操作同时进行
        guard !isOperationInProgress else {
            completion(false, "恢复操作已在进行中")
            return
        }
        
        // 确保存在备份
        if !backupExists && lastBackupDate == nil {
            print("[CloudKitManager] 警告：尝试恢复但没有检测到备份存在")
            // 再次检查备份是否存在
            let checkGroup = DispatchGroup()
            checkGroup.enter()
            
            // 创建一个查询，检查是否有任何备份记录
            let query = CKQuery(recordType: Self.RecordTypes.backup, predicate: NSPredicate(value: true))
            let operation = CKQueryOperation(query: query)
            operation.resultsLimit = 1
            
            var foundBackup = false
            
            operation.recordMatchedBlock = { (recordID, result) in
                if case .success(_) = result {
                    foundBackup = true
                }
            }
            
            operation.queryResultBlock = { result in
                checkGroup.leave()
            }
            
            privateDB.add(operation)
            
            // 等待检查完成
            checkGroup.wait()
            
            if !foundBackup {
                print("[CloudKitManager] 错误：没有可用的备份")
                completion(false, "没有可用的备份")
                return
            } else {
                print("[CloudKitManager] 发现备份记录，继续恢复...")
                DispatchQueue.main.async {
                    self.backupExists = true
                }
            }
        }
        
        // 检查CloudKit可用性
        checkCloudKitAvailability { [weak self] (available, error) in
            guard let self = self else { return }
            
            guard available else {
                completion(false, error)
                return
            }
            
            DispatchQueue.main.async {
                self.isOperationInProgress = true
                print("[CloudKitManager] 开始执行完整恢复...")
                // 初始化进度
                self.progressHandler?(0.0)
            }
            
            // 创建操作组
            let restoreGroup = DispatchGroup()
            
            // 恢复JSON数据
            restoreGroup.enter()
            self.restoreJSONData { (success, error) in
                if !success {
                    print("[CloudKitManager] JSON数据恢复失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.33)
                }
                restoreGroup.leave()
            }
            
            // 恢复媒体文件
            restoreGroup.enter()
            self.restoreMediaFiles { (success, error) in
                if !success {
                    print("[CloudKitManager] 媒体文件恢复失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.66)
                }
                restoreGroup.leave()
            }
            
            // 恢复设置
            restoreGroup.enter()
            self.restoreSettings { (success, error) in
                if !success {
                    print("[CloudKitManager] 设置恢复失败: \(error ?? "未知错误")")
                }
                // 更新进度
                DispatchQueue.main.async {
                    self.progressHandler?(0.9)
                }
                restoreGroup.leave()
            }
            
            // 所有恢复操作完成后
            restoreGroup.notify(queue: .main) {
                self.isOperationInProgress = false
                // 完成进度
                self.progressHandler?(1.0)
                print("[CloudKitManager] 完整恢复操作完成")
                
                // 更新上次备份日期
                self.fetchLastBackupDate()
                
                // 通知数据存储重新加载
                NotificationCenter.default.post(name: NSNotification.Name("CloudRestoreCompleted"), object: nil)
                
                completion(true, nil)
            }
        }
    }
    
    // MARK: - 备份方法
    
    // 备份JSON数据文件
    private func backupJSONData(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始备份JSON数据文件...")
        
        // 获取文档目录中的所有JSON文件
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        // 检查音频记录文件，尝试分析音频文件的存储位置
        let audioRecordingsPath = documentsDirectory.appendingPathComponent("audio_recordings.json")
        if fileManager.fileExists(atPath: audioRecordingsPath.path) {
            print("[CloudKitManager] 尝试分析音频记录文件 audio_recordings.json")
            do {
                let data = try Data(contentsOf: audioRecordingsPath)
                if let json = try? JSONSerialization.jsonObject(with: data, options: []),
                   let recordings = json as? [[String: Any]] {
                    print("[CloudKitManager] 发现 \(recordings.count) 条音频记录")
                    
                    // 分析前5条记录（或全部，如果少于5条）
                    let recordsToAnalyze = min(5, recordings.count)
                    for i in 0..<recordsToAnalyze {
                        let recording = recordings[i]
                        print("[CloudKitManager] 音频记录 #\(i+1):")
                        
                        // 打印记录的所有键
                        print("  键: \(recording.keys.joined(separator: ", "))")
                        
                        // 查找可能包含文件路径的键
                        for (key, value) in recording {
                            if key.lowercased().contains("path") || key.lowercased().contains("file") || key.lowercased().contains("url") {
                                print("  \(key): \(value)")
                                
                                // 如果值是字符串，检查它是否指向一个存在的文件
                                if let path = value as? String {
                                    let fullPath: String
                                    if path.hasPrefix("/") {
                                        // 绝对路径
                                        fullPath = path
                                    } else {
                                        // 相对路径
                                        fullPath = documentsDirectory.appendingPathComponent(path).path
                                    }
                                    
                                    if fileManager.fileExists(atPath: fullPath) {
                                        print("  文件存在: \(fullPath)")
                                    } else {
                                        print("  文件不存在: \(fullPath)")
                                    }
                                }
                            }
                        }
                        
                        // 特别检查id字段，可能是文件名
                        if let id = recording["id"] as? String {
                            print("  ID: \(id)")
                            
                            // 检查几个可能的位置
                            let possibleLocations = [
                                documentsDirectory.appendingPathComponent(id),
                                documentsDirectory.appendingPathComponent("AudioRecordings").appendingPathComponent(id),
                                documentsDirectory.appendingPathComponent("Recordings").appendingPathComponent(id)
                            ]
                            
                            for location in possibleLocations {
                                if fileManager.fileExists(atPath: location.path) {
                                    print("  ID匹配文件存在: \(location.path)")
                                }
                            }
                            
                            // 检查是否有带扩展名的文件
                            for ext in ["m4a", "mp3", "wav", "caf"] {
                                let fileWithExt = id + "." + ext
                                let locations = [
                                    documentsDirectory.appendingPathComponent(fileWithExt),
                                    documentsDirectory.appendingPathComponent("AudioRecordings").appendingPathComponent(fileWithExt),
                                    documentsDirectory.appendingPathComponent("Recordings").appendingPathComponent(fileWithExt)
                                ]
                                
                                for location in locations {
                                    if fileManager.fileExists(atPath: location.path) {
                                        print("  ID+扩展名匹配文件存在: \(location.path)")
                                    }
                                }
                            }
                        }
                    }
                } else {
                    print("[CloudKitManager] 无法解析音频记录JSON文件")
                }
            } catch {
                print("[CloudKitManager] 读取音频记录文件失败: \(error.localizedDescription)")
            }
        } else {
            print("[CloudKitManager] 音频记录文件不存在")
        }
        
        do {
            // 获取文档目录中的所有JSON文件
            let fileURLs = try fileManager.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: nil)
                .filter { $0.pathExtension == "json" }
            
            print("[CloudKitManager] 找到 \(fileURLs.count) 个JSON文件需要备份")
            
            let group = DispatchGroup()
            var anyFailure = false
            var failureReason: String?
            
            // 备份每个JSON文件
            for fileURL in fileURLs {
                group.enter()
                
                do {
                    // 读取文件数据
                    _ = try Data(contentsOf: fileURL)
                    let fileName = fileURL.lastPathComponent
                    
                    // 创建CloudKit记录
                    let recordID = CKRecord.ID(recordName: "json_\(fileName)")
                    let record = CKRecord(recordType: Self.RecordTypes.backup, recordID: recordID)
                    record["fileName"] = fileName as CKRecordValue
                    record["fileType"] = "json" as CKRecordValue
                    record["lastUpdated"] = Date() as CKRecordValue
                    
                    // 创建数据资产
                    let asset = CKAsset(fileURL: fileURL)
                    record["data"] = asset
                    
                    // 保存记录到CloudKit
                    let saveOperation = CKModifyRecordsOperation(recordsToSave: [record], recordIDsToDelete: nil)
                    saveOperation.savePolicy = .changedKeys  // 使用changedKeys策略，只更新变化的字段
                    saveOperation.modifyRecordsResultBlock = { result in
                        switch result {
                        case .success:
                            print("[CloudKitManager] 成功备份JSON文件: \(fileName)")
                        case .failure(let error):
                            print("[CloudKitManager] 保存JSON文件 \(fileName) 失败: \(error.localizedDescription)")
                            anyFailure = true
                            failureReason = error.localizedDescription
                        }
                        group.leave()
                    }
                    self.privateDB.add(saveOperation)
                } catch {
                    print("[CloudKitManager] 读取JSON文件 \(fileURL.lastPathComponent) 失败: \(error.localizedDescription)")
                    anyFailure = true
                    failureReason = error.localizedDescription
                    group.leave()
                }
            }
            
            // 所有JSON文件处理完成后
            group.notify(queue: .main) {
                if anyFailure {
                    completion(false, failureReason ?? "部分或全部JSON文件备份失败")
                } else {
                    completion(true, nil)
                }
            }
            
        } catch {
            print("[CloudKitManager] 获取JSON文件列表失败: \(error.localizedDescription)")
            completion(false, error.localizedDescription)
        }
    }
    
    /**
     备份所有媒体文件（图片和音频）到CloudKit
     - Parameter completion: 完成后的回调，包含是否成功和错误消息
     */
    private func backupMediaFiles(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始备份媒体文件...")
        
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        // 检查音频目录的所有可能路径
        let possibleAudioPaths = [
            documentsDirectory.appendingPathComponent("AudioRecordings"),
            documentsDirectory.appendingPathComponent("Recordings"),
            documentsDirectory.appendingPathComponent("Audio"),
            documentsDirectory.appendingPathComponent("Voice"),
            documentsDirectory.appendingPathComponent("VoiceRecordings"),
            documentsDirectory.appendingPathComponent("VoiceMemos"),
            documentsDirectory.appendingPathComponent("Audios")
        ]
        
        // 检查图片目录的所有可能路径
        let possibleImagePaths = [
            documentsDirectory.appendingPathComponent("Images"),
            documentsDirectory.appendingPathComponent("Photos"),
            documentsDirectory.appendingPathComponent("Pictures")
        ]
        
        // 收集所有存在的目录
        var mediaDirectories = [URL]()
        
        // 添加所有存在的音频目录
        for audioPath in possibleAudioPaths {
            if fileManager.fileExists(atPath: audioPath.path) {
                mediaDirectories.append(audioPath)
                print("[CloudKitManager] 找到音频目录: \(audioPath.lastPathComponent)")
            }
        }
        
        // 添加所有存在的图片目录
        for imagePath in possibleImagePaths {
            if fileManager.fileExists(atPath: imagePath.path) {
                mediaDirectories.append(imagePath)
                print("[CloudKitManager] 找到图片目录: \(imagePath.lastPathComponent)")
            }
        }
        
        // 使用DispatchGroup跟踪所有异步操作
        let group = DispatchGroup()
        var encounteredErrors = false
        var errorMessages = [String]()
        
        // 处理每个媒体目录
        for directoryURL in mediaDirectories {
            if !fileManager.fileExists(atPath: directoryURL.path) {
                continue // 跳过不存在的目录
            }
            
            do {
                // 获取目录中的所有文件
                let fileURLs = try fileManager.contentsOfDirectory(at: directoryURL, 
                                                                  includingPropertiesForKeys: nil)
                let directoryName = directoryURL.lastPathComponent
                
                print("[CloudKitManager] 在 \(directoryName) 中找到 \(fileURLs.count) 个文件需要备份")
                
                // 处理目录中的每个文件
                for fileURL in fileURLs {
                    if fileURL.lastPathComponent.hasPrefix(".") {
                        continue // 跳过隐藏文件
                    }
                    
                    group.enter()
                    self.backupMediaFile(from: fileURL, directoryName: directoryName) { (success, error) in
                        if !success {
                            encounteredErrors = true
                            if let errorMsg = error {
                                errorMessages.append("\(fileURL.lastPathComponent): \(errorMsg)")
                            }
                        }
                        group.leave()
                    }
                }
            } catch {
                print("[CloudKitManager] 获取 \(directoryURL.lastPathComponent) 目录中的文件失败: \(error.localizedDescription)")
                encounteredErrors = true
                errorMessages.append("\(directoryURL.lastPathComponent): \(error.localizedDescription)")
            }
        }
        
        // 检查文档根目录中的音频文件
        do {
            let rootFiles = try fileManager.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: nil)
            let audioExtensions = ["m4a", "mp3", "wav", "caf", "aac", "aiff"]
            let audioFiles = rootFiles.filter { audioExtensions.contains($0.pathExtension.lowercased()) }
            
            if !audioFiles.isEmpty {
                print("[CloudKitManager] 找到 \(audioFiles.count) 个根目录音频文件需要备份")
                
                // 处理每个根目录音频文件
                for audioFile in audioFiles {
                    group.enter()
                    self.backupMediaFile(from: audioFile, directoryName: "root") { (success, error) in
                        if !success {
                            encounteredErrors = true
                            if let errorMsg = error {
                                errorMessages.append("根目录_\(audioFile.lastPathComponent): \(errorMsg)")
                            }
                        }
                        group.leave()
                    }
                }
            }
        } catch {
            print("[CloudKitManager] 获取根目录文件失败: \(error.localizedDescription)")
            encounteredErrors = true
            errorMessages.append("根目录: \(error.localizedDescription)")
        }
        
        // 所有备份完成后的回调
        group.notify(queue: .main) {
            if encounteredErrors {
                let errorMessage = "部分媒体文件备份失败: \(errorMessages.joined(separator: "; "))"
                print("[CloudKitManager] \(errorMessage)")
                completion(false, errorMessage)
            } else {
                print("[CloudKitManager] 所有媒体文件备份成功")
                completion(true, nil)
            }
        }
    }
    
    /**
     将要备份的媒体文件从本地复制到临时位置，创建CKAsset并保存到CloudKit
     - Parameters:
        - fileURL: 要备份的媒体文件URL
        - directoryName: 文件所在的目录名称
        - completion: 备份完成后的回调，包含是否成功和错误消息
     */
    private func backupMediaFile(from fileURL: URL, directoryName: String, completion: @escaping (Bool, String?) -> Void) {
        let fileManager = FileManager.default
        
        // 确定文件信息
        let fileName = fileURL.lastPathComponent
        let fileType = fileURL.pathExtension.lowercased()
        
        print("[CloudKitManager] 开始备份媒体文件: \(fileName) 从 \(directoryName)")
        
        // 跳过隐藏文件
        if fileName.hasPrefix(".") {
            completion(true, nil)
            return
        }
        
        // 检查源文件是否存在且可读
        if !fileManager.isReadableFile(atPath: fileURL.path) {
            print("[CloudKitManager] 警告：文件无法读取: \(fileURL.path)")
            completion(false, "文件无法读取: \(fileName)")
            return
        }
        
        // 获取文件大小
        do {
            let fileAttributes = try fileManager.attributesOfItem(atPath: fileURL.path)
            let fileSize = fileAttributes[.size] as? Int64 ?? 0
            print("[CloudKitManager] 备份文件 \(fileName) 大小: \(fileSize) 字节")
            
            // 创建唯一的记录ID
            let recordID = CKRecord.ID(recordName: "\(directoryName)_\(fileName)")
            let record = CKRecord(recordType: Self.RecordTypes.mediaFile, recordID: recordID)
            
            // 设置记录属性
            record["fileName"] = fileName as CKRecordValue
            record["fileType"] = fileType as CKRecordValue
            record["directory"] = directoryName as CKRecordValue
            record["lastUpdated"] = Date() as CKRecordValue
            
            // 创建临时文件
            let tempDir = fileManager.temporaryDirectory
            let tempFileName = UUID().uuidString + "." + fileType
            let tempURL = tempDir.appendingPathComponent(tempFileName)
            
            // 复制文件到临时位置
            try fileManager.copyItem(at: fileURL, to: tempURL)
            
            // 验证临时文件已创建
            if !fileManager.fileExists(atPath: tempURL.path) {
                print("[CloudKitManager] 错误：无法创建临时文件: \(tempURL.path)")
                completion(false, "无法创建临时文件: \(fileName)")
                return
            }
            
            // 创建媒体资产（从临时文件）
            let asset = CKAsset(fileURL: tempURL)
            
            // 确保使用一致的字段名称 - fileAsset
            record["fileAsset"] = asset
            
            // 保存记录到CloudKit
            let saveOperation = CKModifyRecordsOperation(recordsToSave: [record], recordIDsToDelete: nil)
            saveOperation.savePolicy = .changedKeys  // 使用changedKeys策略，只更新变化的字段
            saveOperation.modifyRecordsResultBlock = { result in
                // 删除临时文件
                try? fileManager.removeItem(at: tempURL)
                
                switch result {
                case .success:
                    print("[CloudKitManager] 成功备份媒体文件: \(fileName)")
                    completion(true, nil)
                case .failure(let error):
                    print("[CloudKitManager] 保存媒体文件 \(fileName) 失败: \(error.localizedDescription)")
                    completion(false, error.localizedDescription)
                }
            }
            self.privateDB.add(saveOperation)
            
        } catch {
            print("[CloudKitManager] 处理媒体文件 \(fileName) 时出错: \(error.localizedDescription)")
            completion(false, error.localizedDescription)
        }
    }
    
    // 备份应用设置
    private func backupSettings(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始备份应用设置...")
        
        // 创建唯一的设置记录
        let recordID = CKRecord.ID(recordName: "app_settings")
        let record = CKRecord(recordType: Self.RecordTypes.appSettings, recordID: recordID)
        
        // 获取用户默认设置
        let userDefaults = UserDefaults.standard
        
        // 只备份我们应用自己的设置，避免系统设置
        let safeKeysToBackup = [
            "colorSchemeValue",
            "firstLaunch",
            "lastBackupDate",
            "cloud_backup_enabled",
            "notificationEnabled",
            "dailyReminderTime",
            "weeklyReportDay",
            "audioFeedbackEnabled",
            "hapticFeedbackEnabled",
            "customThemeColor"
        ]
        
        // 将安全的设置值添加到记录中
        for key in safeKeysToBackup {
            if let value = userDefaults.object(forKey: key) as? CKRecordValue {
                // 确保键名符合CloudKit要求 - 只使用字母、数字和下划线
                let safeKey = key.replacingOccurrences(of: "[-.]", with: "_", options: .regularExpression)
                record[safeKey] = value
                print("[CloudKitManager] 备份设置: \(key) -> \(safeKey)")
            }
        }
        
        // 添加备份时间戳
        record["backup_timestamp"] = Date() as CKRecordValue
        
        // 保存记录到CloudKit
        let saveOperation = CKModifyRecordsOperation(recordsToSave: [record], recordIDsToDelete: nil)
        saveOperation.savePolicy = .changedKeys  // 使用changedKeys策略
        saveOperation.modifyRecordsResultBlock = { result in
            switch result {
            case .success:
                print("[CloudKitManager] 成功备份应用设置")
                completion(true, nil)
            case .failure(let error):
                print("[CloudKitManager] 保存应用设置失败: \(error.localizedDescription)")
                completion(false, error.localizedDescription)
            }
        }
        self.privateDB.add(saveOperation)
    }
    
    // 保存备份时间戳
    private func saveBackupTimestamp(date: Date, completion: @escaping (Bool, String?) -> Void) {
        let recordID = CKRecord.ID(recordName: "backup_timestamp")
        
        // 首先尝试获取现有时间戳记录
        privateDB.fetch(withRecordID: recordID) { (record, error) in
            let timestampRecord: CKRecord
            
            if let existingRecord = record {
                // 更新现有记录
                timestampRecord = existingRecord
            } else {
                // 创建新记录
                timestampRecord = CKRecord(recordType: "BackupTimestamp", recordID: recordID)
            }
            
            // 设置时间戳
            timestampRecord["timestamp"] = date as CKRecordValue
            
            // 保存记录
            let saveOperation = CKModifyRecordsOperation(recordsToSave: [timestampRecord], recordIDsToDelete: nil)
            saveOperation.savePolicy = .changedKeys  // 使用changedKeys策略
            saveOperation.modifyRecordsResultBlock = { result in
                switch result {
                case .success:
                    print("[CloudKitManager] 成功保存备份时间戳: \(date)")
                    
                    // 修复线程安全问题，确保在主线程上更新发布属性
                    DispatchQueue.main.async {
                        self.lastBackupDate = date
                    }
                    
                    completion(true, nil)
                case .failure(let error):
                    print("[CloudKitManager] 保存备份时间戳失败: \(error.localizedDescription)")
                    completion(false, error.localizedDescription)
                }
            }
            self.privateDB.add(saveOperation)
        }
    }
    
    // MARK: - 恢复方法
    
    // 恢复JSON数据文件 - 更安全的方法，通过ID直接获取记录
    private func restoreJSONData(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始恢复JSON数据文件...")
        
        // 获取document目录中的所有可能的JSON文件名
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        let possibleFileNames = [
            "habits.json",
            "triggers.json",
            "resets.json",
            "reviews.json",
            "habit_reports.json",
            "audio_recordings.json",
            "xmoment_sessions.json",
            "xmoment_user_stats.json",
            "todo.json",
            "userstatistics.json",
            "settings.json",
            "milestones.json"
        ]
        
        let group = DispatchGroup()
        var anySuccess = false
        var lastError: String?
        
        // 对每个可能的文件，尝试直接通过ID获取
        for fileName in possibleFileNames {
            group.enter()
            
            // 创建记录ID
            let recordID = CKRecord.ID(recordName: "json_\(fileName)")
            
            // 尝试获取记录
            self.privateDB.fetch(withRecordID: recordID) { (record, error) in
                if let error = error {
                    if let ckError = error as? CKError, ckError.code == .unknownItem {
                        print("[CloudKitManager] 文件 \(fileName) 在备份中不存在，跳过")
                    } else {
                        print("[CloudKitManager] 获取文件 \(fileName) 失败: \(error.localizedDescription)")
                        lastError = error.localizedDescription
                    }
                    group.leave()
                    return
                }
                
                guard let record = record, 
                      let fileAsset = record["data"] as? CKAsset,
                      let fileURL = fileAsset.fileURL else {
                    print("[CloudKitManager] 备份记录对于文件 \(fileName) 无效")
                    group.leave()
                    return
                }
                
                // 目标文件路径
                let destinationURL = documentsDirectory.appendingPathComponent(fileName)
                
                do {
                    // 如果目标文件已存在，则先删除
                    if fileManager.fileExists(atPath: destinationURL.path) {
                        try fileManager.removeItem(at: destinationURL)
                    }
                    
                    // 复制文件
                    try fileManager.copyItem(at: fileURL, to: destinationURL)
                    print("[CloudKitManager] 成功恢复JSON文件: \(fileName)")
                    anySuccess = true
                } catch {
                    print("[CloudKitManager] 恢复JSON文件 \(fileName) 失败: \(error.localizedDescription)")
                    lastError = error.localizedDescription
                }
                
                group.leave()
            }
        }
        
        // 所有可能的JSON文件处理完成后
        group.notify(queue: .main) {
            if anySuccess {
                completion(true, nil)
            } else {
                completion(false, lastError ?? "未能找到或恢复任何JSON文件")
            }
        }
    }
    
    /**
     从CloudKit恢复所有媒体文件
     - Parameter completion: 完成后的回调，包含是否成功和错误消息
     */
    public func restoreMediaFiles(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始恢复媒体文件...")
        
        // 确保必要的目录存在
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        let imagesDirURL = documentsDirectory.appendingPathComponent("Images")
        let audiosDirURL = documentsDirectory.appendingPathComponent("Audios")
        let recordingsDirURL = documentsDirectory.appendingPathComponent("AudioRecordings")
        
        // 创建所有需要的目录
        let directories = [imagesDirURL, audiosDirURL, recordingsDirURL]
        
        for directory in directories {
            if !fileManager.fileExists(atPath: directory.path) {
                do {
                    try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
                    print("[CloudKitManager] 创建目录: \(directory.lastPathComponent)")
                } catch {
                    print("[CloudKitManager] 创建目录失败: \(error.localizedDescription)")
                    completion(false, "创建必要目录失败: \(error.localizedDescription)")
                    return
                }
            }
        }
        
        // 使用直接的通用方法获取记录
        self.getMediaRecordsDirectly(completion: { [weak self] success, records, errorMessage in
            guard let self = self else { return }
            
            if success, let records = records, !records.isEmpty {
                print("[CloudKitManager] 直接获取到 \(records.count) 条媒体记录")
                DispatchQueue.main.async {
                    self.mediaRecords = records
                    self.processMediaRecordsForRestore(completion: completion)
                }
            } else {
                // 如果CloudKit获取失败或无记录，尝试本地提取
                print("[CloudKitManager] CloudKit获取媒体记录失败或无记录，尝试从本地提取: \(errorMessage ?? "未知错误")")
                self.extractMediaInfoFromBackup { localSuccess, localRecords, localError in
                    if localSuccess, let localRecords = localRecords, !localRecords.isEmpty {
                        print("[CloudKitManager] 从本地提取了 \(localRecords.count) 条媒体记录信息")
                        DispatchQueue.main.async {
                             self.mediaRecords = localRecords
                             self.processMediaRecordsForRestore(completion: completion)
                        }
                    } else {
                         print("[CloudKitManager] 从本地提取媒体信息也失败: \(localError ?? "无本地记录")")
                         DispatchQueue.main.async {
                            completion(false, errorMessage ?? localError ?? "无法获取媒体记录")
                         }
                    }
                }
            }
        })
    }
    
    /**
     尝试直接获取所有媒体记录，仅使用 directory 查询 (再次诊断)
     */
    private func getMediaRecordsDirectly(completion: @escaping (Bool, [CKRecord]?, String?) -> Void) {
        print("[CloudKitManager] 尝试直接获取所有媒体记录 (仅使用 directory 查询 - 再次诊断)...")
        
        // 仅使用 directory 字段进行查询和排序，这是已知配置正确的字段
        let predicate = NSPredicate(format: "directory != %@", "a_non_existent_directory_name_to_match_all_theoretically")
        let query = CKQuery(recordType: Self.RecordTypes.mediaFile, predicate: predicate)
        query.sortDescriptors = [NSSortDescriptor(key: "directory", ascending: true)] 
        
        var allFetchedRecords = [CKRecord]()
        let operation = CKQueryOperation(query: query)
        operation.resultsLimit = CKQueryOperation.maximumResults // 获取尽可能多的记录
        
        operation.recordMatchedBlock = { recordID, result in
            switch result {
            case .success(let record):
                allFetchedRecords.append(record)
            case .failure(let error):
                print("[CloudKitManager] 获取单个记录失败: \(recordID.recordName) - \(error.localizedDescription)")
            }
        }
        
        // 递归获取函数
        func fetchNextBatch(cursor: CKQueryOperation.Cursor?) {
            let batchOperation: CKQueryOperation
            if let cursor = cursor {
                print("[CloudKitManager] 使用游标继续获取...")
                batchOperation = CKQueryOperation(cursor: cursor)
            } else {
                // 首次查询，使用原始query
                batchOperation = operation // 复用第一次配置好的operation
            }
            batchOperation.resultsLimit = CKQueryOperation.maximumResults
            
            batchOperation.recordMatchedBlock = { recordID, result in
                 switch result {
                 case .success(let record):
                    // 确保不重复添加
                    if !allFetchedRecords.contains(where: { $0.recordID == record.recordID }) {
                         allFetchedRecords.append(record)
                    }
                 case .failure(let error):
                     print("[CloudKitManager] 获取单个记录失败 (批次): \(recordID.recordName) - \(error.localizedDescription)")
                 }
             }
            
            batchOperation.queryResultBlock = { result in
                 switch result {
                 case .success(let nextCursor):
                     if let nextCursor = nextCursor {
                         // 还有更多记录，继续获取
                         fetchNextBatch(cursor: nextCursor)
                     } else {
                         // 所有记录获取完毕
                         print("[CloudKitManager] 直接查询(directory)完成，共找到 \(allFetchedRecords.count) 条记录")
                         DispatchQueue.main.async {
                            completion(true, allFetchedRecords.isEmpty ? nil : allFetchedRecords, nil)
                         }
                     }
                 case .failure(let error):
                     print("[CloudKitManager] 直接获取记录查询失败(directory): \(error.localizedDescription)")
                     // 查询失败，触发回退
                     DispatchQueue.main.async {
                         completion(false, nil, "CloudKit查询失败(directory): \(error.localizedDescription)")
                     }
                 }
             }
             self.privateDB.add(batchOperation)
        }
        
        // 开始第一次获取
        fetchNextBatch(cursor: nil)
    }
    
    /**
     通过尝试获取一个示例记录来检查权限问题
     */
    private func checkPermissionsWithSampleRecord(completion: @escaping (Bool, [CKRecord]?, String?) -> Void) {
        print("[CloudKitManager] 检查CloudKit权限...")
        
        // 创建一个临时记录来测试权限
        let recordID = CKRecord.ID(recordName: "test_permissions")
        let testRecordType = "TestPermissions" // 使用字符串而不是枚举
        let record = CKRecord(recordType: testRecordType, recordID: recordID)
        record["test"] = "value" as CKRecordValue
        
        // 尝试保存这个记录
        privateDB.save(record) { (_, error) in
            // 不论成功失败，删除测试记录
            defer { self.privateDB.delete(withRecordID: recordID) { _,_ in } }
            
            if let error = error {
                print("[CloudKitManager] 权限测试失败: \(error.localizedDescription)")
                var errorMessage = "CloudKit访问失败: \(error.localizedDescription)"
                if let ckError = error as? CKError {
                    switch ckError.code {
                    case .networkUnavailable, .networkFailure:
                         errorMessage = "网络连接不可用，请检查您的网络连接"
                    case .notAuthenticated:
                         errorMessage = "没有iCloud权限，请检查您的iCloud设置"
                    case .permissionFailure:
                         errorMessage = "没有足够的权限访问CloudKit数据"
                    default: break
                    }
                }
                DispatchQueue.main.async {
                    // 权限测试失败，直接回调错误
                    completion(false, nil, errorMessage)
                }
            } else {
                print("[CloudKitManager] 权限测试成功，但CloudKit查询本身失败")
                 // 权限OK，但查询失败，说明问题在于查询或数据本身
                 DispatchQueue.main.async {
                     completion(false, nil, "CloudKit查询失败，请检查CloudKit Dashboard索引设置或稍后再试")
                 }
            }
        }
    }
    
    /**
     尝试从备份记录（如音频记录JSON）中提取媒体文件信息
     */
    private func extractMediaInfoFromBackup(completion: @escaping (Bool, [CKRecord]?, String?) -> Void) {
        print("[CloudKitManager] 尝试从备份记录中提取媒体文件信息...")
        
        // 检查是否存在音频记录文件
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioRecordingsPath = documentsDirectory.appendingPathComponent("audio_recordings.json")
        
        if fileManager.fileExists(atPath: audioRecordingsPath.path) {
            do {
                let data = try Data(contentsOf: audioRecordingsPath)
                if let json = try? JSONSerialization.jsonObject(with: data, options: []),
                   let recordings = json as? [[String: Any]] {
                    
                    print("[CloudKitManager] 从音频记录文件中找到 \(recordings.count) 条记录")
                    
                    // 创建假的CKRecord代表这些媒体文件
                    var fakeRecords = [CKRecord]()
                    
                    for (index, recording) in recordings.enumerated() {
                        // 尝试找到文件路径或文件名
                        var fileName: String?
                        var filePath: String?
                        
                        // 查找可能包含路径的键
                        for (key, value) in recording {
                            if key.lowercased().contains("path") || key.lowercased().contains("file") || key.lowercased().contains("url") {
                                if let path = value as? String {
                                    filePath = path
                                    fileName = URL(fileURLWithPath: path).lastPathComponent
                                    break
                                }
                            }
                        }
                        
                        // 如果没有找到路径，使用ID作为文件名
                        if fileName == nil, let id = recording["id"] as? String {
                            fileName = id + ".m4a"  // 假设是音频文件
                        }
                        
                        // 创建一个假的CKRecord
                        if let fileName = fileName {
                            let recordID = CKRecord.ID(recordName: "fake_\(index)_\(fileName)")
                            let record = CKRecord(recordType: Self.RecordTypes.mediaFile, recordID: recordID)
                            record["fileName"] = fileName as CKRecordValue
                            record["fileType"] = "audio" as CKRecordValue
                            record["directory"] = "AudioRecordings" as CKRecordValue
                            
                            // 如果找到了文件路径，检查文件是否存在
                            if let filePath = filePath {
                                let fullPath: String
                                if filePath.hasPrefix("/") {
                                    fullPath = filePath
                                } else {
                                    fullPath = documentsDirectory.appendingPathComponent(filePath).path
                                }
                                
                                if fileManager.fileExists(atPath: fullPath) {
                                    print("[CloudKitManager] 找到本地媒体文件: \(fullPath)")
                                    
                                    // 创建一个临时URL和资产
                                    let tempDir = fileManager.temporaryDirectory
                                    let tempURL = tempDir.appendingPathComponent(fileName)
                                    
                                    do {
                                        // 复制文件到临时位置
                                        try fileManager.copyItem(at: URL(fileURLWithPath: fullPath), to: tempURL)
                                        
                                        // 创建一个资产
                                        let asset = CKAsset(fileURL: tempURL)
                                        record["fileAsset"] = asset
                                        
                                        fakeRecords.append(record)
                                    } catch {
                                        print("[CloudKitManager] 无法创建临时文件: \(error.localizedDescription)")
                                    }
                                }
                            }
                        }
                    }
                    
                    if !fakeRecords.isEmpty {
                        print("[CloudKitManager] 从本地记录创建了 \(fakeRecords.count) 个假媒体记录")
                        completion(true, fakeRecords, nil)
                    } else {
                        completion(false, nil, "无法从音频记录文件中提取媒体文件信息")
                    }
                } else {
                    print("[CloudKitManager] 无法解析音频记录JSON文件")
                    completion(false, nil, "无法解析音频记录JSON文件")
                }
            } catch {
                print("[CloudKitManager] 读取音频记录文件失败: \(error.localizedDescription)")
                completion(false, nil, "读取音频记录文件失败")
            }
        } else {
            print("[CloudKitManager] 音频记录文件不存在，无法恢复媒体文件")
            completion(false, nil, "没有找到媒体记录，可能没有备份媒体文件")
        }
    }
    
    /**
     处理已获取的媒体记录并进行恢复
     */
    private func processMediaRecordsForRestore(completion: @escaping (Bool, String?) -> Void) {
        if self.mediaRecords.isEmpty {
            print("[CloudKitManager] 没有找到媒体记录进行恢复")
            DispatchQueue.main.async {
                completion(true, "没有媒体文件需要恢复")
            }
            return
        }
        
        print("[CloudKitManager] 开始恢复 \(self.mediaRecords.count) 个媒体文件")
        
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let imagesDirURL = documentsDirectory.appendingPathComponent("Images")
        let audiosDirURL = documentsDirectory.appendingPathComponent("Audios")
        let recordingsDirURL = documentsDirectory.appendingPathComponent("AudioRecordings")
        
        // 使用DispatchGroup追踪所有异步操作
        let group = DispatchGroup()
        var encounteredError = false
        var errorMessages = [String]()
        var successCount = 0
        
        // 处理每个媒体记录
        for record in self.mediaRecords {
            // 尝试获取文件名和类型，处理不同的字段名可能性
            let fileName: String
            let fileType: String
            
            // 尝试获取文件名 - 可能是fileName或name字段
            if let name = record["fileName"] as? String {
                fileName = name
            } else if let name = record["name"] as? String {
                fileName = name
            } else if let name = record.recordID.recordName.split(separator: "_").last {
                // 如果记录ID包含文件名，尝试从中提取
                fileName = String(name)
            } else {
                print("[CloudKitManager] 记录中缺少文件名信息")
                continue
            }
            
            // 尝试获取文件类型 - 可能存在或从文件名推断
            if let type = record["fileType"] as? String {
                fileType = type
            } else if let type = record["type"] as? String {
                fileType = type
            } else if let ext = fileName.split(separator: ".").last {
                // 从文件名中提取扩展名作为类型
                fileType = String(ext).lowercased()
            } else {
                // 默认未知类型
                fileType = "unknown"
            }
            
            // 确定目标目录
            var destinationDirectory: URL
            
            // 如果有目录字段，使用它来确定目标目录
            if let directory = record["directory"] as? String {
                switch directory.lowercased() {
                case "images":
                    destinationDirectory = imagesDirURL
                case "audios":
                    destinationDirectory = audiosDirURL
                case "audiorecordings":
                    destinationDirectory = recordingsDirURL
                case "root":
                    // 在根目录中的文件保存到对应目录
                    if ["jpg", "jpeg", "png", "gif"].contains(fileType) {
                        destinationDirectory = imagesDirURL
                    } else if ["m4a", "mp3", "wav", "caf", "aac"].contains(fileType) {
                        destinationDirectory = audiosDirURL
                    } else {
                        destinationDirectory = documentsDirectory
                    }
                default:
                    // 尝试在文档目录中创建自定义目录
                    destinationDirectory = documentsDirectory.appendingPathComponent(directory)
                    
                    // 确保目录存在
                    if !fileManager.fileExists(atPath: destinationDirectory.path) {
                        do {
                            try fileManager.createDirectory(at: destinationDirectory, withIntermediateDirectories: true)
                            print("[CloudKitManager] 创建自定义目录: \(directory)")
                        } catch {
                            print("[CloudKitManager] 创建自定义目录失败: \(error.localizedDescription)")
                            destinationDirectory = documentsDirectory  // 回退到文档目录
                        }
                    }
                }
            } else {
                // 根据文件类型猜测目标目录
                if ["jpg", "jpeg", "png", "gif", "image"].contains(fileType) {
                    destinationDirectory = imagesDirURL
                } else if ["m4a", "mp3", "wav", "caf", "aac", "audio", "recording"].contains(fileType) {
                    destinationDirectory = audiosDirURL
                } else {
                    // 默认放在记录目录
                    destinationDirectory = recordingsDirURL
                }
            }
            
            let destinationURL = destinationDirectory.appendingPathComponent(fileName)
            
            // 尝试先从fileAsset字段获取资产，如果不存在则尝试data字段
            if record["fileAsset"] is CKAsset {
                group.enter()
                self.downloadMediaFile(from: record, assetField: "fileAsset", to: destinationURL, group: group) { (success, errorMessage) in
                    if success {
                        successCount += 1
                    } else {
                        encounteredError = true
                        if let message = errorMessage {
                            errorMessages.append("\(fileName): \(message)")
                        }
                    }
                }
            } else if record["data"] is CKAsset {
                group.enter()
                self.downloadMediaFile(from: record, assetField: "data", to: destinationURL, group: group) { (success, errorMessage) in
                    if success {
                        successCount += 1
                    } else {
                        encounteredError = true
                        if let message = errorMessage {
                            errorMessages.append("\(fileName): \(message)")
                        }
                    }
                }
            } else {
                print("[CloudKitManager] 记录中没有找到有效的文件资产字段: \(fileName)")
                errorMessages.append("\(fileName): 没有找到有效的文件资产")
            }
        }
        
        // 所有下载完成后的回调
        group.notify(queue: .main) {
            print("[CloudKitManager] 媒体文件恢复完成，成功: \(successCount)/\(self.mediaRecords.count)")
            
            if successCount > 0 {
                if encounteredError {
                    let errorMessage = "部分媒体文件恢复失败: \(errorMessages.joined(separator: "; "))"
                    print("[CloudKitManager] \(errorMessage)")
                    completion(true, errorMessage)  // 视为部分成功
                } else {
                    print("[CloudKitManager] 所有媒体文件恢复成功")
                    completion(true, nil)
                }
            } else {
                completion(false, "所有媒体文件恢复失败: \(errorMessages.joined(separator: "; "))")
            }
        }
    }
    
    /**
     从CKRecord下载媒体文件并写入本地路径
     - Parameters:
        - record: 包含媒体文件的CKRecord
        - assetField: 记录中包含资产的字段名
        - destinationURL: 要保存到的本地路径
        - group: 用于异步操作的DispatchGroup
        - completion: 下载完成后的回调，包含是否成功和错误消息
     */
    private func downloadMediaFile(from record: CKRecord, assetField: String, to destinationURL: URL, group: DispatchGroup, completion: @escaping (Bool, String?) -> Void) {
        guard let fileAsset = record[assetField] as? CKAsset,
              let assetURL = fileAsset.fileURL else {
            print("[CloudKitManager] 记录中没有有效的文件资产: \(assetField)")
            group.leave()
            DispatchQueue.main.async {
                completion(false, "记录中没有有效的文件资产")
            }
            return
        }
        
        // 检查源文件是否存在
        if !FileManager.default.fileExists(atPath: assetURL.path) {
            print("[CloudKitManager] 源文件不存在: \(assetURL.path)")
            group.leave()
            DispatchQueue.main.async {
                completion(false, "源文件不存在")
            }
            return
        }
        
        // 使用后台队列进行文件操作
        DispatchQueue.global(qos: .userInitiated).async {
            let fileManager = FileManager.default
            
            // 检查目标路径是否存在，如果存在则删除
            if fileManager.fileExists(atPath: destinationURL.path) {
                do {
                    try fileManager.removeItem(at: destinationURL)
                    print("[CloudKitManager] 已删除现有文件: \(destinationURL.lastPathComponent)")
                } catch {
                    print("[CloudKitManager] 删除现有文件失败: \(error.localizedDescription)")
                    group.leave()
                    DispatchQueue.main.async {
                        completion(false, "删除现有文件失败: \(error.localizedDescription)")
                    }
                    return
                }
            }
            
            // 确保目标目录存在
            let destinationDirectory = destinationURL.deletingLastPathComponent()
            if !fileManager.fileExists(atPath: destinationDirectory.path) {
                do {
                    try fileManager.createDirectory(at: destinationDirectory, withIntermediateDirectories: true)
                    print("[CloudKitManager] 创建目录: \(destinationDirectory.lastPathComponent)")
                } catch {
                    print("[CloudKitManager] 创建目录失败: \(error.localizedDescription)")
                    group.leave()
                    DispatchQueue.main.async {
                        completion(false, "创建目录失败: \(error.localizedDescription)")
                    }
                    return
                }
            }
            
            // 复制文件到目标位置
            do {
                try fileManager.copyItem(at: assetURL, to: destinationURL)
                print("[CloudKitManager] 成功恢复文件: \(destinationURL.lastPathComponent)")
                group.leave()
                DispatchQueue.main.async {
                    completion(true, nil)
                }
            } catch {
                print("[CloudKitManager] 复制文件失败: \(error.localizedDescription)")
                group.leave()
                DispatchQueue.main.async {
                    completion(false, "复制文件失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 恢复应用设置
    private func restoreSettings(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始恢复应用设置...")
        
        // 创建唯一的设置记录ID
        let recordID = CKRecord.ID(recordName: "app_settings")
        
        // 原始键到安全键的映射
        let keyMapping = [
            "colorSchemeValue": "colorSchemeValue",
            "firstLaunch": "firstLaunch",
            "lastBackupDate": "lastBackupDate",
            "cloud_backup_enabled": "cloud_backup_enabled",
            "notificationEnabled": "notificationEnabled",
            "dailyReminderTime": "dailyReminderTime",
            "weeklyReportDay": "weeklyReportDay",
            "audioFeedbackEnabled": "audioFeedbackEnabled",
            "hapticFeedbackEnabled": "hapticFeedbackEnabled",
            "customThemeColor": "customThemeColor"
        ]
        
        // 安全键到原始键的反向映射
        let reverseKeyMapping = Dictionary(uniqueKeysWithValues: keyMapping.map { ($0.value.replacingOccurrences(of: "[-.]", with: "_", options: .regularExpression), $0.key) })
        
        // 获取设置记录
        privateDB.fetch(withRecordID: recordID) { (record, error) in
            if let error = error {
                // 只有在记录确实不存在时才是真正的错误
                if let cloudError = error as? CKError, cloudError.code == .unknownItem {
                    print("[CloudKitManager] 应用设置记录不存在，可能是首次备份")
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                } else {
                    print("[CloudKitManager] 获取应用设置记录失败: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(false, error.localizedDescription)
                    }
                }
                return
            }
            
            guard let record = record else {
                print("[CloudKitManager] 应用设置记录为空")
                DispatchQueue.main.async {
                    completion(false, "应用设置记录为空")
                }
                return
            }
            
            // 恢复设置到UserDefaults
            let userDefaults = UserDefaults.standard
            
            // 获取所有键（除了备份时间戳）
            let recordKeys = record.allKeys().filter { $0 != "backup_timestamp" }
            
            // 恢复每个设置
            for safeKey in recordKeys {
                if let originalKey = reverseKeyMapping[safeKey], let value = record[safeKey] {
                    userDefaults.set(value, forKey: originalKey)
                    print("[CloudKitManager] 恢复设置: \(safeKey) -> \(originalKey)")
                } else if let value = record[safeKey] {
                    // 如果找不到映射，就直接使用安全键
                    userDefaults.set(value, forKey: safeKey)
                    print("[CloudKitManager] 恢复设置 (无映射): \(safeKey)")
                }
            }
            
            print("[CloudKitManager] 成功恢复应用设置")
            DispatchQueue.main.async {
                completion(true, nil)
            }
        }
    }
    
    // MARK: - 助手方法
    
    // 获取上次备份日期
    private func fetchLastBackupDate() {
        let recordID = CKRecord.ID(recordName: "backup_timestamp")
        
        privateDB.fetch(withRecordID: recordID) { [weak self] (record, error) in
            guard let self = self else { return }
            
            if let error = error {
                // 只有在记录确实不存在时才是真正的错误
                if let cloudError = error as? CKError, cloudError.code == .unknownItem {
                    print("[CloudKitManager] 备份时间戳记录不存在，可能是首次备份或之前未使用时间戳")
                    
                    // 尝试通过检查备份记录来推断备份时间
                    self.inferBackupDateFromRecords()
                } else {
                    DispatchQueue.main.async {
                        print("[CloudKitManager] 获取备份时间戳失败: \(error.localizedDescription)")
                    }
                }
                return
            }
            
            if let record = record, let timestamp = record["timestamp"] as? Date {
                DispatchQueue.main.async {
                    self.lastBackupDate = timestamp
                    self.backupExists = true  // 如果有时间戳，肯定有备份
                    print("[CloudKitManager] 获取到上次备份时间: \(timestamp)")
                }
            }
        }
    }
    
    // 尝试从备份记录中推断备份日期
    private func inferBackupDateFromRecords() {
        print("[CloudKitManager] 尝试从备份记录中推断最近的备份日期...")
        
        // 查询JSON备份记录，按lastUpdated排序
        let predicate = NSPredicate(value: true)
        let query = CKQuery(recordType: Self.RecordTypes.backup, predicate: predicate)
        query.sortDescriptors = [NSSortDescriptor(key: "lastUpdated", ascending: false)]
        
        let operation = CKQueryOperation(query: query)
        operation.resultsLimit = 1  // 只需要最新的一条
        
        operation.recordMatchedBlock = { [weak self] (recordID, result) in
            guard let self = self else { return }
            
            switch result {
            case .success(let record):
                if let lastUpdated = record["lastUpdated"] as? Date {
                    DispatchQueue.main.async {
                        self.lastBackupDate = lastUpdated
                        self.backupExists = true
                        print("[CloudKitManager] 从备份记录推断出备份日期: \(lastUpdated)")
                    }
                } else {
                    print("[CloudKitManager] 备份记录中无lastUpdated字段")
                    DispatchQueue.main.async {
                        self.backupExists = true  // 仍然标记存在备份
                    }
                }
            case .failure(let error):
                print("[CloudKitManager] 获取备份记录失败: \(error.localizedDescription)")
            }
        }
        
        operation.queryResultBlock = { [weak self] result in
            guard let self = self else { return }
            
            if case .success(_) = result, self.lastBackupDate == nil {
                // 如果仍然没有找到日期但查询成功，检查是否应标记为有备份
                self.checkIfBackupExists()
            }
        }
        
        privateDB.add(operation)
    }
    
    /**
     获取所有媒体记录并存储在mediaRecords属性中
     */
    private func fetchMediaRecords(usingSorting: Bool, completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 开始获取所有媒体记录...")
        
        // 使用简单查询获取所有媒体记录，不对recordName字段进行查询
        let query = CKQuery(recordType: Self.RecordTypes.mediaFile, predicate: NSPredicate(value: true))
        
        // 可选排序，但不使用recordName作为排序键
        if usingSorting {
            query.sortDescriptors = [NSSortDescriptor(key: "lastUpdated", ascending: false)]
        }
        
        let queryOperation = CKQueryOperation(query: query)
        
        var fetchedRecords = [CKRecord]()
        
        queryOperation.recordMatchedBlock = { (recordID, result) in
            switch result {
            case .success(let record):
                print("[CloudKitManager] 找到媒体记录: \(recordID.recordName)")
                fetchedRecords.append(record)
            case .failure(let error):
                print("[CloudKitManager] 获取媒体记录失败: \(error.localizedDescription)")
            }
        }
        
        queryOperation.queryResultBlock = { [weak self] (result) in
            guard let self = self else { return }
            
            switch result {
            case .success(_):
                print("[CloudKitManager] 媒体记录查询完成，找到 \(fetchedRecords.count) 条记录")
                DispatchQueue.main.async {
                    self.mediaRecords = fetchedRecords
                    completion(true, nil)
                }
            case .failure(let error):
                print("[CloudKitManager] 媒体记录查询失败: \(error.localizedDescription)")
                
                // 错误处理 - 可能需要检查网络连接或iCloud权限
                if let ckError = error as? CKError {
                    switch ckError.code {
                    case .networkUnavailable, .networkFailure:
                        DispatchQueue.main.async {
                            completion(false, "网络连接不可用，请检查您的网络连接")
                        }
                    case .notAuthenticated:
                        DispatchQueue.main.async {
                            completion(false, "iCloud账户未认证，请检查您的iCloud设置")
                        }
                    case .serverRejectedRequest:
                        DispatchQueue.main.async {
                            completion(false, "服务器拒绝请求，请稍后再试")
                        }
                    default:
                        // 如果是索引错误，尝试使用替代方法获取所有记录
                        if ckError.code == .serverRejectedRequest && ckError.localizedDescription.contains("queryable") {
                            self.fetchAllMediaRecordsManually(completion: completion)
                            return
                        }
                        DispatchQueue.main.async {
                            completion(false, "查询媒体记录失败: \(error.localizedDescription)")
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(false, "查询媒体记录失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        self.privateDB.add(queryOperation)
    }
    
    /**
     手动获取所有媒体记录 - 当标准查询失败时使用
     这是一个备用方法，不使用可能引起问题的字段进行查询
     */
    private func fetchAllMediaRecordsManually(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 尝试使用备用方法获取媒体记录...")
        
        var fetchedRecords = [CKRecord]()
        
        // 使用简单的查询但避免使用可能引起问题的字段
        let query = CKQuery(recordType: Self.RecordTypes.mediaFile, predicate: NSPredicate(value: true))
        // 不设置排序以避免索引问题
        
        // 使用区域ID创建操作
        let operation = CKQueryOperation(query: query)
        
        // 接收记录的回调
        operation.recordMatchedBlock = { (recordID, result) in
            switch result {
            case .success(let record):
                fetchedRecords.append(record)
                print("[CloudKitManager] 找到媒体记录: \(recordID.recordName)")
            case .failure(let error):
                print("[CloudKitManager] 获取媒体记录失败: \(error.localizedDescription)")
            }
        }
        
        // 查询完成的回调
        operation.queryResultBlock = { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(_):
                // 在主线程上更新UI相关的状态
                DispatchQueue.main.async {
                    print("[CloudKitManager] 媒体记录手动查询完成，找到 \(fetchedRecords.count) 条记录")
                    self.mediaRecords = fetchedRecords
                    completion(true, nil)
                }
            case .failure(let error):
                print("[CloudKitManager] 备用方法查询也失败: \(error.localizedDescription)")
                
                // 尝试直接列出所有记录的最后一次尝试
                self.listAllRecords(completion: completion)
            }
        }
        
        // 添加操作到数据库
        self.privateDB.add(operation)
    }
    
    /**
     列出所有记录的最后一次尝试方法
     */
    private func listAllRecords(completion: @escaping (Bool, String?) -> Void) {
        print("[CloudKitManager] 尝试列出所有CloudKit记录...")
        
        // 获取所有记录类型，并为每种类型执行查询
        let recordTypes = [Self.RecordTypes.mediaFile]
        var allMediaRecords = [CKRecord]()
        let group = DispatchGroup()
        
        for recordType in recordTypes {
            group.enter()
            
            // 创建简单查询
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
            let operation = CKQueryOperation(query: query)
            
            // 设置回调
            operation.recordMatchedBlock = { (recordID, result) in
                switch result {
                case .success(let record):
                    // 只保留媒体文件记录
                    if recordType == Self.RecordTypes.mediaFile {
                        allMediaRecords.append(record)
                    }
                    print("[CloudKitManager] 找到记录: \(recordType) - \(recordID.recordName)")
                case .failure(let error):
                    print("[CloudKitManager] 获取记录失败: \(error.localizedDescription)")
                }
            }
            
            operation.queryResultBlock = { _ in
                group.leave()
            }
            
            self.privateDB.add(operation)
        }
        
        // 当所有查询完成时
        group.notify(queue: .main) {
            print("[CloudKitManager] 记录列表查询完成，找到 \(allMediaRecords.count) 条媒体记录")
            
            if !allMediaRecords.isEmpty {
                self.mediaRecords = allMediaRecords
                completion(true, nil)
            } else {
                completion(false, "无法获取媒体记录")
            }
        }
    }
} 