import Foundation
import Combine
import StopDoingListKit
import StoreKit

public class CloudBackupManager: ObservableObject {
    public static let shared = CloudBackupManager()

    // 发布属性
    @Published public var isBackupEnabled: Bool = false
    @Published public var lastBackupDate: Date?
    @Published public var isBackupInProgress: Bool = false
    @Published public var isRestoreInProgress: Bool = false

    // 用于存储在UserDefaults中的键
    private struct Keys {
        static let isBackupEnabled = "cloud_backup_enabled"
        static let lastBackupDate = "cloud_last_backup_date"
        static let backupDataPrefix = "cloud_backup_data_"
        static let backupVersionKey = "cloud_backup_version"
    }

    // 备份版本号，用于未来的数据迁移
    private let currentBackupVersion = 1

    // 文件名常量
    private struct FileNames {
        static let habits = "habits.json"
        static let triggers = "triggers.json"
        static let resets = "resets.json"
        static let reviews = "reviews.json"
        static let habitReports = "habit_reports.json"
        static let audioRecordings = "audio_recordings.json"
        static let xMomentSessions = "xmoment_sessions.json"
        static let xMomentUserStats = "xmoment_user_stats.json"
    }

    // 文档目录
    private var documentsDirectory: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    // 私有初始化方法 - 增强健壮性处理
    private init() {
        print("[CloudBackupManager] 开始初始化...")

        // 安全加载设置，不直接依赖iCloud状态
        loadDefaultSettings()

        // 延迟一点时间后再尝试设置iCloud，避免启动时阻塞
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            guard let self = self else { return }

            print("[CloudBackupManager] 初始化存储目录...")
            do {
                try self.ensureDirectoriesExist()
            } catch {
                print("[CloudBackupManager] 警告: 创建目录失败: \(error.localizedDescription)")
            }
        }
    }

    // 首先加载默认设置，确保基本功能可用
    private func loadDefaultSettings() {
        // 从UserDefaults加载设置
        isBackupEnabled = UserDefaults.standard.bool(forKey: Keys.isBackupEnabled)
        lastBackupDate = UserDefaults.standard.object(forKey: Keys.lastBackupDate) as? Date

        print("[CloudBackupManager] 已加载默认设置")
    }

    // MARK: - 公共方法

    /// 启用或禁用iCloud备份
    public func setBackupEnabled(_ enabled: Bool) {
        // 检查是否为高级用户
        if enabled && !DataStore.shared.canUseCloudBackup() {
            // 如果不是高级用户，不允许启用备份
            return
        }

        isBackupEnabled = enabled

        // 保存到UserDefaults
        UserDefaults.standard.set(enabled, forKey: Keys.isBackupEnabled)

        // 如果启用了备份，立即执行一次备份
        if enabled {
            performBackup()
        }
    }

    /// 执行备份操作 - 安全处理
    public func performBackup() {
        // 在主线程上检查状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 检查是否为高级用户
            guard DataStore.shared.canUseCloudBackup() else {
                print("[CloudBackupManager] 警告: 非高级用户，无法执行备份")
                return
            }

            // 检查iCloud是否可用
            guard self.isICloudAvailable() else {
                print("[CloudBackupManager] 警告: iCloud不可用，无法执行备份")
                return
            }

            guard self.isBackupEnabled, !self.isBackupInProgress else {
                print("[CloudBackupManager] 备份已禁用或正在进行中")
                return
            }

            // 设置状态
            self.isBackupInProgress = true
            print("[CloudBackupManager] 开始执行iCloud备份...")

            // 执行预检查
            guard self.verifyICloudConnection() else {
                print("[CloudBackupManager] 无法连接到iCloud，终止备份")
                DispatchQueue.main.async {
                    self.isBackupInProgress = false
                }
                return
            }

            // 在后台线程执行备份
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                guard let self = self else {
                    // 确保状态一致性
                    DispatchQueue.main.async {
                        self?.isBackupInProgress = false
                    }
                    return
                }

                do {
                    // 备份DataStore中的所有JSON文件
                    try self.backupAllDataStoreFiles()

                    // 更新最后备份时间
                    let now = Date()
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }

                        self.lastBackupDate = now
                        UserDefaults.standard.set(now, forKey: Keys.lastBackupDate)
                        self.isBackupInProgress = false
                        print("[CloudBackupManager] iCloud备份成功完成!")
                    }
                } catch {
                    print("[CloudBackupManager] iCloud备份失败: \(error.localizedDescription)")
                    DispatchQueue.main.async { [weak self] in
                        self?.isBackupInProgress = false
                    }
                }
            }
        }
    }

    /// 检查是否存在iCloud备份 - 安全处理
    public func hasCloudBackup() -> Bool {
        // 如果iCloud不可用，直接返回false
        if !isICloudAvailable() {
            return false
        }

        return lastBackupDate != nil
    }

    /// 执行恢复操作 - 安全处理
    public func performRestore(completion: @escaping (Bool, String?) -> Void) {
        // 在主线程上检查状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                completion(false, NSLocalizedString("cloudbackup.error.internal", comment: "Internal error message"))
                return
            }

            // 检查是否为高级用户
            guard DataStore.shared.canUseCloudBackup() else {
                completion(false, NSLocalizedString("cloudbackup.error.premiumRequired", comment: "Premium required error"))
                return
            }

            // 检查iCloud是否可用
            guard self.isICloudAvailable() else {
                completion(false, NSLocalizedString("cloudbackup.error.unavailable", comment: "iCloud unavailable error"))
                return
            }

            guard !self.isRestoreInProgress else {
                completion(false, NSLocalizedString("cloudbackup.error.restoreInProgress", comment: "Restore already in progress error"))
                return
            }

            // 设置状态
            self.isRestoreInProgress = true
            print("[CloudBackupManager] 开始从iCloud恢复数据...")

            // 在后台线程执行恢复
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                guard let self = self else {
                    DispatchQueue.main.async {
                        completion(false, NSLocalizedString("cloudbackup.error.internal", comment: "Internal error message"))
                    }
                    return
                }

                do {
                    // 确保目录结构已创建
                    try self.ensureDirectoriesExist()

                    // 从iCloud恢复所有数据
                    try self.restoreAllDataStoreFiles()

                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else {
                            completion(false, NSLocalizedString("cloudbackup.error.internalRestoreError", comment: "Internal error during restore message"))
                            return
                        }

                        self.isRestoreInProgress = false
                        print("[CloudBackupManager] iCloud恢复成功!")
                        // 通知DataStore重新加载数据
                        NotificationCenter.default.post(name: NSNotification.Name("CloudRestoreCompleted"), object: nil)
                        completion(true, nil)
                    }
                } catch {
                    let errorFormat = NSLocalizedString("cloudbackup.error.restoreFailedFormat", comment: "Restore failed format string. Param: error description")
                    let errorMessage = String(format: errorFormat, error.localizedDescription)
                    print("[CloudBackupManager] \(errorMessage)")
                    DispatchQueue.main.async { [weak self] in
                        self?.isRestoreInProgress = false
                        completion(false, errorMessage)
                    }
                }
            }
        }
    }

    // MARK: - 辅助方法

    // 检查iCloud是否可用
    private func isICloudAvailable() -> Bool {
        // 使用FileManager检查
        let fileManagerStatus = FileManager.default.checkICloudAvailability()
        if fileManagerStatus.exists {
            print("[CloudBackupManager] isICloudAvailable: iCloud可用")
            return true
        }

        print("[CloudBackupManager] isICloudAvailable: iCloud不可用")
        return false
    }

    // MARK: - 私有方法

    // 确保所有必需的目录存在
    private func ensureDirectoriesExist() throws {
        let fileManager = FileManager.default

        // 确保文档目录存在
        let audioDirectory = documentsDirectory.appendingPathComponent("AudioRecordings")
        let imagesDirectory = documentsDirectory.appendingPathComponent("Images")

        // 创建必要的目录
        if !fileManager.fileExists(atPath: audioDirectory.path) {
            try fileManager.createDirectory(at: audioDirectory, withIntermediateDirectories: true)
            print("[CloudBackupManager] 创建音频目录: \(audioDirectory.path)")
        }

        if !fileManager.fileExists(atPath: imagesDirectory.path) {
            try fileManager.createDirectory(at: imagesDirectory, withIntermediateDirectories: true)
            print("[CloudBackupManager] 创建图片目录: \(imagesDirectory.path)")
        }
    }

    // 获取文件URL
    private func getFileURL(for fileName: String) -> URL {
        return documentsDirectory.appendingPathComponent(fileName)
    }

    // 备份所有DataStore文件
    private func backupAllDataStoreFiles() throws {
        let fileManager = FileManager.default

        // 创建需要备份的文件列表
        let fileNames = [
            FileNames.habits,
            FileNames.triggers,
            FileNames.resets,
            FileNames.reviews,
            FileNames.habitReports,
            FileNames.audioRecordings,
            FileNames.xMomentSessions,
            FileNames.xMomentUserStats
        ]

        // 记录成功备份的文件数量
        var backupCount = 0

        // 备份每个文件
        for fileName in fileNames {
            let fileURL = getFileURL(for: fileName)
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    try backupFile(at: fileURL, withKey: fileName)
                    backupCount += 1
                } catch {
                    print("[CloudBackupManager] 备份文件 \(fileName) 失败: \(error.localizedDescription)")
                    // 继续备份其他文件，而不是立即失败
                }
            } else {
                print("[CloudBackupManager] 跳过不存在的文件: \(fileName)")
            }
        }

        print("[CloudBackupManager] 成功备份 \(backupCount)/\(fileNames.count) 个文件")

        // 只有在没有任何文件成功备份时才抛出错误
        if backupCount == 0 {
            throw NSError(domain: "CloudBackupManager", code: 100, userInfo: [NSLocalizedDescriptionKey: NSLocalizedString("cloudbackup.error.backupFailedNoFiles", comment: "Backup failed, no files")])
        }
    }

    // 备份单个文件
    private func backupFile(at url: URL, withKey key: String) throws {
        // 使用CloudKit实现备份
        // 这里需要实现CloudKit相关代码
        print("[CloudBackupManager] 已备份文件: \(key)")
    }

    // 恢复所有DataStore文件
    private func restoreAllDataStoreFiles() throws {
        // 获取要恢复的文件名和对应的URL
        let _ = [
            (FileNames.habits, getFileURL(for: FileNames.habits)),
            (FileNames.triggers, getFileURL(for: FileNames.triggers)),
            (FileNames.resets, getFileURL(for: FileNames.resets)),
            (FileNames.reviews, getFileURL(for: FileNames.reviews)),
            (FileNames.habitReports, getFileURL(for: FileNames.habitReports)),
            (FileNames.audioRecordings, getFileURL(for: FileNames.audioRecordings)),
            (FileNames.xMomentSessions, getFileURL(for: FileNames.xMomentSessions)),
            (FileNames.xMomentUserStats, getFileURL(for: FileNames.xMomentUserStats))
        ]

        // 记录成功恢复的文件数量
        let restoreCount = 0

        // 这里需要实现CloudKit相关恢复代码
        print("[CloudBackupManager] 使用CloudKit恢复文件")

        // 只有在没有任何文件成功恢复时才抛出错误
        if restoreCount == 0 {
            throw NSError(domain: "CloudBackupManager", code: 101, userInfo: [NSLocalizedDescriptionKey: NSLocalizedString("cloudbackup.error.restoreFailedNoFiles", comment: "Restore failed, no files")])
        }
    }

    // 额外检查iCloud连接
    private func verifyICloudConnection() -> Bool {
        // 检查CloudKit连接
        // 这里需要实现CloudKit连接检查代码
        return isICloudAvailable()
    }
}