import SwiftUI
import ObjectiveC
import CloudKit

public struct CloudBackupView: View {
    // 仅使用CloudKit管理器
    @ObservedObject private var cloudKitManager = CloudKitManager.shared
    
    @State private var showingBackupConfirmation = false
    @State private var showingRestoreConfirmation = false
    @State private var showingRestoreSuccess = false
    @State private var showingRestoreFailure = false
    @State private var showingBackupSuccess = false
    @State private var showingSizeWarning = false
    @State private var errorMessage: String = ""
    @State private var lastBackupTimeString: String = ""
    @State private var progressValue: Double = 0
    @State private var statusMessage: String = ""
    
    public var body: some View {
        List {
            Section {
                Toggle(isOn: Binding(
                    get: { true },  // 始终启用
                    set: { _ in }   // 不允许修改
                )) {
                    Text(NSLocalizedString("cloudbackup.view.enableLabel", comment: "Enable iCloud Backup Toggle Label"))
                }
                .disabled(true)     // 禁用切换
                
                HStack {
                    Text(NSLocalizedString("cloudbackup.view.lastBackupLabel", comment: "Last Backup Label"))
                    Spacer()
                    Text(lastBackupTimeString)
                        .foregroundColor(.secondary)
                }
                
                if !statusMessage.isEmpty {
                    HStack {
                        Text(NSLocalizedString("cloudbackup.view.statusLabel", comment: "Status Label"))
                        Spacer()
                        Text(statusMessage)
                            .foregroundColor(.secondary)
                    }
                }
                
                if cloudKitManager.isOperationInProgress {
                    ProgressView(value: progressValue, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle())
                        .padding(.vertical, 5)
                }
                
                Button(action: {
                    showingBackupConfirmation = true
                }) {
                    HStack {
                        Text(NSLocalizedString("cloudbackup.button.backupNow", comment: "Backup Now Button"))
                        Spacer()
                        if cloudKitManager.isOperationInProgress {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        } else {
                            Image(systemName: "arrow.up.to.line")
                        }
                    }
                }
                .disabled(cloudKitManager.isOperationInProgress)
                
                Button(action: {
                    showingRestoreConfirmation = true
                }) {
                    HStack {
                        Text(NSLocalizedString("cloudbackup.button.restore", comment: "Restore Button"))
                        Spacer()
                        if cloudKitManager.isOperationInProgress {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        } else {
                            Image(systemName: "arrow.down.to.line")
                        }
                    }
                }
                .disabled(cloudKitManager.isOperationInProgress || (!cloudKitManager.backupExists && cloudKitManager.lastBackupDate == nil))
            }
            
            Section {
                VStack(alignment: .leading, spacing: 10) {
                    Text(NSLocalizedString("cloudbackup.section.aboutTitle", comment: "About iCloud Backup Section Title"))
                        .font(.headline)
                    
                    Text(NSLocalizedString("cloudbackup.section.aboutDesc1", comment: "About iCloud Backup Description Line 1"))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text(NSLocalizedString("cloudbackup.section.aboutDesc2", comment: "About iCloud Backup Description Line 2"))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text(NSLocalizedString("cloudbackup.section.aboutDesc3", comment: "About iCloud Backup Description Line 3"))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 5)
            }
        }
        .navigationTitle(NSLocalizedString("cloudbackup.view.title", comment: "iCloud Backup View Title"))
        .onAppear {
            // 检查CloudKit可用性
            checkCloudKitAvailability()
            // 刷新上次备份时间
            updateLastBackupTime()
            // 设置进度回调
            setupProgressHandler()
        }
        .alert(NSLocalizedString("cloudbackup.alert.backupConfirmTitle", comment: "Backup confirmation alert title"), isPresented: $showingBackupConfirmation) {
            Button(NSLocalizedString("cloudbackup.alert.cancelButton", comment: "Cancel button"), role: .cancel) { }
            Button(NSLocalizedString("cloudbackup.alert.backupButton", comment: "Backup button")) {
                performBackup()
            }
        } message: {
            Text(NSLocalizedString("cloudbackup.alert.backupConfirmMessage", comment: "Backup confirmation alert message"))
        }
        .alert(NSLocalizedString("cloudbackup.alert.restoreConfirmTitle", comment: "Restore confirmation alert title"), isPresented: $showingRestoreConfirmation) {
            Button(NSLocalizedString("cloudbackup.alert.cancelButton", comment: "Cancel button"), role: .cancel) { }
            Button(NSLocalizedString("cloudbackup.alert.restoreButton", comment: "Restore button"), role: .destructive) {
                performRestore()
            }
        } message: {
            Text(NSLocalizedString("cloudbackup.alert.restoreConfirmMessage", comment: "Restore confirmation alert message"))
        }
        .alert(NSLocalizedString("cloudbackup.alert.restoreSuccessTitle", comment: "Restore success alert title"), isPresented: $showingRestoreSuccess) {
            Button(NSLocalizedString("cloudbackup.alert.okButton", comment: "OK button"), role: .cancel) { }
        } message: {
            Text(NSLocalizedString("cloudbackup.alert.restoreSuccessMessage", comment: "Restore success alert message"))
        }
        .alert(NSLocalizedString("cloudbackup.alert.restoreFailureTitle", comment: "Restore failure alert title"), isPresented: $showingRestoreFailure) {
            Button(NSLocalizedString("cloudbackup.alert.okButton", comment: "OK button"), role: .cancel) { }
        } message: {
            Text(errorMessage.isEmpty ? NSLocalizedString("cloudbackup.alert.restoreFailureDefaultMessage", comment: "Default restore failure message") : errorMessage)
        }
        .alert(NSLocalizedString("cloudbackup.alert.backupSuccessTitle", comment: "Backup success alert title"), isPresented: $showingBackupSuccess) {
            Button(NSLocalizedString("cloudbackup.alert.okButton", comment: "OK button"), role: .cancel) { 
                // 更新上次备份时间显示
                updateLastBackupTime()
            }
        } message: {
            Text(NSLocalizedString("cloudbackup.alert.backupSuccessMessage", comment: "Backup success alert message"))
        }
        .alert(NSLocalizedString("cloudbackup.alert.sizeWarningTitle", comment: "Size warning alert title"), isPresented: $showingSizeWarning) {
            Button(NSLocalizedString("cloudbackup.alert.okButton", comment: "OK button"), role: .cancel) { }
        } message: {
            Text(NSLocalizedString("cloudbackup.alert.sizeWarningMessage", comment: "Size warning alert message"))
        }
    }
    
    // 设置进度回调
    private func setupProgressHandler() {
        cloudKitManager.progressHandler = { progress in
            DispatchQueue.main.async {
                self.progressValue = progress
                
                // 根据进度设置状态消息
                if progress < 0.33 {
                    self.statusMessage = NSLocalizedString("cloudbackup.status.processingJson", comment: "Processing JSON status")
                } else if progress < 0.66 {
                    self.statusMessage = NSLocalizedString("cloudbackup.status.processingMedia", comment: "Processing media status")
                } else {
                    self.statusMessage = NSLocalizedString("cloudbackup.status.processingSettings", comment: "Processing settings status")
                }
            }
        }
    }
    
    // 检查CloudKit可用性
    private func checkCloudKitAvailability() {
        cloudKitManager.checkCloudKitAvailability { available, _ in
            DispatchQueue.main.async {
                if !available {
                    self.errorMessage = NSLocalizedString("cloudbackup.error.checkUnavailable", comment: "iCloud unavailable check error")
                    self.showingRestoreFailure = true
                }
                print("[CloudBackupView] CloudKit可用性: \(available)")
            }
        }
    }
    
    // 日期格式化
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }
    
    // 刷新上次备份时间
    private func updateLastBackupTime() {
        let date = cloudKitManager.lastBackupDate
        
        DispatchQueue.main.async {
            if let backupDate = date {
                self.lastBackupTimeString = self.dateFormatter.string(from: backupDate)
                print("[CloudBackupView] 上次备份时间更新为: \(self.lastBackupTimeString)")
            } else {
                // 如果有备份存在但没有日期，使用一个通用消息
                if cloudKitManager.backupExists {
                    self.lastBackupTimeString = NSLocalizedString("cloudbackup.view.backupAvailable", comment: "Backup available status")
                    print("[CloudBackupView] 无备份日期但有备份存在")
                } else {
                    self.lastBackupTimeString = NSLocalizedString("cloudbackup.view.neverBackedUp", comment: "Never backed up status")
                    print("[CloudBackupView] 无备份信息")
                }
            }
        }
    }
    
    // 执行备份
    private func performBackup() {
        // 使用CloudKit执行备份（包括媒体文件）
        if cloudKitManager.isOperationInProgress {
            return
        }
        
        // 重置进度和状态
        self.progressValue = 0
        self.statusMessage = NSLocalizedString("cloudbackup.status.preparingBackup", comment: "Preparing backup status")
        
        cloudKitManager.performFullBackup { success, error in
            DispatchQueue.main.async {
                // 清除状态消息
                self.statusMessage = ""
                
                if success {
                    self.updateLastBackupTime()
                    self.showingBackupSuccess = true
                } else {
                    self.errorMessage = error ?? NSLocalizedString("cloudbackup.error.backupFailedDefault", comment: "Default backup failure message")
                    self.showingRestoreFailure = true
                }
            }
        }
    }
    
    // 执行恢复
    private func performRestore() {
        // 使用CloudKit恢复
        if cloudKitManager.isOperationInProgress {
            return
        }
        
        // 重置进度和状态
        self.progressValue = 0
        self.statusMessage = NSLocalizedString("cloudbackup.status.preparingRestore", comment: "Preparing restore status")
        
        cloudKitManager.performFullRestore { success, error in
            DispatchQueue.main.async {
                // 清除状态消息
                self.statusMessage = ""
                
                if success {
                    self.showingRestoreSuccess = true
                } else {
                    self.errorMessage = error ?? NSLocalizedString("cloudbackup.error.restoreFailedDefault", comment: "Default restore failure message")
                    self.showingRestoreFailure = true
                }
            }
        }
    }
}

extension FileManager {
    func checkICloudAvailability() -> (exists: Bool, token: Any?) {
        // 不使用runtime黑魔法，改用更安全的方法
        var isAvailable = false
        var iCloudToken: Any? = nil
        
        // 尝试通过文件url来检测iCloud是否可用
        if let ubiquityURL = FileManager.default.url(forUbiquityContainerIdentifier: nil) {
            print("[iCloud检测] 找到iCloud容器URL: \(ubiquityURL)")
            isAvailable = true
        }
        
        // 使用原生方法获取token作为备用检测手段
        if let originalToken = FileManager.default.value(forKey: "ubiquityIdentityToken") {
            print("[iCloud检测] 获取到ubiquityIdentityToken")
            iCloudToken = originalToken
            isAvailable = true
        }
        
        // 更新结果
        if isAvailable {
            print("[iCloud检测] iCloud可用")
        } else {
            print("[iCloud检测] iCloud不可用")
        }
        
        return (isAvailable, iCloudToken)
    }
} 