---
description: 
globs: 
alwaysApply: true
---
memory_bank_strategy:
  initialization: |
      <thinking>
      - **CHECK FOR MEMORY BANK:**
      </thinking>
          <thinking>
        * First, check if the memory-bank/ directory exists.
          </thinking>
          <thinking>
        * If memory-bank DOES exist, skip immediately to `if_memory_bank_exists`.
          </thinking>
  if_no_memory_bank: |
      1. **Inform the User:**  
          "No Memory Bank was found. I recommend creating one to  maintain project context. Would you like to switch to Architect mode to do this?"
      2. **Conditional Actions:**
         * If the user declines:
          <thinking>
          I need to proceed with the task without Memory Bank functionality.
          </thinking>
          a. Inform the user that the Memory Bank will not be created.
          b. Set the status to '[MEMORY BANK: INACTIVE]'.
          c. Proceed with the task using the current context if needed or if no task is provided, use the `ask_followup_question` tool.
         * If the user agrees:
          Switch to Architect mode to create the Memory Bank.
  if_memory_bank_exists: |
        **READ *ALL* MEMORY BANK FILES**
        <thinking>
        I will read all memory bank files, one at a time.
        </thinking>
        Plan: Read all mandatory files sequentially.
        1. Read `productContext.md`
        2. Read `activeContext.md` 
        3. Read `systemPatterns.md` 
        4. Read `decisionLog.md` 
        5. Read `progress.md` 
        6. Set status to [MEMORY BANK: ACTIVE] and inform user.
        7. Proceed with the task using the context from the Memory Bank or if no task is provided, use the `ask_followup_question` tool.
      
general:
  status_prefix: "Begin EVERY response with either '[MEMORY BANK: ACTIVE]' or '[MEMORY BANK: INACTIVE]', according to the current state of the Memory Bank."

memory_bank_updates:
  frequency:
  - "UPDATE MEMORY BANK THROUGHOUT THE CHAT SESSION, WHEN SIGNIFICANT CHANGES OCCUR IN THE PROJECT."
  decisionLog.md:
    trigger: "When a significant architectural decision is made (new component, data flow change, technology choice, etc.). Use your judgment to determine significance."
    action: |
      <thinking>
      I need to update decisionLog.md with a decision, the rationale, and any implications. 
      </thinking>
      Use append_to_file to *append* new information. Never overwrite existing entries. Always include a timestamp.  
    format: |
      "[YYYY-MM-DD HH:MM:SS] - [Summary of Change/Focus/Issue]"
  productContext.md:
    trigger: "When the high-level project description, goals, features, or overall architecture changes significantly. Use your judgment to determine significance."
    action: |
      <thinking>
      A fundamental change has occurred which warrants an update to productContext.md.
      </thinking>
      Use append_to_file to *append* new information or use apply_diff to modify existing entries if necessary. Timestamp and summary of change will be appended as footnotes to the end of the file.
    format: "[YYYY-MM-DD HH:MM:SS] - [Summary of Change]"
  systemPatterns.md:
    trigger: "When new architectural patterns are introduced or existing ones are modified. Use your judgement."
    action: |
      <thinking>
      I need to update systemPatterns.md with a brief summary and time stamp.
      </thinking>
      Use append_to_file to *append* new patterns or use apply_diff to modify existing entries if warranted. Always include a timestamp.
    format: "[YYYY-MM-DD HH:MM:SS] - [Description of Pattern/Change]"
  activeContext.md:
    trigger: "When the current focus of work changes, or when significant progress is made. Use your judgement."
    action: |
      <thinking>
      I need to update activeContext.md with a brief summary and time stamp.
      </thinking>
      Use append_to_file to *append* to the relevant section (Current Focus, Recent Changes, Open Questions/Issues) or use apply_diff to modify existing entries if warranted.  Always include a timestamp.
    format: "[YYYY-MM-DD HH:MM:SS] - [Summary of Change/Focus/Issue]"
  progress.md:
      trigger: "When a task begins, is completed, or if there are any changes Use your judgement."
      action: |
        <thinking>
        I need to update progress.md with a brief summary and time stamp.
        </thinking>
        Use append_to_file to *append* the new entry, never overwrite existing entries. Always include a timestamp.
      format: "[YYYY-MM-DD HH:MM:SS] - [Summary of Change/Focus/Issue]"

umb:
  trigger: "^(Update Memory Bank|UMB)$"
  instructions:
    - "Halt Current Task: Stop current activity"
    - "Acknowledge Command: '[MEMORY BANK: UPDATING]'"
    - "Review Chat History"
  core_update_process: |
      1. Current Session Review:
          - Analyze complete chat history
          - Extract cross-mode information
          - Track mode transitions
          - Map activity relationships
      2. Comprehensive Updates:
          - Update from all mode perspectives
          - Preserve context across modes
          - Maintain activity threads
          - Document mode interactions
      3. Memory Bank Synchronization:
          - Update all affected *.md files
          - Ensure cross-mode consistency
          - Preserve activity context
          - Document continuation points
  task_focus: "During a UMB update, focus on capturing any clarifications, questions answered, or context provided *during the chat session*. This information should be added to the appropriate Memory Bank files (likely `activeContext.md` or `decisionLog.md`), using the other modes' update formats as a guide.  *Do not* attempt to summarize the entire project or perform actions outside the scope of the current chat."
  cross-mode_updates: "During a UMB update, ensure that all relevant information from the chat session is captured and added to the Memory Bank. This includes any clarifications, questions answered, or context provided during the chat. Use the other modes' update formats as a guide for adding this information to the appropriate Memory Bank files."
  post_umb_actions:
    - "Memory Bank fully synchronized"
    - "All mode contexts preserved"
    - "Session can be safely closed"
    - "Next assistant will have complete context"
  override_file_restrictions: true
  override_mode_restrictions: true