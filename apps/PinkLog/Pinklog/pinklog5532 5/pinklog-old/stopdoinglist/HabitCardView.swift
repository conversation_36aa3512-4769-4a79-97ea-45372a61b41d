import SwiftUI
import StopDoingListKit

struct HabitCardView: View {
    let habit: Habit
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和图标
            HStack {
                Text(habit.name)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 分类图标
                Image(systemName: getCategoryIcon())
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .padding(6)
                    .background(
                        Circle()
                            .fill(Color(UIColor.systemGray5))
                    )
            }
            
            // 描述
            if let description = habit.description, !description.isEmpty {
                Text(description)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            // 目标天数
            HStack {
                Text(String(format: NSLocalizedString("goal_days_format", comment: ""), habit.goal))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                
                Spacer()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(colorScheme == .dark ? 
                      Color(UIColor.systemBackground) : 
                      Color.white)
        )
        // 非常明显的描边
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    Color.blue, // 使用明显的蓝色描边
                    lineWidth: 2.5
                )
        )
        // 不需要阴影效果
        .frame(height: 130)
        // 添加垂直间距，增强浮动感
        .padding(.vertical, 8)
        .padding(.horizontal, 4)
    }
    
    // 获取分类图标
    private func getCategoryIcon() -> String {
        let category = habit.tags.first ?? NSLocalizedString("default", comment: "")
        switch category {
        case NSLocalizedString("health", comment: ""): return "heart.fill"
        case NSLocalizedString("work", comment: ""): return "briefcase.fill"
        case NSLocalizedString("relationships", comment: ""): return "person.2.fill"
        case NSLocalizedString("finance", comment: ""): return "dollarsign.circle.fill"
        case NSLocalizedString("digital_habits", comment: ""): return "laptopcomputer"
        default: return "square.grid.2x2.fill"
        }
    }
} 