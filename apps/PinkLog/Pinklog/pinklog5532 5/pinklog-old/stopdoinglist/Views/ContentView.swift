struct ContentView: View {
    @StateObject var dataStore = DataStore.shared
    @State private var showAddReview = false
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HabitListView(showingAddHabit: $showingAddHabit)
                .tabItem {
                    Label("X Do", systemImage: "list.bullet")
                }
                .tag(0)
            
            AllReviewsView(dataStore: dataStore)
                .tabItem {
                    Label("回顾", systemImage: "text.book.closed")
                }
                .tag(1)
            
            // ... 其他标签页 ...
        }
        // ... 其他修饰符 ...
    }
} 