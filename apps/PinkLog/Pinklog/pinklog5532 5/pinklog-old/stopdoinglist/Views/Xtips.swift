import Foundation
import SwiftUI
import Combine
import StopDoingListKit


// MARK: - 里程碑类型
enum MilestoneCategory: String, Codable, CaseIterable {
    case quantity = "milestone_category_quantity"
    case consistency = "milestone_category_consistency"
    case habitFormation = "milestone_category_habit_formation" // 占位，需要更复杂逻辑
    case deepWork = "milestone_category_deep_work"
    case specialMoment = "milestone_category_special_moment"

    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "Milestone Category Name")
    }
}

// MARK: - 里程碑定义
struct Milestone: Identifiable, Codable, Hashable {
    let id: String // 使用 String 作为 ID，便于存储和查找
    let name: String
    let description: String
    let category: MilestoneCategory
    let iconName: String? // SF Symbol name
    let condition: (StopDoingListKit.DataStore) -> Bool // 明确指定DataStore类型

    // 为了 Codable 和 Hashable，condition 不能直接编码，需要特殊处理
    // 这里我们只比较 id
    static func == (lhs: Milestone, rhs: Milestone) -> Bool {
        lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    // 注意：condition 闭包无法直接 Codable。
    // 在实际应用中，如果需要持久化 Milestone 定义本身，
    // 可能需要将条件表示为可编码的规则（如字符串或字典），并在运行时解析。
    // 目前我们只持久化达成的里程碑 ID。
    enum CodingKeys: String, CodingKey {
        case id, name, description, category, iconName
    }

     // 手动实现 Codable 以跳过 condition
    init(id: String, name: String, description: String, category: MilestoneCategory, iconName: String? = nil, condition: @escaping (StopDoingListKit.DataStore) -> Bool) {
        self.id = id
        self.name = name
        self.description = description
        self.category = category
        self.iconName = iconName
        self.condition = condition
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        category = try container.decode(MilestoneCategory.self, forKey: .category)
        iconName = try container.decodeIfPresent(String.self, forKey: .iconName)
        // condition 在解码时设为 false，因为无法解码闭包
        condition = { _ in false }
        print("警告：Milestone condition 在解码时被忽略。")
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(description, forKey: .description)
        try container.encode(category, forKey: .category)
        try container.encodeIfPresent(iconName, forKey: .iconName)
        // 不编码 condition
    }

    var localizedName: String {
        NSLocalizedString(name, comment: "Milestone Name")
    }
    var localizedDescription: String {
        NSLocalizedString(description, comment: "Milestone Description")
    }
}

// MARK: - 引导提示上下文
enum GuidanceContext {
    case appLaunch
    case returnToHome // 从其他页面返回 HabitListView
    case afterSaveSuccess(achievedMilestone: Milestone?) // 成功保存 X Moment 后
    case idle // 默认或空闲状态
}

// MARK: - Tips 管理器
class TipsManager: ObservableObject {
    @Published var currentTip: String? = nil // 当前显示的 Tip 文本
    @Published var lastAchievedMilestone: Milestone? = nil // 用于触发庆祝效果

    private var allMilestones: [Milestone] = []
    private var achievedMilestoneIDs: Set<String> = []
    private var didDefineMilestones = false // 添加标记位
    private let defineQueue = DispatchQueue(label: "com.xdolist.milestoneDefineQueue") // 用于确保线程安全定义

    // 包装有歧义的方法调用
    private func getXMomentSessionsUnambiguous() -> [XMomentSession] {
        // 直接访问文件系统来获取会话数据
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentSessionsURL = documentsDirectory.appendingPathComponent("xmoment_sessions.json")

        do {
            if fileManager.fileExists(atPath: xMomentSessionsURL.path) {
                let data = try Data(contentsOf: xMomentSessionsURL)
                return try JSONDecoder().decode([XMomentSession].self, from: data)
            }
        } catch {
            print("加载 X Moment 会话数据失败: \(error)")
        }
        return []
    }

    private func getXMomentUserStatsUnambiguous() -> XMomentUserStats {
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentUserStatsURL = documentsDirectory.appendingPathComponent("xmoment_user_stats.json")

        do {
            if fileManager.fileExists(atPath: xMomentUserStatsURL.path) {
                let data = try Data(contentsOf: xMomentUserStatsURL)
                return try JSONDecoder().decode(XMomentUserStats.self, from: data)
            }
        } catch {
            print("加载 X Moment 用户统计数据失败: \(error)")
        }
        return XMomentUserStats()
    }

    private func getTotalXMomentCount() -> Int {
        let stats = getXMomentUserStatsUnambiguous()
        return stats.totalCompletedCount + stats.totalInterruptedCount
    }

    private func getTotalXUnits() -> Double {
        return getXMomentSessionsUnambiguous().reduce(0) { $0 + ($1.progressXU) }
    }

    private func getXMomentStreakDays() -> Int {
        return getXMomentUserStatsUnambiguous().currentStreak
    }

    private func updateAchievedMilestones(_ milestoneIDs: Set<String>) {
        var stats = getXMomentUserStatsUnambiguous()
        stats.achievedMilestoneIDs = milestoneIDs

        // 保存更新后的用户统计数据
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let xMomentUserStatsURL = documentsDirectory.appendingPathComponent("xmoment_user_stats.json")

        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(stats)
            try data.write(to: xMomentUserStatsURL)
        } catch {
            print("保存 X Moment 用户统计数据失败: \(error)")
        }
    }

    private func getDailyTip() -> String {
        let tips = [
            "Tip 1",
            "Tip 2",
            "Tip 3",
            "Tip 4",
            "Tip 5"
        ]
        let randomIndex = Int.random(in: 0..<tips.count)
        return tips[randomIndex]
    }

    init() {
        // Milestone definition moved to lazy loading
    }

    // --- 里程碑定义 ---
    private func defineMilestones() {
        // 在专门的队列上执行，防止重复定义和确保线程安全
        defineQueue.sync { // 使用 sync 确保调用者等待定义完成
            guard !didDefineMilestones else { return } // 检查标记位

            allMilestones = [
                // --- 数量 (Quantity/Volume) ---
                Milestone(id: "q_count_10", name: "milestone_name_q_count_10", description: "milestone_desc_q_count_10", category: .quantity, iconName: "10.circle", condition: { $0.getTotalXMomentCount() >= 10 }),
                Milestone(id: "q_count_50", name: "milestone_name_q_count_50", description: "milestone_desc_q_count_50", category: .quantity, iconName: "50.circle", condition: { $0.getTotalXMomentCount() >= 50 }),
                Milestone(id: "q_count_100", name: "milestone_name_q_count_100", description: "milestone_desc_q_count_100", category: .quantity, iconName: "100.circle", condition: { $0.getTotalXMomentCount() >= 100 }),
                Milestone(id: "q_xu_100", name: "milestone_name_q_xu_100", description: "milestone_desc_q_xu_100", category: .quantity, iconName: "target", condition: { $0.getTotalXUnits() >= 100 }),
                Milestone(id: "q_xu_500", name: "milestone_name_q_xu_500", description: "milestone_desc_q_xu_500", category: .quantity, iconName: "target", condition: { $0.getTotalXUnits() >= 500 }),
                Milestone(id: "q_duration_10h", name: "milestone_name_q_duration_10h", description: "milestone_desc_q_duration_10h", category: .quantity, iconName: "clock", condition: { $0.getTotalXMomentDuration() >= 36000 }),
                Milestone(id: "q_duration_50h", name: "milestone_name_q_duration_50h", description: "milestone_desc_q_duration_50h", category: .quantity, iconName: "clock.fill", condition: { $0.getTotalXMomentDuration() >= 180000 }),

                // --- 连续性 (Consistency/Streaks) ---
                Milestone(id: "c_streak_3", name: "milestone_name_c_streak_3", description: "milestone_desc_c_streak_3", category: .consistency, iconName: "calendar.badge.clock", condition: { $0.getXMomentStreakDays() >= 3 }),
                Milestone(id: "c_streak_7", name: "milestone_name_c_streak_7", description: "milestone_desc_c_streak_7", category: .consistency, iconName: "calendar", condition: { $0.getXMomentStreakDays() >= 7 }),
                Milestone(id: "c_streak_14", name: "milestone_name_c_streak_14", description: "milestone_desc_c_streak_14", category: .consistency, iconName: "calendar", condition: { $0.getXMomentStreakDays() >= 14 }),
                Milestone(id: "c_streak_30", name: "milestone_name_c_streak_30", description: "milestone_desc_c_streak_30", category: .consistency, iconName: "calendar", condition: { $0.getXMomentStreakDays() >= 30 }),

                // --- 深度专注 (Deep Work) ---
                Milestone(id: "d_duration_record", name: "milestone_name_d_duration_record", description: "milestone_desc_d_duration_record", category: .deepWork, iconName: "timer", condition: { dataStore in
                    // 需要一个方法来获取本次会话时长并与历史最大时长比较
                    // 这个条件需要在保存会话 *之后* 检查，传入本次会话时长
                    return false // 暂时无法在全局检查
                }),
                Milestone(id: "d_overclock_1", name: "milestone_name_d_overclock_1", description: "milestone_desc_d_overclock_1", category: .deepWork, iconName: "bolt.fill", condition: { dataStore in
                    // 需要检查所有会话中是否存在 overclockFactor > 1.0 且 overclockDuration >= 300
                    let sessions = self.getXMomentSessionsUnambiguous()
                    return sessions.contains { $0.overclockFactor > 1.0 && $0.overclockDuration >= 300 }
                }),
                Milestone(id: "d_overclock_10", name: "milestone_name_d_overclock_10", description: "milestone_desc_d_overclock_10", category: .deepWork, iconName: "bolt.circle.fill", condition: { dataStore in
                    let sessions = self.getXMomentSessionsUnambiguous()
                    return sessions.filter { $0.overclockFactor > 1.0 && $0.overclockDuration >= 300 }.count >= 10
                }),

                // --- 特殊时刻 (Special Moment) ---
                Milestone(id: "s_anniversary_1y", name: "milestone_name_s_anniversary_1y", description: "milestone_desc_s_anniversary_1y", category: .specialMoment, iconName: "gift.fill", condition: { dataStore in
                    let sessions = self.getXMomentSessionsUnambiguous()
                    guard let firstSession = sessions.min(by: { $0.startTime < $1.startTime }) else { return false }
                    return Calendar.current.dateComponents([.year], from: firstSession.startTime, to: Date()).year ?? 0 >= 1
                }),
                Milestone(id: "s_newyear_focus", name: "milestone_name_s_newyear_focus", description: "milestone_desc_s_newyear_focus", category: .specialMoment, iconName: "sparkles", condition: { dataStore in
                    let today = Date()
                    let calendar = Calendar.current
                    let components = calendar.dateComponents([.month, .day], from: today)
                    let sessions = self.getXMomentSessionsUnambiguous()
                    return components.month == 1 && components.day == 1 &&
                    sessions.contains { calendar.isDate($0.startTime, inSameDayAs: today) }
                }),
                Milestone(id: "s_midnight_focus", name: "milestone_name_s_midnight_focus", description: "milestone_desc_s_midnight_focus", category: .specialMoment, iconName: "moon.stars.fill", condition: { dataStore in
                    let calendar = Calendar.current
                    let sessions = self.getXMomentSessionsUnambiguous()
                    return sessions.contains { session in
                        let hour = calendar.component(.hour, from: session.endTime) // 检查结束时间
                        return hour == 0
                    }
                }),
            ]
            didDefineMilestones = true // 设置标记位
        }
    }

    // 添加确保里程碑已定义的方法
    private func ensureMilestonesDefined() {
        if !didDefineMilestones {
            defineMilestones() // 调用同步定义方法
        }
    }

    // --- 里程碑检查与处理 ---
    func checkAndCelebrateMilestones(dataStore: StopDoingListKit.DataStore, sessionDuration: TimeInterval? = nil) {
        ensureMilestonesDefined() // 确保里程碑已定义

        // 从DataStore获取已达成的里程碑ID
        let stats = self.getXMomentUserStatsUnambiguous()
        achievedMilestoneIDs = stats.achievedMilestoneIDs

        var newlyAchieved: Milestone? = nil

        for milestone in allMilestones {
            // 如果尚未达成，并且条件满足
            if !achievedMilestoneIDs.contains(milestone.id) {
                var conditionMet = false
                if milestone.id == "d_duration_record" {
                    // 特殊处理：单次时长记录 - 自行实现逻辑，不依赖DataStore方法
                    if let currentDuration = sessionDuration {
                        // 获取历史最长时长，但排除当前时长
                        let sessions = self.getXMomentSessionsUnambiguous()
                        var maxDuration: TimeInterval = 0

                        for session in sessions {
                            // 如果这个会话与当前时长非常接近（允许微小误差），则跳过
                            if abs(session.duration - currentDuration) < 1.0 {
                                continue
                            }
                            if session.duration > maxDuration {
                                maxDuration = session.duration
                            }
                        }

                        // 检查当前时长是否大于历史最长
                        if currentDuration > maxDuration {
                            conditionMet = true
                        }
                    } else {
                        // 常规检查
                        conditionMet = milestone.condition(dataStore)
                    }


                    if conditionMet {
                        achievedMilestoneIDs.insert(milestone.id)
                        newlyAchieved = milestone // 只庆祝第一个新达成的
                        // 理论上可以一次达成多个，但只庆祝一个避免干扰
                        break
                    }
                }
            }

            // 将更新后的已达成里程碑保存到DataStore
            self.updateAchievedMilestones(achievedMilestoneIDs)

            if let achieved = newlyAchieved {
                lastAchievedMilestone = achieved
                updateTip(context: .afterSaveSuccess(achievedMilestone: achieved), dataStore: dataStore)
            } else {
                // 如果没有新里程碑，则显示保存后的常规提示
                lastAchievedMilestone = nil // 清除上次庆祝状态
                updateTip(context: .afterSaveSuccess(achievedMilestone: nil), dataStore: dataStore)
            }
        }

        // --- 引导提示生成 ---
        func updateTip(context: GuidanceContext, dataStore: StopDoingListKit.DataStore) {
            DispatchQueue.main.async { // 确保在主线程更新 @Published 属性
                // 如果是 afterSaveSuccess 且有里程碑，使用格式化字符串
                if case .afterSaveSuccess(let achievedMilestone) = context, let milestone = achievedMilestone {
                    // 使用本地化格式字符串和本地化名称/描述
                     self.currentTip = String(format: NSLocalizedString("milestone_achieved_format", comment: "Milestone achievement message format"),
                                           milestone.localizedName, milestone.localizedDescription)
                } else {
                    // 否则，生成常规本地化提示
                    let tipKeys = [
                        "tip_deep_dialogue",
                        "tip_less_do_more_be",
                        "tip_time_investment",
                        "tip_polish_focus",
                        "tip_feel_inner_power"
                        // 可以添加更多 daily tip keys
                    ]
                    let randomKey = tipKeys.randomElement() ?? "tip_deep_dialogue"
                    self.currentTip = NSLocalizedString(randomKey, comment: "General guidance tip")
                }
            }
        }

        func generateGuidancePrompt(context: GuidanceContext, dataStore: StopDoingListKit.DataStore) -> String? {
            let sessions = self.getXMomentSessionsUnambiguous()
            let calendar = Calendar.current
            let today = Date()

            switch context {
            case .appLaunch, .returnToHome:
                let todaySessions = sessions.filter { calendar.isDate($0.startTime, inSameDayAs: today) }
                let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
                let yesterdaySessions = sessions.filter { calendar.isDate($0.startTime, inSameDayAs: yesterday) }

                // 检查是否有接近的里程碑
                if let upcoming = checkForUpcomingMilestone(dataStore: dataStore) {
                    return upcoming
                }

                if todaySessions.isEmpty {
                    if yesterdaySessions.isEmpty {
                        // 多日无记录
                        let daysSinceLast = daysSinceLastSession(sessions: sessions, calendar: calendar, today: today)
                        if daysSinceLast > 2 {
                            return NSLocalizedString("guidance_paused_restart", comment: "Guidance for long inactivity")
                        } else {
                            return NSLocalizedString("guidance_start_today", comment: "Guidance for starting today")
                        }
                    } else {
                        // 昨日有记录，今日无
                        return NSLocalizedString("guidance_continue_from_yesterday", comment: "Guidance to continue from yesterday")
                    }
                } else {
                    // 今日已有记录
                    if todaySessions.count >= 3 {
                        let key = ["guidance_focused_relax", "guidance_rest_after_focus"].randomElement()!
                        return NSLocalizedString(key, comment: "Guidance after multiple sessions")
                    } else {
                        // 可以考虑根据上次专注时间给予不同提示，或显示默认 Tip
                        return getDefaultIdleTip(dataStore: dataStore) // 显示默认提示
                    }
                }

            case .afterSaveSuccess(let achievedMilestone):
                if let milestone = achievedMilestone {
                    // 使用本地化格式字符串和本地化名称/描述
                    return String(format: NSLocalizedString("milestone_achieved_format", comment: "Milestone achievement message format"),
                                  milestone.localizedName, milestone.localizedDescription)
                } else {
                    // 检查本次会话是否超频
                    if let lastSession = sessions.last, lastSession.overclockFactor > 1.0 && lastSession.overclockDuration >= 300 {
                        let bonusXU = lastSession.xUnits * (lastSession.overclockFactor - 1.0)
                        return String(format: NSLocalizedString("guidance_overclock_success_format", comment: "Overclock success message format"), String(format: "%.0f", bonusXU))
                    }
                    // 检查是否刚完成长时间专注
                    if let lastSession = sessions.last, lastSession.duration >= 3600 { // 超过1小时
                        return NSLocalizedString("guidance_long_focus_completed", comment: "Guidance after long focus session")
                    }
                    // 默认保存成功提示
                    let key = ["guidance_save_success_garden", "guidance_save_success_investment", "guidance_save_success_keep_going"].randomElement()!
                    return NSLocalizedString(key, comment: "Default save success message")
                }
            case .idle:
                // 空闲状态下，可以显示默认的鼓励性提示或即将到来的里程碑
                if let upcoming = checkForUpcomingMilestone(dataStore: dataStore) {
                    return upcoming
                }
                return getDefaultIdleTip(dataStore: dataStore)
            }
        }

        // 检查即将达成的里程碑
        func checkForUpcomingMilestone(dataStore: StopDoingListKit.DataStore) -> String? {
            ensureMilestonesDefined() // 确保里程碑已定义

            for milestone in allMilestones {
                if achievedMilestoneIDs.contains(milestone.id) { continue } // 跳过已达成的

                // 这里需要为每个里程碑类型定义 "接近" 的逻辑
                // 例如：次数达到目标的80%，连续天数差1-2天等
                // 为简化，我们只检查几个简单的
                switch milestone.id {
                case "q_count_10": if self.getTotalXMomentCount() >= 7 { return NSLocalizedString("upcoming_milestone_q_count_10", comment: "") }
                case "q_count_50": if self.getTotalXMomentCount() >= 40 { return NSLocalizedString("upcoming_milestone_q_count_50", comment: "") }
                case "q_xu_100": if self.getTotalXUnits() >= 80 { return NSLocalizedString("upcoming_milestone_q_xu_100", comment: "") }
                case "c_streak_7": if self.getXMomentStreakDays() == 6 { return NSLocalizedString("upcoming_milestone_c_streak_7", comment: "") }
                case "c_streak_14": if self.getXMomentStreakDays() >= 11 { return NSLocalizedString("upcoming_milestone_c_streak_14", comment: "") }
                default: break
                }
            }
            return nil
        }

        // 计算距离上次专注的天数
        func daysSinceLastSession(sessions: [XMomentSession], calendar: Calendar, today: Date) -> Int {
            guard let lastSession = sessions.max(by: { $0.startTime < $1.startTime }) else { return Int.max }
            return calendar.dateComponents([.day], from: lastSession.startTime, to: today).day ?? Int.max
        }

        // 获取默认的空闲提示
        func getDefaultIdleTip(dataStore: StopDoingListKit.DataStore) -> String? {
            let tipKeys = [
                "tip_deep_dialogue",
                "tip_less_do_more_be",
                "tip_time_investment",
                "tip_polish_focus",
                "tip_feel_inner_power"
                 // 可以添加更多 daily tip keys
            ]
             let randomKey = tipKeys.randomElement() ?? "tip_deep_dialogue"
             return NSLocalizedString(randomKey, comment: "Default idle tip")
        }

        // --- 持久化 ---
        // 已删除原有的loadAchievedMilestones和saveAchievedMilestones方法，改为使用DataStore中的方法
    }
}
