import SwiftUI

struct EnhancedAudioWaveformView: View {
    let samples: [Float]
    let color: Color
    let inactiveColor: Color
    let progress: Double
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 2) {
                ForEach(Array(samples.enumerated()), id: \.offset) { index, sample in
                    let height = CGFloat(sample) * geometry.size.height
                    let progressPoint = Double(index) / Double(samples.count)
                    
                    RoundedRectangle(cornerRadius: 1)
                        .fill(progressPoint <= progress ? color : inactiveColor)
                        .frame(height: max(height, 4))
                }
            }
        }
    }
} 