import SwiftUI

// Renamed to avoid conflicts with potential duplicate declarations elsewhere
struct XDoOverclockedDeepSpaceView: View {
    // State variables for animation timing and effects
    @State private var starSeed = Int.random(in: 0...1000) // Initialize with random seed
    @State private var nebulaPhase: Double = 0.0
    @State private var nebulaRotation: Double = 0.0
    
    // Transition control
    @State private var transitionProgress: CGFloat = 0.0
    
    // Animation phase - controlled externally
    var showFullEffect: Bool = false
    
    var body: some View {
        TimelineView(.animation(minimumInterval: 1.0 / 30.0)) { timeline in // Update 30 times per second
            Canvas { context, size in
                let time = timeline.date.timeIntervalSinceReferenceDate
                
                // 1. Draw Pure Black Background with transition
                // Blend from transparent to pure black based on transition progress
                context.fill(
                    Path(CGRect(origin: .zero, size: size)),
                    with: .color(Color.black.opacity(transitionProgress))
                )

                // 2. Draw Minimal Stars with transition
                if transitionProgress > 0.3 { // Start showing stars after background begins to appear
                    let starAlpha = calculateTransitionValue(
                        from: 0,
                        to: 1,
                        progress: (transitionProgress - 0.3) / 0.7 // Remap to 0-1 range
                    )
                    drawStars(context: context, size: size, time: time, alpha: starAlpha)
                }

                // 3. Draw Very Subtle Nebula Glow with transition
                if transitionProgress > 0.5 { // Start showing nebula after stars begin to appear
                    let nebulaAlpha = calculateTransitionValue(
                        from: 0, 
                        to: 1,
                        progress: (transitionProgress - 0.5) / 0.5 // Remap to 0-1 range
                    )
                    drawSubtleNebula(context: context, size: size, time: time, alpha: nebulaAlpha)
                }
            }
            // Use drawingGroup for Metal acceleration
            .drawingGroup()
            // Ensure the effect ignores safe areas to fill the screen
            .ignoresSafeArea()
            // 添加一个纯黑色背景层，确保在浅色模式下也是黑色
            .background(Color.black)
        }
        // 强制使用深色模式，确保视觉效果始终保持一致
        .colorScheme(.dark)
        .onChange(of: showFullEffect) { _, newValue in
            // When showFullEffect becomes true, animate the transition
            withAnimation(.easeInOut(duration: 1.8)) {
                transitionProgress = newValue ? 1.0 : 0.0
            }
        }
        // Initialize transition on appear
        .onAppear {
            // Start with hidden state
            transitionProgress = 0.0
            
            // After a very short delay, start the transition animation if showFullEffect is true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if showFullEffect {
                    withAnimation(.easeInOut(duration: 1.8)) {
                        transitionProgress = 1.0
                    }
                }
            }
            
            // Start animation for nebula effects
            withAnimation(.linear(duration: 120).repeatForever(autoreverses: false)) {
                nebulaPhase = .pi * 2
            }
            withAnimation(.linear(duration: 150).repeatForever(autoreverses: true)) {
                nebulaRotation = 15 // Reduced rotation range
            }
        }
    }

    // --- Helper Functions ---
    
    // Helper function to calculate transition values
    private func calculateTransitionValue(from: CGFloat, to: CGFloat, progress: CGFloat) -> CGFloat {
        // Ensure progress is in 0-1 range
        let clampedProgress = min(max(progress, 0), 1)
        // Use smooth step function for more elegant easing
        let smoothProgress = clampedProgress * clampedProgress * (3 - 2 * clampedProgress)
        // Linear interpolation between from and to values
        return from + (to - from) * smoothProgress
    }

    // Draw subtle nebula effect with alpha control
    private func drawSubtleNebula(context: GraphicsContext, size: CGSize, time: Double, alpha: CGFloat = 1.0) {
        let centerX = size.width / 2
        let centerY = size.height / 2
        
        // Base properties - larger radius for more spread out effect
        let baseRadius = max(size.width, size.height) * 0.7
        // Very subtle pulse
        let pulse = sin(time * 0.2 + nebulaPhase) * 0.05 + 0.95 
        let currentRadius = baseRadius * pulse

        // Extremely subtle colors - very low opacity, further modified by alpha
        let nebulaColor = Color(hex: "3A00B0").opacity(0.04 * Double(alpha)) // Much dimmer purple
        let edgeColor = Color.clear // Fade to pure black

        let gradient = Gradient(colors: [nebulaColor, edgeColor])

        context.fill(
            Path(ellipseIn: CGRect(
                x: centerX - currentRadius,
                y: centerY - currentRadius,
                width: currentRadius * 2,
                height: currentRadius * 2
            )),
            with: .radialGradient(
                gradient,
                center: CGPoint(x: centerX, y: centerY),
                startRadius: currentRadius * 0.2,
                endRadius: currentRadius
            )
        )
        
        // Very subtle secondary glow
        let secondaryRadius = currentRadius * 0.6
        let secondaryColor = Color(hex: "102040").opacity(0.03 * Double(alpha)) // Very dark blue, almost invisible
        let secondaryEdge = Color.clear
        let secondaryGradient = Gradient(colors: [secondaryColor, secondaryEdge])
        let angleOffset = nebulaRotation * .pi / 180.0
        let offsetX = cos(angleOffset) * baseRadius * 0.15
        let offsetY = sin(angleOffset) * baseRadius * 0.15

        context.fill(
            Path(ellipseIn: CGRect(
                x: centerX + offsetX - secondaryRadius,
                y: centerY + offsetY - secondaryRadius,
                width: secondaryRadius * 2,
                height: secondaryRadius * 2
            )),
            with: .radialGradient(
                secondaryGradient,
                center: CGPoint(x: centerX + offsetX, y: centerY + offsetY),
                startRadius: secondaryRadius * 0.1,
                endRadius: secondaryRadius
            )
        )
    }

    // Draw stars with alpha control
    private func drawStars(context: GraphicsContext, size: CGSize, time: Double, alpha: CGFloat = 1.0) {
        // Use a seeded random number generator for consistent star placement
        var rng = SeededRandomNumberGenerator(seed: UInt64(starSeed))
        let starCount = 120 // Significantly reduced star count (from 300)

        for _ in 0..<starCount {
            // Generate consistent position based on seed
            let x = CGFloat.random(in: 0...size.width, using: &rng)
            let y = CGFloat.random(in: 0...size.height, using: &rng)
            
            // Most stars should be very small
            let baseSize = CGFloat.random(in: 0.2...0.8, using: &rng)
            
            // Slower twinkle effect
            let twinkleSpeed = Double.random(in: 0.3...0.7, using: &rng) // Slower twinkling
            let twinklePhase = Double.random(in: 0...(2 * .pi), using: &rng)
            let twinkle = abs(sin(time * twinkleSpeed + twinklePhase))
            
            // Lower opacity range for all stars, modified by alpha parameter
            let baseOpacity = Double.random(in: 0.1...0.5, using: &rng) * Double(alpha) // Dimmer stars overall
            let currentOpacity = baseOpacity * (0.5 + twinkle * 0.5)
            
            // Color - mostly white, but very dim
            let hueValue = 0.6 + Double.random(in: -0.02...0.03, using: &rng)
            let clampedHue = min(max(hueValue, 0), 1)
            
            let color = Color(
                hue: clampedHue, 
                saturation: Double.random(in: 0.0...0.05, using: &rng), // Even lower saturation
                brightness: 1.0,
                opacity: currentOpacity
            )

            // Make star size also subtly twinkle
            let currentSize = baseSize * (0.8 + twinkle * 0.2)

            // Draw only the star, no glow effect for most stars
            context.fill(
                Path(ellipseIn: CGRect(x: x - currentSize / 2, y: y - currentSize / 2, width: currentSize, height: currentSize)),
                with: .color(color)
            )

            // Only add very subtle glow to a few stars (10% chance)
            if currentOpacity > 0.4 && currentSize > 0.6 && Double.random(in: 0...1, using: &rng) < 0.1 {
                let glowRadius = currentSize * 1.2 // Smaller glow
                let glowOpacity = (currentOpacity - 0.3) * 0.15 // Very faint glow
                context.fill(
                    Path(ellipseIn: CGRect(x: x - glowRadius / 2, y: y - glowRadius / 2, width: glowRadius, height: glowRadius)),
                    with: .color(color.opacity(glowOpacity))
                )
            }
        }
    }
}

// Helper for seeded random numbers (for consistent stars)
struct SeededRandomNumberGenerator: RandomNumberGenerator {
    private var state: UInt64
    init(seed: UInt64) {
        state = seed == 0 ? 1 : seed // Avoid zero seed
    }
    mutating func next() -> UInt64 {
        state &*= 0x3fffffffffffffff // Keep it positive
        state &+= 1
        var x = state
        x ^= x >> 12
        x ^= x << 25
        x ^= x >> 27
        return x &* 0x2545F4914F6CDD1D
    }
}

// Preview Provider
#Preview {
    ZStack {
        Color.gray.opacity(0.2) // Background to simulate UI before transition
        XDoOverclockedDeepSpaceView(showFullEffect: true)
    }
    // 在预览中也显示两种模式下的效果
    .preferredColorScheme(.light)
}

// 另一个预览，展示深色模式下的效果，确保两种模式都能正确显示
#Preview {
    XDoOverclockedDeepSpaceView(showFullEffect: true)
        .preferredColorScheme(.dark)
} 