import SwiftUI
import PhotosUI

public struct ImagePreviewView: View {
    let item: PhotosPickerItem
    let onImageLoaded: (UIImage) -> Void
    
    @State private var image: Image?
    @State private var isLoading = true
    @State private var loadError = false
    
    public var body: some View {
        Group {
            if let image = image {
                image
                    .resizable()
                    .scaledToFill()
            } else if isLoading {
                ProgressView()
                    .frame(minWidth: 80, minHeight: 80)
            } else if loadError {
                Image(systemName: "exclamationmark.triangle")
                    .resizable()
                    .scaledToFit()
                    .padding(20)
                    .foregroundColor(.orange)
            } else {
                Image(systemName: "photo")
                    .resizable()
                    .scaledToFit()
                    .padding(20)
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        // 避免重复加载
        guard image == nil && !loadError else { return }
        
        isLoading = true
        loadError = false
        
        Task {
            do {
                // 使用优先级较低的任务加载图片，避免阻塞UI
                let data = try await Task.detached(priority: .userInitiated) {
                    try await item.loadTransferable(type: Data.self)
                }.value
                
                if let data = data, let uiImage = UIImage(data: data) {
                    await MainActor.run {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            image = Image(uiImage: uiImage)
                        }
                        isLoading = false
                        // 通知外部图片已加载
                        onImageLoaded(uiImage)
                    }
                } else {
                    await MainActor.run {
                        loadError = true
                        isLoading = false
                        print("无法从数据创建图片")
                    }
                }
            } catch {
                await MainActor.run {
                    loadError = true
                    isLoading = false
                    print("加载图片失败: \(error.localizedDescription)")
                }
            }
        }
    }
} 