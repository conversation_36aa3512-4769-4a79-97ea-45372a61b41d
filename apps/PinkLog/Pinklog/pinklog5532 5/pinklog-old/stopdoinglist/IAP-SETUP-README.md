# X Do 应用内购买设置指南

本文档提供了在X Do应用中设置和测试应用内购买的详细步骤。

## 已实现功能

我们已经实现了以下内购功能：

1. **StoreManager**：负责处理所有购买相关操作的单例类
2. **功能限制逻辑**：
   - 免费版限制添加最多3个习惯
   - 免费版限制XMoment每天使用3次
   - 免费版无法使用iCloud备份功能
3. **购买提示界面**：当用户尝试使用高级功能时弹出
4. **本地StoreKit配置文件**：用于开发和测试

## 设置步骤

### 1. Xcode项目配置

1. 打开X Do的Xcode项目
2. 在项目导航器中，右键点击项目文件夹，选择"Add Files to..."
3. 找到并添加`StoreKitConfig.storekit`文件
4. 在Xcode编辑器中打开Scheme管理器（Product > Scheme > Edit Scheme）
5. 选择"Run"，然后选择"Options"标签
6. 在"StoreKit Configuration"下拉菜单中，选择`StoreKitConfig.storekit`文件
7. 点击"Close"保存更改

### 2. App Store Connect设置

1. 登录[App Store Connect](https://appstoreconnect.apple.com/)
2. 转到"我的应用"，选择X Do应用
3. 点击"功能"标签，然后选择"应用内购买项目"
4. 点击"+"按钮，选择"非消耗型"
5. 填写以下信息：
   - 参考名称：X Do 高级版
   - 产品ID：top.trysapp.xdo.premium
   - 价格：选择合适的价格等级
   - 本地化信息：添加中文和英文的显示名称和描述
6. 点击"提交"保存内购项目

### 3. 测试内购功能

#### 本地测试（使用StoreKit配置文件）

1. 确保已在Scheme中选择了StoreKit配置文件
2. 运行应用程序
3. 尝试添加超过3个习惯或使用XMoment超过3次
4. 应该会弹出购买提示
5. 点击"立即升级"按钮进行购买
6. 在测试环境中，购买会立即成功，无需实际付款

#### 沙盒测试（使用App Store Connect）

1. 在App Store Connect中创建一个沙盒测试账户
2. 在设备上，转到设置 > App Store，退出当前Apple ID
3. 运行应用程序，尝试进行购买
4. 系统会提示使用沙盒测试账户登录
5. 完成购买流程
6. 验证高级功能是否已解锁

## 故障排除

如果遇到问题，请检查：

1. StoreKit配置文件是否正确加载
2. 产品ID是否与App Store Connect中设置的一致
3. 网络连接是否正常
4. 应用的Bundle ID是否与App Store Connect中注册的一致
5. 沙盒测试账户是否正常工作

## 代码结构

- `StoreManager.swift`：处理所有内购相关操作的核心类
- `DataStore+Premium.swift`：DataStore的扩展，用于处理高级功能检查
- `PurchasePromptView.swift`：购买提示界面
- `StoreKitConfig.storekit`：本地测试配置文件

## 注意事项

- 确保在发布前完成所有测试
- 更新隐私政策，包含应用内购买相关信息
- 确保用户可以恢复购买
- 确保应用在没有网络连接的情况下仍能正确识别已购买状态
