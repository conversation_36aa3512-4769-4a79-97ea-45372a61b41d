//
//  stopdoinglistApp.swift
//  stopdoinglist
//
//  Created by thr33 on 2025/3/6.
//

import SwiftUI
import AVFoundation
import UIKit
import Foundation
import StopDoingListKit
import ObjectiveC
import UserNotifications
import Combine
import StoreKit

// MARK: - Migration Manager
class MigrationManager {
    static let shared = MigrationManager()

    func isMigrationNeeded() -> <PERSON><PERSON> {
        // 始终返回false，不需要迁移
        return false
    }
}

// MARK: - UI配置辅助工具
class UIConfigurationHelper {
    // 保存默认外观，以便在需要时恢复
    private static var defaultAppearance: UITabBarAppearance?

    // 配置TabBar外观，确保在所有情况下都有不透明背景
    static func configureTabBarAppearance() {
        // 创建不透明背景的外观
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()

        // 设置图标颜色
        let itemAppearance = UITabBarItemAppearance()
        itemAppearance.normal.iconColor = UIColor.systemGray
        itemAppearance.selected.iconColor = UIColor.systemBlue

        // 应用到TabBar外观
        appearance.stackedLayoutAppearance = itemAppearance

        // 移除顶部分隔线
        appearance.shadowColor = .clear
        appearance.shadowImage = UIImage()

        // 保存默认外观以便后续恢复
        defaultAppearance = appearance.copy()

        // 全局应用TabBar外观设置
        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            // 确保滚动边缘外观与标准外观相同，防止透明效果
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }

        // 确保没有分隔线显示
        UITabBar.appearance().clipsToBounds = true
    }

    // 根据xmoment状态和颜色模弎更新TabBar外观
    static func updateTabBarForXMoment(isActive: Bool, isLightMode: Bool) {
        DispatchQueue.main.async {
            // 获取主要TabBarController
            guard let mainTabBarController = getMainTabBarController() else { return }

            // 准备外观
            let appearance: UITabBarAppearance

            if isActive && isLightMode {
                // xmoment活动且为浅色模式：使用黑色背景
                appearance = UITabBarAppearance()
                appearance.configureWithOpaqueBackground()
                appearance.backgroundColor = UIColor.black

                // 设置图标颜色为白色，以便在黑色背景上更清晰
                let itemAppearance = UITabBarItemAppearance()
                itemAppearance.normal.iconColor = UIColor.lightGray
                itemAppearance.selected.iconColor = UIColor.white

                // 应用到TabBar外观
                appearance.stackedLayoutAppearance = itemAppearance

                // 移除顶部分隔线
                appearance.shadowColor = .clear
                appearance.shadowImage = UIImage()

                // 直接设置最终外观
                mainTabBarController.tabBar.standardAppearance = appearance
                if #available(iOS 15.0, *) {
                    mainTabBarController.tabBar.scrollEdgeAppearance = appearance
                }
                // 立即设置全局外观
                UITabBar.appearance().standardAppearance = appearance
                if #available(iOS 15.0, *) {
                    UITabBar.appearance().scrollEdgeAppearance = appearance
                }
            } else {
                // 恢复默认外观
                if let savedAppearance = defaultAppearance {
                    appearance = savedAppearance
                } else {
                    // 如果没有保存默认外观，则创建新的
                    appearance = UITabBarAppearance()
                    appearance.configureWithOpaqueBackground()

                    // 设置图标颜色
                    let itemAppearance = UITabBarItemAppearance()
                    itemAppearance.normal.iconColor = UIColor.systemGray
                    itemAppearance.selected.iconColor = UIColor.systemBlue

                    // 应用到TabBar外观
                    appearance.stackedLayoutAppearance = itemAppearance

                    // 移除顶部分隔线
                    appearance.shadowColor = .clear
                    appearance.shadowImage = UIImage()
                }

                // 直接设置最终外观
                mainTabBarController.tabBar.standardAppearance = appearance
                if #available(iOS 15.0, *) {
                    mainTabBarController.tabBar.scrollEdgeAppearance = appearance
                }
                // 立即设置全局外观
                UITabBar.appearance().standardAppearance = appearance
                if #available(iOS 15.0, *) {
                    UITabBar.appearance().scrollEdgeAppearance = appearance
                }
            }

            // 全局外观已在上面直接设置，此处无需操作
        }
    }


    // 递归查找所有TabBarController
    private static func findTabBarControllers(in viewController: UIViewController?) -> [UITabBarController] {
        guard let viewController = viewController else { return [] }

        var tabBarControllers: [UITabBarController] = []

        // 检查当前控制器
        if let tabBarController = viewController as? UITabBarController {
            tabBarControllers.append(tabBarController)
        }

        // 检查子控制器
        if let navigationController = viewController as? UINavigationController {
            tabBarControllers.append(contentsOf: findTabBarControllers(in: navigationController.visibleViewController))
        } else if let tabBarController = viewController as? UITabBarController {
            for childVC in tabBarController.viewControllers ?? [] {
                tabBarControllers.append(contentsOf: findTabBarControllers(in: childVC))
            }
        } else if let presentedVC = viewController.presentedViewController {
            tabBarControllers.append(contentsOf: findTabBarControllers(in: presentedVC))
        }

        // 检查子视图控制器
        for childVC in viewController.children {
            tabBarControllers.append(contentsOf: findTabBarControllers(in: childVC))
        }

        return tabBarControllers
    }

    // 获取应用程序中的主要TabBarController
    private static func getMainTabBarController() -> UITabBarController? {
        // 尝试从应用程序的窗口层次结构中获取TabBarController
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            return nil
        }

        // 如果根视图控制器就是TabBarController，直接返回
        if let tabBarController = rootViewController as? UITabBarController {
            return tabBarController
        }

        // 否则递归查找所有TabBarController
        let tabBarControllers = findTabBarControllers(in: rootViewController)
        return tabBarControllers.first
    }
}

@main
struct StopDoingListApp: App {
    // 系统对象
    @StateObject var dataStore = DataStore.shared
    @StateObject var cloudBackupManager = CloudBackupManager.shared
    @StateObject var storeManager = StoreManager.shared

    // 应用程序配置状态
    @AppStorage("colorSchemeValue") private var colorSchemeValue: Int = 0

    init() {
        setupApp()
    }

    var body: some Scene {
        WindowGroup {
            AnimatedAppRoot {
                ContentView()
                    .environmentObject(dataStore)
                    .environmentObject(cloudBackupManager)
                    .environmentObject(storeManager)
            }
            .preferredColorScheme(getPreferredColorScheme())
            .onAppear {
                // 应用启动时执行数据加载和同步操作
                setupObservers()
            }
        }
    }

    // 设置应用程序
    private func setupApp() {
        #if DEBUG
        print("Debug mode active")
        #endif

        // 使用AppStorage代替UserDefaults
        let currentColorValue = UserDefaults.standard.integer(forKey: "colorSchemeValue")
        colorSchemeValue = currentColorValue

        // 安全初始化iCloud键值存储与CloudBackupManager
        safeInitializeCloudStorage()

        // 首先初始化目录结构和文件，确保所有必要的存储位置都存在
        // 创建一个不可变的引用来避免在逃逸闭包中捕获可变的self
        let initializeStorage = self.initializeStorageDirectories
        DispatchQueue.global(qos: .userInitiated).async {
            initializeStorage()
        }

        // 直接确保 DataStore 加载
        DataStore.shared.onInitialLoadComplete {
            // DataStore 加载完成后的操作可以放在这里
            // 如果启用了iCloud备份，并且有备份数据，可以尝试恢复
            if CloudBackupManager.shared.isBackupEnabled && CloudBackupManager.shared.hasCloudBackup() {
                print("发现iCloud备份数据，可以在设置中恢复")
            }
        }

        // 初始应用 UI 样式和配置 (这些可以与后台检查并行)
        // 创建一个不可变的引用来避免在逃逸闭包中捕获可变的self
        let initializeManagers = self.initializeManagers
        DispatchQueue.main.async {
            StopDoingListApp.applyUserInterfaceStyle(colorValue: currentColorValue)
            UIConfigurationHelper.configureTabBarAppearance()
            UIKeyboardLayoutGuide.fixKeyboardConstraints()
            UIView.fixSnapshotIssues()

            // 初始化CloudManager和HapticFeedbackManager
            initializeManagers()
        }
    }

    // 使用安全的方式初始化iCloud存储
    private func safeInitializeCloudStorage() {
        print("[初始化] 检查iCloud状态...")

        // 安全使用iCloud存储 - 不会阻塞主线程或导致崩溃
        // 实际的初始化逻辑由CloudBackupManager自己管理
        // 在这里只需要确保我们对CloudBackupManager.shared的访问是安全的

        // 由于CloudBackupManager被重构为异步初始化iCloud，这里不需要主动操作
        // 保持这个方法是为了保持代码结构一致性
    }

    // 设置通知观察者
    private func setupObservers() {
        // 监听iCloud恢复完成通知，重新加载数据
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudRestoreCompleted"),
            object: nil,
            queue: .main
        ) { _ in
            // 通知DataStore重新加载数据
            dataStore.reloadAllData()
        }
    }

    // 获取首选颜色方案
    private func getPreferredColorScheme() -> ColorScheme? {
        switch colorSchemeValue {
        case 1:
            return .light
        case 2:
            return .dark
        default:
            return nil
        }
    }

    // 新增初始化所有存储目录的方法
    private func initializeStorageDirectories() {
        print("[初始化] 开始初始化存储目录...")

        // 安全地检查iCloud备份状态
        var hasBackup = false
        
        // 不使用do-catch，因为checkICloudAvailability不会抛出错误
        if FileManager.default.checkICloudAvailability().exists {
            // 只有当用户登录了iCloud时才检查备份状态
            let cloudManager = CloudBackupManager.shared
            hasBackup = cloudManager.hasCloudBackup() && cloudManager.isBackupEnabled

            if hasBackup {
                print("[初始化] 检测到iCloud备份且已启用，跳过创建空白文件")
            }
        }

        // 创建必要的目录
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioRecordingsDirectory = documentsDirectory.appendingPathComponent("AudioRecordings")
        let imagesDirectory = documentsDirectory.appendingPathComponent("Images")

        try? FileManager.default.createDirectory(at: audioRecordingsDirectory, withIntermediateDirectories: true)
        try? FileManager.default.createDirectory(at: imagesDirectory, withIntermediateDirectories: true)
        print("[初始化] 创建媒体目录完成")

        // 如果没有备份或备份未启用，则创建初始化文件
        if !hasBackup {
            // 检查并创建各种JSON文件
            let defaultTodoData = """
            []
            """

            let defaultStatisticsData = """
            {
                "completedTaskCount": 0,
                "totalTasksCreated": 0,
                "taskCompletionStreak": 0,
                "longestCompletionStreak": 0,
                "lastCompletionDate": null,
                "achievedMilestoneIDs": [],
                "freeModeSessionCount": 0,
                "freeModeTotalDuration": 0,
                "freeModeBestDuration": 0
            }
            """

            let defaultSettingsData = """
            {
                "isDarkMode": false,
                "notificationEnabled": true,
                "dailyReminderTime": "20:00",
                "weeklyReportDay": 0,
                "audioFeedbackEnabled": true,
                "hapticFeedbackEnabled": true,
                "customThemeColor": "#FF6B6B",
                "isFirstLaunch": true,
                "lastBackupDate": null,
                "isCloudBackupEnabled": true,
                "isAutomaticBackupEnabled": true,
                "backupFrequency": 7
            }
            """

            let defaultMilestonesData = """
            []
            """

            // 创建各个JSON文件
            createJSONFileIfNeeded(fileName: "todo.json", defaultData: defaultTodoData)
            createJSONFileIfNeeded(fileName: "userstatistics.json", defaultData: defaultStatisticsData)
            createJSONFileIfNeeded(fileName: "settings.json", defaultData: defaultSettingsData)
            createJSONFileIfNeeded(fileName: "milestones.json", defaultData: defaultMilestonesData)

            print("[初始化] 创建JSON数据文件完成")
        }

        print("[初始化] 存储目录初始化完成")
    }

    // 创建JSON文件的辅助方法
    private func createJSONFileIfNeeded(fileName: String, defaultData: String) {
        let fileURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0].appendingPathComponent(fileName)
        if !FileManager.default.fileExists(atPath: fileURL.path) {
            do {
                try defaultData.write(to: fileURL, atomically: true, encoding: .utf8)
                print("[初始化] 创建文件: \(fileName)")
            } catch {
                print("[错误] 创建文件失败 \(fileName): \(error.localizedDescription)")
            }
        }
    }

    // 初始化管理器的方法
    private func initializeManagers() {
        // 初始化StoreManager
        Task {
            await StoreManager.shared.requestProducts()
            await StoreManager.shared.updatePurchaseStatus()
        }

        print("管理器初始化完成")
    }

    // 直接应用UI样式 - 静态版本
    static func applyUserInterfaceStyle(colorValue: Int) {
        DispatchQueue.main.async {
            let windows = UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }

            let style: UIUserInterfaceStyle
            switch colorValue {
            case 1:
                style = .light
            case 2:
                style = .dark
            default:
                style = .unspecified
            }

            windows.forEach { window in
                window.overrideUserInterfaceStyle = style
            }
        }
    }
}

// MARK: - 键盘布局修复
extension UIKeyboardLayoutGuide {
    static func fixKeyboardConstraints() {
        // 使用方法交换来修复键盘约束问题
        if let originalMethod = class_getInstanceMethod(UIView.self, NSSelectorFromString("_systemInputAssistantViewHeightConstraint")),
           let swizzledMethod = class_getInstanceMethod(UIView.self, #selector(UIView.fixedSystemInputAssistantViewHeightConstraint)) {
            method_exchangeImplementations(originalMethod, swizzledMethod)
        }
    }
}

extension UIView {
    @objc func fixedSystemInputAssistantViewHeightConstraint() -> NSLayoutConstraint? {
        // 调用原始方法
        let originalConstraint = self.fixedSystemInputAssistantViewHeightConstraint()

        // 如果约束存在，修改其优先级
        if let constraint = originalConstraint {
            constraint.priority = .defaultHigh
        }

        return originalConstraint
    }
}

// MARK: - 视图快照修复
extension UIView {
    static func fixSnapshotIssues() {
        // 交换 snapshotView 方法
        if let originalMethod = class_getInstanceMethod(UIView.self, #selector(UIView.snapshotView(afterScreenUpdates:))),
           let swizzledMethod = class_getInstanceMethod(UIView.self, #selector(UIView.fixedSnapshotView(afterScreenUpdates:))) {
            method_exchangeImplementations(originalMethod, swizzledMethod)
        }
    }

    @objc func fixedSnapshotView(afterScreenUpdates: Bool) -> UIView? {
        // 如果视图不在可见窗口中，强制使用 afterScreenUpdates: true
        if self.window == nil {
            return self.fixedSnapshotView(afterScreenUpdates: true)
        }
        return self.fixedSnapshotView(afterScreenUpdates: afterScreenUpdates)
    }
}

// 新增动画根视图
struct AnimatedAppRoot<Content: View>: View {
    @State private var blurRadius: CGFloat = 10
    @State private var listOffset: CGFloat = 168
    let content: () -> Content
    init(@ViewBuilder content: @escaping () -> Content) {
        self.content = content
    }
    var body: some View {
        content()
            .blur(radius: blurRadius)
            .offset(y: listOffset)
            .animation(.easeOut(duration: 1), value: blurRadius)
            .onAppear {
                DispatchQueue.main.async {
                    withAnimation(.easeOut(duration: 1)) {
                        blurRadius = 0
                        listOffset = 0
                    }
                }
            }
    }
}

