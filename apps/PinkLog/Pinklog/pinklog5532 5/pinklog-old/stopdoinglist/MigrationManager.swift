import Foundation
import SwiftData
import SwiftUI

class MigrationManager {
    static let shared = MigrationManager()
    
    // 检查是否需要迁移
    func isMigrationNeeded() -> Bool {
        return !UserDefaults.standard.bool(forKey: "dataStoreMigrationCompleted")
    }
    
    // 标记迁移完成
    func markMigrationComplete() {
        UserDefaults.standard.set(true, forKey: "dataStoreMigrationCompleted")
    }
    
    // 执行迁移
    func migrateFromSwiftData(completion: @escaping (Bool) -> Void) {
        // 获取 SwiftData 容器
        guard let modelContainer = try? ModelContainer(for: Habit.self, Reset.self, Trigger.self, Review.self) else {
            print("无法创建 ModelContainer")
            completion(false)
            return
        }
        
        let context = modelContainer.mainContext
        
        // 1. 迁移习惯数据
        do {
            let oldHabits = try context.fetch(FetchDescriptor<SwiftData.Habit>())
            
            var newHabits: [DataModels.Habit] = []
            var newTriggers: [DataModels.Trigger] = []
            var newResets: [DataModels.Reset] = []
            var newReviews: [DataModels.Review] = []
            
            // 2. 转换每个习惯及其关联数据
            for oldHabit in oldHabits {
                // 创建新的习惯对象
                var newHabit = DataModels.Habit(
                    id: oldHabit.id ?? UUID(),
                    name: oldHabit.name,
                    desc: oldHabit.desc,
                    startDate: oldHabit.startDate,
                    lastResetDate: oldHabit.lastResetDate,
                    targetDays: oldHabit.targetDays,
                    category: oldHabit.category,
                    reminderTime: oldHabit.reminderTime,
                    sortOrder: oldHabit.sortOrder,
                    colorStyle: oldHabit.colorStyle,
                    colorIndex: oldHabit.colorIndex
                )
                
                // 处理重置记录
                for oldReset in oldHabit.resets {
                    let newReset = DataModels.Reset(
                        id: oldReset.id,
                        habitId: newHabit.id,
                        date: oldReset.date,
                        streakDays: oldReset.streakDays,
                        note: oldReset.note
                    )
                    
                    newResets.append(newReset)
                    newHabit.resetIds.append(newReset.id)
                }
                
                // 处理触发因素
                for oldTrigger in oldHabit.triggers {
                    // 处理音频文件
                    var audioPath: String? = nil
                    if let oldAudioURL = oldTrigger.audioRecordingURL {
                        // 复制音频文件
                        if let url = URL(string: oldAudioURL) {
                            if let data = try? Data(contentsOf: url) {
                                audioPath = MediaManager.shared.saveAudio(data: data, for: oldTrigger.id)
                            }
                        }
                    }
                    
                    // 处理图片
                    var imagePaths: [String] = []
                    for (index, oldImagePath) in oldTrigger.images.enumerated() {
                        if let newPath = MediaManager.shared.migrateImageFile(
                            from: oldImagePath,
                            for: oldTrigger.id,
                            index: index
                        ) {
                            imagePaths.append(newPath)
                        }
                    }
                    
                    // 转换枚举类型
                    let triggerType: DataModels.Trigger.TriggerType
                    switch oldTrigger.triggerType {
                    case .emotion:
                        triggerType = .emotion
                    case .environment:
                        triggerType = .environment
                    case .time:
                        triggerType = .time
                    case .social:
                        triggerType = .social
                    case .physical:
                        triggerType = .physical
                    }
                    
                    // 转换心情枚举
                    var mood: DataModels.Trigger.MoodType? = nil
                    if let oldMood = oldTrigger.mood {
                        switch oldMood {
                        case .great:
                            mood = .great
                        case .good:
                            mood = .good
                        case .neutral:
                            mood = .neutral
                        case .bad:
                            mood = .bad
                        case .terrible:
                            mood = .terrible
                        }
                    }
                    
                    let newTrigger = DataModels.Trigger(
                        id: oldTrigger.id,
                        habitId: newHabit.id,
                        triggerType: triggerType,
                        triggerDesc: oldTrigger.triggerDesc,
                        frequency: oldTrigger.frequency,
                        lastOccurred: oldTrigger.lastOccurred,
                        notes: oldTrigger.notes,
                        mood: mood,
                        location: oldTrigger.location,
                        alternativeBehaviors: oldTrigger.alternativeBehaviors,
                        audioRecordingPath: audioPath,
                        audioDuration: oldTrigger.audioDuration,
                        audioWaveformData: oldTrigger.audioWaveformData,
                        imagePaths: imagePaths,
                        tags: oldTrigger.tags
                    )
                    
                    newTriggers.append(newTrigger)
                    newHabit.triggerIds.append(newTrigger.id)
                }
                
                // 处理回顾
                for oldReview in oldHabit.reviews {
                    let newReview = DataModels.Review(
                        id: oldReview.id,
                        habitId: newHabit.id,
                        date: oldReview.date,
                        content: oldReview.content
                    )
                    
                    newReviews.append(newReview)
                    newHabit.reviewIds.append(newReview.id)
                }
                
                newHabits.append(newHabit)
            }
            
            // 3. 保存所有新数据
            let dataStore = DataStore.shared
            
            // 设置数据
            dataStore.habits = newHabits
            dataStore.triggers = newTriggers
            dataStore.resets = newResets
            dataStore.reviews = newReviews
            
            // 保存数据
            dataStore.saveAllData()
            
            // 标记迁移完成
            markMigrationComplete()
            
            completion(true)
        } catch {
            print("迁移数据失败: \(error)")
            completion(false)
        }
    }
}

// 迁移视图
struct MigrationView: View {
    @Binding var isMigrationComplete: Bool
    @Binding var showAlert: Bool
    @State private var migrationMessage = "正在准备迁移数据..."
    
    var body: some View {
        VStack {
            Text("数据迁移")
                .font(.largeTitle)
                .padding()
            
            ProgressView()
                .padding()
            
            Text(migrationMessage)
                .padding()
        }
        .onAppear {
            // 执行迁移
            MigrationManager.shared.migrateFromSwiftData { success in
                if success {
                    migrationMessage = "迁移完成！"
                    isMigrationComplete = true
                } else {
                    migrationMessage = "迁移失败，请联系开发者。"
                    showAlert = true
                }
            }
        }
        .alert("迁移失败", isPresented: $showAlert) {
            Button("确定") {
                // 尝试继续使用应用
                isMigrationComplete = true
            }
        } message: {
            Text("数据迁移过程中出现错误，部分数据可能丢失。")
        }
    }
} 