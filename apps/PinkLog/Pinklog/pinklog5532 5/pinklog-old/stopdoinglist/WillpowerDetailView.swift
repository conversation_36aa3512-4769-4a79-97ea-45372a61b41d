struct WillpowerDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 标题与简介
                    VStack(alignment: .leading, spacing: 16) {
                        Text("willpower_balance_science")
                            .font(.title)
                            .fontWeight(.bold)
                            .padding(.top, 16)

                        Text("willpower_balance_description")
                            .font(.body)
                            .foregroundColor(.primary)
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 数据解读
                    VStack(alignment: .leading, spacing: 16) {
                        Text("data_interpretation")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        dataSection(
                            title: "willpower_persistence",
                            description: "willpower_description",
                            items: [
                                NSLocalizedString("accumulated_days", comment: ""),
                                NSLocalizedString("habit_consistency", comment: ""),
                                NSLocalizedString("challenge_difficulty", comment: ""),
                                NSLocalizedString("time_weighting", comment: "")
                            ],
                            iconName: "brain.head.profile",
                            color: .green
                        )

                        dataSection(
                            title: "temptation_and_failure",
                            description: "temptation_description",
                            items: [
                                NSLocalizedString("reset_frequency", comment: ""),
                                NSLocalizedString("cyclic_pattern", comment: ""),
                                NSLocalizedString("environmental_impact", comment: ""),
                                NSLocalizedString("recovery_speed", comment: "")
                            ],
                            iconName: "exclamationmark.octagon",
                            color: .red
                        )

                        // 新增：触发记录与反思指标部分
                        dataSection(
                            title: "trigger_and_reflection",
                            description: "trigger_reflection_description",
                            items: [
                                NSLocalizedString("trigger_awareness", comment: ""),
                                NSLocalizedString("reflection_depth", comment: ""),
                                NSLocalizedString("sharing_engagement", comment: ""),
                                NSLocalizedString("behavioral_insight", comment: "")
                            ],
                            iconName: "brain.fill",
                            color: .purple
                        )

                        dataSection(
                            title: "balance_index",
                            description: "balance_description",
                            items: [
                                NSLocalizedString("sensitivity", comment: ""),
                                NSLocalizedString("stability", comment: ""),
                                NSLocalizedString("progression", comment: ""),
                                NSLocalizedString("motivation", comment: "")
                            ],
                            iconName: "scale.3d",
                            color: .blue
                        )
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 科学依据
                    VStack(alignment: .leading, spacing: 16) {
                        Text("scientific_basis")
                            .font(.title2)
                            .fontWeight(.bold)

                        scienceSection(
                            title: "prefrontal_cortex",
                            description: "neuroscience_description",
                            reference: "Heatherton & Wagner, 2011"
                        )

                        scienceSection(
                            title: "willpower_resource_model",
                            description: "psychology_research",
                            reference: "Baumeister et al., 2018"
                        )

                        scienceSection(
                            title: "visualization_behavior_change",
                            description: "behavioral_science",
                            reference: "Fogg Behavior Model, 2020"
                        )
                    }

                    // 分隔线
                    Divider()
                        .padding(.vertical, 8)

                    // 专家建议
                    VStack(alignment: .leading, spacing: 16) {
                        Text("expert_advice")
                            .font(.title2)
                            .fontWeight(.bold)

                        expertTipSection(
                            role: "neuroscientist",
                            tip: "neuroscientist_tip"
                        )

                        expertTipSection(
                            role: "behavioral_psychologist",
                            tip: "psychologist_tip"
                        )

                        expertTipSection(
                            role: "habit_research_expert",
                            tip: "habit_expert_tip"
                        )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 30)
            }
            .navigationTitle("willpower_science")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("close") {
                        dismiss()
                    }
                }
            }
        }
    }

    // 数据部分组件
    private func dataSection(title: String, description: String, items: [String], iconName: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: iconName)
                    .font(.system(size: 22))
                    .foregroundColor(color)
                    .frame(width: 30, height: 30)

                Text(NSLocalizedString(title, comment: ""))
                    .font(.headline)
                    .foregroundColor(.primary)
            }

            Text(NSLocalizedString(description, comment: ""))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.leading, 42)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.self) { item in
                    HStack(alignment: .top, spacing: 8) {
                        Text("•")
                            .foregroundColor(color)

                        Text(item)
                            .font(.system(size: 14))
                            .foregroundColor(.primary)
                    }
                }
            }
            .padding(.leading, 42)
        }
        .padding(.vertical, 8)
    }

    // 科学部分组件
    private func scienceSection(title: String, description: String, reference: String) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(NSLocalizedString(title, comment: ""))
                .font(.headline)
                .foregroundColor(.primary)

            Text(NSLocalizedString(description, comment: ""))
                .font(.subheadline)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)

            Text(reference)
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
        .padding(.vertical, 4)
    }

    // 专家建议组件
    private func expertTipSection(role: String, tip: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            ZStack {
                Circle()
                    .fill(Color.blue.opacity(0.1))
                    .frame(width: 44, height: 44)

                Image(systemName: "person.fill")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.blue)
            }

            VStack(alignment: .leading, spacing: 6) {
                Text(NSLocalizedString(role, comment: ""))
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(NSLocalizedString(tip, comment: ""))
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 4)
    }
}