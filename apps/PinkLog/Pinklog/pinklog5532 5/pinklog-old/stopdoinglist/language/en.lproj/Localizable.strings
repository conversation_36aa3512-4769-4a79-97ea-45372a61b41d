// AddHabitView Translations
"default" = "Default";
"health" = "Health";
"work" = "Work";
"relationships" = "Relationships";
"finance" = "Finance";
"digital_habits" = "Digital";
"custom" = "Custom";
"basic_info" = "Basic Info";
"habit_name" = "X Do Name";
"description" = "Description";
"goal_setting" = "Goal Setting";
"days_format" = "%d Days";
"category" = "Category";
"card_color" = "Card Color";
"reminder_settings" = "Reminder";
"enable_reminder" = "Enable Reminder";
"reminder_time" = "Reminder Time";
"add_habit" = "Add Habit";
"cancel" = "Cancel";
"done" = "Done";
"add_habit_placeholder_name" = "What do you want to stop today？";
"add_habit_placeholder_reason" = "Why？";
// EditHabitView Translations
"edit_habit" = "Edit Habit";
"delete_item" = "Delete Item";
"delete_habit" = "Delete Habit";
"delete" = "Delete";
"delete_habit_confirmation" = "Delete '%@'? This cannot be undone.";
// Settings Menu Translations
"settings" = "Settings";
"appearance" = "Appearance";
"system" = "System";
"light" = "Light";
"dark" = "Dark";
"language" = "Language";
"notification_settings" = "Notifications";
"privacy_policy" = "Privacy Policy";
"data_management" = "Data Mgmt";
"export_data" = "Export Data";
"backup_settings" = "Backup";
"personalization" = "Personalization";
"card_sort" = "Card Sort";
"about_app" = "About";
"about_xdo" = "About X Do";
"rate_app" = "Rate Us";
"settings_and_options" = "Settings & Options";
// AddReviewView Translations
"add_review" = "Add Review";
"record_trigger" = "Record Trigger";
"save" = "Save";
"location_access_denied" = "Allow location access in settings";
"microphone_access_denied" = "Allow mic access in settings";
"emotional" = "Emotional";
"environmental" = "Environmental";
"physical" = "Physical";
"events" = "Events";
// Guiding Questions
"what_were_you_doing" = "What were you doing?";
"how_did_you_feel" = "How did you feel?";
"what_were_you_thinking" = "What were you thinking?";
"how_often_does_this_happen" = "How often does this happen?";
"what_helps_resist" = "What helps you resist?";
"what_do_you_usually_do" = "What do you usually do?";
"any_environmental_factors" = "Any environmental factors?";
"any_warning_signs" = "Any warning signs?";
"similar_to_past" = "Similar to past experiences?";
"coping_strategies" = "Coping strategies tried?";
// Mood Types
"terrible" = "Terrible";
"bad" = "Bad";
"neutral" = "Neutral";
"good" = "Good";
"great" = "Great";
// Trigger Main Categories
// Trigger Types (Legacy)
"emotion" = "Emotion";
"environment" = "Environment";
"time" = "Time";
"social" = "Social";
"physical" = "Physical";
// Trigger Categories
"negative_emotions" = "Negative Emotions";
"positive_emotions" = "Positive Emotions";
"mental_states" = "Mental States";
"locations" = "Locations";
"times" = "Times";
"social_contexts" = "Social Contexts";
"media_influence" = "Media Influence";
"withdrawal_symptoms" = "Withdrawal";
"body_conditions" = "Body Conditions";
"physiological_needs" = "Physiological Needs";
"conflicts" = "Conflicts";
"failures" = "Failures";
"life_events" = "Life Events";
"no_obvious_trigger" = "No Obvious Trigger";
// Trigger Options
"stress" = "Stress";
"anxiety" = "Anxiety";
"depression" = "Depression";
"anger" = "Anger";
"loneliness" = "Loneliness";
"boredom" = "Boredom";
"guilt" = "Guilt";
"jealousy" = "Jealousy";
"fear" = "Fear";
"excitement" = "Excitement";
"joy" = "Joy";
"relaxation" = "Relaxation";
"comfort" = "Comfort";
"confidence" = "Confidence";
"satisfaction" = "Satisfaction";
"fatigue" = "Fatigue";
"drowsiness" = "Drowsiness";
"impulsiveness" = "Impulsiveness";
"lack_of_control" = "Lack of Control";
"rationalization" = "Rationalization";
"nostalgia" = "Nostalgia";
"craving" = "Craving";
"temptation" = "Temptation";
// Locations
"bar" = "Bar";
"ktv" = "KTV";
"restaurant" = "Restaurant";
"smoking_area" = "Smoking Area";
"convenience_store" = "Convenience Store";
"supermarket" = "Supermarket";
"home" = "Home";
"office" = "Office";
"usual_places" = "Usual Places";
// Times
"morning" = "Morning";
"noon" = "Noon";
"evening" = "Evening";
"late_night" = "Late Night";
"weekend" = "Weekend";
"holidays" = "Holidays";
"work_break" = "Work Break";
"after_meals" = "After Meals";
"before_bed" = "Before Bed";
// Social Contexts
"friend_gathering" = "Friend Gathering";
"family_dinner" = "Family Dinner";
"social_pressure" = "Social Pressure";
"peer_influence" = "Peer Influence";
"colleague_influence" = "Colleague Influence";
"unfamiliar_environment" = "Unfamiliar Env";
// Media Influence
"advertising" = "Advertising";
"movies" = "Movies";
"tv_shows" = "TV Shows";
"social_media" = "Social Media";
"easily_accessible" = "Easily Accessible";
// Physical Symptoms
"headache" = "Headache";
"nausea" = "Nausea";
"insomnia" = "Insomnia";
"irritability" = "Irritability";
"poor_concentration" = "Poor Focus";
"strong_urge" = "Strong Urge";
"illness" = "Illness";
"pain" = "Pain";
"hormonal_changes" = "Hormonal Changes";
"menstruation" = "Menstruation";
"weight_changes" = "Weight Changes";
"hunger" = "Hunger";
"thirst" = "Thirst";
"sleep_deprivation" = "Sleep Deprived";
"physical_exhaustion" = "Exhaustion";
// Events
"family_conflict" = "Family Conflict";
"work_conflict" = "Work Conflict";
"interpersonal_conflict" = "Interpersonal Conflict";
"disagreement" = "Disagreement";
"work_failure" = "Work Failure";
"study_setback" = "Study Setback";
"plan_disruption" = "Plan Disruption";
"unmet_expectations" = "Unmet Expectations";
"unemployment" = "Unemployment";
"breakup" = "Breakup";
"moving" = "Moving";
"bereavement" = "Bereavement";
"major_changes" = "Major Changes";
"habitual_behavior" = "Habitual Behavior";
"inner_desire" = "Inner Desire";
"no_obvious_reason" = "No Obvious Reason";
"custom_reason" = "Custom";
// UI Elements
"select_specific_reason" = "Select Specific Reason";
"add_custom_reason" = "Add Custom Reason";
"clear" = "Clear";
"markdown_guide" = "MD Guide";
"edit_mode" = "Edit Mode";
"preview_mode" = "Preview";
"markdown_guide_title" = "Markdown Guide";
"close_guide" = "Close";
// Markdown Examples
"heading" = "Heading";
"emphasis" = "Emphasis";
"lists" = "Lists";
"quotes" = "Quotes";
"code" = "Code";
"links_and_images" = "Links & Images";
"preview" = "Preview";
// Coming Soon Feature Descriptions
"export_data_description" = "Export feature coming soon with CSV/PDF formats for data analysis.";
"backup_settings_description" = "Advanced backup features coming soon with cloud and local options.";
"q2_2025" = "Q2 2025";
"q3_2025" = "Q3 2025";
// HabitDashboardView Translations
"overview" = "Overview";
"trigger_analysis" = "Trigger Analysis";
"mood_analysis" = "Mood Analysis";
"time_analysis" = "Time Analysis";
"streak_progress" = "Streak Progress";
"target_days" = "Target: %d Days";
"compared_to_last_week" = "vs Last Week";
"longest_streak" = "Longest Streak";
"average_interval" = "Avg Interval";
"na" = "N/A";
"recent_trigger" = "Recent Trigger";
"no_triggers_recorded" = "No Triggers Recorded";
"quick_stats" = "Quick Stats";
"total_triggers" = "Total Triggers";
"average_mood" = "Avg Mood";
"primary_factor" = "Primary Factor";
"emotional_psychological" = "Emotional";
"environmental_social" = "Environmental";
"physical_factors" = "Physical";
"specific_events" = "Events";
// Time Formats
"days_unit" = "Days";
"day_unit" = "Day";
"days_ago" = "%d Days Ago";
"today" = "Today";
"yesterday" = "Yesterday";
// HabitDashboardView - Tabs
// HabitDashboardView - Streak Progress
"goal_days_format" = "Goal: %d Days";
// HabitDashboardView - Recent Trigger
// HabitDashboardView - Quick Stats
"primary_trigger" = "Primary Trigger";
// HabitDashboardView - Trigger Categories
"emotional_category" = "Emotional";
"environmental_category" = "Environmental";
"physical_category" = "Physical";
"events_category" = "Events";
// ReviewListView
"review_list_debug" = "Review List: Count";
// SharePosterView Translations
"poster_template_minimal" = "X";
"poster_template_geometric" = "Blue Screen";
"poster_template_wave" = "Pixels";
"poster_template_grid" = "Rainbow";
"poster_template_frame" = "Dots";
"poster_template_geometric_alt" = "Peas";
"poster_template_wave_alt" = "Stripes";
"poster_template_frame_alt" = "Tech";
"poster_template_n64" = "Cube";
"poster_template_gba" = "Purple";
"poster_template_atari" = "Calendar";
"aspect_ratio" = "Ratio";
"share_poster" = "Share Poster";
"preparing_share" = "Preparing...";
"share_prepare_failed" = "Failed to prepare";
"back" = "Back";
// ReviewSection Translations
"daily_review" = "Daily Review";
"view_mode" = "View Mode";
"week" = "Week";
"month" = "Month";
"year" = "Year";
"latest_reviews" = "Latest Reviews";
"view_all" = "View All";
"no_reviews" = "No Reviews";
"add_review_hint" = "Add reviews to track your journey";
// Calendar Translations
"sun" = "Sun";
"mon" = "Mon";
"tue" = "Tue";
"wed" = "Wed";
"thu" = "Thu";
"fri" = "Fri";
"sat" = "Sat";
"week_format" = "Week W, MMM yyyy";
"month_format" = "MMM yyyy";
"year_format" = "yyyy";
"january" = "Jan";
"february" = "Feb";
"march" = "Mar";
"april" = "Apr";
"may" = "May";
"june" = "Jun";
"july" = "Jul";
"august" = "Aug";
"september" = "Sep";
"october" = "Oct";
"november" = "Nov";
"december" = "Dec";
// Review Detail Translations
"review_detail" = "Review Details";
"playback_error" = "Playback Error";
"ok" = "OK";
"images" = "Images";
"recordings" = "Recordings";
"recordings_count" = "Recordings (%d)";
"items" = "items";
"all_reviews" = "All Reviews";
"occurrence_count" = "Occurred %d times";
// HabitListView Translations
"success_rate" = "Success Rate";
"date_added" = "Date Added";
"custom_order" = "Custom";
"x_do_list" = "XMoment";
"empty_list_title" = "Your XMoment is Empty";
"empty_list_description" = "Tap + to add a habit you want to quit";
"add_x_do" = "Add X Do";
// WillpowerTugOfWar Translations
"willpower_field" = "Willpower Field";
"temptation_field" = "Temptation Field";
"willpower_strength" = "Willpower Strength";
"temptation_strength" = "Temptation Strength";
"resets" = "Resets";
"days_accumulated" = "Days Accum.";
"start_journey" = "Start Your Willpower Journey";
"strong_willpower" = "Strong Willpower: Keep it up";
"stable_willpower" = "Stable Willpower: Stay alert";
"balanced_energy" = "Balanced Energy: Maintain";
"increasing_temptation" = "Temptation Rising: Be vigilant";
"weakening_willpower" = "Willpower Weakening: Refocus";
"critical_imbalance" = "Critical Imbalance: Intervene now";
"willpower_tug_of_war" = "Willpower Tug of War";
"willpower_balance_science" = "Willpower Balance: Science";
"willpower_balance_description" = "Willpower Balance quantifies your self-control vs temptation. Based on behavioral science and neuropsychology, it helps track and improve willpower during your journey.";
"willpower_science" = "Willpower Science";
"close" = "Close";
"trigger_and_reflection" = "Trigger & Reflection";
"trigger_reflection_description" = "Record and understand habit triggers and reflect on relapses";
"trigger_awareness" = "Trigger Awareness";
"reflection_depth" = "Reflection Depth";
"sharing_engagement" = "Sharing";
"behavioral_insight" = "Behavioral Insight";
"engagement_metrics" = "Engagement Metrics";
"triggers_recorded" = "Triggers Recorded";
"reflections_made" = "Reflections Made";
"shares_count" = "Shares";
"exceptional_willpower" = "Exceptional Willpower: Peak control";
"growing_willpower" = "Growing Willpower: Building strength";
// AboutXDoView Translations
"about_xdo" = "About X Do";
"less_to_do" = "Less To Do";
"more_to_be" = "More To Be";
"busy_life_message" = "Stop filling life with meaningless busyness";
"better_self_message" = "Focus on becoming better, not doing more";
"why_need_xdo" = "Why Need X Do?";
"time_waste" = "Time Sink: Hours wasted on social media, videos & pointless social interaction";
"health_crisis" = "Health Crisis: Poor sleep, eating & movement habits damage physical & mental health";
"financial_pressure" = "Financial Pressure: Impulse buys & bad investments empty wallets & increase stress";
"self_doubt" = "Self-Doubt: Failed attempts at change lead to 'I can't do it' cycle";
"data_shows" = "Data Shows:";
"habit_failure_rate" = "95% of people fail at habit change";
"daily_time_waste" = "Average person wastes 3.5hrs daily on meaningless activities";
"resolution_abandon" = "80% of resolutions abandoned by February";
"xdo_unique_value" = "X Do's Unique Value";
"scientific_method" = "Science, Not Willpower";
"scientific_description" = "Based on behavioral science & neuroplasticity to help you start with 'not doing'";
"easy_to_use" = "Simple, Life-Integrated";
"easy_description" = "Just seconds to record, no complex processes";
"visual_progress" = "Visual Progress, Enhanced Motivation";
"visual_description" = "Clear progress bars & achievements show every step forward";
"privacy_protection" = "Privacy Protected";
"privacy_description" = "Your data belongs only to you - never shared or sold";
"user_testimonials" = "User Testimonials";
"programmer_testimonial" = "I used to stay up late on my phone and feel exhausted every day. With X Do, I quit late nights and now feel energized with better productivity.";
"mother_testimonial" = "X Do helped me quit impulse shopping. Now our family budget is more reasonable and life is easier.";
"version" = "Version";
// Daily Tips
"daily_tip_1" = "Take it slow, one issue at a time.";
"daily_tip_2" = "Your feelings are temporary, they'll pass.";
"daily_tip_3" = "Find a healthy substitute activity.";
"daily_tip_4" = "Practice deep breathing or meditation.";
"daily_tip_5" = "Talk to a trusted friend about your struggles.";
"daily_tip_6" = "Focus on progress, not setbacks.";
"daily_tip_7" = "Remember why you started.";
"daily_tip_8" = "Avoid trigger environments or people.";
"daily_tip_9" = "Be kind to yourself about slip-ups.";
"daily_tip_10" = "Set small, achievable goals.";
"daily_tip_11" = "Reward yourself for milestones.";
"daily_tip_12" = "Read inspiring stories or quotes.";
"daily_tip_13" = "Exercise to relieve stress.";
"daily_tip_14" = "Ensure adequate sleep.";
"daily_tip_15" = "Journal your thoughts and feelings.";
"daily_tip_16" = "Learn new coping skills.";
"daily_tip_17" = "Visualize a better future without the habit.";
"daily_tip_18" = "Change takes time and patience.";
"daily_tip_19" = "Celebrate small victories.";
"daily_tip_20" = "Seek help if needed - it's not weakness.";
// More Daily Tips
"daily_tip_21" = "Don't quit - just be slightly better than yesterday.";
"daily_tip_22" = "Progress isn't about speed, but consistency.";
"daily_tip_23" = "You're doing something difficult but right.";
"daily_tip_24" = "Each resistance builds strength.";
"daily_tip_25" = "Stop 'doing' busywork, start 'being' who you want.";
"daily_tip_26" = "The power of repetition is beyond imagination.";
"daily_tip_27" = "You're becoming better every day.";
"daily_tip_28" = "Today's restraint is for tomorrow's ease.";
"daily_tip_29" = "Hope comes after persistence, not before.";
"daily_tip_30" = "Stop painfully to exist joyfully.";
"daily_tip_31" = "Stop to move forward.";
"daily_tip_32" = "Stop doing, start living.";
"daily_tip_33" = "Say no to ineffective, yes to valuable.";
"daily_tip_34" = "Leaving comfort is hard but necessary.";
"daily_tip_35" = "Not doing helps you be yourself better.";
"daily_tip_36" = "Today's 'no' builds tomorrow's freedom.";
"daily_tip_37" = "Don't quit stopping - it leads to becoming.";
"daily_tip_38" = "Each temptation resisted invests in your future self.";
"daily_tip_39" = "Persistence is hard but worth it.";
"daily_tip_40" = "Maintain 'not doing' to maintain 'becoming'.";
"daily_tip_41" = "One less wrong step is one closer to right.";
"daily_tip_42" = "Every 'no' maintains inner order.";
"daily_tip_43" = "By stopping, you shape a lighter, truer you.";
"daily_tip_44" = "Stopping is wisdom, becoming is freedom.";
"daily_tip_45" = "You're not stopping behavior, but mediocrity.";
"daily_tip_46" = "Change starts with one 'no'.";
"daily_tip_47" = "Lost in 'To do'? Find yourself in 'X Do'.";
"daily_tip_48" = "'Not doing' is a choice and strength.";
"daily_tip_49" = "Tired of busyness? Try the power of 'not doing'.";
"daily_tip_50" = "'Stopping' gives your soul space to breathe.";
"daily_tip_51" = "Simplify your actions, enrich your being.";
"daily_tip_52" = "The more you don't, the more you are.";
"daily_tip_53" = "Want change? Start with what to stop.";
"daily_tip_54" = "Stressed? Subtract from life.";
"daily_tip_55" = "'Not doing' is self-love too.";
"daily_tip_56" = "Want authenticity? Stop pretending, start being.";
"daily_tip_57" = "You can stop, rest, and become.";
"daily_tip_58" = "Stop self-criticism, allow gradual progress.";
"daily_tip_59" = "We know it's hard, but you're improving.";
"daily_tip_60" = "The hardest path often leads highest.";
"daily_tip_61" = "Persistence is cool.";
"daily_tip_62" = "No one said it's easy, but it's worth it.";
"daily_tip_63" = "Hesitate once, risk starting over.";
"daily_tip_64" = "This moment's decision defines your future self.";
"daily_tip_65" = "Process bitter, results sweet.";
"daily_tip_66" = "Celebrate each small 'stop' - they build your inner castle.";
"daily_tip_67" = "Less external doing, more internal being.";
"daily_tip_68" = "Fewer to-dos, deeper existence.";
"daily_tip_69" = "Embrace discomfort - it precedes change.";
"daily_tip_70" = "Release what exhausts to embrace what grows.";
"daily_tip_71" = "Best efficiency is knowing when to stop.";
"daily_tip_72" = "Less 'must', more 'want to'.";
"daily_tip_73" = "Value is who you become, not what you do.";
"daily_tip_74" = "'Done' lists bring achievement, 'stop' lists bring liberation.";
"daily_tip_75" = "Don't let To-dos fill you - leave space to breathe.";
"daily_tip_76" = "Doing more blurs vision, doing less clarifies.";
"daily_tip_77" = "Today's restraint is tomorrow's freedom.";
"daily_tip_78" = "Remove distractions to become more focused.";
"daily_tip_79" = "World wants 'To do', heart wants 'To be'.";
"daily_tip_80" = "Focus less on doing, more on being.";
"daily_tip_81" = "You deserve a life not hijacked by tasks.";
"daily_tip_82" = "Your value isn't how much you do.";
// Data Interpretation
"data_interpretation" = "Data Interpretation";
"willpower_persistence" = "Willpower & Persistence";
"willpower_description" = "Measures ability to resist temptation, calculated from:";
"temptation_and_failure" = "Temptation & Failure";
"temptation_description" = "Quantifies habit disruptions & failures, considering:";
"balance_index" = "Balance Index";
"balance_description" = "Comprehensive metric using non-linear algorithm ensuring:";
"scientific_basis" = "Scientific Basis";
"expert_advice" = "Expert Advice";
"neuroscientist" = "Neuroscientist";
"neuroscientist_tip" = "Break complex goals into small steps. Each completion releases dopamine, strengthening willpower & motivation.";
"behavioral_psychologist" = "Behavioral Psychologist";
"psychologist_tip" = "Create 'if-then' plans for anticipated temptations. This pre-decision reduces real-time willpower consumption.";
"habit_research_expert" = "Habit Research Expert";
"habit_expert_tip" = "Focus on identity shift not outcomes. Think 'I'm someone who doesn't do X' rather than 'I'll stop doing X'.";
"prefrontal_cortex" = "Prefrontal Cortex & Self-Control";
"neuroscience_description" = "Neuroscience shows self-control primarily managed by prefrontal cortex, crucial for impulse handling, long-term planning & inhibiting bad behaviors. Our tool enhances PFC activity via visual feedback.";
"willpower_resource_model" = "Willpower Resource Model";
"psychology_research" = "Psychology supports 'willpower as resource' view - self-control temporarily weakens with use but recovers with rest & strategies. Our algorithm accounts for resource fluctuation.";
"visualization_behavior_change" = "Visualization & Behavior Change";
"behavioral_science" = "Behavioral science confirms real-time visual feedback significantly improves behavior change success. Seeing cumulative effects activates dopamine reward pathways more effectively.";
"accumulated_days" = "Accumulated days: total days maintained across habits";
"habit_consistency" = "Habit consistency: stability of habit maintenance";
"challenge_difficulty" = "Challenge difficulty: calibrated by habit type & history";
"time_weighting" = "Time weighting: recent maintenance weighted higher";
"reset_frequency" = "Reset frequency: number of habit resets";
"cyclic_pattern" = "Cyclic patterns: whether failures show regularity";
"environmental_impact" = "Environmental impact: external factors like stress periods";
"recovery_speed" = "Recovery speed: time from failure to resumption";
"sensitivity" = "Sensitivity: small progress/setbacks visually reflected";
"stability" = "Stability: no dramatic fluctuation from single failure";
"progression" = "Progression: reflects long-term trends";
"motivation" = "Motivation: designed to maximize encouragement";
// Location Manager Messages
"location_services_disabled" = "Location services disabled, enable in settings";
"location_permission_denied" = "Location permission denied, enable in settings";
"location_unknown_status" = "Unknown location permission status";
"location_permission_denied_simple" = "Location permission denied";
"location_network_error" = "Network error, can't get location";
"location_unknown" = "Current location unknown";
"location_error_format" = "Location error: %@";
// Permission Descriptions
"microphone_permission_description" = "Mic access needed to record voice descriptions";
"location_permission_description" = "Location access needed to record trigger locations";
"camera_permission_description" = "Camera access needed to take photos of triggers";
"photo_library_permission_description" = "Photo library access needed to select and save images";
// TriggerAnalysisView
"all_trigger_records" = "All Triggers";
"microphone_permission_title" = "Mic Permission";
"open_settings" = "Open Settings";
"microphone_permission_needed" = "Mic permission needed for recording";
"playback_error_title" = "Playback Error";
"confirm" = "Confirm";
"storage_info_title" = "Storage Info";
"media_files_size" = "Media files total: %@";
"trigger_count" = "Trigger count: %d";
"cleanup_old_files" = "Clean Old Files";
"cleanup_confirmation" = "Clean media files older than 30 days?";
"cleanup_complete" = "Cleanup Complete";
"files_cleaned" = "%d files cleaned";
"unknown_time" = "Unknown time";
"type_and_mood" = "Type & Mood";
"trigger_type" = "Trigger Type";
"not_selected" = "Not Selected";
"mood" = "Mood";
"trigger_details" = "Trigger Details";
"recordings" = "Recordings (%d)";
"location_parse_error" = "Can't parse location";
"location_error" = "Can't get location: %@";
"audio_playback_error" = "Audio playback failed: %@";
"cleanup_audio_failed" = "Audio cleanup failed: %@";
"cleanup_images_failed" = "Images cleanup failed: %@";
// Sort Method
"sort_method" = "Sort By";
// Card Colors
"default_color" = "Default";
// ComingSoonView
"coming_soon" = "Coming Soon";
"estimated_release_time" = "Est. Release";
"notify_feature_available" = "Notify when available";
// Version
"version_number" = "Version 1.0.0";
// HabitDetailView - Achievement Section
"persisted_days" = "Days Kept";
"target_days" = "Target";
"reset_times" = "Resets";
"days" = "Days";
"number_of_times" = "times";
"success_rate_trend" = "Success Rate Trend";
"share_achievement" = "Share Achievement";
"generate_poster" = "Create poster to show progress";
"reset_habit" = "Reset Habit";
"reset_confirmation" = "Reset '%@'? This will record %d days kept.";
"reset" = "Reset";
// HabitDetailView - Statistics Section
"data_analysis" = "Data Analysis";
"multi_dimensional_insight" = "Multi-Dimensional Insight";
"loading_analysis" = "Loading analysis...";
"statistics_chart" = "Statistics Chart";
"trigger_type_distribution" = "Trigger Type Distribution";
"most_frequent_trigger" = "Most frequent: %@ (%d times)";
"mood_distribution" = "Mood Distribution";
"most_frequent_mood" = "Most frequent: %@ (%d times)";
"reset_days_distribution" = "Reset Days Distribution";
"average_persistence" = "Avg %d days before reset, %d resets total";
"not_enough_data" = "Not enough data for analysis";
// HabitDetailView - Trigger Analysis Section
"latest_trigger_records" = "Latest Triggers";
"no_trigger_records" = "No trigger records";
"trigger_record_hint" = "Record triggers to better analyze and control habits";
"add_trigger" = "Add Trigger";
// Chart Types
"mood_distribution_title" = "Mood Distribution";
"reset_distribution" = "Reset Distribution";
"date_format" = "MMM d, yyyy";
// Success Rate Chart
"current_rate" = "Current: %d%%";
"reset_point" = "Reset Point";
"days_since_creation" = "Since: %d days";
"longest_record" = "Longest: %d days";
// Chart Legends
"blue_curve" = "Success Rate";
"red_reset" = "Reset Point";
// Chart Axis Labels
"percentage_100" = "100%";
"percentage_75" = "75%";
"percentage_50" = "50%";
"percentage_25" = "25%";
"percentage_0" = "0%";
// Date Formats
"short_date_format" = "MM-dd";
// HabitDetailView - Chart Labels
"reset_streak_days" = "Reset (%d days)";
"current_streak_days" = "Current (%d days)";
"persist_days" = "%d Days";
"completion_percent" = "%d%% Complete";
"persistence_journey" = "Journey";
// HabitDetailView - MetricCard
"average_persist_days" = "Avg Days";
"progress" = "Progress";
// Fallback values
"unknown_mood" = "Unknown Mood";
// Chart Labels
"count" = "Count";
"mood" = "Mood";
"reset_count" = "Reset Count";
"item_count_format" = "%d";
// EnhancedTimeAnalysisSection Translations
"chart_type" = "Chart Type";
"hourly_distribution" = "Hourly";
"weekly_distribution" = "Weekly";
"monthly_distribution" = "Monthly";
"high_risk_periods" = "High Risk Periods";
"not_enough_data_for_risk_periods" = "Not enough data for risk analysis";
"peak_trigger_hour" = "%@ is peak trigger time";
"trigger_count_in_period" = "%d triggers in this period";
"high_risk_weekday" = "%@ is high risk day";
"trigger_count_on_day" = "%d triggers on this day";
"most_triggers_month" = "%@ has most triggers";
"trigger_count_in_month" = "%d triggers this month";
"midnight" = "Midnight";
"evening" = "Evening";
"no_hourly_distribution_data" = "No hourly data";
"afternoon" = "Afternoon";
"no_weekly_distribution_data" = "No weekly data";
"weekday" = "Weekday";
"no_monthly_distribution_data" = "No monthly data";
"month" = "Month";
// EnhancedTriggerAnalysisSection Translations
"trigger_factor_distribution" = "Trigger Distribution";
"no_trigger_factor_data" = "No trigger data";
"percentage" = "Percentage";
"subcategory_distribution" = "Subcategory Distribution";
"no_subcategory_data" = "No subcategory data";
"detailed_options_analysis" = "Detailed Options";
"no_detailed_options_data" = "No detailed options data";
"trigger_factor_trend" = "Trigger Trend";
"no_trend_data" = "No trend data";
"date" = "Date";
// EnhancedMoodAnalysisSection Translations
"mood_distribution" = "Mood Distribution";
"no_mood_data" = "No mood data";
"mood_trend" = "Mood Trend";
"no_mood_trend_data" = "No mood trend data";
"mood_trigger_relationship" = "Mood-Trigger Relationship";
"no_mood_trigger_relationship_data" = "No mood-trigger relationship data";
"mood_calendar" = "Mood Calendar";
"no_mood_calendar_data" = "No mood calendar data";
"average" = "Average";
"mood_value" = "Mood Value";
// Additional EnhancedMoodAnalysisSection Translations
"mood_legend" = "Mood:";
"mood_range" = "🙁 → 😊";
"days_count_format" = "%d days";
"no_data" = "No data";
// TimeRange Translations
"quarter" = "Quarter";
"all" = "All";
"this_week" = "This Week";
"this_month" = "This Month";
"this_quarter" = "This Quarter";
"this_year" = "This Year";
"all_time" = "All Time";
// DashboardTab Translations
"overview" = "Overview";
"trends" = "Trends";
"categories" = "Categories";
"insights" = "Insights";
// Statistics Dashboard Translations
"total_xdo_count" = "Total X Dos";
"total_resets" = "Total Resets";
"average_streak_days" = "Avg Streak Days";
"longest_streak" = "Longest Streak";
"total_reviews" = "Total Reviews";
"total_triggers" = "Total Triggers";
"best_performance" = "Best Performance";
"current_streak" = "Current Streak";
"loading" = "Loading...";
"no_data_available" = "No data";
"persistence_efficiency" = "Persistence";
"resilience" = "Resilience";
"comprehensive" = "Overall";
"metrics_explanation" = "Metrics Explained";
// Metrics Explanation
"success_rate_description" = "Evaluates overall performance based on current status, reset frequency and historical best.";
"success_rate_tip_1" = "Increase current streak days";
"success_rate_tip_2" = "Reduce resets within 90 days";
"success_rate_tip_3" = "Work toward new longest streak";
"persistence_efficiency_description" = "Evaluates average persistence after each reset, reflecting long-term stability.";
"persistence_efficiency_tip_1" = "Extend persistence after each reset";
"persistence_efficiency_tip_2" = "Reduce total reset count";
"persistence_efficiency_tip_3" = "Maintain stable habit patterns";
"resilience_description" = "Measures recovery ability after failure, focusing on interval length and trends.";
"resilience_tip_1" = "Extend time between resets";
"resilience_tip_2" = "Avoid multiple resets in short time";
"resilience_tip_3" = "Maintain increasing reset intervals";
"how_to_improve" = "How to Improve";
// Trend Analysis
"trend_analysis" = "Trend Analysis";
"persistence_trend" = "Persistence Trend";
"reset_analysis" = "Reset Analysis";
"weekly_distribution" = "Weekly Distribution";
"hourly_distribution" = "Hourly Distribution";
"most_common_reset_time" = "Most common reset: %@:00 - %d:00";
"all_categories" = "All";
"search_options" = "Search";
"no_matches" = "No matches";
"no_data_yet" = "No data yet";
"refresh" = "Refresh";
"total_count" = "Total: %d";
"upward_trend" = "Upward";
"downward_trend" = "Downward";
"trend_percentage" = "%@%@%%";
"maintaining_upward_trend" = "Maintaining upward trend in %@";
"showing_downward_trend" = "Showing downward trend in %@";
"calculating" = "Calculating...";
// Time Periods
"morning" = "Morning";
"night" = "Night";
// Category Analysis
"category_distribution" = "Category Distribution";
"trigger_subcategory_analysis" = "Trigger Subcategory Analysis";
"trigger_options_heatmap" = "Trigger Options Heatmap";
"mood_trigger_correlation" = "Mood-Trigger Correlation";
"total_items" = "Total Items";
"subcategories" = "%d subcategories";
"mood_distribution" = "Mood Distribution";
"mood_by_category" = "Mood distribution for %@";
"all_triggers_mood" = "Mood distribution for all triggers";
"search_placeholder" = "Search...";
"clear_search" = "Clear";
"occurrences" = "%d occurrences";
"percentage" = "Percentage";
"value" = "Value";
// Insights
"ai_insights" = "AI Insights";
"overall_performance" = "Overall Performance";
"improvement_suggestions" = "Suggestions";
"analyzing" = "Analyzing...";
"performance_breakdown" = "Performance Breakdown";
"score" = "Score";
"persistence_days" = "Persistence Days";
"behavior_consistency" = "Consistency";
"recovery_ability" = "Recovery";
"out_of" = "/";
"no_insights_yet" = "No insights yet";
"best_category_performance" = "%@ category performs best";
"category_success_rate" = "This category has %d%% success rate, averaging %.1f days";
"peak_reset_time" = "%@ is peak reset time";
"reset_time_detail" = "Most frequent reset time is %d:00-%d:00 (%@), may need more attention";
"continuous_improvement" = "Continuous improvement";
"improvement_percentage" = "Your persistence rate increased by %.1f%% (in %@). Keep it up!";
"needs_attention" = "Needs attention";
"decline_percentage" = "Your persistence rate declined by %.1f%% in %@, may need strategy adjustment";
"habit_formation" = "Habit formed";
"habit_formation_detail" = "\"%@\" maintained for %d days, now a stable habit!";
// Motivational Quotes
"quote_1" = "Our life is what our thoughts make it.";
"quote_2" = "Habits aren't destiny - they're choices we can change daily.";
"quote_3" = "Don't let yesterday take up too much of today.";
"quote_4" = "Every unwanted habit satisfies some need in your life.";
"quote_5" = "A journey of a thousand miles begins with a single step.";
"quote_6" = "True growth is improving a little each day.";
"quote_7" = "Develop the habit of doing the difficult thing first.";
"quote_8" = "All our habits eventually form our character.";
"quote_9" = "Habits require no heroics, just daily consistency.";
"quote_10" = "Creating good habits is easier than breaking bad ones.";
"author_1" = "Marcus Aurelius";
"author_2" = "Stephen Covey";
"author_3" = "Will Rogers";
"author_4" = "James Clear";
"author_5" = "Xunzi";
"author_6" = "Heidegger";
"author_7" = "Margaret Thatcher";
"author_8" = "Bill Phillips";
"author_9" = "Aristotle";
"author_10" = "Mark Twain";
// MoodTriggerCorrelationChart
"all_categories" = "All Categories";
"mood_trigger_relationship" = "Mood-Trigger Relationship";
"all_triggers_mood" = "Mood distribution for all triggers";
"mood_by_category" = "Mood distribution for %@ category";
// StatisticsView specific strings
"streak_distribution_title" = "Streak Distribution";
"best_performing_title" = "Best Performance";
"current_streak_format" = "Streak: %d days";
"category_format" = "Category: %@";
"recent_activity_title" = "Recent Activity";
"no_reset_records" = "No reset records";
"xdo_count_format" = "X Dos: %d";
"average_streak_format" = "Avg streak: %.1f days";
"period_label_days_range" = "%d-%d days";
"period_label_month" = "Month %d";
"period_label_quarter" = "Q%d";
"no_reset_data" = "No reset data";
"streak_distribution_range_label" = "Days Range";
"streak_distribution_count_label" = "Habit Count";
"start_date_label" = "Start";
"active_xdo_count_label" = "Active X Dos";
"weekday_label_format" = "%@";
"improve_persistence_in_period" = "Improve persistence in %@";
"highest_reset_rate_in_period" = "Highest reset rate in %@. Try setting reminders or incentives.";
"focus_on_category" = "Focus on \"%@\" category";
"low_success_rate_suggestion" = "This category has low success rate. Consider reducing difficulty or finding better incentives.";
"build_stronger_incentives" = "Build stronger incentives";
"incentive_suggestion" = "Set clear short-term goals and rewards to make persistence more attractive";
"define_clearer_habit_flow" = "Define clearer habit flow";
"habit_flow_suggestion" = "Create fixed triggers and processes for each habit to increase automatic execution";
"share_your_success" = "Share your success";
"sharing_suggestion" = "You've established good habits. Sharing experiences helps others and reinforces your habits.";
"focus_on_weekday" = "%@ needs special attention";
"weekday_reset_suggestion" = "This day has highest reset rate. Prepare coping strategies and build willpower reserves.";
"performance_level_excellent" = "Excellent";
"performance_level_excellent_description" = "Outstanding performance across all metrics!";
"performance_level_good" = "Good";
"performance_level_good_description" = "Performing well, keep it up!";
"performance_level_average" = "Average";
"performance_level_average_description" = "Doing okay, room for improvement.";
"performance_level_needs_improvement" = "Needs Improvement";
"performance_level_needs_improvement_description" = "Performance needs work, focus on weak areas.";
"performance_level_poor" = "Poor";
"performance_level_poor_description" = "Performance is low, need to revisit strategies.";
"max_score_format" = "%d/%d";
"percentage_format" = "%d%%";
"all_label" = "All";
"mood_trigger_correlation_title" = "Mood-Trigger Correlation";
// Additional keys for StatisticsView
"streak_range_0_7" = "0-7 days";
"streak_range_8_14" = "8-14 days";
"streak_range_15_30" = "15-30 days";
"streak_range_31_60" = "31-60 days";
"streak_range_60_plus" = "60+ days";
"search_options_placeholder" = "Search options";
"refresh_button" = "Refresh";
"persistence_days_factor" = "Persistence Days";
"success_rate_factor" = "Success Rate";
"behavior_consistency_factor" = "Consistency";
"recovery_rate_factor" = "Recovery Rate";
// ContentView Translations
"add_tab" = "Add";
"statistics_tab" = "Stats";
"days_unit_card" = "Days";
"hours_unit_card" = "Hrs";
"minutes_unit_card" = "Min";
"seconds_unit_card" = "Sec";
"reset_text" = "Reset";
"times_count" = "%d times";
// DataModels Translations
"trigger_type_emotion" = "Emotion";
"trigger_type_environment" = "Environment";
"trigger_type_time" = "Time";
"trigger_type_social" = "Social";
"trigger_type_physical" = "Physical";
"mood_type_great" = "Great";
"mood_type_good" = "Good";
"mood_type_neutral" = "Neutral";
"mood_type_bad" = "Bad";
"mood_type_terrible" = "Terrible";
// Search related
"search_habits" = "Search habits";
"close_search" = "Close search";
"open_search" = "Open search";
// HabitRowContent
"created_on_date" = "Created: %@";
// iCloud backup related
"icloud_sync" = "iCloud Sync";
"icloud_backup" = "iCloud Backup";
"icloud_backup_description" = "Enable iCloud sync to backup data to iCloud and restore when changing devices";
"last_sync" = "Last sync";
"sync_status" = "Sync status";
"never_synced" = "Never synced";
"sync_status_idle" = "Idle";
"sync_status_syncing" = "Syncing...";
"sync_status_success" = "Sync success: %@";
"sync_status_error" = "Sync error: %@";
"manual_sync" = "Manual Sync";
"manual_sync_description" = "Manually sync data to iCloud or restore from iCloud";
"sync_to_icloud" = "Sync to iCloud";
"restore_from_icloud" = "Restore from iCloud";
"restore_confirmation_title" = "Confirm Restore";
"restore_confirmation_message" = "Restoring from iCloud will overwrite all current data. Continue?";
"restore" = "Restore";
"error" = "Error";
"icloud_account_unavailable" = "iCloud account unavailable, check your iCloud settings";
"syncing" = "Syncing";
"syncing_message" = "Processing data, please wait...";
// Error messages
"error_code" = "Error code: %d";
"icloud_not_authenticated" = "Not logged into iCloud or iCloud Drive not enabled.\n\nPlease log in to iCloud and ensure iCloud Drive is enabled in device settings.";
"icloud_permission_failure" = "No permission to access iCloud.\n\nPlease enable iCloud access for this app in device settings.";
"icloud_network_error" = "Network error.\n\nPlease check your connection and try again.";
"icloud_service_unavailable" = "iCloud service unavailable.\n\nPlease try again later.";
"icloud_unknown_error" = "Unknown iCloud error.\n\nPlease check your iCloud settings and try again.";
"icloud_no_backup" = "No iCloud backup found.\n\nPlease sync to iCloud first before attempting to restore.";
"syncing_message" = "Syncing data to iCloud, please wait...";
// XMomentStatsView Translations
"stats_overview" = "Focus Overview";
"key_metrics_period" = "Key Metrics This Period";
"focus_count" = "Focus Count";
"total_duration" = "Total Duration";
"average_duration" = "Avg Duration";
"completion_rate" = "Completion Rate";
"focus_quality" = "Focus Quality";
"interruption_quality" = "Interruptions & Overclock";
"interruption_count" = "Interruptions";
"overclock_count" = "Overclock Count";
"average_multiplier" = "Avg Multiplier: %@x";
"duration_distribution" = "Duration Distribution";
"duration_intervals" = "Focus count by duration interval";
"less_than_5min" = "<5min";
"5_to_15min" = "5-15min";
"15_to_25min" = "15-25min";
"25_to_40min" = "25-40min";
"40_to_60min" = "40-60min";
"more_than_60min" = ">60min";
"category_distribution" = "Category Distribution";
"category_time_ratio" = "Category ratio & duration";
"uncategorized" = "Unclassified";
"other_categories" = "+ %d others";
"mood_distribution" = "Mood Distribution";
"mood_count_distribution" = "Focus count by mood";
"focused" = "Focused";
"efficient" = "Efficient";
"calm" = "Calm";
"relaxed" = "Relaxed";
"tired" = "Tired";
"distracted" = "Distracted";
"interrupted" = "Interrupted";
"duration_trend" = "Duration Trend";
"daily_duration_change" = "Daily total duration changes";
"average" = "Avg: %d min";
"focus_duration_minutes" = "Focus duration:";
"focus_count_times" = "Focus count:";
"times_suffix" = "times";
"timeblock_analysis" = "Timeblock Analysis";
"timeblock_distribution" = "Focus distribution across day periods";
"metrics" = "Metrics:";
"count" = "Count";
"duration" = "Duration";
"interruption" = "Interruption";
"best_focus_period" = "Your best focus period is %@, with %d sessions averaging %d minutes each";
"focus_calendar" = "Focus Calendar";
"view_daily_records" = "View daily focus records";
"no_focus_records" = "No focus records this month";
"overclock_stats" = "Overclock Stats";
"overclock_performance" = "Understand your overclock performance";
"overclock_duration" = "Overclock Duration";
"overclock_efficiency_tip" = "Through overclock, you've gained %@ of extra focus time, a %d%% increase in focus efficiency.";
"free_mode_stats" = "Free Mode Stats";
"focus_training_progress" = "Focus Training Progress";
"free_mode_count" = "Free Mode Count";
"best_record" = "Best Record";
"focus_threshold_rate" = "Focus Threshold Rate";
"view_detailed_stats" = "View Detailed Stats";
"streak_stats" = "Streak Stats";
"streak_achievements" = "Streak achievements";
"days_suffix" = "days";
"best_streak_days" = "Best Streak Days";
"streak_new_record" = "Amazing! You've set a new personal streak record. Keep it up!";
"streak_21_days" = "%d consecutive days of focus! You've developed a stable focus habit!";
"streak_14_days" = "Over two weeks of consecutive focus! You're one step away from forming a habit!";
"streak_7_days" = "Over a week of consecutive focus! You're building a good focus habit!";
"streak_3_days" = "%d consecutive days of focus! Keep going and you'll soon form a habit!";
"streak_continue" = "%d consecutive days of focus! Keep it up!";
"dashboard" = "Dashboard";
"data_insights" = "Data Insights";
"correlation_analysis" = "Correlation Analysis";
"calendar_view" = "Calendar View";
"growth_guide" = "Growth Guide";
"xmoment_stats" = "X Moment Stats";
"time_0_6" = "0-6AM";
"time_6_12" = "6-12AM";
"time_12_18" = "12-6PM";
"time_18_24" = "6-12PM";
// XMomentSessionDetailView Translations
"session_details" = "Session Details";
"interrupted" = "Interrupted";
"completed" = "Completed";
"started_at_prefix" = "Started at";
"duration_prefix" = "Duration";
"session_info" = "Session Info";
"session_type" = "Type";
"free_mode" = "Free Mode";
"standard_mode" = "Standard Mode";
"category" = "Category";
"session_id" = "Session ID";
"time_info" = "Time Info";
"start_time" = "Start Time";
"end_time" = "End Time";
"duration_time" = "Duration";
"notes" = "Notes";
"overclock_info" = "Overclock Info";
"overclock_factor" = "Overclock Factor";
"overclock_multiplier" = "x";
"actual_duration" = "Actual Duration";
"stats_info" = "Stats Info";
"xu_earned" = "XU Earned";
"xu_value" = "%.1f";
// XMomentSessionDetailView Example Data
"example_note" = "This is a sample note recording my thoughts and progress during this focus session. The session went well and I completed my planned task goals.";
// XMomentMainBase Translations
"save_xmoment_session_failed" = "Failed to save X Moment session data";
"load_xmoment_session_failed" = "Failed to load X Moment session data";
"recovered_sessions" = "Recovered X Moment sessions from UserDefaults";
"migrated_to_json" = "Migrated X Moment session data from UserDefaults to JSON file";
"migration_failed" = "Failed to migrate X Moment session data";
"delete_xmoment_session_failed" = "Failed to delete X Moment session data";
"save_xmoment_stats_failed" = "Failed to save X Moment user stats data";
// FreeModeMilestone Names
"first_experience" = "First Experience";
"focus_start" = "Focus Start";
"focus_intermediate" = "Focus Intermediate";
"focus_threshold" = "Focus Threshold";
"deep_focus" = "Deep Focus";
"continuous_improvement" = "Continuous Improvement";
"steady_progress" = "Steady Progress";
"focus_habit_formation" = "Focus Habit Formation";
"focus_master" = "Focus Master";
// FreeModeMilestone Descriptions
"complete_first_free_mode" = "Complete first free mode focus";
"reach_5_minutes" = "Reach 5 minutes in free mode";
"reach_15_minutes" = "Reach 15 minutes in free mode";
"reach_30_minutes" = "Reach 30-minute threshold in free mode";
"reach_60_minutes" = "Reach 60 minutes in free mode";
"improve_3_times" = "Improve free mode focus duration 3 consecutive times";
"improve_5_times" = "Improve free mode focus duration 5 consecutive times";
"reach_30_minutes_5_times" = "Reach 30 minutes in free mode 5 times";
"reach_30_minutes_10_times" = "Reach 30 minutes in free mode 10 times";
// MilestoneCelebrationView
"milestone_achieved" = "Achieved";
// XMomentInfoView Translations
"xmoment_title" = "X Moment - Immersive Focus, Redefining Time Value";
"why_choose_xmoment" = "Why Choose X Moment?";
"xmoment_description" = "Tired of mindless scrolling and craving quality focus time? X Moment isn't just another Pomodoro timer, but an immersive focus portal. It helps transform fragmented, addictive time into high-quality moments of deep work, learning or rest, embracing the \"X Do: Less Doing, More Being\" philosophy.";
"how_to_start" = "How to Start & Experience?";
"how_to_use" = "In the app, simply long-press the \"wave\" area (card center) to activate X Moment. Choose your focus duration (15/30/55 minutes, or quick 1 minute start), then say goodbye to cold countdowns. You'll experience a visual meditation: a dynamic \"chaos\" or \"noise\" gradually evolving, clarifying, until presenting a serene and harmonious order (like calm ripples or rhythmic heartbeats), immersing you and making you forget time.";
"focus_value" = "Focus Value: X Units";
"xu_description" = "Each focus session earns valuable \"X Units\". XU transforms abstract willpower into visible, measurable positive action. At the end, you can optionally record mood and category, helping you review and analyze your focus patterns.";
"overclock_reward" = "Overclock Reward System";
"overclock_description" = "After completing the base focus time, X Moment enters \"Overclock\" mode with calm purple visuals and lower frequency. Continuing focus in this state earns 1.5x XU rewards, with higher XU value per minute! Must maintain overclock for at least 5 minutes to activate rewards.";
"overclock_multiplier" = "Overclock Multiplier";
"xu_multiplier" = "x XU";
"minimum_time" = "Minimum Time Req.";
"minutes" = "minutes";
"free_mode_description" = "Free Mode is a special mode designed for users in early stages of focus training. Unlike Standard Mode's strict timing, Free Mode has no interruption concept, allowing users to gradually improve focus ability within their comfort zone.";
"core_features" = "Core Features";
"no_interruption" = "No Interruption: Freely pause during focus, reducing mental pressure";
"milestone_incentive" = "Milestone Incentives: Stage-based achievements to motivate continued focus";
"progress_oriented" = "Progress-Oriented: De-emphasizes XU values, emphasizes time progress and personal growth";
"xu_calculation" = "XU Calculation Principles";
"xu_under_30" = "Under 30 minutes: Lower base XU with small milestone bonuses";
"xu_over_30" = "30+ minutes: Same as Standard Mode, reflecting the focus threshold concept";
"personal_best" = "Personal Best: Extra progress bonus when breaking personal records";
"free_mode_bridge" = "Free Mode is a bridge to efficient focus, helping users transition from \"can't focus\" to \"can focus\", and ultimately to the deep focus state of Standard Mode.";
"focus_theme" = "Focus Interface Theme";
"theme_description" = "Choose your preferred focus recording interface theme for a better atmosphere during your focus process.";
"change_theme" = "Tap to change theme";
"who_for" = "Who's It For?";
"target_audience" = "Designed for those seeking inner calm, quality time, and premium experiences. X Moment isn't just a tool but a sensory and spiritual journey, bringing value and satisfaction beyond mere functionality.";
"core_highlights" = "Core Highlights";
"no_timer" = "No Digital Timer: Unique visualization of time passing, focusing on the present instead of clock anxiety.";
"sensory_experience" = "Premium Sensory Experience: Smooth animations, immersive visuals and optional sounds for an enjoyable experience.";
"quantify_willpower" = "Quantified Willpower: Measure focus results with XU, giving visible rewards to your efforts.";
"being_philosophy" = "\"Being\" Philosophy: Guide users from \"completing tasks\" to \"experiencing the process\".";
"about_xmoment" = "About X Moment";
// MenuView Translations
"focus_record_theme" = "Focus Record Theme";
"xmoment_section" = "X Moment";
"menu_done" = "Done";
// DailyFocusDetailView Translations
"daily_summary" = "Daily Summary";
"session_records" = "Session Records";
"no_focus_today" = "No focus records today.";
"duration_prefix" = "Duration";
"category_prefix" = "Category";
"mood_prefix" = "Mood";
"notes_prefix" = "Notes";
"study" = "Study";
"code_review" = "Code Review";
"write_report" = "Write Report";
"read_docs" = "Read Docs";
// TimingTemplateManager Translations
"timing_template_title" = "Timing Animation Templates";
"mind_aura" = "Mind Aura";
"lofi" = "Lofi";
"journey" = "Journey Flight";
"mind_aura_description" = "Particle animation evolving into heartbeat curve, visualizing focus process";
"lofi_description" = "Lofi Hip Hop style with soft, warm colors and dynamic elements for an immersive focus experience";
"journey_description" = "Flight journey style, from departure to arrival, visualizing the journey of focus time";
"template_preview" = "Template Preview";
// XMomentThemeManager Translations
"theme_apple" = "Apple Style";
"theme_retro" = "Retro Electronic";
"theme_pomodoro_log" = "Pomodoro Log New";
"theme_elegant" = "Elegant Modern";
"theme_neon_cyber" = "Neon Cyber";
"theme_sketch" = "Hand Sketch";
"theme_boarding_pass" = "Boarding Pass";
"theme_apple_description" = "Apple design style focus record theme with clean, clear completion summary interface";
"theme_retro_description" = "Retro electronic device style with texture and unique decorative elements for nostalgia";
"theme_pomodoro_log_description" = "Newly designed pomodoro log theme with retro game style and single-screen ultimate UX";
"theme_elegant_description" = "Premium elegant modern design with sophisticated card layout for ultimate user experience";
"theme_neon_cyber_description" = "Neon cyber style with vibrant colors and tech elements, full of future vibes and energy";
"theme_sketch_description" = "Hand sketch style with paper texture background and graphite lines for authentic notebook feel";
"theme_boarding_pass_description" = "Boarding pass style design simulating real airline boarding pass visuals, visualizing the focus journey";
"theme_preview" = "Theme Preview";
"theme_in_development" = "View in development...";
// XMomentRecordSketch & Shared
"category_work" = "Work";
"category_study" = "Study";
"category_creative" = "Creative";
"category_personal" = "Personal";
"title_focus_completed" = "Focus Completed!";
"label_start_time" = "Start Time";
"label_end_time" = "End Time";
"label_focus_duration" = "Focus Duration";
"label_reward_earned" = "Reward Earned";
"label_overclock_bonus" = "Overclock Bonus";
"label_category" = "Category";
"label_status" = "Status";
"mood_focused" = "Focused";
"mood_efficient" = "Efficient";
"mood_tired" = "Tired";
"mood_distracted" = "Distracted";
"label_notes" = "Notes";
"placeholder_add_notes" = "Add notes...";
"button_skip" = "Skip";
"button_save" = "Save";
// XMomentRecordRetro & Shared
"category_exercise" = "Exercise";
"category_other" = "Other";
"preview_notes_free_mode_record" = "Free mode focus record";
// XMomentRecordElegant & XMomentRecordApple & Shared
"label_this_focus_session" = "This Focus Session";
"placeholder_add_short_notes" = "Enter short notes...";
"duration_format_elegant" = "%dmin %02dsec"; // Specific format for Elegant style
"label_feeling" = "Feeling"; // Used in Apple style
// Xtips.swift Milestone Categories
"milestone_category_quantity" = "Quantity";
"milestone_category_consistency" = "Consistency";
"milestone_category_habit_formation" = "Habit Formation";
"milestone_category_deep_work" = "Deep Work";
"milestone_category_special_moment" = "Special Moment";
// Xtips.swift Milestone Names & Descriptions
"milestone_name_q_count_10" = "Ten Sessions";
"milestone_desc_q_count_10" = "Complete 10 X Moment focus sessions.";
"milestone_name_q_count_50" = "Fifty Sessions";
"milestone_desc_q_count_50" = "Complete 50 X Moment focus sessions.";
"milestone_name_q_count_100" = "Hundred Sessions";
"milestone_desc_q_count_100" = "Complete 100 X Moment focus sessions.";
"milestone_name_q_xu_100" = "100 XU Achieved";
"milestone_desc_q_xu_100" = "Accumulate 100 XU total.";
"milestone_name_q_xu_500" = "500 XU Achieved";
"milestone_desc_q_xu_500" = "Accumulate 500 XU total.";
"milestone_name_q_duration_10h" = "Ten Hour Focus";
"milestone_desc_q_duration_10h" = "Accumulate 10 hours of total focus time.";
"milestone_name_c_streak_3" = "Three Day Streak";
"milestone_desc_c_streak_3" = "Use X Moment for 3 consecutive days.";
"milestone_name_c_streak_7" = "Seven Day Commitment";
"milestone_desc_c_streak_7" = "Use X Moment for 7 consecutive days.";
"milestone_name_c_streak_14" = "Fortnight Focus";
"milestone_desc_c_streak_14" = "Use X Moment for 14 consecutive days.";
"milestone_name_c_streak_30" = "Monthly Focus";
"milestone_desc_c_streak_30" = "Use X Moment for 30 consecutive days.";
"milestone_name_d_duration_record" = "Breaking Limits";
"milestone_desc_d_duration_record" = "Set a new personal record for single X Moment duration.";
"milestone_name_d_overclock_1" = "First Overclock";
"milestone_desc_d_overclock_1" = "Successfully complete your first overclock focus (≥ 5 minutes overclock time).";
"milestone_name_d_overclock_10" = "Overclock Master (10)";
"milestone_desc_d_overclock_10" = "Complete 10 effective overclock focus sessions.";
"milestone_name_s_anniversary_1y" = "Focus Anniversary";
"milestone_desc_s_anniversary_1y" = "Use X Moment for one year.";
"milestone_name_s_newyear_focus" = "New Year's First Focus";
"milestone_desc_s_newyear_focus" = "Complete an X Moment session on New Year's Day.";
"milestone_name_s_midnight_focus" = "Midnight Thinker";
"milestone_desc_s_midnight_focus" = "Complete an X Moment session at midnight (00:00-01:00).";
// Xtips.swift Tips & Guidance
"tip_deep_dialogue" = "Each focus session is a deep dialogue with yourself.";
"tip_less_do_more_be" = "Less doing, more being. Let X Moment help you find the present.";
"tip_time_investment" = "Time is your most valuable resource - invest it with focus.";
"tip_polish_focus" = "Polish your focus like a diamond.";
"tip_feel_inner_power" = "Be still and feel the inner power flowing.";
"guidance_paused_restart" = "Your inner compass senses a pause. Time to restart and light up your focus.";
"guidance_start_today" = "Today, begin with an X Moment to find inner calm.";
"guidance_continue_from_yesterday" = "Continue yesterday's focus - ready to start today's X Moment?";
"guidance_focused_relax" = "You're radiating focus! Remember to give your eyes and mind some rest.";
"guidance_rest_after_focus" = "After intense focus, a short break or walk helps energy flow better.";
"milestone_achieved_format" = "🎉 Milestone achieved: %@! %@";
"guidance_overclock_success_format" = "Excellent! You've completed an overclock focus, earning an extra %@ XU bonus!";
"guidance_long_focus_completed" = "Deep immersion completed! Feel the clarity and calm in your mind now.";
"guidance_save_success_garden" = "Focus recorded, adding greenery to your inner garden.";
"guidance_save_success_investment" = "Each focus session is an investment in a better self.";
"guidance_save_success_keep_going" = "Recording complete! Keep up this awareness and engagement.";
"upcoming_milestone_q_count_10" = "Keep going! Almost at 10 focus sessions!";
"upcoming_milestone_q_count_50" = "Amazing! You're about to reach the 50 focus sessions milestone!";
"upcoming_milestone_q_xu_100" = "Your focus is building strength, nearly at 100 XU!";
"upcoming_milestone_c_streak_7" = "Keep it up! Tomorrow you'll unlock the seven-day commitment badge!";
"upcoming_milestone_c_streak_14" = "Great persistence! Two-week consecutive focus is just ahead!";
// MARK: - XMomentInsightsView Localizations
"view.xmomentInsights.categoryEnum.time" = "Time";
"view.xmomentInsights.categoryEnum.category" = "Category";
"view.xmomentInsights.categoryEnum.mood" = "Mood";
"view.xmomentInsights.categoryEnum.records" = "Records";
"view.xmomentInsights.picker.title" = "Insight Category";
"view.xmomentInsights.time.header.title" = "Time Insights";
"view.xmomentInsights.time.mostFocusedDay.title" = "Most Focused Date";
"view.xmomentInsights.time.mostFocusedDay.detailFormat" = "%@ focus sessions";
"view.xmomentInsights.time.longestDurationDay.title" = "Longest Duration Date";
"view.xmomentInsights.time.longestDurationDay.detailFormat" = "Total duration %@";
"view.xmomentInsights.time.densestTimeSlot.title" = "Highest Density Timeframe";
"view.xmomentInsights.time.densestTimeSlot.detailFormat" = "%@ sessions total";
"view.xmomentInsights.time.longestTimeSlot.title" = "Longest Duration Timeframe";
"view.xmomentInsights.time.longestTimeSlot.detailFormat" = "Total duration %@";
"view.xmomentInsights.time.noFocusDays.title" = "No Focus Days This Week";
"view.xmomentInsights.category.header.title" = "Category Insights";
"view.xmomentInsights.category.mostUsed.title" = "Most Used Category";
"view.xmomentInsights.category.mostUsed.detailFormat" = "%@ focus sessions";
"view.xmomentInsights.category.longestDuration.title" = "Longest Duration Category";
"view.xmomentInsights.category.longestDuration.detailFormat" = "Total duration %@";
"view.xmomentInsights.category.highestAvgDuration.title" = "Highest Avg Duration Category";
"view.xmomentInsights.category.highestAvgDuration.detailFormat" = "Average %@";
"view.xmomentInsights.category.lowestInterruption.title" = "Lowest Interruption Category";
"view.xmomentInsights.category.lowestInterruption.detailFormat" = "Only %.1f%% interruption rate";
"view.xmomentInsights.mood.header.title" = "Mood Insights";
"view.xmomentInsights.mood.mostCommon.title" = "Most Common Mood";
"view.xmomentInsights.mood.mostCommon.detailFormat" = "Occurred %@ times";
"view.xmomentInsights.mood.longestDuration.title" = "Longest Duration Mood";
"view.xmomentInsights.mood.longestDuration.detailFormat" = "Total duration %@";
"view.xmomentInsights.mood.distributionChart.title" = "Mood Distribution";
"view.xmomentInsights.mood.distributionChart.angleValueLabel" = "Count";
"view.xmomentInsights.mood.distributionChart.categoryValueLabel" = "Mood Category";
"view.xmomentInsights.mood.distributionChart.noData" = "No mood data yet";
"view.xmomentInsights.xu.header.title" = "XU Insights";
"view.xmomentInsights.xu.highestXuSession.title" = "Highest XU Session";
"view.xmomentInsights.xu.highestXuSession.valueFormat" = "%.1f XU";
"view.xmomentInsights.xu.highestXuSession.detailFormat" = "%@ · %@";
"view.xmomentInsights.xu.highestEfficiencySession.title" = "Highest Efficiency Session";
"view.xmomentInsights.xu.highestEfficiencySession.valueFormat" = "%.2f XU/hour";
"view.xmomentInsights.xu.highestEfficiencySession.detailFormat" = "%@ · %@";
"view.xmomentInsights.xu.mostXuDay.title" = "Most XU Date";
"view.xmomentInsights.xu.mostXuDay.detailFormat" = "Total %.1f XU";
"view.xmomentInsights.xu.mostXuCategory.title" = "Most XU Category";
"view.xmomentInsights.xu.mostXuCategory.detailFormat" = "Total %.1f XU";
"view.xmomentInsights.records.header.title" = "Record Insights";
"view.xmomentInsights.records.longestSession.title" = "Longest Session";
"view.xmomentInsights.records.longestSession.mode.free" = "Free Mode";
"view.xmomentInsights.records.longestSession.mode.standard" = "Standard Mode";
"view.xmomentInsights.records.longestSession.detailFormat" = "%@ · %@";
"view.xmomentInsights.records.longestOverclock.title" = "Longest Overclock";
"view.xmomentInsights.records.longestOverclock.detailFormat" = "%@ · %.1fx factor";
"view.xmomentInsights.records.bestStreak.title" = "Best Streak Days";
"view.xmomentInsights.records.bestStreak.valueFormat" = "%@ days";
"view.xmomentInsights.records.bestStreak.detail" = "Consistency is key to success";
"view.xmomentInsights.records.mostSessionsDay.title" = "Most Sessions in a Day";
"view.xmomentInsights.records.mostSessionsDay.valueFormat" = "%@ sessions";
"view.xmomentInsights.records.mostSessionsDay.detailFormat" = "Date: %@";
"view.xmomentInsights.records.longestDayDuration.title" = "Longest Day Duration";
"view.xmomentInsights.records.longestDayDuration.detailFormat" = "Date: %@";
"common.category.unclassified" = "Unclassified";
"view.xmomentInsights.time.timeSlot.morning" = "Morning (0-6AM)";
"view.xmomentInsights.time.timeSlot.forenoon" = "Forenoon (6-12AM)";
"view.xmomentInsights.time.timeSlot.afternoon" = "Afternoon (12-6PM)";
"view.xmomentInsights.time.timeSlot.evening" = "Evening (6-12PM)";
"common.durationFormat.hoursMinutes" = "%dh %dm";
"common.durationFormat.minutesSeconds" = "%dm %ds";
"common.durationFormat.seconds" = "%ds";
"common.dateFormat.monthDay" = "MMM d";
// MARK: - XMomentDashboardView Localizations
"view.xmomentDashboard.type.standardMode" = "Standard";
"view.xmomentDashboard.type.freeMode" = "Free";
"view.xmomentDashboard.type.overclockIncluded" = "Inc. Overclock";
"view.xmomentDashboard.type.overclockTime" = "Overclock Time";
"view.xmomentDashboard.type.overallAverage" = "Overall Avg";
"view.xmomentDashboard.type.longestRecord" = "Longest Record";
"view.xmomentDashboard.type.shortestRecord" = "Shortest Record";
"view.xmomentDashboard.type.overclockRecord" = "Overclock Record";
"view.xmomentDashboard.overview.title" = "Focus Overview";
"view.xmomentDashboard.overview.totalSessions" = "Total Sessions";
"view.xmomentDashboard.overview.totalDuration" = "Total Duration";
"view.xmomentDashboard.overview.totalXu" = "Total XU";
"view.xmomentDashboard.focusSessions.title" = "Focus Session Distribution";
"view.xmomentDashboard.chartLabel.count" = "Count";
"view.xmomentDashboard.chartLabel.modeType" = "Mode Type";
"view.xmomentDashboard.focusSessions.annotationFormat" = "%@";
"view.xmomentDashboard.focusSessions.legendFormat" = "%@";
"view.xmomentDashboard.noFocusData" = "No focus data yet";
"view.xmomentDashboard.focusDuration.title" = "Focus Duration Distribution";
"view.xmomentDashboard.chartLabel.durationHours" = "Duration (hours)";
"view.xmomentDashboard.chartLabel.type" = "Type";
"view.xmomentDashboard.focusDuration.totalFormat" = "Total focus duration: %@";
"view.xmomentDashboard.noDurationData" = "No duration data yet";
"view.xmomentDashboard.interruptions.title" = "Interruption Stats";
"view.xmomentDashboard.interruptions.totalCount" = "Total Interruptions";
"view.xmomentDashboard.interruptions.rateFormat" = "%.1f%%";
"view.xmomentDashboard.interruptions.totalRate" = "Total Interruption Rate";
"view.xmomentDashboard.interruptions.modeRateComparisonTitle" = "Mode Interruption Rates";
"view.xmomentDashboard.interruptions.modeRateDetailFormat" = "%.1f%% (%@/%@)";
"view.xmomentDashboard.averageDuration.title" = "Average Focus Duration";
"view.xmomentDashboard.chartLabel.durationMinutes" = "Duration (minutes)";
"view.xmomentDashboard.extremeDurations.title" = "Focus Records";
"view.xmomentDashboard.extremeDurations.chartLabel.recordType" = "Record Type";
"view.xmomentDashboard.extremeDurations.longestRecordTitle" = "Longest Focus Record";
"view.xmomentDashboard.extremeDurations.overclockRecordTitle" = "Longest Overclock Record";
"view.xmomentDashboard.extremeDurations.shortestRecordTitle" = "Shortest Complete Record";
"view.xmomentDashboard.extremeDurations.detailFormat" = "%@ · %@";
"common.durationFormat.minutes" = "%d minutes";
"common.durationFormat.hoursRounded" = "%d hours";
"common.durationFormat.minutesRounded" = "%d minutes";
"common.dateFormat.monthDayTime" = "MM-dd HH:mm";
"view.xmomentDashboard.extremeDurations.overclockFactorFormat" = "Overclock factor: %@x";
// MARK: - XMomentCorrelationView Localizations
"view.correlation.analysisType.timeInterruption" = "Time-Interruption";
"view.correlation.analysisType.categoryDuration" = "Category-Duration";
"view.correlation.analysisType.moodDuration" = "Mood-Duration";
"view.correlation.analysisType.weekdayFocus" = "Weekday-Focus";
"view.correlation.analysisType.customCorrelation" = "Custom Analysis";
"view.correlation.picker.title" = "Correlation Analysis Type";
"view.correlation.header.title" = "Correlation Analysis";
"view.correlation.header.description" = "Discover connections between dimensions and understand your focus patterns";
"view.correlation.timeInterruption.title" = "Time & Interruption Rate";
"view.correlation.chartLabel.timeSlot" = "Time Slot";
"view.correlation.chartLabel.interruptionRate" = "Interruption Rate";
"view.correlation.percentageFormat.int" = "%.0f%%";
"view.correlation.chartLabel.averageInterruptionRate" = "Avg Interruption Rate";
"view.correlation.chartLabel.average" = "Average";
"view.correlation.percentageFormat.axis" = "%d%%";
"view.correlation.noDataAvailable" = "Not enough data for correlation analysis";
"view.correlation.insights.title" = "Insights";
"view.correlation.timeInterruption.insight.best" = "**Best focus period**: %@, only %d%% interruption rate. Schedule important tasks here.";
"view.correlation.timeInterruption.insight.worst" = "**Distraction-prone period**: %@, high %d%% interruption rate. May need extra focus techniques.";
"view.correlation.timeInterruption.insight.suggestion" = "Understanding your biological clock and working with it can significantly improve focus efficiency. Adjust your schedule to leverage low-interruption golden hours.";
"view.correlation.categoryDuration.title" = "Category & Focus Duration";
"view.correlation.chartLabel.avgDurationMinutes" = "Avg Focus Duration (min)";
"view.correlation.chartLabel.category" = "Category";
"view.correlation.categoryDuration.legend.size" = "Dot size = Focus count";
"view.correlation.categoryDuration.legend.lowInterruption" = "Low interruption";
"view.correlation.categoryDuration.legend.highInterruption" = "High interruption";
"view.correlation.categoryDuration.insight.longest" = "**Longest focus category**: \\\"%@\\\", averaging %@ per session.";
"view.correlation.categoryDuration.insight.lowestInterruption" = "**Lowest interruption category**: \\\"%@\\\", %d%% interruption rate.";
"view.correlation.categoryDuration.insight.suggestion" = "Different task types need different focus strategies. For easily interrupted tasks, try Pomodoro technique or environment adjustments.";
"view.correlation.moodDuration.title" = "Mood & Focus Duration";
"view.correlation.chartLabel.mood" = "Mood";
"view.correlation.chartLabel.moodCategory" = "Mood Category";
"view.correlation.moodDuration.sampleCount" = "Sample size: %@ focus records";
"view.correlation.moodDuration.insight.comparison" = "Average focus duration is longest when feeling \\\"%@\\\" at %@, and shortest when feeling \\\"%@\\\" at only %@.";
"view.correlation.moodDuration.insight.suggestion" = "Emotional state significantly affects focus quality. Before important tasks, try brief meditation, deep breathing, or positive thinking to adjust your mood.";
"view.correlation.weekdayFocus.title" = "Weekday & Focus Performance";
"view.correlation.weekdayFocus.countSubtitle" = "Focus Count Distribution";
"view.correlation.chartLabel.weekday" = "Weekday";
"view.correlation.chartLabel.focusCount" = "Focus Count";
"view.correlation.weekdayFocus.durationSubtitle" = "Average Duration Distribution";
"view.correlation.chartLabel.weeklyAverage" = "Weekly Average";
"view.correlation.chartLabel.weeklyAverage.short" = "Weekly Avg";
"view.correlation.weekdayFocus.insight.mostFrequent" = "**Most frequent focus day**: %@, averaging %d sessions.";
"view.correlation.weekdayFocus.insight.longestDuration" = "**Longest average duration day**: %@, averaging %@.";
"view.correlation.weekdayFocus.insight.suggestion" = "Understanding your weekly focus peaks and valleys helps better plan tasks. Consider scheduling more challenging work on days when your focus ability is stronger.";
"view.correlation.custom.title" = "Custom Correlation Analysis";
"view.correlation.custom.description" = "Future versions will support custom selection of any two dimensions for correlation analysis. Stay tuned!";
"view.correlation.custom.feedbackPrompt" = "Which dimension correlations would you most like to see? Let us know in feedback!";
"view.correlation.generalInsights.title" = "What's Correlation Analysis For?";
"view.correlation.generalInsights.point1" = "Correlation analysis reveals relationships between different factors, helping you discover hidden focus patterns.";
"view.correlation.generalInsights.point2" = "Understanding these correlations lets you make smarter decisions, like choosing optimal focus times and avoiding distracting situations.";
"view.correlation.generalInsights.point3" = "As data accumulates, analysis becomes more accurate and personalized, continuously optimizing your focus strategy.";
"common.weekday.sun" = "Sunday";
"common.weekday.mon" = "Monday";
"common.weekday.tue" = "Tuesday";
"common.weekday.wed" = "Wednesday";
"common.weekday.thu" = "Thursday";
"common.weekday.fri" = "Friday";
"common.weekday.sat" = "Saturday";
"common.durationFormat.hoursMinutesVar" = "%@h %@m";
"common.durationFormat.minutesVar" = "%@m";
"common.unit.minute" = "min";

// FreeToStandardTransitionView Translations
"transition.readyForChallenge" = "You're Ready for a New Challenge!";
"transition.focusProgressTitle" = "Focus Training Progress";
"transition.readiness" = "Readiness";
"transition.standardModeReadyTitle" = "Standard Mode Ready Conditions";
"transition.condition.reach30Min5Times" = "Reach 30 minutes at least 5 times";
"transition.condition.longestDuration45Min" = "Longest focus duration reaches 45 minutes";
"transition.condition.complete10FreeMode" = "Complete at least 10 free mode sessions";
"transition.congratsMessage" = "Congratulations! Your focus ability has reached a level ready for Standard Mode. Standard Mode offers a more structured focus experience and richer reward mechanisms.";
"transition.continueFreeModeMessage" = "Continue using Free Mode to develop your focus ability. When you meet the conditions above, you'll be ready to try Standard Mode.";
"transition.learnStandardModeButton" = "Learn About Standard Mode";
"transition.standardModeInfoTitle" = "Standard Mode Introduction";
"transition.feature.structuredFocusTitle" = "Structured Focus";
"transition.feature.structuredFocusDesc" = "Standard Mode offers three preset durations: 15, 25, and 55 minutes, helping you maintain more planned focus sessions.";
"transition.feature.overclockRewardTitle" = "Overclock Rewards";
"transition.feature.overclockRewardDesc" = "After completing the base time, enter overclock state to earn 1.5x XU rewards, encouraging you to extend your focus time.";
"transition.feature.higherEfficiencyTitle" = "Higher Efficiency";
"transition.feature.higherEfficiencyDesc" = "Research shows that structured focus time significantly improves work and study efficiency, helping you better complete tasks.";
"transition.feature.focusStatsTitle" = "Focus Statistics";
"transition.feature.focusStatsDesc" = "Access more detailed focus data analysis, including overclock time and interruption analysis, helping you continuously improve.";
"transition.tip.switchModes" = "Tip: You can switch between the two modes at any time, selecting the most suitable focus method based on your state.";
"transition.understoodButton" = "I Understand";
"transition.closeButton" = "Close";

// FreeModeTrendView Translations
"freeTrend.picker.timeRange" = "Time Range";
"chart.axis.date" = "Date";
"chart.axis.durationMinutes" = "Duration (minutes)";
"freeTrend.chart.focusThreshold" = "Focus Threshold";
"freeTrend.analysis.title" = "Focus Progress Analysis";
"freeTrend.analysis.trend.insufficientData" = "Insufficient data for trend analysis";
"freeTrend.analysis.trend.steadyIncrease" = "Focus ability steadily increasing";
"freeTrend.analysis.trend.slightIncrease" = "Focus ability slightly improving";
"freeTrend.analysis.trend.stable" = "Focus ability remains stable";
"freeTrend.analysis.trend.decrease" = "Focus ability decreasing, needs attention";
"freeTrend.analysis.thresholdRateFormat" = "Focus threshold achievement rate: %d%%";
"freeTrend.analysis.comparison.stable" = "Stable compared to last week";
"freeTrend.analysis.comparison.improvedFormat" = "Improved by %d minutes compared to last week";
"freeTrend.analysis.comparison.decreasedFormat" = "Decreased by %d minutes compared to last week";
"freeTrend.suggestion.expert" = "You've mastered focus ability expertly! Try Standard Mode for more challenges!";
"freeTrend.suggestion.good" = "Your focus ability is quite good, keep it up and try longer focus periods.";
"freeTrend.suggestion.improving" = "You're gradually building focus habits, try reaching the 30-minute threshold more frequently.";
"freeTrend.suggestion.beginner" = "Focus is a skill that can be cultivated, start with small goals and gradually increase your focus duration.";

// FreeModeResultView Translations
"freeResult.title" = "Focus Completed!";
"freeResult.newRecordBanner" = "New Personal Best Record!";
"freeResult.yourProgress" = "Your Progress";
"freeResult.rewardEarned" = "Reward Earned";
"freeResult.reward.baseFormat" = "Base: %.1f XU";
"freeResult.reward.milestoneFormat" = "Milestone: %.1f XU";
"freeResult.reward.progressFormat" = "Progress bonus: %.1f XU";
"freeResult.button.again" = "Focus Again";
"common.duration.underMinute" = "Less than a minute";
"common.duration.minutesFormat" = "%d minutes";
"freeResult.comparison.improvedPercentFormat" = "Focused %@ longer than last time, a %d%% improvement!";
"freeResult.comparison.improvedSignificantFormat" = "Focused %@ longer than last time, significant progress!";
"freeResult.comparison.improvedNoticeableFormat" = "Focused %@ longer than last time, noticeable improvement!";
"freeResult.comparison.improvedKeepGoingFormat" = "Focused %@ longer than last time, keep going!";
"freeResult.comparison.lessKeepTryingFormat" = "Focused for %@, keep trying!";
"freeResult.comparison.sameStable" = "Same focus duration as last time, maintaining stability!";
"freeResult.encourage.newRecord.awesome" = "Awesome! You've set a new personal record, your focus ability is steadily improving!";
"freeResult.encourage.newRecord.breakthrough" = "Breakthrough yourself! Every improvement is an exercise for your focus ability.";
"freeResult.encourage.newRecord.newHeight" = "New height! Your focus ability continues to grow.";
"freeResult.encourage.under5.everyMinute" = "Every minute counts as progress, try to maintain longer next time.";
"freeResult.encourage.under5.practice" = "Focus is a skill that requires constant practice.";
"freeResult.encourage.under5.goodStart" = "Good start! Try to reach the 5-minute milestone next time.";
"freeResult.encourage.under15.goodTry" = "Good try! Your focus ability is developing.";
"freeResult.encourage.under15.keepPracticing" = "Keep practicing, 15-minute focus will be more effective.";
"freeResult.encourage.under15.strengthenWillpower" = "Each focus session strengthens your willpower.";
"freeResult.encourage.under30.nearThreshold" = "Getting close to the focus threshold! 30 minutes is an important milestone.";
"freeResult.encourage.under30.improving" = "Your focus ability is improving, keep it up!";
"freeResult.encourage.under30.goodFocus" = "Good focus! Try to break through the 30-minute threshold next time.";
"freeResult.encourage.under45.congratsThreshold" = "Congrats on passing the focus threshold! You now have basic focus ability.";
"freeResult.encourage.under45.valuable" = "Focus over 30 minutes is highly valuable, keep it up!";
"freeResult.encourage.under45.tryStandard" = "You can try Standard Mode now, more challenges await you there.";
"freeResult.encourage.over45.deepFocus" = "Deep focus! Your focus ability is already quite remarkable.";
"freeResult.encourage.over45.outstanding" = "Outstanding performance! You've mastered the art of focus.";
"freeResult.encourage.over45.readyForStandard" = "You're ready for bigger challenges, try Standard Mode!";

// FreeModeCalendarView Translations
"freeCalendar.viewDetailsFormat" = "View focus details for %@";
"freeCalendar.progress.thisMonth" = "This Month's Progress";
"freeCalendar.progress.totalCount" = "Total";
"freeCalendar.progress.averageDuration" = "Average";
"freeCalendar.progress.thresholdReached" = "Translation";
"freeCalendar.progress.longestDuration" = "Longest";
"freeCalendar.progress.accumulatedKeepGoingFormat" = "Accumulated %@ of focus this month, keep going!";

// Language Picker
"simplified_chinese" = "Simplified Chinese";
"traditional_chinese" = "Traditional Chinese";
"english" = "English";

// Moods
"mood_tired" = "Tired";
"mood_distracted" = "Distracted";
"mood_focused" = "Focused";

// Categories
"category_study" = "Study";
"category_exercise" = "Exercise";
"category_other" = "Other";

// Stats View Tabs
"view.stats.tab.dashboard" = "Dashboard";
"view.stats.tab.insights" = "Insights";
"view.stats.tab.correlation" = "Correlation";
"view.stats.tab.calendar" = "Calendar";
"view.stats.tab.trend" = "Trend";
"view.stats.tab.guide" = "Guide";

// --- HabitListView Willpower Section --- 
// Menu Items
"willpower.menu.stats" = "Stats";
"willpower.menu.template" = "Template";
"willpower.menu.theme" = "Theme";
"willpower.menu.about" = "About X Moment";
// Completion View
"willpower.completion.title" = "Focus Complete!";
"willpower.completion.skip" = "Skip";
"willpower.completion.record" = "Record";
// Metrics
"willpower.metric.totalXu" = "Total XU";
"willpower.metric.todayFormat" = "%@ Today";
"willpower.metric.totalFocusHours" = "Total Focus (h)";
"willpower.metric.thisWeekFormat" = "%@ This Week";
// Milestones
"willpower.milestone.5min" = "🎉 5 minutes reached! The first step of focus.";
"willpower.milestone.10min" = "👍 10 minutes reached! Keep the rhythm.";
"willpower.milestone.15min" = "💪 15 minutes reached! Focus power is increasing.";
"willpower.milestone.20min" = "🌟 20 minutes reached! Approaching the focus threshold.";
"willpower.milestone.30min" = "🏆 Congrats on reaching the 30-minute focus threshold!";
"willpower.milestone.45min" = "🔥 45 minutes! Your focus is impressive.";
"willpower.milestone.60min" = "🎖️ One hour! You've mastered the art of deep focus.";
"willpower.milestone.multipleOf15minFormat" = "🚀 %d minutes! Sustained focus is key to success.";
"willpower.milestone.genericFormat" = "🏁 %d minutes! Keep it up.";
"willpower.milestone.nameFormat" = "Free Mode %d min";
// Tips
"willpower.tip.dialogue" = "Every focus session is a deep dialogue with your inner self.";
"willpower.tip.less_is_more" = "Do less, be more. Let X Moment help you find the present.";
"willpower.tip.invest_time" = "Time is the most precious resource; invest it with focus.";
"willpower.tip.polish_focus" = "Polish your focus like polishing a diamond.";
"willpower.tip.feel_power" = "Quiet down and feel the inner power flowing.";
"willpower.tip.default" = "Focus is the bridge to deep work and inner peace."; // Default tip

// --- CloudBackupManager --- 
"cloudbackup.error.internal" = "Internal error";
"cloudbackup.error.unavailable" = "iCloud is unavailable or the user is not logged in";
"cloudbackup.error.restoreInProgress" = "Restore operation already in progress";
"cloudbackup.error.internalRestoreError" = "Internal error occurred during restore";
"cloudbackup.error.restoreFailedFormat" = "iCloud restore failed: %@";
"cloudbackup.error.backupFailedNoFiles" = "Failed to back up any files";
"cloudbackup.error.restoreFailedNoFiles" = "Failed to restore any files from iCloud";
// ... existing code ...
"cloudbackup.error.restoreFailedNoFiles" = "Failed to restore any files from iCloud";

// --- CloudBackupView --- 
"cloudbackup.view.title" = "iCloud Backup";
"cloudbackup.view.enableLabel" = "Enable iCloud Backup";
"cloudbackup.view.lastBackupLabel" = "Last Backup";
"cloudbackup.view.statusLabel" = "Status";
"cloudbackup.button.backupNow" = "Backup Now";
"cloudbackup.button.restore" = "Restore from iCloud";
"cloudbackup.section.aboutTitle" = "About iCloud Backup";
"cloudbackup.section.aboutDesc1" = "When iCloud Backup is enabled, your habit records and settings are automatically backed up to your iCloud account.";
"cloudbackup.section.aboutDesc2" = "This allows you to sync data between devices and restore data after reinstalling the app.";
"cloudbackup.section.aboutDesc3" = "All data, including media files, is backed up using CloudKit. Please ensure you are logged into your iCloud account.";
"cloudbackup.alert.backupConfirmTitle" = "Confirm Backup";
"cloudbackup.alert.cancelButton" = "Cancel";
"cloudbackup.alert.backupButton" = "Backup";
"cloudbackup.alert.backupConfirmMessage" = "Do you want to back up your data to iCloud? This will overwrite the previous backup.";
"cloudbackup.alert.restoreConfirmTitle" = "Confirm Restore";
"cloudbackup.alert.restoreButton" = "Restore";
"cloudbackup.alert.restoreConfirmMessage" = "Do you want to restore data from iCloud? This will overwrite all data on this device.";
"cloudbackup.alert.restoreSuccessTitle" = "Restore Successful";
"cloudbackup.alert.okButton" = "OK";
"cloudbackup.alert.restoreSuccessMessage" = "Data has been successfully restored from iCloud.";
"cloudbackup.alert.restoreFailureTitle" = "Restore Failed";
"cloudbackup.alert.restoreFailureDefaultMessage" = "Failed to restore data from iCloud. Please try again later.";
"cloudbackup.alert.backupSuccessTitle" = "Backup Successful";
"cloudbackup.alert.backupSuccessMessage" = "Data has been successfully backed up to iCloud.";
"cloudbackup.alert.sizeWarningTitle" = "Backup Files Too Large";
"cloudbackup.alert.sizeWarningMessage" = "Some files exceed the backup size limit and could not be backed up.";
"cloudbackup.status.processingJson" = "Processing JSON files...";
"cloudbackup.status.processingMedia" = "Processing media files...";
"cloudbackup.status.processingSettings" = "Processing app settings...";
"cloudbackup.status.preparingBackup" = "Preparing backup...";
"cloudbackup.status.preparingRestore" = "Preparing restore...";
"cloudbackup.error.checkUnavailable" = "iCloud is unavailable. Please ensure you are logged into your iCloud account and CloudKit is enabled.";
"cloudbackup.error.backupFailedDefault" = "Backup failed. Please ensure iCloud is set up correctly and try again.";
"cloudbackup.error.restoreFailedDefault" = "An unknown error occurred during restore.";
"cloudbackup.view.neverBackedUp" = "Never backed up";
"cloudbackup.view.backupAvailable" = "Backup available";

// ... existing code ...
"trigger_details" = "Trigger Details";
"no_trigger_records" = "No Trigger Records";
"no_chart_data" = "Not Enough Data for Chart";
"trigger_frequency" = "Trigger Frequency";
"top_five_triggers" = "Showing Top 5 Triggers";

// --- Trigger Categories ---
// Main Categories
"emotional_category_zh" = "Emotional & Psychological";
"environmental_category_zh" = "Environmental & Social";
"physical_category_zh" = "Physical Factors";
"events_category_zh" = "Specific Events";

// Sub Categories
"negative_emotions_zh" = "Negative Emotions";
"positive_emotions_zh" = "Positive Emotions";
"mental_states_zh" = "Mental States";
"specific_locations_zh" = "Specific Locations";
"specific_times_zh" = "Specific Times";
"social_occasions_zh" = "Social Occasions";
"media_influence_zh" = "Media Influence";
"withdrawal_symptoms_zh" = "Withdrawal Symptoms";
"physical_conditions_zh" = "Physical Conditions";
"physiological_needs_zh" = "Physiological Needs";
"conflicts_zh" = "Conflicts";
"failures_setbacks_zh" = "Failures & Setbacks";
"major_events_zh" = "Major Events";
"no_obvious_reason_zh" = "No Obvious Reason";

// Moods
"great_mood_zh" = "Great";
"good_mood_zh" = "Good";
"neutral_mood_zh" = "Neutral";
"bad_mood_zh" = "Bad";
"terrible_mood_zh" = "Terrible";

// --- Specific Trigger Options ---
// Negative Emotions
"stress_zh" = "Stress";
"anxiety_zh" = "Anxiety";
"depression_zh" = "Depression";
"anger_zh" = "Anger";
"loneliness_zh" = "Loneliness";
"boredom_zh" = "Boredom";
"guilt_zh" = "Guilt";
"jealousy_zh" = "Jealousy";
"fear_zh" = "Fear";

// Positive Emotions
"excitement_zh" = "Excitement";
"joy_zh" = "Joy";
"relaxation_zh" = "Relaxation";
"comfort_zh" = "Comfort";
"confidence_zh" = "Confidence";
"satisfaction_zh" = "Satisfaction";

// Mental States
"fatigue_zh" = "Fatigue";
"drowsiness_zh" = "Drowsiness";
"impulsiveness_zh" = "Impulsiveness";
"lack_of_control_zh" = "Lack of Control";
"rationalization_zh" = "Rationalization";
"nostalgia_zh" = "Nostalgia";
"craving_zh" = "Craving";
"temptation_zh" = "Temptation";

// Locations
"bar_zh" = "Bar";
"ktv_zh" = "KTV/Karaoke";
"restaurant_zh" = "Restaurant";
"smoking_area_zh" = "Smoking Area";
"convenience_store_zh" = "Convenience Store";
"supermarket_zh" = "Supermarket";
"home_zh" = "Home";
"office_zh" = "Office";
"usual_places_zh" = "Usual Places";

// Times
"morning_zh" = "Morning";
"noon_zh" = "Noon";
"evening_zh" = "Evening";
"late_night_zh" = "Late Night";
"weekend_zh" = "Weekend";
"holidays_zh" = "Holidays";
"work_break_zh" = "Work Break";
"after_meals_zh" = "After Meals";
"before_bed_zh" = "Before Bed";

// Social Contexts
"friend_gathering_zh" = "Friend Gathering";
"family_dinner_zh" = "Family Dinner";
"social_pressure_zh" = "Social Pressure";
"peer_influence_zh" = "Peer Influence";
"colleague_influence_zh" = "Colleague Influence";
"unfamiliar_environment_zh" = "Unfamiliar Environment";

// Media Influence
"advertising_zh" = "Advertising";
"movies_zh" = "Movies";
"tv_shows_zh" = "TV Shows";
"social_media_zh" = "Social Media";
"easily_accessible_zh" = "Easily Accessible";

// Withdrawal Symptoms
"headache_zh" = "Headache";
"nausea_zh" = "Nausea";
"insomnia_zh" = "Insomnia";
"irritability_zh" = "Irritability";
"poor_concentration_zh" = "Poor Concentration";
"strong_urge_zh" = "Strong Urge";

// Body Conditions
"illness_zh" = "Illness";
"pain_zh" = "Pain";
"hormonal_changes_zh" = "Hormonal Changes";
"menstruation_zh" = "Menstruation";
"weight_changes_zh" = "Weight Changes";

// Physiological Needs
"hunger_zh" = "Hunger";
"thirst_zh" = "Thirst";
"sleep_deprivation_zh" = "Sleep Deprivation";
"physical_exhaustion_zh" = "Physical Exhaustion";

// Conflicts
"family_conflict_zh" = "Family Conflict";
"work_conflict_zh" = "Work Conflict";
"interpersonal_conflict_zh" = "Interpersonal Conflict";
"disagreement_zh" = "Disagreement";

// Failures
"work_failure_zh" = "Work Failure";
"study_setback_zh" = "Study Setback";
"plan_disruption_zh" = "Plan Disruption";
"unmet_expectations_zh" = "Unmet Expectations";

// Life Events
"unemployment_zh" = "Unemployment";
"breakup_zh" = "Breakup";
"moving_zh" = "Moving";
"bereavement_zh" = "Bereavement";
"major_changes_zh" = "Major Changes";

// No Obvious Trigger
"habitual_behavior_zh" = "Habitual Behavior";
"inner_desire_zh" = "Inner Desire";
"no_obvious_reason_zh" = "No Obvious Reason";
"custom_reason_zh" = "Custom";

// --- International Versions of Triggers ---
// These are used when displaying the triggers in the UI
"stress" = "Stress";
"anxiety" = "Anxiety";
"depression" = "Depression";
"anger" = "Anger";
"loneliness" = "Loneliness";
"boredom" = "Boredom";
"guilt" = "Guilt";
"jealousy" = "Jealousy";
"fear" = "Fear";
"excitement" = "Excitement";
"joy" = "Joy";
"relaxation" = "Relaxation";
"comfort" = "Comfort";
"confidence" = "Confidence";
"satisfaction" = "Satisfaction";
"fatigue" = "Fatigue";
"drowsiness" = "Drowsiness";
"impulsiveness" = "Impulsiveness";
"lack_of_control" = "Lack of Control";
"rationalization" = "Rationalization";
"nostalgia" = "Nostalgia";
"craving" = "Craving";
"temptation" = "Temptation";
"bar" = "Bar";
"ktv" = "KTV/Karaoke";
"restaurant" = "Restaurant";
"smoking_area" = "Smoking Area";
"convenience_store" = "Convenience Store";
"supermarket" = "Supermarket";
"home" = "Home";
"office" = "Office";
"usual_places" = "Usual Places";
"morning" = "Morning";
"noon" = "Noon";
"evening" = "Evening";
"late_night" = "Late Night";
"weekend" = "Weekend";
"holidays" = "Holidays";
"work_break" = "Work Break";
"after_meals" = "After Meals";
"before_bed" = "Before Bed";
"friend_gathering" = "Friend Gathering";
"family_dinner" = "Family Dinner";
"social_pressure" = "Social Pressure";
"peer_influence" = "Peer Influence";
"colleague_influence" = "Colleague Influence";
"unfamiliar_environment" = "Unfamiliar Environment";
"advertising" = "Advertising";
"movies" = "Movies";
"tv_shows" = "TV Shows";
"social_media" = "Social Media";
"easily_accessible" = "Easily Accessible";
"headache" = "Headache";
"nausea" = "Nausea";
"insomnia" = "Insomnia";
"irritability" = "Irritability";
"poor_concentration" = "Poor Concentration";
"strong_urge" = "Strong Urge";
"illness" = "Illness";
"pain" = "Pain";
"hormonal_changes" = "Hormonal Changes";
"menstruation" = "Menstruation";
"weight_changes" = "Weight Changes";
"hunger" = "Hunger";
"thirst" = "Thirst";
"sleep_deprivation" = "Sleep Deprivation";
"physical_exhaustion" = "Physical Exhaustion";
"family_conflict" = "Family Conflict";
"work_conflict" = "Work Conflict";
"interpersonal_conflict" = "Interpersonal Conflict";
"disagreement" = "Disagreement";
"work_failure" = "Work Failure";
"study_setback" = "Study Setback";
"plan_disruption" = "Plan Disruption";
"unmet_expectations" = "Unmet Expectations";
"unemployment" = "Unemployment";
"breakup" = "Breakup";
"moving" = "Moving";
"bereavement" = "Bereavement";
"major_changes" = "Major Changes";
"habitual_behavior" = "Habitual Behavior";
"inner_desire" = "Inner Desire";
"no_obvious_reason" = "No Obvious Reason";
"custom_reason" = "Custom";

// Categories
"emotional" = "Emotional & Psychological";
"environmental" = "Environmental & Social";
"physical" = "Physical Factors";
"events" = "Specific Events";
"negative_emotions" = "Negative Emotions";
"positive_emotions" = "Positive Emotions";
"mental_states" = "Mental States";
"locations" = "Specific Locations";
"times" = "Specific Times";
"social_contexts" = "Social Occasions";
"media_influence" = "Media Influence";
"withdrawal_symptoms" = "Withdrawal Symptoms";
"body_conditions" = "Physical Conditions";
"physiological_needs" = "Physiological Needs";
"conflicts" = "Conflicts";
"failures" = "Failures & Setbacks";
"life_events" = "Major Events";
"no_obvious_trigger" = "No Obvious Reason";

// Moods for triggers
"great" = "Great";
"good" = "Good";
"neutral" = "Neutral";
"bad" = "Bad";
"terrible" = "Terrible";

// Heatmap Legend
"emotional" = "Emotional";
"environmental" = "Environmental";
"physical" = "Physical";
"events" = "Events";

"common.done" = "Done！";

// --- CTO Button Text ---
"upgrade_to_pro" = "Upgrade to Pro";
"unlock_premium_features" = "Unlock Premium Features";

// --- PurchasePromptView Localization ---
"original_price_format" = "Original price: ";
"release_full_potential" = "Upgrade to Pro";
"lifetime_benefits" = "XMoment Lifetime Benefits";
"unlimited_xdo_creation" = "Unlimited XMoment Project Creation";
"unlimited_xdo_desc" = "Break the limits, enjoy unlimited creation, and manage all your goals efficiently";
"unlimited_xmoment_focus" = "Unlimited Xmoment Focus";
"unlimited_xmoment_desc" = "Say goodbye to 3 times daily limit, enter deep focus state anytime to improve efficiency";
"unlimited_icloud_sync" = "Unlimited iCloud Data Sync";
"unlimited_icloud_desc" = "Safely sync your data to all devices, never worry about data loss again";
"free_feature_upgrades" = "Free Feature Upgrades";
"free_upgrades_desc" = "Free upgrades to upcoming innovative features, always stay ahead";
"one_time_purchase" = "One-time purchase, unlock all features permanently";
"limited_time_offer" = "Limited Time Offer";
"save_40_percent" = "Save 40%";
"coffee_unlock_pro" = "A cup of coffee! Unlock All";
"secure_payment_via_apple" = "Secure payment processed by Apple";
"restore_premium_purchases" = "Restore Purchased Premium";
"loading_product_info" = "Loading product information...";
"unable_to_load_products" = "Unable to load product information";
"thank_you_for_purchase" = "Thank you for your purchase!";
"upgrade_success_message" = "You have successfully upgraded to XMoment Premium\nEnjoy all premium features";

// Privacy Policy Page
"privacy_policy_title" = "Privacy Policy";
"last_updated" = "Last Updated: December 1, 2023";
"overview" = "Overview";
"privacy_overview" = "X Do App is committed to protecting your privacy and personal data. This Privacy Policy outlines how we collect, use, and safeguard the information you provide when using our application.";
"information_collection" = "Information Collection";
"collection_details" = "We collect only the minimum information necessary for the normal operation of the application. All data is stored on your device and is not transmitted to our servers. X Do does not collect your personally identifiable information such as name, email address, or geographic location.";
"information_usage" = "Information Usage";
"usage_details" = "The information collected is used solely to provide the core functionalities of the application, such as habit tracking, statistics, and reminders. We do not use your data for marketing purposes or share it with third parties.";
"data_storage_security" = "Data Storage & Security";
"storage_details" = "All data is stored locally on your device. We have implemented appropriate technical and organizational security measures to prevent your personal data from accidental loss, use, or unauthorized access.";
"information_sharing" = "Information Sharing";
"sharing_details" = "We do not share your personal data with any third parties unless required by law or with your explicit consent.";
"your_rights" = "Your Rights";
"rights_details" = "You have the right to delete the app and all data stored within it at any time. As all data is stored locally, uninstalling the application will remove all information related to X Do.";
"childrens_privacy" = "Children's Privacy";
"children_details" = "Our services are not directed to children under 13. We do not knowingly collect personally identifiable information from children under 13.";
"privacy_updates" = "Privacy Policy Updates";
"updates_details" = "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy and updating the date.";
"contact_us" = "Contact Us";
"contact_details" = "If you have any questions or concerns about our Privacy Policy, please contact <NAME_EMAIL>.";
"privacy_agreement" = "By using X Do, you agree to the terms of this Privacy Policy.";

// Daily Sessions View
"today_sessions" = "Today's Sessions";
"view_details" = "View Details";
"no_sessions_today" = "No focus sessions today";
"total_duration" = "Total Duration";
"completed" = "Completed";
"interrupted" = "Interrupted";
"overclock" = "Overclock";

// WillpowerTugOfWar Metrics
"interrupted_sessions" = "Interruptions";
"total_interruptions" = "Total Interruptions";
"total_focus_hours" = "Focus Hours";
"total_focus_time" = "Total Focus Time";

"hours_unit_detailed" = "h";
"session_status_completed" = "Completed";
"session_status_interrupted" = "Interrupted";
"interrupted_by_long_press" = "interrupted by long press";