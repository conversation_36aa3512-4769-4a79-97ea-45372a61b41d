import SwiftUI
import StopDoingListKit
import StoreKit

struct CloudBackupView: View {
    @StateObject private var cloudBackupManager = CloudBackupManager.shared
    @StateObject private var storeManager = StoreManager.shared
    @State private var showingPurchasePrompt = false
    @State private var showingBackupConfirmation = false
    @State private var showingRestoreConfirmation = false
    @State private var showingBackupSuccess = false
    @State private var showingRestoreSuccess = false
    @State private var showingBackupFailure = false
    @State private var showingRestoreFailure = false
    @State private var errorMessage = ""
    @State private var statusMessage = ""
    @State private var progressValue: Double = 0

    private var lastBackupTimeString: String {
        if let date = cloudBackupManager.lastBackupDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            return formatter.string(from: date)
        } else {
            return NSLocalizedString("cloudbackup.view.noBackup", comment: "No backup yet")
        }
    }

    var body: some View {
        List {
            Section {
                // 高级功能提示
                if !storeManager.isPremium {
                    PremiumFeatureRow()
                } else {
                    // iCloud备份开关
                    Toggle(isOn: Binding(
                        get: { cloudBackupManager.isBackupEnabled },
                        set: { cloudBackupManager.setBackupEnabled($0) }
                    )) {
                        Text("启用iCloud备份")
                    }
                    .disabled(!storeManager.isPremium)

                    // 上次备份时间
                    HStack {
                        Text("上次备份")
                        Spacer()
                        Text(lastBackupTimeString)
                            .foregroundColor(.secondary)
                    }

                    // 状态信息
                    if !statusMessage.isEmpty {
                        HStack {
                            Text("状态")
                            Spacer()
                            Text(statusMessage)
                                .foregroundColor(.secondary)
                        }
                    }

                    // 进度条
                    if cloudBackupManager.isBackupInProgress || cloudBackupManager.isRestoreInProgress {
                        ProgressView()
                            .progressViewStyle(LinearProgressViewStyle())
                            .padding(.vertical, 5)
                    }

                    // 立即备份按钮
                    Button(action: {
                        showingBackupConfirmation = true
                    }) {
                        HStack {
                            Text("立即备份")
                            Spacer()
                            if cloudBackupManager.isBackupInProgress {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Image(systemName: "arrow.up.to.line")
                            }
                        }
                    }
                    .disabled(cloudBackupManager.isBackupInProgress || cloudBackupManager.isRestoreInProgress)

                    // 恢复备份按钮
                    Button(action: {
                        showingRestoreConfirmation = true
                    }) {
                        HStack {
                            Text("恢复备份")
                            Spacer()
                            if cloudBackupManager.isRestoreInProgress {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Image(systemName: "arrow.down.to.line")
                            }
                        }
                    }
                    .disabled(cloudBackupManager.isBackupInProgress || cloudBackupManager.isRestoreInProgress || !cloudBackupManager.hasCloudBackup())
                }
            }

            // 说明部分
            Section(header: Text("关于iCloud备份")) {
                VStack(alignment: .leading, spacing: 10) {
                    Text("iCloud备份可以帮助您在多台设备间同步数据，或在更换设备时恢复数据。")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("备份内容包括：")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.top, 5)

                    BulletPoint(text: "所有习惯及其详细信息")
                    BulletPoint(text: "所有触发因素和重置记录")
                    BulletPoint(text: "所有反思和笔记")
                    BulletPoint(text: "X Moment会话记录")
                }
                .padding(.vertical, 5)
            }
        }
        .navigationTitle("iCloud备份")
        .onAppear {
            // 检查是否为高级用户
            if !storeManager.isPremium {
                statusMessage = "需要升级到高级版才能使用此功能"
            } else {
                statusMessage = ""
            }
        }
        .alert("确认备份", isPresented: $showingBackupConfirmation) {
            Button("取消", role: .cancel) { }
            Button("备份") {
                performBackup()
            }
        } message: {
            Text("确定要将当前数据备份到iCloud吗？这将覆盖之前的备份。")
        }
        .alert("确认恢复", isPresented: $showingRestoreConfirmation) {
            Button("取消", role: .cancel) { }
            Button("恢复", role: .destructive) {
                performRestore()
            }
        } message: {
            Text("确定要从iCloud恢复数据吗？这将覆盖当前的所有数据，此操作不可撤销。")
        }
        .alert("备份成功", isPresented: $showingBackupSuccess) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("您的数据已成功备份到iCloud。")
        }
        .alert("恢复成功", isPresented: $showingRestoreSuccess) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("您的数据已成功从iCloud恢复。")
        }
        .alert("操作失败", isPresented: $showingBackupFailure) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
        .alert("操作失败", isPresented: $showingRestoreFailure) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
        .sheet(isPresented: $showingPurchasePrompt) {
            PurchasePromptView()
        }
    }

    // 执行备份
    private func performBackup() {
        if !storeManager.isPremium {
            showingPurchasePrompt = true
            return
        }

        statusMessage = "正在准备备份..."
        cloudBackupManager.performBackup()

        // 监听备份状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            monitorBackupProgress()
        }
    }

    // 执行恢复
    private func performRestore() {
        if !storeManager.isPremium {
            showingPurchasePrompt = true
            return
        }

        statusMessage = "正在准备恢复..."

        cloudBackupManager.restoreFromBackup { success, error in
            DispatchQueue.main.async {
                statusMessage = ""

                if success {
                    showingRestoreSuccess = true
                } else {
                    errorMessage = error ?? "恢复失败，请稍后再试"
                    showingRestoreFailure = true
                }
            }
        }
    }

    // 监控备份进度
    private func monitorBackupProgress() {
        if cloudBackupManager.isBackupInProgress {
            statusMessage = "正在备份..."

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if !cloudBackupManager.isBackupInProgress {
                    statusMessage = ""
                    if cloudBackupManager.lastBackupDate != nil {
                        showingBackupSuccess = true
                    } else {
                        errorMessage = "备份失败，请稍后再试"
                        showingBackupFailure = true
                    }
                } else {
                    // 继续监控
                    monitorBackupProgress()
                }
            }
        } else {
            statusMessage = ""
            if cloudBackupManager.lastBackupDate != nil {
                showingBackupSuccess = true
            }
        }
    }
}

// 高级功能提示行
struct PremiumFeatureRow: View {
    @State private var showingPurchasePrompt = false
    @StateObject private var storeManager = StoreManager.shared

    var body: some View {
        // 如果用户已经是高级用户，则不显示任何内容
        if storeManager.isPremium {
            EmptyView()
        } else {
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "crown.fill")
                        .foregroundColor(.yellow)
                    Text("高级功能")
                        .font(.headline)
                        .foregroundColor(.primary)
                    Spacer()
                }

                Text("iCloud备份是高级版的专属功能。升级后即可使用此功能，在多台设备间同步您的数据。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)

                Button(action: {
                    showingPurchasePrompt = true
                }) {
                    Text("升级到高级版")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                .padding(.top, 5)
            }
            .padding(.vertical, 10)
            .sheet(isPresented: $showingPurchasePrompt) {
                PurchasePromptView()
            }
        }
    }
}

// 项目符号组件
struct BulletPoint: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            Text("•")
                .foregroundColor(.secondary)
            Text(text)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    NavigationStack {
        CloudBackupView()
    }
}
