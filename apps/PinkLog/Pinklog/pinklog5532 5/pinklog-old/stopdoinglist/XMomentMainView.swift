import SwiftUI
import <PERSON>DoingListKit
import AudioToolbox
import UIKit
import AVFoundation
import Combine
import SpriteKit
import StoreKit

// Add wrapper to make UUID identifiable
struct IdentifiableUUID: Identifiable {
    let id: UUID

    init(_ uuid: UUID) {
        self.id = uuid
    }
}

struct XMomentMainView: View {
    @EnvironmentObject private var dataStore: DataStore
    @State private var showingMenu = false
    @Environment(\.colorScheme) private var colorScheme
    @State private var showingCompletionOptions = false

    // X Moment 相关状态
    @State private var xMomentState: WillpowerTugOfWar.XMomentState = .inactive
    @State private var isChoosingTime: Bool = false
    @State private var isOverclocked: Bool = false
    @State private var xMomentIsFreeMode: Bool = false
    @State private var showingPurchasePrompt = false

    // 控制背景元素效果的状态
    @State private var backgroundElementsOpacity: Double = 1.0
    @State private var backgroundElementsBlurRadius: CGFloat = 0
    @State private var showOverclockEffect: Bool = false

    // Extract the header section
    private var headerSection: some View {
        VStack(spacing: 10) {
            HStack {
                Text("x_do_list")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Spacer()

                Button(action: { showingMenu = true }) {
                    ZStack {
                        Circle()
                            .fill(Color.secondary.opacity(0.15))
                            .frame(width: 28, height: 28)
                            .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 0)

                        Image(systemName: "ellipsis")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(ScaleButtonStyle())
            }
        }
        .padding(.horizontal)
        .padding(.top, 16)
    }

    var body: some View {
        NavigationStack {
            ZStack {
                Color.black.ignoresSafeArea()
                
                Color(UIColor.systemBackground)
                    .ignoresSafeArea()

                backgroundEffectsView

                mainScrollView
            }
            .sheet(isPresented: $showingMenu) {
                XMomentMenuView()
            }
            .sheet(isPresented: $showingPurchasePrompt) {
                PurchasePromptView()
            }
            .onAppear {
                NotificationCenter.default.addObserver(forName: NSNotification.Name("ShowPurchasePrompt"), object: nil, queue: .main) { _ in
                    showingPurchasePrompt = true
                }
            }
            .onDisappear {
                NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
            }
        }
    }

    // 背景效果视图
    private var backgroundEffectsView: some View {
        Group {
            if xMomentState != .inactive {
                // 始终使用黑色背景作为基础层，确保不会闪烁
                Color.black
                    .ignoresSafeArea()

                // 如果是超频状态，在黑色背景上叠加深空效果
                if showOverclockEffect {
                    XDoOverclockedDeepSpaceView()
                }
            }
        }
    }

    // 主要滚动视图内容
    private var mainScrollContent: some View {
        VStack(spacing: 24) {
            // Top section - Now controlled by opacity/interaction
            headerSection
                .opacity(backgroundElementsOpacity)
                .allowsHitTesting(xMomentState == .inactive)

            // WillpowerTugOfWar Card
            WillpowerTugOfWar(
                showingCompletionOptions: $showingCompletionOptions,
                xMomentState: $xMomentState,
                isChoosingTime: $isChoosingTime,
                isOverclocked: $isOverclocked,
                xMomentIsFreeMode: $xMomentIsFreeMode
            )
            .onTapGesture {
                if !dataStore.canUseXMoment() {
                    NotificationCenter.default.post(name: NSNotification.Name("ShowPurchasePrompt"), object: nil)
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray5) : Color(UIColor.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
            )
            .padding(.horizontal)

            // 当日会话记录区域 - 只在非活动状态显示
            if xMomentState == .inactive {
                DailyXMomentSessionsView()
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(colorScheme == .dark ? Color(UIColor.systemGray5) : Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                    )
                    .padding(.horizontal)
            }
        }
        .padding(.bottom, 30)
    }

    // 主要滚动视图
    private var mainScrollView: some View {
        ScrollView(.vertical, showsIndicators: true) {
            mainScrollContent
        }
        .scrollBounceBehavior(.automatic)
        .background(stateChangeListeners)
    }

    // 应用状态变化监听器
    private var stateChangeListeners: some View {
        EmptyView()
            .onChange(of: xMomentState) { _, newState in
                updateBackgroundEffects(newState: newState, choosingTime: isChoosingTime, overclocked: isOverclocked)
            }
            .onChange(of: isChoosingTime) { _, newChoosingTime in
                updateBackgroundEffects(newState: xMomentState, choosingTime: newChoosingTime, overclocked: isOverclocked)
            }
            .onChange(of: isOverclocked) { _, newOverclocked in
                updateBackgroundEffects(newState: xMomentState, choosingTime: isChoosingTime, overclocked: newOverclocked)
            }
    }

    // 更新背景效果
    private func updateBackgroundEffects(newState: WillpowerTugOfWar.XMomentState, choosingTime: Bool, overclocked: Bool) {
        // 当xmoment活动时，始终隐藏普通元素
        let targetOpacity: Double = (newState != .inactive) ? 0.0 : 1.0

        // 只有在超频状态下才显示深空效果，但始终保持黑色背景
        let showDeepSpace: Bool = (newState != .inactive) && overclocked

        // 使用更长的动画时间，确保过渡更平滑
        withAnimation(.linear(duration: 0.8)) {
            backgroundElementsOpacity = targetOpacity
            backgroundElementsBlurRadius = 0.0 // 始终不使用模糊效果
        }

        // 单独处理深空效果的显示，使用稍微延迟的动画，确保黑色背景先显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.linear(duration: 0.7)) {
                showOverclockEffect = showDeepSpace
            }
        }
    }
}

// 菜单视图
struct XMomentMenuView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataStore: DataStore
    @State private var showingCloudBackup = false
    @State private var showingSettings = false
    @State private var showingAbout = false
    @State private var showingPurchasePrompt = false

    var body: some View {
        List {
            // 云备份
            Button {
                showingCloudBackup = true
                dismiss()
            } label: {
                Label(NSLocalizedString("cloud_backup", comment: ""), systemImage: "icloud")
            }
            .listRowBackground(Color.clear)

            // 设置
            Button {
                showingSettings = true
                dismiss()
            } label: {
                Label(NSLocalizedString("settings", comment: ""), systemImage: "gear")
            }
            .listRowBackground(Color.clear)

            // 关于
            Button {
                showingAbout = true
                dismiss()
            } label: {
                Label(NSLocalizedString("about", comment: ""), systemImage: "info.circle")
            }
            .listRowBackground(Color.clear)
        }
        .listStyle(.plain)
        .background(.ultraThinMaterial)
        .cornerRadius(10)
        .frame(minWidth: 180)
    }
}

// 按钮缩放样式
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.2, dampingFraction: 0.6), value: configuration.isPressed)
    }
}
