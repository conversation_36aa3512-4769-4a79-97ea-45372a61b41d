import Foundation
import SwiftUI
import CoreLocation
import AVFoundation

// MARK: - LocationManager

class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    private lazy var locationManager: CLLocationManager = {
        let manager = CLLocationManager()
        manager.delegate = self
        manager.desiredAccuracy = kCLLocationAccuracyBest
        return manager
    }()
    
    private var isInitialRequest = true
    private var isManagerInitialized = false
    
    @Published var location: CLLocation?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    
    override init() {
        super.init()
        
        // 初始化时检查授权状态
        authorizationStatus = locationManager.authorizationStatus
    }
    
    // 延迟初始化位置服务，避免UI阻塞
    func initializeLocationServices() {
        // 只在第一次调用时执行，避免重复初始化
        guard isInitialRequest else { return }
        isInitialRequest = false
        
        // 使用低优先级队列执行初始化
        DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 1.5) { [weak self] in
            guard let self = self else { return }
            
            // 检查位置服务是否启用
            if !CLLocationManager.locationServicesEnabled() {
                DispatchQueue.main.async {
                    self.errorMessage = NSLocalizedString("location_services_disabled", comment: "")
                }
                return
            }
            
            DispatchQueue.main.async {
                // 标记为已初始化
                self.isManagerInitialized = true
                
                // 根据授权状态采取不同操作
                switch self.authorizationStatus {
                case .notDetermined:
                    // 首次请求权限
                    self.locationManager.requestWhenInUseAuthorization()
                case .restricted, .denied:
                    // 权限被拒绝，但不显示错误提示，避免干扰用户
                    break
                case .authorizedWhenInUse, .authorizedAlways:
                    // 已有权限，但不立即请求位置，等用户主动请求
                    break
                @unknown default:
                    break
                }
            }
        }
    }
    
    func requestLocation() {
        // 如果是首次请求，先初始化位置服务
        if isInitialRequest || !isManagerInitialized {
            initializeLocationServices()
            
            // 延迟一点时间再请求位置，确保初始化完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self, self.isManagerInitialized else { return }
                self.performLocationRequest()
            }
            return
        }
        
        performLocationRequest()
    }
    
    // 实际执行位置请求的方法
    private func performLocationRequest() {
        isLoading = true
        errorMessage = nil
        
        // 在后台线程检查位置服务和授权状态
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // 检查位置服务是否启用
            if !CLLocationManager.locationServicesEnabled() {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = NSLocalizedString("location_services_disabled", comment: "")
                }
                return
            }
            
            DispatchQueue.main.async {
                // 根据授权状态采取不同操作
                switch self.authorizationStatus {
                case .notDetermined:
                    // 首次请求权限
                    self.locationManager.requestWhenInUseAuthorization()
                    // 不立即请求位置，等待授权回调
                case .restricted, .denied:
                    // 权限被拒绝，提示用户
                    self.isLoading = false
                    self.errorMessage = NSLocalizedString("location_permission_denied", comment: "")
                case .authorizedWhenInUse, .authorizedAlways:
                    // 已有权限，直接请求位置
                    self.locationManager.requestLocation()
                @unknown default:
                    // 处理未来可能的新状态
                    self.isLoading = false
                    self.errorMessage = NSLocalizedString("location_unknown_status", comment: "")
                }
            }
        }
    }
    
    // MARK: - CLLocationManagerDelegate
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        // 更新授权状态
        authorizationStatus = manager.authorizationStatus
        
        // 如果获得了授权，且正在加载，则请求位置
        if (authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways) && isLoading {
            locationManager.requestLocation()
        } else if authorizationStatus == .denied || authorizationStatus == .restricted {
            isLoading = false
            errorMessage = NSLocalizedString("location_permission_denied", comment: "")
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // 在主线程更新UI相关属性
        DispatchQueue.main.async {
            self.location = location
            self.isLoading = false
            self.errorMessage = nil
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // 在主线程更新UI相关属性
        DispatchQueue.main.async {
            self.isLoading = false
            
            // 提供更友好的错误信息
            if let clError = error as? CLError {
                switch clError.code {
                case .denied:
                    self.errorMessage = NSLocalizedString("location_permission_denied_simple", comment: "")
                case .network:
                    self.errorMessage = NSLocalizedString("location_network_error", comment: "")
                case .locationUnknown:
                    self.errorMessage = NSLocalizedString("location_unknown", comment: "")
                default:
                    self.errorMessage = String(format: NSLocalizedString("location_error_format", comment: ""), error.localizedDescription)
                }
            } else {
                self.errorMessage = String(format: NSLocalizedString("location_error_format", comment: ""), error.localizedDescription)
            }
            
            print("Location error: \(error.localizedDescription)")
        }
    }
}

// MARK: - AudioRecording

struct AudioRecordingModel: Identifiable, Codable {
    var id = UUID()
    let url: URL
    let duration: TimeInterval
    let waveformSamples: [CGFloat]
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, duration, waveformSamples, createdAt, urlString
    }
    
    init(url: URL, duration: TimeInterval, waveformSamples: [CGFloat]) {
        self.url = url
        self.duration = duration
        self.waveformSamples = waveformSamples
        self.createdAt = Date()
    }
    
    // 编码
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(duration, forKey: .duration)
        try container.encode(waveformSamples, forKey: .waveformSamples)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(url.absoluteString, forKey: .urlString)
    }
    
    // 解码
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        duration = try container.decode(TimeInterval.self, forKey: .duration)
        waveformSamples = try container.decode([CGFloat].self, forKey: .waveformSamples)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        let urlString = try container.decode(String.self, forKey: .urlString)
        url = URL(string: urlString)!
    }
} 