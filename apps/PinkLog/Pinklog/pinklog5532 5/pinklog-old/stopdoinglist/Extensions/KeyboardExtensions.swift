import SwiftUI
import UIKit

// 新的修饰符，接受 FocusState 绑定
struct DismissKeyboardOnTapModifier<Field: Hashable>: ViewModifier {
    var focusedField: FocusState<Field?>.Binding

    func body(content: Content) -> some View {
        content
            .simultaneousGesture(TapGesture().onEnded {
                // 通过绑定将视图的 FocusState 设为 nil
                focusedField.wrappedValue = nil
            })
    }
}

// 扩展 View 以方便使用新的修饰符
extension View {
    func dismissKeyboardOnTap<Field: Hashable>(focused: FocusState<Field?>.Binding) -> some View {
        modifier(DismissKeyboardOnTapModifier(focusedField: focused))
    }
}

// 保留这个扩展可能有用
extension UIApplication {
    func endEditing() {
        sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
} 