import SwiftUI

struct ScaleButtonStyle: ButtonStyle {
    var scaleFactor: CGFloat = 0.98

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? scaleFactor : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(
                configuration.isPressed ?
                    .easeInOut(duration: 0.1) : // 按下更快，提高响应性
                    .interpolatingSpring(mass: 0.5, stiffness: 100, damping: 10, initialVelocity: 0), // 松开回弹
                value: configuration.isPressed
            )
            .contentShape(Rectangle()) // 确保整个区域可点击
    }
}

// 扩展 View 以支持部分圆角
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// 自定义形状以支持部分圆角
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}