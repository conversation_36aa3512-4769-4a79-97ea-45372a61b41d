import SwiftUI
import StopDoingListKit
import StoreKit
import Combine

struct PurchasePromptView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var storeManager = StoreManager.shared
    @State private var showingThankYou = false
    @State private var animateButton = false
    @State private var pulseEffect = false
    // Removed: timeRemaining, timer, timerConnected
    @State private var discountLabelAngle: Double = 0
    @State private var discountLabelScale: CGFloat = 1.0

    // Removed: formatTime function

    // 计算原价，将当前价格乘以1.6
    private func calculateOriginalPrice(from priceString: String) -> String {
        // 提取价格字符串中的数字
        let digits = priceString.filter { "0123456789.".contains($0) }
        if let price = Double(digits) {
            let originalPrice = price * 1.6

            // 获取货币符号（假设在数字前面）
            let currencySymbol = priceString.prefix(while: { !$0.isNumber })

            // 格式化价格（保持与当前价格相同的小数位）
            let decimalPlaces = digits.contains(".") ? digits.split(separator: ".").last?.count ?? 0 : 0
            let formatter = NumberFormatter()
            formatter.minimumFractionDigits = decimalPlaces
            formatter.maximumFractionDigits = decimalPlaces

            if let formattedPrice = formatter.string(from: NSNumber(value: originalPrice)) {
                return "\(currencySymbol)\(formattedPrice)"
            }
        }

        // 无法解析价格时，返回价格的1.6倍
        return NSLocalizedString("original_price_format", comment: "Original price format") + priceString
    }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题和图标
                    VStack(spacing: 16) {
                        Text(NSLocalizedString("release_full_potential", comment: "Release X Do full potential"))
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        Text(NSLocalizedString("lifetime_benefits", comment: "X Do Pro lifetime benefits"))
                            .font(.headline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                            .foregroundColor(.secondary)
                    }

                    // 功能列表 - 使用同样的水平边距
                    VStack(alignment: .leading, spacing: 16) {
                        FeatureRow(icon: "bolt.circle.fill", 
                                   title: NSLocalizedString("unlimited_xmoment_focus", comment: "Unlimited Xmoment focus"), 
                                   description: NSLocalizedString("unlimited_xmoment_desc", comment: "Description for unlimited Xmoment focus"))
                        FeatureRow(icon: "icloud.circle.fill", 
                                   title: NSLocalizedString("unlimited_icloud_sync", comment: "Unlimited iCloud data sync"), 
                                   description: NSLocalizedString("unlimited_icloud_desc", comment: "Description for unlimited iCloud sync"))
                        FeatureRow(icon: "star.circle.fill", 
                                   title: NSLocalizedString("free_feature_upgrades", comment: "Free upgrades to new features"), 
                                   description: NSLocalizedString("free_upgrades_desc", comment: "Description for free feature upgrades"))
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.secondarySystemBackground))
                    )
                    .padding(.horizontal, 20)

                    // 价格和购买按钮 - 使用同样的水平边距
                    if let product = storeManager.products.first {
                        VStack(spacing: 16) {
                            HStack(alignment: .top, spacing: 16) {
                                Image(systemName: "checkmark.seal.fill")
                                    .foregroundColor(.green)
                                    .frame(width: 30, height: 30)
                                Text(NSLocalizedString("one_time_purchase", comment: "One-time purchase, unlock all features permanently"))
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 4)

                            VStack(spacing: 8) {
                                // 价格标签
                                HStack {
                                    Text(NSLocalizedString("limited_time_offer", comment: "Limited time offer"))
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color.red)
                                        .cornerRadius(4)
                                        .rotationEffect(.degrees(discountLabelAngle))
                                        .scaleEffect(discountLabelScale)
                                        .animation(
                                            Animation.spring(response: 0.3, dampingFraction: 0.5)
                                                .repeatForever(autoreverses: true),
                                            value: discountLabelScale
                                        )
                                    Spacer()
                                    // Removed: Countdown Text display
                                }

                                HStack(alignment: .center, spacing: 12) {
                                    Text(product.displayPrice)
                                        .font(.title)
                                        .fontWeight(.bold)

                                    // 计算原价并显示为划线文本
                                    Text(calculateOriginalPrice(from: product.displayPrice))
                                        .font(.title3)
                                        .foregroundColor(.secondary)
                                        .strikethrough(true, color: .secondary)

                                    // 折扣标签
                                    Text(NSLocalizedString("save_40_percent", comment: "Save 40%"))
                                        .font(.caption)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 3)
                                        .background(Color.green)
                                        .cornerRadius(4)
                                }
                            }
                            .padding(.vertical, 4)

                            Button(action: {
                                // 强化按钮动效
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                                    animateButton = true
                                }

                                // 启动脉冲效果
                                withAnimation(.easeInOut(duration: 0.6).repeatCount(1)) {
                                    pulseEffect = true
                                }

                                // 还原动画状态
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    animateButton = false
                                    pulseEffect = false
                                }

                                Task {
                                    await storeManager.purchasePremium()
                                    if storeManager.isPremium {
                                        showingThankYou = true
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                            dismiss()
                                        }
                                    }
                                }
                            }) {
                                HStack {
                                    Image(systemName: "bolt.fill")
                                        .font(.headline)
                                    Text(NSLocalizedString("coffee_unlock_pro", comment: "A cup of coffee! Unlock X Do Pro permanently"))
                                        .font(.headline)
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.blue, Color(red: 0.0, green: 0.5, blue: 1.0)]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )

                                        // 脉冲效果
                                        if pulseEffect {
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.white.opacity(0.6), lineWidth: 2)
                                                .scaleEffect(1.05)
                                                .opacity(pulseEffect ? 0 : 0.8)
                                        }
                                    }
                                )
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .strokeBorder(Color.white.opacity(0.3), lineWidth: 1)
                                )
                                .shadow(color: Color.blue.opacity(0.5), radius: 10, x: 0, y: 5)
                                .scaleEffect(animateButton ? 0.95 : 1)
                            }
                            .disabled(storeManager.isPurchaseInProgress)
                            .overlay {
                                if storeManager.isPurchaseInProgress {
                                    ProgressView()
                                        .tint(.white)
                                }
                            }

                            Text(NSLocalizedString("secure_payment_via_apple", comment: "Secure payment processed by Apple"))
                                .font(.footnote)
                                .foregroundColor(.secondary)
                                .padding(.top, 4)

                            Button(NSLocalizedString("restore_premium_purchases", comment: "Restore purchased premium version")) {
                                Task {
                                    await storeManager.restorePurchases()
                                    if storeManager.isPremium {
                                        showingThankYou = true
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                            dismiss()
                                        }
                                    }
                                }
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                            .padding(.top, 8)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(.secondarySystemBackground))
                        )
                        .padding(.horizontal, 20)
                    } else if storeManager.isLoading {
                        VStack {
                            ProgressView()
                                .padding(.bottom, 8)
                            Text(NSLocalizedString("loading_product_info", comment: "Loading product information..."))
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(.secondarySystemBackground))
                        )
                        .padding(.horizontal, 20)
                    } else {
                        Text(NSLocalizedString("unable_to_load_products", comment: "Unable to load product information"))
                            .foregroundColor(.red)
                            .padding()
                    }

                    // 错误信息
                    if let errorMessage = storeManager.errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.footnote)
                            .padding()
                    }

                    Spacer()
                }
                .padding(.bottom)
                .padding(.top)
            }
            .navigationBarTitle("", displayMode: .inline)
            .overlay {
                if showingThankYou {
                    ThankYouView()
                        .transition(.opacity)
                }
            }
            .animation(.easeInOut, value: showingThankYou)
            .onAppear {
                if storeManager.products.isEmpty && !storeManager.isLoading {
                    Task {
                        await storeManager.requestProducts()
                    }
                }

                // Removed: Timer connection logic

                // 启动限时优惠标签动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                        self.discountLabelAngle = 5
                    }

                    withAnimation(Animation.spring(response: 0.5, dampingFraction: 0.6).repeatForever(autoreverses: true)) {
                        self.discountLabelScale = 1.1
                    }
                }
            }
            // Removed: .onReceive(timer) logic
            .onDisappear {
                // Removed: self.timerConnected?.cancel()
            }
        }
    }
}

// 功能行组件
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30, height: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

// 感谢购买视图
struct ThankYouView: View {
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .shadow(color: .green.opacity(0.3), radius: 5, x: 0, y: 0)

                Text(NSLocalizedString("thank_you_for_purchase", comment: "Thank you for your purchase!"))
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text(NSLocalizedString("upgrade_success_message", comment: "You have successfully upgraded to X Do Premium\nEnjoy all premium features"))
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground).opacity(0.2))
                    .blur(radius: 2)
            )
        }
    }
}

#Preview {
    PurchasePromptView()
}
