<!-- Generated by SpecStory -->

# 部署MCP软件项目 (2025-05-14 17:50:25)

_**User**_

帮我部署这个mcp
@https://github.com/NightTrek/Software-planning-mcp 

---

_**Assistant**_



---

[MODE: EXECUTE]

我将帮您部署这个Software-planning-mcp工具。根据提供的GitHub链接，有两种安装方式：通过Smithery自动安装或手动安装。

---

```bash
git clone https://github.com/NightTrek/Software-planning-mcp.git
```

```
(base) thr33@thr33deMacBook-Pro pinklog % git clone https://github.com/NightTrek/Software-planning-mcp.git g
it clone https://github.com/NightTrek/Software-planning-mcp.git
正克隆到 'Software-planning-mcp'...
remote: Enumerating objects: 34, done.
remote: Counting objects: 100% (34/34), done.
remote: Compressing objects: 100% (27/27), done.
remote: Total 34 (delta 10), reused 26 (delta 6), pack-reused 0 (from 0)
接收对象中: 100% (34/34), 171.30 KiB | 166.00 KiB/s, 完成.
处理 delta 中: 100% (10/10), 完成.
(base) thr33@thr33deMacBook-Pro pinklog %
```

---

```bash
cd Software-planning-mcp && ls
```

```
(base) thr33@thr33deMacBook-Pro pinklog % cd Software-planning-mcp && ls
Dockerfile                      package.json                    software-planning-tool-logo.png
LICENSE                         pnpm-lock.yaml                  src
README.md                       smithery.yaml                   tsconfig.json
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

现在，我们有两种安装方式：

## 方法一：通过Smithery自动安装

这种方法更简单，适用于Claude Desktop用户。

---

```bash
npm -v
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % npm -v
10.9.2
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

如果您使用Claude Desktop，可以通过以下命令安装：

---

Cancelled

---

_**User**_

我用的是cursor

---

_**Assistant**_



---

[MODE: EXECUTE]

了解了，您使用的是Cursor而不是Claude Desktop。那么我们需要采用手动安装的方式。根据仓库说明，以下是具体步骤：

---

```bash
pnpm -v || npm -v
```

```
(base) thr33@thr33deMacBook-Pro pinklog % pnpm -v || npm -v
zsh: command not found: pnpm
10.9.2
(base) thr33@thr33deMacBook-Pro pinklog %
```

---

看起来您的系统中没有安装pnpm。我们先安装pnpm，然后继续安装过程：

---

```bash
npm install -g pnpm
```

```
(base) thr33@thr33deMacBook-Pro pinklog % npm install -g pnpm

added 1 package in 10s

1 package is looking for funding
  run `npm fund` for details
(base) thr33@thr33deMacBook-Pro pinklog %
```

---

现在我们已经安装了pnpm，接下来进行手动安装的步骤：

---

```bash
cd Software-planning-mcp && pnpm install
```

```
(base) thr33@thr33deMacBook-Pro pinklog % cd Software-planning-mcp && pnpm install
Lockfile is up to date, resolution step is skipped
Packages: +17
+++++++++++++++++
Progress: resolved 17, reused 0, downloaded 17, added 17, done

dependencies:
+ @modelcontextprotocol/sdk 0.6.0

devDependencies:
+ @types/node 20.17.11
+ typescript 5.7.2

> software-planning-tool@0.1.0 prepare /Users/<USER>/apps/PinkLog/设计3/pinklog/Software-planning-mcp
> npm run build


> software-planning-tool@0.1.0 build
> tsc && node -e "require('fs').chmodSync('build/index.js', '755')"


Done in 11.4s using pnpm v10.11.0
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

安装完成后，构建项目已自动执行。现在，我们需要配置Cursor的MCP设置。

对于Cursor，我们需要找到MCP设置文件并添加此工具。让我们首先确认MCP设置文件的位置：

---

```bash
ls -la ~/Library/Application\ Support/Cursor/ || ls -la ~/.cursor/
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % ls -la ~/Library/Application\ Support/Cursor/ || ls 
-la ~/.cursor/                                          ls -la ~/Library/Application\ Support/Cursor/ || ls 
-la ~/.cursor/
total 192
drwx------   36 <USER>  <GROUP>   1152  5 14 16:24 .
drwx------@ 179 <USER>  <GROUP>   5728  5 14 11:19 ..
-rw-------    1 <USER>  <GROUP>      0  4 27 19:19 .com.todesktop.230313mzl4w4u92.lT3Vei
srwxr-xr-x@   1 thr33  staff      0  5 14 16:22 0.49-main.sock
drwxr-xr-x    3 <USER>  <GROUP>     96  5 14 16:31 Backups
drwx------    3 <USER>  <GROUP>     96  4 24 19:16 Cache
drwxr-xr-x    5 <USER>  <GROUP>    160  5 11 10:07 CachedData
drwxr-xr-x    2 <USER>  <GROUP>     64  5 10 11:47 CachedExtensionVSIXs
drwxr-xr-x    3 <USER>  <GROUP>     96  4 24 19:16 CachedProfilesData
drwx------    4 <USER>  <GROUP>    128  4 24 19:16 Code Cache
-rw-------@   1 <USER>  <GROUP>  20480  5 12 10:36 Cookies
-rw-------@   1 <USER>  <GROUP>      0  5 12 10:36 Cookies-journal
drwx------@   7 <USER>  <GROUP>    224  4 24 19:16 Crashpad
drwx------    7 <USER>  <GROUP>    224  4 24 19:16 DawnGraphiteCache
drwx------    7 <USER>  <GROUP>    224  4 24 19:16 DawnWebGPUCache
drwx------    7 <USER>  <GROUP>    224  4 24 19:16 GPUCache
drwx------    3 <USER>  <GROUP>     96  4 24 19:16 Local Storage
-rw-------@   1 <USER>  <GROUP>   1525  5 14 13:02 Network Persistent State
-rw-------    1 <USER>  <GROUP>     41  4 24 19:16 Preferences
drwx------    4 <USER>  <GROUP>    128  5  5 20:12 Service Worker
drwx------    8 <USER>  <GROUP>    256  5 14 13:18 Session Storage
drwx------    5 <USER>  <GROUP>    160  4 24 19:16 Shared Dictionary
-rw-------@   1 <USER>  <GROUP>   4096  4 24 19:22 SharedStorage
-rw-------@   1 <USER>  <GROUP>      0  5 14 16:24 SharedStorage-wal
-rw-------@   1 <USER>  <GROUP>   1024  5 14 16:23 TransportSecurity
-rw-------@   1 <USER>  <GROUP>  36864  4 24 19:20 Trust Tokens
-rw-------    1 <USER>  <GROUP>      0  4 24 19:20 Trust Tokens-journal
drwxr-xr-x    8 <USER>  <GROUP>    256  5  6 02:53 User
drwx------    6 <USER>  <GROUP>    192  5  5 20:25 WebStorage
drwx------    3 <USER>  <GROUP>     96  5 14 16:22 blob_storage
drwxr-xr-x    3 <USER>  <GROUP>     96  4 24 19:20 clp
-rw-r--r--@   1 <USER>  <GROUP>      5  5 14 16:22 code.lock
-rw-r--r--@   1 <USER>  <GROUP>  14564  5 14 16:22 languagepacks.json
drwxr-xr-x   12 <USER>  <GROUP>    384  5 14 16:22 logs
-rw-r--r--    1 <USER>  <GROUP>     36  4 24 19:16 machineid
drwxr-xr-x    5 <USER>  <GROUP>    160  5 14 16:22 sentry
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

我们需要找到Cursor的MCP设置文件，通常位于用户目录下的特定位置。让我们查找一下：

---

```bash
ls -la ~/Library/Application\ Support/Cursor/User/
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % ls -la ~/Library/Application\ Support/Cursor/User/
total 16
drwxr-xr-x    8 <USER>  <GROUP>    256  5  6 02:53 .
drwx------   36 <USER>  <GROUP>   1152  5 14 16:24 ..
drwxr-xr-x  579 <USER>  <GROUP>  18528  5 14 16:31 History
drwxr-xr-x    8 <USER>  <GROUP>    256  5 14 17:51 globalStorage
-rw-r--r--    1 <USER>  <GROUP>    144  4 24 19:16 keybindings.json
-rw-r--r--    1 <USER>  <GROUP>    251  5  6 02:53 settings.json
drwxr-xr-x    2 <USER>  <GROUP>     64  4 24 19:16 snippets
drwxr-xr-x   41 <USER>  <GROUP>   1312  5 14 16:23 workspaceStorage
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

```bash
ls -la ~/Library/Application\ Support/Cursor/User/globalStorage/
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % ls -la ~/Library/Application\ Support/Cursor/User/gl
obalStorage/                                            ls -la ~/Library/Application\ Support/Cursor/User/gl
obalStorage/
total 1669216
drwxr-xr-x  8 <USER>  <GROUP>        256  5 14 17:51 .
drwxr-xr-x  8 <USER>  <GROUP>        256  5  6 02:53 ..
drwxr-xr-x  4 <USER>   <GROUP>        128  5 11 10:10 backups
-rw-r--r--@ 1 <USER>  <GROUP>  417648640  5 14 17:22 state.vscdb
-rw-r--r--@ 1 <USER>  <GROUP>      32768  5 14 17:01 state.vscdb-shm
-rw-r--r--@ 1 <USER>  <GROUP>    5063512  5 14 17:52 state.vscdb-wal
-rw-r--r--@ 1 <USER>  <GROUP>  417648640  5 14 13:18 state.vscdb.backup
-rw-r--r--@ 1 <USER>  <GROUP>      75290  5 14 17:51 storage.json
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

我们需要为Cursor创建MCP配置文件。首先获取项目的绝对路径：

---

```bash
pwd
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % pwd
/Users/<USER>/apps/PinkLog/设计3/pinklog/Software-planning-mcp
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

让我们在Cursor中创建MCP配置目录和文件：

---

```bash
mkdir -p ~/Library/Application\ Support/Cursor/User/globalStorage/cursor-mcp
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % 

```

---

现在我们创建MCP配置文件，指向我们的Software-planning-mcp项目：

---

```diff
- 
+ {
+   "mcpServers": {
+     "software-planning-tool": {
+       "command": "node",
+       "args": [
+         "/Users/<USER>/apps/PinkLog/设计3/pinklog/Software-planning-mcp/build/index.js"
+       ],
+       "disabled": false,
+       "autoApprove": []
+     }
+   }
+ } 
```

---

现在我们可以检查MCP是否已经成功构建：

---

```bash
ls -la build/
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % ls -la build/
total 56
drwxr-xr-x@  6 <USER>  <GROUP>    192  5 14 17:52 .
drwxr-xr-x@ 15 <USER>  <GROUP>    480  5 14 17:52 ..
-rwxr-xr-x@  1 <USER>  <GROUP>  12324  5 14 17:52 index.js
-rw-r--r--@  1 <USER>  <GROUP>   3263  5 14 17:52 prompts.js
-rw-r--r--@  1 <USER>  <GROUP>   3445  5 14 17:52 storage.js
-rw-r--r--@  1 <USER>  <GROUP>     11  5 14 17:52 types.js
(base) thr33@thr33deMacBook-Pro Software-planning-mcp %
```

---

太好了！Software-planning-mcp已经成功安装并配置完成。以下是安装概要：

1. 我们克隆了GitHub仓库：`git clone https://github.com/NightTrek/Software-planning-mcp.git`
2. 安装了pnpm：`npm install -g pnpm`
3. 安装了项目依赖并自动构建：`pnpm install`
4. 创建了Cursor的MCP配置目录：`mkdir -p ~/Library/Application\ Support/Cursor/User/globalStorage/cursor-mcp`
5. 创建了MCP配置文件，指向软件规划工具

现在，您可以重启Cursor以加载新的MCP配置。重启后，您应该可以在Cursor中通过MCP工具使用Software-planning-tool了。

使用方法：
1. 在Cursor中，您可以通过调用工具来使用Software-planning-tool
2. 可用工具包括：
   - `start_planning`：开始一个新的规划会话
   - `add_todo`：添加一个新的待办项
   - `get_todos`：获取当前计划中的所有待办项
   - `update_todo_status`：更新待办项的完成状态
   - `save_plan`：保存当前实施计划
   - `remove_todo`：从当前计划中删除待办项

请重启Cursor以使配置生效。

---

_**User**_

你配置文件写错文件了，要追加写入/Users/<USER>/.cursor/mcp.json

---

_**Assistant**_



---

[MODE: EXECUTE]

抱歉，我写错了配置文件的位置。我们需要将配置写入到`/Users/<USER>/.cursor/mcp.json`。让我来修正这个问题：

---

```bash
mkdir -p /Users/<USER>/.cursor/
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % 

```

---

```diff
          "@modelcontextprotocol/server-sequential-thinking"
        ]
+     },
+     "software-planning-tool": {
+       "command": "node",
+       "args": [
+         "/Users/<USER>/apps/PinkLog/设计3/pinklog/Software-planning-mcp/build/index.js"
+       ],
+       "disabled": false,
+       "autoApprove": []
      }
    }
```

---

看起来原文件已经存在并有其他MCP服务器配置。让我检查一下完整的配置文件内容：

---

Read file: /Users/<USER>/.cursor/mcp.json

---

很好！配置已经成功添加到正确的文件中。现在Software-planning-tool应该已经正确配置在您的Cursor中了。

让我们清理之前错误创建的配置文件：

---

```bash
rm -f ~/Library/Application\ Support/Cursor/User/globalStorage/cursor-mcp/settings.json
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % 

```

---

```bash
rmdir ~/Library/Application\ Support/Cursor/User/globalStorage/cursor-mcp 2>/dev/null || true
```

```
(base) thr33@thr33deMacBook-Pro Software-planning-mcp % 

```

---

现在配置已经正确添加到了`/Users/<USER>/.cursor/mcp.json`文件中。

Software-planning-mcp已成功部署并配置。请重启Cursor以使配置生效。重启后，您可以通过MCP接口使用以下工具：

- `start_planning`: 开始一个新的规划会话，设置目标
- `add_todo`: 添加新的待办项，包括标题、描述、复杂度和可选的代码示例
- `get_todos`: 获取当前计划中的所有待办项
- `update_todo_status`: 更新待办项的完成状态
- `save_plan`: 保存当前实施计划
- `remove_todo`: 从当前计划中删除待办项

这个MCP工具可以帮助您更好地规划软件开发项目，跟踪任务进度，并维护详细的开发计划。

---

