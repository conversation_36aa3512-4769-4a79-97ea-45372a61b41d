# 消耗型物品产品列表UI设计

## 概述

为了让用户能够快速识别和管理消耗型物品，我们在产品列表的ProductCard组件中为消耗型物品设计了特殊的角标和库存条显示，提供直观的库存状态信息。

## 设计特点

### 1. 消耗品特殊角标

#### 视觉设计
- **位置**: 产品图片左上角
- **样式**: 青色胶囊形状标签
- **图标**: 白色水滴图标 (`drop.fill`)
- **文字**: "消耗" 白色文字
- **效果**: 青色阴影，增强立体感

#### 技术实现
```swift
if product.isConsumable {
    HStack(spacing: 2) {
        Image(systemName: "drop.fill")
            .font(.system(size: 8))
            .foregroundColor(.white)
        Text("消耗")
            .font(.system(size: 8, weight: .medium))
            .foregroundColor(.white)
    }
    .padding(.horizontal, 4)
    .padding(.vertical, 2)
    .background(
        Capsule()
            .fill(Color.cyan)
            .shadow(color: .cyan.opacity(0.3), radius: 1, x: 0, y: 1)
    )
}
```

### 2. 库存状态显示系统

#### 库存状态分级
- **充足** (>50%): 绿色 + 对勾图标
- **适中** (20-50%): 蓝色 + 减号图标
- **不足** (5-20%): 橙色 + 警告三角形
- **即将用完** (<5%): 红色 + 感叹号圆圈
- **已用完** (=0): 灰色 + 叉号圆圈

#### 库存进度条
- **样式**: 线性进度条，动态颜色
- **颜色**: 根据库存状态自动调整
- **比例**: 垂直缩放0.8倍，更紧凑
- **百分比**: 右侧显示精确百分比

### 3. 详细库存信息

#### 库存概览行
- **左侧**: 库存状态图标 + "库存" 标签
- **右侧**: 库存百分比（彩色显示）

#### 库存详情行
- **剩余量**: "剩余 X.X单位" 格式显示
- **预计用完时间**: "约X天" 格式显示（基于消耗趋势）

### 4. 智能布局适配

#### 消耗品专属布局
- 消耗品显示完整的库存信息区域
- 库存条替代原有的类别标签位置
- 保持整体卡片高度一致性

#### 非消耗品保持原样
- 非消耗品继续显示类别标签
- 不显示库存相关信息
- 保持原有的布局结构

## 用户体验优势

### 1. 快速识别
- **一眼识别**: 青色角标让消耗品一目了然
- **状态直观**: 颜色编码的库存状态系统
- **信息丰富**: 百分比、剩余量、预计时间一应俱全

### 2. 库存管理
- **预警功能**: 低库存自动红色警示
- **趋势预测**: 基于历史消耗计算预计用完时间
- **精确显示**: 小数点精度的剩余量显示

### 3. 视觉层次
- **主次分明**: 角标突出，库存条辅助
- **色彩协调**: 青色主题与整体设计和谐
- **信息密度**: 紧凑布局，信息丰富但不拥挤

## 技术实现细节

### 核心组件修改

**文件**: `Views/Components/ProductCard.swift`

#### 1. 角标实现
- 在产品图片ZStack中添加角标
- 使用条件渲染 `if product.isConsumable`
- 胶囊形状背景 + 阴影效果

#### 2. 库存条实现
- VStack布局包含三行信息
- ProgressView自定义样式
- 动态颜色绑定库存状态

#### 3. 布局逻辑
- 消耗品显示库存信息
- 非消耗品显示类别标签
- 互斥显示，保持布局一致

### 数据依赖

**来源**: `Models/Product+Extension.swift`

#### 关键属性
- `isConsumable`: 是否为消耗品
- `stockStatus`: 库存状态枚举
- `stockPercentage`: 库存百分比
- `currentQuantity`: 当前剩余量
- `unitType`: 单位类型
- `estimatedDaysUntilEmpty`: 预计用完天数

#### 计算逻辑
- 基于初始数量和总消耗量计算剩余
- 根据历史消耗趋势预测用完时间
- 动态更新库存状态和颜色

## 设计原则

### 1. 一致性原则
- 与现有设计语言保持一致
- 颜色使用遵循应用主题
- 图标风格统一

### 2. 可用性原则
- 信息层次清晰
- 交互反馈及时
- 视觉负担最小

### 3. 扩展性原则
- 支持未来功能扩展
- 组件化设计便于复用
- 数据驱动的动态显示

## 未来优化方向

### 1. 交互增强
- 点击库存条快速跳转补货页面
- 长按显示详细库存历史
- 滑动手势快速记录消耗

### 2. 视觉优化
- 库存条动画效果
- 低库存闪烁提醒
- 渐变色库存条

### 3. 功能扩展
- 库存预警推送
- 自动补货提醒
- 消耗趋势图表

## 总结

通过这套专门为消耗型物品设计的产品列表UI，用户可以：

- **快速识别**: 一眼区分消耗品和普通产品
- **实时监控**: 直观了解库存状态和剩余量
- **提前规划**: 基于预测提前安排补货
- **高效管理**: 在列表页面就能掌握关键信息

这种设计既保持了产品列表的整体一致性，又突出了消耗品的特殊性，为用户提供了更好的库存管理体验。角标和库存条的组合设计，让消耗品管理变得直观而高效。