import Foundation

// MARK: - 里程碑计算器服务

class MilestoneCalculator {
    
    /// 计算并返回给定产品的所有已解锁里程碑
    static func calculateUnlockedMilestones(for product: Product) -> [Milestone] {
        if product.valuationMethod == "daily" {
            return calculateDailyMilestones(for: product)
        } else {
            return calculateUsageMilestones(for: product)
        }
    }
    
    // MARK: - 按次计价里程碑计算
    private static func calculateUsageMilestones(for product: Product) -> [Milestone] {
        guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else { return [] }
        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        var unlocked: [Milestone] = []

        let usageTypes: [MilestoneType] = [.firstUse, .hundredUses, .highFrequency, .loveAtFirstSight, .consistentGlow]
        let commonTypes: [MilestoneType] = [.cost50Percent, .cost10Percent, .costIsOne, .oneYearAnniversary, .topRated, .trueValuePoint, .veteran, .valueBalancePoint]

        (usageTypes + commonTypes).forEach {
            if var milestone = checkUsageMilestone($0, for: product, sortedRecords: sortedRecords) {
                milestone.unlockedDate = milestone.unlockedDate ?? Date()
                unlocked.append(milestone)
            }
        }
        
        return unlocked.sorted { $0.unlockedDate! < $1.unlockedDate! }
    }
    
    // MARK: - 按天计价里程碑计算
    private static func calculateDailyMilestones(for product: Product) -> [Milestone] {
        guard let purchaseDate = product.purchaseDate else { return [] }
        var unlocked: [Milestone] = []
        
        let dailyTypes: [MilestoneType] = [.firstWeek, .firstMonth, .halfYear]
        let commonTypes: [MilestoneType] = [.cost50Percent, .cost10Percent, .costIsOne, .oneYearAnniversary, .topRated, .trueValuePoint, .veteran, .valueBalancePoint]

        (dailyTypes + commonTypes).forEach {
            if var milestone = checkDailyMilestone($0, for: product, purchaseDate: purchaseDate) {
                milestone.unlockedDate = milestone.unlockedDate ?? Date()
                unlocked.append(milestone)
            }
        }
        
        return unlocked.sorted { $0.unlockedDate! < $1.unlockedDate! }
    }

    // MARK: - 单个里程碑检查逻辑
    
    /// 检查“按次计价”的里程碑
    private static func checkUsageMilestone(_ type: MilestoneType, for product: Product, sortedRecords: [UsageRecord]) -> Milestone? {
        var milestone = type.details
        switch type {
        case .cost50Percent: 
            if let rec = findRecord(where: { $0.costPerUse <= product.price * 0.5 }, in: sortedRecords, product: product) { milestone.unlockedDate = rec.date; return milestone }
        case .cost10Percent:
            if let rec = findRecord(where: { $0.costPerUse <= product.price * 0.1 }, in: sortedRecords, product: product) { milestone.unlockedDate = rec.date; return milestone }
        case .costIsOne:
            if let rec = findRecord(where: { $0.costPerUse <= 1.0 }, in: sortedRecords, product: product) { milestone.unlockedDate = rec.date; return milestone }
        case .firstUse:
            if let rec = sortedRecords.first { milestone.unlockedDate = rec.date; return milestone }
        case .hundredUses:
            if sortedRecords.count >= 100 { milestone.unlockedDate = sortedRecords[99].date; return milestone }
        case .highFrequency:
            if let date = findHighFrequencyDate(in: sortedRecords) { milestone.unlockedDate = date; return milestone }
        case .oneYearAnniversary:
            if let pDate = product.purchaseDate, let oneYearLater = Calendar.current.date(byAdding: .year, value: 1, to: pDate), let rec = sortedRecords.first(where: { $0.date ?? Date() >= oneYearLater }) { milestone.unlockedDate = rec.date; return milestone }
        case .loveAtFirstSight:
            if let rec = sortedRecords.first, rec.satisfaction == 5 { milestone.unlockedDate = rec.date; return milestone }
        case .topRated:
            if product.averageSatisfaction >= 4.5 && sortedRecords.count >= 10 { milestone.unlockedDate = sortedRecords.last?.date; return milestone }
        case .consistentGlow:
            if let date = findConsistentGlowDate(in: sortedRecords) { milestone.unlockedDate = date; return milestone }
        case .trueValuePoint:
            if let rec = findRecord(where: { $0.costPerUse <= product.averageSatisfaction }, in: sortedRecords, product: product) { milestone.unlockedDate = rec.date; return milestone }
        case .veteran:
            if let pDate = product.purchaseDate, let oneYearLater = Calendar.current.date(byAdding: .year, value: 1, to: pDate), sortedRecords.last?.date ?? Date() >= oneYearLater, sortedRecords.count >= 100, product.averageSatisfaction >= 4.0 { milestone.unlockedDate = sortedRecords.last?.date; return milestone }
        case .valueBalancePoint:
            if let date = findValueBalancePoint(for: product, sortedRecords: sortedRecords) { milestone.unlockedDate = date; return milestone }
        default: break
        }
        return nil
    }
    
    /// 检查“按天计价”的里程碑
    private static func checkDailyMilestone(_ type: MilestoneType, for product: Product, purchaseDate: Date) -> Milestone? {
        var milestone = type.details
        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
        let costPerDay = product.totalCostOfOwnership / Double(max(1, daysSincePurchase))

        switch type {
        case .cost50Percent:
            if costPerDay <= product.price * 0.5 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: Int(product.price * 0.5 / (product.totalCostOfOwnership / Double(daysSincePurchase))), to: purchaseDate); return milestone }
        case .cost10Percent:
            if costPerDay <= product.price * 0.1 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: Int(product.price * 0.1 / (product.totalCostOfOwnership / Double(daysSincePurchase))), to: purchaseDate); return milestone }
        case .costIsOne:
            if costPerDay <= 1.0 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: Int(1.0 / (product.totalCostOfOwnership / Double(daysSincePurchase))), to: purchaseDate); return milestone }
        case .firstWeek:
            if daysSincePurchase >= 7 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: 7, to: purchaseDate); return milestone }
        case .firstMonth:
            if daysSincePurchase >= 30 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: 30, to: purchaseDate); return milestone }
        case .halfYear:
            if daysSincePurchase >= 180 { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: 180, to: purchaseDate); return milestone }
        case .oneYearAnniversary:
            if daysSincePurchase >= 365 { milestone.unlockedDate = Calendar.current.date(byAdding: .year, value: 1, to: purchaseDate); return milestone }
        case .topRated:
            if product.averageSatisfaction >= 4.5 && daysSincePurchase > 30 { milestone.unlockedDate = Date(); return milestone } // 假设拥有超30天且高分则解锁
        case .trueValuePoint:
            if costPerDay <= product.averageSatisfaction { milestone.unlockedDate = Calendar.current.date(byAdding: .day, value: Int(product.averageSatisfaction / (product.totalCostOfOwnership / Double(daysSincePurchase))), to: purchaseDate); return milestone }
        case .veteran:
            if daysSincePurchase >= 365 && product.averageSatisfaction >= 4.0 { milestone.unlockedDate = Date(); return milestone }
        case .valueBalancePoint:
            if let date = findValueBalancePointDaily(for: product, purchaseDate: purchaseDate) { milestone.unlockedDate = date; return milestone }
        default: break
        }
        return nil
    }

    // MARK: - 辅助计算函数
    private static func findRecord(where condition: (UsageRecord) -> Bool, in records: [UsageRecord], product: Product) -> UsageRecord? {
        for (index, record) in records.enumerated() {
            record.costPerUse = product.totalCostOfOwnership / Double(index + 1)
            if condition(record) { return record }
        }
        return nil
    }
    
    private static func findHighFrequencyDate(in records: [UsageRecord]) -> Date? {
        guard records.count >= 10 else { return nil }
        var dateBuffer: [Date] = []
        for record in records {
            if let date = record.date {
                dateBuffer.append(date)
                dateBuffer.removeAll { $0 < Calendar.current.date(byAdding: .month, value: -1, to: date)! }
                if dateBuffer.count >= 10 { return date }
            }
        }
        return nil
    }
    
    private static func findConsistentGlowDate(in records: [UsageRecord]) -> Date? {
        var consecutiveCount = 0
        for record in records {
            if record.satisfaction == 5 {
                consecutiveCount += 1
                if consecutiveCount >= 5 { return record.date }
            } else {
                consecutiveCount = 0
            }
        }
        return nil
    }

    // MARK: - 价值平衡点计算

    /// 查找价值平衡点（按次计价）
    private static func findValueBalancePoint(for product: Product, sortedRecords: [UsageRecord]) -> Date? {
        guard sortedRecords.count >= 3 else { return nil }

        // 生成基础数据点（基于使用记录）
        let baseDataPoints = generateBaseDataPoints(for: product, sortedRecords: sortedRecords)
        let enhancedMetrics = ValuePointCalculator.calculateEnhancedMetrics(dataPoints: baseDataPoints, product: product)

        guard baseDataPoints.count == enhancedMetrics.count else { return nil }

        // 查找交叉点
        for i in 1..<baseDataPoints.count {
            let currentPoint = baseDataPoints[i]
            let currentEnhanced = enhancedMetrics[i]

            // 计算价值指数（使用改进算法）
            let valueIndex = calculateImprovedValueIndex(enhanced: currentEnhanced, basePoint: currentPoint, product: product)

            // 计算标准化成本效率指数
            let costEfficiencyIndex = calculateCostEfficiencyIndex(basePoint: currentPoint, allPoints: baseDataPoints)

            // 检查是否为交叉点（价值指数首次超过成本效率指数）
            if valueIndex >= costEfficiencyIndex {
                // 找到对应的使用记录
                if let matchingRecord = sortedRecords.first(where: { record in
                    guard let recordDate = record.date else { return false }
                    let daysDiff = abs(Calendar.current.dateComponents([.day], from: recordDate, to: currentPoint.date).day ?? 0)
                    return daysDiff <= 1 // 允许1天的误差
                }) {
                    return matchingRecord.date
                }
            }
        }

        return nil
    }

    /// 查找价值平衡点（按天计价）
    private static func findValueBalancePointDaily(for product: Product, purchaseDate: Date) -> Date? {
        guard let _ = product.purchaseDate else { return nil }

        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
        guard daysSincePurchase >= 7 else { return nil } // 至少需要一周数据

        // 生成每日数据点
        for day in 7...daysSincePurchase {
            let currentDate = Calendar.current.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()

            // 计算该日期的价值指数和成本效率
            let valueIndex = estimateDailyValueIndex(for: product, at: currentDate)
            let costEfficiencyIndex = calculateDailyCostEfficiency(for: product, at: currentDate, purchaseDate: purchaseDate)

            // 检查交叉点
            if valueIndex >= costEfficiencyIndex {
                return currentDate
            }
        }

        return nil
    }

    // MARK: - 辅助计算方法

    /// 计算改进的价值指数
    private static func calculateImprovedValueIndex(
        enhanced: ValuePointCalculator.EnhancedValuePointData,
        basePoint: ValuePointData,
        product: Product
    ) -> Double {
        let daysSincePurchase = Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: basePoint.date).day ?? 0
        let stage = ProductLifecycleStage(daysSincePurchase: daysSincePurchase)
        let weights = stage.getWeights()

        return (enhanced.valuePerception * weights.satisfaction +
                enhanced.usageStickiness * weights.usage +
                enhanced.emotionalInvestment * weights.cost) * 100
    }

    /// 计算成本效率指数
    private static func calculateCostEfficiencyIndex(basePoint: ValuePointData, allPoints: [ValuePointData]) -> Double {
        let costs = allPoints.map { $0.costEffectiveness }
        let maxCost = costs.max() ?? 1
        let minCost = costs.min() ?? 0
        let costRange = max(maxCost - minCost, 1)

        // 成本效率指数：成本越低，效率越高
        return ((maxCost - basePoint.costEffectiveness) / costRange) * 100
    }

    /// 估算每日价值指数
    private static func estimateDailyValueIndex(for product: Product, at date: Date) -> Double {
        let daysSincePurchase = Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: date).day ?? 0
        let stage = ProductLifecycleStage(daysSincePurchase: daysSincePurchase)

        // 基于产品平均满意度和生命周期阶段估算价值指数
        let baseSatisfaction = product.averageSatisfaction / 5.0 // 转换为0-1
        let timeProgress = min(1.0, Double(daysSincePurchase) / 365.0) // 一年内的进度

        // 简化的价值指数估算
        let estimatedValue = baseSatisfaction * 0.6 + timeProgress * 0.4
        return estimatedValue * 100
    }

    /// 计算每日成本效率
    private static func calculateDailyCostEfficiency(for product: Product, at date: Date, purchaseDate: Date) -> Double {
        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: date).day ?? 1
        let costPerDay = product.totalCostOfOwnership / Double(max(1, daysSincePurchase))
        let initialCostPerDay = product.totalCostOfOwnership // 第一天的成本

        // 成本效率指数：相对于初始成本的改善程度
        let efficiency = max(0, (initialCostPerDay - costPerDay) / initialCostPerDay)
        return efficiency * 100
    }

    /// 生成基础数据点（基于使用记录）
    private static func generateBaseDataPoints(for product: Product, sortedRecords: [UsageRecord]) -> [ValuePointData] {
        var dataPoints: [ValuePointData] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .short

        for (index, record) in sortedRecords.enumerated() {
            guard let recordDate = record.date else { continue }

            // 计算累计使用成本
            let cumulativeUsage = Double(index + 1)
            let costPerUse = product.totalCostOfOwnership / cumulativeUsage

            let dataPoint = ValuePointData(
                date: recordDate,
                label: dateFormatter.string(from: recordDate),
                costEffectiveness: costPerUse,
                usageRate: cumulativeUsage,
                satisfaction: Double(record.satisfaction)
            )

            dataPoints.append(dataPoint)
        }

        return dataPoints
    }
}

// MARK: - CoreData扩展
private var costPerUseKey: UInt8 = 0
extension UsageRecord {
    var costPerUse: Double {
        get { objc_getAssociatedObject(self, &costPerUseKey) as? Double ?? 0.0 }
        set { objc_setAssociatedObject(self, &costPerUseKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
}