//
//  FamilySharingService.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  家庭共享功能 - 核心服务层
//

import Foundation
import CoreData
import SwiftUI
import Combine

// MARK: - 家庭共享服务协议
protocol FamilySharingServiceProtocol {
    // 家庭成员管理
    func addFamilyMember(_ member: FamilyMember) async throws
    func updateFamilyMember(_ member: FamilyMember) async throws
    func removeFamilyMember(_ memberId: UUID) async throws
    func getFamilyMembers() -> [FamilyMember]
    func getFamilyMember(by id: UUID) -> FamilyMember?
    
    // 共享订阅管理
    func createSharedSubscription(_ sharedSubscription: SharedSubscription) async throws
    func updateSharedSubscription(_ sharedSubscription: SharedSubscription) async throws
    func removeSharedSubscription(_ subscriptionId: UUID) async throws
    func getSharedSubscriptions() -> [SharedSubscription]
    func getSharedSubscription(for subscriptionId: UUID) -> SharedSubscription?
    
    // 使用记录和统计
    func recordFamilyUsage(_ session: UsageSession, for memberId: UUID) async throws
    func getFamilyUsageStatistics(for subscriptionId: UUID, period: StatisticsPeriod) -> FamilyUsageStatistics
    func calculateMemberCostShare(for memberId: UUID, subscriptionId: UUID) -> Double
    func generateFamilyUsageReport(period: StatisticsPeriod) -> FamilyUsageReport
}

// MARK: - 家庭共享服务实现
class FamilySharingService: ObservableObject, FamilySharingServiceProtocol {
    private let context: NSManagedObjectContext
    private let subscriptionService: SubscriptionService
    
    @Published var familyMembers: [FamilyMember] = []
    @Published var sharedSubscriptions: [SharedSubscription] = []
    @Published var familyUsageStatistics: [UUID: FamilyUsageStatistics] = [:]
    
    private var cancellables = Set<AnyCancellable>()
    
    // 数据存储键
    private let familyMembersKey = "FamilyMembers"
    private let sharedSubscriptionsKey = "SharedSubscriptions"
    
    init(context: NSManagedObjectContext, subscriptionService: SubscriptionService) {
        self.context = context
        self.subscriptionService = subscriptionService
        
        loadFamilyData()
        setupNotifications()
    }
    
    // MARK: - 数据加载和保存
    private func loadFamilyData() {
        loadFamilyMembers()
        loadSharedSubscriptions()
        updateFamilyStatistics()
    }
    
    private func loadFamilyMembers() {
        if let data = UserDefaults.standard.data(forKey: familyMembersKey),
           let members = try? JSONDecoder().decode([FamilyMember].self, from: data) {
            familyMembers = members
        } else {
            // 创建默认的主账户成员
            let owner = FamilyMember(
                name: "我",
                nickname: "主账户",
                role: .owner
            )
            familyMembers = [owner]
            saveFamilyMembers()
        }
    }
    
    private func saveFamilyMembers() {
        if let data = try? JSONEncoder().encode(familyMembers) {
            UserDefaults.standard.set(data, forKey: familyMembersKey)
        }
    }
    
    private func loadSharedSubscriptions() {
        if let data = UserDefaults.standard.data(forKey: sharedSubscriptionsKey),
           let subscriptions = try? JSONDecoder().decode([SharedSubscription].self, from: data) {
            sharedSubscriptions = subscriptions
        }
    }
    
    private func saveSharedSubscriptions() {
        if let data = try? JSONEncoder().encode(sharedSubscriptions) {
            UserDefaults.standard.set(data, forKey: sharedSubscriptionsKey)
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.updateFamilyStatistics()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 家庭成员管理
    func addFamilyMember(_ member: FamilyMember) async throws {
        await MainActor.run {
            familyMembers.append(member)
            saveFamilyMembers()
        }
    }
    
    func updateFamilyMember(_ member: FamilyMember) async throws {
        await MainActor.run {
            if let index = familyMembers.firstIndex(where: { $0.id == member.id }) {
                familyMembers[index] = member
                saveFamilyMembers()
            }
        }
    }
    
    func removeFamilyMember(_ memberId: UUID) async throws {
        try await MainActor.run {
            // 不能删除家庭主账户
            guard let member = familyMembers.first(where: { $0.id == memberId }),
                  member.role != .owner else {
                throw FamilySharingError.cannotRemoveOwner
            }
            
            familyMembers.removeAll { $0.id == memberId }
            
            // 从所有共享订阅中移除该成员
            for index in sharedSubscriptions.indices {
                sharedSubscriptions[index].familyMembers.removeAll { $0 == memberId }
            }
            
            saveFamilyMembers()
            saveSharedSubscriptions()
        }
    }
    
    func getFamilyMembers() -> [FamilyMember] {
        return familyMembers
    }
    
    func getFamilyMember(by id: UUID) -> FamilyMember? {
        return familyMembers.first { $0.id == id }
    }
    
    // MARK: - 共享订阅管理
    func createSharedSubscription(_ sharedSubscription: SharedSubscription) async throws {
        await MainActor.run {
            sharedSubscriptions.append(sharedSubscription)
            saveSharedSubscriptions()
        }
    }
    
    func updateSharedSubscription(_ sharedSubscription: SharedSubscription) async throws {
        await MainActor.run {
            if let index = sharedSubscriptions.firstIndex(where: { $0.id == sharedSubscription.id }) {
                sharedSubscriptions[index] = sharedSubscription
                saveSharedSubscriptions()
            }
        }
    }
    
    func removeSharedSubscription(_ subscriptionId: UUID) async throws {
        await MainActor.run {
            sharedSubscriptions.removeAll { $0.subscriptionId == subscriptionId }
            saveSharedSubscriptions()
        }
    }
    
    func getSharedSubscriptions() -> [SharedSubscription] {
        return sharedSubscriptions
    }
    
    func getSharedSubscription(for subscriptionId: UUID) -> SharedSubscription? {
        return sharedSubscriptions.first { $0.subscriptionId == subscriptionId }
    }
    
    // MARK: - 使用记录和统计
    func recordFamilyUsage(_ session: UsageSession, for memberId: UUID) async throws {
        // 更新成员的使用统计
        await MainActor.run {
            if let index = familyMembers.firstIndex(where: { $0.id == memberId }) {
                familyMembers[index].totalUsageSessions += 1
                familyMembers[index].totalUsageTime += session.duration
                familyMembers[index].lastActiveDate = session.startTime
                
                // 更新平均满意度
                let currentTotal = familyMembers[index].averageSatisfactionRating * Double(familyMembers[index].totalUsageSessions - 1)
                familyMembers[index].averageSatisfactionRating = (currentTotal + Double(session.satisfactionRating)) / Double(familyMembers[index].totalUsageSessions)
                
                saveFamilyMembers()
            }
        }
        
        // 记录使用会话到订阅服务
        var familySession = session
        familySession.familyMember = getFamilyMember(by: memberId)?.displayName
        familySession.sessionType = .family
        
        try await subscriptionService.recordUsageSession(familySession)
    }
    
    func getFamilyUsageStatistics(for subscriptionId: UUID, period: StatisticsPeriod) -> FamilyUsageStatistics {
        let usageSessions = subscriptionService.getUsageSessions(for: subscriptionId)
        let filteredSessions = filterSessionsByPeriod(usageSessions, period: period)
        
        let totalSessions = filteredSessions.count
        let totalTime = filteredSessions.reduce(0) { $0 + $1.duration }
        
        var memberStats: [UUID: MemberUsageStats] = [:]
        var costDistribution: [UUID: Double] = [:]
        
        // 计算每个成员的使用统计
        for member in familyMembers {
            let memberSessions = filteredSessions.filter { $0.familyMember == member.displayName }
            let memberTime = memberSessions.reduce(0) { $0 + $1.duration }
            let memberSessionCount = memberSessions.count
            let averageSatisfaction = memberSessions.isEmpty ? 0 : memberSessions.reduce(0) { $0 + Double($1.satisfactionRating) } / Double(memberSessions.count)
            let usagePercentage = totalTime > 0 ? memberTime / totalTime : 0
            
            // 计算成本分摊
            let costShare = calculateMemberCostShare(for: member.id, subscriptionId: subscriptionId)
            
            memberStats[member.id] = MemberUsageStats(
                memberId: member.id,
                usageSessions: memberSessionCount,
                usageTime: memberTime,
                averageSatisfaction: averageSatisfaction,
                usagePercentage: usagePercentage,
                costShare: costShare
            )
            
            costDistribution[member.id] = costShare
        }
        
        return FamilyUsageStatistics(
            subscriptionId: subscriptionId,
            totalUsageSessions: totalSessions,
            totalUsageTime: totalTime,
            memberUsageDistribution: memberStats,
            costDistribution: costDistribution,
            period: period
        )
    }
    
    func calculateMemberCostShare(for memberId: UUID, subscriptionId: UUID) -> Double {
        guard let sharedSubscription = getSharedSubscription(for: subscriptionId) else {
            return 0
        }
        
        // 获取订阅的总成本
        let subscriptions = subscriptionService.getAllSubscriptions()
        guard let subscription = subscriptions.first(where: { $0.id == subscriptionId }) else {
            return 0
        }
        
        let totalCost = subscription.price / 12.0 // 月度成本
        return sharedSubscription.calculateMemberCost(for: memberId, totalCost: totalCost)
    }
    
    func generateFamilyUsageReport(period: StatisticsPeriod) -> FamilyUsageReport {
        let sharedSubscriptionIds = sharedSubscriptions.map { $0.subscriptionId }
        var subscriptionReports: [SubscriptionUsageReport] = []
        var totalFamilyCost: Double = 0
        var totalFamilyUsageTime: TimeInterval = 0
        
        for subscriptionId in sharedSubscriptionIds {
            let statistics = getFamilyUsageStatistics(for: subscriptionId, period: period)
            let subscriptions = subscriptionService.getAllSubscriptions()
            
            if let subscription = subscriptions.first(where: { $0.id == subscriptionId }) {
                let monthlyCost = subscription.price / 12.0
                totalFamilyCost += monthlyCost
                totalFamilyUsageTime += statistics.totalUsageTime
                
                let report = SubscriptionUsageReport(
                    subscriptionId: subscriptionId,
                    subscriptionName: subscription.name ?? "未知订阅",
                    totalCost: monthlyCost,
                    totalUsageTime: statistics.totalUsageTime,
                    memberStats: statistics.memberUsageDistribution,
                    costDistribution: statistics.costDistribution
                )
                
                subscriptionReports.append(report)
            }
        }
        
        return FamilyUsageReport(
            period: period,
            generatedDate: Date(),
            totalFamilyMembers: familyMembers.count,
            totalSharedSubscriptions: sharedSubscriptions.count,
            totalFamilyCost: totalFamilyCost,
            totalFamilyUsageTime: totalFamilyUsageTime,
            subscriptionReports: subscriptionReports,
            memberSummaries: generateMemberSummaries(period: period)
        )
    }
    
    // MARK: - 辅助方法
    private func filterSessionsByPeriod(_ sessions: [UsageSession], period: StatisticsPeriod) -> [UsageSession] {
        let calendar = Calendar.current
        let now = Date()
        
        let startDate: Date
        switch period {
        case .weekly:
            startDate = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        case .monthly:
            startDate = calendar.dateInterval(of: .month, for: now)?.start ?? now
        case .quarterly:
            startDate = calendar.date(byAdding: .month, value: -3, to: now) ?? now
        case .yearly:
            startDate = calendar.dateInterval(of: .year, for: now)?.start ?? now
        case .custom:
            startDate = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        }
        
        return sessions.filter { $0.startTime >= startDate }
    }
    
    private func generateMemberSummaries(period: StatisticsPeriod) -> [MemberSummary] {
        return familyMembers.map { member in
            let memberSubscriptions = sharedSubscriptions.filter { $0.familyMembers.contains(member.id) }
            let totalCost = memberSubscriptions.reduce(0) { total, shared in
                return total + calculateMemberCostShare(for: member.id, subscriptionId: shared.subscriptionId)
            }
            
            return MemberSummary(
                memberId: member.id,
                memberName: member.displayName,
                totalSubscriptions: memberSubscriptions.count,
                totalCost: totalCost,
                totalUsageTime: member.totalUsageTime,
                averageSatisfaction: member.averageSatisfactionRating,
                isActive: member.isRecentlyActive
            )
        }
    }
    
    private func updateFamilyStatistics() {
        for sharedSubscription in sharedSubscriptions {
            let statistics = getFamilyUsageStatistics(for: sharedSubscription.subscriptionId, period: .monthly)
            familyUsageStatistics[sharedSubscription.subscriptionId] = statistics
        }
    }
}

// MARK: - 家庭使用报告数据模型
struct FamilyUsageReport {
    let period: StatisticsPeriod
    let generatedDate: Date
    let totalFamilyMembers: Int
    let totalSharedSubscriptions: Int
    let totalFamilyCost: Double
    let totalFamilyUsageTime: TimeInterval
    let subscriptionReports: [SubscriptionUsageReport]
    let memberSummaries: [MemberSummary]
    
    var averageCostPerMember: Double {
        guard totalFamilyMembers > 0 else { return 0 }
        return totalFamilyCost / Double(totalFamilyMembers)
    }
    
    var averageUsageTimePerMember: TimeInterval {
        guard totalFamilyMembers > 0 else { return 0 }
        return totalFamilyUsageTime / Double(totalFamilyMembers)
    }
}

struct SubscriptionUsageReport {
    let subscriptionId: UUID
    let subscriptionName: String
    let totalCost: Double
    let totalUsageTime: TimeInterval
    let memberStats: [UUID: MemberUsageStats]
    let costDistribution: [UUID: Double]
}

struct MemberSummary {
    let memberId: UUID
    let memberName: String
    let totalSubscriptions: Int
    let totalCost: Double
    let totalUsageTime: TimeInterval
    let averageSatisfaction: Double
    let isActive: Bool
}

// MARK: - 错误定义
enum FamilySharingError: LocalizedError {
    case cannotRemoveOwner
    case memberNotFound
    case subscriptionNotShared
    case invalidCostSharing
    
    var errorDescription: String? {
        switch self {
        case .cannotRemoveOwner:
            return "无法删除家庭主账户"
        case .memberNotFound:
            return "找不到指定的家庭成员"
        case .subscriptionNotShared:
            return "该订阅不是共享订阅"
        case .invalidCostSharing:
            return "无效的成本分摊设置"
        }
    }
}