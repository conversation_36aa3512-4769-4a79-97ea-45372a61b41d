import Foundation
import UserNotifications
import UIKit

// MARK: - 备份通知管理器

/// 备份通知管理器 - 管理备份相关的用户通知
@MainActor
class BackupNotificationManager: NSObject, ObservableObject {
    
    // MARK: - 单例
    static let shared = BackupNotificationManager()
    
    // MARK: - 发布属性
    @Published var notificationPermissionGranted: Bool = false
    
    // MARK: - 私有属性
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // 通知类别标识符
    private let backupSuccessCategory = "BACKUP_SUCCESS"
    private let backupFailureCategory = "BACKUP_FAILURE"
    private let backupReminderCategory = "BACKUP_REMINDER"
    
    // MARK: - 初始化
    override init() {
        super.init()
        setupNotificationCategories()
        checkNotificationPermission()
        notificationCenter.delegate = self
    }
    
    // MARK: - 公共方法
    
    /// 请求通知权限
    func requestNotificationPermission() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(options: [.alert, .sound, .badge])
            await MainActor.run {
                notificationPermissionGranted = granted
            }
            return granted
        } catch {
            print("请求通知权限失败: \(error)")
            return false
        }
    }
    
    /// 检查通知权限状态
    func checkNotificationPermission() {
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.notificationPermissionGranted = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// 发送备份成功通知
    func sendBackupSuccessNotification(
        entitiesCount: Int,
        backupSize: String,
        duration: TimeInterval
    ) async {
        guard notificationPermissionGranted else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "备份完成"
        content.body = "已成功备份 \(entitiesCount) 个项目，大小 \(backupSize)"
        content.sound = .default
        content.categoryIdentifier = backupSuccessCategory
        content.badge = 0 // 清除角标
        
        // 添加用户信息
        content.userInfo = [
            "type": "backup_success",
            "entities_count": entitiesCount,
            "backup_size": backupSize,
            "duration": duration
        ]
        
        await sendNotification(content: content, identifier: "backup_success_\(Date().timeIntervalSince1970)")
    }
    
    /// 发送备份失败通知
    func sendBackupFailureNotification(error: String, retryAvailable: Bool = true) async {
        guard notificationPermissionGranted else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "备份失败"
        content.body = error
        content.sound = .default
        content.categoryIdentifier = backupFailureCategory
        
        // 添加用户信息
        content.userInfo = [
            "type": "backup_failure",
            "error": error,
            "retry_available": retryAvailable
        ]
        
        await sendNotification(content: content, identifier: "backup_failure_\(Date().timeIntervalSince1970)")
    }
    
    /// 发送备份提醒通知
    func sendBackupReminderNotification(daysSinceLastBackup: Int) async {
        guard notificationPermissionGranted else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "备份提醒"
        content.body = "您已经 \(daysSinceLastBackup) 天没有备份数据了，建议立即备份"
        content.sound = .default
        content.categoryIdentifier = backupReminderCategory
        
        // 添加用户信息
        content.userInfo = [
            "type": "backup_reminder",
            "days_since_last_backup": daysSinceLastBackup
        ]
        
        await sendNotification(content: content, identifier: "backup_reminder_\(Date().timeIntervalSince1970)")
    }
    
    /// 调度定期备份提醒
    func scheduleBackupReminder(afterDays days: Int) async {
        guard notificationPermissionGranted else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "定期备份提醒"
        content.body = "是时候备份您的数据了，保护您的重要信息"
        content.sound = .default
        content.categoryIdentifier = backupReminderCategory
        
        // 设置触发时间
        let trigger = UNTimeIntervalNotificationTrigger(
            timeInterval: TimeInterval(days * 24 * 60 * 60),
            repeats: false
        )
        
        let request = UNNotificationRequest(
            identifier: "scheduled_backup_reminder",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
            print("已调度 \(days) 天后的备份提醒")
        } catch {
            print("调度备份提醒失败: \(error)")
        }
    }
    
    /// 取消所有备份相关通知
    func cancelAllBackupNotifications() {
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [
            "scheduled_backup_reminder"
        ])
        
        notificationCenter.removeDeliveredNotifications(withIdentifiers: [
            "backup_success",
            "backup_failure",
            "backup_reminder"
        ])
    }
    
    /// 清除通知角标
    func clearBadge() {
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
    
    // MARK: - 私有方法
    
    /// 设置通知类别
    private func setupNotificationCategories() {
        // 备份成功类别
        let successCategory = UNNotificationCategory(
            identifier: backupSuccessCategory,
            actions: [],
            intentIdentifiers: [],
            options: []
        )
        
        // 备份失败类别 - 添加重试操作
        let retryAction = UNNotificationAction(
            identifier: "RETRY_BACKUP",
            title: "重试备份",
            options: [.foreground]
        )
        
        let failureCategory = UNNotificationCategory(
            identifier: backupFailureCategory,
            actions: [retryAction],
            intentIdentifiers: [],
            options: []
        )
        
        // 备份提醒类别 - 添加立即备份操作
        let backupNowAction = UNNotificationAction(
            identifier: "BACKUP_NOW",
            title: "立即备份",
            options: [.foreground]
        )
        
        let remindLaterAction = UNNotificationAction(
            identifier: "REMIND_LATER",
            title: "稍后提醒",
            options: []
        )
        
        let reminderCategory = UNNotificationCategory(
            identifier: backupReminderCategory,
            actions: [backupNowAction, remindLaterAction],
            intentIdentifiers: [],
            options: []
        )
        
        // 注册类别
        notificationCenter.setNotificationCategories([
            successCategory,
            failureCategory,
            reminderCategory
        ])
    }
    
    /// 发送通知
    private func sendNotification(content: UNMutableNotificationContent, identifier: String) async {
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: nil // 立即发送
        )
        
        do {
            try await notificationCenter.add(request)
            print("通知已发送: \(content.title)")
        } catch {
            print("发送通知失败: \(error)")
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension BackupNotificationManager: UNUserNotificationCenterDelegate {
    
    /// 应用在前台时收到通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound])
    }
    
    /// 用户点击通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        let actionIdentifier = response.actionIdentifier
        
        Task {
            await handleNotificationResponse(actionIdentifier: actionIdentifier, userInfo: userInfo)
            completionHandler()
        }
    }
    
    /// 处理通知响应
    private func handleNotificationResponse(actionIdentifier: String, userInfo: [AnyHashable: Any]) async {
        switch actionIdentifier {
        case "RETRY_BACKUP":
            // 重试备份
            await retryBackup()
            
        case "BACKUP_NOW":
            // 立即备份
            await performImmediateBackup()
            
        case "REMIND_LATER":
            // 稍后提醒（1小时后）
            await scheduleBackupReminder(afterDays: 0)
            
        case UNNotificationDefaultActionIdentifier:
            // 用户点击了通知本身
            handleDefaultNotificationAction(userInfo: userInfo)
            
        default:
            break
        }
    }
    
    /// 重试备份
    private func retryBackup() async {
        let result = await BackupManager.shared.performCrossDeviceBackup(description: "重试备份")
        
        if result.success {
            await sendBackupSuccessNotification(
                entitiesCount: result.entitiesProcessed,
                backupSize: "未知",
                duration: result.duration
            )
        } else {
            await sendBackupFailureNotification(
                error: result.error?.localizedDescription ?? "重试失败",
                retryAvailable: false
            )
        }
    }
    
    /// 执行立即备份
    private func performImmediateBackup() async {
        let result = await BackupManager.shared.performCrossDeviceBackup(description: "手动备份")
        
        if result.success {
            await sendBackupSuccessNotification(
                entitiesCount: result.entitiesProcessed,
                backupSize: "未知",
                duration: result.duration
            )
        } else {
            await sendBackupFailureNotification(
                error: result.error?.localizedDescription ?? "备份失败"
            )
        }
    }
    
    /// 处理默认通知操作
    private func handleDefaultNotificationAction(userInfo: [AnyHashable: Any]) {
        guard let type = userInfo["type"] as? String else { return }
        
        switch type {
        case "backup_success":
            // 可以导航到备份历史页面
            print("用户查看备份成功通知")
            
        case "backup_failure":
            // 可以导航到备份设置页面
            print("用户查看备份失败通知")
            
        case "backup_reminder":
            // 可以导航到备份页面
            print("用户查看备份提醒通知")
            
        default:
            break
        }
    }
}
