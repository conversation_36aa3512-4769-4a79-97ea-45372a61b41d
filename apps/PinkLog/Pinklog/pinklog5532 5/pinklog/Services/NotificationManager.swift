import Foundation
import UserNotifications
import CoreData

class NotificationManager: ObservableObject {
    static let shared = NotificationManager()

    @Published var isAuthorized = false

    private init() {
        checkAuthorizationStatus()
    }

    // 检查通知权限状态
    func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }

    // 请求通知权限
    func requestAuthorization() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { success, error in
            DispatchQueue.main.async {
                self.isAuthorized = success
                if let error = error {
                    print("通知权限请求失败: \(error.localizedDescription)")
                }
            }
        }
    }

    // 为产品创建到期提醒
    func scheduleExpiryReminder(for product: Product) {
        guard isAuthorized, let expiryDate = product.expiryDate, let id = product.id else { return }

        // 创建提醒内容
        let content = UNMutableNotificationContent()
        content.title = "产品即将过期"
        content.body = "您的\(product.name ?? "产品")将在\(formatRemainingDays(until: expiryDate))后过期"
        content.sound = .default
        content.userInfo = ["productId": id.uuidString]

        // 创建触发器
        // 读取用户设置的提前天数，默认为7天
        let daysBeforeExpiry = UserDefaults.standard.integer(forKey: "reminderDaysBeforeExpiry")
        let actualDaysBefore = daysBeforeExpiry == 0 ? 7 : daysBeforeExpiry // 如果未设置或为0，则用默认值

        let calendar = Calendar.current
        let reminderDate = calendar.date(byAdding: .day, value: -actualDaysBefore, to: expiryDate) ?? expiryDate

        // 如果提醒日期已经过去，则不创建提醒
        if reminderDate < Date() {
            return
        }

        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: reminderDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)

        // 创建请求
        let identifier = "expiry-\(id.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        // 添加通知请求
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加过期提醒失败: \(error.localizedDescription)")
            }
        }
    }

    // 为产品创建保修到期提醒
    func scheduleWarrantyReminder(for product: Product) {
        guard isAuthorized, let warrantyEndDate = product.warrantyEndDate, let id = product.id else { return }

        // 创建提醒内容
        let content = UNMutableNotificationContent()
        content.title = "产品保修即将到期"
        content.body = "您的\(product.name ?? "产品")的保修将在\(formatRemainingDays(until: warrantyEndDate))后到期"
        content.sound = .default
        content.userInfo = ["productId": id.uuidString]

        // 创建触发器
        // 读取用户设置的提前天数，默认为30天
        let daysBeforeWarrantyEnd = UserDefaults.standard.integer(forKey: "reminderDaysBeforeWarrantyEnd")
        let actualDaysBefore = daysBeforeWarrantyEnd == 0 ? 30 : daysBeforeWarrantyEnd // 如果未设置或为0，则用默认值

        let calendar = Calendar.current
        let reminderDate = calendar.date(byAdding: .day, value: -actualDaysBefore, to: warrantyEndDate) ?? warrantyEndDate

        // 如果提醒日期已经过去，则不创建提醒
        if reminderDate < Date() {
            return
        }

        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: reminderDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)

        // 创建请求
        let identifier = "warranty-\(id.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        // 添加通知请求
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加保修提醒失败: \(error.localizedDescription)")
            }
        }
    }

    // 为低使用率产品创建提醒
    func scheduleLowUsageReminder(for product: Product) { // 移除 daysThreshold 参数
        // 如果产品是按天计算价值的，则不应该有低使用率提醒
        if product.valuationMethod == "daily" {
            return
        }

        guard isAuthorized, let id = product.id else { return }

        // 读取用户设置的低使用率提醒天数，默认为30天
        let lowUsageDaysSetting = UserDefaults.standard.integer(forKey: "lowUsageReminderDays")
        let actualDaysThreshold = lowUsageDaysSetting == 0 ? 30 : lowUsageDaysSetting // 如果未设置或为0，则用默认值

        // 获取最后使用日期
        guard let lastUsageDate = product.lastUsageDate else {
            // 如果没有使用记录，使用购买日期
            guard let purchaseDate = product.purchaseDate else { return }

            // 如果购买日期超过阈值天数，并且产品不是按天计费，则创建提醒
            let calendar = Calendar.current
            let now = Date()
            let components = calendar.dateComponents([.day], from: purchaseDate, to: now)

            if let days = components.day, days >= actualDaysThreshold, product.valuationMethod != "daily" {
                // 创建提醒内容
                let content = UNMutableNotificationContent()
                content.title = "未使用产品提醒"
                content.body = "您的\(product.name ?? "产品")购买后还未使用，已经过去\(days)天了"
                content.sound = .default
                content.userInfo = ["productId": id.uuidString]

                // 创建触发器（明天中午12点）
                var dateComponents = DateComponents()
                dateComponents.hour = 12
                dateComponents.minute = 0
                let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)

                // 创建请求
                let identifier = "lowUsage-\(id.uuidString)"
                let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

                // 添加通知请求
                UNUserNotificationCenter.current().add(request) { error in
                    if let error = error {
                        print("添加低使用率提醒失败: \(error.localizedDescription)")
                    }
                }
            }

            return
        }

        // 计算距离最后使用日期的天数
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: lastUsageDate, to: now)

        if let days = components.day, days >= actualDaysThreshold {
            // 创建提醒内容
            let content = UNMutableNotificationContent()
            content.title = "低使用率产品提醒"
            content.body = "您的\(product.name ?? "产品")已经\(days)天未使用了"
            content.sound = .default
            content.userInfo = ["productId": id.uuidString]

            // 创建触发器（明天中午12点）
            var dateComponents = DateComponents()
            dateComponents.hour = 12
            dateComponents.minute = 0
            let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)

            // 创建请求
            let identifier = "lowUsage-\(id.uuidString)"
            let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

            // 添加通知请求
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("添加低使用率提醒失败: \(error.localizedDescription)")
                }
            }
        }
    }

    // 为产品创建维护提醒
    func scheduleMaintenanceReminder(for product: Product, daysSinceLastMaintenance: Int) {
        guard isAuthorized, let id = product.id else { return }

        // 创建提醒内容
        let content = UNMutableNotificationContent()
        content.title = "产品维护提醒"
        content.body = "您的\(product.name ?? "产品")已经\(daysSinceLastMaintenance)天未进行维护，建议进行定期保养"
        content.sound = .default
        content.userInfo = ["productId": id.uuidString]

        // 创建触发器（明天中午12点）
        var dateComponents = DateComponents()
        dateComponents.hour = 12
        dateComponents.minute = 0
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)

        // 创建请求
        let identifier = "maintenance-\(id.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        // 添加通知请求
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加维护提醒失败: \(error.localizedDescription)")
            }
        }
    }

    // 为借阅记录创建到期提醒 (UsageRecord版本)
    func scheduleLoanDueReminder(for product: Product, recordId: UUID, dueDate: Date, borrowerName: String) {
        guard isAuthorized, let productId = product.id else { return }

        // 创建提醒内容
        let content = UNMutableNotificationContent()
        content.title = "借出物品即将到期"
        content.body = "您借给\(borrowerName)的\(product.name ?? "物品")将在\(formatRemainingDays(until: dueDate))后到期归还"
        content.sound = .default
        content.userInfo = ["productId": productId.uuidString, "recordId": recordId.uuidString]

        // 创建触发器
        // 提前3天提醒
        let calendar = Calendar.current
        let reminderDate = calendar.date(byAdding: .day, value: -3, to: dueDate) ?? dueDate

        // 如果提醒日期已经过去，则不创建提醒
        if reminderDate < Date() {
            return
        }

        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: reminderDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)

        // 创建请求
        let identifier = "loan-\(recordId.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        // 添加通知请求
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加借阅到期提醒失败: \(error.localizedDescription)")
            }
        }
    }

    // 为借阅记录创建到期提醒 (LoanRecord版本 - 保留向后兼容性)
    func scheduleLoanDueReminder(for product: Product, loanRecord: LoanRecord) {
        guard isAuthorized, let dueDate = loanRecord.dueDate, let productId = product.id, let loanId = loanRecord.id else { return }

        // 创建提醒内容
        let content = UNMutableNotificationContent()
        content.title = "借出物品即将到期"
        content.body = "您借给\(loanRecord.borrowerName ?? "某人")的\(product.name ?? "物品")将在\(formatRemainingDays(until: dueDate))后到期归还"
        content.sound = .default
        content.userInfo = ["productId": productId.uuidString, "loanId": loanId.uuidString]

        // 创建触发器
        // 提前3天提醒
        let calendar = Calendar.current
        let reminderDate = calendar.date(byAdding: .day, value: -3, to: dueDate) ?? dueDate

        // 如果提醒日期已经过去，则不创建提醒
        if reminderDate < Date() {
            return
        }

        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: reminderDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)

        // 创建请求
        let identifier = "loan-\(loanId.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        // 添加通知请求
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加借阅到期提醒失败: \(error.localizedDescription)")
            }
        }
    }

    // 取消借阅提醒
    func cancelLoanReminder(for id: UUID) {
        let identifier = "loan-\(id.uuidString)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
    }

    // 取消产品的所有提醒
    func cancelReminders(for productId: UUID) {
        let identifiers = [
            "expiry-\(productId.uuidString)",
            "warranty-\(productId.uuidString)",
            "lowUsage-\(productId.uuidString)",
            "maintenance-\(productId.uuidString)"
        ]

        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }

    // 取消所有提醒
    func cancelAllReminders() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }

    // 格式化剩余天数
    private func formatRemainingDays(until date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: date)

        if let days = components.day {
            if days <= 0 {
                return "今天"
            } else if days == 1 {
                return "明天"
            } else if days < 30 {
                return "\(days)天"
            } else if days < 365 {
                let months = days / 30
                return "\(months)个月"
            } else {
                let years = days / 365
                return "\(years)年"
            }
        }

        return "未知时间"
    }
}
