import Foundation
import Network
import Combine

// MARK: - 网络状态监听器

/// 网络状态监听器 - 监听网络连接状态和类型变化
@MainActor
class NetworkMonitor: ObservableObject {
    
    // MARK: - 单例
    static let shared = NetworkMonitor()
    
    // MARK: - 发布属性
    @Published var isConnected: Bool = false
    @Published var connectionType: NWInterface.InterfaceType?
    @Published var isExpensive: Bool = false
    @Published var isConstrained: Bool = false
    
    // MARK: - 私有属性
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor", qos: .utility)
    
    // MARK: - 初始化
    private init() {
        startMonitoring()
    }
    
    // MARK: - 公共方法
    
    /// 开始监听网络状态
    func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        monitor.start(queue: queue)
    }
    
    /// 停止监听网络状态
    func stopMonitoring() {
        monitor.cancel()
    }
    
    /// 检查是否适合进行大数据传输
    var isSuitableForLargeTransfer: Bool {
        return isConnected && !isExpensive && !isConstrained
    }
    
    /// 检查是否为WiFi连接
    var isWiFiConnected: Bool {
        return isConnected && connectionType == .wifi
    }
    
    /// 检查是否为蜂窝网络连接
    var isCellularConnected: Bool {
        return isConnected && connectionType == .cellular
    }
    
    /// 获取网络状态描述
    var statusDescription: String {
        if !isConnected {
            return "无网络连接"
        }
        
        var description = connectionTypeDescription
        
        if isExpensive {
            description += " (计费网络)"
        }
        
        if isConstrained {
            description += " (受限网络)"
        }
        
        return description
    }
    
    /// 获取连接类型描述
    var connectionTypeDescription: String {
        guard let type = connectionType else {
            return "网络连接检测中"
        }
        
        switch type {
        case .wifi:
            return "WiFi网络"
        case .cellular:
            return "蜂窝数据网络"
        case .wiredEthernet:
            return "有线以太网"
        case .loopback:
            return "本地回环连接"
        case .other:
            return "专用网络连接" // 更清晰的描述，通常指VPN或热点共享
        @unknown default:
            return "未识别的网络类型"
        }
    }
    
    // MARK: - 私有方法
    
    /// 更新网络状态
    private func updateNetworkStatus(_ path: NWPath) {
        // 更新连接状态
        isConnected = path.status == .satisfied
        
        // 更新连接类型
        connectionType = path.availableInterfaces.first?.type
        
        // 更新网络特性
        isExpensive = path.isExpensive
        isConstrained = path.isConstrained
        
        // 打印网络状态变化（调试用）
        print("网络状态更新: \(statusDescription)")
    }
    
    deinit {
        Task { @MainActor in
            stopMonitoring()
        }
    }
}

// MARK: - 网络状态扩展

extension NetworkMonitor {
    
    /// 网络质量评估
    enum NetworkQuality {
        case excellent  // WiFi，无限制
        case good      // WiFi，有限制或蜂窝网络无限制
        case fair      // 蜂窝网络，有限制但不计费
        case poor      // 蜂窝网络，计费且受限
        case unavailable // 无网络
    }
    
    /// 获取当前网络质量
    var networkQuality: NetworkQuality {
        guard isConnected else {
            return .unavailable
        }
        
        switch connectionType {
        case .wifi:
            return isConstrained ? .good : .excellent
        case .cellular:
            if isExpensive && isConstrained {
                return .poor
            } else if isExpensive || isConstrained {
                return .fair
            } else {
                return .good
            }
        case .wiredEthernet:
            return .excellent
        default:
            return .fair
        }
    }
    
    /// 根据网络质量判断是否适合备份
    func isSuitableForBackup(wifiOnly: Bool = false) -> Bool {
        guard isConnected else { return false }
        
        if wifiOnly {
            return connectionType == .wifi
        }
        
        switch networkQuality {
        case .excellent, .good:
            return true
        case .fair:
            return true // 允许在一般网络下备份
        case .poor, .unavailable:
            return false
        }
    }
    
    /// 获取建议的备份策略
    func getRecommendedBackupStrategy() -> BackupStrategy {
        switch networkQuality {
        case .excellent:
            return .fullBackup
        case .good:
            return .incrementalBackup
        case .fair:
            return .incrementalBackupCompressed
        case .poor:
            return .essentialDataOnly
        case .unavailable:
            return .offline
        }
    }
}

// MARK: - 备份策略

/// 根据网络状况推荐的备份策略
enum BackupStrategy {
    case fullBackup                    // 完整备份
    case incrementalBackup            // 增量备份
    case incrementalBackupCompressed  // 压缩增量备份
    case essentialDataOnly           // 仅备份关键数据
    case offline                     // 离线模式，不备份
    
    var description: String {
        switch self {
        case .fullBackup:
            return "完整备份"
        case .incrementalBackup:
            return "增量备份"
        case .incrementalBackupCompressed:
            return "压缩增量备份"
        case .essentialDataOnly:
            return "仅备份关键数据"
        case .offline:
            return "离线模式"
        }
    }
}

// MARK: - 网络监听器扩展方法

extension NetworkMonitor {
    
    /// 等待网络连接
    func waitForConnection(timeout: TimeInterval = 30.0) async -> Bool {
        if isConnected {
            return true
        }
        
        return await withCheckedContinuation { continuation in
            var cancellable: AnyCancellable?
            var timeoutTask: Task<Void, Never>?
            
            // 设置超时
            timeoutTask = Task {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                cancellable?.cancel()
                continuation.resume(returning: false)
            }
            
            // 监听连接状态变化
            cancellable = $isConnected
                .filter { $0 } // 只关心连接成功的情况
                .first()
                .sink { _ in
                    timeoutTask?.cancel()
                    continuation.resume(returning: true)
                }
        }
    }
    
    /// 等待WiFi连接
    func waitForWiFiConnection(timeout: TimeInterval = 30.0) async -> Bool {
        if isWiFiConnected {
            return true
        }
        
        return await withCheckedContinuation { continuation in
            var cancellable: AnyCancellable?
            var timeoutTask: Task<Void, Never>?
            
            // 设置超时
            timeoutTask = Task {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                cancellable?.cancel()
                continuation.resume(returning: false)
            }
            
            // 监听WiFi连接状态变化
            cancellable = Publishers.CombineLatest($isConnected, $connectionType)
                .filter { isConnected, type in
                    isConnected && type == .wifi
                }
                .first()
                .sink { _ in
                    timeoutTask?.cancel()
                    continuation.resume(returning: true)
                }
        }
    }
    
    /// 检查网络连接并提供用户友好的错误信息
    func checkNetworkAvailability() -> (available: Bool, message: String?) {
        guard isConnected else {
            return (false, "无网络连接，请检查网络设置")
        }
        
        if isExpensive && connectionType == .cellular {
            return (true, "当前使用蜂窝网络，可能产生流量费用")
        }
        
        if isConstrained {
            return (true, "当前网络受限，备份速度可能较慢")
        }
        
        return (true, nil)
    }
}
