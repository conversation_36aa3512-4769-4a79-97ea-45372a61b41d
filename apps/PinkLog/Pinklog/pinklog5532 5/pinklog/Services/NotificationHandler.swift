//
//  NotificationHandler.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  通知处理器 - 处理订阅提醒通知的用户交互
//

import Foundation
import UserNotifications
import SwiftUI
import CoreData

@MainActor
class NotificationHandler: NSObject, ObservableObject {
    static let shared = NotificationHandler()
    
    @Published var pendingNavigationAction: NavigationAction?
    
    private var context: NSManagedObjectContext?
    private var reminderService: SubscriptionReminderService?
    
    enum NavigationAction {
        case showSubscription(UUID)
        case showReminderManagement
        case showSubscriptionList
    }
    
    override init() {
        super.init()
        setupNotificationCenter()
    }
    
    func configure(context: NSManagedObjectContext) {
        self.context = context
        self.reminderService = SubscriptionReminderService(context: context)
    }
    
    private func setupNotificationCenter() {
        UNUserNotificationCenter.current().delegate = self
        
        // 设置通知操作
        setupNotificationActions()
    }
    
    private func setupNotificationActions() {
        // 续订提醒操作
        let renewAction = UNNotificationAction(
            identifier: "RENEW_ACTION",
            title: "立即续订",
            options: [.foreground]
        )
        
        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE_ACTION",
            title: "稍后提醒",
            options: []
        )
        
        let viewAction = UNNotificationAction(
            identifier: "VIEW_ACTION",
            title: "查看详情",
            options: [.foreground]
        )
        
        // 续订提醒类别
        let renewalCategory = UNNotificationCategory(
            identifier: "RENEWAL_REMINDER",
            actions: [renewAction, snoozeAction, viewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        // 试用期结束提醒操作
        let subscribeAction = UNNotificationAction(
            identifier: "SUBSCRIBE_ACTION",
            title: "立即订阅",
            options: [.foreground]
        )
        
        let cancelTrialAction = UNNotificationAction(
            identifier: "CANCEL_TRIAL_ACTION",
            title: "取消试用",
            options: [.destructive]
        )
        
        // 试用期结束类别
        let trialExpiryCategory = UNNotificationCategory(
            identifier: "TRIAL_EXPIRY",
            actions: [subscribeAction, cancelTrialAction, viewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        // 使用不足提醒操作
        let optimizeAction = UNNotificationAction(
            identifier: "OPTIMIZE_ACTION",
            title: "优化使用",
            options: [.foreground]
        )
        
        let cancelSubscriptionAction = UNNotificationAction(
            identifier: "CANCEL_SUBSCRIPTION_ACTION",
            title: "考虑取消",
            options: [.destructive]
        )
        
        // 使用不足类别
        let underutilizedCategory = UNNotificationCategory(
            identifier: "UNDERUTILIZED",
            actions: [optimizeAction, cancelSubscriptionAction, viewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        // 成本警告操作
        let reviewCostAction = UNNotificationAction(
            identifier: "REVIEW_COST_ACTION",
            title: "查看成本",
            options: [.foreground]
        )
        
        let findAlternativeAction = UNNotificationAction(
            identifier: "FIND_ALTERNATIVE_ACTION",
            title: "寻找替代",
            options: [.foreground]
        )
        
        // 成本警告类别
        let costAlertCategory = UNNotificationCategory(
            identifier: "COST_ALERT",
            actions: [reviewCostAction, findAlternativeAction, viewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        // 注册所有类别
        UNUserNotificationCenter.current().setNotificationCategories([
            renewalCategory,
            trialExpiryCategory,
            underutilizedCategory,
            costAlertCategory
        ])
    }
    
    // MARK: - 处理通知操作
    private func handleNotificationAction(
        actionIdentifier: String,
        subscriptionId: UUID,
        reminderType: ReminderType
    ) async {
        guard let context = context,
              let reminderService = reminderService else {
            print("NotificationHandler 未正确配置")
            return
        }
        
        switch actionIdentifier {
        case "RENEW_ACTION":
            await handleRenewAction(subscriptionId: subscriptionId)
            
        case "SNOOZE_ACTION":
            await reminderService.snoozeReminder(subscriptionId: subscriptionId, type: reminderType)
            
        case "VIEW_ACTION":
            pendingNavigationAction = .showSubscription(subscriptionId)
            
        case "SUBSCRIBE_ACTION":
            await handleSubscribeAction(subscriptionId: subscriptionId)
            
        case "CANCEL_TRIAL_ACTION":
            await handleCancelTrialAction(subscriptionId: subscriptionId)
            
        case "OPTIMIZE_ACTION":
            pendingNavigationAction = .showSubscription(subscriptionId)
            
        case "CANCEL_SUBSCRIPTION_ACTION":
            await handleCancelSubscriptionAction(subscriptionId: subscriptionId)
            
        case "REVIEW_COST_ACTION":
            pendingNavigationAction = .showSubscription(subscriptionId)
            
        case "FIND_ALTERNATIVE_ACTION":
            pendingNavigationAction = .showSubscription(subscriptionId)
            
        default:
            print("未知的通知操作: \(actionIdentifier)")
        }
    }
    
    // MARK: - 具体操作处理
    private func handleRenewAction(subscriptionId: UUID) async {
        guard let context = context else { return }
        
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", subscriptionId as CVarArg)
        
        do {
            if let product = try context.fetch(request).first {
                // 更新续订日期
                let calendar = Calendar.current
                if let currentRenewalDate = product.expiryDate {
                    // 根据计费周期计算下次续订日期
                    let subscriptionService = SubscriptionService(context: context, coreDataRepository: CoreDataRepository(context: context))
                    if let subscriptionData = subscriptionService.extractSubscriptionData(from: product) {
                        let nextRenewalDate: Date
                        switch subscriptionData.billingCycle {
                        case .monthly:
                            nextRenewalDate = calendar.date(byAdding: .month, value: 1, to: currentRenewalDate) ?? currentRenewalDate
                        case .quarterly:
                            nextRenewalDate = calendar.date(byAdding: .month, value: 3, to: currentRenewalDate) ?? currentRenewalDate
                        case .annual:
                            nextRenewalDate = calendar.date(byAdding: .year, value: 1, to: currentRenewalDate) ?? currentRenewalDate
                        case .semiAnnual:
                            nextRenewalDate = calendar.date(byAdding: .month, value: 6, to: currentRenewalDate) ?? currentRenewalDate
                        case .custom:
                            let days = subscriptionData.customCycleDays ?? 30
                            nextRenewalDate = calendar.date(byAdding: .day, value: days, to: currentRenewalDate) ?? currentRenewalDate
                        }
                        
                        product.expiryDate = nextRenewalDate
                        
                        // 更新订阅数据
                        var updatedData = subscriptionData
                        updatedData.nextRenewalDate = nextRenewalDate
                        updatedData.status = .active
                        
                        if let jsonData = try? JSONEncoder().encode(updatedData),
                           let jsonString = String(data: jsonData, encoding: .utf8) {
                            product.purchaseNotes = jsonString
                        }
                        
                        try context.save()
                        
                        // 重新调度提醒
                        await reminderService?.scheduleRenewalReminders(for: product)
                        
                        print("订阅已续订至: \(nextRenewalDate)")
                    }
                }
            }
        } catch {
            print("处理续订操作失败: \(error)")
        }
    }
    
    private func handleSubscribeAction(subscriptionId: UUID) async {
        // 将试用订阅转换为正式订阅
        guard let context = context else { return }
        
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", subscriptionId as CVarArg)
        
        do {
            if let product = try context.fetch(request).first {
                let subscriptionService = SubscriptionService(context: context, coreDataRepository: CoreDataRepository(context: context))
                if var subscriptionData = subscriptionService.extractSubscriptionData(from: product) {
                    subscriptionData.status = .active
                    
                    if let jsonData = try? JSONEncoder().encode(subscriptionData),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        product.purchaseNotes = jsonString
                    }
                    
                    try context.save()
                    print("试用订阅已转换为正式订阅")
                }
            }
        } catch {
            print("处理订阅操作失败: \(error)")
        }
    }
    
    private func handleCancelTrialAction(subscriptionId: UUID) async {
        // 取消试用订阅
        guard let context = context else { return }
        
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", subscriptionId as CVarArg)
        
        do {
            if let product = try context.fetch(request).first {
                let subscriptionService = SubscriptionService(context: context, coreDataRepository: CoreDataRepository(context: context))
                if var subscriptionData = subscriptionService.extractSubscriptionData(from: product) {
                    subscriptionData.status = .cancelled
                    
                    if let jsonData = try? JSONEncoder().encode(subscriptionData),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        product.purchaseNotes = jsonString
                    }
                    
                    try context.save()
                    
                    // 取消相关提醒
                    await reminderService?.cancelReminders(for: product.id ?? UUID())
                    
                    print("试用订阅已取消")
                }
            }
        } catch {
            print("处理取消试用操作失败: \(error)")
        }
    }
    
    private func handleCancelSubscriptionAction(subscriptionId: UUID) async {
        // 标记订阅为待取消状态
        guard let context = context else { return }
        
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", subscriptionId as CVarArg)
        
        do {
            if let product = try context.fetch(request).first {
                let subscriptionService = SubscriptionService(context: context, coreDataRepository: CoreDataRepository(context: context))
                if var subscriptionData = subscriptionService.extractSubscriptionData(from: product) {
                    subscriptionData.status = .cancelled
                    
                    if let jsonData = try? JSONEncoder().encode(subscriptionData),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        product.purchaseNotes = jsonString
                    }
                    
                    try context.save()
                    print("订阅已标记为待取消")
                }
            }
        } catch {
            print("处理取消订阅操作失败: \(error)")
        }
    }
    
    // MARK: - 导航处理
    func handlePendingNavigation() {
        // 这个方法将在主界面中调用，用于处理待处理的导航操作
        if let action = pendingNavigationAction {
            pendingNavigationAction = nil
            
            switch action {
            case .showSubscription(let subscriptionId):
                NotificationCenter.default.post(
                    name: .navigateToSubscription,
                    object: nil,
                    userInfo: ["subscriptionId": subscriptionId]
                )
                
            case .showReminderManagement:
                NotificationCenter.default.post(
                    name: .showReminderManagement,
                    object: nil
                )
                
            case .showSubscriptionList:
                NotificationCenter.default.post(
                    name: .showSubscriptionList,
                    object: nil
                )
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationHandler: UNUserNotificationCenterDelegate {
    // 应用在前台时收到通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    // 用户点击通知或通知操作
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        guard let subscriptionIdString = userInfo["subscriptionId"] as? String,
              let subscriptionId = UUID(uuidString: subscriptionIdString),
              let reminderTypeString = userInfo["reminderType"] as? String,
              let reminderType = ReminderType(rawValue: reminderTypeString) else {
            completionHandler()
            return
        }
        
        Task {
            await handleNotificationAction(
                actionIdentifier: response.actionIdentifier,
                subscriptionId: subscriptionId,
                reminderType: reminderType
            )
            
            await MainActor.run {
                completionHandler()
            }
        }
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let showReminderManagement = Notification.Name("showReminderManagement")
    static let showSubscriptionList = Notification.Name("showSubscriptionList")
}