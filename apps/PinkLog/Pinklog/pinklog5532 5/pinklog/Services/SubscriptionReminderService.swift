//
//  SubscriptionReminderService.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅续订提醒服务
//

import Foundation
import UserNotifications
import CoreData
import SwiftUI

// MARK: - 提醒类型枚举
enum ReminderType: String, CaseIterable {
    case renewal = "renewal"
    case trialExpiry = "trial_expiry"
    case priceChange = "price_change"
    case underutilized = "underutilized"
    case costAlert = "cost_alert"
    
    var displayName: String {
        switch self {
        case .renewal: return "续订提醒"
        case .trialExpiry: return "试用期结束"
        case .priceChange: return "价格变动"
        case .underutilized: return "使用不足"
        case .costAlert: return "成本警告"
        }
    }
    
    var icon: String {
        switch self {
        case .renewal: return "arrow.clockwise.circle"
        case .trialExpiry: return "clock.badge.exclamationmark"
        case .priceChange: return "dollarsign.circle"
        case .underutilized: return "chart.line.downtrend.xyaxis"
        case .costAlert: return "exclamationmark.triangle"
        }
    }
}

// MARK: - 提醒数据模型
struct SubscriptionReminder: Identifiable {
    let id: UUID
    let subscriptionId: UUID
    let subscriptionName: String
    let type: ReminderType
    let scheduledDate: Date
    let title: String
    let body: String
    let isActive: Bool
    let notificationId: String
    
    init(subscriptionId: UUID, subscriptionName: String, type: ReminderType, scheduledDate: Date, title: String, body: String) {
        self.id = UUID()
        self.subscriptionId = subscriptionId
        self.subscriptionName = subscriptionName
        self.type = type
        self.scheduledDate = scheduledDate
        self.title = title
        self.body = body
        self.isActive = true
        self.notificationId = "\(type.rawValue)_\(subscriptionId.uuidString)_\(Int(scheduledDate.timeIntervalSince1970))"
    }
}

// MARK: - 订阅提醒服务
class SubscriptionReminderService: ObservableObject {
    private let context: NSManagedObjectContext
    private let notificationCenter = UNUserNotificationCenter.current()
    
    @Published var hasNotificationPermission = false
    @Published var activeReminders: [SubscriptionReminder] = []
    
    init(context: NSManagedObjectContext) {
        self.context = context
        checkNotificationPermission()
        loadActiveReminders()
    }
    
    // MARK: - 权限管理
    func requestNotificationPermission() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(options: [.alert, .badge, .sound])
            await MainActor.run {
                self.hasNotificationPermission = granted
            }
            return granted
        } catch {
            print("请求通知权限失败: \(error)")
            return false
        }
    }
    
    private func checkNotificationPermission() {
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.hasNotificationPermission = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - 续订提醒调度
    func scheduleRenewalReminders(for product: Product) async {
        guard product.isVirtualProduct,
              product.reminderEnabled,
              let expiryDate = product.expiryDate,
              let productId = product.id else {
            return
        }
        
        // 取消现有提醒
        await cancelReminders(for: productId)
        
        let reminderDays = Int(product.subscriptionReminderDays)
        let productName = product.name ?? "订阅服务"
        
        // 计算提醒日期
        let calendar = Calendar.current
        var reminderDates = [7, 3, 1].compactMap { days in
            calendar.date(byAdding: .day, value: -days, to: expiryDate)
        }.filter { $0 > Date() } // 只保留未来的日期
        
        // 如果用户自定义了提醒天数，也添加进去
        if reminderDays > 0 && reminderDays != 7 && reminderDays != 3 && reminderDays != 1 {
            if let customDate = calendar.date(byAdding: .day, value: -reminderDays, to: expiryDate),
               customDate > Date() {
                reminderDates.append(customDate)
            }
        }
        
        // 调度提醒
        for reminderDate in reminderDates.sorted() {
            let daysUntilExpiry = calendar.dateComponents([.day], from: reminderDate, to: expiryDate).day ?? 0
            
            let title = "订阅即将到期"
            let body = "您的\(productName)将在\(daysUntilExpiry)天后到期，请及时续订以避免服务中断。"
            
            let reminder = SubscriptionReminder(
                subscriptionId: productId,
                subscriptionName: productName,
                type: .renewal,
                scheduledDate: reminderDate,
                title: title,
                body: body
            )
            
            await scheduleNotification(for: reminder)
        }
        
        // 更新最后提醒日期
        // 使用异步方式避免递归保存问题
        Task { @MainActor in
            if let productObjectID = product.objectID.isTemporaryID ? nil : product.objectID {
                let mainContext = PersistenceController.shared.container.viewContext
                do {
                    let productInContext = try mainContext.existingObject(with: productObjectID) as? Product
                    productInContext?.lastReminderDate = Date()
                    if mainContext.hasChanges {
                        try mainContext.save()
                    }
                } catch {
                    print("更新产品提醒日期失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 试用期提醒
    func scheduleTrialExpiryReminder(for product: Product) async {
        guard product.isVirtualProduct,
              product.currentSubscriptionStatus == .subscriptionTrial,
              let expiryDate = product.expiryDate,
              let productId = product.id else {
            return
        }
        
        let productName = product.name ?? "试用服务"
        let calendar = Calendar.current
        
        // 试用期结束前1天提醒
        if let reminderDate = calendar.date(byAdding: .day, value: -1, to: expiryDate),
           reminderDate > Date() {
            
            let title = "试用期即将结束"
            let body = "您的\(productName)试用期将在明天结束，请考虑是否继续订阅。"
            
            let reminder = SubscriptionReminder(
                subscriptionId: productId,
                subscriptionName: productName,
                type: .trialExpiry,
                scheduledDate: reminderDate,
                title: title,
                body: body
            )
            
            await scheduleNotification(for: reminder)
        }
    }
    
    // MARK: - 使用不足提醒
    func scheduleUnderutilizedReminder(for product: Product) async {
        guard product.isVirtualProduct,
              let productId = product.id,
              let context = product.managedObjectContext else {
            return
        }

        let productName = product.name ?? "订阅服务"

        // 检查最近30天的使用情况
        let calendar = Calendar.current
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()

        var usageCount = 0
        context.performAndWait {
            let usageRecords = (product.usageRecords?.allObjects as? [UsageRecord] ?? [])
                .filter { record in
                    guard let date = record.date else { return false }
                    return date >= thirtyDaysAgo
                }
            usageCount = usageRecords.count
        }

        // 如果使用次数少于5次，调度提醒
        if usageCount < 5 {
            let title = "订阅使用不足"
            let body = "您的\(productName)最近30天仅使用了\(usageCount)次，考虑是否需要继续订阅。"

            // 在下次续订前7天提醒
            if let expiryDate = product.expiryDate,
               let reminderDate = calendar.date(byAdding: .day, value: -7, to: expiryDate),
               reminderDate > Date() {

                let reminder = SubscriptionReminder(
                    subscriptionId: productId,
                    subscriptionName: productName,
                    type: .underutilized,
                    scheduledDate: reminderDate,
                    title: title,
                    body: body
                )

                await scheduleNotification(for: reminder)
            }
        }
    }
    
    // MARK: - 成本警告提醒
    func scheduleCostAlertReminder(for product: Product) async {
        guard product.isVirtualProduct,
              let productId = product.id else {
            return
        }
        
        let productName = product.name ?? "订阅服务"
        let monthlyCost = product.price / 12.0
        
        // 如果月费用超过500元，调度成本警告
        if monthlyCost > 500 {
            let title = "高成本订阅提醒"
            let body = "您的\(productName)月费用为¥\(String(format: "%.2f", monthlyCost))，建议评估使用价值。"
            
            // 在下次续订前14天提醒
            let calendar = Calendar.current
            if let expiryDate = product.expiryDate,
               let reminderDate = calendar.date(byAdding: .day, value: -14, to: expiryDate),
               reminderDate > Date() {
                
                let reminder = SubscriptionReminder(
                    subscriptionId: productId,
                    subscriptionName: productName,
                    type: .costAlert,
                    scheduledDate: reminderDate,
                    title: title,
                    body: body
                )
                
                await scheduleNotification(for: reminder)
            }
        }
    }
    
    // MARK: - 通知调度
    func scheduleNotification(for reminder: SubscriptionReminder) async {
        guard hasNotificationPermission else {
            print("没有通知权限，无法调度提醒")
            return
        }
        
        let content = UNMutableNotificationContent()
        content.title = reminder.title
        content.body = reminder.body
        content.sound = .default
        content.badge = 1
        
        // 添加用户信息
        content.userInfo = [
            "subscriptionId": reminder.subscriptionId.uuidString,
            "reminderType": reminder.type.rawValue,
            "subscriptionName": reminder.subscriptionName
        ]
        
        // 设置触发时间
        let triggerDate = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: reminder.scheduledDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: false)
        
        // 创建请求
        let request = UNNotificationRequest(
            identifier: reminder.notificationId,
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
            print("已调度提醒: \(reminder.title) - \(reminder.scheduledDate)")
            
            // 添加到活跃提醒列表
            await MainActor.run {
                self.activeReminders.append(reminder)
            }
        } catch {
            print("调度通知失败: \(error)")
        }
    }
    
    // MARK: - 提醒管理
    func cancelReminders(for subscriptionId: UUID) async {
        let identifiersToRemove = activeReminders
            .filter { $0.subscriptionId == subscriptionId }
            .map { $0.notificationId }
        
        notificationCenter.removePendingNotificationRequests(withIdentifiers: identifiersToRemove)
        
        await MainActor.run {
            self.activeReminders.removeAll { $0.subscriptionId == subscriptionId }
        }
    }
    
    func cancelAllReminders() async {
        notificationCenter.removeAllPendingNotificationRequests()
        
        await MainActor.run {
            self.activeReminders.removeAll()
        }
    }
    
    private func loadActiveReminders() {
        notificationCenter.getPendingNotificationRequests { requests in
            let reminders = requests.compactMap { request -> SubscriptionReminder? in
                guard let subscriptionIdString = request.content.userInfo["subscriptionId"] as? String,
                      let subscriptionId = UUID(uuidString: subscriptionIdString),
                      let reminderTypeString = request.content.userInfo["reminderType"] as? String,
                      let reminderType = ReminderType(rawValue: reminderTypeString),
                      let subscriptionName = request.content.userInfo["subscriptionName"] as? String,
                      let trigger = request.trigger as? UNCalendarNotificationTrigger,
                      let triggerDate = Calendar.current.date(from: trigger.dateComponents) else {
                    return nil
                }
                
                return SubscriptionReminder(
                    subscriptionId: subscriptionId,
                    subscriptionName: subscriptionName,
                    type: reminderType,
                    scheduledDate: triggerDate,
                    title: request.content.title,
                    body: request.content.body
                )
            }
            
            DispatchQueue.main.async {
                self.activeReminders = reminders
            }
        }
    }
    
    // MARK: - 批量操作
    func scheduleAllReminders() async {
        let subscriptions = getAllVirtualSubscriptions()
        
        for subscription in subscriptions {
            if subscription.reminderEnabled {
                await scheduleRenewalReminders(for: subscription)
                await scheduleTrialExpiryReminder(for: subscription)
                await scheduleUnderutilizedReminder(for: subscription)
                await scheduleCostAlertReminder(for: subscription)
            }
        }
    }
    
    func updateReminderSettings(for product: Product, enabled: Bool, reminderDays: Int) async {
        product.reminderEnabled = enabled
        product.subscriptionReminderDays = Int16(reminderDays)
        
        do {
            try context.save()
            
            if enabled {
                await scheduleRenewalReminders(for: product)
            } else {
                await cancelReminders(for: product.id ?? UUID())
            }
        } catch {
            print("更新提醒设置失败: \(error)")
        }
    }
    
    // MARK: - 辅助方法
    private func getAllVirtualSubscriptions() -> [Product] {
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "isVirtualProduct == YES")
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取虚拟订阅失败: \(error)")
            return []
        }
    }
}

// MARK: - 扩展：通知处理
extension SubscriptionReminderService {
    func handleNotificationResponse(_ response: UNNotificationResponse) {
        let userInfo = response.notification.request.content.userInfo
        
        guard let subscriptionIdString = userInfo["subscriptionId"] as? String,
              let subscriptionId = UUID(uuidString: subscriptionIdString),
              let reminderTypeString = userInfo["reminderType"] as? String,
              let reminderType = ReminderType(rawValue: reminderTypeString) else {
            return
        }
        
        // 根据用户操作处理
        switch response.actionIdentifier {
        case UNNotificationDefaultActionIdentifier:
            // 用户点击了通知，可以导航到订阅详情页
            NotificationCenter.default.post(
                name: .navigateToSubscription,
                object: subscriptionId
            )
            
        case "SNOOZE_ACTION":
            // 用户选择稍后提醒
            Task {
                await snoozeReminder(subscriptionId: subscriptionId, type: reminderType)
            }
            
        case "DISMISS_ACTION":
            // 用户选择忽略
            break
            
        default:
            break
        }
    }
    
    func snoozeReminder(subscriptionId: UUID, type: ReminderType) async {
        // 1小时后再次提醒
        let snoozeDate = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        
        // 找到对应的订阅
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", subscriptionId as CVarArg)
        
        do {
            if let product = try context.fetch(request).first {
                let productName = product.name ?? "订阅服务"
                let title = "订阅提醒（稍后提醒）"
                let body = "这是您稍后提醒的\(productName)订阅通知。"
                
                let reminder = SubscriptionReminder(
                    subscriptionId: subscriptionId,
                    subscriptionName: productName,
                    type: type,
                    scheduledDate: snoozeDate,
                    title: title,
                    body: body
                )
                
                await scheduleNotification(for: reminder)
            }
        } catch {
            print("处理稍后提醒失败: \(error)")
        }
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let navigateToSubscription = Notification.Name("navigateToSubscription")
}