import Foundation
import CoreData
import Combine

// MARK: - 会话仓库
class ConversationRepository: CoreDataRepository<Conversation> {
    
    // MARK: - 会话查询方法
    
    /// 获取所有会话，按最后消息时间降序排列
    func fetchAllConversations() -> [Conversation] {
        let sortDescriptors = [NSSortDescriptor(key: "lastMessageDate", ascending: false)]
        return fetchAll(sortDescriptors: sortDescriptors)
    }
    
    /// 获取最近的会话
    func fetchRecentConversations(limit: Int = 10) -> [Conversation] {
        let sortDescriptors = [NSSortDescriptor(key: "lastMessageDate", ascending: false)]
        let allConversations = fetchAll(sortDescriptors: sortDescriptors)
        return Array(allConversations.prefix(limit))
    }
    
    /// 根据标题搜索会话
    func searchConversations(query: String) -> [Conversation] {
        let predicate = NSPredicate(format: "title CONTAINS[cd] %@", query)
        let sortDescriptors = [NSSortDescriptor(key: "lastMessageDate", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    /// 获取指定日期范围内的会话
    func fetchConversations(from startDate: Date, to endDate: Date) -> [Conversation] {
        let predicate = NSPredicate(format: "startDate >= %@ AND startDate <= %@", startDate as NSDate, endDate as NSDate)
        let sortDescriptors = [NSSortDescriptor(key: "lastMessageDate", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // MARK: - 会话操作方法
    
    /// 创建新会话
    func createConversation(title: String) -> Conversation? {
        var createdConversation: Conversation? = nil
        
        let success = save { context in
            let conversation = Conversation(context: context)
            conversation.id = UUID()
            conversation.title = title
            conversation.startDate = Date()
            conversation.lastMessageDate = Date()
            createdConversation = conversation
        }
        
        return success ? createdConversation : nil
    }
    
    /// 更新会话标题
    func updateConversationTitle(conversationId: UUID, newTitle: String) -> Bool {
        guard let conversation = fetchById(id: conversationId) else { return false }
        
        return save { _ in
            conversation.title = newTitle
        }
    }
    
    /// 更新会话的最后消息时间
    func updateLastMessageDate(conversationId: UUID, date: Date = Date()) -> Bool {
        guard let conversation = fetchById(id: conversationId) else { return false }
        
        return save { _ in
            conversation.lastMessageDate = date
        }
    }
    
    /// 删除会话（会级联删除所有相关消息）
    func deleteConversation(conversationId: UUID) -> Bool {
        guard let conversation = fetchById(id: conversationId) else { return false }
        return delete(entity: conversation)
    }
}

// MARK: - 消息仓库
class MessageRepository: CoreDataRepository<Message> {
    
    // MARK: - 消息查询方法
    
    /// 获取指定会话的所有消息，按时间戳升序排列
    func fetchMessagesByConversation(conversationId: UUID) -> [Message] {
        let predicate = NSPredicate(format: "conversation.id == %@", conversationId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    /// 获取指定会话的最新消息
    func fetchLatestMessage(conversationId: UUID) -> Message? {
        let predicate = NSPredicate(format: "conversation.id == %@", conversationId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
        let messages = fetch(predicate: predicate, sortDescriptors: sortDescriptors)
        return messages.first
    }
    
    /// 获取用户消息
    func fetchUserMessages(conversationId: UUID) -> [Message] {
        let predicate = NSPredicate(format: "conversation.id == %@ AND isFromUser == YES", conversationId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    /// 获取AI助手消息
    func fetchAssistantMessages(conversationId: UUID) -> [Message] {
        let predicate = NSPredicate(format: "conversation.id == %@ AND isFromUser == NO", conversationId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // MARK: - 消息操作方法
    
    /// 添加消息到指定会话
    func addMessage(
        to conversationId: UUID,
        text: String,
        isFromUser: Bool,
        role: MessageRole,
        analysisContext: AnalysisContext? = nil,
        attachments: [MessageAttachment] = []
    ) -> Message? {
        // 首先查找会话
        let conversationRequest = Conversation.fetchRequest()
        conversationRequest.predicate = NSPredicate(format: "id == %@", conversationId as CVarArg)

        guard let conversation = try? getContext().fetch(conversationRequest).first else {
            return nil
        }
        
        var createdMessage: Message? = nil
        
        let success = save { context in
            let message = Message(context: context)
            message.id = UUID()
            message.text = text
            message.timestamp = Date()
            message.isFromUser = isFromUser
            message.role = role.rawValue
            message.conversation = conversation
            
            // 序列化复杂数据
            if let analysisContext = analysisContext {
                message.analysisContextData = try? JSONEncoder().encode(analysisContext)
            }
            
            if !attachments.isEmpty {
                message.attachmentsData = try? JSONEncoder().encode(attachments)
            }
            
            createdMessage = message
        }
        
        return success ? createdMessage : nil
    }
    
    /// 删除消息
    func deleteMessage(messageId: UUID) -> Bool {
        guard let message = fetchById(id: messageId) else { return false }
        return delete(entity: message)
    }
}

// MARK: - 数据转换扩展
extension MessageRepository {
    
    /// 将Core Data Message转换为ConversationMessage
    func convertToConversationMessage(_ message: Message) -> ConversationMessage? {
        guard let roleString = message.role,
              let role = MessageRole(rawValue: roleString) else {
            return nil
        }
        
        // 反序列化分析上下文
        var analysisContext: AnalysisContext? = nil
        if let contextData = message.analysisContextData {
            analysisContext = try? JSONDecoder().decode(AnalysisContext.self, from: contextData)
        }
        
        // 反序列化附件
        var attachments: [MessageAttachment] = []
        if let attachmentsData = message.attachmentsData {
            attachments = (try? JSONDecoder().decode([MessageAttachment].self, from: attachmentsData)) ?? []
        }
        
        return ConversationMessage(
            role: role,
            content: message.text ?? "",
            analysisContext: analysisContext,
            attachments: attachments
        )
    }
    
    /// 将ConversationMessage转换为Core Data Message（用于批量导入等场景）
    func convertFromConversationMessage(
        _ conversationMessage: ConversationMessage,
        to conversationId: UUID
    ) -> Message? {
        return addMessage(
            to: conversationId,
            text: conversationMessage.content,
            isFromUser: conversationMessage.role == .user,
            role: conversationMessage.role,
            analysisContext: conversationMessage.analysisContext,
            attachments: conversationMessage.attachments
        )
    }
    
    /// 批量转换会话消息
    func convertToConversationMessages(_ messages: [Message]) -> [ConversationMessage] {
        return messages.compactMap { convertToConversationMessage($0) }
    }
}

// MARK: - 便利方法扩展
extension ConversationRepository {
    
    /// 获取会话的消息数量
    func getMessageCount(conversationId: UUID) -> Int {
        let predicate = NSPredicate(format: "conversation.id == %@", conversationId as CVarArg)
        let request = Message.fetchRequest()
        request.predicate = predicate
        
        do {
            return try getContext().count(for: request)
        } catch {
            print("Error counting messages for conversation \(conversationId): \(error)")
            return 0
        }
    }
    
    /// 检查会话是否为空
    func isConversationEmpty(conversationId: UUID) -> Bool {
        return getMessageCount(conversationId: conversationId) == 0
    }
}
