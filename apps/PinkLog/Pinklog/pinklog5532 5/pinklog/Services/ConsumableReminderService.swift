//
//  ConsumableReminderService.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 消耗品提醒服务
@MainActor
class ConsumableReminderService: ObservableObject {
    
    // MARK: - 单例
    static let shared = ConsumableReminderService()
    
    // MARK: - 私有属性
    private let productRepository: ProductRepository
    private let reminderManager: ProactiveReminderManager
    private var cancellables = Set<AnyCancellable>()
    private var checkTimer: Timer?
    
    // MARK: - 初始化
    private init() {
        self.productRepository = ProductRepository.shared
        self.reminderManager = ProactiveReminderManager.shared
        
        setupPeriodicCheck()
        setupProductChangeObserver()
    }
    
    // MARK: - 公共方法
    
    /// 检查所有消耗品的库存状态并生成提醒
    func checkConsumableStock() {
        let allProducts = productRepository.fetchAllProducts()
        let consumableProducts = allProducts.filter { $0.isConsumable == true }
        
        for product in consumableProducts {
            checkProductStock(product)
        }
    }
    
    /// 检查单个产品的库存状态
    func checkProductStock(_ product: Product) {
        guard product.isConsumable == true else { return }
        
        // 检查生命周期状态变化
        checkLifecycleStatusChanges(for: product)
        
        // 检查是否需要库存警告
        if product.needsStockAlert {
            generateLowStockReminder(for: product)
        }
        
        // 检查是否即将用完
        if let estimatedDays = product.estimatedDaysUntilEmpty,
           estimatedDays <= 3 && estimatedDays > 0 {
            generateRunningOutReminder(for: product, daysLeft: estimatedDays)
        }
        
        // 检查是否已用完
        if product.stockPercentage <= 0.01 { // 小于1%视为用完
            generateOutOfStockReminder(for: product)
        }
    }
    
    /// 检查生命周期状态变化
    private func checkLifecycleStatusChanges(for product: Product) {
        let currentStatus = product.lifecycleStatus
        
        switch currentStatus {
        case .depleted:
            generateDepletedStatusReminder(for: product)
        case .expired:
            generateExpiredStatusReminder(for: product)
        case .discontinued:
            // 停用状态通常是用户主动操作，不需要提醒
            break
        case .archived:
            // 归档状态通常是用户主动操作，不需要提醒
            break
        default:
            break
        }
        
        // 检查是否有状态更新建议
        let suggestions = product.getLifecycleStatusSuggestions()
        if !suggestions.isEmpty {
            generateLifecycleStatusSuggestionReminder(for: product, suggestions: suggestions)
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置定期检查
    private func setupPeriodicCheck() {
        // 每小时检查一次
        checkTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkConsumableStock()
            }
        }
        
        // 立即执行一次检查
        checkConsumableStock()
    }
    
    /// 设置产品变化监听
    private func setupProductChangeObserver() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                Task { @MainActor in
                    // 延迟检查，避免频繁触发
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                    self?.checkConsumableStock()
                }
            }
            .store(in: &cancellables)
    }
    
    /// 生成低库存提醒
    private func generateLowStockReminder(for product: Product) {
        let reminderId = "low_stock_\(product.id?.uuidString ?? "unknown")"
        
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .warning &&
            $0.title.contains("库存不足") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的库存提醒
        }
        
        let stockPercentage = Int(product.stockPercentage * 100)
        let currentQuantity = formatQuantity(product.actualCurrentQuantity)
        let unitType = product.unitType ?? ""
        
        let reminder = ProactiveReminder(
            type: .warning,
            priority: stockPercentage < 10 ? .high : .medium,
            title: "\(product.name ?? "消耗品")库存不足",
            content: "当前库存仅剩 \(stockPercentage)%（\(currentQuantity)\(unitType)），建议及时补充库存。",
            actionButtons: ["立即补充", "稍后提醒", "忽略"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 0.95,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成即将用完提醒
    private func generateRunningOutReminder(for product: Product, daysLeft: Int) {
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .reminder &&
            $0.title.contains("即将用完") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的即将用完提醒
        }
        
        let currentQuantity = formatQuantity(product.actualCurrentQuantity)
        let unitType = product.unitType ?? ""
        
        let reminder = ProactiveReminder(
            type: .reminder,
            priority: .high,
            title: "\(product.name ?? "消耗品")即将用完",
            content: "根据您的使用习惯，\(product.name ?? "该消耗品")预计将在 \(daysLeft) 天后用完（当前剩余：\(currentQuantity)\(unitType)）。建议提前准备补充。",
            actionButtons: ["立即补充", "设置提醒", "查看详情"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 0.85,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: daysLeft + 1, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成已用完提醒
    private func generateOutOfStockReminder(for product: Product) {
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .warning &&
            $0.title.contains("已用完") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的已用完提醒
        }
        
        let reminder = ProactiveReminder(
            type: .warning,
            priority: .high,
            title: "\(product.name ?? "消耗品")已用完",
            content: "\(product.name ?? "该消耗品")库存已耗尽，请及时补充以免影响正常使用。",
            actionButtons: ["立即补充", "暂时忽略"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 1.0,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成已耗尽状态提醒
    private func generateDepletedStatusReminder(for product: Product) {
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .warning &&
            $0.title.contains("已耗尽") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的耗尽提醒
        }
        
        let reminder = ProactiveReminder(
            type: .warning,
            priority: .high,
            title: "\(product.name ?? "消耗品")已耗尽",
            content: "\(product.name ?? "该消耗品")库存已完全耗尽，建议及时补充库存或考虑归档该产品。",
            actionButtons: ["补充库存", "归档产品", "稍后处理"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 1.0,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成已过期状态提醒
    private func generateExpiredStatusReminder(for product: Product) {
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .warning &&
            $0.title.contains("已过期") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的过期提醒
        }
        
        let reminder = ProactiveReminder(
            type: .warning,
            priority: .high,
            title: "\(product.name ?? "消耗品")已过期",
            content: "\(product.name ?? "该消耗品")已超过保质期或长期未使用，建议检查产品状态并决定是否继续使用。",
            actionButtons: ["重新激活", "归档产品", "查看详情"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 0.9,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成生命周期状态建议提醒
    private func generateLifecycleStatusSuggestionReminder(for product: Product, suggestions: [LifecycleStatusSuggestion]) {
        // 只处理高优先级的建议
        let highPrioritySuggestions = suggestions.filter { $0.priority == .high }
        guard !highPrioritySuggestions.isEmpty else { return }
        
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .suggestion &&
            $0.title.contains("状态更新建议") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的建议提醒
        }
        
        let suggestion = highPrioritySuggestions.first!
        let actionText = suggestion.type == .archive ? "归档" : 
                        suggestion.type == .discontinue ? "停用" : "更新状态"
        
        let reminder = ProactiveReminder(
            type: .suggestion,
            priority: .medium,
            title: "\(product.name ?? "消耗品")状态更新建议",
            content: suggestion.description,
            actionButtons: [actionText, "查看详情", "忽略"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 0.8,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 14, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成自动归档建议提醒
    func generateAutoArchiveSuggestionReminder(for product: Product, suggestion: ArchiveSuggestion) {
        // 检查是否已存在相同的提醒
        let existingReminders = reminderManager.reminders.filter {
            $0.relatedProductIds.contains(product.id ?? UUID()) &&
            $0.type == .suggestion &&
            $0.title.contains("归档建议") &&
            $0.status == .unread
        }
        
        if !existingReminders.isEmpty {
            return // 已存在未读的归档建议提醒
        }
        
        let confidenceText = String(format: "%.0f%%", suggestion.confidence * 100)
        let reasonText = suggestion.reason.description
        
        let reminder = ProactiveReminder(
            type: .suggestion,
            priority: suggestion.confidence > 0.8 ? .high : .medium,
            title: "\(product.name ?? "消耗品")归档建议",
            content: "\(reasonText)，系统建议归档此产品（置信度：\(confidenceText)）。",
            actionButtons: ["立即归档", "推迟归档", "忽略建议"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: suggestion.confidence,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    
    /// 生成归档确认提醒
    func generateArchiveConfirmationReminder(for product: Product) {
        let reminder = ProactiveReminder(
            type: .insight,
            priority: .low,
            title: "\(product.name ?? "消耗品")已归档",
            content: "\(product.name ?? "该消耗品")已成功归档。如需重新使用，可在归档列表中找到并恢复。",
            actionButtons: ["查看归档列表", "确定"],
            relatedProductIds: [product.id ?? UUID()],
            confidence: 1.0,
            timestamp: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 3, to: Date())
        )
        
        reminderManager.addReminder(reminder)
    }
    

    
    /// 格式化数量显示
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
    
    deinit {
        checkTimer?.invalidate()
    }
}