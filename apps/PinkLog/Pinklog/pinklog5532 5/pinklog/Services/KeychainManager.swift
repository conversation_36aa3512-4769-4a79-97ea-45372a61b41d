import Foundation
import Security
import UIKit

class KeychainManager {
    static let shared = KeychainManager()
    
    private let service = "top.trysapp.pinklog"
    private let productCountKey = "product_count"
    private let premiumStatusKey = "premium_status"
    private let deviceUsageHistoryKey = "device_usage_history" // 新增：设备使用历史
    private let maxFreeItems = 10
    
    private init() {
        initializeDeviceUsageHistory()
    }
    
    var productCount: Int {
        get {
            return getInt(for: productCountKey) ?? 0
        }
        set {
            set(newValue, for: productCountKey)
        }
    }
    
    var isPremiumUser: Bool {
        get {
            return getBool(for: premiumStatusKey) ?? false
        }
        set {
            set(newValue, for: premiumStatusKey)
        }
    }
    
    func incrementProductCount() {
        productCount += 1
        recordDeviceItemUsage()
    }
    
    func canAddProduct() -> Bool {
        return isPremiumUser || getTotalItemsUsed() < maxFreeItems
    }
    
    func getRemainingFreeItems() -> Int {
        if isPremiumUser {
            return Int.max
        }
        return max(0, maxFreeItems - getTotalItemsUsed())
    }
    
    // MARK: - 设备使用历史管理
    
    /// 初始化设备使用历史
    private func initializeDeviceUsageHistory() {
        let deviceId = getDeviceIdentifier()
        var history = getDeviceUsageHistory()
        
        if history[deviceId] == nil {
            history[deviceId] = 0
            setDeviceUsageHistory(history)
            print("🔒 初始化设备使用历史: \(deviceId)")
        }
    }
    
    /// 获取设备唯一标识符
    private func getDeviceIdentifier() -> String {
        // 使用设备的identifierForVendor，在APP卸载重装后会改变
        // 但我们需要一个更持久的标识符
        if let identifier = UIDevice.current.identifierForVendor?.uuidString {
            return identifier
        }
        
        // 如果无法获取identifierForVendor，生成一个随机标识符并存储在Keychain中
        let fallbackKey = "device_fallback_id"
        if let existingId = getString(for: fallbackKey) {
            return existingId
        }
        
        let newId = UUID().uuidString
        set(newId, for: fallbackKey)
        return newId
    }
    
    /// 获取设备使用历史记录
    private func getDeviceUsageHistory() -> [String: Int] {
        guard let data = getData(for: deviceUsageHistoryKey),
              let history = try? JSONDecoder().decode([String: Int].self, from: data) else {
            return [:]
        }
        return history
    }
    
    /// 设置设备使用历史记录
    private func setDeviceUsageHistory(_ history: [String: Int]) {
        do {
            let data = try JSONEncoder().encode(history)
            set(data, for: deviceUsageHistoryKey)
        } catch {
            print("❌ 保存设备使用历史失败: \(error)")
        }
    }
    
    /// 获取总共使用的物品数量（包括历史记录）
    private func getTotalItemsUsed() -> Int {
        let deviceId = getDeviceIdentifier()
        let history = getDeviceUsageHistory()
        let currentDeviceUsage = history[deviceId] ?? 0
        
        // 返回当前设备的历史使用量和当前计数的最大值
        return max(currentDeviceUsage, productCount)
    }
    
    /// 记录设备使用物品
    func recordDeviceItemUsage() {
        let deviceId = getDeviceIdentifier()
        var history = getDeviceUsageHistory()
        let currentUsage = history[deviceId] ?? 0
        
        // 更新设备使用记录
        history[deviceId] = max(currentUsage + 1, productCount)
        setDeviceUsageHistory(history)
        
        print("🔒 记录设备物品使用: \(deviceId) -> \(history[deviceId] ?? 0)")
    }
    
    /// 同步设备使用历史
    func syncDeviceUsageHistory(with actualCount: Int) {
        let deviceId = getDeviceIdentifier()
        var history = getDeviceUsageHistory()
        let currentDeviceUsage = history[deviceId] ?? 0
        
        // 确保设备使用历史至少记录了实际数量
        if currentDeviceUsage < actualCount {
            history[deviceId] = actualCount
            setDeviceUsageHistory(history)
            print("🔒 同步设备使用历史: \(deviceId) -> \(actualCount)")
        }
    }
    
    private func set(_ value: Int, for key: String) {
        let data = Data(String(value).utf8)
        set(data, for: key)
    }
    
    private func set(_ value: Bool, for key: String) {
        let data = Data(String(value).utf8)
        set(data, for: key)
    }
    
    private func set(_ data: Data, for key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }
    
    private func getInt(for key: String) -> Int? {
        guard let data = getData(for: key),
              let string = String(data: data, encoding: .utf8),
              let value = Int(string) else {
            return nil
        }
        return value
    }
    
    private func getBool(for key: String) -> Bool? {
        guard let data = getData(for: key),
              let string = String(data: data, encoding: .utf8),
              let value = Bool(string) else {
            return nil
        }
        return value
    }
    
    private func getString(for key: String) -> String? {
        guard let data = getData(for: key),
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }
        return string
    }
    
    private func set(_ value: String, for key: String) {
        let data = Data(value.utf8)
        set(data, for: key)
    }
    
    private func getData(for key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecMatchLimit as String: kSecMatchLimitOne,
            kSecReturnData as String: true
        ]
        
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        if status == errSecSuccess {
            return dataTypeRef as? Data
        }
        
        return nil
    }
    
    func deleteAll() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service
        ]
        
        SecItemDelete(query as CFDictionary)
    }
}