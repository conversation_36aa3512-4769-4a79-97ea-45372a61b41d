import Foundation
import Combine

/// 全局缓存管理器 - 统一管理所有组件的缓存清理，避免重复定时器
class GlobalCacheManager {
    static let shared = GlobalCacheManager()
    
    // MARK: - 私有属性
    private var cacheCleanupTimer: Timer?
    private var registeredCaches: [String: CacheCleanupHandler] = [:]
    private let cleanupInterval: TimeInterval = 120 // 2分钟统一清理间隔
    private var isRunning = false
    
    // MARK: - 缓存清理处理器协议
    struct CacheCleanupHandler {
        let name: String
        let cleanupAction: () -> Void
        let priority: CleanupPriority
    }
    
    enum CleanupPriority: Int, CaseIterable {
        case high = 1      // 高优先级：内存敏感的缓存
        case medium = 2    // 中优先级：分析结果缓存
        case low = 3       // 低优先级：UI相关缓存
    }
    
    // MARK: - 初始化
    private init() {
        print("🗂️ GlobalCacheManager 初始化")
    }
    
    deinit {
        stopCacheCleanup()
    }
    
    // MARK: - 公共方法
    
    /// 注册缓存清理处理器
    func registerCache(
        name: String,
        priority: CleanupPriority = .medium,
        cleanupAction: @escaping () -> Void
    ) {
        // 检查是否已经注册过相同名称的缓存
        if registeredCaches[name] != nil {
            print("⚠️ 缓存清理器已存在，跳过重复注册: \(name) (当前注册数: \(registeredCaches.count))")
            return
        }
        
        let handler = CacheCleanupHandler(
            name: name,
            cleanupAction: cleanupAction,
            priority: priority
        )
        
        registeredCaches[name] = handler
        print("📝 注册缓存清理器: \(name) (优先级: \(priority), 总数: \(registeredCaches.count))")
        
        // 如果这是第一个注册的缓存，启动定时器
        if registeredCaches.count == 1 && !isRunning {
            startCacheCleanup()
        }
    }
    
    /// 注销缓存清理处理器
    func unregisterCache(name: String) {
        registeredCaches.removeValue(forKey: name)
        print("🗑️ 注销缓存清理器: \(name)")
        
        // 如果没有注册的缓存了，停止定时器
        if registeredCaches.isEmpty {
            stopCacheCleanup()
        }
    }
    
    /// 手动触发缓存清理
    func performCleanup() {
        guard !registeredCaches.isEmpty else {
            print("ℹ️ 没有注册的缓存清理器，跳过清理")
            return
        }
        
        print("🧹 开始全局缓存清理，共\(registeredCaches.count)个缓存")
        
        // 按优先级排序执行清理
        let sortedHandlers = registeredCaches.values.sorted { $0.priority.rawValue < $1.priority.rawValue }
        
        for handler in sortedHandlers {
            autoreleasepool {
                handler.cleanupAction()
                print("✅ 完成缓存清理: \(handler.name)")
            }
        }
        
        print("🎉 全局缓存清理完成")
    }
    
    /// 获取缓存状态
    func getCacheStatus() -> [String: Any] {
        return [
            "registeredCaches": registeredCaches.count,
            "isRunning": isRunning,
            "cleanupInterval": cleanupInterval,
            "cacheNames": Array(registeredCaches.keys)
        ]
    }
    
    // MARK: - 私有方法
    
    /// 启动缓存清理定时器
    private func startCacheCleanup() {
        guard !isRunning else {
            print("⚠️ 全局缓存清理定时器已在运行")
            return
        }
        
        cacheCleanupTimer = Timer.scheduledTimer(withTimeInterval: cleanupInterval, repeats: true) { [weak self] _ in
            self?.performCleanup()
        }
        
        isRunning = true
        print("✅ 启动全局缓存清理定时器，间隔\(Int(cleanupInterval))秒")
    }
    
    /// 停止缓存清理定时器
    private func stopCacheCleanup() {
        cacheCleanupTimer?.invalidate()
        cacheCleanupTimer = nil
        isRunning = false
        print("🛑 停止全局缓存清理定时器")
    }
}

// MARK: - 便利扩展
extension GlobalCacheManager {
    
    /// 便利方法：注册简单的缓存清理
    func registerSimpleCache(name: String, cleanupAction: @escaping () -> Void) {
        registerCache(name: name, priority: .medium, cleanupAction: cleanupAction)
    }
    
    /// 便利方法：注册高优先级缓存清理（内存敏感）
    func registerHighPriorityCache(name: String, cleanupAction: @escaping () -> Void) {
        registerCache(name: name, priority: .high, cleanupAction: cleanupAction)
    }
    
    /// 便利方法：注册低优先级缓存清理（UI相关）
    func registerLowPriorityCache(name: String, cleanupAction: @escaping () -> Void) {
        registerCache(name: name, priority: .low, cleanupAction: cleanupAction)
    }
}
