import Foundation
import os.log

/// 缓存优化器
/// 提供智能缓存管理和优化策略
class CacheOptimizer {
    
    // MARK: - 单例
    static let shared = CacheOptimizer()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.pinklog.cache", category: "optimizer")
    private var cacheMetrics: [String: CacheMetrics] = [:]
    private let metricsQueue = DispatchQueue(label: "com.pinklog.cache.metrics", qos: .utility)
    private var optimizationTimer: Timer?
    
    // MARK: - 缓存指标
    struct CacheMetrics {
        let cacheName: String
        var hitCount: Int = 0
        var missCount: Int = 0
        var evictionCount: Int = 0
        var totalSize: Int64 = 0
        var itemCount: Int = 0
        var lastAccessTime: Date = Date()
        var creationTime: Date = Date()
        
        var hitRate: Double {
            let total = hitCount + missCount
            guard total > 0 else { return 0 }
            return Double(hitCount) / Double(total)
        }
        
        var averageItemSize: Int64 {
            guard itemCount > 0 else { return 0 }
            return totalSize / Int64(itemCount)
        }
        
        var efficiency: Double {
            // 综合考虑命中率和访问频率
            let accessFrequency = Double(hitCount + missCount) / Date().timeIntervalSince(creationTime)
            return hitRate * min(accessFrequency / 10.0, 1.0) // 标准化访问频率
        }
    }
    
    // MARK: - 缓存策略
    enum CacheStrategy {
        case lru           // 最近最少使用
        case lfu           // 最少使用频率
        case ttl           // 生存时间
        case adaptive      // 自适应策略
    }
    
    // MARK: - 优化建议
    struct OptimizationRecommendation {
        let cacheName: String
        let currentStrategy: CacheStrategy
        let recommendedStrategy: CacheStrategy
        let reason: String
        let expectedImprovement: Double
        let priority: Priority
        
        enum Priority {
            case low, medium, high, critical
        }
    }
    
    private init() {
        startOptimizationTimer()
    }
    
    deinit {
        optimizationTimer?.invalidate()
    }
    
    // MARK: - 公共方法
    
    /// 记录缓存命中
    /// - Parameters:
    ///   - cacheName: 缓存名称
    ///   - itemSize: 项目大小
    func recordCacheHit(cacheName: String, itemSize: Int64 = 0) {
        metricsQueue.async {
            var metrics = self.cacheMetrics[cacheName] ?? CacheMetrics(cacheName: cacheName)
            metrics.hitCount += 1
            metrics.lastAccessTime = Date()
            if itemSize > 0 {
                metrics.totalSize += itemSize
                metrics.itemCount += 1
            }
            self.cacheMetrics[cacheName] = metrics
        }
    }
    
    /// 记录缓存未命中
    /// - Parameter cacheName: 缓存名称
    func recordCacheMiss(cacheName: String) {
        metricsQueue.async {
            var metrics = self.cacheMetrics[cacheName] ?? CacheMetrics(cacheName: cacheName)
            metrics.missCount += 1
            metrics.lastAccessTime = Date()
            self.cacheMetrics[cacheName] = metrics
        }
    }
    
    /// 记录缓存驱逐
    /// - Parameters:
    ///   - cacheName: 缓存名称
    ///   - itemSize: 被驱逐项目的大小
    func recordCacheEviction(cacheName: String, itemSize: Int64 = 0) {
        metricsQueue.async {
            var metrics = self.cacheMetrics[cacheName] ?? CacheMetrics(cacheName: cacheName)
            metrics.evictionCount += 1
            if itemSize > 0 {
                metrics.totalSize -= itemSize
                metrics.itemCount -= 1
            }
            self.cacheMetrics[cacheName] = metrics
        }
    }
    
    /// 更新缓存大小
    /// - Parameters:
    ///   - cacheName: 缓存名称
    ///   - totalSize: 总大小
    ///   - itemCount: 项目数量
    func updateCacheSize(cacheName: String, totalSize: Int64, itemCount: Int) {
        metricsQueue.async {
            var metrics = self.cacheMetrics[cacheName] ?? CacheMetrics(cacheName: cacheName)
            metrics.totalSize = totalSize
            metrics.itemCount = itemCount
            self.cacheMetrics[cacheName] = metrics
        }
    }
    
    /// 获取缓存指标
    /// - Parameter cacheName: 缓存名称
    /// - Returns: 缓存指标
    func getCacheMetrics(for cacheName: String) -> CacheMetrics? {
        return metricsQueue.sync {
            return cacheMetrics[cacheName]
        }
    }
    
    /// 获取所有缓存指标
    /// - Returns: 所有缓存指标
    func getAllCacheMetrics() -> [String: CacheMetrics] {
        return metricsQueue.sync {
            return cacheMetrics
        }
    }
    
    /// 生成优化建议
    /// - Returns: 优化建议列表
    func generateOptimizationRecommendations() -> [OptimizationRecommendation] {
        let metrics = getAllCacheMetrics()
        var recommendations: [OptimizationRecommendation] = []
        
        for (cacheName, metric) in metrics {
            let recommendation = analyzeCache(cacheName: cacheName, metrics: metric)
            if let rec = recommendation {
                recommendations.append(rec)
            }
        }
        
        return recommendations.sorted { $0.priority.rawValue > $1.priority.rawValue }
    }
    
    /// 获取缓存性能报告
    /// - Returns: 性能报告字符串
    func generateCacheReport() -> String {
        let metrics = getAllCacheMetrics()
        
        var report = "=== 缓存性能报告 ===\n"
        report += "生成时间: \(DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium))\n\n"
        
        if metrics.isEmpty {
            report += "暂无缓存数据\n"
            return report
        }
        
        // 总体统计
        let totalHits = metrics.values.reduce(0) { $0 + $1.hitCount }
        let totalMisses = metrics.values.reduce(0) { $0 + $1.missCount }
        let totalSize = metrics.values.reduce(0) { $0 + $1.totalSize }
        let overallHitRate = Double(totalHits) / Double(totalHits + totalMisses)
        
        report += "总体统计:\n"
        report += "  总命中次数: \(totalHits)\n"
        report += "  总未命中次数: \(totalMisses)\n"
        report += "  总体命中率: \(String(format: "%.1f", overallHitRate * 100))%\n"
        report += "  总缓存大小: \(formatMemorySize(totalSize))\n\n"
        
        // 各缓存详情
        for (cacheName, metric) in metrics.sorted(by: { $0.value.efficiency > $1.value.efficiency }) {
            report += "缓存: \(cacheName)\n"
            report += "  命中率: \(String(format: "%.1f", metric.hitRate * 100))%\n"
            report += "  效率评分: \(String(format: "%.2f", metric.efficiency))\n"
            report += "  项目数量: \(metric.itemCount)\n"
            report += "  总大小: \(formatMemorySize(metric.totalSize))\n"
            report += "  平均项目大小: \(formatMemorySize(metric.averageItemSize))\n"
            report += "  驱逐次数: \(metric.evictionCount)\n"
            report += "  最后访问: \(DateFormatter.localizedString(from: metric.lastAccessTime, dateStyle: .none, timeStyle: .medium))\n\n"
        }
        
        // 优化建议
        let recommendations = generateOptimizationRecommendations()
        if !recommendations.isEmpty {
            report += "优化建议:\n"
            for rec in recommendations.prefix(5) {
                report += "  \(rec.cacheName): \(rec.reason)\n"
            }
        }
        
        return report
    }
    
    /// 清理缓存指标
    /// - Parameter cacheName: 缓存名称，如果为nil则清理所有
    func clearCacheMetrics(for cacheName: String? = nil) {
        metricsQueue.async {
            if let cacheName = cacheName {
                self.cacheMetrics.removeValue(forKey: cacheName)
            } else {
                self.cacheMetrics.removeAll()
            }
        }
    }
    
    /// 获取推荐的缓存大小
    /// - Parameters:
    ///   - cacheName: 缓存名称
    ///   - currentSize: 当前大小
    /// - Returns: 推荐大小
    func getRecommendedCacheSize(for cacheName: String, currentSize: Int) -> Int {
        guard let metrics = getCacheMetrics(for: cacheName) else {
            return currentSize
        }
        
        // 基于命中率和驱逐频率调整
        if metrics.hitRate > 0.9 && metrics.evictionCount > Int(Double(metrics.hitCount) * 0.1) {
            // 命中率高但驱逐频繁，建议增大缓存
            return min(currentSize * 2, 1000)
        } else if metrics.hitRate < 0.5 {
            // 命中率低，建议减小缓存
            return max(currentSize / 2, 10)
        }
        
        return currentSize
    }
    
    /// 获取推荐的TTL时间
    /// - Parameters:
    ///   - cacheName: 缓存名称
    ///   - currentTTL: 当前TTL
    /// - Returns: 推荐TTL
    func getRecommendedTTL(for cacheName: String, currentTTL: TimeInterval) -> TimeInterval {
        guard let metrics = getCacheMetrics(for: cacheName) else {
            return currentTTL
        }
        
        let timeSinceLastAccess = Date().timeIntervalSince(metrics.lastAccessTime)
        
        // 基于访问模式调整TTL
        if timeSinceLastAccess < 300 { // 5分钟内有访问
            return max(currentTTL * 1.5, 3600) // 增加到至少1小时
        } else if timeSinceLastAccess > 1800 { // 30分钟无访问
            return max(currentTTL * 0.5, 300) // 减少到至少5分钟
        }
        
        return currentTTL
    }
    
    // MARK: - 私有方法
    
    private func startOptimizationTimer() {
        optimizationTimer = Timer.scheduledTimer(withTimeInterval: 600, repeats: true) { _ in
            self.performPeriodicOptimization()
        }
    }
    
    private func performPeriodicOptimization() {
        let recommendations = generateOptimizationRecommendations()
        
        // 记录高优先级的优化建议
        for rec in recommendations.filter({ $0.priority == .high || $0.priority == .critical }) {
            logger.warning("缓存优化建议 - \(rec.cacheName): \(rec.reason)")
        }
        
        // 清理过期指标
        cleanupOldMetrics()
    }
    
    private func analyzeCache(cacheName: String, metrics: CacheMetrics) -> OptimizationRecommendation? {
        let currentStrategy: CacheStrategy = .adaptive // 假设当前使用自适应策略
        
        // 分析命中率
        if metrics.hitRate < 0.3 {
            return OptimizationRecommendation(
                cacheName: cacheName,
                currentStrategy: currentStrategy,
                recommendedStrategy: .ttl,
                reason: "命中率过低(\(String(format: "%.1f", metrics.hitRate * 100))%)，建议使用TTL策略减少无效缓存",
                expectedImprovement: 0.4,
                priority: .high
            )
        }
        
        // 分析驱逐频率
        if metrics.evictionCount > Int(Double(metrics.hitCount) * 0.2) {
            return OptimizationRecommendation(
                cacheName: cacheName,
                currentStrategy: currentStrategy,
                recommendedStrategy: .lru,
                reason: "驱逐频率过高，建议使用LRU策略优化内存使用",
                expectedImprovement: 0.3,
                priority: .medium
            )
        }
        
        // 分析访问模式
        let timeSinceCreation = Date().timeIntervalSince(metrics.creationTime)
        let accessFrequency = Double(metrics.hitCount + metrics.missCount) / timeSinceCreation
        
        if accessFrequency < 0.01 { // 每100秒少于1次访问
            return OptimizationRecommendation(
                cacheName: cacheName,
                currentStrategy: currentStrategy,
                recommendedStrategy: .ttl,
                reason: "访问频率过低，建议使用较短的TTL",
                expectedImprovement: 0.2,
                priority: .low
            )
        }
        
        return nil
    }
    
    private func cleanupOldMetrics() {
        metricsQueue.async {
            let cutoffDate = Date().addingTimeInterval(-7200) // 2小时前
            
            for (cacheName, metrics) in self.cacheMetrics {
                if metrics.lastAccessTime < cutoffDate {
                    self.cacheMetrics.removeValue(forKey: cacheName)
                    self.logger.info("清理过期缓存指标: \(cacheName)")
                }
            }
        }
    }
    
    private func formatMemorySize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB, .useBytes]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Priority扩展

extension CacheOptimizer.OptimizationRecommendation.Priority {
    var rawValue: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
}
