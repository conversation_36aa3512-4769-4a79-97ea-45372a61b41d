import Foundation
import CoreData
import CloudKit
import Combine
import BackgroundTasks
import os.log
import UIKit

// MARK: - 备份管理错误

/// 备份管理错误类型
enum BackupManagerError: Error, LocalizedError {
    case backupInProgress
    case restoreInProgress
    case cloudKitUnavailable
    case dataExportFailed(String)
    case dataImportFailed(String)
    case versionCreationFailed(String)
    case networkUnavailable
    case insufficientStorage
    case userCancelled
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .backupInProgress:
            return "备份正在进行中"
        case .restoreInProgress:
            return "恢复正在进行中"
        case .cloudKitUnavailable:
            return "CloudKit服务不可用"
        case .dataExportFailed(let reason):
            return "数据导出失败: \(reason)"
        case .dataImportFailed(let reason):
            return "数据导入失败: \(reason)"
        case .versionCreationFailed(let reason):
            return "版本创建失败: \(reason)"
        case .networkUnavailable:
            return "网络连接不可用"
        case .insufficientStorage:
            return "存储空间不足"
        case .userCancelled:
            return "用户取消操作"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - 备份操作结果

/// 备份操作结果
struct BackupOperationResult {
    let success: Bool
    let version: BackupVersion?
    let error: BackupError?
    let duration: TimeInterval
    let entitiesProcessed: Int
    let warnings: [String]
    
    static func success(
        version: BackupVersion,
        duration: TimeInterval,
        entities: Int,
        warnings: [String] = []
    ) -> BackupOperationResult {
        return BackupOperationResult(
            success: true,
            version: version,
            error: nil,
            duration: duration,
            entitiesProcessed: entities,
            warnings: warnings
        )
    }
    
    static func failure(
        error: BackupError,
        duration: TimeInterval = 0,
        entities: Int = 0
    ) -> BackupOperationResult {
        return BackupOperationResult(
            success: false,
            version: nil,
            error: error,
            duration: duration,
            entitiesProcessed: entities,
            warnings: []
        )
    }
}

// MARK: - 恢复操作结果

/// 恢复操作结果
struct RestoreOperationResult {
    let success: Bool
    let version: BackupVersion?
    let error: BackupError?
    let duration: TimeInterval
    let entitiesRestored: Int
    let warnings: [String]

    static func success(
        version: BackupVersion,
        duration: TimeInterval,
        entities: Int,
        warnings: [String] = []
    ) -> RestoreOperationResult {
        return RestoreOperationResult(
            success: true,
            version: version,
            error: nil,
            duration: duration,
            entitiesRestored: entities,
            warnings: warnings
        )
    }

    static func failure(
        error: BackupError,
        duration: TimeInterval = 0,
        entities: Int = 0
    ) -> RestoreOperationResult {
        return RestoreOperationResult(
            success: false,
            version: nil,
            error: error,
            duration: duration,
            entitiesRestored: entities,
            warnings: []
        )
    }
}

// MARK: - 备份配置

/// 备份配置
struct BackupConfiguration: Codable {
    let autoBackupEnabled: Bool
    let backupFrequency: BackupFrequency
    let wifiOnlyBackup: Bool
    let lowPowerModeBackup: Bool
    let maxBackupVersions: Int
    let compressionEnabled: Bool
    
    enum BackupFrequency: String, CaseIterable, Codable {
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case manual = "manual"
        
        var displayName: String {
            switch self {
            case .daily: return "每日"
            case .weekly: return "每周"
            case .monthly: return "每月"
            case .manual: return "手动"
            }
        }
        
        var interval: TimeInterval {
            switch self {
            case .daily: return 24 * 60 * 60
            case .weekly: return 7 * 24 * 60 * 60
            case .monthly: return 30 * 24 * 60 * 60
            case .manual: return 0
            }
        }
    }
    
    static let `default` = BackupConfiguration(
        autoBackupEnabled: true,
        backupFrequency: .weekly,
        wifiOnlyBackup: false,  // 修改为允许移动网络备份，提升便利性
        lowPowerModeBackup: false,
        maxBackupVersions: 10,
        compressionEnabled: true
    )
}

// MARK: - 备份管理器

/// 备份管理器 - 备份系统的核心协调器
@MainActor
class BackupManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = BackupManager()
    
    // MARK: - 发布属性
    @Published var isBackupInProgress: Bool = false
    @Published var isRestoreInProgress: Bool = false
    @Published var currentOperation: String = ""
    @Published var operationProgress: Double = 0.0
    @Published var lastBackupDate: Date?
    @Published var lastError: BackupError?
    @Published var configuration: BackupConfiguration = .default
    
    // MARK: - 私有属性
    private let context: NSManagedObjectContext
    private let cloudKitService = CloudKitBackupService.shared
    private let iCloudDocumentsService = iCloudDocumentsBackupService.shared
    private let versionManager = BackupVersionManager.shared
    private let notificationManager = BackupNotificationManager.shared
    private let errorHandler = BackupErrorHandler.shared
    private let userDefaults = UserDefaults.standard

    // 日志记录器
    private let logger = Logger(subsystem: "com.pinklog.backup", category: "BackupManager")

    // 组件实例
    private var dataExporter: BackupDataExporter
    private var dataImporter: BackupDataImporter
    
    // 配置键
    private let configurationKey = "backup_configuration"
    private let lastBackupDateKey = "last_backup_date"
    
    // 取消令牌
    private var cancellationToken: Bool = false
    
    // MARK: - 初始化
    private init() {
        // 获取Core Data上下文
        self.context = PersistenceController.shared.container.viewContext
        
        // 初始化组件
        self.dataExporter = BackupDataExporter(context: context)
        self.dataImporter = BackupDataImporter(context: context)
        
        // 加载配置
        loadConfiguration()
        loadLastBackupDate()
    }
    
    // MARK: - 跨设备备份功能
    
    /// 执行跨设备备份（使用iCloud Documents）
    func performCrossDeviceBackup(description: String = "手动跨设备备份") async -> BackupOperationResult {
        let startTime = Date()
        logger.info("🔄 开始跨设备备份")
        
        guard !isBackupInProgress && !isRestoreInProgress else {
            logger.warning("⚠️ 操作已在进行中")
            return .failure(error: .backupInProgress)
        }
        
        isBackupInProgress = true
        cancellationToken = false
        operationProgress = 0.0
        currentOperation = "准备备份..."
        lastError = nil
        
        defer {
            isBackupInProgress = false
            currentOperation = ""
            operationProgress = 0.0
        }
        
        do {
            // 检查iCloud Documents可用性
            updateOperation("检查iCloud Documents可用性...")
            updateProgress(0.1)
            
            if !iCloudDocumentsService.isAvailable {
                logger.error("❌ iCloud Documents不可用")
                return .failure(error: .iCloudDocumentsUnavailable)
            }
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            // 导出数据
            updateOperation("导出数据...")
            updateProgress(0.3)
            let exportResult = await dataExporter.exportAllData()
            guard exportResult.success, let container = exportResult.container else {
                let errorMessage = exportResult.error?.localizedDescription ?? "未知错误"
                logger.error("❌ 数据导出失败: \(errorMessage)")
                return .failure(error: .dataExportFailed(errorMessage))
            }
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            updateProgress(0.7)
            
            // 保存到iCloud Documents
            updateOperation("保存到iCloud Documents...")
            let documentsMetadata = try await iCloudDocumentsService.saveBackup(
                container,
                description: description,
                type: .full
            )
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            // 更新最后备份时间
            lastBackupDate = Date()
            saveLastBackupDate()
            
            // 发送成功通知
            await notificationManager.sendBackupSuccessNotification(
                entitiesCount: exportResult.exportedEntities,
                backupSize: container.formattedTotalSize,
                duration: Date().timeIntervalSince(startTime)
            )
            
            updateProgress(1.0)
            updateOperation("备份已完成!")
            
            // 保持成功状态显示2秒钟
            try await Task.sleep(nanoseconds: 2_000_000_000)
            
            // 创建结果
            let duration = Date().timeIntervalSince(startTime)
            
            // 创建虚拟版本对象，因为跨设备备份不依赖于CloudKit版本管理
            let virtualVersion = BackupVersion(
                id: documentsMetadata.backupId,
                type: .full,
                status: .completed,
                totalEntities: container.totalEntities,
                totalBinaryAssets: container.totalBinaryAssets,
                estimatedSize: Int64(documentsMetadata.fileSize),
                dataChecksum: container.dataChecksum,
                deviceModel: UIDevice.current.model,
                systemVersion: UIDevice.current.systemVersion,
                appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知",
                deviceId: UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString,
                description: documentsMetadata.description
            )
            
            return .success(
                version: virtualVersion,
                duration: duration,
                entities: exportResult.exportedEntities,
                warnings: exportResult.warnings
            )
            
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            let backupError = convertToBackupError(BackupManagerError.unknownError(error))
            lastError = backupError
            
            // 使用错误处理器处理错误
            await errorHandler.handleError(backupError, context: "跨设备备份")
            
            // 发送失败通知
            await notificationManager.sendBackupFailureNotification(
                error: backupError.localizedDescription,
                retryAvailable: true
            )
            
            return .failure(error: backupError, duration: duration)
        }
    }
    
    /// 从iCloud Documents恢复备份
    func restoreFromiCloudDocuments(_ backupId: UUID) async -> RestoreOperationResult {
        let startTime = Date()
        logger.info("🔄 开始从iCloud Documents恢复，备份ID: \(backupId)")
        
        guard !isBackupInProgress && !isRestoreInProgress else {
            logger.warning("⚠️ 操作已在进行中")
            return .failure(error: .restoreInProgress)
        }
        
        isRestoreInProgress = true
        cancellationToken = false
        operationProgress = 0.0
        lastError = nil
        
        defer {
            isRestoreInProgress = false
            currentOperation = ""
            operationProgress = 0.0
        }
        
        do {
            // 1. 创建恢复前备份
            updateOperation("创建恢复前备份...")
            updateProgress(0.1)
            
            isRestoreInProgress = false
            let preRestoreResult = await performCrossDeviceBackup(description: "恢复前自动备份")
            isRestoreInProgress = true
            
            if !preRestoreResult.success {
                logger.warning("⚠️ 恢复前备份失败，但继续恢复操作")
            }
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            // 2. 从iCloud Documents加载备份数据
            updateOperation("加载备份数据...")
            updateProgress(0.3)
            
            let backupFile = try await iCloudDocumentsService.loadBackup(with: backupId)
            let container = backupFile.backupData
            
            logger.info("✅ 备份数据加载成功: \(container.totalEntities)个实体")
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            updateProgress(0.7)
            
            // 3. 导入数据
            updateOperation("恢复数据...")
            let importResult = await dataImporter.importAllData(from: container)
            guard importResult.success else {
                let errorMessage = importResult.error?.localizedDescription ?? "未知错误"
                logger.error("❌ 数据导入失败: \(errorMessage)")
                return .failure(error: .dataImportFailed(errorMessage))
            }
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            updateProgress(1.0)
            updateOperation("恢复完成")
            
            logger.info("✅ 从iCloud Documents恢复成功: \(importResult.importedEntities)个实体")
            
            let duration = Date().timeIntervalSince(startTime)
            
            // 创建虚拟版本对象用于返回结果
            let virtualVersion = BackupVersion(
                id: backupFile.metadata.backupId,
                type: backupFile.metadata.backupType == .full ? .full : .incremental,
                status: .completed,
                totalEntities: backupFile.metadata.totalEntities,
                totalBinaryAssets: 0,
                estimatedSize: Int64(backupFile.metadata.fileSize),
                dataChecksum: backupFile.metadata.dataChecksum,
                deviceModel: backupFile.metadata.deviceModel,
                systemVersion: backupFile.metadata.systemVersion,
                appVersion: backupFile.metadata.appVersion,
                deviceId: backupFile.metadata.deviceIdentifier,
                cloudKitRecordId: nil,
                cloudKitZoneId: nil,
                backupDuration: duration,
                compressionRatio: nil,
                errorCount: 0,
                warningCount: importResult.warnings.count,
                description: backupFile.metadata.description,
                tags: ["icloud-documents", "cross-device"],
                parentVersionId: nil,
                childVersionIds: []
            )
            
            return .success(
                version: virtualVersion,
                duration: duration,
                entities: importResult.importedEntities,
                warnings: importResult.warnings
            )
            
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            let restoreError = convertToBackupError(BackupManagerError.unknownError(error))
            lastError = restoreError
            
            logger.error("❌ iCloud Documents恢复失败: \(error.localizedDescription)")
            
            return .failure(error: restoreError, duration: duration)
        }
    }
    
    /// 获取所有可用的跨设备备份
    func getAllAvailableBackups() async -> [CrossDeviceBackupMetadata] {
        // 刷新iCloud Documents备份列表
        await iCloudDocumentsService.scanAvailableBackups()
        return iCloudDocumentsService.availableBackups
    }

    // MARK: - 数据恢复

    // MARK: - 跨设备恢复功能
    
    /// 从iCloud Documents恢复备份（跨设备兼容模式）
    func restoreFromiCloudDocumentsWithCrossDeviceMode(_ backupId: UUID) async -> RestoreOperationResult {
        let startTime = Date()
        logger.info("🔄 开始从iCloud Documents恢复（跨设备模式），备份ID: \(backupId)")
        
        guard !isBackupInProgress && !isRestoreInProgress else {
            logger.warning("⚠️ 操作已在进行中")
            return .failure(error: .restoreInProgress)
        }
        
        isRestoreInProgress = true
        cancellationToken = false
        operationProgress = 0.0
        lastError = nil
        
        defer {
            isRestoreInProgress = false
            currentOperation = ""
            operationProgress = 0.0
        }
        
        do {
            // 从iCloud Documents加载备份数据
            updateOperation("加载备份数据...")
            updateProgress(0.3)
            
            let backupFile = try await iCloudDocumentsService.loadBackup(with: backupId)
            let container = backupFile.backupData
            
            logger.info("✅ 备份数据加载成功: \(container.totalEntities)个实体")
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            updateProgress(0.7)
            
            // 导入数据（启用跨设备兼容模式）
            updateOperation("恢复数据（跨设备模式）...")
            let importResult = await dataImporter.importCrossDeviceData(from: container)
            guard importResult.success else {
                let errorMessage = importResult.error?.localizedDescription ?? "未知错误"
                logger.error("❌ 跨设备数据导入失败: \(errorMessage)")
                return .failure(error: .dataImportFailed(errorMessage))
            }
            
            if cancellationToken {
                return .failure(error: .operationCancelled)
            }
            
            updateProgress(1.0)
            updateOperation("跨设备恢复完成")
            
            logger.info("✅ 跨设备恢复成功: \(importResult.importedEntities)个实体, \(importResult.skippedEntities)个跳过")
            
            let duration = Date().timeIntervalSince(startTime)
            
            // 创建虚拟版本对象用于返回结果
            let virtualVersion = BackupVersion(
                id: backupFile.metadata.backupId,
                type: backupFile.metadata.backupType == .full ? .full : .incremental,
                status: .completed,
                totalEntities: backupFile.metadata.totalEntities,
                totalBinaryAssets: 0,
                estimatedSize: Int64(backupFile.metadata.fileSize),
                dataChecksum: backupFile.metadata.dataChecksum,
                deviceModel: backupFile.metadata.deviceModel,
                systemVersion: backupFile.metadata.systemVersion,
                appVersion: backupFile.metadata.appVersion,
                deviceId: backupFile.metadata.deviceIdentifier,
                description: "跨设备恢复: \(backupFile.metadata.description ?? "")"
            )
            
            return .success(
                version: virtualVersion,
                duration: duration,
                entities: importResult.importedEntities,
                warnings: importResult.warnings
            )
            
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            let restoreError = convertToBackupError(BackupManagerError.unknownError(error))
            lastError = restoreError
            return .failure(error: restoreError, duration: duration)
        }
    }

    // MARK: - 取消操作

    /// 取消当前操作
    func cancelCurrentOperation() {
        cancellationToken = true
        dataExporter.cancelExport()
        dataImporter.cancelImport()
    }

    // MARK: - CloudKit操作

    /// 上传备份到CloudKit
    private func uploadBackupToCloudKit(
        container: BackupDataContainer,
        version: BackupVersion
    ) async -> (success: Bool, recordId: String?) {
        logger.info("☁️ 开始上传备份到CloudKit...")
        logger.info("📊 准备上传: \(container.totalEntities)个实体")

        do {
            // 1. 验证CloudKit Schema
            logger.info("🔍 验证CloudKit Schema...")
            let schemaResult = await cloudKitService.validateAndCreateSchema()
            if !schemaResult.success {
                logger.warning("⚠️ Schema验证失败，但继续尝试备份: \(schemaResult.error?.localizedDescription ?? "未知错误")")
            }
            
            // 2. 保存备份元数据
            logger.info("💾 保存备份元数据...")
            let metadataResult = await cloudKitService.saveBackupMetadata(container)
            guard metadataResult.success, let metadataRecord = metadataResult.data else {
                logger.error("❌ 备份元数据保存失败")
                if let error = metadataResult.error {
                    logger.error("❌ 详细错误: \(error.localizedDescription)")
                }
                return (false, nil)
            }
            logger.info("✅ 备份元数据保存成功: \(metadataRecord.recordID.recordName)")

            // 2. 完整保存所有业务数据实体
            logger.info("📋 开始上传完整业务数据...")
            
            var allRecords: [CKRecord] = []
            var uploadedEntitiesCount = 0

            // 保存Products
            logger.info("📦 上传产品数据: \(container.products.count)个")
            for product in container.products {
                let recordResult = await cloudKitService.createCKRecord(from: product)
                if recordResult.success, let record = recordResult.data {
                    allRecords.append(record)
                    uploadedEntitiesCount += 1
                } else {
                    logger.warning("⚠️ 产品记录创建失败: \(product.id)")
                }
            }

            // 保存UsageRecords  
            logger.info("📊 上传使用记录: \(container.usageRecords.count)个")
            for usageRecord in container.usageRecords {
                let recordResult = await cloudKitService.createCKRecord(from: usageRecord)
                if recordResult.success, let record = recordResult.data {
                    allRecords.append(record)
                    uploadedEntitiesCount += 1
                } else {
                    logger.warning("⚠️ 使用记录创建失败: \(usageRecord.id)")
                }
            }

            // 保存Categories
            logger.info("🏷️ 上传分类数据: \(container.categories.count)个")
            for category in container.categories {
                let recordResult = await cloudKitService.createCKRecord(from: category)
                if recordResult.success, let record = recordResult.data {
                    allRecords.append(record)
                    uploadedEntitiesCount += 1
                }
            }

            // 保存Tags
            logger.info("🔖 上传标签数据: \(container.tags.count)个")
            for tag in container.tags {
                let recordResult = await cloudKitService.createCKRecord(from: tag)
                if recordResult.success, let record = recordResult.data {
                    allRecords.append(record)
                    uploadedEntitiesCount += 1
                }
            }

            // 保存RelatedExpenses
            logger.info("💰 上传相关费用: \(container.relatedExpenses.count)个")
            for expense in container.relatedExpenses {
                let recordResult = await cloudKitService.createCKRecord(from: expense)
                if recordResult.success, let record = recordResult.data {
                    allRecords.append(record)
                    uploadedEntitiesCount += 1
                }
            }

            logger.info("📊 准备批量上传: \(allRecords.count)条记录，实体数量: \(uploadedEntitiesCount)")

            // 3. 分批上传所有记录（避免单次请求过大）
            let uploadResult = await cloudKitService.saveLargeRecordBatch(allRecords)
            guard uploadResult.success else {
                logger.error("❌ 业务数据上传失败")
                return (false, nil)
            }

            logger.info("✅ 所有业务数据上传完成: \(uploadedEntitiesCount)个实体")

            return (true, metadataRecord.recordID.recordName)

        } catch {
            return (false, nil)
        }
    }

    /// 从CloudKit下载备份
    private func downloadBackupFromCloudKit(
        version: BackupVersion
    ) async -> (success: Bool, container: BackupDataContainer?) {
        guard let recordId = version.cloudKitRecordId else {
            logger.error("❌ 备份版本缺少CloudKit记录ID")
            return (false, nil)
        }

        logger.info("📥 开始从CloudKit下载备份: \(recordId)")

        do {
            // 1. 获取备份元数据
            logger.info("📋 获取备份元数据...")
            let metadataRecordID = CKRecord.ID(recordName: recordId)
            let metadataResult = await cloudKitService.fetchRecord(with: metadataRecordID)
            
            guard metadataResult.success, let metadataRecord = metadataResult.data else {
                logger.error("❌ 备份元数据获取失败")
                return (false, nil)
            }

            // 2. 如果备份包含CKAsset文件，下载并解析
            // 检查正确的字段名称：backupDataAsset（而不是backupData）
            if let backupAsset = metadataRecord["backupDataAsset"] as? CKAsset,
               let assetURL = backupAsset.fileURL {
                logger.info("📦 发现完整备份文件，开始下载...")

                let data = try Data(contentsOf: assetURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                let container = try decoder.decode(BackupDataContainer.self, from: data)
                logger.info("✅ 完整备份数据下载成功: \(container.totalEntities)个实体")
                return (true, container)
            } else {
                logger.warning("⚠️ 未找到backupDataAsset字段，检查可用字段...")
                for key in metadataRecord.allKeys() {
                    logger.info("📋 可用字段: \(key) = \(type(of: metadataRecord[key]))")
                }
            }

            // 3. 如果没有Asset文件，则逐个查询所有业务数据记录
            logger.info("📊 开始下载分散的业务数据记录...")
            
            // 创建基础的容器结构（这里需要实现完整的从记录重建逻辑）
            logger.warning("⚠️ 分散记录恢复功能需要完整实现")
            return (false, nil) // 临时返回失败，需要完整实现分散记录的重建逻辑

        } catch {
            logger.error("❌ 备份下载失败: \(error.localizedDescription)")
            return (false, nil)
        }
    }

    // MARK: - 配置管理

    /// 更新备份配置
    func updateConfiguration(_ newConfiguration: BackupConfiguration) {
        configuration = newConfiguration
        saveConfiguration()

        // 通知自动备份调度器配置变更
        Task {
            await AutoBackupScheduler.shared.updateConfiguration(newConfiguration)
        }
    }

    /// 加载配置
    private func loadConfiguration() {
        guard let data = userDefaults.data(forKey: configurationKey) else {
            configuration = .default
            return
        }

        do {
            let decoder = JSONDecoder()
            configuration = try decoder.decode(BackupConfiguration.self, from: data)
        } catch {
            configuration = .default
        }
    }

    /// 保存配置
    private func saveConfiguration() {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(configuration)
            userDefaults.set(data, forKey: configurationKey)
        } catch {
            // 保存失败，记录错误
            lastError = .unknownError(error)
        }
    }

    /// 加载最后备份时间
    private func loadLastBackupDate() {
        lastBackupDate = userDefaults.object(forKey: lastBackupDateKey) as? Date
    }

    /// 保存最后备份时间
    private func saveLastBackupDate() {
        if let date = lastBackupDate {
            userDefaults.set(date, forKey: lastBackupDateKey)
        }
    }

    // MARK: - 辅助方法

    /// 更新操作状态
    private func updateOperation(_ operation: String) {
        DispatchQueue.main.async {
            self.currentOperation = operation
        }
    }

    /// 更新进度
    private func updateProgress(_ progress: Double) {
        DispatchQueue.main.async {
            self.operationProgress = progress
        }
    }

    /// 检查是否需要自动备份
    func shouldPerformAutoBackup() -> Bool {
        guard configuration.autoBackupEnabled else { return false }

        guard let lastBackup = lastBackupDate else {
            return true // 从未备份过
        }

        let timeSinceLastBackup = Date().timeIntervalSince(lastBackup)
        return timeSinceLastBackup >= configuration.backupFrequency.interval
    }

    /// 获取备份状态摘要
    func getBackupStatus() -> BackupStatus {
        // 🚀 使用新的独立文件系统获取统计信息
        let iCloudService = iCloudDocumentsBackupService.shared
        let availableBackups = iCloudService.availableBackups
        
        // 计算总备份大小
        let totalBackupSize = availableBackups.reduce(into: 0) { result, backup in
            result += backup.fileSize
        }

        return BackupStatus(
            isConfigured: iCloudService.isAvailable,
            lastBackupDate: lastBackupDate,
            availableVersions: availableBackups.count,
            totalBackupSize: totalBackupSize,
            autoBackupEnabled: configuration.autoBackupEnabled,
            nextScheduledBackup: getNextScheduledBackupDate()
        )
    }

    /// 获取下次计划备份时间
    private func getNextScheduledBackupDate() -> Date? {
        guard configuration.autoBackupEnabled,
              configuration.backupFrequency != .manual,
              let lastBackup = lastBackupDate else {
            return nil
        }

        return lastBackup.addingTimeInterval(configuration.backupFrequency.interval)
    }

    // MARK: - 错误转换

    /// 转换BackupManagerError为BackupError
    private func convertToBackupError(_ managerError: BackupManagerError) -> BackupError {
        switch managerError {
        case .backupInProgress:
            return .backupInProgress
        case .restoreInProgress:
            return .restoreInProgress
        case .cloudKitUnavailable:
            return .cloudKitAccountUnavailable
        case .dataExportFailed(let reason):
            return .dataExportFailed(reason)
        case .dataImportFailed(let reason):
            return .dataImportFailed(reason)
        case .versionCreationFailed(let reason):
            return .dataValidationFailed(reason)
        case .networkUnavailable:
            return .networkUnavailable
        case .insufficientStorage:
            return .insufficientStorage
        case .userCancelled:
            return .operationCancelled
        case .unknownError(let error):
            return .unknownError(error)
        }
    }
}

// MARK: - 备份状态

/// 备份状态信息
struct BackupStatus {
    let isConfigured: Bool
    let lastBackupDate: Date?
    let availableVersions: Int
    let totalBackupSize: Int64
    let autoBackupEnabled: Bool
    let nextScheduledBackup: Date?

    var statusDescription: String {
        if !isConfigured {
            return "备份未配置"
        }

        if let lastBackup = lastBackupDate {
            let formatter = RelativeDateTimeFormatter()
            return "上次备份: \(formatter.localizedString(for: lastBackup, relativeTo: Date()))"
        } else {
            return "从未备份"
        }
    }

    var formattedTotalSize: String {
        return ByteCountFormatter.string(fromByteCount: totalBackupSize, countStyle: .file)
    }
}
