import Foundation
import CloudKit
import Network
import UIKit

// MARK: - 备份错误处理器

/// 备份错误处理器 - 统一处理备份系统中的各种错误
@MainActor
class BackupErrorHandler: ObservableObject {
    
    // MARK: - 单例
    static let shared = BackupErrorHandler()
    
    // MARK: - 发布属性
    @Published var currentError: BackupError?
    @Published var errorHistory: [ErrorRecord] = []
    @Published var retryCount: Int = 0
    @Published var isRetrying: Bool = false
    
    // MARK: - 私有属性
    private let maxRetryCount = 3
    private let baseRetryDelay: TimeInterval = 2.0
    private let maxRetryDelay: TimeInterval = 60.0
    private let networkMonitor: NetworkMonitor = NetworkMonitor.shared
    private let userDefaults = UserDefaults.standard
    
    // 重试定时器
    private var retryTimer: Timer?
    
    // UserDefaults键
    private let errorHistoryKey = "backup_error_history"
    private let retryCountKey = "backup_retry_count"
    
    // MARK: - 初始化
    private init() {
        loadErrorHistory()
        loadRetryCount()
    }
    
    // MARK: - 公共方法
    
    /// 处理错误
    func handleError(_ error: Error, context: String = "") {
        let backupError = convertToBackupError(error)
        currentError = backupError
        
        // 记录错误
        let errorRecord = ErrorRecord(
            error: backupError,
            context: context,
            timestamp: Date(),
            deviceInfo: getDeviceInfo()
        )
        
        errorHistory.append(errorRecord)
        saveErrorHistory()
        
        // 记录日志
        logError(errorRecord)
        
        // 决定是否自动重试
        if shouldAutoRetry(backupError) {
            scheduleRetry(for: backupError)
        } else {
            retryCount = 0
            saveRetryCount()
        }
    }
    
    /// 清除当前错误
    func clearCurrentError() {
        currentError = nil
        retryCount = 0
        saveRetryCount()
        cancelRetry()
    }
    
    /// 手动重试
    func manualRetry() async -> Bool {
        guard let error = currentError else { return false }
        
        // 检查重试条件
        guard canRetry(error) else { return false }
        
        isRetrying = true
        
        // 等待适当的延迟
        let delay = calculateRetryDelay()
        try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
        
        // 检查重试前置条件
        let preConditionResult = await checkRetryPreConditions(for: error)
        
        isRetrying = false
        
        if preConditionResult.canRetry {
            retryCount += 1
            saveRetryCount()
            return true
        } else {
            // 更新错误信息
            if let newError = preConditionResult.blockingError {
                handleError(newError, context: "重试前置条件检查")
            }
            return false
        }
    }
    
    /// 获取错误统计
    func getErrorStatistics() -> ErrorStatistics {
        let last24Hours = Date().addingTimeInterval(-24 * 60 * 60)
        let recentErrors = errorHistory.filter { $0.timestamp >= last24Hours }
        
        var errorCounts: [String: Int] = [:]
        var severityCounts: [ErrorSeverity: Int] = [:]
        
        for record in recentErrors {
            let errorType = String(describing: record.error)
            errorCounts[errorType, default: 0] += 1
            severityCounts[record.error.severity, default: 0] += 1
        }
        
        return ErrorStatistics(
            totalErrors: errorHistory.count,
            recentErrors: recentErrors.count,
            errorCounts: errorCounts,
            severityCounts: severityCounts,
            lastErrorTime: errorHistory.last?.timestamp
        )
    }
    
    /// 清除错误历史
    func clearErrorHistory() {
        errorHistory.removeAll()
        saveErrorHistory()
    }
    
    /// 导出错误日志
    func exportErrorLogs() -> String {
        var logContent = "PinkLog 备份错误日志\n"
        logContent += "导出时间: \(Date().formatted())\n"
        logContent += "设备信息: \(getDeviceInfo())\n\n"
        
        for (index, record) in errorHistory.enumerated() {
            logContent += "错误 #\(index + 1)\n"
            logContent += "时间: \(record.timestamp.formatted())\n"
            logContent += "类型: \(record.error)\n"
            logContent += "描述: \(record.error.errorDescription ?? "无描述")\n"
            logContent += "上下文: \(record.context)\n"
            logContent += "严重程度: \(record.error.severity.displayName)\n"
            logContent += "设备信息: \(record.deviceInfo)\n"
            logContent += "---\n\n"
        }
        
        return logContent
    }
    
    // MARK: - 私有方法
    
    /// 转换为备份错误
    private func convertToBackupError(_ error: Error) -> BackupError {
        // 如果已经是BackupError，直接返回
        if let backupError = error as? BackupError {
            return backupError
        }
        
        // 如果是CloudKit错误，转换为相应的备份错误
        if let ckError = error as? CKError {
            return convertCloudKitError(ckError)
        }
        
        // 如果是网络错误，转换为相应的备份错误
        if let urlError = error as? URLError {
            return convertURLError(urlError)
        }
        
        // 其他错误转换为未知错误
        return .unknownError(error)
    }
    
    /// 转换CloudKit错误
    private func convertCloudKitError(_ error: CKError) -> BackupError {
        switch error.code {
        case .notAuthenticated:
            return .cloudKitAccountUnavailable
        case .quotaExceeded:
            return .cloudKitQuotaExceeded
        case .permissionFailure:
            return .cloudKitPermissionDenied
        case .networkUnavailable, .networkFailure:
            return .networkUnavailable
        case .serviceUnavailable, .internalError:
            return .cloudKitServerError(error.code.rawValue)
        case .serverRecordChanged:
            return .cloudKitSyncConflict
        case .unknownItem:
            return .cloudKitRecordNotFound
        case .zoneNotFound:
            return .cloudKitZoneNotFound
        case .userDeletedZone:
            return .cloudKitUserDeletedZone
        case .assetFileNotFound:
            return .cloudKitAssetUploadFailed("资源文件未找到")
        case .assetFileModified:
            return .cloudKitAssetUploadFailed("资源文件已被修改")
        default:
            return .cloudKitServerError(error.code.rawValue)
        }
    }
    
    /// 转换URL错误
    private func convertURLError(_ error: URLError) -> BackupError {
        switch error.code {
        case .notConnectedToInternet:
            return .networkUnavailable
        case .timedOut:
            return .networkTimeout
        case .cannotConnectToHost, .cannotFindHost:
            return .networkConnectionFailed(error.localizedDescription)
        case .dataNotAllowed:
            return .wifiRequired
        default:
            return .networkConnectionFailed(error.localizedDescription)
        }
    }
    
    /// 判断是否应该自动重试
    private func shouldAutoRetry(_ error: BackupError) -> Bool {
        return error.isRetryable && retryCount < maxRetryCount
    }
    
    /// 判断是否可以重试
    private func canRetry(_ error: BackupError) -> Bool {
        return error.isRetryable && retryCount < maxRetryCount
    }
    
    /// 计算重试延迟（指数退避）
    private func calculateRetryDelay() -> TimeInterval {
        let delay = baseRetryDelay * pow(2.0, Double(retryCount))
        return min(delay, maxRetryDelay)
    }
    
    /// 调度重试
    private func scheduleRetry(for error: BackupError) {
        cancelRetry()
        
        let delay = calculateRetryDelay()
        
        retryTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            Task {
                await self?.performAutoRetry()
            }
        }
    }
    
    /// 取消重试
    private func cancelRetry() {
        retryTimer?.invalidate()
        retryTimer = nil
    }
    
    /// 执行自动重试
    private func performAutoRetry() async {
        guard let error = currentError else { return }
        
        let success = await manualRetry()
        
        if !success && retryCount >= maxRetryCount {
            // 达到最大重试次数，停止重试
            retryCount = 0
            saveRetryCount()
        }
    }
    
    /// 检查重试前置条件
    private func checkRetryPreConditions(for error: BackupError) async -> (canRetry: Bool, blockingError: BackupError?) {
        // 检查网络连接
        if !networkMonitor.isConnected {
            return (false, .networkUnavailable)
        }
        
        // 检查特定错误的前置条件
        switch error {
        case .wifiRequired:
            if networkMonitor.connectionType != .wifi {
                return (false, .wifiRequired)
            }
        case .lowBattery:
            if ProcessInfo.processInfo.isLowPowerModeEnabled {
                return (false, .lowBattery)
            }
        case .deviceStorageFull:
            // 检查存储空间
            if getAvailableStorage() < 100 * 1024 * 1024 { // 100MB
                return (false, .deviceStorageFull)
            }
        case .cloudKitAccountUnavailable:
            // 检查CloudKit账户状态
            if !CloudKitBackupService.shared.isAvailable {
                return (false, .cloudKitAccountUnavailable)
            }
        default:
            break
        }
        
        return (true, nil)
    }
    
    /// 获取可用存储空间
    private func getAvailableStorage() -> Int64 {
        do {
            let fileURL = URL(fileURLWithPath: NSHomeDirectory())
            let values = try fileURL.resourceValues(forKeys: [.volumeAvailableCapacityForImportantUsageKey])
            return values.volumeAvailableCapacityForImportantUsage ?? 0
        } catch {
            return 0
        }
    }
    
    /// 获取设备信息
    private func getDeviceInfo() -> String {
        let device = UIDevice.current
        return "\(device.model) (\(device.systemName) \(device.systemVersion))"
    }
    
    /// 记录错误日志
    private func logError(_ record: ErrorRecord) {
        print("🔴 备份错误: \(record.error)")
        print("   时间: \(record.timestamp)")
        print("   上下文: \(record.context)")
        print("   严重程度: \(record.error.severity.displayName)")
        print("   可重试: \(record.error.isRetryable)")
    }
    
    /// 加载错误历史
    private func loadErrorHistory() {
        // 由于ErrorRecord包含复杂类型，这里简化处理
        // 实际实现中可以使用Codable进行序列化
        errorHistory = []
    }
    
    /// 保存错误历史
    private func saveErrorHistory() {
        // 保持最近100条错误记录
        if errorHistory.count > 100 {
            errorHistory = Array(errorHistory.suffix(100))
        }
        
        // 实际实现中可以使用Codable进行序列化保存
    }
    
    /// 加载重试次数
    private func loadRetryCount() {
        retryCount = userDefaults.integer(forKey: retryCountKey)
    }
    
    /// 保存重试次数
    private func saveRetryCount() {
        userDefaults.set(retryCount, forKey: retryCountKey)
    }
    
    deinit {
        Task { @MainActor in
            cancelRetry()
        }
    }
}

// MARK: - 错误记录

struct ErrorRecord: Identifiable {
    let id = UUID()
    let error: BackupError
    let context: String
    let timestamp: Date
    let deviceInfo: String
}

// MARK: - 错误统计

struct ErrorStatistics {
    let totalErrors: Int
    let recentErrors: Int
    let errorCounts: [String: Int]
    let severityCounts: [ErrorSeverity: Int]
    let lastErrorTime: Date?
}
