import Foundation
import Combine
import CloudKit

// MARK: - 版本管理错误

/// 版本管理错误类型
enum VersionManagerError: Error, LocalizedError {
    case versionNotFound(UUID)
    case storageError(String)
    case invalidVersionData(String)
    case dependencyViolation(String)
    case quotaExceeded(Int)
    
    var errorDescription: String? {
        switch self {
        case .versionNotFound(let id):
            return "版本未找到: \(id)"
        case .storageError(let reason):
            return "存储错误: \(reason)"
        case .invalidVersionData(let reason):
            return "版本数据无效: \(reason)"
        case .dependencyViolation(let reason):
            return "依赖关系违反: \(reason)"
        case .quotaExceeded(let limit):
            return "版本数量超出限制: \(limit)"
        }
    }
}

// MARK: - 备份版本管理器

/// 备份版本管理器 - 管理备份版本、历史记录和版本选择恢复功能
@MainActor
class BackupVersionManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = BackupVersionManager()
    
    // MARK: - 发布属性
    @Published var versions: [BackupVersion] = []
    @Published var isLoading: Bool = false
    @Published var lastError: VersionManagerError?
    
    // MARK: - 私有属性
    private let userDefaults = UserDefaults.standard
    private let versionsKey = "backup_versions"
    private let maxVersionsLimit = 100 // 最大版本数量限制
    private let maxVersionAge: TimeInterval = 365 * 24 * 60 * 60 // 1年
    
    // MARK: - 初始化
    private init() {
        loadVersions()
    }
    
    // MARK: - 版本管理
    
    /// 创建新版本
    func createVersion(
        type: BackupVersionType,
        description: String? = nil,
        tags: [String] = [],
        parentVersionId: UUID? = nil
    ) -> BackupVersion {
        let version = BackupVersion(
            type: type,
            description: description,
            tags: tags,
            parentVersionId: parentVersionId
        )
        
        versions.insert(version, at: 0)
        saveVersions()
        
        return version
    }
    
    /// 更新版本信息
    func updateVersion(_ updatedVersion: BackupVersion) throws {
        guard let index = versions.firstIndex(where: { $0.id == updatedVersion.id }) else {
            throw VersionManagerError.versionNotFound(updatedVersion.id)
        }
        
        versions[index] = updatedVersion
        saveVersions()
    }
    
    /// 删除版本
    func deleteVersion(_ versionId: UUID) throws {
        guard let index = versions.firstIndex(where: { $0.id == versionId }) else {
            throw VersionManagerError.versionNotFound(versionId)
        }
        
        let version = versions[index]
        
        // 检查是否有子版本依赖
        let dependentVersions = versions.filter { $0.parentVersionId == versionId }
        if !dependentVersions.isEmpty {
            let dependentIds = dependentVersions.map { $0.id.uuidString }.joined(separator: ", ")
            throw VersionManagerError.dependencyViolation("版本被以下版本依赖: \(dependentIds)")
        }
        
        // 标记为已删除而不是直接删除
        let deletedVersion = BackupVersion(
            id: version.id,
            type: version.type,
            status: .deleted,
            totalEntities: version.totalEntities,
            totalBinaryAssets: version.totalBinaryAssets,
            estimatedSize: version.estimatedSize,
            dataChecksum: version.dataChecksum,
            deviceModel: version.deviceModel,
            systemVersion: version.systemVersion,
            appVersion: version.appVersion,
            deviceId: version.deviceId,
            cloudKitRecordId: version.cloudKitRecordId,
            cloudKitZoneId: version.cloudKitZoneId,
            backupDuration: version.backupDuration,
            compressionRatio: version.compressionRatio,
            errorCount: version.errorCount,
            warningCount: version.warningCount,
            description: version.description,
            tags: version.tags,
            parentVersionId: version.parentVersionId,
            childVersionIds: version.childVersionIds
        )
        
        versions[index] = deletedVersion
        saveVersions()
    }
    
    /// 物理删除版本（永久删除）
    func permanentlyDeleteVersion(_ versionId: UUID) throws {
        guard let index = versions.firstIndex(where: { $0.id == versionId }) else {
            throw VersionManagerError.versionNotFound(versionId)
        }

        let version = versions[index]

        // 如果有CloudKit记录ID，尝试删除CloudKit记录
        if let cloudKitRecordId = version.cloudKitRecordId {
            Task {
                // 异步删除CloudKit记录，不阻塞UI
                await deleteCloudKitRecord(recordId: cloudKitRecordId)
            }
        }

        versions.remove(at: index)
        saveVersions()

        print("✅ 版本已从本地删除: \(versionId)")
    }

    /// 删除CloudKit记录
    private func deleteCloudKitRecord(recordId: String) async {
        print("🗑️ 开始删除CloudKit记录: \(recordId)")

        // 获取CloudKit服务实例
        let cloudKitService = CloudKitBackupService.shared

        // 创建记录ID
        let ckRecordId = CKRecord.ID(recordName: recordId)

        // 执行删除操作
        let result = await cloudKitService.deleteRecord(with: ckRecordId)

        if result.success {
            print("✅ CloudKit记录删除成功: \(recordId)")
        } else {
            print("❌ CloudKit记录删除失败: \(result.error?.localizedDescription ?? "未知错误")")
            // 删除失败不影响本地删除操作，只记录错误
        }
    }
    
    // MARK: - 版本查询
    
    /// 获取版本
    func getVersion(_ versionId: UUID) -> BackupVersion? {
        return versions.first { $0.id == versionId }
    }
    
    /// 查询版本
    func queryVersions(_ query: BackupVersionQuery) -> [BackupVersion] {
        var filteredVersions = versions

        // 首先过滤掉已删除的版本
        filteredVersions = filteredVersions.filter { $0.status != .deleted }

        // 按状态过滤
        if let status = query.status {
            filteredVersions = filteredVersions.filter { $0.status == status }
        }
        
        // 按类型过滤
        if let type = query.type {
            filteredVersions = filteredVersions.filter { $0.type == type }
        }
        
        // 按设备ID过滤
        if let deviceId = query.deviceId {
            filteredVersions = filteredVersions.filter { $0.deviceId == deviceId }
        }
        
        // 按日期范围过滤
        if let dateRange = query.dateRange {
            filteredVersions = filteredVersions.filter { dateRange.contains($0.createdAt) }
        }
        
        // 排序
        switch query.sortBy {
        case .createdAtDescending:
            filteredVersions.sort { $0.createdAt > $1.createdAt }
        case .createdAtAscending:
            filteredVersions.sort { $0.createdAt < $1.createdAt }
        case .sizeDescending:
            filteredVersions.sort { $0.estimatedSize > $1.estimatedSize }
        case .sizeAscending:
            filteredVersions.sort { $0.estimatedSize < $1.estimatedSize }
        case .typeAndDate:
            filteredVersions.sort { first, second in
                if first.type != second.type {
                    return first.type.rawValue < second.type.rawValue
                }
                return first.createdAt > second.createdAt
            }
        }
        
        // 限制数量
        if let limit = query.limit {
            filteredVersions = Array(filteredVersions.prefix(limit))
        }
        
        return filteredVersions
    }
    
    /// 获取可用版本（可恢复的版本）
    func getAvailableVersions() -> [BackupVersion] {
        return versions.filter { $0.canRestore }
    }
    
    /// 获取最新的完整备份版本
    func getLatestFullBackup() -> BackupVersion? {
        return versions
            .filter { $0.type == .full && $0.status == .completed }
            .sorted { $0.createdAt > $1.createdAt }
            .first
    }
    
    /// 获取指定版本的增量备份链
    func getIncrementalChain(for versionId: UUID) -> [BackupVersion] {
        guard let version = getVersion(versionId) else { return [] }
        
        var chain: [BackupVersion] = [version]
        
        // 向上查找父版本
        var currentVersion = version
        while let parentId = currentVersion.parentVersionId,
              let parentVersion = getVersion(parentId) {
            chain.insert(parentVersion, at: 0)
            currentVersion = parentVersion
        }
        
        return chain
    }
    
    // MARK: - 版本统计
    
    /// 获取版本统计信息
    func getStatistics() -> BackupVersionStatistics {
        let completedVersions = versions.filter { $0.status == .completed }
        let failedVersions = versions.filter { $0.status == .failed }
        
        let totalSize = completedVersions.reduce(0) { $0 + $1.estimatedSize }
        let averageSize = completedVersions.isEmpty ? 0 : totalSize / Int64(completedVersions.count)
        
        let totalBackupTime = completedVersions.compactMap { $0.backupDuration }.reduce(0, +)
        let averageBackupTime = completedVersions.isEmpty ? 0 : totalBackupTime / Double(completedVersions.count)
        
        let sortedDates = versions.map { $0.createdAt }.sorted()
        
        return BackupVersionStatistics(
            totalVersions: versions.count,
            completedVersions: completedVersions.count,
            failedVersions: failedVersions.count,
            totalSize: totalSize,
            averageSize: averageSize,
            oldestVersion: sortedDates.first,
            newestVersion: sortedDates.last,
            totalBackupTime: totalBackupTime,
            averageBackupTime: averageBackupTime
        )
    }
    
    // MARK: - 版本清理
    
    /// 清理过期版本
    func cleanupExpiredVersions() {
        let cutoffDate = Date().addingTimeInterval(-maxVersionAge)
        let expiredVersions = versions.filter { $0.createdAt < cutoffDate && $0.status != .completed }
        
        for version in expiredVersions {
            try? permanentlyDeleteVersion(version.id)
        }
    }
    
    /// 清理超出限制的版本
    func cleanupExcessVersions() {
        guard versions.count > maxVersionsLimit else { return }
        
        // 保留最新的版本，删除最旧的非完成状态版本
        let sortedVersions = versions.sorted { $0.createdAt > $1.createdAt }
        let versionsToDelete = sortedVersions.dropFirst(maxVersionsLimit)
        
        for version in versionsToDelete {
            if version.status != .completed {
                try? permanentlyDeleteVersion(version.id)
            }
        }
    }
    
    /// 执行完整清理
    func performCleanup() {
        cleanupExpiredVersions()
        cleanupExcessVersions()
    }

    // MARK: - 数据持久化

    /// 加载版本数据
    private func loadVersions() {
        isLoading = true

        defer {
            isLoading = false
        }

        guard let data = userDefaults.data(forKey: versionsKey) else {
            versions = []
            return
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            versions = try decoder.decode([BackupVersion].self, from: data)
        } catch {
            lastError = .storageError("加载版本数据失败: \(error.localizedDescription)")
            versions = []
        }
    }

    /// 保存版本数据
    private func saveVersions() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
            let data = try encoder.encode(versions)
            userDefaults.set(data, forKey: versionsKey)
        } catch {
            lastError = .storageError("保存版本数据失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 版本验证

    /// 验证版本数据完整性
    func validateVersions() -> [String] {
        var issues: [String] = []

        // 检查重复ID
        let ids = versions.map { $0.id }
        let uniqueIds = Set(ids)
        if ids.count != uniqueIds.count {
            issues.append("发现重复的版本ID")
        }

        // 检查父子关系
        for version in versions {
            if let parentId = version.parentVersionId {
                if !versions.contains(where: { $0.id == parentId }) {
                    issues.append("版本 \(version.id) 的父版本 \(parentId) 不存在")
                }
            }

            for childId in version.childVersionIds {
                if !versions.contains(where: { $0.id == childId }) {
                    issues.append("版本 \(version.id) 的子版本 \(childId) 不存在")
                }
            }
        }

        // 检查增量备份链
        let incrementalVersions = versions.filter { $0.type == .incremental }
        for version in incrementalVersions {
            if version.parentVersionId == nil {
                issues.append("增量备份版本 \(version.id) 缺少父版本")
            }
        }

        return issues
    }

    // MARK: - 导入导出

    /// 导出版本数据
    func exportVersions() throws -> Data {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        return try encoder.encode(versions)
    }

    /// 导入版本数据
    func importVersions(from data: Data, mergeStrategy: MergeStrategy = .skipExisting) throws {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let importedVersions = try decoder.decode([BackupVersion].self, from: data)

        switch mergeStrategy {
        case .skipExisting:
            let existingIds = Set(versions.map { $0.id })
            let newVersions = importedVersions.filter { !existingIds.contains($0.id) }
            versions.append(contentsOf: newVersions)

        case .overwriteExisting:
            let importedIds = Set(importedVersions.map { $0.id })
            versions.removeAll { importedIds.contains($0.id) }
            versions.append(contentsOf: importedVersions)

        case .replaceAll:
            versions = importedVersions
        }

        saveVersions()
    }

    /// 合并策略
    enum MergeStrategy {
        case skipExisting      // 跳过已存在的版本
        case overwriteExisting // 覆盖已存在的版本
        case replaceAll        // 替换所有版本
    }
}

// MARK: - 版本管理器扩展

extension BackupVersionManager {

    /// 获取版本树结构
    func getVersionTree() -> [VersionTreeNode] {
        let rootVersions = versions.filter { $0.parentVersionId == nil }
        return rootVersions.map { buildVersionTree(for: $0) }
    }

    /// 构建版本树节点
    private func buildVersionTree(for version: BackupVersion) -> VersionTreeNode {
        let children = versions
            .filter { $0.parentVersionId == version.id }
            .map { buildVersionTree(for: $0) }

        return VersionTreeNode(version: version, children: children)
    }

    /// 获取版本路径（从根到指定版本）
    func getVersionPath(to versionId: UUID) -> [BackupVersion]? {
        guard let targetVersion = getVersion(versionId) else { return nil }

        var path: [BackupVersion] = []
        var currentVersion = targetVersion

        // 向上追溯到根版本
        while true {
            path.insert(currentVersion, at: 0)

            guard let parentId = currentVersion.parentVersionId,
                  let parentVersion = getVersion(parentId) else {
                break
            }

            currentVersion = parentVersion
        }

        return path
    }

    /// 检查版本是否可以安全删除
    func canSafelyDelete(_ versionId: UUID) -> (canDelete: Bool, reason: String?) {
        guard let version = getVersion(versionId) else {
            return (false, "版本不存在")
        }

        // 检查是否有子版本依赖
        let dependentVersions = versions.filter { $0.parentVersionId == versionId }
        if !dependentVersions.isEmpty {
            return (false, "存在 \(dependentVersions.count) 个依赖版本")
        }

        // 检查是否是最新的完整备份
        if version.type == .full,
           let latestFull = getLatestFullBackup(),
           latestFull.id == versionId {
            let incrementalCount = versions.filter { $0.parentVersionId == versionId }.count
            if incrementalCount > 0 {
                return (false, "这是最新的完整备份，有 \(incrementalCount) 个增量备份依赖它")
            }
        }

        return (true, nil)
    }
}

// MARK: - 版本树节点

/// 版本树节点
struct VersionTreeNode {
    let version: BackupVersion
    let children: [VersionTreeNode]

    var hasChildren: Bool {
        return !children.isEmpty
    }

    var depth: Int {
        return children.isEmpty ? 0 : children.map { $0.depth }.max()! + 1
    }
}
