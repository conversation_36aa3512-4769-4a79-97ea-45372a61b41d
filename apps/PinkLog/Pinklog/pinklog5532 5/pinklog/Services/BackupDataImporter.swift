import Foundation
import CoreData
import Combine

// MARK: - 导入错误类型

/// 数据导入错误类型
enum DataImportError: Error, LocalizedError {
    case coreDataContextUnavailable
    case invalidBackupData(String)
    case entityCreationFailed(String)
    case relationshipResolutionFailed(String)
    case dataValidationFailed(String)
    case duplicateDataDetected(String)
    case binaryDataRestoreFailed(String)
    case importCancelled
    case crossDeviceCompatibilityIssue(String)
    case partialImportCompleted(String)
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .coreDataContextUnavailable:
            return "Core Data上下文不可用"
        case .invalidBackupData(let reason):
            return "备份数据无效: \(reason)"
        case .entityCreationFailed(let entity):
            return "创建实体失败: \(entity)"
        case .relationshipResolutionFailed(let reason):
            return "关系解析失败: \(reason)"
        case .dataValidationFailed(let reason):
            return "数据验证失败: \(reason)"
        case .duplicateDataDetected(let reason):
            return "检测到重复数据: \(reason)"
        case .binaryDataRestoreFailed(let reason):
            return "Binary数据恢复失败: \(reason)"
        case .importCancelled:
            return "导入操作已取消"
        case .crossDeviceCompatibilityIssue(let reason):
            return "跨设备兼容性问题: \(reason)。建议使用跨设备备份导入模式"
        case .partialImportCompleted(let reason):
            return "部分导入完成: \(reason)。某些数据可能由于兼容性问题被跳过"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
    
    /// 是否为跨设备相关错误
    var isCrossDeviceIssue: Bool {
        switch self {
        case .crossDeviceCompatibilityIssue, .partialImportCompleted:
            return true
        case .invalidBackupData(let reason):
            return reason.contains("版本") || reason.contains("设备") || reason.contains("兼容")
        default:
            return false
        }
    }
    
    /// 获取用户友好的解决建议
    var userFriendlyMessage: String {
        switch self {
        case .crossDeviceCompatibilityIssue:
            return "这个备份来自不同的设备或应用版本。请尝试使用\"跨设备恢复\"选项。"
        case .partialImportCompleted:
            return "备份恢复部分完成。某些数据由于兼容性原因被跳过，但核心数据已成功导入。"
        case .invalidBackupData(let reason) where reason.contains("validation errors occurred"):
            return "备份数据包含多个验证错误。这通常是由于跨设备兼容性问题引起的，请尝试跨设备恢复模式。"
        default:
            return errorDescription ?? "发生未知错误"
        }
    }
}

// MARK: - 导入结果

/// 数据导入结果
struct DataImportResult {
    let success: Bool
    let error: DataImportError?
    let importedEntities: Int
    let skippedEntities: Int
    let processingTime: TimeInterval
    let warnings: [String]
    
    static func success(imported: Int, skipped: Int = 0, time: TimeInterval, warnings: [String] = []) -> DataImportResult {
        return DataImportResult(success: true, error: nil, importedEntities: imported, skippedEntities: skipped, processingTime: time, warnings: warnings)
    }
    
    static func failure(_ error: DataImportError, time: TimeInterval = 0) -> DataImportResult {
        return DataImportResult(success: false, error: error, importedEntities: 0, skippedEntities: 0, processingTime: time, warnings: [])
    }
}

// MARK: - 导入进度

/// 导入进度信息
struct ImportProgress {
    let currentStep: String
    let completedEntities: Int
    let totalEntities: Int
    let progress: Double
    let estimatedTimeRemaining: TimeInterval?
    
    var progressDescription: String {
        return "\(currentStep) (\(completedEntities)/\(totalEntities))"
    }
}

// MARK: - 冲突解决策略

/// 数据冲突解决策略
enum ConflictResolutionStrategy {
    case skipExisting        // 跳过已存在的数据
    case overwriteExisting   // 覆盖已存在的数据
    case mergeData          // 合并数据（保留最新）
    case askUser            // 询问用户（暂不实现）
}

// MARK: - 数据导入引擎

/// 数据导入引擎 - 负责从备份数据恢复到Core Data
@MainActor
class BackupDataImporter: ObservableObject {
    
    // MARK: - 发布属性
    @Published var isImporting: Bool = false
    @Published var currentProgress: ImportProgress?
    @Published var lastError: DataImportError?
    
    // MARK: - 私有属性
    private let context: NSManagedObjectContext
    private var cancellationToken: Bool = false
    private let conflictStrategy: ConflictResolutionStrategy
    
    // Repository实例
    private lazy var productRepository = ProductRepository(context: context)
    private lazy var usageRecordRepository = UsageRecordRepository(context: context)
    private lazy var relatedExpenseRepository = RelatedExpenseRepository(context: context)
    private lazy var categoryRepository = CategoryRepository(context: context)
    private lazy var tagRepository = TagRepository(context: context)
    private lazy var loanRecordRepository = LoanRecordRepository(context: context)
    private lazy var expenseTypeRepository = ExpenseTypeRepository(context: context)
    private lazy var purchaseChannelRepository = PurchaseChannelRepository(context: context)
    private lazy var reminderRepository = ReminderRepository(context: context)
    private lazy var productLinkRepository = ProductLinkRepository(context: context)
    private lazy var conversationRepository = ConversationRepository(context: context)
    private lazy var messageRepository = MessageRepository(context: context)
    
    // 实体ID映射表（用于关系重建）
    private var entityIdMappings: [String: [UUID: NSManagedObject]] = [:]
    
    // MARK: - 初始化
    init(context: NSManagedObjectContext, conflictStrategy: ConflictResolutionStrategy = .skipExisting) {
        self.context = context
        self.conflictStrategy = conflictStrategy
    }
    
    // MARK: - 公共方法
    
    /// 导入所有数据
    func importAllData(from container: BackupDataContainer) async -> DataImportResult {
        return await importAllData(from: container, enableCrossDeviceMode: true)
    }
    
    /// 导入所有数据（支持跨设备模式）
    func importAllData(from container: BackupDataContainer, enableCrossDeviceMode: Bool) async -> DataImportResult {
        let startTime = Date()
        
        guard !isImporting else {
            return .failure(.importCancelled)
        }
        
        isImporting = true
        cancellationToken = false
        lastError = nil
        entityIdMappings.removeAll()
        
        defer {
            isImporting = false
            currentProgress = nil
        }
        
        do {
            // 验证备份数据（根据模式选择验证策略）
            updateProgress("正在验证备份数据...", 0, container.totalEntities)
            let validationResult = container.validateBackup(allowCrossDeviceValidation: enableCrossDeviceMode)
            if !validationResult.isValid {
                let errorMessage = validationResult.errors.joined(separator: "; ")
                return .failure(.invalidBackupData(errorMessage))
            }
            
            var importedCount = 0
            var skippedCount = 0
            var warnings: [String] = []
            warnings.append(contentsOf: validationResult.warnings)
            
            // 按依赖顺序导入实体
            // 1. 基础实体（无依赖）
            updateProgress("正在导入分类数据...", importedCount, container.totalEntities)
            let (categoryImported, categorySkipped) = try await importCategoriesWithErrorHandling(container.categories, enableCrossDeviceMode: enableCrossDeviceMode)
            importedCount += categoryImported
            skippedCount += categorySkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入标签数据...", importedCount, container.totalEntities)
            let (tagImported, tagSkipped) = try await importTags(container.tags)
            importedCount += tagImported
            skippedCount += tagSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入费用类型...", importedCount, container.totalEntities)
            let (expenseTypeImported, expenseTypeSkipped) = try await importExpenseTypes(container.expenseTypes)
            importedCount += expenseTypeImported
            skippedCount += expenseTypeSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入购买渠道分类...", importedCount, container.totalEntities)
            let (channelCategoryImported, channelCategorySkipped) = try await importPurchaseChannelCategories(container.purchaseChannelCategories)
            importedCount += channelCategoryImported
            skippedCount += channelCategorySkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入购买渠道...", importedCount, container.totalEntities)
            let (channelImported, channelSkipped) = try await importPurchaseChannels(container.purchaseChannels)
            importedCount += channelImported
            skippedCount += channelSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            // 2. 产品实体（依赖分类、标签、购买渠道）
            updateProgress("正在导入产品数据...", importedCount, container.totalEntities)
            let (productImported, productSkipped) = try await importProducts(container.products)
            importedCount += productImported
            skippedCount += productSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            // 3. 依赖产品的实体
            updateProgress("正在导入使用记录...", importedCount, container.totalEntities)
            let (usageImported, usageSkipped) = try await importUsageRecords(container.usageRecords)
            importedCount += usageImported
            skippedCount += usageSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入费用记录...", importedCount, container.totalEntities)
            let (expenseImported, expenseSkipped) = try await importRelatedExpenses(container.relatedExpenses)
            importedCount += expenseImported
            skippedCount += expenseSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入借阅记录...", importedCount, container.totalEntities)
            let (loanImported, loanSkipped) = try await importLoanRecords(container.loanRecords)
            importedCount += loanImported
            skippedCount += loanSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入提醒数据...", importedCount, container.totalEntities)
            let (reminderImported, reminderSkipped) = try await importReminders(container.reminders)
            importedCount += reminderImported
            skippedCount += reminderSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入产品关联...", importedCount, container.totalEntities)
            let (linkImported, linkSkipped) = try await importProductLinks(container.productLinks)
            importedCount += linkImported
            skippedCount += linkSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            // 4. 对话和消息
            updateProgress("正在导入对话数据...", importedCount, container.totalEntities)
            let (conversationImported, conversationSkipped) = try await importConversations(container.conversations)
            importedCount += conversationImported
            skippedCount += conversationSkipped
            
            if cancellationToken { return .failure(.importCancelled) }
            
            updateProgress("正在导入消息数据...", importedCount, container.totalEntities)
            let (messageImported, messageSkipped) = try await importMessages(container.messages)
            importedCount += messageImported
            skippedCount += messageSkipped
            
            // 保存上下文
            updateProgress("正在保存数据...", importedCount, container.totalEntities)
            try await saveContext()
            
            let processingTime = Date().timeIntervalSince(startTime)
            return .success(imported: importedCount, skipped: skippedCount, time: processingTime, warnings: warnings)
            
        } catch let error as DataImportError {
            let processingTime = Date().timeIntervalSince(startTime)
            lastError = error
            return .failure(error, time: processingTime)
        } catch {
            let processingTime = Date().timeIntervalSince(startTime)
            let importError = DataImportError.unknownError(error)
            lastError = importError
            return .failure(importError, time: processingTime)
        }
    }
    
    /// 增量导入数据
    func importIncrementalData(from container: BackupDataContainer) async -> DataImportResult {
        // 增量导入与完整导入的逻辑基本相同
        // 区别在于冲突解决策略可能不同
        return await importAllData(from: container)
    }
    
    /// 跨设备导入数据（专用方法）
    func importCrossDeviceData(from container: BackupDataContainer) async -> DataImportResult {
        // 使用更宽松的验证和更强的容错机制
        return await importAllData(from: container, enableCrossDeviceMode: true)
    }
    
    /// 取消导入操作
    func cancelImport() {
        cancellationToken = true
    }

    // MARK: - 私有导入方法
    
    /// 导入分类数据（带错误处理）
    private func importCategoriesWithErrorHandling(_ backupCategories: [BackupCategory], enableCrossDeviceMode: Bool) async throws -> (imported: Int, skipped: Int) {
        if enableCrossDeviceMode {
            return try await importCategoriesRobust(backupCategories)
        } else {
            return try await importCategories(backupCategories)
        }
    }
    
    /// 导入分类数据（鲁棒模式）
    private func importCategoriesRobust(_ backupCategories: [BackupCategory]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0
                var errors: [String] = []

                for backupCategory in backupCategories {
                    do {
                        // 数据验证
                        if !backupCategory.validateData() {
                            skipped += 1
                            errors.append("分类数据验证失败: \(backupCategory.id)")
                            continue
                        }
                        
                        if let existingCategory = self.findExistingEntity(Category.self, id: backupCategory.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["Category", default: [:]][backupCategory.id] = existingCategory
                                continue
                            }
                        }

                        let category = Category(context: self.context)
                        category.id = backupCategory.id
                        category.name = backupCategory.name
                        category.icon = backupCategory.icon

                        self.entityIdMappings["Category", default: [:]][backupCategory.id] = category
                        imported += 1
                    } catch {
                        skipped += 1
                        errors.append("分类创建失败: \(backupCategory.id) - \(error.localizedDescription)")
                    }
                }
                
                // 在鲁棒模式下，即使有部分失败也继续
                print("分类导入完成: 成功 \(imported), 跳过 \(skipped), 错误: \(errors)")
                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入分类数据
    private func importCategories(_ backupCategories: [BackupCategory]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupCategory in backupCategories {
                    do {
                        if let existingCategory = self.findExistingEntity(Category.self, id: backupCategory.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["Category", default: [:]][backupCategory.id] = existingCategory
                                continue
                            }
                        }

                        let category = Category(context: self.context)
                        category.id = backupCategory.id
                        category.name = backupCategory.name
                        category.icon = backupCategory.icon

                        // 暂时不处理父分类关系，在第二轮处理

                        self.entityIdMappings["Category", default: [:]][backupCategory.id] = category
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("Category: \(backupCategory.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入标签数据
    private func importTags(_ backupTags: [BackupTag]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupTag in backupTags {
                    do {
                        if let existingTag = self.findExistingEntity(Tag.self, id: backupTag.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["Tag", default: [:]][backupTag.id] = existingTag
                                continue
                            }
                        }

                        let tag = Tag(context: self.context)
                        tag.id = backupTag.id
                        tag.name = backupTag.name
                        tag.color = backupTag.color
                        tag.type = backupTag.type

                        self.entityIdMappings["Tag", default: [:]][backupTag.id] = tag
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("Tag: \(backupTag.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入费用类型数据
    private func importExpenseTypes(_ backupTypes: [BackupExpenseType]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupType in backupTypes {
                    do {
                        if let existingType = self.findExistingEntity(ExpenseType.self, id: backupType.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["ExpenseType", default: [:]][backupType.id] = existingType
                                continue
                            }
                        }

                        let expenseType = ExpenseType(context: self.context)
                        expenseType.id = backupType.id
                        expenseType.name = backupType.name
                        expenseType.icon = backupType.icon

                        self.entityIdMappings["ExpenseType", default: [:]][backupType.id] = expenseType
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("ExpenseType: \(backupType.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入购买渠道分类数据
    private func importPurchaseChannelCategories(_ backupCategories: [BackupPurchaseChannelCategory]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupCategory in backupCategories {
                    do {
                        if let existingCategory = self.findExistingEntity(PurchaseChannelCategory.self, id: backupCategory.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["PurchaseChannelCategory", default: [:]][backupCategory.id] = existingCategory
                                continue
                            }
                        }

                        let category = PurchaseChannelCategory(context: self.context)
                        category.id = backupCategory.id
                        category.name = backupCategory.name
                        category.icon = backupCategory.icon

                        self.entityIdMappings["PurchaseChannelCategory", default: [:]][backupCategory.id] = category
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("PurchaseChannelCategory: \(backupCategory.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入购买渠道数据
    private func importPurchaseChannels(_ backupChannels: [BackupPurchaseChannel]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                // 确保至少有一个默认的PurchaseChannelCategory存在
                let defaultCategory = self.ensureDefaultPurchaseChannelCategory()
                
                for backupChannel in backupChannels {
                    do {
                        if let existingChannel = self.findExistingEntity(PurchaseChannel.self, id: backupChannel.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["PurchaseChannel", default: [:]][backupChannel.id] = existingChannel
                                continue
                            }
                        }

                        let channel = PurchaseChannel(context: self.context)
                        channel.id = backupChannel.id
                        channel.name = backupChannel.name
                        
                        // 处理必需的category关系
                        if let categoryId = backupChannel.categoryId,
                           let existingCategory = self.entityIdMappings["PurchaseChannelCategory"]?[categoryId] as? PurchaseChannelCategory {
                            // 使用映射中的分类（刚导入的或已存在的）
                            channel.category = existingCategory
                        } else if let categoryId = backupChannel.categoryId,
                                  let existingCategory = self.findExistingEntity(PurchaseChannelCategory.self, id: categoryId) {
                            // 使用备份中指定的已存在分类
                            channel.category = existingCategory
                        } else if let appropriateCategory = self.getAppropriateCategory(for: backupChannel.name) {
                            // 根据渠道名称智能选择合适的分类
                            channel.category = appropriateCategory
                        } else {
                            // 如果没有找到对应的分类，使用默认分类
                            channel.category = defaultCategory
                        }
                        
                        // 设置其他属性（如果BackupPurchaseChannel包含的话）
                        channel.usageCount = 0 // 默认使用次数
                        
                        self.entityIdMappings["PurchaseChannel", default: [:]][backupChannel.id] = channel
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("PurchaseChannel: \(backupChannel.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }
    
    /// 确保存在默认的购买渠道分类
    private func ensureDefaultPurchaseChannelCategory() -> PurchaseChannelCategory {
        // 先尝试查找现有的默认分类
        let fetchRequest = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
        fetchRequest.predicate = NSPredicate(format: "name == %@", "其他")
        fetchRequest.fetchLimit = 1
        
        do {
            let existingCategories = try context.fetch(fetchRequest)
            if let existingCategory = existingCategories.first {
                return existingCategory
            }
        } catch {
            print("查找默认购买渠道分类失败: \(error)")
        }
        
        // 如果没有找到，创建默认分类
        let defaultCategory = PurchaseChannelCategory(context: context)
        defaultCategory.id = UUID()
        defaultCategory.name = "其他"
        defaultCategory.icon = "ellipsis.circle"
        
        // 同时创建常用的线上和线下分类
        createCommonPurchaseChannelCategories()
        
        return defaultCategory
    }
    
    /// 创建常用的购买渠道分类
    private func createCommonPurchaseChannelCategories() {
        let commonCategories = [
            ("线上", "network"),
            ("线下", "building.2")
        ]
        
        for (name, icon) in commonCategories {
            let fetchRequest = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
            fetchRequest.predicate = NSPredicate(format: "name == %@", name)
            fetchRequest.fetchLimit = 1
            
            do {
                let existingCategories = try context.fetch(fetchRequest)
                if existingCategories.isEmpty {
                    let category = PurchaseChannelCategory(context: context)
                    category.id = UUID()
                    category.name = name
                    category.icon = icon
                }
            } catch {
                print("创建购买渠道分类失败: \(error)")
            }
        }
    }
    
    /// 根据渠道名称智能选择合适的分类
    private func getAppropriateCategory(for channelName: String) -> PurchaseChannelCategory? {
        let onlineChannels = ["淘宝", "京东", "拼多多", "天猫", "亚马逊", "苹果商店", "微信小程序"]
        let offlineChannels = ["实体店", "超市", "商场", "专卖店"]
        
        let categoryName: String
        if onlineChannels.contains(channelName) {
            categoryName = "线上"
        } else if offlineChannels.contains(channelName) {
            categoryName = "线下"
        } else {
            categoryName = "其他"
        }
        
        let fetchRequest = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
        fetchRequest.predicate = NSPredicate(format: "name == %@", categoryName)
        fetchRequest.fetchLimit = 1
        
        do {
            let categories = try context.fetch(fetchRequest)
            return categories.first
        } catch {
            print("查找购买渠道分类失败: \(error)")
            return nil
        }
    }

    /// 导入使用记录数据
    private func importUsageRecords(_ backupRecords: [BackupUsageRecord]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupRecord in backupRecords {
                    if self.cancellationToken {
                        continuation.resume(returning: (imported, skipped))
                        return
                    }

                    // 检查是否已存在
                    let fetchRequest = UsageRecord.fetchRequest()
                    fetchRequest.predicate = NSPredicate(format: "id == %@", backupRecord.id as CVarArg)

                    do {
                        let existingRecords = try self.context.fetch(fetchRequest)

                        if let existingRecord = existingRecords.first {
                            // 更新现有记录
                            self.updateUsageRecord(existingRecord, from: backupRecord)
                            imported += 1
                        } else {
                            // 创建新记录
                            let newRecord = UsageRecord(context: self.context)
                            self.updateUsageRecord(newRecord, from: backupRecord)
                            imported += 1
                        }
                    } catch {
                        skipped += 1
                        print("导入使用记录失败: \(error)")
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入费用记录数据
    private func importRelatedExpenses(_ backupExpenses: [BackupRelatedExpense]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupExpense in backupExpenses {
                    if self.cancellationToken {
                        continuation.resume(returning: (imported, skipped))
                        return
                    }

                    // 检查是否已存在
                    let fetchRequest = RelatedExpense.fetchRequest()
                    fetchRequest.predicate = NSPredicate(format: "id == %@", backupExpense.id as CVarArg)

                    do {
                        let existingExpenses = try self.context.fetch(fetchRequest)

                        if let existingExpense = existingExpenses.first {
                            // 更新现有记录
                            self.updateRelatedExpense(existingExpense, from: backupExpense)
                            imported += 1
                        } else {
                            // 创建新记录
                            let newExpense = RelatedExpense(context: self.context)
                            self.updateRelatedExpense(newExpense, from: backupExpense)
                            imported += 1
                        }
                    } catch {
                        skipped += 1
                        print("导入费用记录失败: \(error)")
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入借阅记录数据
    private func importLoanRecords(_ backupRecords: [BackupLoanRecord]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupRecord in backupRecords {
                    if self.cancellationToken {
                        continuation.resume(returning: (imported, skipped))
                        return
                    }

                    // 检查是否已存在
                    let fetchRequest = LoanRecord.fetchRequest()
                    fetchRequest.predicate = NSPredicate(format: "id == %@", backupRecord.id as CVarArg)

                    do {
                        let existingRecords = try self.context.fetch(fetchRequest)

                        if let existingRecord = existingRecords.first {
                            // 更新现有记录
                            self.updateLoanRecord(existingRecord, from: backupRecord)
                            imported += 1
                        } else {
                            // 创建新记录
                            let newRecord = LoanRecord(context: self.context)
                            self.updateLoanRecord(newRecord, from: backupRecord)
                            imported += 1
                        }
                    } catch {
                        skipped += 1
                        print("导入借阅记录失败: \(error)")
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入提醒数据
    private func importReminders(_ backupReminders: [BackupReminder]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupReminder in backupReminders {
                    if self.cancellationToken {
                        continuation.resume(returning: (imported, skipped))
                        return
                    }

                    // 检查是否已存在
                    let fetchRequest = Reminder.fetchRequest()
                    fetchRequest.predicate = NSPredicate(format: "id == %@", backupReminder.id as CVarArg)

                    do {
                        let existingReminders = try self.context.fetch(fetchRequest)

                        if let existingReminder = existingReminders.first {
                            // 更新现有记录
                            self.updateReminder(existingReminder, from: backupReminder)
                            imported += 1
                        } else {
                            // 创建新记录
                            let newReminder = Reminder(context: self.context)
                            self.updateReminder(newReminder, from: backupReminder)
                            imported += 1
                        }
                    } catch {
                        skipped += 1
                        print("导入提醒失败: \(error)")
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入产品关联数据
    private func importProductLinks(_ backupLinks: [BackupProductLink]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupLink in backupLinks {
                    if self.cancellationToken {
                        continuation.resume(returning: (imported, skipped))
                        return
                    }

                    // 检查是否已存在
                    let fetchRequest = ProductLink.fetchRequest()
                    fetchRequest.predicate = NSPredicate(format: "id == %@", backupLink.id as CVarArg)

                    do {
                        let existingLinks = try self.context.fetch(fetchRequest)

                        if let existingLink = existingLinks.first {
                            // 更新现有记录
                            self.updateProductLink(existingLink, from: backupLink)
                            imported += 1
                        } else {
                            // 创建新记录
                            let newLink = ProductLink(context: self.context)
                            self.updateProductLink(newLink, from: backupLink)
                            imported += 1
                        }
                    } catch {
                        skipped += 1
                        print("导入产品关联失败: \(error)")
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入产品数据
    private func importProducts(_ backupProducts: [BackupProduct]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupProduct in backupProducts {
                    do {
                        if let existingProduct = self.findExistingEntity(Product.self, id: backupProduct.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["Product", default: [:]][backupProduct.id] = existingProduct
                                continue
                            }
                        }

                        let product = Product(context: self.context)
                        product.id = backupProduct.id
                        product.name = backupProduct.name
                        product.brand = backupProduct.brand
                        product.model = backupProduct.model
                        product.price = backupProduct.price
                        product.quantity = Int16(backupProduct.quantity)
                        product.purchaseDate = backupProduct.purchaseDate
                        product.purchaseMotivation = backupProduct.purchaseMotivation
                        product.purchaseNotes = backupProduct.purchaseNotes
                        product.isVirtualProduct = backupProduct.isVirtualProduct
                        product.expectedLifespan = Int16(backupProduct.expectedLifespan)
                        product.expectedUsageFrequency = backupProduct.expectedUsageFrequency
                        product.valuationMethod = backupProduct.valuationMethod
                        product.warrantyEndDate = backupProduct.warrantyEndDate
                        product.warrantyDetails = backupProduct.warrantyDetails
                        product.warrantyImage = backupProduct.warrantyImage
                        product.expiryDate = backupProduct.expiryDate
                        product.initialSatisfaction = Int16(backupProduct.initialSatisfaction)

                        // 恢复消耗型物品相关字段
                        product.isConsumable = backupProduct.isConsumable
                        if backupProduct.isConsumable {
                            product.currentQuantity = backupProduct.currentQuantity ?? 0.0
                            product.unitType = backupProduct.unitType
                            product.minStockAlert = backupProduct.minStockAlert ?? 0.0
                            product.consumptionRate = backupProduct.consumptionRate ?? 0.0
                            product.consumptionUnitType = backupProduct.consumptionUnitType
                            product.unitConversionRatio = backupProduct.unitConversionRatio ?? 1.0
                        }

                        // 处理关系
                        if let categoryId = backupProduct.categoryId,
                           let category = self.entityIdMappings["Category"]?[categoryId] as? Category {
                            product.category = category
                        }

                        if let channelId = backupProduct.purchaseChannelId,
                           let channel = self.entityIdMappings["PurchaseChannel"]?[channelId] as? PurchaseChannel {
                            product.purchaseChannelRelation = channel
                        }

                        // 处理Binary数据
                        if let imagesContainer = backupProduct.images {
                            product.images = try self.restoreBinaryData(from: imagesContainer)
                        }

                        self.entityIdMappings["Product", default: [:]][backupProduct.id] = product
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("Product: \(backupProduct.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入对话数据
    private func importConversations(_ backupConversations: [BackupConversation]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupConversation in backupConversations {
                    do {
                        if let existingConversation = self.findExistingEntity(Conversation.self, id: backupConversation.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                self.entityIdMappings["Conversation", default: [:]][backupConversation.id] = existingConversation
                                continue
                            }
                        }

                        let conversation = Conversation(context: self.context)
                        conversation.id = backupConversation.id
                        conversation.title = backupConversation.title
                        conversation.startDate = backupConversation.startDate
                        conversation.lastMessageDate = backupConversation.lastMessageDate

                        self.entityIdMappings["Conversation", default: [:]][backupConversation.id] = conversation
                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("Conversation: \(backupConversation.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    /// 导入消息数据
    private func importMessages(_ backupMessages: [BackupMessage]) async throws -> (imported: Int, skipped: Int) {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                var imported = 0
                var skipped = 0

                for backupMessage in backupMessages {
                    do {
                        if let existingMessage = self.findExistingEntity(Message.self, id: backupMessage.id) {
                            if self.conflictStrategy == .skipExisting {
                                skipped += 1
                                continue
                            }
                        }

                        let message = Message(context: self.context)
                        message.id = backupMessage.id
                        message.text = backupMessage.text
                        message.timestamp = backupMessage.timestamp
                        message.isFromUser = backupMessage.isFromUser
                        message.role = backupMessage.role

                        // 关联对话
                        if let conversation = self.entityIdMappings["Conversation"]?[backupMessage.conversationId] as? Conversation {
                            message.conversation = conversation
                        }

                        // 处理Binary数据
                        if let analysisContainer = backupMessage.analysisContextData {
                            message.analysisContextData = try self.restoreBinaryData(from: analysisContainer)
                        }

                        if let attachmentsContainer = backupMessage.attachmentsData {
                            message.attachmentsData = try self.restoreBinaryData(from: attachmentsContainer)
                        }

                        imported += 1
                    } catch {
                        continuation.resume(throwing: DataImportError.entityCreationFailed("Message: \(backupMessage.id)"))
                        return
                    }
                }

                continuation.resume(returning: (imported, skipped))
            }
        }
    }

    // MARK: - 辅助方法

    /// 查找已存在的实体
    private func findExistingEntity<T: NSManagedObject>(_ entityType: T.Type, id: UUID) -> T? {
        let request = NSFetchRequest<T>(entityName: String(describing: entityType))
        request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        request.fetchLimit = 1

        do {
            let results = try context.fetch(request)
            return results.first
        } catch {
            return nil
        }
    }

    /// 恢复Binary数据
    private func restoreBinaryData(from container: BinaryDataContainer) throws -> Data {
        guard let dataString = container.data else {
            throw DataImportError.binaryDataRestoreFailed("数据为空")
        }

        switch container.strategy {
        case .base64:
            guard let data = Data(base64Encoded: dataString) else {
                throw DataImportError.binaryDataRestoreFailed("Base64解码失败")
            }
            return data
        case .fileReference:
            let fileURL = URL(fileURLWithPath: dataString)
            guard FileManager.default.fileExists(atPath: fileURL.path) else {
                throw DataImportError.binaryDataRestoreFailed("文件不存在: \(dataString)")
            }
            return try Data(contentsOf: fileURL)
        case .excluded:
            throw DataImportError.binaryDataRestoreFailed("数据被排除")
        }
    }

    /// 保存Core Data上下文
    private func saveContext() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            context.perform {
                do {
                    if self.context.hasChanges {
                        try self.context.save()
                    }
                    continuation.resume()
                } catch {
                    let nsError = error as NSError
                    // Broader range for validation errors, typically 1550-1600
                    if nsError.domain == "NSCocoaErrorDomain" && nsError.code >= 1550 && nsError.code <= 1600 {
                        var detailedErrorMessages = "Core Data Validation Errors:"
                        if let detailedErrors = nsError.userInfo[NSDetailedErrorsKey] as? [NSError] {
                            for subError in detailedErrors {
                                let objectName = (subError.userInfo["NSValidationErrorObject"] as? NSManagedObject)?.entity.name ?? "Unknown Entity"
                                let propertyName = subError.userInfo["NSValidationErrorKey"] as? String ?? "Unknown Property"
                                let errorMessage = subError.localizedDescription
                                detailedErrorMessages += "\n- Entity: \(objectName), Property: \(propertyName), Error: \(errorMessage)"
                            }
                        } else {
                            // If detailed errors are not available, dump the whole user info dictionary.
                            detailedErrorMessages += " \(nsError.localizedDescription). UserInfo: \(nsError.userInfo.description)"
                        }
                        continuation.resume(throwing: DataImportError.dataValidationFailed(detailedErrorMessages))
                    } else {
                        continuation.resume(throwing: DataImportError.unknownError(error))
                    }
                }
            }
        }
    }

    /// 更新进度
    private func updateProgress(_ step: String, _ completed: Int, _ total: Int) {
        let progress = total > 0 ? Double(completed) / Double(total) : 0.0
        let progressInfo = ImportProgress(
            currentStep: step,
            completedEntities: completed,
            totalEntities: total,
            progress: progress,
            estimatedTimeRemaining: nil
        )

        DispatchQueue.main.async {
            self.currentProgress = progressInfo
        }
    }

    /// 更新使用记录
    private func updateUsageRecord(_ record: UsageRecord, from backup: BackupUsageRecord) {
        record.id = backup.id
        record.date = backup.date
        record.notes = backup.notes
        record.usageType = backup.usageType
        record.satisfaction = Int16(backup.satisfaction)
        record.emotionalValue = Int16(backup.emotionalValue)
        record.duration = backup.duration
        record.scenario = backup.scenario
        record.title = backup.title
        record.memories = backup.memories
        record.isLoanedOut = backup.isLoanedOut
        record.borrowerName = backup.borrowerName
        record.contactInfo = backup.contactInfo
        record.dueDate = backup.dueDate
        record.returnDate = backup.returnDate
        record.recipient = backup.recipient
        record.transferPrice = backup.transferPrice
        record.transferTypeString = backup.transferType
        record.wearCondition = backup.wearCondition
        record.isStory = backup.isStory

        // 恢复消耗量相关字段
        record.consumedQuantity = backup.consumedQuantity ?? 0.0
        record.remainingQuantity = backup.remainingQuantity ?? 0.0
        record.consumptionUnit = backup.consumptionUnit

        // 恢复图片数据
        if let imagesContainer = backup.images {
            do {
                record.images = try restoreBinaryData(from: imagesContainer)
                print("✅ 恢复图片数据成功: \(record.images?.count ?? 0) 字节")
            } catch {
                print("❌ 恢复图片数据失败: \(error)")
            }
        } else {
            print("ℹ️ 备份中没有图片数据")
        }

        // 恢复录音数据
        if let audioContainer = backup.audioRecordings {
            do {
                let restoredData = try restoreBinaryData(from: audioContainer)

                // 检查是否是新的音频数据包格式
                if audioContainer.fileName?.contains("audio_package") == true {
                    print("📦 恢复音频数据包格式")
                    try restoreAudioPackage(restoredData, to: record)
                } else {
                    // 旧格式或文件名数组格式
                    record.audioRecordings = restoredData
                    print("✅ 恢复录音数据成功（旧格式）: \(restoredData.count) 字节")
                }
            } catch {
                print("❌ 恢复录音数据失败: \(error)")
            }
        } else {
            print("ℹ️ 备份中没有录音数据")
        }

        // 设置产品关联
        let productRequest = Product.fetchRequest()
        productRequest.predicate = NSPredicate(format: "id == %@", backup.productId as CVarArg)
        if let product = try? context.fetch(productRequest).first {
            record.product = product
        }
    }

    /// 更新费用记录
    private func updateRelatedExpense(_ expense: RelatedExpense, from backup: BackupRelatedExpense) {
        expense.id = backup.id
        expense.amount = backup.amount
        expense.date = backup.date
        expense.notes = backup.notes

        // 设置产品关联
        let productRequest = Product.fetchRequest()
        productRequest.predicate = NSPredicate(format: "id == %@", backup.productId as CVarArg)
        if let product = try? context.fetch(productRequest).first {
            expense.product = product
        }

        // 设置费用类型关联
        let typeRequest = ExpenseType.fetchRequest()
        typeRequest.predicate = NSPredicate(format: "id == %@", backup.expenseTypeId as CVarArg)
        if let expenseType = try? context.fetch(typeRequest).first {
            expense.type = expenseType
        }
    }

    /// 更新借阅记录
    private func updateLoanRecord(_ record: LoanRecord, from backup: BackupLoanRecord) {
        record.id = backup.id
        record.borrowerName = backup.borrowerName
        record.contactInfo = backup.contactInfo
        record.createdAt = backup.createdAt
        record.dueDate = backup.dueDate
        record.returnDate = backup.returnDate
        record.status = backup.status
        record.notes = backup.notes
        record.isLoanedOut = backup.isLoanedOut

        // 设置产品关联
        let productRequest = Product.fetchRequest()
        productRequest.predicate = NSPredicate(format: "id == %@", backup.productId as CVarArg)
        if let product = try? context.fetch(productRequest).first {
            record.product = product
        }
    }

    /// 更新提醒
    private func updateReminder(_ reminder: Reminder, from backup: BackupReminder) {
        reminder.id = backup.id
        reminder.message = backup.message
        reminder.date = backup.date
        reminder.isActive = backup.isActive
        reminder.reminderType = backup.reminderType

        // 设置产品关联（提醒可以关联多个产品）
        var products: Set<Product> = []
        for productId in backup.productIds {
            let productRequest = Product.fetchRequest()
            productRequest.predicate = NSPredicate(format: "id == %@", productId as CVarArg)
            if let product = try? context.fetch(productRequest).first {
                products.insert(product)
            }
        }
        reminder.product = NSSet(set: products)
    }

    /// 更新产品关联
    private func updateProductLink(_ link: ProductLink, from backup: BackupProductLink) {
        link.id = backup.id
        link.relationshipType = backup.relationshipType
        link.notes = backup.notes
        link.createdAt = backup.createdAt
        link.strength = Int16(backup.strength)
        link.isBidirectional = backup.isBidirectional
        link.usageCount = Int32(backup.usageCount)
        link.lastUsedTogether = backup.lastUsedTogether
        link.usageScenario = backup.usageScenario
        link.groupID = backup.groupID

        // 设置源产品关联
        let sourceRequest = Product.fetchRequest()
        sourceRequest.predicate = NSPredicate(format: "id == %@", backup.sourceProductId as CVarArg)
        if let sourceProduct = try? context.fetch(sourceRequest).first {
            link.sourceProduct = sourceProduct
        }

        // 设置目标产品关联
        let targetRequest = Product.fetchRequest()
        targetRequest.predicate = NSPredicate(format: "id == %@", backup.targetProductId as CVarArg)
        if let targetProduct = try? context.fetch(targetRequest).first {
            link.targetProduct = targetProduct
        }
    }

    /// 恢复音频数据包并重建音频文件
    private func restoreAudioPackage(_ packageData: Data, to record: UsageRecord) throws {
        do {
            // 解析音频数据包
            guard let audioPackage = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(packageData) as? [String: Any],
                  let fileInfos = audioPackage["fileInfos"] as? [[String: Any]],
                  let allAudioData = audioPackage["audioData"] as? Data else {
                throw DataImportError.invalidBackupData("无法解析音频数据包")
            }

            print("📦 解析音频数据包: \(fileInfos.count) 个文件, 总数据: \(allAudioData.count) 字节")

            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            var restoredFileNames: [String] = []

            // 恢复每个音频文件
            for fileInfo in fileInfos {
                guard let fileName = fileInfo["fileName"] as? String,
                      let startOffset = fileInfo["startOffset"] as? Int,
                      let size = fileInfo["size"] as? Int else {
                    print("⚠️ 跳过无效的文件信息")
                    continue
                }

                // 提取文件数据
                let endOffset = startOffset + size
                guard endOffset <= allAudioData.count else {
                    print("⚠️ 文件数据超出范围: \(fileName)")
                    continue
                }

                let fileData = allAudioData.subdata(in: startOffset..<endOffset)
                let audioURL = documentsDirectory.appendingPathComponent(fileName)

                // 写入文件
                try fileData.write(to: audioURL)
                restoredFileNames.append(fileName)
                print("📁 恢复音频文件: \(fileName), 大小: \(fileData.count) 字节")
            }

            // 更新记录的音频数据为文件名数组
            if !restoredFileNames.isEmpty {
                let fileNamesData = try NSKeyedArchiver.archivedData(withRootObject: restoredFileNames, requiringSecureCoding: false)
                record.audioRecordings = fileNamesData
                print("✅ 音频数据包恢复完成: \(restoredFileNames.count) 个文件")
            } else {
                print("⚠️ 没有成功恢复任何音频文件")
            }

        } catch {
            print("❌ 恢复音频数据包失败: \(error)")
            throw error
        }
    }
}
