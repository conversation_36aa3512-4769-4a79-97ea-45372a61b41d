//
//  ConsumableAutoArchiveService.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import Foundation
import CoreData
import Combine
import SwiftUI

/// 消耗品智能自动归档服务
@MainActor
class ConsumableAutoArchiveService: ObservableObject {
    
    // MARK: - 配置参数
    
    /// 自动归档配置
    struct AutoArchiveConfig {
        let unusedDaysThreshold: Int        // 未使用天数阈值
        let depletedDaysThreshold: Int      // 耗尽后天数阈值
        let expiredDaysThreshold: Int       // 过期后天数阈值
        let enableAutoArchive: Bool         // 是否启用自动归档
        let enableSmartSuggestions: Bool    // 是否启用智能建议
        
        static let `default` = AutoArchiveConfig(
            unusedDaysThreshold: 90,        // 90天未使用
            depletedDaysThreshold: 30,      // 耗尽30天后
            expiredDaysThreshold: 7,        // 过期7天后
            enableAutoArchive: true,
            enableSmartSuggestions: true
        )
    }
    
    // MARK: - 属性
    
    @Published var config = AutoArchiveConfig.default
    @Published var pendingArchiveProducts: [Product] = []
    @Published var archivedProducts: [Product] = []
    @Published var archiveSuggestions: [ArchiveSuggestion] = []
    
    private let repository: ProductRepository
    private let reminderService: ConsumableReminderService
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext,
         repository: ProductRepository = ProductRepository.shared,
         reminderService: ConsumableReminderService = ConsumableReminderService.shared) {
        self.context = context
        self.repository = repository
        self.reminderService = reminderService
        
        setupPeriodicCheck()
        loadConfiguration()
    }
    
    // MARK: - 配置管理
    
    private func loadConfiguration() {
        // 从UserDefaults加载配置
        let defaults = UserDefaults.standard
        
        config = AutoArchiveConfig(
            unusedDaysThreshold: defaults.object(forKey: "autoArchive.unusedDaysThreshold") as? Int ?? 90,
            depletedDaysThreshold: defaults.object(forKey: "autoArchive.depletedDaysThreshold") as? Int ?? 30,
            expiredDaysThreshold: defaults.object(forKey: "autoArchive.expiredDaysThreshold") as? Int ?? 7,
            enableAutoArchive: defaults.object(forKey: "autoArchive.enableAutoArchive") as? Bool ?? true,
            enableSmartSuggestions: defaults.object(forKey: "autoArchive.enableSmartSuggestions") as? Bool ?? true
        )
    }
    
    func updateConfiguration(_ newConfig: AutoArchiveConfig) {
        config = newConfig
        
        // 保存到UserDefaults
        let defaults = UserDefaults.standard
        defaults.set(newConfig.unusedDaysThreshold, forKey: "autoArchive.unusedDaysThreshold")
        defaults.set(newConfig.depletedDaysThreshold, forKey: "autoArchive.depletedDaysThreshold")
        defaults.set(newConfig.expiredDaysThreshold, forKey: "autoArchive.expiredDaysThreshold")
        defaults.set(newConfig.enableAutoArchive, forKey: "autoArchive.enableAutoArchive")
        defaults.set(newConfig.enableSmartSuggestions, forKey: "autoArchive.enableSmartSuggestions")
    }
    
    // MARK: - 定期检查
    
    private func setupPeriodicCheck() {
        // 每天检查一次
        Timer.publish(every: 24 * 60 * 60, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.performAutoArchiveCheck()
                }
            }
            .store(in: &cancellables)
        
        // 应用启动时检查一次
        Task {
            await performAutoArchiveCheck()
        }
    }
    
    // MARK: - 自动归档检查
    
    /// 执行自动归档检查
    func performAutoArchiveCheck() async {
        guard config.enableAutoArchive else { return }
        
        let allProducts = repository.fetchAllProducts()
        let consumableProducts = allProducts.filter { $0.isConsumable == true }
        
        var candidatesForArchive: [Product] = []
        var newSuggestions: [ArchiveSuggestion] = []
        
        for product in consumableProducts {
            // 跳过已归档的产品
            if product.lifecycleStatus == .archived {
                continue
            }
            
            let suggestion = evaluateProductForArchive(product)
            if let suggestion = suggestion {
                newSuggestions.append(suggestion)
                
                // 如果是自动归档建议，添加到候选列表
                if suggestion.shouldAutoArchive {
                    candidatesForArchive.append(product)
                }
            }
        }
        
        // 更新建议列表
        archiveSuggestions = newSuggestions.sorted { $0.priority.rawValue < $1.priority.rawValue }
        pendingArchiveProducts = candidatesForArchive
        
        // 执行自动归档
        if !candidatesForArchive.isEmpty {
            await executeAutoArchive(candidatesForArchive)
        }
        
        // 生成归档提醒
        if config.enableSmartSuggestions && !newSuggestions.isEmpty {
            await generateArchiveReminders(newSuggestions)
        }
    }
    
    // MARK: - 产品评估
    
    private func evaluateProductForArchive(_ product: Product) -> ArchiveSuggestion? {
        // 检查产品是否被忽略或延迟
        if isProductIgnoredOrPostponed(product) {
            return nil
        }
        
        let currentStatus = product.lifecycleStatus
        let daysSincePurchase = product.daysSincePurchase
        
        // 新建物品保护期：购买后7天内不参与自动归档评估
        if daysSincePurchase < 7 {
            return nil
        }
        
        // 评估不同的归档条件
        
        // 1. 长期未使用 - 只对有使用记录的产品进行评估
        if currentStatus == .active {
            // 检查是否有使用记录
            guard let daysSinceLastUsage = product.daysSinceLastUsage else {
                // 没有使用记录的产品不参与长期未使用评估
                return nil
            }
            
            if daysSinceLastUsage >= config.unusedDaysThreshold {
                return ArchiveSuggestion(
                    product: product,
                    reason: .longTermUnused(days: daysSinceLastUsage),
                    priority: .medium,
                    shouldAutoArchive: true,
                    confidence: calculateConfidence(for: product, reason: .longTermUnused(days: daysSinceLastUsage))
                )
            }
        }
        
        // 2. 已耗尽且长时间未补充
        if currentStatus == .depleted {
            let daysSinceDepletion = calculateDaysSinceDepletion(product)
            if daysSinceDepletion >= config.depletedDaysThreshold {
                return ArchiveSuggestion(
                    product: product,
                    reason: .depletedLongTime(days: daysSinceDepletion),
                    priority: .high,
                    shouldAutoArchive: true,
                    confidence: 0.9
                )
            }
        }
        
        // 3. 已过期且超过处理期限
        if currentStatus == .expired {
            let daysSinceExpiry = calculateDaysSinceExpiry(product)
            if daysSinceExpiry >= config.expiredDaysThreshold {
                return ArchiveSuggestion(
                    product: product,
                    reason: .expiredLongTime(days: daysSinceExpiry),
                    priority: .high,
                    shouldAutoArchive: false, // 过期物品需要手动确认
                    confidence: 0.95
                )
            }
        }
        
        // 4. 智能模式：基于使用模式的预测
        if config.enableSmartSuggestions {
            if let smartSuggestion = evaluateSmartArchiveConditions(product) {
                return smartSuggestion
            }
        }
        
        return nil
    }
    
    // MARK: - 智能评估
    
    private func evaluateSmartArchiveConditions(_ product: Product) -> ArchiveSuggestion? {
        // 分析使用模式
        let usagePattern = analyzeUsagePattern(product)
        
        // 季节性物品检测
        if usagePattern.isSeasonalItem && usagePattern.isOffSeason {
            return ArchiveSuggestion(
                product: product,
                reason: .seasonalOffPeriod,
                priority: .low,
                shouldAutoArchive: false,
                confidence: usagePattern.confidence
            )
        }
        
        // 替代品检测
        if usagePattern.hasActiveAlternatives {
            return ArchiveSuggestion(
                product: product,
                reason: .replacedByAlternative,
                priority: .medium,
                shouldAutoArchive: false,
                confidence: usagePattern.confidence
            )
        }
        
        // 使用频率急剧下降
        if usagePattern.hasSignificantUsageDecline {
            return ArchiveSuggestion(
                product: product,
                reason: .usageDecline(rate: usagePattern.declineRate),
                priority: .medium,
                shouldAutoArchive: false,
                confidence: usagePattern.confidence
            )
        }
        
        return nil
    }
    
    // MARK: - 使用模式分析
    
    private func analyzeUsagePattern(_ product: Product) -> UsagePattern {
        guard let records = product.usageRecords?.allObjects as? [UsageRecord],
              !records.isEmpty else {
            return UsagePattern()
        }
        
        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        
        // 分析季节性
        let seasonality = analyzeSeasonality(sortedRecords)
        
        // 分析替代品
        let alternatives = analyzeAlternatives(product)
        
        // 分析使用趋势
        let trend = analyzeUsageTrend(sortedRecords)
        
        return UsagePattern(
            isSeasonalItem: seasonality.isSeasonal,
            isOffSeason: seasonality.isOffSeason,
            hasActiveAlternatives: alternatives.hasActive,
            hasSignificantUsageDecline: trend.hasSignificantDecline,
            declineRate: trend.declineRate,
            confidence: min(seasonality.confidence, alternatives.confidence, trend.confidence)
        )
    }
    
    // MARK: - 执行归档
    
    private func executeAutoArchive(_ products: [Product]) async {
        for product in products {
            await archiveProduct(product, reason: "自动归档")
        }
        
        // 更新归档产品列表
        archivedProducts = repository.fetchAllProducts().filter { $0.lifecycleStatus == .archived }
    }
    
    /// 归档单个产品
    func archiveProduct(_ product: Product, reason: String) async {
        product.archive()
        
        // 记录归档信息
        await recordArchiveAction(product: product, reason: reason)
        
        // 保存更改到Core Data
        do {
            try context.save()
        } catch {
            print("归档产品时保存失败: \(error)")
        }
        
        // 从待归档列表中移除
        pendingArchiveProducts.removeAll { $0.id == product.id }
        
        // 添加到已归档列表
        if !archivedProducts.contains(where: { $0.id == product.id }) {
            archivedProducts.append(product)
        }
    }
    
    /// 恢复归档产品
    func unarchiveProduct(_ product: Product) async {
        product.unarchive()
        
        // 记录恢复信息
        await recordUnarchiveAction(product: product)
        
        // 保存更改
        repository.save { _ in }
        
        // 从已归档列表中移除
        archivedProducts.removeAll { $0.id == product.id }
    }
    
    // MARK: - 辅助方法
    
    private func calculateDaysSinceDepletion(_ product: Product) -> Int {
        // 查找最后一次库存为0的记录
        guard let records = product.usageRecords?.allObjects as? [UsageRecord] else { return 0 }
        
        let depletionRecords = records.filter { $0.remainingQuantity <= 0 }
        guard let lastDepletion = depletionRecords.max(by: { ($0.date ?? Date()) < ($1.date ?? Date()) }) else {
            return 0
        }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: lastDepletion.date ?? Date(), to: Date())
        return components.day ?? 0
    }
    
    private func calculateDaysSinceExpiry(_ product: Product) -> Int {
        guard let expiryDate = product.expiryDate else { return 0 }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: expiryDate, to: Date())
        return max(0, components.day ?? 0)
    }
    
    private func calculateConfidence(for product: Product, reason: ArchiveReason) -> Double {
        switch reason {
        case .longTermUnused(let days):
            // 未使用时间越长，置信度越高
            return min(0.95, Double(days) / Double(config.unusedDaysThreshold * 2))
        case .depletedLongTime:
            return 0.9
        case .expiredLongTime:
            return 0.95
        default:
            return 0.7
        }
    }
    
    private func recordArchiveAction(product: Product, reason: String) async {
        // 这里可以记录归档操作到日志或创建特殊的使用记录
        print("📦 产品已归档: \(product.name ?? "未知") - 原因: \(reason)")
    }
    
    private func recordUnarchiveAction(product: Product) async {
        // 记录恢复操作
        print("📤 产品已恢复: \(product.name ?? "未知")")
    }
    
    private func generateArchiveReminders(_ suggestions: [ArchiveSuggestion]) async {
        for suggestion in suggestions {
            if suggestion.priority == .high {
                reminderService.generateAutoArchiveSuggestionReminder(for: suggestion.product, suggestion: suggestion)
            }
        }
    }
    
    // MARK: - 建议管理
    
    /// 忽略归档建议
    func ignoreArchiveSuggestion(for product: Product) {
        archiveSuggestions.removeAll { $0.product.id == product.id }
        
        // 记录忽略操作，避免短期内再次建议
        let key = "ignored_archive_\(product.id?.uuidString ?? "")"
        UserDefaults.standard.set(Date(), forKey: key)
    }
    
    /// 延迟归档建议
    func postponeArchiveSuggestion(for product: Product, days: Int = 7) {
        archiveSuggestions.removeAll { $0.product.id == product.id }
        
        // 记录延迟操作
        let key = "postponed_archive_\(product.id?.uuidString ?? "")"
        let postponeUntil = Calendar.current.date(byAdding: .day, value: days, to: Date()) ?? Date()
        UserDefaults.standard.set(postponeUntil, forKey: key)
    }
    
    /// 检查产品是否被忽略或延迟
    private func isProductIgnoredOrPostponed(_ product: Product) -> Bool {
        guard let productId = product.id?.uuidString else { return false }
        
        // 检查是否被忽略（忽略期限为30天）
        let ignoredKey = "ignored_archive_\(productId)"
        if let ignoredDate = UserDefaults.standard.object(forKey: ignoredKey) as? Date {
            let daysSinceIgnored = Calendar.current.dateComponents([.day], from: ignoredDate, to: Date()).day ?? 0
            if daysSinceIgnored < 30 {
                return true
            }
        }
        
        // 检查是否被延迟
        let postponedKey = "postponed_archive_\(productId)"
        if let postponedUntil = UserDefaults.standard.object(forKey: postponedKey) as? Date {
            if Date() < postponedUntil {
                return true
            }
        }
        
        return false
    }
    
    /// 执行单个产品归档（公共方法）
    func executeArchive(for product: Product) async throws {
        await archiveProduct(product, reason: "手动归档")
    }
    
    /// 获取配置（兼容性方法）
    var configuration: AutoArchiveConfiguration {
        return AutoArchiveConfiguration(
            isEnabled: config.enableAutoArchive,
            unusedDaysThreshold: config.unusedDaysThreshold,
            depletedDaysThreshold: config.depletedDaysThreshold,
            expiredDaysThreshold: config.expiredDaysThreshold,
            enableSmartSuggestions: config.enableSmartSuggestions,
            confidenceThreshold: 0.7,
            checkFrequencyDays: 7
        )
    }
    
    /// 更新配置（兼容性方法）
    func updateConfiguration(_ configuration: AutoArchiveConfiguration) {
        let newConfig = AutoArchiveConfig(
            unusedDaysThreshold: configuration.unusedDaysThreshold,
            depletedDaysThreshold: configuration.depletedDaysThreshold,
            expiredDaysThreshold: configuration.expiredDaysThreshold,
            enableAutoArchive: configuration.isEnabled,
            enableSmartSuggestions: configuration.enableSmartSuggestions
        )
        updateConfiguration(newConfig)
    }
    
    /// 执行归档检查（兼容性方法）
    func performArchiveCheck() async {
        await performAutoArchiveCheck()
    }
}

// MARK: - 数据模型

/// 归档建议
struct ArchiveSuggestion: Identifiable {
    let id = UUID()
    let product: Product
    let reason: ArchiveReason
    let priority: Priority
    let shouldAutoArchive: Bool
    let confidence: Double
    let createdAt = Date()
    
    enum Priority: Int, CaseIterable {
        case high = 1
        case medium = 2
        case low = 3
        
        var color: Color {
            switch self {
            case .high: return .red
            case .medium: return .orange
            case .low: return .blue
            }
        }
        
        var description: String {
            switch self {
            case .high: return "高优先级"
            case .medium: return "中优先级"
            case .low: return "低优先级"
            }
        }
    }
}

/// 归档原因
enum ArchiveReason {
    case longTermUnused(days: Int)
    case depletedLongTime(days: Int)
    case expiredLongTime(days: Int)
    case seasonalOffPeriod
    case replacedByAlternative
    case usageDecline(rate: Double)
    case userRequested
    
    var description: String {
        switch self {
        case .longTermUnused(let days):
            return "已\(days)天未使用"
        case .depletedLongTime(let days):
            return "耗尽\(days)天未补充"
        case .expiredLongTime(let days):
            return "过期\(days)天未处理"
        case .seasonalOffPeriod:
            return "季节性物品非使用期"
        case .replacedByAlternative:
            return "已有替代品在使用"
        case .usageDecline(let rate):
            return "使用频率下降\(String(format: "%.1f", rate * 100))%"
        case .userRequested:
            return "用户手动归档"
        }
    }
}

/// 使用模式分析结果
struct UsagePattern {
    let isSeasonalItem: Bool
    let isOffSeason: Bool
    let hasActiveAlternatives: Bool
    let hasSignificantUsageDecline: Bool
    let declineRate: Double
    let confidence: Double
    
    init(isSeasonalItem: Bool = false,
         isOffSeason: Bool = false,
         hasActiveAlternatives: Bool = false,
         hasSignificantUsageDecline: Bool = false,
         declineRate: Double = 0.0,
         confidence: Double = 0.5) {
        self.isSeasonalItem = isSeasonalItem
        self.isOffSeason = isOffSeason
        self.hasActiveAlternatives = hasActiveAlternatives
        self.hasSignificantUsageDecline = hasSignificantUsageDecline
        self.declineRate = declineRate
        self.confidence = confidence
    }
}

// MARK: - 扩展方法

private extension ConsumableAutoArchiveService {
    
    func analyzeSeasonality(_ records: [UsageRecord]) -> (isSeasonal: Bool, isOffSeason: Bool, confidence: Double) {
        // 简化的季节性分析
        // 实际实现可以更复杂，分析月份分布等
        return (false, false, 0.5)
    }
    
    func analyzeAlternatives(_ product: Product) -> (hasActive: Bool, confidence: Double) {
        // 简化的替代品分析
        // 可以基于类别、标签等分析是否有活跃的替代品
        return (false, 0.5)
    }
    
    func analyzeUsageTrend(_ records: [UsageRecord]) -> (hasSignificantDecline: Bool, declineRate: Double, confidence: Double) {
        guard records.count >= 10 else {
            return (false, 0.0, 0.3)
        }
        
        // 简化的趋势分析
        let midIndex = records.count / 2
        let firstHalf = records[0..<midIndex]
        let secondHalf = records[midIndex..<records.count]
        
        let firstHalfCount = firstHalf.count
        let secondHalfCount = secondHalf.count
        
        if firstHalfCount > 0 && secondHalfCount > 0 {
            let declineRate = Double(firstHalfCount - secondHalfCount) / Double(firstHalfCount)
            return (declineRate > 0.5, declineRate, 0.7)
        }
        
        return (false, 0.0, 0.5)
    }
}