import Foundation
import UIKit
import Combine
import CryptoKit
import os.log

// MARK: - iCloud Documents备份错误

/// iCloud Documents备份错误类型
enum iCloudDocumentsBackupError: Error, LocalizedError {
    case iCloudUnavailable
    case containerAccessFailed
    case documentsDirectoryNotFound
    case fileWriteFailed(String)
    case fileReadFailed(String)
    case invalidBackupFile
    case encodingFailed
    case decodingFailed
    case deviceInfoRetrievalFailed
    
    var errorDescription: String? {
        switch self {
        case .iCloudUnavailable:
            return "iCloud服务不可用"
        case .containerAccessFailed:
            return "无法访问iCloud容器"
        case .documentsDirectoryNotFound:
            return "未找到Documents目录"
        case .fileWriteFailed(let reason):
            return "文件写入失败: \(reason)"
        case .fileReadFailed(let reason):
            return "文件读取失败: \(reason)"
        case .invalidBackupFile:
            return "无效的备份文件"
        case .encodingFailed:
            return "数据编码失败"
        case .decodingFailed:
            return "数据解码失败"
        case .deviceInfoRetrievalFailed:
            return "获取设备信息失败"
        }
    }
}

// MARK: - 跨设备备份文件结构

/// 跨设备备份文件元数据
struct CrossDeviceBackupMetadata: Codable {
    let backupId: UUID
    let deviceName: String
    let deviceModel: String
    let systemVersion: String
    let appVersion: String
    let deviceIdentifier: String
    let createdAt: Date
    let backupType: BackupType
    let totalEntities: Int
    let fileSize: Int64
    let dataChecksum: String
    let description: String?
    
    enum BackupType: String, Codable, CaseIterable {
        case full = "full"
        case incremental = "incremental"
        
        var displayName: String {
            switch self {
            case .full: return "完整备份"
            case .incremental: return "增量备份"
            }
        }
    }
    
    /// 格式化的文件大小
    var formattedFileSize: String {
        ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter.string(from: createdAt)
    }
}

/// 跨设备备份文件容器
struct CrossDeviceBackupFile: Codable {
    let metadata: CrossDeviceBackupMetadata
    let backupData: BackupDataContainer

    /// 生成文件名
    var fileName: String {
        let deviceName = metadata.deviceName.replacingOccurrences(of: " ", with: "_")
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: metadata.createdAt)
        return "PinkLog_\(deviceName)_\(timestamp)_\(metadata.backupType.rawValue).pinklogbackup"
    }
}

/// 旧版本的简化备份文件结构（用于向后兼容）
struct LegacyCrossDeviceBackupFile: Codable {
    let backupId: UUID?
    let deviceName: String?
    let deviceModel: String?
    let systemVersion: String?
    let appVersion: String?
    let deviceIdentifier: String?
    let createdAt: Date?
    let backupType: CrossDeviceBackupMetadata.BackupType?
    let totalEntities: Int?
    let dataChecksum: String?
    let description: String?

    // 可能存在的其他旧字段
    let version: String?
    let fileSize: Int64?
}

// MARK: - iCloud Documents备份服务

/// iCloud Documents备份服务 - 提供跨设备备份文件访问
@MainActor
class iCloudDocumentsBackupService: ObservableObject {
    
    // MARK: - 单例
    static let shared = iCloudDocumentsBackupService()
    
    // MARK: - 发布属性
    @Published var isAvailable: Bool = false
    @Published var availableBackups: [CrossDeviceBackupMetadata] = []
    @Published var isScanning: Bool = false
    @Published var lastError: iCloudDocumentsBackupError?

    // MARK: - 私有属性
    private let fileManager = FileManager.default
    private let logger = Logger(subsystem: "com.pinklog.backup", category: "iCloudDocumentsBackup")
    private var refreshTimer: Timer?

    // MARK: - 内存管理
    /// 缓存的完整备份文件数据（用于临时存储）
    private var cachedBackupFiles: [UUID: CrossDeviceBackupFile] = [:]
    /// 缓存清理定时器
    private var cacheCleanupTimer: Timer?
    /// 最后访问时间记录
    private var lastAccessTimes: [UUID: Date] = [:]
    /// 缓存超时时间（秒）
    private let cacheTimeout: TimeInterval = 300 // 5分钟
    
    // iCloud容器标识符 (需要与项目配置保持一致)
    private let ubiquityContainerIdentifier = "iCloud.top.trysapp.pinklog"
    
    // 备份文件目录名
    private let backupDirectoryName = "PinkLogBackups"
    
    // MARK: - 初始化
    private init() {
        // 使用全局缓存管理器，避免重复定时器
        registerCacheCleanup()

        // 不在初始化时立即检查iCloud可用性，等待外部调用
        print("📋 iCloudDocumentsBackupService初始化完成，等待启动信号")
    }

    /// 启动iCloud备份服务（延迟启动）
    func startService() {
        print("🚀 启动iCloud备份服务...")
        checkiCloudAvailability()
    }
    
    /// 启动iCloud备份服务并等待完成（同步版本）
    func startServiceAndWait() async {
        print("🚀 启动iCloud备份服务...")
        await checkiCloudAvailabilityAndWait()
    }
    
    // MARK: - iCloud可用性检查
    
    /// 检查iCloud Documents可用性
    func checkiCloudAvailability() {
        logger.info("🔍 检查iCloud Documents可用性...")
        
        Task {
            await checkiCloudAvailabilityAndWait()
        }
    }
    
    /// 检查iCloud Documents可用性并等待完成（同步版本）
    func checkiCloudAvailabilityAndWait() async {
        logger.info("🔍 检查iCloud Documents可用性...")
        
        // 检查iCloud账户状态
        guard let containerURL = fileManager.url(forUbiquityContainerIdentifier: ubiquityContainerIdentifier) else {
            logger.error("❌ 无法获取iCloud容器URL")
            await MainActor.run {
                self.isAvailable = false
                self.lastError = .containerAccessFailed
            }
            return
        }
        
        logger.info("✅ iCloud容器URL获取成功: \(containerURL.path)")
        
        // 确保Documents目录存在
        let documentsURL = containerURL.appendingPathComponent("Documents")
        if !fileManager.fileExists(atPath: documentsURL.path) {
            do {
                try fileManager.createDirectory(at: documentsURL, withIntermediateDirectories: true)
                logger.info("✅ Documents目录创建成功")
            } catch {
                logger.error("❌ Documents目录创建失败: \(error.localizedDescription)")
                await MainActor.run {
                    self.isAvailable = false
                    self.lastError = .documentsDirectoryNotFound
                }
                return
            }
        }
        
        await MainActor.run {
            self.isAvailable = true
            self.lastError = nil
        }

        // 延迟扫描备份文件，避免阻塞启动流程
        Task.detached(priority: .background) { [weak self] in
            // 等待2秒，让应用完成主要启动流程
            try? await Task.sleep(nanoseconds: 2_000_000_000)
            await self?.scanAvailableBackups()
        }

        // 启动定期刷新
        await MainActor.run {
            self.startPeriodicRefresh()
        }
        
        logger.info("✅ iCloud Documents服务启动完成")
    }
    
    // MARK: - 备份文件管理
    
    /// 保存备份到iCloud Documents
    func saveBackup(_ container: BackupDataContainer, 
                   description: String? = nil,
                   type: CrossDeviceBackupMetadata.BackupType = .full) async throws -> CrossDeviceBackupMetadata {
        
        logger.info("💾 开始保存备份到iCloud Documents...")
        
        guard isAvailable else {
            throw iCloudDocumentsBackupError.iCloudUnavailable
        }
        
        // 获取设备信息
        let deviceInfo = await getDeviceInfo()
        
        // 创建备份元数据
        let metadata = CrossDeviceBackupMetadata(
            backupId: UUID(),
            deviceName: deviceInfo.name,
            deviceModel: deviceInfo.model,
            systemVersion: deviceInfo.systemVersion,
            appVersion: deviceInfo.appVersion,
            deviceIdentifier: deviceInfo.identifier,
            createdAt: Date(),
            backupType: type,
            totalEntities: container.totalEntities,
            fileSize: Int64(container.estimatedSize),
            dataChecksum: container.dataChecksum,
            description: description
        )
        
        // 创建备份文件容器
        let backupFile = CrossDeviceBackupFile(
            metadata: metadata,
            backupData: container
        )
        
        // 编码备份数据
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        let backupData: Data
        do {
            backupData = try encoder.encode(backupFile)
            logger.info("✅ 备份数据编码成功，大小: \(ByteCountFormatter.string(fromByteCount: Int64(backupData.count), countStyle: .file))")
        } catch {
            logger.error("❌ 备份数据编码失败: \(error.localizedDescription)")
            throw iCloudDocumentsBackupError.encodingFailed
        }
        
        // 获取保存路径
        let backupDirectoryURL = try getBackupDirectoryURL()
        let fileURL = backupDirectoryURL.appendingPathComponent(backupFile.fileName)
        
        // 🚀 关键优化：分离存储metadata和数据
        do {
            // 1. 保存主备份文件（只包含数据）
            let dataOnlyFile = BackupDataContainer(
                backupType: container.backupType,
                products: container.products,
                usageRecords: container.usageRecords,
                relatedExpenses: container.relatedExpenses,
                categories: container.categories,
                tags: container.tags,
                loanRecords: container.loanRecords,
                expenseTypes: container.expenseTypes,
                purchaseChannels: container.purchaseChannels,
                purchaseChannelCategories: container.purchaseChannelCategories,
                reminders: container.reminders,
                productLinks: container.productLinks,
                conversations: container.conversations,
                messages: container.messages
            )

            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let dataOnlyBackupData = try encoder.encode(dataOnlyFile)

            try dataOnlyBackupData.write(to: fileURL, options: [.atomic])
            logger.info("✅ 备份数据文件保存成功: \(fileURL.lastPathComponent)")

            // 2. 保存独立的metadata文件
            let metadataFileName = backupFile.fileName.replacingOccurrences(of: ".pinklogbackup", with: ".metadata.json")
            let metadataURL = backupDirectoryURL.appendingPathComponent(metadataFileName)
            let metadataData = try encoder.encode(metadata)

            try metadataData.write(to: metadataURL, options: [.atomic])
            logger.info("✅ metadata文件保存成功: \(metadataFileName)")



            // 设置文件为需要上传到iCloud
            for var url in [fileURL, metadataURL] {
                var resourceValues = URLResourceValues()
                resourceValues.hasHiddenExtension = false
                resourceValues.isExcludedFromBackup = false
                try url.setResourceValues(resourceValues)
                try fileManager.startDownloadingUbiquitousItem(at: url)
            }
            logger.info("✅ 文件已标记为上传到iCloud")

        } catch {
            logger.error("❌ 备份文件保存失败: \(error.localizedDescription)")
            throw iCloudDocumentsBackupError.fileWriteFailed(error.localizedDescription)
        }
        
        // 更新可用备份列表
        await scanAvailableBackups()

        // 自动清理旧备份（异步执行，不影响当前备份操作）
        Task {
            do {
                let deletedCount = try await autoCleanupBackups()
                if deletedCount > 0 {
                    logger.info("🧹 自动清理完成，删除了 \(deletedCount) 个旧备份")
                }
            } catch {
                logger.warning("⚠️ 自动清理失败: \(error.localizedDescription)")
            }
        }

        logger.info("✅ 跨设备备份完成: \(fileURL.lastPathComponent)")
        return metadata
    }
    
    /// 扫描可用的备份文件（metadata分离优化版本）
    func scanAvailableBackups() async {
        // 使用内存监控跟踪整个扫描过程
        await MemoryMonitor.shared.monitorMemoryUsage("备份文件扫描") {
            logger.info("🔍 扫描可用的备份文件...")

            await MainActor.run {
                self.isScanning = true
            }

            defer {
                Task { @MainActor in
                    self.isScanning = false
                }
            }

            guard isAvailable else {
                logger.warning("⚠️ iCloud不可用，跳过扫描")
                return
            }

            do {
                let backupDirectoryURL = try getBackupDirectoryURL()

                // 🚀 优先使用独立metadata文件系统（高效加载）
                let independentMetadata = await loadAllIndependentMetadata(in: backupDirectoryURL)
                if !independentMetadata.isEmpty {
                    logger.info("🔍 检测到独立metadata文件系统，使用快速加载模式")
                    logger.info("⚡ 从独立metadata文件快速加载: \(independentMetadata.count)个备份")
                    
                    await MainActor.run {
                        self.availableBackups = independentMetadata
                    }
                    return
                }

                logger.info("📋 未找到独立metadata文件，执行完整文件扫描...")

                // 在后台线程触发iCloud文件查询，避免UI阻塞
                await Task.detached(priority: .userInitiated) {
                    do {
                        try FileManager.default.startDownloadingUbiquitousItem(at: backupDirectoryURL)
                        await MainActor.run {
                            self.logger.info("✅ 已触发iCloud文件同步")
                        }
                    } catch {
                        await MainActor.run {
                            self.logger.warning("⚠️ 触发iCloud文件同步失败: \(error.localizedDescription)")
                        }
                    }
                }.value

                // 在后台线程执行文件系统扫描，避免UI阻塞
                let fileURLs = await Task.detached(priority: .userInitiated) {
                    do {
                        return try FileManager.default.contentsOfDirectory(
                            at: backupDirectoryURL,
                            includingPropertiesForKeys: [
                                .fileSizeKey,
                                .contentModificationDateKey,
                                .ubiquitousItemDownloadingStatusKey
                            ],
                            options: [.skipsHiddenFiles, .includesDirectoriesPostOrder]
                        )
                    } catch {
                        await MainActor.run {
                            self.logger.error("❌ 扫描备份目录失败: \(error.localizedDescription)")
                        }
                        return []
                    }
                }.value

                // 过滤出备份文件
                let backupFileURLs = fileURLs.filter { $0.pathExtension == "pinklogbackup" }

                // 优化：分批处理文件，避免同时加载太多文件到内存，并添加主线程保护
                let batchSize = 2 // 减少批次大小，降低内存压力
                var backups: [CrossDeviceBackupMetadata] = []

                for (_, batch) in backupFileURLs.chunked(into: batchSize).enumerated() {
                    // 处理批次
                    let batchResults = await processBatch(batch)

                    backups.append(contentsOf: batchResults)

                    // 每处理一个批次后，让出主线程控制权
                    await Task.yield()

                    // 强制内存清理和监控
                    if backups.count % (batchSize * 2) == 0 {
                        MemoryMonitor.shared.recordMemoryUsage("批次处理完成 - 已处理\(backups.count)个文件")

                        // 简单的暂停处理，避免过度占用资源
                        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                    }
                }

                // 按创建时间倒序排列
                backups.sort { $0.createdAt > $1.createdAt }

                await MainActor.run {
                    self.availableBackups = backups
                }

                logger.info("✅ 扫描完成，找到 \(backups.count) 个备份文件")
                MemoryMonitor.shared.recordMemoryUsage("备份文件扫描完成")

            } catch {
                logger.error("❌ 扫描备份文件失败: \(error.localizedDescription)")
                await MainActor.run {
                    self.lastError = .fileReadFailed(error.localizedDescription)
                }
            }
        }
    }

    /// 批量处理备份文件（内存优化 + 后台线程优化）
    private func processBatch(_ fileURLs: [URL]) async -> [CrossDeviceBackupMetadata] {
        // 🚀 将整个批处理移到后台线程，避免主线程阻塞
        return await Task.detached(priority: .userInitiated) {
            var results: [CrossDeviceBackupMetadata] = []

            for fileURL in fileURLs {
                do {
                    // 检查文件是否可用
                    let resourceValues = try fileURL.resourceValues(forKeys: [
                        .ubiquitousItemDownloadingStatusKey
                    ])

                    // 如果文件未下载，先触发下载
                    if let downloadingStatus = resourceValues.ubiquitousItemDownloadingStatus {
                        switch downloadingStatus {
                        case .notDownloaded:
                            await MainActor.run {
                                self.logger.info("📥 开始下载远程备份文件: \(fileURL.lastPathComponent)")
                            }
                            try FileManager.default.startDownloadingUbiquitousItem(at: fileURL)
                            
                            // 给下载一些时间，然后检查状态
                            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒
                            
                            // 重新检查下载状态
                            let newResourceValues = try? fileURL.resourceValues(forKeys: [.ubiquitousItemDownloadingStatusKey])
                            if let newStatus = newResourceValues?.ubiquitousItemDownloadingStatus {
                                switch newStatus {
                                case .downloaded, .current:
                                    await MainActor.run {
                                        self.logger.info("✅ 文件下载完成: \(fileURL.lastPathComponent)")
                                    }
                                    // 下载完成，继续处理
                                case .notDownloaded:
                                    await MainActor.run {
                                        self.logger.warning("⏳ 文件仍在下载中，将在下次扫描时重新检查: \(fileURL.lastPathComponent)")
                                    }
                                    continue
                                default:
                                    await MainActor.run {
                                        self.logger.warning("⚠️ 下载状态未知，跳过文件: \(fileURL.lastPathComponent)")
                                    }
                                    continue
                                }
                            } else {
                                await MainActor.run {
                                    self.logger.warning("⚠️ 无法检查下载状态，跳过文件: \(fileURL.lastPathComponent)")
                                }
                                continue
                            }
                        case .downloaded:
                            await MainActor.run {
                                self.logger.info("✅ 文件已下载: \(fileURL.lastPathComponent)")
                            }
                        case .current:
                            await MainActor.run {
                                self.logger.info("✅ 文件是最新的: \(fileURL.lastPathComponent)")
                            }
                        default:
                            await MainActor.run {
                                self.logger.warning("⚠️ 未知下载状态: \(fileURL.lastPathComponent)")
                            }
                        }
                    }

                    let metadata = try await self.loadBackupMetadata(from: fileURL)
                    results.append(metadata)
                    await MainActor.run {
                        self.logger.info("✅ 加载备份元数据: \(fileURL.lastPathComponent)")
                    }
                } catch {
                    await MainActor.run {
                        self.logger.warning("⚠️ 无法加载备份文件元数据: \(fileURL.lastPathComponent), 错误: \(error.localizedDescription)")
                    }
                }
            }

            return results
        }.value
    }
    
    /// 加载指定备份的完整数据（内存优化版本，带缓存）
    func loadBackup(with backupId: UUID) async throws -> CrossDeviceBackupFile {
        return try await MemoryMonitor.shared.monitorMemoryUsage("加载完整备份文件: \(backupId)") {
            logger.info("📖 开始加载备份数据: \(backupId)")
            
            // 详细记录当前可用备份列表状态
            logger.info("📋 当前可用备份数量: \(self.availableBackups.count)")
            for (index, backup) in self.availableBackups.enumerated() {
                logger.info("📄 备份[\(index)]: ID=\(backup.backupId), 设备=\(backup.deviceName), 时间=\(backup.formattedCreatedAt)")
            }

            // 检查缓存
            if let cachedFile = cachedBackupFiles[backupId] {
                lastAccessTimes[backupId] = Date()
                logger.info("✅ 从缓存加载备份数据: \(backupId)")
                MemoryMonitor.shared.recordMemoryUsage("从缓存加载备份文件")
                return cachedFile
            }

            // 验证目标备份是否在可用列表中
            guard let targetBackup = self.availableBackups.first(where: { $0.backupId == backupId }) else {
                logger.error("❌ 目标备份不在可用列表中: \(backupId)")
                logger.info("🔍 可用备份IDs: \(self.availableBackups.map { $0.backupId.uuidString })")
                throw iCloudDocumentsBackupError.invalidBackupFile
            }
            
            logger.info("✅ 找到目标备份: 设备=\(targetBackup.deviceName), 时间=\(targetBackup.formattedCreatedAt)")

            let backupDirectoryURL = try getBackupDirectoryURL()
            logger.info("📁 备份目录: \(backupDirectoryURL.path)")

            // 查找对应的备份文件
            let fileURLs = try fileManager.contentsOfDirectory(at: backupDirectoryURL, includingPropertiesForKeys: nil)
            let backupFiles = fileURLs.filter { $0.pathExtension == "pinklogbackup" }
            
            logger.info("📂 目录中找到 \(backupFiles.count) 个备份文件:")
            for (index, fileURL) in backupFiles.enumerated() {
                let fileSize = (try? fileURL.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                logger.info("📄 文件[\(index)]: \(fileURL.lastPathComponent) (\(fileSize) bytes)")
            }

            var metadataLoadErrors: [String] = []
            
            for fileURL in backupFiles {
                logger.info("🔍 检查文件: \(fileURL.lastPathComponent)")
                
                // 检查iCloud同步状态
                let syncStatus = await checkiCloudSyncStatus(for: fileURL)
                logger.info("☁️ iCloud文件状态: \(syncStatus)")
                
                if syncStatus == "iCloud占位符文件" {
                    logger.warning("⚠️ 检测到iCloud占位符，尝试触发下载...")
                    do {
                        try fileManager.startDownloadingUbiquitousItem(at: fileURL)
                        // 等待下载开始
                        try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                        
                        let newStatus = await checkiCloudSyncStatus(for: fileURL)
                        logger.info("📥 下载后状态: \(newStatus)")
                    } catch {
                        logger.warning("⚠️ 触发下载失败: \(error.localizedDescription)")
                    }
                }
                
                do {
                    let fileMetadata = try await loadBackupMetadata(from: fileURL)
                    logger.info("✅ 元数据加载成功: ID=\(fileMetadata.backupId), 设备=\(fileMetadata.deviceName)")
                    
                    if fileMetadata.backupId == backupId {
                        logger.info("🎯 找到匹配的备份文件: \(fileURL.lastPathComponent)")
                        
                        // 找到匹配的文件，加载完整数据
                        logger.info("📥 开始加载完整备份数据...")
                        let data = try Data(contentsOf: fileURL)
                        logger.info("📦 文件数据加载完成，大小: \(data.count) bytes")
                        
                        let decoder = JSONDecoder()
                        decoder.dateDecodingStrategy = .iso8601

                        // 首先尝试解析JSON结构
                        do {
                            guard let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                                logger.error("❌ 文件不是有效的JSON格式")
                                throw iCloudDocumentsBackupError.invalidBackupFile
                            }
                            
                            logger.info("✅ JSON格式验证成功")
                            logger.info("📋 JSON根级字段: \(Array(jsonObject.keys).sorted())")
                            
                            // 检查必需的字段
                            if let metadataObj = jsonObject["metadata"] as? [String: Any] {
                                logger.info("✅ 发现metadata字段，包含键: \(Array(metadataObj.keys).sorted())")
                            } else {
                                logger.error("❌ 缺少metadata字段")
                            }
                            
                            if let backupDataObj = jsonObject["backupData"] as? [String: Any] {
                                logger.info("✅ 发现backupData字段，包含键: \(Array(backupDataObj.keys).sorted())")
                            } else {
                                logger.error("❌ 缺少backupData字段")
                            }
                            
                        } catch {
                            logger.error("❌ JSON结构分析失败: \(error.localizedDescription)")
                        }

                        // 尝试解码为新格式
                        let backupFile: CrossDeviceBackupFile
                        do {
                            backupFile = try decoder.decode(CrossDeviceBackupFile.self, from: data)
                            logger.info("✅ 新格式JSON解码成功")
                        } catch {
                            logger.warning("⚠️ 新格式解码失败，尝试旧格式: \(error.localizedDescription)")
                            
                            // 尝试解码为旧格式（直接的BackupDataContainer）
                            do {
                                let container = try decoder.decode(BackupDataContainer.self, from: data)
                                logger.info("✅ 检测到旧格式：BackupDataContainer")
                                
                                // 构造临时元数据
                                let legacyMetadata = CrossDeviceBackupMetadata(
                                    backupId: backupId,
                                    deviceName: container.deviceInfo.deviceModel, // 使用deviceModel作为deviceName
                                    deviceModel: container.deviceInfo.deviceModel,
                                    systemVersion: container.deviceInfo.systemVersion,
                                    appVersion: container.deviceInfo.appVersion,
                                    deviceIdentifier: container.deviceInfo.deviceId,
                                    createdAt: container.createdAt,
                                    backupType: .full,
                                    totalEntities: container.totalEntities,
                                    fileSize: Int64(data.count),
                                    dataChecksum: container.dataChecksum,
                                    description: "从旧格式自动转换"
                                )
                                
                                backupFile = CrossDeviceBackupFile(
                                    metadata: legacyMetadata,
                                    backupData: container
                                )
                                logger.info("✅ 旧格式转换为新格式成功")
                            } catch {
                                logger.error("❌ 旧格式解码也失败: \(error.localizedDescription)")
                                throw error
                            }
                        }

                        // 缓存加载的数据
                        cachedBackupFiles[backupId] = backupFile
                        lastAccessTimes[backupId] = Date()

                        logger.info("✅ 备份数据加载成功并已缓存: \(backupId)")
                        MemoryMonitor.shared.recordMemoryUsage("完整备份文件加载完成并缓存")
                        return backupFile
                    } else {
                        logger.info("⏭️ ID不匹配，继续查找下一个文件")
                    }
                } catch {
                    let errorMsg = "元数据加载失败[\(fileURL.lastPathComponent)]: \(error.localizedDescription)"
                    metadataLoadErrors.append(errorMsg)
                    logger.warning("⚠️ \(errorMsg)")
                    continue
                }
            }
            
            // 记录所有失败信息
            logger.error("❌ 在 \(backupFiles.count) 个文件中未找到匹配的备份ID: \(backupId)")
            if !metadataLoadErrors.isEmpty {
                logger.error("⚠️ 元数据加载错误汇总:")
                for (index, error) in metadataLoadErrors.enumerated() {
                    logger.error("   [\(index+1)] \(error)")
                }
            }

            throw iCloudDocumentsBackupError.invalidBackupFile
        }
    }
    
    /// 删除指定的备份文件和对应的metadata文件
    func deleteBackup(with backupId: UUID) async throws {
        logger.info("🗑️ 删除备份: \(backupId)")

        let backupDirectoryURL = try getBackupDirectoryURL()
        let fileURLs = try fileManager.contentsOfDirectory(at: backupDirectoryURL, includingPropertiesForKeys: nil)

        // 第一轮：尝试通过UUID匹配删除
        for fileURL in fileURLs {
            guard fileURL.pathExtension == "pinklogbackup" else { continue }

            do {
                let metadata = try await loadBackupMetadata(from: fileURL)
                if metadata.backupId == backupId {
                    // 删除备份文件
                    try fileManager.removeItem(at: fileURL)
                    logger.info("✅ 备份文件删除成功: \(fileURL.lastPathComponent)")
                    
                    // 🚀 同时删除对应的metadata文件
                    let metadataFileName = fileURL.lastPathComponent.replacingOccurrences(of: ".pinklogbackup", with: ".metadata.json")
                    let metadataURL = backupDirectoryURL.appendingPathComponent(metadataFileName)
                    
                    if fileManager.fileExists(atPath: metadataURL.path) {
                        do {
                            try fileManager.removeItem(at: metadataURL)
                            logger.info("✅ 对应metadata文件删除成功: \(metadataFileName)")
                        } catch {
                            logger.warning("⚠️ metadata文件删除失败: \(metadataFileName), 错误: \(error.localizedDescription)")
                        }
                    } else {
                        logger.info("ℹ️ 未找到对应的metadata文件: \(metadataFileName)")
                    }

                    // 更新可用备份列表
                    await scanAvailableBackups()
                    return
                }
            } catch {
                continue
            }
        }

        // 第二轮：如果UUID匹配失败，尝试通过确定性UUID匹配删除（针对降级元数据的情况）
        logger.warning("⚠️ UUID匹配失败，尝试确定性UUID匹配删除")
        for fileURL in fileURLs {
            guard fileURL.pathExtension == "pinklogbackup" else { continue }

            let deterministicUUID = generateDeterministicUUID(from: fileURL)
            if deterministicUUID == backupId {
                do {
                    // 删除备份文件
                    try fileManager.removeItem(at: fileURL)
                    logger.info("✅ 备份文件删除成功（确定性UUID匹配）: \(fileURL.lastPathComponent)")
                    
                    // 🚀 同时删除对应的metadata文件
                    let metadataFileName = fileURL.lastPathComponent.replacingOccurrences(of: ".pinklogbackup", with: ".metadata.json")
                    let metadataURL = backupDirectoryURL.appendingPathComponent(metadataFileName)
                    
                    if fileManager.fileExists(atPath: metadataURL.path) {
                        do {
                            try fileManager.removeItem(at: metadataURL)
                            logger.info("✅ 对应metadata文件删除成功: \(metadataFileName)")
                        } catch {
                            logger.warning("⚠️ metadata文件删除失败: \(metadataFileName), 错误: \(error.localizedDescription)")
                        }
                    } else {
                        logger.info("ℹ️ 未找到对应的metadata文件: \(metadataFileName)")
                    }

                    // 更新可用备份列表
                    await scanAvailableBackups()
                    return
                } catch {
                    logger.error("❌ 文件删除失败: \(error.localizedDescription)")
                    throw error
                }
            }
        }

        logger.error("❌ 未找到匹配的备份文件: \(backupId)")
        throw iCloudDocumentsBackupError.invalidBackupFile
    }

    /// 清空所有备份文件（危险操作）
    func clearAllBackupFiles() async throws -> Int {
        logger.warning("🚨 开始清空所有iCloud Documents备份文件...")

        let backupDirectoryURL = try getBackupDirectoryURL()
        let fileURLs = try fileManager.contentsOfDirectory(at: backupDirectoryURL, includingPropertiesForKeys: nil)

        var deletedCount = 0
        var errors: [String] = []

        for fileURL in fileURLs {
            guard fileURL.pathExtension == "pinklogbackup" else { continue }

            do {
                try fileManager.removeItem(at: fileURL)
                deletedCount += 1
                logger.info("✅ 删除备份文件: \(fileURL.lastPathComponent)")
            } catch {
                let errorMsg = "删除文件 \(fileURL.lastPathComponent) 失败: \(error.localizedDescription)"
                errors.append(errorMsg)
                logger.error("❌ \(errorMsg)")
            }
        }

        if !errors.isEmpty {
            let combinedError = "部分文件删除失败: \(errors.joined(separator: "; "))"
            logger.error("⚠️ \(combinedError)")
            // 即使有部分失败，也返回成功删除的数量，但记录错误
        }

        // 2. 删除所有metadata文件
        var metadataDeletedCount = 0
        for fileURL in fileURLs {
            if fileURL.lastPathComponent.contains(".metadata.json") {
                do {
                    try fileManager.removeItem(at: fileURL)
                    metadataDeletedCount += 1
                    logger.info("✅ 删除metadata文件: \(fileURL.lastPathComponent)")
                } catch {
                    let errorMsg = "删除metadata文件 \(fileURL.lastPathComponent) 失败: \(error.localizedDescription)"
                    errors.append(errorMsg)
                    logger.error("❌ \(errorMsg)")
                }
            }
        }



        // 3. 清理内存缓存
        await clearAllCache()

        // 4. 重置可用备份列表
        await MainActor.run {
            self.availableBackups = []
        }

        if metadataDeletedCount > 0 {
            logger.info("🎉 清空操作完成，总共删除了 \(deletedCount) 个备份文件和 \(metadataDeletedCount) 个metadata文件")
        } else {
            logger.info("🎉 清空操作完成，总共删除了 \(deletedCount) 个备份文件")
        }
        
        return deletedCount
    }

    /// 自动清理备份文件，每种类型保留最新的2个版本
    func autoCleanupBackups() async throws -> Int {
        logger.info("🧹 开始自动清理备份文件...")

        // 先刷新备份列表
        await scanAvailableBackups()

        // 按备份类型分组
        let groupedBackups = groupBackupsByType(availableBackups)

        var totalDeleted = 0

        for (backupTypeDescription, backups) in groupedBackups {
            logger.info("📂 处理备份类型: \(backupTypeDescription), 共 \(backups.count) 个备份")

            // 按创建时间排序，最新的在前
            let sortedBackups = backups.sorted { $0.createdAt > $1.createdAt }

            // 保留最新的2个，删除其余的
            let backupsToDelete = Array(sortedBackups.dropFirst(2))

            if !backupsToDelete.isEmpty {
                logger.info("🗑️ 需要删除 \(backupsToDelete.count) 个旧备份")

                for backup in backupsToDelete {
                    do {
                        try await deleteBackup(with: backup.backupId)
                        totalDeleted += 1
                        logger.info("✅ 删除旧备份: \(backup.formattedCreatedAt)")
                    } catch {
                        logger.error("❌ 删除备份失败: \(backup.formattedCreatedAt), 错误: \(error.localizedDescription)")
                    }
                }
            } else {
                logger.info("ℹ️ 该类型备份数量不超过2个，无需清理")
            }
        }

        // 最后刷新备份列表
        await scanAvailableBackups()

        logger.info("🎉 自动清理完成，总共删除了 \(totalDeleted) 个备份文件")
        return totalDeleted
    }

    /// 按备份类型分组备份
    private func groupBackupsByType(_ backups: [CrossDeviceBackupMetadata]) -> [String: [CrossDeviceBackupMetadata]] {
        var grouped: [String: [CrossDeviceBackupMetadata]] = [:]

        for backup in backups {
            let typeKey = determineBackupTypeFromDescription(backup.description)

            if grouped[typeKey] == nil {
                grouped[typeKey] = []
            }
            grouped[typeKey]?.append(backup)
        }

        return grouped
    }

    /// 根据描述确定备份类型
    private func determineBackupTypeFromDescription(_ description: String?) -> String {
        guard let desc = description else {
            return "未知类型"
        }

        if desc.contains("手动跨设备备份") || desc == "手动跨设备备份" {
            return "手动跨设备备份"
        } else if desc.contains("恢复前自动备份") || desc == "恢复前自动备份" {
            return "恢复前自动备份"
        } else if desc.contains("自动备份") || desc == "自动备份" {
            return "自动备份"
        } else {
            return "其他类型"
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 获取备份目录URL
    private func getBackupDirectoryURL() throws -> URL {
        guard let containerURL = fileManager.url(forUbiquityContainerIdentifier: ubiquityContainerIdentifier) else {
            throw iCloudDocumentsBackupError.containerAccessFailed
        }
        
        let documentsURL = containerURL.appendingPathComponent("Documents")
        let backupDirectoryURL = documentsURL.appendingPathComponent(backupDirectoryName)
        
        // 确保备份目录存在
        if !fileManager.fileExists(atPath: backupDirectoryURL.path) {
            try fileManager.createDirectory(at: backupDirectoryURL, withIntermediateDirectories: true)
            logger.info("✅ 备份目录创建成功: \(backupDirectoryURL.path)")
        }
        
        return backupDirectoryURL
    }
    
    /// 从文件加载备份元数据（metadata分离优化版本，增强容错）
    private func loadBackupMetadata(from fileURL: URL) async throws -> CrossDeviceBackupMetadata {
        // 使用内存监控来跟踪这个操作
        return try await MemoryMonitor.shared.monitorMemoryUsage("加载备份元数据: \(fileURL.lastPathComponent)") {
            
            logger.info("📖 开始加载元数据: \(fileURL.lastPathComponent)")
            
            // 检查文件是否存在且可读
            guard fileManager.fileExists(atPath: fileURL.path) else {
                logger.error("❌ 文件不存在: \(fileURL.path)")
                throw iCloudDocumentsBackupError.fileReadFailed("文件不存在")
            }
            
            // 检查文件大小
            do {
                let fileAttributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                let fileSize = fileAttributes[.size] as? Int ?? 0
                logger.info("📏 文件大小: \(fileSize) bytes")
                
                if fileSize == 0 {
                    logger.error("❌ 文件为空: \(fileURL.lastPathComponent)")
                    throw iCloudDocumentsBackupError.fileReadFailed("文件为空")
                }
            } catch {
                logger.error("❌ 无法获取文件属性: \(error.localizedDescription)")
                throw iCloudDocumentsBackupError.fileReadFailed("无法获取文件属性")
            }

            // 🚀 第一优先级：尝试从独立metadata文件加载（只需几KB）
            if let metadata = await loadMetadataFromFile(backupFileName: fileURL.lastPathComponent) {
                logger.info("⚡ 从独立metadata文件快速加载: \(fileURL.lastPathComponent)")
                return metadata
            }

            logger.info("📋 独立metadata文件不存在，使用流式读取备份文件")

            // 🚀 第二优先级：使用流式读取，避免一次性加载整个文件到内存
            do {
                let metadata = try await extractMetadataWithStreamReading(from: fileURL)

                if let validMetadata = metadata {
                    logger.info("✅ 流式元数据提取成功: \(fileURL.lastPathComponent)")
                    return validMetadata
                }
            } catch {
                logger.warning("⚠️ 流式读取失败: \(error.localizedDescription)")
            }

            logger.warning("🔄 流式提取失败，尝试完整文件解码重试机制")

            // 🚀 第三优先级：重试机制 - 尝试完整解码（作为最后手段）
            do {
                let metadata = try await loadMetadataWithFullDecoding(from: fileURL)
                logger.info("✅ 完整解码重试成功: \(fileURL.lastPathComponent)")
                return metadata
            } catch {
                logger.warning("⚠️ 完整解码也失败: \(error.localizedDescription)")
            }

            logger.warning("🔄 所有解码方式都失败，生成降级元数据: \(fileURL.lastPathComponent)")

            // 🚀 第四优先级：降级处理 - 从文件名和属性生成基本元数据
            return try await generateFallbackMetadata(from: fileURL)
        }
    }

    /// 尝试完整文件解码（最后手段）
    private func loadMetadataWithFullDecoding(from fileURL: URL) async throws -> CrossDeviceBackupMetadata {
        logger.info("🔄 尝试完整文件解码: \(fileURL.lastPathComponent)")
        
        return try await Task.detached(priority: .userInitiated) {
            // 读取完整文件数据
            let data = try Data(contentsOf: fileURL)
            
            await MainActor.run {
                self.logger.info("📦 完整文件读取成功，大小: \(data.count) bytes")
            }
            
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            // 尝试解码为 CrossDeviceBackupFile
            do {
                let backupFile = try decoder.decode(CrossDeviceBackupFile.self, from: data)
                await MainActor.run {
                    self.logger.info("✅ 完整解码为 CrossDeviceBackupFile 成功")
                }
                return backupFile.metadata
            } catch {
                await MainActor.run {
                    self.logger.warning("⚠️ CrossDeviceBackupFile 解码失败: \(error.localizedDescription)")
                }
            }
            
            // 尝试旧版本格式
            if let legacyMetadata = try await self.tryLegacyFormats(data: data, fileURL: fileURL) {
                await MainActor.run {
                    self.logger.info("✅ 旧版本格式解码成功")
                }
                return legacyMetadata
            }
            
            await MainActor.run {
                self.logger.error("❌ 所有解码方式都失败")
            }
            throw iCloudDocumentsBackupError.decodingFailed
        }.value
    }
    
    /// 生成降级元数据（从文件名和文件属性提取信息）
    private func generateFallbackMetadata(from fileURL: URL) async throws -> CrossDeviceBackupMetadata {
        logger.info("📝 尝试从文件名生成降级元数据: \(fileURL.lastPathComponent)")

        // 获取文件属性
        let resourceValues = try fileURL.resourceValues(forKeys: [
            .fileSizeKey,
            .contentModificationDateKey,
            .creationDateKey
        ])

        let fileSize = Int64(resourceValues.fileSize ?? 0)
        let modificationDate = resourceValues.contentModificationDate ?? Date()
        let creationDate = resourceValues.creationDate ?? modificationDate

        // 解析文件名：PinkLog_iPhone_20250630_112528_full.pinklogbackup
        let fileName = fileURL.deletingPathExtension().lastPathComponent
        let components = fileName.components(separatedBy: "_")

        var deviceName = "未知设备"
        var createdAt = creationDate
        var backupType: CrossDeviceBackupMetadata.BackupType = .full

        if components.count >= 4 {
            // 提取设备名（可能包含多个部分）
            let deviceComponents = Array(components[1..<components.count-3])
            if !deviceComponents.isEmpty {
                deviceName = deviceComponents.joined(separator: "_")
            }

            // 提取日期和时间
            let dateString = components[components.count-3]
            let timeString = components[components.count-2]
            let typeString = components[components.count-1]

            // 解析备份类型
            if let type = CrossDeviceBackupMetadata.BackupType(rawValue: typeString) {
                backupType = type
            }

            // 解析创建时间
            let dateTimeString = "\(dateString)_\(timeString)"
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyyMMdd_HHmmss"
            if let parsedDate = formatter.date(from: dateTimeString) {
                createdAt = parsedDate
            }
        }

        // 生成设备信息
        let deviceInfo = await getDeviceInfo()

        // 创建基于文件名的确定性UUID（这样删除时能够匹配）
        let deterministicUUID = generateDeterministicUUID(from: fileURL)

        // 创建降级元数据
        let metadata = CrossDeviceBackupMetadata(
            backupId: deterministicUUID,
            deviceName: deviceName,
            deviceModel: "未知型号",
            systemVersion: "未知版本",
            appVersion: "未知版本",
            deviceIdentifier: deviceInfo.identifier, // 使用当前设备标识符作为默认值
            createdAt: createdAt,
            backupType: backupType,
            totalEntities: 0, // 无法从文件名获取，设为0
            fileSize: fileSize,
            dataChecksum: "未知", // 无法计算，设为未知
            description: "从文件名恢复的元数据"
        )

        logger.info("✅ 降级元数据生成成功: 设备=\(deviceName), 时间=\(createdAt), 类型=\(backupType.rawValue), ID=\(deterministicUUID)")
        return metadata
    }

    /// 基于文件URL生成确定性UUID
    private func generateDeterministicUUID(from fileURL: URL) -> UUID {
        // 使用文件名和文件大小生成确定性的UUID
        let fileName = fileURL.lastPathComponent
        let fileSize = (try? fileURL.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0

        // 创建一个基于文件名和大小的字符串
        let seedString = "\(fileName)_\(fileSize)"

        // 使用SHA256哈希生成确定性的UUID
        let data = seedString.data(using: .utf8) ?? Data()
        let hash = SHA256.hash(data: data)

        // 从哈希的前16字节创建UUID
        let uuidBytes = Array(hash.prefix(16))
        let uuid = UUID(uuid: (
            uuidBytes[0], uuidBytes[1], uuidBytes[2], uuidBytes[3],
            uuidBytes[4], uuidBytes[5], uuidBytes[6], uuidBytes[7],
            uuidBytes[8], uuidBytes[9], uuidBytes[10], uuidBytes[11],
            uuidBytes[12], uuidBytes[13], uuidBytes[14], uuidBytes[15]
        ))

        return uuid
    }

    /// 尝试使用旧版本格式解码
    private func tryLegacyFormats(data: Data, fileURL: URL) async throws -> CrossDeviceBackupMetadata? {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        // 尝试解码为纯 BackupDataContainer（可能是早期版本直接保存的格式）
        do {
            let container = try decoder.decode(BackupDataContainer.self, from: data)
            logger.info("📦 检测到旧格式：直接保存的BackupDataContainer")
            return try await convertLegacyContainerToMetadata(container, fileURL: fileURL)
        } catch {
            logger.debug("❌ 不是BackupDataContainer格式: \(error.localizedDescription)")
        }

        // 尝试其他可能的旧格式
        // 例如：可能存在的简化版本的CrossDeviceBackupFile
        do {
            // 尝试解码为只包含基本字段的简化版本
            let simplifiedFile = try decoder.decode(LegacyCrossDeviceBackupFile.self, from: data)
            logger.info("📦 检测到旧格式：简化版CrossDeviceBackupFile")
            return await convertLegacyFileToMetadata(simplifiedFile, fileURL: fileURL)
        } catch {
            logger.debug("❌ 不是简化版CrossDeviceBackupFile格式: \(error.localizedDescription)")
        }

        return nil
    }

    /// 将旧版本的BackupDataContainer转换为元数据
    private func convertLegacyContainerToMetadata(_ container: BackupDataContainer, fileURL: URL) async throws -> CrossDeviceBackupMetadata {
        logger.info("🔄 转换旧版本BackupDataContainer为元数据")

        // 获取文件属性
        let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
        let fileSize = Int64(resourceValues.fileSize ?? 0)

        // 从文件名解析基本信息
        let fileName = fileURL.deletingPathExtension().lastPathComponent
        let components = fileName.components(separatedBy: "_")

        var deviceName = "未知设备"
        var backupType: CrossDeviceBackupMetadata.BackupType = .full

        // 尝试从文件名提取更多信息
        if components.count >= 4 {
            let deviceComponents = Array(components[1..<components.count-3])
            if !deviceComponents.isEmpty {
                deviceName = deviceComponents.joined(separator: "_")
            }

            let typeString = components[components.count-1]
            if let type = CrossDeviceBackupMetadata.BackupType(rawValue: typeString) {
                backupType = type
            }
        }

        return CrossDeviceBackupMetadata(
            backupId: UUID(), // 生成新的UUID
            deviceName: deviceName,
            deviceModel: container.deviceInfo.deviceModel,
            systemVersion: container.deviceInfo.systemVersion,
            appVersion: container.deviceInfo.appVersion,
            deviceIdentifier: container.deviceInfo.deviceId,
            createdAt: container.createdAt,
            backupType: backupType,
            totalEntities: container.totalEntities,
            fileSize: fileSize,
            dataChecksum: container.dataChecksum,
            description: "从旧格式转换的备份"
        )
    }

    /// 将简化版旧文件转换为元数据
    private func convertLegacyFileToMetadata(_ legacyFile: LegacyCrossDeviceBackupFile, fileURL: URL) async -> CrossDeviceBackupMetadata {
        logger.info("🔄 转换简化版旧文件为元数据")

        // 获取文件属性
        let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey])
        let fileSize = Int64(resourceValues?.fileSize ?? 0)

        return CrossDeviceBackupMetadata(
            backupId: legacyFile.backupId ?? UUID(),
            deviceName: legacyFile.deviceName ?? "未知设备",
            deviceModel: legacyFile.deviceModel ?? "未知型号",
            systemVersion: legacyFile.systemVersion ?? "未知版本",
            appVersion: legacyFile.appVersion ?? "未知版本",
            deviceIdentifier: legacyFile.deviceIdentifier ?? UUID().uuidString,
            createdAt: legacyFile.createdAt ?? Date(),
            backupType: legacyFile.backupType ?? .full,
            totalEntities: legacyFile.totalEntities ?? 0,
            fileSize: fileSize,
            dataChecksum: legacyFile.dataChecksum ?? "未知",
            description: legacyFile.description
        )
    }

    /// 使用流式读取提取元数据（避免加载整个文件到内存）- 后台线程优化
    private func extractMetadataWithStreamReading(from fileURL: URL) async throws -> CrossDeviceBackupMetadata? {
        logger.info("🚀 开始流式读取元数据: \(fileURL.lastPathComponent)")

        // 🚀 关键优化：将文件I/O操作移到后台线程，避免主线程阻塞
        return try await Task.detached(priority: .userInitiated) {
            // 获取文件大小
            let fileAttributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
            let fileSize = fileAttributes[.size] as? Int ?? 0

            await MainActor.run {
                self.logger.info("📏 文件大小: \(fileSize) bytes")
            }

            // 使用FileHandle进行流式读取，避免一次性加载整个文件
            let fileHandle = try FileHandle(forReadingFrom: fileURL)
            defer {
                try? fileHandle.close()
            }

            // 🚀 真正的最佳实践：并行读取开头和末尾4KB
            await MainActor.run {
                self.logger.info("🎯 采用真正的最佳实践：并行读取开头和末尾4KB")
            }

            return try await self.readMetadataParallel(fileHandle: fileHandle, fileSize: fileSize)
        }.value
    }

    /// 尝试只提取元数据，避免加载完整备份数据（内存优化）
    private func extractMetadataOnly(from data: Data) throws -> CrossDeviceBackupMetadata? {
        logger.info("🚀 尝试快速提取元数据，数据大小: \(data.count) bytes")

        // 先尝试流式解析，只读取文件开头的metadata部分
        if let metadata = try extractMetadataFromPartialData(data) {
            logger.info("✅ 流式元数据提取成功")
            return metadata
        }

        logger.info("🔄 流式提取失败，尝试完整JSON解析...")

        // 回退到完整JSON解析
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            logger.warning("❌ JSON解析失败：不是有效的JSON对象")
            return nil
        }

        guard let metadataDict = json["metadata"] as? [String: Any] else {
            logger.warning("❌ 未找到metadata字段")
            return nil
        }

        logger.info("✅ 找到metadata字段，开始解析...")

        // 手动构建元数据对象，避免解码整个文件
        guard let backupIdString = metadataDict["backupId"] as? String,
              let backupId = UUID(uuidString: backupIdString) else {
            logger.warning("❌ backupId解析失败")
            return nil
        }

        guard let deviceName = metadataDict["deviceName"] as? String,
              let deviceModel = metadataDict["deviceModel"] as? String,
              let systemVersion = metadataDict["systemVersion"] as? String,
              let appVersion = metadataDict["appVersion"] as? String,
              let deviceIdentifier = metadataDict["deviceIdentifier"] as? String,
              let createdAtString = metadataDict["createdAt"] as? String,
              let backupTypeString = metadataDict["backupType"] as? String,
              let totalEntities = metadataDict["totalEntities"] as? Int,
              let fileSize = metadataDict["fileSize"] as? Int64,
              let dataChecksum = metadataDict["dataChecksum"] as? String else {
            logger.warning("❌ 必需字段解析失败")
            return nil
        }

        // 解析日期
        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            logger.warning("❌ 日期解析失败: \(createdAtString)")
            return nil
        }

        // 解析备份类型
        let backupType = CrossDeviceBackupMetadata.BackupType(rawValue: backupTypeString) ?? .full

        // 获取描述（可选）
        let description = metadataDict["description"] as? String

        logger.info("✅ 快速元数据提取成功: \(backupId)")

        return CrossDeviceBackupMetadata(
            backupId: backupId,
            deviceName: deviceName,
            deviceModel: deviceModel,
            systemVersion: systemVersion,
            appVersion: appVersion,
            deviceIdentifier: deviceIdentifier,
            createdAt: createdAt,
            backupType: backupType,
            totalEntities: totalEntities,
            fileSize: fileSize,
            dataChecksum: dataChecksum,
            description: description
        )
    }

    /// 从部分数据中提取元数据（流式解析）
    private func extractMetadataFromPartialData(_ data: Data) throws -> CrossDeviceBackupMetadata? {
        // 首先尝试从文件开头搜索
        if let metadata = try extractMetadataFromStart(data) {
            return metadata
        }

        // 如果开头没找到，尝试从文件末尾搜索
        logger.info("🔄 开头未找到metadata，尝试从文件末尾搜索...")
        return try extractMetadataFromEnd(data)
    }

    /// 从文件开头提取元数据（优化版本）
    private func extractMetadataFromStart(_ data: Data) throws -> CrossDeviceBackupMetadata? {
        // 优化策略：使用智能读取大小，基于文件大小动态调整
        let optimalReadSize = calculateOptimalReadSize(for: data.count, position: .start)
        let partialData = data.prefix(optimalReadSize)

        logger.info("🔍 智能流式解析（开头），读取前 \(optimalReadSize) bytes")

        if let metadata = try parsePartialData(partialData, position: "开头") {
            logger.info("✅ 从文件开头流式解析成功，使用 \(optimalReadSize) bytes")
            return metadata
        }

        // 如果智能读取失败，尝试一次更大的读取
        if optimalReadSize < 262144 && data.count > optimalReadSize {
            let fallbackSize = min(262144, data.count)
            let fallbackData = data.prefix(fallbackSize)

            logger.info("🔄 开头智能解析失败，尝试回退读取 \(fallbackSize) bytes")

            if let metadata = try parsePartialData(fallbackData, position: "开头") {
                logger.info("✅ 开头回退解析成功")
                return metadata
            }
        }

        return nil
    }

    /// 从文件末尾提取元数据（优化版本）
    private func extractMetadataFromEnd(_ data: Data) throws -> CrossDeviceBackupMetadata? {
        // 优化策略：使用智能读取大小，基于文件大小动态调整
        let optimalReadSize = calculateOptimalReadSize(for: data.count, position: .end)
        let startIndex = max(0, data.count - optimalReadSize)
        let partialData = data.suffix(from: startIndex)

        logger.info("🔍 智能流式解析（末尾），读取后 \(optimalReadSize) bytes")

        if let metadata = try parsePartialData(partialData, position: "末尾") {
            logger.info("✅ 从文件末尾流式解析成功，使用 \(optimalReadSize) bytes")
            return metadata
        }

        // 如果智能读取失败，尝试一次更大的读取
        if optimalReadSize < 262144 && data.count > optimalReadSize {
            let fallbackSize = min(262144, data.count)
            let fallbackStartIndex = max(0, data.count - fallbackSize)
            let fallbackData = data.suffix(from: fallbackStartIndex)

            logger.info("🔄 末尾智能解析失败，尝试回退读取 \(fallbackSize) bytes")

            if let metadata = try parsePartialData(fallbackData, position: "末尾") {
                logger.info("✅ 末尾回退解析成功")
                return metadata
            }
        }

        logger.warning("❌ 所有流式解析尝试都失败")
        return nil
    }



    /// 并行读取开头和末尾metadata（真正的最佳实践）
    private func readMetadataParallel(fileHandle: FileHandle, fileSize: Int) async throws -> CrossDeviceBackupMetadata? {
        let readSize = 4096 // 固定4KB，足够大多数metadata

        // 并行读取开头和末尾
        async let startResult = readMetadataFromPosition(fileHandle: fileHandle, offset: 0, size: readSize, position: "开头")
        async let endResult = readMetadataFromPosition(fileHandle: fileHandle, offset: max(0, fileSize - readSize), size: readSize, position: "末尾")

        // 等待任一结果成功
        let results = try await [startResult, endResult]

        for result in results {
            if let metadata = result {
                return metadata
            }
        }

        logger.warning("❌ 并行读取开头和末尾都失败")
        return nil
    }

    /// 从指定位置读取metadata
    private func readMetadataFromPosition(fileHandle: FileHandle, offset: Int, size: Int, position: String) async throws -> CrossDeviceBackupMetadata? {
        logger.info("🔍 并行读取: 从\(position)读取 \(size) bytes")

        // 移动文件指针
        try fileHandle.seek(toOffset: UInt64(offset))

        // 读取数据
        let data = try fileHandle.read(upToCount: size) ?? Data()

        // 立即释放缓冲区
        await Task.yield()

        // 尝试解析metadata
        if let metadata = try parsePartialDataForMetadata(data, position: "\(position)-\(size)B") {
            logger.info("✅ 并行读取成功！从\(position)用 \(size) bytes 找到metadata")
            return metadata
        }

        return nil
    }

    /// 解析部分数据以提取元数据
    private func parsePartialDataForMetadata(_ data: Data, position: String) throws -> CrossDeviceBackupMetadata? {
        // 转换为字符串
        guard let jsonString = String(data: data, encoding: .utf8) else {
            logger.warning("❌ 无法转换为UTF-8字符串（\(position)）")
            return nil
        }

        // 查找metadata字段
        guard let metadataStart = jsonString.range(of: "\"metadata\"") else {
            logger.warning("❌ 在\(position)\(data.count)字节中未找到metadata字段")
            return nil
        }

        // 提取metadata对象
        return try extractMetadataObject(from: jsonString, startRange: metadataStart, position: position)
    }

    /// 从JSON字符串中提取metadata对象
    private func extractMetadataObject(from jsonString: String, startRange: Range<String.Index>, position: String) throws -> CrossDeviceBackupMetadata? {
        // 从metadata开始位置查找完整的metadata对象
        let metadataStartIndex = startRange.upperBound
        let remainingString = String(jsonString[metadataStartIndex...])

        // 查找metadata对象的开始
        guard let objectStart = remainingString.range(of: "{") else {
            logger.warning("❌ 未找到metadata对象开始（\(position)）")
            return nil
        }

        // 找到完整的JSON对象（处理嵌套的大括号）
        let objectStartIndex = objectStart.lowerBound
        let objectString = String(remainingString[objectStartIndex...])

        guard let metadataJsonString = extractCompleteJsonObject(from: objectString) else {
            logger.warning("❌ 无法提取完整的metadata JSON对象（\(position)）")
            return nil
        }

        // 解析metadata JSON
        guard let metadataData = metadataJsonString.data(using: .utf8) else {
            logger.warning("❌ 无法转换metadata为Data（\(position)）")
            return nil
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let metadata = try decoder.decode(CrossDeviceBackupMetadata.self, from: metadataData)

            logger.info("✅ 提取到metadata JSON（\(position)），长度: \(metadataJsonString.count) 字符")
            return metadata
        } catch {
            logger.warning("❌ 解析metadata JSON失败（\(position)）: \(error.localizedDescription)")
            return nil
        }
    }

    /// 提取完整的JSON对象（处理嵌套大括号）
    private func extractCompleteJsonObject(from jsonString: String) -> String? {
        var braceCount = 0
        var startIndex: String.Index?
        var endIndex: String.Index?

        for (index, char) in jsonString.enumerated() {
            let stringIndex = jsonString.index(jsonString.startIndex, offsetBy: index)

            if char == "{" {
                if braceCount == 0 {
                    startIndex = stringIndex
                }
                braceCount += 1
            } else if char == "}" {
                braceCount -= 1
                if braceCount == 0 && startIndex != nil {
                    endIndex = jsonString.index(after: stringIndex)
                    break
                }
            }
        }

        guard let start = startIndex, let end = endIndex else {
            return nil
        }

        return String(jsonString[start..<end])
    }

    /// 元数据位置枚举
    private enum MetadataPosition {
        case start
        case end
    }



    /// 计算最优读取大小（废弃的低效方法，保留兼容性）
    private func calculateOptimalReadSize(for fileSize: Int, position: MetadataPosition) -> Int {
        // 新策略：总是从最小的4KB开始
        return 4096
    }

    /// 解析部分数据
    private func parsePartialData(_ partialData: Data, position: String) throws -> CrossDeviceBackupMetadata? {
        // 尝试不同的编码方式
        var jsonString: String?

        // 首先尝试UTF-8
        if let utf8String = String(data: partialData, encoding: .utf8) {
            jsonString = utf8String
        }
        // 如果UTF-8失败，尝试ASCII
        else if let asciiString = String(data: partialData, encoding: .ascii) {
            jsonString = asciiString
            logger.info("ℹ️ 使用ASCII编码解析（\(position)）")
        }
        // 如果ASCII也失败，尝试Latin1
        else if let latin1String = String(data: partialData, encoding: .isoLatin1) {
            jsonString = latin1String
            logger.info("ℹ️ 使用Latin1编码解析（\(position)）")
        }

        guard let validJsonString = jsonString else {
            logger.warning("❌ 无法转换为字符串（\(position)）")
            return nil
        }

        // 查找metadata字段的开始位置
        guard let metadataStart = validJsonString.range(of: "\"metadata\"") else {
            logger.warning("❌ 在\(position)\(partialData.count)字节中未找到metadata字段")
            return nil
        }

        // 从metadata开始位置查找完整的metadata对象
        let metadataStartIndex = metadataStart.upperBound
        let remainingString = String(validJsonString[metadataStartIndex...])

        // 查找metadata对象的开始和结束
        guard let objectStart = remainingString.range(of: "{") else {
            logger.warning("❌ 未找到metadata对象开始（\(position)）")
            return nil
        }

        // 简单的括号匹配来找到metadata对象的结束
        var braceCount = 0
        var objectEnd: String.Index?
        let startIndex = objectStart.lowerBound

        for (index, char) in remainingString[startIndex...].enumerated() {
            if char == "{" {
                braceCount += 1
            } else if char == "}" {
                braceCount -= 1
                if braceCount == 0 {
                    objectEnd = remainingString.index(startIndex, offsetBy: index + 1)
                    break
                }
            }
        }

        guard let endIndex = objectEnd else {
            logger.warning("❌ 未找到metadata对象结束（\(position)）")
            return nil
        }

        let metadataJsonString = String(remainingString[startIndex..<endIndex])
        logger.info("✅ 提取到metadata JSON（\(position)），长度: \(metadataJsonString.count) 字符")

        // 解析提取的metadata JSON
        guard let metadataData = metadataJsonString.data(using: .utf8),
              let metadataDict = try JSONSerialization.jsonObject(with: metadataData) as? [String: Any] else {
            logger.warning("❌ metadata JSON解析失败（\(position)）")
            return nil
        }

        // 使用相同的解析逻辑
        return try parseMetadataDict(metadataDict)
    }

    /// 解析metadata字典
    private func parseMetadataDict(_ metadataDict: [String: Any]) throws -> CrossDeviceBackupMetadata? {
        guard let backupIdString = metadataDict["backupId"] as? String,
              let backupId = UUID(uuidString: backupIdString),
              let deviceName = metadataDict["deviceName"] as? String,
              let deviceModel = metadataDict["deviceModel"] as? String,
              let systemVersion = metadataDict["systemVersion"] as? String,
              let appVersion = metadataDict["appVersion"] as? String,
              let deviceIdentifier = metadataDict["deviceIdentifier"] as? String,
              let createdAtString = metadataDict["createdAt"] as? String,
              let backupTypeString = metadataDict["backupType"] as? String,
              let totalEntities = metadataDict["totalEntities"] as? Int,
              let fileSize = metadataDict["fileSize"] as? Int64,
              let dataChecksum = metadataDict["dataChecksum"] as? String else {
            return nil
        }

        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            return nil
        }

        let backupType = CrossDeviceBackupMetadata.BackupType(rawValue: backupTypeString) ?? .full
        let description = metadataDict["description"] as? String

        return CrossDeviceBackupMetadata(
            backupId: backupId,
            deviceName: deviceName,
            deviceModel: deviceModel,
            systemVersion: systemVersion,
            appVersion: appVersion,
            deviceIdentifier: deviceIdentifier,
            createdAt: createdAt,
            backupType: backupType,
            totalEntities: totalEntities,
            fileSize: fileSize,
            dataChecksum: dataChecksum,
            description: description
        )
    }

    /// 获取设备信息
    private func getDeviceInfo() async -> (name: String, model: String, systemVersion: String, appVersion: String, identifier: String) {
        let device = UIDevice.current
        
        // 设备名称
        let deviceName = device.name
        
        // 设备型号
        let deviceModel = device.model
        
        // 系统版本
        let systemVersion = device.systemVersion
        
        // 应用版本
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知"
        
        // 设备标识符 (使用vendorIdentifier)
        let deviceIdentifier = device.identifierForVendor?.uuidString ?? "未知"
        
        return (
            name: deviceName,
            model: deviceModel,
            systemVersion: systemVersion,
            appVersion: appVersion,
            identifier: deviceIdentifier
        )
    }
}

// MARK: - 扩展功能

extension iCloudDocumentsBackupService {
    
    /// 获取指定设备的备份文件
    func getBackupsForDevice(_ deviceIdentifier: String) -> [CrossDeviceBackupMetadata] {
        return availableBackups.filter { $0.deviceIdentifier == deviceIdentifier }
    }
    
    /// 获取所有设备列表
    func getAllDevices() -> [(deviceName: String, deviceIdentifier: String, backupCount: Int)] {
        let grouped = Dictionary(grouping: availableBackups) { $0.deviceIdentifier }
        return grouped.map { (identifier, backups) in
            let deviceName = backups.first?.deviceName ?? "未知设备"
            return (deviceName: deviceName, deviceIdentifier: identifier, backupCount: backups.count)
        }.sorted { $0.deviceName < $1.deviceName }
    }
    
    /// 清理旧的备份文件（保留最新的N个）
    func cleanupOldBackups(keepLatest: Int = 10) async throws {
        logger.info("🧹 清理旧备份文件，保留最新 \(keepLatest) 个...")
        
        let sortedBackups = availableBackups.sorted { $0.createdAt > $1.createdAt }
        let backupsToDelete = Array(sortedBackups.dropFirst(keepLatest))
        
        for backup in backupsToDelete {
            do {
                try await deleteBackup(with: backup.backupId)
                logger.info("✅ 删除旧备份: \(backup.deviceName) - \(backup.formattedCreatedAt)")
            } catch {
                logger.warning("⚠️ 删除旧备份失败: \(backup.backupId), 错误: \(error.localizedDescription)")
            }
        }
        
        logger.info("✅ 清理完成，删除了 \(backupsToDelete.count) 个旧备份")
    }
    
    /// 导出备份文件到系统文件应用
    func exportBackupToFiles(with backupId: UUID) async throws -> URL {
        let backupFile = try await loadBackup(with: backupId)
        
        // 获取临时目录
        let tempDir = FileManager.default.temporaryDirectory
        let exportURL = tempDir.appendingPathComponent(backupFile.fileName)
        
        // 编码并保存
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try encoder.encode(backupFile)
        try data.write(to: exportURL)
        
        logger.info("✅ 备份文件导出到临时目录: \(exportURL.path)")
        return exportURL
    }
    
    /// 启动定期刷新
    func startPeriodicRefresh() {
        stopPeriodicRefresh()

        // 24小时 = 24 * 60 * 60 = 86400秒
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 86400.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.scanAvailableBackups()
            }
        }
        logger.info("✅ 启动定期刷新，间隔24小时")
    }
    
    /// 停止定期刷新
    func stopPeriodicRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
    
    /// 手动刷新备份列表
    func refreshBackups() async {
        logger.info("🔄 手动刷新备份列表...")
        await scanAvailableBackups()
    }



    /// 智能加载所有独立metadata文件（新优化方法）
    private func loadAllIndependentMetadata(in backupDirectoryURL: URL) async -> [CrossDeviceBackupMetadata] {
        return await Task.detached(priority: .userInitiated) {
            do {
                // 扫描所有 .metadata.json 文件
                let fileURLs = try FileManager.default.contentsOfDirectory(
                    at: backupDirectoryURL,
                    includingPropertiesForKeys: [.contentModificationDateKey],
                    options: [.skipsHiddenFiles]
                )
                
                let metadataFileURLs = fileURLs.filter { $0.pathExtension == "json" && $0.lastPathComponent.contains(".metadata.") }
                
                if metadataFileURLs.isEmpty {
                    await MainActor.run {
                        self.logger.info("📋 未找到独立metadata文件")
                    }
                    return []
                }
                
                await MainActor.run {
                    self.logger.info("🔍 发现 \(metadataFileURLs.count) 个独立metadata文件")
                }
                
                var metadataList: [CrossDeviceBackupMetadata] = []
                
                for metadataURL in metadataFileURLs {
                    do {
                        let metadataData = try Data(contentsOf: metadataURL)
                        let decoder = JSONDecoder()
                        decoder.dateDecodingStrategy = .iso8601
                        let metadata = try decoder.decode(CrossDeviceBackupMetadata.self, from: metadataData)
                        
                        // 验证对应的备份文件是否存在
                        let backupFileName = metadataURL.lastPathComponent.replacingOccurrences(of: ".metadata.json", with: ".pinklogbackup")
                        let backupFileURL = backupDirectoryURL.appendingPathComponent(backupFileName)
                        
                        if FileManager.default.fileExists(atPath: backupFileURL.path) {
                            metadataList.append(metadata)
                        } else {
                            await MainActor.run {
                                self.logger.warning("⚠️ metadata文件存在但备份文件缺失，正在清理孤立文件: \(backupFileName)")
                            }
                            
                            // 🚀 自动清理孤立的metadata文件
                            do {
                                try FileManager.default.removeItem(at: metadataURL)
                                await MainActor.run {
                                    self.logger.info("✅ 孤立metadata文件已清理: \(metadataURL.lastPathComponent)")
                                }
                            } catch {
                                await MainActor.run {
                                    self.logger.error("❌ 清理孤立metadata文件失败: \(metadataURL.lastPathComponent), 错误: \(error.localizedDescription)")
                                }
                            }
                        }
                        
                    } catch {
                        await MainActor.run {
                            self.logger.warning("⚠️ 加载metadata文件失败: \(metadataURL.lastPathComponent), 错误: \(error.localizedDescription)")
                        }
                    }
                }
                
                // 按创建时间倒序排列
                metadataList.sort { $0.createdAt > $1.createdAt }
                
                await MainActor.run {
                    self.logger.info("✅ 成功加载 \(metadataList.count) 个有效的独立metadata")
                }
                
                return metadataList
                
            } catch {
                await MainActor.run {
                    self.logger.error("❌ 扫描独立metadata文件失败: \(error.localizedDescription)")
                }
                return []
            }
        }.value
    }

    /// 从独立metadata文件快速加载（后台线程优化）
    private func loadMetadataFromFile(backupFileName: String) async -> CrossDeviceBackupMetadata? {
        // 🚀 将文件I/O操作移到后台线程
        return await Task.detached(priority: .userInitiated) {
            do {
                let backupDirectoryURL = try await self.getBackupDirectoryURL()
                let metadataFileName = backupFileName.replacingOccurrences(of: ".pinklogbackup", with: ".metadata.json")
                let metadataURL = backupDirectoryURL.appendingPathComponent(metadataFileName)

                guard FileManager.default.fileExists(atPath: metadataURL.path) else {
                    return nil
                }

                let metadataData = try Data(contentsOf: metadataURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let metadata = try decoder.decode(CrossDeviceBackupMetadata.self, from: metadataData)

                await MainActor.run {
                    self.logger.info("✅ 从独立文件加载metadata: \(metadataFileName)")
                }
                return metadata

            } catch {
                await MainActor.run {
                    self.logger.warning("⚠️ 从独立文件加载metadata失败: \(error.localizedDescription)")
                }
                return nil
            }
        }.value
    }

    // MARK: - 内存管理方法

    /// 注册到全局缓存管理器（替代独立定时器）
    private func registerCacheCleanup() {
        GlobalCacheManager.shared.registerCache(
            name: "iCloudDocumentsBackupService",
            priority: .high  // 备份服务使用高优先级，因为内存敏感
        ) { [weak self] in
            Task { @MainActor in
                await self?.cleanupExpiredCache()
            }
        }
        logger.info("📝 已注册到全局缓存管理器: iCloudDocumentsBackupService")
    }

    /// 停止缓存清理定时器
    private func stopCacheCleanupTimer() {
        cacheCleanupTimer?.invalidate()
        cacheCleanupTimer = nil
    }

    /// 清理过期的缓存
    private func cleanupExpiredCache() async {
        let now = Date()
        var expiredKeys: [UUID] = []

        for (backupId, lastAccess) in lastAccessTimes {
            if now.timeIntervalSince(lastAccess) > cacheTimeout {
                expiredKeys.append(backupId)
            }
        }

        if !expiredKeys.isEmpty {
            for key in expiredKeys {
                cachedBackupFiles.removeValue(forKey: key)
                lastAccessTimes.removeValue(forKey: key)
            }

            logger.info("🧹 清理了 \(expiredKeys.count) 个过期的备份文件缓存")
            MemoryMonitor.shared.recordMemoryUsage("清理过期备份缓存后")
        }
    }

    /// 检查文件的iCloud同步状态
    private func checkiCloudSyncStatus(for fileURL: URL) async -> String {
        return await Task.detached(priority: .userInitiated) {
            // 首先检查文件是否实际存在
            if FileManager.default.fileExists(atPath: fileURL.path) {
                return "本地文件"
            }
            
            // 如果文件不存在，可能是iCloud占位符
            do {
                // 尝试获取iCloud状态
                let resourceValues = try fileURL.resourceValues(forKeys: [.isUbiquitousItemKey])
                if let isUbiquitous = resourceValues.isUbiquitousItem, isUbiquitous {
                    return "iCloud占位符文件"
                } else {
                    return "文件不存在"
                }
            } catch {
                return "状态检查失败: \(error.localizedDescription)"
            }
        }.value
    }
    


    /// 强制清理所有缓存（用于内存压力时）
    func clearAllCache() async {
        let cacheCount = cachedBackupFiles.count
        cachedBackupFiles.removeAll()
        lastAccessTimes.removeAll()

        if cacheCount > 0 {
            logger.info("🧹 强制清理了所有备份文件缓存，共 \(cacheCount) 个")
            MemoryMonitor.shared.recordMemoryUsage("强制清理所有备份缓存后")
        }
    }

    /// 清理特定备份的缓存
    func clearCache(for backupId: UUID) {
        if cachedBackupFiles.removeValue(forKey: backupId) != nil {
            lastAccessTimes.removeValue(forKey: backupId)
            logger.info("🧹 清理了备份文件缓存: \(backupId)")
            MemoryMonitor.shared.recordMemoryUsage("清理特定备份缓存后")
        }
    }

    /// 获取当前缓存状态
    func getCacheStatus() -> (count: Int, totalSizeMB: Double) {
        let count = cachedBackupFiles.count
        // 估算内存使用（这是一个粗略估算）
        let estimatedSizeMB = Double(count) * 50.0 // 假设每个备份文件平均50MB
        return (count, estimatedSizeMB)
    }
}

// MARK: - 内存优化扩展

extension MemoryMonitor {
    /// 异步监控内存使用的便利方法
    func monitorMemoryUsage<T>(_ context: String, block: () async throws -> T) async rethrows -> T {
        recordMemoryUsage("\(context) - 开始")
        let result = try await block()
        recordMemoryUsage("\(context) - 结束")
        return result
    }
}