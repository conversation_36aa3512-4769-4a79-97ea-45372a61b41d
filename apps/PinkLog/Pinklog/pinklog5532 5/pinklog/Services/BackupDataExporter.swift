import Foundation
import CoreData
import Combine

// MARK: - 导出错误类型

/// 数据导出错误类型
enum DataExportError: Error, LocalizedError {
    case coreDataContextUnavailable
    case entityFetchFailed(String)
    case dataValidationFailed(String)
    case relationshipInconsistency(String)
    case binaryDataProcessingFailed(String)
    case exportCancelled
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .coreDataContextUnavailable:
            return "Core Data上下文不可用"
        case .entityFetchFailed(let entity):
            return "获取实体数据失败: \(entity)"
        case .dataValidationFailed(let reason):
            return "数据验证失败: \(reason)"
        case .relationshipInconsistency(let reason):
            return "关系一致性检查失败: \(reason)"
        case .binaryDataProcessingFailed(let reason):
            return "Binary数据处理失败: \(reason)"
        case .exportCancelled:
            return "导出操作已取消"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - 导出结果

/// 数据导出结果
struct DataExportResult {
    let success: Bool
    let container: BackupDataContainer?
    let error: DataExportError?
    let exportedEntities: Int
    let processingTime: TimeInterval
    let warnings: [String]
    
    static func success(_ container: BackupDataContainer, entities: Int, time: TimeInterval, warnings: [String] = []) -> DataExportResult {
        return DataExportResult(success: true, container: container, error: nil, exportedEntities: entities, processingTime: time, warnings: warnings)
    }
    
    static func failure(_ error: DataExportError, time: TimeInterval = 0) -> DataExportResult {
        return DataExportResult(success: false, container: nil, error: error, exportedEntities: 0, processingTime: time, warnings: [])
    }
}

// MARK: - 导出进度

/// 导出进度信息
struct ExportProgress {
    let currentStep: String
    let completedEntities: Int
    let totalEntities: Int
    let progress: Double
    let estimatedTimeRemaining: TimeInterval?
    
    var progressDescription: String {
        return "\(currentStep) (\(completedEntities)/\(totalEntities))"
    }
}

// MARK: - 数据导出引擎

/// 数据导出引擎 - 负责从Core Data提取数据并转换为备份格式
@MainActor
class BackupDataExporter: ObservableObject {
    
    // MARK: - 发布属性
    @Published var isExporting: Bool = false
    @Published var currentProgress: ExportProgress?
    @Published var lastError: DataExportError?
    
    // MARK: - 私有属性
    private let context: NSManagedObjectContext
    private var cancellationToken: Bool = false
    private let progressUpdateInterval: TimeInterval = 0.1
    
    // Repository实例
    private lazy var productRepository = ProductRepository(context: context)
    private lazy var usageRecordRepository = UsageRecordRepository(context: context)
    private lazy var relatedExpenseRepository = RelatedExpenseRepository(context: context)
    private lazy var categoryRepository = CategoryRepository(context: context)
    private lazy var tagRepository = TagRepository(context: context)
    private lazy var loanRecordRepository = LoanRecordRepository(context: context)
    private lazy var expenseTypeRepository = ExpenseTypeRepository(context: context)
    private lazy var purchaseChannelRepository = PurchaseChannelRepository(context: context)
    private lazy var reminderRepository = ReminderRepository(context: context)
    private lazy var productLinkRepository = ProductLinkRepository(context: context)
    private lazy var conversationRepository = ConversationRepository(context: context)
    private lazy var messageRepository = MessageRepository(context: context)
    
    // MARK: - 初始化
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - 公共方法
    
    /// 导出所有数据
    func exportAllData() async -> DataExportResult {
        let startTime = Date()
        
        guard !isExporting else {
            return .failure(.exportCancelled)
        }
        
        isExporting = true
        cancellationToken = false
        lastError = nil
        
        defer {
            isExporting = false
            currentProgress = nil
        }
        
        do {
            // 预计算总实体数量
            let totalEntities = try await calculateTotalEntities()
            var completedEntities = 0
            var warnings: [String] = []
            
            // 导出各类实体数据
            updateProgress("正在导出产品数据...", completedEntities, totalEntities)
            let products = try await exportProducts()
            completedEntities += products.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出使用记录...", completedEntities, totalEntities)
            let usageRecords = try await exportUsageRecords()
            completedEntities += usageRecords.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出费用记录...", completedEntities, totalEntities)
            let relatedExpenses = try await exportRelatedExpenses()
            completedEntities += relatedExpenses.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出分类数据...", completedEntities, totalEntities)
            let categories = try await exportCategories()
            completedEntities += categories.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出标签数据...", completedEntities, totalEntities)
            let tags = try await exportTags()
            completedEntities += tags.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出借阅记录...", completedEntities, totalEntities)
            let loanRecords = try await exportLoanRecords()
            completedEntities += loanRecords.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出费用类型...", completedEntities, totalEntities)
            let expenseTypes = try await exportExpenseTypes()
            completedEntities += expenseTypes.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出购买渠道分类...", completedEntities, totalEntities)
            let purchaseChannelCategories = try await exportPurchaseChannelCategories()
            completedEntities += purchaseChannelCategories.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出购买渠道...", completedEntities, totalEntities)
            let purchaseChannels = try await exportPurchaseChannels()
            completedEntities += purchaseChannels.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出提醒数据...", completedEntities, totalEntities)
            let reminders = try await exportReminders()
            completedEntities += reminders.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出产品关联...", completedEntities, totalEntities)
            let productLinks = try await exportProductLinks()
            completedEntities += productLinks.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出对话数据...", completedEntities, totalEntities)
            let conversations = try await exportConversations()
            completedEntities += conversations.count
            
            if cancellationToken { return .failure(.exportCancelled) }
            
            updateProgress("正在导出消息数据...", completedEntities, totalEntities)
            let messages = try await exportMessages()
            completedEntities += messages.count
            
            // 创建备份容器
            updateProgress("正在创建备份容器...", completedEntities, totalEntities)
            let container = BackupDataContainer(
                backupType: .full,
                products: products,
                usageRecords: usageRecords,
                relatedExpenses: relatedExpenses,
                categories: categories,
                tags: tags,
                loanRecords: loanRecords,
                expenseTypes: expenseTypes,
                purchaseChannels: purchaseChannels,
                purchaseChannelCategories: purchaseChannelCategories,
                reminders: reminders,
                productLinks: productLinks,
                conversations: conversations,
                messages: messages
            )
            
            // 验证导出数据
            updateProgress("正在验证数据完整性...", completedEntities, totalEntities)
            let validationResult = container.validateBackup()
            if !validationResult.isValid {
                let errorMessage = validationResult.errors.joined(separator: "; ")
                return .failure(.dataValidationFailed(errorMessage))
            }
            
            warnings.append(contentsOf: validationResult.warnings)
            
            let processingTime = Date().timeIntervalSince(startTime)
            return .success(container, entities: completedEntities, time: processingTime, warnings: warnings)
            
        } catch let error as DataExportError {
            let processingTime = Date().timeIntervalSince(startTime)
            lastError = error
            return .failure(error, time: processingTime)
        } catch {
            let processingTime = Date().timeIntervalSince(startTime)
            let exportError = DataExportError.unknownError(error)
            lastError = exportError
            return .failure(exportError, time: processingTime)
        }
    }
    
    /// 增量导出数据（自指定日期以来的变更）
    func exportIncrementalData(since date: Date) async -> DataExportResult {
        let startTime = Date()
        
        guard !isExporting else {
            return .failure(.exportCancelled)
        }
        
        isExporting = true
        cancellationToken = false
        lastError = nil
        
        defer {
            isExporting = false
            currentProgress = nil
        }
        
        do {
            // 注意：这里需要实际的lastModified字段支持
            // 当前实现为简化版本，实际应用中需要在Core Data模型中添加lastModified字段
            
            var warnings: [String] = []
            warnings.append("增量导出功能需要Core Data模型支持lastModified字段")
            
            // 暂时使用完整导出作为增量导出的实现
            // 在实际应用中，应该根据lastModified字段过滤数据
            let result = await exportAllData()
            
            if result.success, let container = result.container {
                // 创建增量备份容器
                let incrementalContainer = BackupDataContainer(
                    backupType: .incremental,
                    products: container.products,
                    usageRecords: container.usageRecords,
                    relatedExpenses: container.relatedExpenses,
                    categories: container.categories,
                    tags: container.tags,
                    loanRecords: container.loanRecords,
                    expenseTypes: container.expenseTypes,
                    purchaseChannels: container.purchaseChannels,
                    purchaseChannelCategories: container.purchaseChannelCategories,
                    reminders: container.reminders,
                    productLinks: container.productLinks,
                    conversations: container.conversations,
                    messages: container.messages
                )
                
                let processingTime = Date().timeIntervalSince(startTime)
                return .success(incrementalContainer, entities: result.exportedEntities, time: processingTime, warnings: warnings)
            } else {
                return result
            }
            
        } catch {
            let processingTime = Date().timeIntervalSince(startTime)
            let exportError = DataExportError.unknownError(error)
            lastError = exportError
            return .failure(exportError, time: processingTime)
        }
    }
    
    /// 取消导出操作
    func cancelExport() {
        cancellationToken = true
    }

    // MARK: - 私有导出方法

    /// 计算总实体数量
    private func calculateTotalEntities() async throws -> Int {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    var total = 0
                    total += try self.context.count(for: Product.fetchRequest())
                    total += try self.context.count(for: UsageRecord.fetchRequest())
                    total += try self.context.count(for: RelatedExpense.fetchRequest())
                    total += try self.context.count(for: Category.fetchRequest())
                    total += try self.context.count(for: Tag.fetchRequest())
                    total += try self.context.count(for: LoanRecord.fetchRequest())
                    total += try self.context.count(for: ExpenseType.fetchRequest())
                    total += try self.context.count(for: PurchaseChannel.fetchRequest())
                    total += try self.context.count(for: PurchaseChannelCategory.fetchRequest())
                    total += try self.context.count(for: Reminder.fetchRequest())
                    total += try self.context.count(for: ProductLink.fetchRequest())
                    total += try self.context.count(for: Conversation.fetchRequest())
                    total += try self.context.count(for: Message.fetchRequest())
                    continuation.resume(returning: total)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("计算实体总数失败"))
                }
            }
        }
    }

    /// 导出产品数据
    private func exportProducts() async throws -> [BackupProduct] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let products = self.productRepository.fetchAll()
                    let backupProducts = products.map { BackupProduct.fromCoreData($0) }
                    continuation.resume(returning: backupProducts)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Product"))
                }
            }
        }
    }

    /// 导出使用记录数据
    private func exportUsageRecords() async throws -> [BackupUsageRecord] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let records = self.usageRecordRepository.fetchAll()
                    let backupRecords = records.map { BackupUsageRecord.fromCoreData($0) }
                    continuation.resume(returning: backupRecords)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("UsageRecord"))
                }
            }
        }
    }

    /// 导出相关费用数据
    private func exportRelatedExpenses() async throws -> [BackupRelatedExpense] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let expenses = self.relatedExpenseRepository.fetchAll()
                    let backupExpenses = expenses.map { BackupRelatedExpense.fromCoreData($0) }
                    continuation.resume(returning: backupExpenses)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("RelatedExpense"))
                }
            }
        }
    }

    /// 导出分类数据
    private func exportCategories() async throws -> [BackupCategory] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let categories = self.categoryRepository.fetchAll()
                    let backupCategories = categories.map { BackupCategory.fromCoreData($0) }
                    continuation.resume(returning: backupCategories)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Category"))
                }
            }
        }
    }

    /// 导出标签数据
    private func exportTags() async throws -> [BackupTag] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let tags = self.tagRepository.fetchAll()
                    let backupTags = tags.map { BackupTag.fromCoreData($0) }
                    continuation.resume(returning: backupTags)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Tag"))
                }
            }
        }
    }

    /// 导出借阅记录数据
    private func exportLoanRecords() async throws -> [BackupLoanRecord] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let loans = self.loanRecordRepository.fetchAll()
                    let backupLoans = loans.map { BackupLoanRecord.fromCoreData($0) }
                    continuation.resume(returning: backupLoans)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("LoanRecord"))
                }
            }
        }
    }

    /// 导出费用类型数据
    private func exportExpenseTypes() async throws -> [BackupExpenseType] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let types = self.expenseTypeRepository.fetchAll()
                    let backupTypes = types.map { BackupExpenseType.fromCoreData($0) }
                    continuation.resume(returning: backupTypes)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("ExpenseType"))
                }
            }
        }
    }

    /// 导出购买渠道分类数据
    private func exportPurchaseChannelCategories() async throws -> [BackupPurchaseChannelCategory] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let fetchRequest = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
                    let categories = try self.context.fetch(fetchRequest)
                    let backupCategories = categories.map { BackupPurchaseChannelCategory.fromCoreData($0) }
                    continuation.resume(returning: backupCategories)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("PurchaseChannelCategory"))
                }
            }
        }
    }

    /// 导出购买渠道数据
    private func exportPurchaseChannels() async throws -> [BackupPurchaseChannel] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let channels = self.purchaseChannelRepository.fetchAll()
                    let backupChannels = channels.map { BackupPurchaseChannel.fromCoreData($0) }
                    continuation.resume(returning: backupChannels)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("PurchaseChannel"))
                }
            }
        }
    }

    /// 导出提醒数据
    private func exportReminders() async throws -> [BackupReminder] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let reminders = self.reminderRepository.fetchAll()
                    let backupReminders = reminders.map { BackupReminder.fromCoreData($0) }
                    continuation.resume(returning: backupReminders)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Reminder"))
                }
            }
        }
    }

    /// 导出产品关联数据
    private func exportProductLinks() async throws -> [BackupProductLink] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let links = self.productLinkRepository.fetchAll()
                    let backupLinks = links.map { BackupProductLink.fromCoreData($0) }
                    continuation.resume(returning: backupLinks)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("ProductLink"))
                }
            }
        }
    }

    /// 导出对话数据
    private func exportConversations() async throws -> [BackupConversation] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let conversations = self.conversationRepository.fetchAll()
                    let backupConversations = conversations.map { BackupConversation.fromCoreData($0) }
                    continuation.resume(returning: backupConversations)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Conversation"))
                }
            }
        }
    }

    /// 导出消息数据
    private func exportMessages() async throws -> [BackupMessage] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let messages = self.messageRepository.fetchAll()
                    let backupMessages = messages.map { BackupMessage.fromCoreData($0) }
                    continuation.resume(returning: backupMessages)
                } catch {
                    continuation.resume(throwing: DataExportError.entityFetchFailed("Message"))
                }
            }
        }
    }

    /// 更新进度
    private func updateProgress(_ step: String, _ completed: Int, _ total: Int) {
        let progress = total > 0 ? Double(completed) / Double(total) : 0.0
        let progressInfo = ExportProgress(
            currentStep: step,
            completedEntities: completed,
            totalEntities: total,
            progress: progress,
            estimatedTimeRemaining: nil
        )

        DispatchQueue.main.async {
            self.currentProgress = progressInfo
        }
    }
}

// MARK: - Repository扩展

// 为了支持导出功能，需要确保所有Repository都有fetchAll方法
// 这些扩展提供了缺失的Repository类


