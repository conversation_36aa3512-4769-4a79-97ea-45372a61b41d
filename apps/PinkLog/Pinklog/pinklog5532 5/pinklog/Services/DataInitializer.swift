import Foundation
import CoreData
import SwiftUI

class DataInitializer {
    static let shared = DataInitializer()
    
    private init() {}
    
    // 初始化应用数据
    func initializeAppData(context: NSManagedObjectContext) {
        // 检查是否已经初始化过
        let fetchRequest: NSFetchRequest<ExpenseType> = ExpenseType.fetchRequest()
        
        do {
            let count = try context.count(for: fetchRequest)
            if count == 0 {
                // 创建基础费用类型
                createExpenseTypes(in: context)
                
                // 创建基础类别
                createCategories(in: context)
                
                // 创建基础标签
                createTags(in: context)
                
                // 保存上下文
                try context.save()
                
                print("基础数据初始化完成")
            }
        } catch {
            print("初始化基础数据失败: \(error)")
        }
    }
    
    // 创建基础费用类型
    private func createExpenseTypes(in context: NSManagedObjectContext) {
        let expenseTypes = [
            "维修": "wrench.and.screwdriver",
            "保养": "sparkles",
            "配件": "puzzlepiece",
            "耗材": "cart",
            "电池更换": "battery.100",
            "软件订阅": "app.badge",
            "干洗": "bubbles.and.sparkles",
            "其他": "ellipsis.circle"
        ]
        
        for (name, icon) in expenseTypes {
            let expenseType = ExpenseType(context: context)
            expenseType.id = UUID()
            expenseType.name = name
            expenseType.icon = icon
        }
    }
    
    // 创建基础类别
    private func createCategories(in context: NSManagedObjectContext) {
        let categories = [
            "电子产品": "laptopcomputer",
            "服装": "tshirt",
            "家居": "house",
            "厨房": "fork.knife",
            "美妆": "paintpalette",
            "健康": "heart",
            "户外": "leaf",
            "书籍": "book",
            "办公": "briefcase",
            "其他": "ellipsis.circle"
        ]
        
        for (name, icon) in categories {
            let category = Category(context: context)
            category.id = UUID()
            category.name = name
            category.icon = icon
        }
    }
    
    // 创建基础标签
    private func createTags(in context: NSManagedObjectContext) {
        let tags = [
            ("喜爱", "red"),
            ("必需", "blue"),
            ("奢侈", "purple"),
            ("实用", "green"),
            ("收藏", "orange"),
            ("礼物", "pink")
        ]
        
        for (name, color) in tags {
            let tag = Tag(context: context)
            tag.id = UUID()
            tag.name = name
            tag.color = color
            tag.type = "基础标签"
        }
    }
    
    // 创建示例数据
    func createSampleData(in context: NSManagedObjectContext) {
        // 获取已有的费用类型
        let expenseTypeRepository = ExpenseTypeRepository(context: context)
        let expenseTypes = expenseTypeRepository.fetchAll()
        var expenseTypeDict = [String: ExpenseType]()
        for expenseType in expenseTypes {
            if let name = expenseType.name {
                expenseTypeDict[name] = expenseType
            }
        }
        
        // 获取已有的类别
        let categoryRepository = CategoryRepository(context: context)
        let categories = categoryRepository.fetchAll()
        var categoryDict = [String: Category]()
        for category in categories {
            if let name = category.name {
                categoryDict[name] = category
            }
        }
        
        // 获取已有的标签
        let tagRepository = TagRepository(context: context)
        let tags = tagRepository.fetchAll()
        var tagDict = [String: Tag]()
        for tag in tags {
            if let name = tag.name {
                tagDict[name] = tag
            }
        }
        
        // 创建示例产品
        let calendar = Calendar.current
        let now = Date()
        
        // 示例产品1：笔记本电脑
        let laptop = Product(context: context)
        laptop.id = UUID()
        laptop.name = "MacBook Pro"
        laptop.brand = "Apple"
        laptop.model = "M1 Pro 14英寸"
        laptop.price = 14999
        laptop.purchaseDate = calendar.date(byAdding: .month, value: -6, to: now)!
        laptop.category = categoryDict["电子产品"]
        laptop.purchaseMotivation = "提升效率"
        laptop.warrantyEndDate = calendar.date(byAdding: .year, value: 1, to: laptop.purchaseDate!)
        laptop.initialSatisfaction = 5
        laptop.addToTags(tagDict["实用"]!)
        
        // 添加使用记录
        for i in 0..<30 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*3, to: now)!
            usageRecord.satisfaction = Int16(4 + (i % 2))
            usageRecord.product = laptop
            usageRecord.scenario = ["工作", "学习", "娱乐"].randomElement()
            usageRecord.duration = Double([2, 3, 4, 5].randomElement()!)
        }
        
        // 添加费用记录
        let laptopExpense = RelatedExpense(context: context)
        laptopExpense.id = UUID()
        laptopExpense.amount = 299
        laptopExpense.date = calendar.date(byAdding: .month, value: -2, to: now)!
        laptopExpense.product = laptop
        laptopExpense.type = expenseTypeDict["配件"]
        laptopExpense.notes = "购买保护壳"
        
        // 示例产品2：运动鞋
        let shoes = Product(context: context)
        shoes.id = UUID()
        shoes.name = "跑步鞋"
        shoes.brand = "Nike"
        shoes.model = "Air Zoom"
        shoes.price = 899
        shoes.purchaseDate = calendar.date(byAdding: .month, value: -3, to: now)!
        shoes.category = categoryDict["服装"]
        shoes.purchaseMotivation = "兴趣爱好"
        shoes.initialSatisfaction = 4
        shoes.addToTags(tagDict["喜爱"]!)
        
        // 添加使用记录
        for i in 0..<15 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*5, to: now)!
            usageRecord.satisfaction = Int16(3 + (i % 3))
            usageRecord.product = shoes
            usageRecord.scenario = "跑步"
        }
        
        // 示例产品3：护肤品
        let skincare = Product(context: context)
        skincare.id = UUID()
        skincare.name = "面霜"
        skincare.brand = "La Mer"
        skincare.price = 1500
        skincare.purchaseDate = calendar.date(byAdding: .month, value: -2, to: now)!
        skincare.category = categoryDict["美妆"]
        skincare.purchaseMotivation = "冲动消费"
        skincare.expiryDate = calendar.date(byAdding: .month, value: 10, to: skincare.purchaseDate!)
        skincare.initialSatisfaction = 3
        skincare.addToTags(tagDict["奢侈"]!)
        
        // 添加使用记录
        for i in 0..<20 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*3, to: now)!
            usageRecord.satisfaction = Int16(2 + (i % 4))
            usageRecord.product = skincare
        }
        
        // 保存上下文
        do {
            try context.save()
            print("示例数据创建完成")
        } catch {
            print("创建示例数据失败: \(error)")
        }
    }
}
