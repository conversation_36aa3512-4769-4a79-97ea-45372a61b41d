import Foundation

/// 问题类型枚举
/// 用于分类用户问题，以便提供针对性的数据和回答
enum QuestionType: String, CaseIterable {
    case productPerformance = "产品表现"
    case consumptionPattern = "消费模式"
    case categoryAnalysis = "类别分析"
    case valueAnalysis = "价值分析"
    case general = "综合分析"
    
    var description: String {
        return self.rawValue
    }
    
    /// 问题类型的优先级（用于模糊匹配时的权重）
    var priority: Int {
        switch self {
        case .productPerformance: return 5
        case .valueAnalysis: return 4
        case .categoryAnalysis: return 3
        case .consumptionPattern: return 2
        case .general: return 1
        }
    }
}

/// 问题分析器
/// 负责分析用户问题并确定最适合的问题类型
class QuestionAnalyzer {
    
    // MARK: - 关键词映射
    
    /// 产品表现相关关键词
    private let performanceKeywords = [
        "表现", "最好", "最佳", "优秀", "推荐", "值得", "性能",
        "排名", "排行", "top", "最", "哪个好", "哪些好",
        "评价", "评分", "满意", "质量", "效果"
    ]
    
    /// 消费模式相关关键词
    private let consumptionKeywords = [
        "消费", "习惯", "模式", "趋势", "频率", "购买",
        "花费", "支出", "投资", "预算", "开销",
        "买了", "购入", "入手", "剁手", "消费行为"
    ]
    
    /// 类别分析相关关键词
    private let categoryKeywords = [
        "类别", "分类", "种类", "类型", "品类",
        "电子产品", "家居", "服装", "美妆", "食品",
        "数码", "家电", "书籍", "运动", "旅行",
        "哪个类别", "什么类型", "分布"
    ]
    
    /// 价值分析相关关键词
    private let valueKeywords = [
        "价值", "值得", "划算", "性价比", "回报",
        "投资", "收益", "亏损", "赚", "亏",
        "成本", "效益", "价格", "便宜", "贵",
        "物有所值", "超值", "坑"
    ]
    
    /// 综合分析相关关键词
    private let generalKeywords = [
        "总结", "概况", "整体", "全部", "所有",
        "统计", "数据", "报告", "分析", "建议",
        "怎么样", "如何", "什么情况", "状况"
    ]
    
    // MARK: - 核心方法
    
    /// 分析问题类型
    /// - Parameter question: 用户问题
    /// - Returns: 识别出的问题类型
    func analyzeQuestionType(_ question: String) -> QuestionType {
        let lowercaseQuestion = question.lowercased()
        
        // 计算每种类型的匹配分数
        let scores = QuestionType.allCases.map { type in
            (type, calculateMatchScore(for: type, in: lowercaseQuestion))
        }
        
        // 找到得分最高的类型
        let bestMatch = scores.max { $0.1 < $1.1 }
        
        // 如果最高分数太低，返回综合分析
        if let match = bestMatch, match.1 > 0 {
            return match.0
        } else {
            return .general
        }
    }
    
    /// 分析问题的详细信息
    /// - Parameter question: 用户问题
    /// - Returns: 问题分析结果
    func analyzeQuestionDetails(_ question: String) -> QuestionAnalysisResult {
        let questionType = analyzeQuestionType(question)
        let keywords = extractRelevantKeywords(from: question, for: questionType)
        let confidence = calculateConfidence(for: questionType, in: question)
        let suggestedDataTypes = getSuggestedDataTypes(for: questionType)
        
        return QuestionAnalysisResult(
            questionType: questionType,
            confidence: confidence,
            extractedKeywords: keywords,
            suggestedDataTypes: suggestedDataTypes,
            originalQuestion: question
        )
    }
    
    // MARK: - 辅助方法
    
    /// 计算问题类型的匹配分数
    private func calculateMatchScore(for type: QuestionType, in question: String) -> Double {
        let keywords = getKeywords(for: type)
        var score = 0.0
        var totalWeight = 0.0
        
        for keyword in keywords {
            let weight = getKeywordWeight(keyword)
            totalWeight += weight
            
            if question.contains(keyword) {
                score += weight
                
                // 如果是完整词匹配，给额外加分
                if isCompleteWordMatch(keyword: keyword, in: question) {
                    score += weight * 0.5
                }
            }
        }
        
        // 标准化分数并考虑类型优先级
        let normalizedScore = totalWeight > 0 ? score / totalWeight : 0.0
        return normalizedScore * Double(type.priority) / 5.0
    }
    
    /// 获取指定类型的关键词
    private func getKeywords(for type: QuestionType) -> [String] {
        switch type {
        case .productPerformance:
            return performanceKeywords
        case .consumptionPattern:
            return consumptionKeywords
        case .categoryAnalysis:
            return categoryKeywords
        case .valueAnalysis:
            return valueKeywords
        case .general:
            return generalKeywords
        }
    }
    
    /// 获取关键词权重
    private func getKeywordWeight(_ keyword: String) -> Double {
        // 根据关键词长度和重要性分配权重
        switch keyword.count {
        case 1: return 0.5
        case 2: return 1.0
        case 3: return 1.5
        case 4...: return 2.0
        default: return 1.0
        }
    }
    
    /// 检查是否为完整词匹配
    private func isCompleteWordMatch(keyword: String, in question: String) -> Bool {
        // 简化的完整词匹配检查
        let pattern = "\\b\(NSRegularExpression.escapedPattern(for: keyword))\\b"
        let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: question.utf16.count)
        return regex?.firstMatch(in: question, options: [], range: range) != nil
    }
    
    /// 提取相关关键词
    private func extractRelevantKeywords(from question: String, for type: QuestionType) -> [String] {
        let keywords = getKeywords(for: type)
        let lowercaseQuestion = question.lowercased()
        
        return keywords.filter { lowercaseQuestion.contains($0) }
    }
    
    /// 计算置信度
    private func calculateConfidence(for type: QuestionType, in question: String) -> Double {
        let score = calculateMatchScore(for: type, in: question.lowercased())
        
        // 将分数转换为置信度（0-1之间）
        return min(score, 1.0)
    }
    
    /// 获取建议的数据类型
    private func getSuggestedDataTypes(for type: QuestionType) -> [DataContextType] {
        switch type {
        case .productPerformance:
            return [.productPerformance, .valueAnalysis]
        case .consumptionPattern:
            return [.consumptionPattern, .general]
        case .categoryAnalysis:
            return [.categoryAnalysis, .general]
        case .valueAnalysis:
            return [.valueAnalysis, .productPerformance]
        case .general:
            return [.general, .productPerformance, .consumptionPattern]
        }
    }
}

/// 问题分析结果
struct QuestionAnalysisResult {
    let questionType: QuestionType
    let confidence: Double
    let extractedKeywords: [String]
    let suggestedDataTypes: [DataContextType]
    let originalQuestion: String
    
    /// 是否为高置信度结果
    var isHighConfidence: Bool {
        return confidence >= 0.7
    }
    
    /// 格式化的分析摘要
    var summary: String {
        let confidencePercent = Int(confidence * 100)
        return "问题类型: \(questionType.description) (置信度: \(confidencePercent)%)"
    }
}
