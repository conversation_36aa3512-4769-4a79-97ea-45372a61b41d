import Foundation
import SwiftUI
import Combine

// MARK: - 主动消息类型
enum ProactiveMessageType: String, CaseIterable, Codable {
    case reminder = "提醒"
    case suggestion = "建议"
    case warning = "警告"
    case insight = "洞察"
    case opportunity = "机会"
    case maintenance = "维护"
    
    var icon: String {
        switch self {
        case .reminder:
            return "bell.fill"
        case .suggestion:
            return "lightbulb.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .insight:
            return "brain.head.profile"
        case .opportunity:
            return "star.fill"
        case .maintenance:
            return "wrench.and.screwdriver.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .reminder:
            return .orange
        case .suggestion:
            return .blue
        case .warning:
            return .red
        case .insight:
            return .purple
        case .opportunity:
            return .green
        case .maintenance:
            return .yellow
        }
    }
}

// MARK: - 主动消息优先级
enum ProactiveMessagePriority: Int, CaseIterable, Codable {
    case low = 1
    case medium = 2
    case high = 3
    case urgent = 4
    
    var description: String {
        switch self {
        case .low:
            return "低优先级"
        case .medium:
            return "中等优先级"
        case .high:
            return "高优先级"
        case .urgent:
            return "紧急"
        }
    }
}

// MARK: - 主动消息数据模型
struct ProactiveMessage: Identifiable, Codable {
    let id = UUID()
    let type: ProactiveMessageType
    let priority: ProactiveMessagePriority
    let title: String
    let content: String
    let actionButtons: [String] // 建议的动作按钮标记
    let relatedProductIds: [UUID]
    let confidence: Double
    let timestamp: Date
    let expiresAt: Date?
    
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
}

// MARK: - 主动服务配置
struct ProactiveServiceConfig: Codable {
    var isEnabled: Bool = true
    var frequency: ProactiveFrequency = .daily
    var enabledMessageTypes: [ProactiveMessageType] = ProactiveMessageType.allCases
    var minimumConfidence: Double = 0.7
    var maxMessagesPerSession: Int = 3
    var quietHoursStart: Int = 22
    var quietHoursEnd: Int = 8
    
    enum ProactiveFrequency: String, CaseIterable, Codable {
        case realtime = "实时"
        case hourly = "每小时"
        case daily = "每日"
        case weekly = "每周"
        
        var intervalSeconds: TimeInterval {
            switch self {
            case .realtime:
                return 300 // 5分钟
            case .hourly:
                return 3600 // 1小时
            case .daily:
                return 86400 // 24小时
            case .weekly:
                return 604800 // 7天
            }
        }
    }
}

// MARK: - 主动服务类
@MainActor
class ProactiveService: ObservableObject {
    
    // MARK: - 发布属性
    @Published var config: ProactiveServiceConfig
    @Published var pendingMessages: [ProactiveMessage] = []
    @Published var isAnalyzing: Bool = false
    @Published var lastAnalysisTime: Date?
    @Published var analysisProgress: Double = 0.0
    
    // MARK: - 私有属性
    private let aiDataContextBuilder: AIDataContextBuilder
    private let analyticsAssistant: AnalyticsAssistant
    private var analysisTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(
        aiDataContextBuilder: AIDataContextBuilder,
        analyticsAssistant: AnalyticsAssistant
    ) {
        self.aiDataContextBuilder = aiDataContextBuilder
        self.analyticsAssistant = analyticsAssistant
        
        // 从UserDefaults加载配置
        self.config = Self.loadConfig()
        
        // 设置定时分析
        setupPeriodicAnalysis()
        
        // 监听配置变化
        $config
            .sink { [weak self] newConfig in
                self?.saveConfig(newConfig)
                self?.setupPeriodicAnalysis()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 执行主动分析
    func performProactiveAnalysis() async {
        guard config.isEnabled && !isAnalyzing else { return }
        
        isAnalyzing = true
        analysisProgress = 0.0
        
        do {
            // 1. 构建用户数据上下文 (20%)
            analysisProgress = 0.2
            let userContext = await aiDataContextBuilder.buildUserDataContext()
            
            // 2. 分析异常模式 (40%)
            analysisProgress = 0.4
            let anomalies = await analyzeAnomalies(userContext: userContext)
            
            // 3. 识别优化机会 (60%)
            analysisProgress = 0.6
            let opportunities = await identifyOptimizationOpportunities(userContext: userContext)
            
            // 4. 生成主动消息 (80%)
            analysisProgress = 0.8
            let messages = await generateProactiveMessages(
                anomalies: anomalies,
                opportunities: opportunities,
                userContext: userContext
            )
            
            // 5. 过滤和排序消息 (100%)
            analysisProgress = 1.0
            let filteredMessages = filterAndPrioritizeMessages(messages)
            
            // 更新待发送消息
            pendingMessages = filteredMessages
            lastAnalysisTime = Date()
            
            // 推送所有消息到提醒中心
            await pushMessagesToReminderCenter(filteredMessages)
            
        } catch {
            print("主动分析失败: \(error)")
        }
        
        isAnalyzing = false
    }
    
    /// 推送消息到提醒中心
    func pushMessageToReminderCenter(_ message: ProactiveMessage) async {
        // 转换为提醒格式
        let reminder = ProactiveReminder(from: message)
        
        // 添加到提醒管理器
        await MainActor.run {
            ProactiveReminderManager.shared.addReminder(reminder)
        }
    }
    
    /// 标记消息为已处理
    func markMessageAsProcessed(_ messageId: UUID) {
        pendingMessages.removeAll { $0.id == messageId }
    }
    
    /// 更新配置
    func updateConfig(_ newConfig: ProactiveServiceConfig) {
        config = newConfig
    }
    
    // MARK: - 私有方法
    
    /// 设置定时分析
    private func setupPeriodicAnalysis() {
        analysisTimer?.invalidate()
        
        guard config.isEnabled else { return }
        
        analysisTimer = Timer.scheduledTimer(withTimeInterval: config.frequency.intervalSeconds, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performProactiveAnalysis()
            }
        }
    }
    
    /// 分析异常模式
    private func analyzeAnomalies(userContext: UserDataContext) async -> [String] {
        var anomalies: [String] = []
        
        // 检查消费异常
        if userContext.consumptionTrends.spendingVelocity == .fast {
            anomalies.append("最近消费频率异常高，建议关注预算控制")
        }
        
        // 检查低使用率产品
        let lowUsageProducts = userContext.topPerformingProducts.filter { $0.worthIndex < 3.0 }
        if lowUsageProducts.count > 3 {
            anomalies.append("发现\(lowUsageProducts.count)个低使用率产品，建议考虑处置")
        }
        
        // 检查过期产品
        // 这里需要访问完整产品数据，暂时使用模拟数据
        anomalies.append("检测到可能有产品即将过期，建议检查保质期")
        
        return anomalies
    }
    
    /// 识别优化机会
    private func identifyOptimizationOpportunities(userContext: UserDataContext) async -> [String] {
        var opportunities: [String] = []
        
        // 分析类别优化机会
        if let topCategory = userContext.topCategories.first {
            opportunities.append("在\(topCategory)类别表现优秀，可考虑增加相关投资")
        }
        
        // 分析使用模式优化
        if userContext.totalProducts > 10 {
            opportunities.append("产品数量较多，建议进行分类整理和价值评估")
        }
        
        // 分析投资回报优化
        opportunities.append("建议定期回顾产品使用情况，优化投资决策")
        
        return opportunities
    }
    
    /// 生成主动消息
    private func generateProactiveMessages(
        anomalies: [String],
        opportunities: [String],
        userContext: UserDataContext
    ) async -> [ProactiveMessage] {
        var messages: [ProactiveMessage] = []
        
        // 生成异常警告消息
        for anomaly in anomalies {
            let message = ProactiveMessage(
                type: .warning,
                priority: .high,
                title: "发现异常模式",
                content: anomaly,
                actionButtons: ["[查看分析:type=异常分析]", "[生成报告:type=异常报告]"],
                relatedProductIds: [],
                confidence: 0.8,
                timestamp: Date(),
                expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
            )
            messages.append(message)
        }
        
        // 生成优化建议消息
        for opportunity in opportunities {
            let message = ProactiveMessage(
                type: .suggestion,
                priority: .medium,
                title: "发现优化机会",
                content: opportunity,
                actionButtons: ["[查看分析:type=优化分析]", "[设置目标:type=优化目标]"],
                relatedProductIds: [],
                confidence: 0.7,
                timestamp: Date(),
                expiresAt: Calendar.current.date(byAdding: .day, value: 14, to: Date())
            )
            messages.append(message)
        }
        
        // 生成定期洞察消息
        if shouldGenerateInsight() {
            let insightMessage = ProactiveMessage(
                type: .insight,
                priority: .low,
                title: "数据洞察",
                content: "您的消费数据显示了一些有趣的模式，总投资¥\(String(format: "%.0f", userContext.totalInvestment))，共\(userContext.totalProducts)个产品。",
                actionButtons: ["[查看分析:type=数据洞察]", "[生成报告:type=月度报告]"],
                relatedProductIds: [],
                confidence: 0.9,
                timestamp: Date(),
                expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
            )
            messages.append(insightMessage)
        }
        
        return messages
    }
    
    /// 过滤和优先级排序消息
    private func filterAndPrioritizeMessages(_ messages: [ProactiveMessage]) -> [ProactiveMessage] {
        return messages
            .filter { message in
                // 过滤过期消息
                !message.isExpired &&
                // 过滤低置信度消息
                message.confidence >= config.minimumConfidence &&
                // 过滤禁用类型
                config.enabledMessageTypes.contains(message.type)
            }
            .sorted { $0.priority.rawValue > $1.priority.rawValue }
            .prefix(config.maxMessagesPerSession)
            .map { $0 }
    }
    
    /// 推送所有消息到提醒中心
    private func pushMessagesToReminderCenter(_ messages: [ProactiveMessage]) async {
        let reminders = messages.map { ProactiveReminder(from: $0) }
        
        await MainActor.run {
            ProactiveReminderManager.shared.addReminders(reminders)
        }
    }
    
    /// 推送高优先级消息到提醒中心（保留兼容性）
    private func pushHighPriorityMessages(_ messages: [ProactiveMessage]) async {
        let highPriorityMessages = messages.filter { $0.priority.rawValue >= ProactiveMessagePriority.high.rawValue }
        
        for message in highPriorityMessages.prefix(2) { // 最多推送2条高优先级消息
            await pushMessageToReminderCenter(message)
        }
    }
    
    /// 构建AI消息内容
    private func buildAIMessageContent(from message: ProactiveMessage) -> String {
        var content = "🤖 **主动助手提醒**\n\n"
        content += "**\(message.title)**\n\n"
        content += message.content
        
        // 添加动作按钮标记
        if !message.actionButtons.isEmpty {
            content += "\n\n"
            content += message.actionButtons.joined(separator: " ")
        }
        
        // 添加置信度信息
        content += "\n\n*置信度: \(Int(message.confidence * 100))%*"
        
        return content
    }
    
    /// 判断是否应该生成洞察
    private func shouldGenerateInsight() -> Bool {
        // 基于时间和用户活跃度判断
        guard let lastTime = lastAnalysisTime else { return true }
        let daysSinceLastAnalysis = Calendar.current.dateComponents([.day], from: lastTime, to: Date()).day ?? 0
        return daysSinceLastAnalysis >= 1
    }
    
    // MARK: - 配置管理
    
    private static func loadConfig() -> ProactiveServiceConfig {
        guard let data = UserDefaults.standard.data(forKey: "ProactiveServiceConfig"),
              let config = try? JSONDecoder().decode(ProactiveServiceConfig.self, from: data) else {
            return ProactiveServiceConfig()
        }
        return config
    }
    
    private func saveConfig(_ config: ProactiveServiceConfig) {
        if let data = try? JSONEncoder().encode(config) {
            UserDefaults.standard.set(data, forKey: "ProactiveServiceConfig")
        }
    }
}
