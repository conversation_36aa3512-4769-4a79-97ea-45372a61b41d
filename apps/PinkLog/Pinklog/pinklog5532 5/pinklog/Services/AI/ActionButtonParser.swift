import Foundation
import SwiftUI

// MARK: - 动作类型枚举
enum ActionType: String, CaseIterable, Identifiable {
    case createReminder = "创建提醒"
    case viewProduct = "查看产品"
    case generateReport = "生成报告"
    case addUsageRecord = "添加使用记录"
    case createProduct = "创建产品"
    case viewAnalytics = "查看分析"
    case setGoal = "设置目标"
    case exportData = "导出数据"
    case scheduleTask = "安排任务"
    case openSettings = "打开设置"
    
    var id: String { self.rawValue }
    
    var icon: String {
        switch self {
        case .createReminder:
            return "bell.badge.plus"
        case .viewProduct:
            return "cube.box"
        case .generateReport:
            return "doc.text.magnifyingglass"
        case .addUsageRecord:
            return "plus.circle"
        case .createProduct:
            return "plus.square"
        case .viewAnalytics:
            return "chart.bar.xaxis"
        case .setGoal:
            return "target"
        case .exportData:
            return "square.and.arrow.up"
        case .scheduleTask:
            return "calendar.badge.plus"
        case .openSettings:
            return "gear"
        }
    }
    
    var color: Color {
        switch self {
        case .createReminder:
            return .orange
        case .viewProduct:
            return .blue
        case .generateReport:
            return .purple
        case .addUsageRecord:
            return .green
        case .createProduct:
            return .mint
        case .viewAnalytics:
            return .indigo
        case .setGoal:
            return .red
        case .exportData:
            return .teal
        case .scheduleTask:
            return .yellow
        case .openSettings:
            return .gray
        }
    }
}

// MARK: - 动作按钮数据模型
struct ActionButton: Identifiable, Equatable {
    let id = UUID()
    let type: ActionType
    let title: String
    let parameters: [String: String]
    
    static func == (lhs: ActionButton, rhs: ActionButton) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 动作按钮解析器
class ActionButtonParser {
    
    // MARK: - 解析AI回答中的动作标记
    static func parseActions(from content: String) -> [ActionButton] {
        var actions: [ActionButton] = []
        
        // 定义动作标记的正则表达式模式
        let patterns: [(ActionType, String)] = [
            (.createReminder, "\\[创建提醒:([^\\]]+)\\]"),
            (.viewProduct, "\\[查看产品:([^\\]]+)\\]"),
            (.generateReport, "\\[生成报告:([^\\]]+)\\]"),
            (.addUsageRecord, "\\[添加使用记录:([^\\]]+)\\]"),
            (.createProduct, "\\[创建产品:([^\\]]+)\\]"),
            (.viewAnalytics, "\\[查看分析:([^\\]]+)\\]"),
            (.setGoal, "\\[设置目标:([^\\]]+)\\]"),
            (.exportData, "\\[导出数据:([^\\]]+)\\]"),
            (.scheduleTask, "\\[安排任务:([^\\]]+)\\]"),
            (.openSettings, "\\[打开设置:([^\\]]+)\\]")
        ]
        
        for (actionType, pattern) in patterns {
            let regex = try? NSRegularExpression(pattern: pattern, options: [])
            let range = NSRange(content.startIndex..<content.endIndex, in: content)
            
            regex?.enumerateMatches(in: content, options: [], range: range) { match, _, _ in
                guard let match = match,
                      let paramRange = Range(match.range(at: 1), in: content) else { return }
                
                let paramString = String(content[paramRange])
                let parameters = parseParameters(paramString)
                
                let action = ActionButton(
                    type: actionType,
                    title: parameters["title"] ?? actionType.rawValue,
                    parameters: parameters
                )
                
                actions.append(action)
            }
        }
        
        // 智能推断动作（基于关键词）
        actions.append(contentsOf: inferActionsFromKeywords(content))
        
        return actions
    }
    
    // MARK: - 解析参数字符串
    private static func parseParameters(_ paramString: String) -> [String: String] {
        var parameters: [String: String] = [:]
        
        // 解析键值对格式：key1=value1,key2=value2
        let pairs = paramString.components(separatedBy: ",")
        
        for pair in pairs {
            let keyValue = pair.components(separatedBy: "=")
            if keyValue.count == 2 {
                let key = keyValue[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let value = keyValue[1].trimmingCharacters(in: .whitespacesAndNewlines)
                parameters[key] = value
            } else {
                // 如果没有键值对格式，将整个字符串作为标题
                parameters["title"] = paramString.trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        return parameters
    }
    
    // MARK: - 基于关键词智能推断动作
    private static func inferActionsFromKeywords(_ content: String) -> [ActionButton] {
        var actions: [ActionButton] = []
        let lowercaseContent = content.lowercased()
        
        // 定义关键词和对应的动作
        let keywordActions: [(keywords: [String], action: ActionType, title: String)] = [
            (["提醒", "通知", "提示"], .createReminder, "创建提醒"),
            (["产品详情", "产品信息", "查看产品"], .viewProduct, "查看产品详情"),
            (["报告", "分析报告", "月报"], .generateReport, "生成分析报告"),
            (["使用记录", "记录使用", "添加记录"], .addUsageRecord, "添加使用记录"),
            (["新产品", "添加产品", "创建产品"], .createProduct, "创建新产品"),
            (["数据分析", "查看分析", "分析数据"], .viewAnalytics, "查看数据分析"),
            (["设置目标", "目标设定"], .setGoal, "设置目标"),
            (["导出", "备份数据"], .exportData, "导出数据"),
            (["安排", "计划", "日程"], .scheduleTask, "安排任务"),
            (["设置", "配置"], .openSettings, "打开设置")
        ]
        
        for (keywords, actionType, title) in keywordActions {
            for keyword in keywords {
                if lowercaseContent.contains(keyword) {
                    let action = ActionButton(
                        type: actionType,
                        title: title,
                        parameters: ["inferred": "true", "keyword": keyword]
                    )
                    
                    // 避免重复添加相同类型的动作
                    if !actions.contains(where: { $0.type == actionType }) {
                        actions.append(action)
                    }
                    break
                }
            }
        }
        
        return actions
    }
    
    // MARK: - 清理内容中的动作标记
    static func cleanContent(_ content: String) -> String {
        var cleanedContent = content
        
        // 移除所有动作标记
        let patterns = [
            "\\[创建提醒:[^\\]]+\\]",
            "\\[查看产品:[^\\]]+\\]",
            "\\[生成报告:[^\\]]+\\]",
            "\\[添加使用记录:[^\\]]+\\]",
            "\\[创建产品:[^\\]]+\\]",
            "\\[查看分析:[^\\]]+\\]",
            "\\[设置目标:[^\\]]+\\]",
            "\\[导出数据:[^\\]]+\\]",
            "\\[安排任务:[^\\]]+\\]",
            "\\[打开设置:[^\\]]+\\]"
        ]
        
        for pattern in patterns {
            let regex = try? NSRegularExpression(pattern: pattern, options: [])
            let range = NSRange(cleanedContent.startIndex..<cleanedContent.endIndex, in: cleanedContent)
            cleanedContent = regex?.stringByReplacingMatches(
                in: cleanedContent,
                options: [],
                range: range,
                withTemplate: ""
            ) ?? cleanedContent
        }
        
        return cleanedContent.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - 动作执行器
class ActionExecutor: ObservableObject {
    @Published var isExecuting: Bool = false
    @Published var lastExecutionResult: String?
    @Published var showingExecutionFeedback: Bool = false

    // 依赖注入的ViewModels和Repositories
    private var productViewModel: ProductViewModel?
    private var usageViewModel: UsageViewModel?
    private var notificationManager = NotificationManager.shared

    // 设置依赖
    func setDependencies(productViewModel: ProductViewModel?, usageViewModel: UsageViewModel?) {
        self.productViewModel = productViewModel
        self.usageViewModel = usageViewModel
    }

    // MARK: - 执行动作
    func executeAction(_ action: ActionButton) {
        isExecuting = true

        Task {
            await MainActor.run {
                switch action.type {
                case .createReminder:
                    executeCreateReminder(action)
                case .viewProduct:
                    executeViewProduct(action)
                case .generateReport:
                    executeGenerateReport(action)
                case .addUsageRecord:
                    executeAddUsageRecord(action)
                case .createProduct:
                    executeCreateProduct(action)
                case .viewAnalytics:
                    executeViewAnalytics(action)
                case .setGoal:
                    executeSetGoal(action)
                case .exportData:
                    executeExportData(action)
                case .scheduleTask:
                    executeScheduleTask(action)
                case .openSettings:
                    executeOpenSettings(action)
                }

                isExecuting = false
                showingExecutionFeedback = true

                // 3秒后自动隐藏反馈
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.showingExecutionFeedback = false
                }
            }
        }
    }
    
    // MARK: - 具体动作执行方法
    private func executeCreateReminder(_ action: ActionButton) {
        // 集成NotificationManager创建提醒
        if let productName = action.parameters["product"] {
            // 查找产品并创建提醒
            if let product = findProduct(by: productName) {
                if let expiryDate = product.expiryDate {
                    notificationManager.scheduleExpiryReminder(for: product)
                    lastExecutionResult = "已为\(productName)创建过期提醒"
                } else {
                    lastExecutionResult = "产品\(productName)没有设置过期日期"
                }
            } else {
                lastExecutionResult = "未找到产品：\(productName)"
            }
        } else {
            lastExecutionResult = "提醒功能需要指定产品名称"
        }
    }

    private func executeViewProduct(_ action: ActionButton) {
        // 集成ProductViewModel查看产品
        if let productName = action.parameters["product"] {
            if let product = findProduct(by: productName) {
                productViewModel?.selectedProduct = product
                lastExecutionResult = "已选择产品：\(productName)"
            } else {
                lastExecutionResult = "未找到产品：\(productName)"
            }
        } else {
            lastExecutionResult = "请指定要查看的产品名称"
        }
    }

    private func executeGenerateReport(_ action: ActionButton) {
        // 集成分析功能生成报告
        if let reportType = action.parameters["type"] {
            switch reportType.lowercased() {
            case "月报", "monthly":
                lastExecutionResult = "月度分析报告生成功能即将推出"
            case "产品分析", "product":
                lastExecutionResult = "产品分析报告生成功能即将推出"
            default:
                lastExecutionResult = "已触发\(reportType)报告生成"
            }
        } else {
            lastExecutionResult = "分析报告生成功能即将推出"
        }
    }

    private func executeAddUsageRecord(_ action: ActionButton) {
        // 集成UsageViewModel添加使用记录
        if let productName = action.parameters["product"] {
            if let product = findProduct(by: productName) {
                usageViewModel?.currentProduct = product
                // 添加默认使用记录
                let success = usageViewModel?.addUsageRecord(
                    date: Date(),
                    satisfaction: 4,
                    notes: "通过AI助手快速添加",
                    scenario: "日常使用"
                ) ?? false

                if success {
                    lastExecutionResult = "已为\(productName)添加使用记录"
                } else {
                    lastExecutionResult = "添加使用记录失败"
                }
            } else {
                lastExecutionResult = "未找到产品：\(productName)"
            }
        } else {
            lastExecutionResult = "请指定要添加使用记录的产品"
        }
    }

    private func executeCreateProduct(_ action: ActionButton) {
        // 集成ProductViewModel创建产品
        if let productName = action.parameters["name"] {
            // 这里只是设置提示，实际创建需要更多信息
            lastExecutionResult = "产品创建向导即将推出，产品名称：\(productName)"
        } else {
            lastExecutionResult = "产品创建向导即将推出"
        }
    }
    
    private func executeViewAnalytics(_ action: ActionButton) {
        // 集成分析查看功能
        if let analysisType = action.parameters["type"] {
            switch analysisType.lowercased() {
            case "值度", "worth":
                lastExecutionResult = "值度分析界面即将打开"
            case "使用率", "usage":
                lastExecutionResult = "使用率分析界面即将打开"
            case "成本", "cost", "tco":
                lastExecutionResult = "成本分析界面即将打开"
            default:
                lastExecutionResult = "分析界面即将打开：\(analysisType)"
            }
        } else {
            lastExecutionResult = "数据分析界面即将打开"
        }
    }

    private func executeSetGoal(_ action: ActionButton) {
        // 集成目标设置功能
        if let goalType = action.parameters["type"] {
            lastExecutionResult = "目标设置功能即将推出：\(goalType)"
        } else {
            lastExecutionResult = "目标设置功能即将推出"
        }
    }

    private func executeExportData(_ action: ActionButton) {
        // 集成数据导出功能
        if let dataType = action.parameters["type"] {
            switch dataType.lowercased() {
            case "产品", "products":
                lastExecutionResult = "产品数据导出功能即将推出"
            case "使用记录", "usage":
                lastExecutionResult = "使用记录导出功能即将推出"
            case "费用", "expenses":
                lastExecutionResult = "费用记录导出功能即将推出"
            default:
                lastExecutionResult = "数据导出功能即将推出：\(dataType)"
            }
        } else {
            lastExecutionResult = "数据导出功能即将推出"
        }
    }

    private func executeScheduleTask(_ action: ActionButton) {
        // 集成任务安排功能
        if let taskType = action.parameters["type"] {
            lastExecutionResult = "任务安排功能即将推出：\(taskType)"
        } else {
            lastExecutionResult = "任务安排功能即将推出"
        }
    }

    private func executeOpenSettings(_ action: ActionButton) {
        // 集成设置界面功能
        if let settingType = action.parameters["type"] {
            lastExecutionResult = "设置界面即将打开：\(settingType)"
        } else {
            lastExecutionResult = "设置界面即将打开"
        }
    }

    // MARK: - 辅助方法
    private func findProduct(by name: String) -> Product? {
        return productViewModel?.products.first { product in
            product.name?.lowercased().contains(name.lowercased()) == true
        }
    }
}
