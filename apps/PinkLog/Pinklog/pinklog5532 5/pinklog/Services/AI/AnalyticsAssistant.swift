import Foundation
import SwiftUI
import Combine
import CoreData

/// 智能分析助手
@MainActor
class AnalyticsAssistant: ObservableObject {
    
    // MARK: - 发布属性
    @Published var conversationHistory: [ConversationMessage] = []
    @Published var isProcessing: Bool = false
    @Published var currentContext: AnalysisContext?
    @Published var monthlyReport: MonthlyReport?
    @Published var isGeneratingReport: Bool = false
    @Published var lastError: String?
    @Published var costTracker: CostTracker = CostTracker()
    @Published var streamingContent: String = ""
    @Published var isStreaming: Bool = false
    @Published var currentConversationId: UUID?
    @Published var markdownRenderingEnabled: Bool = true
    
    // MARK: - 私有属性
    let deepSeekService: DeepSeekAPIService
    private let reportGenerator: MonthlyReportGenerator
    private let contextManager: ConversationContextManager
    private let offlineMode: OfflineAssistant
    private let dataContextBuilder: AIDataContextBuilder
    private let questionAnalyzer: QuestionAnalyzer
    private let conversationRepository: ConversationRepository
    private let messageRepository: MessageRepository
    
    // MARK: - 配置
    private let maxConversationLength = 20
    private let maxTokensPerRequest = 4000
    private let monthlyBudgetLimit: Double = 50.0 // 50元人民币
    
    // MARK: - 初始化
    init(
        deepSeekService: DeepSeekAPIService? = nil,
        reportGenerator: MonthlyReportGenerator? = nil,
        contextManager: ConversationContextManager? = nil,
        offlineMode: OfflineAssistant? = nil,
        dataContextBuilder: AIDataContextBuilder? = nil,
        questionAnalyzer: QuestionAnalyzer? = nil,
        productRepository: ProductRepository? = nil,
        usageRepository: UsageRecordRepository? = nil,
        expenseRepository: RelatedExpenseRepository? = nil,
        overviewEngine: AdvancedOverviewEngine? = nil,
        conversationRepository: ConversationRepository? = nil,
        messageRepository: MessageRepository? = nil
    ) {
        self.deepSeekService = deepSeekService ?? DeepSeekAPIService()

        // 如果提供了reportGenerator，直接使用；否则创建新的实例
        if let reportGenerator = reportGenerator {
            self.reportGenerator = reportGenerator
        } else {
            // 创建默认的Repository实例（使用共享的PersistenceController）
            let context = PersistenceController.shared.container.viewContext
            let defaultProductRepository = productRepository ?? ProductRepository(context: context)
            let defaultUsageRepository = usageRepository ?? UsageRecordRepository(context: context)
            let defaultExpenseRepository = expenseRepository ?? RelatedExpenseRepository(context: context)
            let defaultOverviewEngine = overviewEngine ?? AdvancedOverviewEngine.shared

            self.reportGenerator = MonthlyReportGenerator(
                productRepository: defaultProductRepository,
                usageRepository: defaultUsageRepository,
                expenseRepository: defaultExpenseRepository,
                overviewEngine: defaultOverviewEngine
            )
        }

        self.contextManager = contextManager ?? ConversationContextManager()
        self.offlineMode = offlineMode ?? OfflineAssistant()

        // 创建AIDataContextBuilder实例
        if let dataContextBuilder = dataContextBuilder {
            self.dataContextBuilder = dataContextBuilder
        } else {
            // 创建默认的Repository实例（复用上面创建的实例）
            let context = PersistenceController.shared.container.viewContext
            let defaultProductRepository = productRepository ?? ProductRepository(context: context)
            let defaultUsageRepository = usageRepository ?? UsageRecordRepository(context: context)
            let defaultExpenseRepository = expenseRepository ?? RelatedExpenseRepository(context: context)
            let defaultOverviewEngine = overviewEngine ?? AdvancedOverviewEngine.shared

            self.dataContextBuilder = AIDataContextBuilder(
                productRepository: defaultProductRepository,
                usageRepository: defaultUsageRepository,
                expenseRepository: defaultExpenseRepository,
                overviewEngine: defaultOverviewEngine
            )
        }

        // 创建QuestionAnalyzer实例
        self.questionAnalyzer = questionAnalyzer ?? QuestionAnalyzer()

        // 创建Repository实例
        let context = PersistenceController.shared.container.viewContext
        self.conversationRepository = conversationRepository ?? ConversationRepository(context: context)
        self.messageRepository = messageRepository ?? MessageRepository(context: context)

        loadConversationHistory()
        setupCostTracking()
    }
    
    // MARK: - 公共方法
    
    /// 处理用户问题（流式传输）
    func processUserQuestionStream(
        _ question: String,
        context: AnalysisContext? = nil
    ) {
        // 检查成本限制
        guard !costTracker.isOverBudget else {
            Task {
                let response = offlineMode.processQuestion(question, context: context)
                await MainActor.run {
                    let assistantMessage = ConversationMessage(
                        role: .assistant,
                        content: response.content,
                        analysisContext: currentContext
                    )
                    conversationHistory.append(assistantMessage)
                    saveConversationHistory()
                }
            }
            return
        }

        isProcessing = true
        isStreaming = true
        streamingContent = ""
        lastError = nil

        // 更新上下文
        if let context = context {
            currentContext = context
        }

        // 添加用户消息到历史
        let userMessage = ConversationMessage(
            role: .user,
            content: question,
            analysisContext: currentContext
        )
        conversationHistory.append(userMessage)

        // 保存用户消息到数据库
        saveUserMessage(userMessage)

        Task {
            do {
                // 构建优化的系统提示（成本优化版本）
                let completeDataContext = await dataContextBuilder.buildCompleteUserDataContext()
                let systemPrompt = await buildOptimizedSystemPrompt(completeContext: completeDataContext, question: question)

                // 准备消息
                let messages = prepareMessages(systemPrompt: systemPrompt, userQuestion: question)

                // 调用流式API
                deepSeekService.chatCompletionStream(
                    messages: messages,
                    model: .chat,
                    maxTokens: maxTokensPerRequest,
                    temperature: 0.7,
                    onUpdate: { [weak self] content in
                        Task { @MainActor in
                            self?.streamingContent += content
                        }
                    },
                    onComplete: { [weak self] usage in
                        Task { @MainActor in
                            guard let self = self else { return }

                            self.isProcessing = false
                            self.isStreaming = false

                            // 更新成本追踪
                            if let usage = usage {
                                self.costTracker.addUsage(
                                    inputTokens: usage.promptTokens,
                                    outputTokens: usage.completionTokens,
                                    model: .chat
                                )
                            }

                            // 添加助手消息到历史
                            let assistantMessage = ConversationMessage(
                                role: .assistant,
                                content: self.streamingContent,
                                analysisContext: self.currentContext
                            )
                            self.conversationHistory.append(assistantMessage)

                            // 保存助手消息到数据库
                            self.saveAssistantMessage(assistantMessage)

                            // 清空流式内容
                            self.streamingContent = ""

                            // 保存对话历史
                            self.saveConversationHistory()
                        }
                    },
                    onError: { [weak self] error in
                        Task { @MainActor in
                            guard let self = self else { return }

                            self.isProcessing = false
                            self.isStreaming = false
                            self.lastError = error.localizedDescription

                            // 降级到离线模式
                            let response = self.offlineMode.processQuestion(question, context: context)
                            let assistantMessage = ConversationMessage(
                                role: .assistant,
                                content: response.content,
                                analysisContext: self.currentContext
                            )
                            self.conversationHistory.append(assistantMessage)
                            self.saveAssistantMessage(assistantMessage)
                            self.saveConversationHistory()

                            // 清空流式内容
                            self.streamingContent = ""
                        }
                    }
                )

            } catch {
                await MainActor.run {
                    self.isProcessing = false
                    self.isStreaming = false
                    self.lastError = error.localizedDescription
                    self.streamingContent = ""

                    // 降级到离线模式
                    let response = self.offlineMode.processQuestion(question, context: context)
                    let assistantMessage = ConversationMessage(
                        role: .assistant,
                        content: response.content,
                        analysisContext: self.currentContext
                    )
                    self.conversationHistory.append(assistantMessage)
                    self.saveAssistantMessage(assistantMessage)
                    self.saveConversationHistory()
                }
            }
        }
    }

    /// 处理用户问题（非流式，保持向后兼容）
    func processUserQuestion(
        _ question: String,
        context: AnalysisContext? = nil
    ) async -> AssistantResponse {
        
        // 检查成本限制
        guard !costTracker.isOverBudget else {
            return offlineMode.processQuestion(question, context: context)
        }
        
        isProcessing = true
        lastError = nil
        
        defer { isProcessing = false }
        
        do {
            // 更新上下文
            if let context = context {
                currentContext = context
            }
            
            // 添加用户消息到历史
            let userMessage = ConversationMessage(
                role: .user,
                content: question,
                analysisContext: currentContext
            )
            conversationHistory.append(userMessage)
            saveUserMessage(userMessage)

            // 智能问题路由：分析问题类型并获取相关数据
            let questionAnalysis = questionAnalyzer.analyzeQuestionDetails(question)
            let relevantData = await gatherRelevantData(for: questionAnalysis)

            // 构建包含相关数据的系统提示
            let systemPrompt = await buildSystemPrompt(with: relevantData)

            // 准备消息
            let messages = prepareMessages(systemPrompt: systemPrompt, userQuestion: question)
            
            // 调用 API
            let response = try await deepSeekService.chatCompletion(
                messages: messages,
                model: .chat,
                maxTokens: maxTokensPerRequest,
                temperature: 0.7
            )
            
            // 更新成本追踪
            costTracker.addUsage(
                inputTokens: response.usage.promptTokens,
                outputTokens: response.usage.completionTokens,
                model: .chat
            )
            
            // 解析响应
            let assistantResponse = parseAssistantResponse(response.choices.first?.message.content ?? "")
            
            // 添加助手消息到历史
            let assistantMessage = ConversationMessage(
                role: .assistant,
                content: assistantResponse.content,
                analysisContext: currentContext
            )
            conversationHistory.append(assistantMessage)
            saveAssistantMessage(assistantMessage)

            // 保存对话历史
            saveConversationHistory()
            
            return assistantResponse
            
        } catch {
            lastError = error.localizedDescription
            
            // 降级到离线模式
            return offlineMode.processQuestion(question, context: context)
        }
    }
    
    /// 生成月度报告
    func generateMonthlyReport(for month: Date) async -> MonthlyReport? {
        
        // 检查成本限制
        guard !costTracker.isOverBudget else {
            return offlineMode.generateBasicReport(for: month)
        }
        
        isGeneratingReport = true
        lastError = nil
        
        defer { isGeneratingReport = false }
        
        do {
            // 收集月度数据
            let monthlyData = await reportGenerator.collectMonthlyData(for: month)
            
            // 构建分析提示
            let analysisPrompt = buildMonthlyAnalysisPrompt(data: monthlyData)
            
            // 调用推理模型
            let response = try await deepSeekService.chatCompletion(
                messages: [ChatMessage(role: "user", content: analysisPrompt)],
                model: .reasoner,
                responseFormat: .jsonObject,
                maxTokens: 8000,
                temperature: 0.3
            )
            
            // 更新成本追踪
            costTracker.addUsage(
                inputTokens: response.usage.promptTokens,
                outputTokens: response.usage.completionTokens,
                model: .reasoner
            )
            
            // 解析报告
            let report = try parseMonthlyReport(response.choices.first?.message.content ?? "")
            monthlyReport = report
            
            return report
            
        } catch {
            lastError = error.localizedDescription
            
            // 降级到基础报告
            return offlineMode.generateBasicReport(for: month)
        }
    }
    
    /// 清除对话历史
    func clearConversation() {
        conversationHistory.removeAll()
        currentContext = nil
        currentConversationId = nil
        saveConversationHistory()
    }
    
    /// 设置分析上下文
    func setAnalysisContext(_ context: AnalysisContext) {
        currentContext = context
    }
    
    /// 获取成本统计
    func getCostStatistics() -> CostStatistics {
        return costTracker.getStatistics()
    }
    
    /// 重置成本追踪
    func resetCostTracking() {
        costTracker.reset()
    }

    // MARK: - 主动服务支持已迁移到ProactiveReminderManager
}

// MARK: - 私有方法扩展
private extension AnalyticsAssistant {
    
    /// 构建系统提示
    func buildSystemPrompt() async -> String {
        var prompt = """
        你是PinkLog应用的智能分析助手，专门帮助用户理解和优化他们的消费决策。

        你的核心能力：
        1. 解读三曲线分析结果（价值实现、使用活力、情感依恋）
        2. 提供个性化的消费建议和优化策略
        3. 回答关于产品价值、使用模式、情感连接的问题
        4. 帮助用户做出更明智的购买和处置决策

        回答风格：
        - 专业但易懂，避免过于技术化的术语
        - 提供具体可行的建议
        - 基于数据分析，但考虑用户的情感需求
        - 简洁明了，重点突出

        """

        // 添加用户数据上下文
        let userDataContext = await dataContextBuilder.buildUserDataContext()
        prompt += buildUserDataPrompt(userDataContext)

        // 添加当前上下文信息
        if let context = currentContext {
            prompt += await buildContextualPrompt(context)
        }

        return prompt
    }
    
    /// 构建用户数据提示
    func buildUserDataPrompt(_ context: UserDataContext) -> String {
        var dataPrompt = "\n用户数据概览：\n"

        // 基础统计信息
        dataPrompt += "- 总产品数：\(context.totalProducts)个\n"
        dataPrompt += "- 总投资：\(context.formattedInvestment)\n"
        dataPrompt += "- 平均产品价值：¥\(String(format: "%.0f", context.averageProductValue))\n"

        // 主要类别
        if !context.topCategories.isEmpty {
            dataPrompt += "- 主要类别：\(context.topCategories.joined(separator: "、"))\n"
        }

        // 表现最佳产品
        if !context.topPerformingProducts.isEmpty {
            let topProductNames = context.topPerformingProducts.prefix(3).map { $0.name }
            dataPrompt += "- 表现最佳产品：\(topProductNames.joined(separator: "、"))\n"
        }

        // 最近购买
        if !context.recentPurchases.isEmpty {
            let recentProductNames = context.recentPurchases.prefix(3).map { $0.name }
            dataPrompt += "- 最近购买：\(recentProductNames.joined(separator: "、"))\n"
        }

        // 消费趋势
        let trends = context.consumptionTrends
        dataPrompt += "- 消费趋势：\(trends.monthlySpendingTrend.description)\n"
        dataPrompt += "- 主要消费类别：\(trends.primaryCategory)\n"
        dataPrompt += "- 购买频率：\(trends.purchaseFrequencyDescription)\n"

        dataPrompt += "\n请基于这些真实数据回答用户问题，提供具体、个性化的建议。当用户询问具体产品、类别或趋势时，请引用上述数据中的相关信息。\n"

        return dataPrompt
    }

    /// 构建上下文提示
    func buildContextualPrompt(_ context: AnalysisContext) async -> String {
        var contextPrompt = "\n当前分析上下文：\n"

        // 添加产品信息
        if let productId = context.productId {
            // 这里可以获取产品详细信息
            contextPrompt += "- 正在分析特定产品\n"
        }

        // 添加分析类型
        contextPrompt += "- 分析类型：\(context.analysisType.displayName)\n"

        // 添加时间范围
        if let timeRange = context.timeRange {
            contextPrompt += "- 时间范围：\(timeRange.displayString)\n"
        }

        return contextPrompt
    }
    
    /// 准备消息
    func prepareMessages(systemPrompt: String, userQuestion: String) -> [ChatMessage] {
        var messages: [ChatMessage] = []
        
        // 添加系统消息
        messages.append(ChatMessage(role: "system", content: systemPrompt))
        
        // 添加最近的对话历史（限制长度）
        let recentHistory = Array(conversationHistory.suffix(maxConversationLength))
        for message in recentHistory {
            if message.role != .system {
                messages.append(ChatMessage(role: message.role.rawValue, content: message.content))
            }
        }
        
        return messages
    }
    
    /// 解析助手响应
    func parseAssistantResponse(_ content: String) -> AssistantResponse {
        // 这里可以实现更复杂的解析逻辑
        // 目前返回基础响应
        return AssistantResponse(
            content: content,
            confidence: 0.8,
            suggestions: [],
            relatedInsights: [],
            followUpQuestions: generateFollowUpQuestions(content),
            visualizations: []
        )
    }
    
    /// 生成后续问题
    func generateFollowUpQuestions(_ content: String) -> [String] {
        let commonQuestions = [
            "这个分析结果对我的决策有什么具体影响？",
            "我应该如何优化这个产品的使用？",
            "有什么类似的产品值得考虑？",
            "这个趋势会持续多久？"
        ]
        
        return Array(commonQuestions.prefix(2))
    }
    
    /// 构建月度分析提示
    func buildMonthlyAnalysisPrompt(data: MonthlyData) -> String {
        return """
        请基于以下数据生成专业的月度消费分析报告，以JSON格式返回：
        
        数据概览：
        - 总产品数：\(data.totalProducts)
        - 总消费：¥\(data.totalSpending)
        - 平均价值度：\(data.averageWorthiness)
        - 主要类别：\(data.topCategories.joined(separator: ", "))
        
        请生成包含以下结构的JSON报告：
        {
          "summary": {
            "totalProducts": number,
            "totalSpending": number,
            "averageWorthiness": number,
            "topPerformingCategory": string,
            "keyHighlight": string,
            "overallTrend": string,
            "monthlyGrowth": number
          },
          "insights": [
            {
              "title": string,
              "description": string,
              "category": string,
              "importance": string
            }
          ],
          "recommendations": [
            {
              "title": string,
              "description": string,
              "actionType": string,
              "priority": string,
              "steps": [string]
            }
          ]
        }
        """
    }
    
    /// 解析月度报告
    func parseMonthlyReport(_ jsonContent: String) throws -> MonthlyReport {
        // 这里实现JSON解析逻辑
        // 目前返回示例报告
        let summary = ReportSummary(
            totalProducts: 10,
            totalSpending: 5000.0,
            averageWorthiness: 75.0,
            topPerformingCategory: "电子产品",
            keyHighlight: "本月消费理性，价值度提升",
            overallTrend: .stable,
            monthlyGrowth: 5.2
        )
        
        return MonthlyReport(
            month: Date(),
            summary: summary,
            sections: [],
            insights: [],
            recommendations: [],
            metrics: ReportMetrics(
                totalProducts: 10,
                newProducts: 2,
                disposedProducts: 1,
                totalSpending: 5000.0,
                averageProductValue: 500.0,
                topCategory: "电子产品",
                averageWorthiness: 75.0,
                satisfactionScore: 4.2,
                usageEfficiency: 80.0,
                monthlyGrowthRate: 5.2,
                portfolioHealth: 78.0
            )
        )
    }
    
    /// 加载对话历史
    func loadConversationHistory() {
        // 如果有当前会话ID，加载该会话的消息
        if let conversationId = currentConversationId {
            loadConversation(conversationId: conversationId)
        } else {
            // 否则创建新会话或加载最近的会话
            let recentConversations = conversationRepository.fetchRecentConversations(limit: 1)
            if let latestConversation = recentConversations.first {
                loadConversation(conversationId: latestConversation.id!)
            }
        }
    }

    /// 保存对话历史
    public func saveConversationHistory() {
        // 更新当前会话的最后消息时间
        if let conversationId = currentConversationId {
            _ = conversationRepository.updateLastMessageDate(conversationId: conversationId)
        }
    }
    
    /// 设置成本追踪
    func setupCostTracking() {
        costTracker.setBudgetLimit(monthlyBudgetLimit)
    }

    // MARK: - 会话管理方法

    /// 创建新会话
    public func createNewConversation(title: String? = nil) -> UUID? {
        let conversationTitle = title ?? generateConversationTitle()

        if let conversation = conversationRepository.createConversation(title: conversationTitle) {
            currentConversationId = conversation.id
            conversationHistory.removeAll()
            return conversation.id
        }
        return nil
    }

    /// 加载指定会话
    public func loadConversation(conversationId: UUID) {
        currentConversationId = conversationId

        // 从数据库加载消息
        let messages = messageRepository.fetchMessagesByConversation(conversationId: conversationId)
        conversationHistory = messageRepository.convertToConversationMessages(messages)
    }

    /// 删除会话
    func deleteConversation(conversationId: UUID) -> Bool {
        let success = conversationRepository.deleteConversation(conversationId: conversationId)

        // 如果删除的是当前会话，清空历史
        if success && currentConversationId == conversationId {
            currentConversationId = nil
            conversationHistory.removeAll()
        }

        return success
    }

    /// 重命名会话
    public func renameConversation(conversationId: UUID, newTitle: String) -> Bool {
        return conversationRepository.updateConversationTitle(conversationId: conversationId, newTitle: newTitle)
    }

    /// 获取所有会话
    public func getAllConversations() -> [Conversation] {
        return conversationRepository.fetchAllConversations()
    }

    /// 生成会话标题
    private func generateConversationTitle() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        return "对话 - \(formatter.string(from: Date()))"
    }

    /// 保存用户消息到数据库
    private func saveUserMessage(_ message: ConversationMessage) {
        ensureCurrentConversation()

        guard let conversationId = currentConversationId else { return }

        _ = messageRepository.addMessage(
            to: conversationId,
            text: message.content,
            isFromUser: true,
            role: message.role,
            analysisContext: message.analysisContext,
            attachments: message.attachments
        )
    }

    /// 保存助手消息到数据库
    private func saveAssistantMessage(_ message: ConversationMessage) {
        ensureCurrentConversation()

        guard let conversationId = currentConversationId else { return }

        _ = messageRepository.addMessage(
            to: conversationId,
            text: message.content,
            isFromUser: false,
            role: message.role,
            analysisContext: message.analysisContext,
            attachments: message.attachments
        )
    }

    /// 确保存在当前会话
    private func ensureCurrentConversation() {
        if currentConversationId == nil {
            currentConversationId = createNewConversation()
        }
    }

    // MARK: - 智能问题路由方法

    /// 根据问题分析结果收集相关数据
    func gatherRelevantData(for analysis: QuestionAnalysisResult) async -> RelevantDataContext {
        // 根据问题类型获取最相关的数据类型
        let primaryDataType = analysis.suggestedDataTypes.first ?? .general

        // 使用AIDataContextBuilder获取相关数据
        return await dataContextBuilder.buildRelevantDataContext(for: primaryDataType)
    }

    /// 构建包含相关数据的系统提示
    func buildSystemPrompt(with relevantData: RelevantDataContext) async -> String {
        var prompt = """
        你是PinkLog应用的智能分析助手，专门帮助用户理解和优化他们的消费决策。

        你的核心能力：
        1. 解读三曲线分析结果（价值实现、使用活力、情感依恋）
        2. 提供个性化的消费建议和优化策略
        3. 回答关于产品价值、使用模式、情感连接的问题
        4. 帮助用户做出更明智的购买和处置决策

        回答风格：
        - 专业但易懂，避免过于技术化的术语
        - 提供具体可行的建议
        - 基于数据分析，但考虑用户的情感需求
        - 简洁明了，重点突出

        """

        // 添加针对性的数据上下文
        prompt += buildRelevantDataPrompt(relevantData)

        // 添加通用用户数据上下文（简化版）
        let userDataContext = await dataContextBuilder.buildUserDataContext()
        prompt += buildSimplifiedUserDataPrompt(userDataContext)

        // 添加当前分析上下文
        if let context = currentContext {
            prompt += await buildContextualPrompt(context)
        }

        return prompt
    }

    /// 构建相关数据提示
    func buildRelevantDataPrompt(_ relevantData: RelevantDataContext) -> String {
        var dataPrompt = "\n针对性数据分析（\(relevantData.contextType.description)）：\n"

        // 添加关键指标
        if !relevantData.metrics.isEmpty {
            dataPrompt += "关键指标：\n"
            for (key, value) in relevantData.metrics {
                dataPrompt += "- \(key): \(String(format: "%.1f", value))\n"
            }
        }

        // 添加相关产品
        if !relevantData.products.isEmpty {
            let productNames = relevantData.products.prefix(5).map { $0.name }
            dataPrompt += "相关产品：\(productNames.joined(separator: "、"))\n"
        }

        // 添加洞察
        if !relevantData.insights.isEmpty {
            dataPrompt += "数据洞察：\n"
            for insight in relevantData.insights {
                dataPrompt += "- \(insight)\n"
            }
        }

        dataPrompt += "\n请重点基于以上针对性数据回答用户问题。\n"

        return dataPrompt
    }

    /// 构建简化的用户数据提示
    func buildSimplifiedUserDataPrompt(_ context: UserDataContext) -> String {
        var dataPrompt = "\n用户概况：\n"
        dataPrompt += "- 总产品：\(context.totalProducts)个，总投资：\(context.formattedInvestment)\n"

        if !context.topCategories.isEmpty {
            dataPrompt += "- 主要类别：\(context.topCategories.prefix(2).joined(separator: "、"))\n"
        }

        if !context.topPerformingProducts.isEmpty {
            dataPrompt += "- 表现最佳：\(context.topPerformingProducts.first?.name ?? "无")\n"
        }

        return dataPrompt
    }

    /// 构建完整的用户数据提示（包含所有字段）
    func buildCompleteUserDataPrompt(_ context: CompleteUserDataContext) -> String {
        var dataPrompt = "\n=== 完整用户数据分析 ===\n"

        // 基础统计
        dataPrompt += "\n【基础统计】\n"
        dataPrompt += "- 总产品数：\(context.totalProducts)个\n"
        dataPrompt += "- 总投资金额：\(context.formattedTotalInvestment)\n"
        dataPrompt += "- 总使用记录：\(context.totalUsageRecords)条\n"
        dataPrompt += "- 总相关费用：\(context.formattedTotalExpenses)\n"
        dataPrompt += "- 平均价值指数：\(String(format: "%.1f", context.averageWorthIndex))分\n"
        dataPrompt += "- 平均满意度：\(String(format: "%.1f", context.averageSatisfaction))分\n"
        dataPrompt += "- 数据完整性：\(String(format: "%.1f", context.dataCompletenessScore))%\n"

        // 产品详细信息
        if !context.products.isEmpty {
            dataPrompt += "\n【产品详细信息】\n"
            for (index, product) in context.products.prefix(10).enumerated() {
                dataPrompt += "\n\(index + 1). \(product.name)\n"
                dataPrompt += "   - 品牌：\(product.brand ?? "未知")\n"
                dataPrompt += "   - 型号：\(product.model ?? "未知")\n"
                dataPrompt += "   - 价格：\(product.formattedPrice)\n"
                dataPrompt += "   - 购买日期：\(product.purchaseDateString)\n"
                dataPrompt += "   - 类别：\(product.categoryName ?? "未分类")\n"
                dataPrompt += "   - 购买渠道：\(product.purchaseChannel ?? "未知")\n"
                dataPrompt += "   - 购买动机：\(product.purchaseMotivation ?? "未记录")\n"
                dataPrompt += "   - 使用次数：\(product.totalUsageCount)次\n"
                dataPrompt += "   - 价值指数：\(String(format: "%.1f", product.worthItIndex))分\n"
                dataPrompt += "   - 满意度：\(String(format: "%.1f", product.currentAverageSatisfaction))分\n"
                dataPrompt += "   - 使用频率：\(product.usageFrequencyDescription)\n"
                dataPrompt += "   - 满意度趋势：\(product.satisfactionTrend)\n"
                dataPrompt += "   - 拥有天数：\(product.daysSincePurchase)天\n"
                dataPrompt += "   - 总拥有成本：\(product.formattedTotalCost)\n"

                // 标签信息
                if !product.tags.isEmpty {
                    let tagNames = product.tags.map { $0.name }.joined(separator: "、")
                    dataPrompt += "   - 标签：\(tagNames)\n"
                }

                // 保修和有效期
                if let warrantyEnd = product.warrantyEndDate {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    dataPrompt += "   - 保修到期：\(formatter.string(from: warrantyEnd))\n"
                }

                if let expiryDate = product.expiryDate {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    dataPrompt += "   - 产品到期：\(formatter.string(from: expiryDate))\n"
                }

                // 借阅状态
                if product.isCurrentlyLoaned {
                    dataPrompt += "   - 当前状态：已借出\n"
                    if let loanInfo = product.activeLoanInfo {
                        dataPrompt += "   - 借阅信息：\(loanInfo)\n"
                    }
                }

                // 最近使用记录
                if !product.usageRecords.isEmpty {
                    let recentRecord = product.usageRecords.first!
                    dataPrompt += "   - 最近使用：\(recentRecord.dateString)\n"
                    dataPrompt += "   - 最近满意度：\(recentRecord.satisfactionDescription)\n"
                    if let notes = recentRecord.notes, !notes.isEmpty {
                        dataPrompt += "   - 最近使用备注：\(notes)\n"
                    }
                    if let memories = recentRecord.memories, !memories.isEmpty {
                        dataPrompt += "   - 相关回忆：\(memories)\n"
                    }
                }
            }
        }

        // 类别分析
        if !context.categories.isEmpty {
            dataPrompt += "\n【类别分析】\n"
            for category in context.categories.prefix(5) {
                dataPrompt += "- \(category.name)：\(category.productCount)个产品，总价值\(category.formattedTotalValue)，平均价值指数\(String(format: "%.1f", category.averageWorthIndex))分\n"
            }
        }

        // 最近使用记录
        if !context.usageRecords.isEmpty {
            dataPrompt += "\n【最近使用记录】\n"
            for record in context.usageRecords.prefix(10) {
                dataPrompt += "- \(record.dateString)：\(record.usageTypeDescription)\n"
                dataPrompt += "  满意度：\(record.satisfactionDescription)，情感价值：\(record.emotionalValueDescription)\n"
                if let scenario = record.scenario, !scenario.isEmpty {
                    dataPrompt += "  使用场景：\(scenario)\n"
                }
                if let notes = record.notes, !notes.isEmpty {
                    dataPrompt += "  备注：\(notes)\n"
                }
                if let memories = record.memories, !memories.isEmpty {
                    dataPrompt += "  回忆详情：\(memories)\n"
                }
            }
        }

        // 费用记录
        if !context.expenses.isEmpty {
            dataPrompt += "\n【相关费用记录】\n"
            for expense in context.expenses.prefix(10) {
                dataPrompt += "- \(expense.dateString)：\(expense.typeName) \(expense.formattedAmount)\n"
                if let notes = expense.notes, !notes.isEmpty {
                    dataPrompt += "  备注：\(notes)\n"
                }
            }
        }

        dataPrompt += "\n=== 数据分析完毕 ===\n"
        dataPrompt += "\n请基于以上详细的用户数据进行分析和回答。你可以引用具体的产品名称、使用记录、费用信息等来提供个性化的建议和洞察。\n"

        return dataPrompt
    }

    /// 构建包含完整数据的系统提示
    func buildSystemPromptWithCompleteData(
        relevantData: RelevantDataContext,
        completeContext: CompleteUserDataContext
    ) async -> String {
        var prompt = """
        你是PinkLog应用的智能分析助手，专门帮助用户理解和优化他们的消费决策。

        你的核心能力：
        1. 解读三曲线分析结果（价值实现、使用活力、情感依恋）
        2. 提供个性化的消费建议和优化策略
        3. 回答关于产品价值、使用模式、情感连接的问题
        4. 帮助用户做出更明智的购买和处置决策
        5. 基于用户的真实数据提供具体、个性化的分析

        回答风格：
        - 专业但易懂，避免过于技术化的术语
        - 提供具体可行的建议，引用具体的产品名称和数据
        - 基于数据分析，但考虑用户的情感需求
        - 简洁明了，重点突出
        - 使用用户的真实产品名称、品牌、使用记录等信息

        """

        // 添加针对性的数据上下文
        prompt += buildRelevantDataPrompt(relevantData)

        // 添加完整的用户数据上下文
        prompt += buildCompleteUserDataPrompt(completeContext)

        // 添加当前分析上下文
        if let context = currentContext {
            prompt += await buildContextualPrompt(context)
        }

        return prompt
    }

    /// 构建优化的系统提示（简洁但完整版本）
    func buildOptimizedSystemPrompt(completeContext: CompleteUserDataContext, question: String) async -> String {
        var prompt = """
        你是PinkLog消费分析助手。用户问题："\(question)"

        """

        // 添加精简但完整的核心数据
        prompt += buildCompactCoreData(completeContext)

        prompt += "\n请基于以上真实数据回答用户问题。"

        return prompt
    }

    /// 构建完全全面的核心数据（确保AI能获取所有信息）
    func buildCompactCoreData(_ context: CompleteUserDataContext) -> String {
        var data = ""

        // 1. 基础统计
        data += "=== 用户数据总览 ===\n"
        data += "总计：\(context.totalProducts)个产品，投资\(context.formattedTotalInvestment)\n"
        data += "使用记录：\(context.totalUsageRecords)条，相关费用：\(context.formattedTotalExpenses)\n"
        data += "平均价值指数：\(String(format: "%.1f", context.averageWorthIndex))分，平均满意度：\(String(format: "%.1f", context.averageSatisfaction))分\n\n"

        // 2. 所有产品的完整信息
        data += "=== 完整产品列表 ===\n"
        for product in context.products {
            data += "• \(product.name)"

            // 品牌和型号
            if let brand = product.brand, !brand.isEmpty {
                data += "[\(brand)]"
            }
            if let model = product.model, !model.isEmpty {
                data += "{\(model)}"
            }

            // 类别
            if let category = product.categoryName, !category.isEmpty {
                data += "(\(category))"
            }

            // 基础信息
            data += " ¥\(String(format: "%.0f", product.price))"
            data += " 购于\(product.purchaseDate.formatted(.dateTime.year().month().day()))"

            // 使用统计
            if product.totalUsageCount > 0 {
                data += " 用\(product.totalUsageCount)次"
            }
            if product.currentAverageSatisfaction > 0 {
                data += " 满意\(String(format: "%.1f", product.currentAverageSatisfaction))"
            }
            if product.worthItIndex > 0 {
                data += " 值\(String(format: "%.0f", product.worthItIndex))"
            }

            // 状态信息
            if product.isCurrentlyLoaned {
                data += " [借出中]"
            }
            if product.isNearWarrantyEnd {
                data += " [保修将到期]"
            }
            if product.isNearExpiry {
                data += " [即将过期]"
            }

            // 购买动机和备注
            if let motivation = product.purchaseMotivation, !motivation.isEmpty {
                data += " 动机:\(motivation)"
            }
            if let notes = product.purchaseNotes, !notes.isEmpty {
                data += " 备注:\(notes.prefix(20))"
            }

            // 标签
            if !product.tags.isEmpty {
                let tagNames = product.tags.map { $0.name }.joined(separator: ",")
                data += " 标签:[\(tagNames)]"
            }

            data += "\n"

            // 产品的费用记录
            if !product.relatedExpenses.isEmpty {
                for expense in product.relatedExpenses.prefix(3) {
                    data += "    费用: \(expense.date.formatted(.dateTime.month().day())) \(expense.typeName) ¥\(String(format: "%.0f", expense.amount))"
                    if let notes = expense.notes, !notes.isEmpty {
                        data += " \(notes.prefix(15))"
                    }
                    data += "\n"
                }
            }

            // 产品的最近使用记录
            if !product.usageRecords.isEmpty {
                for record in product.usageRecords.prefix(2) {
                    data += "    使用: \(record.date.formatted(.dateTime.month().day())) 满意\(record.satisfaction)"
                    if let scenario = record.scenario, !scenario.isEmpty {
                        data += " \(scenario)"
                    }
                    if let notes = record.notes, !notes.isEmpty {
                        data += " \(notes.prefix(15))"
                    }
                    if let memories = record.memories, !memories.isEmpty {
                        data += " 回忆:\(memories.prefix(20))"
                    }
                    data += "\n"
                }
            }
        }

        // 3. 所有使用记录（按时间排序）
        if !context.usageRecords.isEmpty {
            data += "\n=== 最近使用记录 ===\n"
            for record in context.usageRecords.prefix(8) {
                data += "• \(record.date.formatted(.dateTime.month().day())) "
                data += "满意\(record.satisfaction) 情感\(record.emotionalValue)"

                if let scenario = record.scenario, !scenario.isEmpty {
                    data += " \(scenario)"
                }
                if let notes = record.notes, !notes.isEmpty {
                    data += " \(notes.prefix(20))"
                }
                if let memories = record.memories, !memories.isEmpty {
                    data += " 回忆:\(memories.prefix(25))"
                }

                // 借阅信息
                if record.isLoanedOut {
                    data += " [借出"
                    if let borrower = record.borrowerName {
                        data += "给\(borrower)"
                    }
                    if let dueDate = record.dueDate {
                        data += " 到期\(dueDate.formatted(.dateTime.month().day()))"
                    }
                    data += "]"
                }

                data += "\n"
            }
        }

        // 4. 所有费用记录（按时间排序）
        if !context.expenses.isEmpty {
            data += "\n=== 所有费用记录 ===\n"
            for expense in context.expenses {
                data += "• \(expense.date.formatted(.dateTime.month().day())) "
                data += "\(expense.typeName) ¥\(String(format: "%.0f", expense.amount))"
                if let notes = expense.notes, !notes.isEmpty {
                    data += " \(notes)"
                }
                data += "\n"
            }
        }

        // 5. 类别统计
        if !context.categories.isEmpty {
            data += "\n=== 类别统计 ===\n"
            for category in context.categories {
                data += "• \(category.name): \(category.productCount)个产品"
                data += " 总值\(category.formattedTotalValue)"
                data += " 平均价值\(String(format: "%.1f", category.averageWorthIndex))分\n"
            }
        }

        return data
    }









    /// 构建特定产品数据
    func buildSpecificProductData(_ context: CompleteUserDataContext, products: [String]) -> String {
        var data = ""

        data += "相关产品详情：\n"
        for productName in products {
            let matchingProducts = context.products.filter {
                $0.name.lowercased().contains(productName.lowercased())
            }

            for product in matchingProducts.prefix(2) {
                data += "• \(product.name)"
                if let brand = product.brand, !brand.isEmpty {
                    data += "(\(brand))"
                }
                data += " - \(product.formattedPrice)"
                if let category = product.categoryName {
                    data += ", \(category)"
                }
                if product.totalUsageCount > 0 {
                    data += ", 使用\(product.totalUsageCount)次"
                }
                data += "\n"
            }
        }

        return data
    }

    /// 构建特定类别数据
    func buildSpecificCategoryData(_ context: CompleteUserDataContext, categories: [String]) -> String {
        var data = ""

        data += "相关类别产品：\n"
        for category in categories {
            let categoryProducts = context.products.filter { product in
                if let productCategory = product.categoryName?.lowercased() {
                    return productCategory.contains(category.lowercased()) || category.lowercased().contains(productCategory)
                }
                return false
            }

            if !categoryProducts.isEmpty {
                data += "【\(category.uppercased())类别】\n"
                for product in categoryProducts.prefix(5) {
                    data += "• \(product.name)"
                    if let brand = product.brand, !brand.isEmpty {
                        data += "(\(brand))"
                    }
                    data += " - \(product.formattedPrice)\n"
                }
            }
        }

        return data
    }

    /// 构建完整品牌索引
    func buildCompleteBrandIndex(_ context: CompleteUserDataContext) -> String {
        var data = "所有品牌：\n"

        let brandGroups = Dictionary(grouping: context.products) {
            $0.brand?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "未知品牌"
        }

        for (brand, products) in brandGroups.sorted(by: { $0.key < $1.key }) {
            data += "• \(brand): \(products.count)个产品\n"
        }

        return data
    }

    /// 构建完整产品列表
    func buildCompleteProductList(_ context: CompleteUserDataContext) -> String {
        var data = "主要产品：\n"

        for product in context.products.prefix(8) {
            data += "• \(product.name)"
            if let brand = product.brand, !brand.isEmpty {
                data += "(\(brand))"
            }
            if let category = product.categoryName {
                data += " - \(category)"
            }
            data += "\n"
        }

        return data
    }

    /// 构建类别特定数据
    func buildCategorySpecificData(_ context: CompleteUserDataContext, question: String) -> String {
        var data = ""

        // 提取问题中提到的类别
        let mentionedCategories = extractMentionedCategories(from: question, in: context)

        if !mentionedCategories.isEmpty {
            data += "相关类别产品：\n"
            for category in mentionedCategories {
                let categoryProducts = context.products.filter {
                    $0.categoryName?.lowercased().contains(category.lowercased()) == true
                }
                for product in categoryProducts.prefix(3) {
                    data += "• \(product.name) - \(product.formattedPrice), 使用\(product.totalUsageCount)次\n"
                }
            }
        } else {
            // 显示主要类别
            for category in context.categories.prefix(3) {
                data += "• \(category.name): \(category.productCount)个产品, 总值\(category.formattedTotalValue)\n"
            }
        }

        return data
    }

    /// 构建表现数据
    func buildPerformanceData(_ context: CompleteUserDataContext) -> String {
        let topProducts = context.products
            .sorted { $0.worthItIndex > $1.worthItIndex }
            .prefix(5)

        var data = "表现最佳产品：\n"
        for product in topProducts {
            data += "• \(product.name) - 价值\(String(format: "%.0f", product.worthItIndex))分, 满意度\(String(format: "%.1f", product.currentAverageSatisfaction))\n"
        }

        return data
    }

    /// 构建消费数据
    func buildConsumptionData(_ context: CompleteUserDataContext) -> String {
        var data = "消费概况：\n"
        data += "• 平均产品价值：¥\(String(format: "%.0f", context.totalInvestment / Double(max(context.totalProducts, 1))))\n"
        data += "• 使用记录：\(context.totalUsageRecords)条\n"
        data += "• 相关费用：\(context.formattedTotalExpenses)\n"

        return data
    }

    /// 构建价值数据
    func buildValueData(_ context: CompleteUserDataContext) -> String {
        var data = "价值分析：\n"
        data += "• 平均价值指数：\(String(format: "%.1f", context.averageWorthIndex))分\n"
        data += "• 平均满意度：\(String(format: "%.1f", context.averageSatisfaction))分\n"

        let highValueProducts = context.products.filter { $0.worthItIndex >= 80 }
        data += "• 高价值产品(\(highValueProducts.count)个)：\(highValueProducts.prefix(3).map { $0.name }.joined(separator: "、"))\n"

        return data
    }

    /// 构建Top产品数据
    func buildTopProductsData(_ context: CompleteUserDataContext) -> String {
        let topProducts = context.products
            .sorted { $0.worthItIndex > $1.worthItIndex }
            .prefix(3)

        var data = "主要产品：\n"
        for product in topProducts {
            data += "• \(product.name)(\(product.brand ?? "")) - \(product.formattedPrice)\n"
        }

        return data
    }

    /// 提取问题中提到的品牌
    func extractMentionedBrands(from question: String, in context: CompleteUserDataContext) -> [String] {
        let allBrands = Set(context.products.compactMap { $0.brand?.lowercased() })
        return allBrands.filter { question.lowercased().contains($0) }
    }

    /// 提取问题中提到的类别
    func extractMentionedCategories(from question: String, in context: CompleteUserDataContext) -> [String] {
        let allCategories = Set(context.products.compactMap { $0.categoryName?.lowercased() })
        let categoryKeywords = ["鞋", "电子", "手机", "电脑", "衣服", "家居", "运动"]

        var mentioned: [String] = []

        // 检查直接类别名称
        for category in allCategories {
            if question.lowercased().contains(category) {
                mentioned.append(category)
            }
        }

        // 检查类别关键词
        for keyword in categoryKeywords {
            if question.contains(keyword) {
                mentioned.append(keyword)
            }
        }

        return mentioned
    }

    /// 构建品牌和产品快速索引
    func buildBrandAndProductIndex(_ context: CompleteUserDataContext) -> String {
        var indexPrompt = "\n=== 品牌和产品快速索引 ===\n"

        // 按品牌分组
        let productsByBrand = Dictionary(grouping: context.products) { product in
            product.brand?.lowercased() ?? "未知品牌"
        }

        indexPrompt += "\n【品牌索引】\n"
        for (brand, products) in productsByBrand.sorted(by: { $0.key < $1.key }) {
            indexPrompt += "- \(brand.capitalized)：\(products.count)个产品\n"
            for product in products {
                indexPrompt += "  • \(product.name)"
                if let model = product.model, !model.isEmpty {
                    indexPrompt += " (\(model))"
                }
                indexPrompt += " - \(product.categoryName ?? "未分类")\n"
            }
        }

        // 按类别分组
        let productsByCategory = Dictionary(grouping: context.products) { product in
            product.categoryName?.lowercased() ?? "未分类"
        }

        indexPrompt += "\n【类别索引】\n"
        for (category, products) in productsByCategory.sorted(by: { $0.key < $1.key }) {
            indexPrompt += "- \(category.capitalized)：\(products.count)个产品\n"
            for product in products {
                indexPrompt += "  • \(product.name)"
                if let brand = product.brand, !brand.isEmpty {
                    indexPrompt += " (\(brand))"
                }
                indexPrompt += "\n"
            }
        }

        // 关键词索引
        indexPrompt += "\n【关键词索引】\n"
        var allKeywords: Set<String> = []

        for product in context.products {
            // 添加产品名称的关键词
            let nameWords = product.name.lowercased().components(separatedBy: CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters))
            allKeywords.formUnion(nameWords.filter { !$0.isEmpty && $0.count > 1 })

            // 添加品牌关键词
            if let brand = product.brand?.lowercased(), !brand.isEmpty {
                allKeywords.insert(brand)
            }

            // 添加型号关键词
            if let model = product.model?.lowercased(), !model.isEmpty {
                let modelWords = model.components(separatedBy: CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters))
                allKeywords.formUnion(modelWords.filter { !$0.isEmpty && $0.count > 1 })
            }

            // 添加标签关键词
            for tag in product.tags {
                allKeywords.insert(tag.name.lowercased())
            }
        }

        let sortedKeywords = Array(allKeywords).sorted()
        for keyword in sortedKeywords {
            let matchingProducts = context.products.filter { product in
                product.name.lowercased().contains(keyword) ||
                product.brand?.lowercased().contains(keyword) == true ||
                product.model?.lowercased().contains(keyword) == true ||
                product.tags.contains { $0.name.lowercased().contains(keyword) }
            }

            if !matchingProducts.isEmpty {
                indexPrompt += "- \(keyword)：\(matchingProducts.map { $0.name }.joined(separator: "、"))\n"
            }
        }

        indexPrompt += "\n=== 索引完毕 ===\n"
        indexPrompt += "请使用以上索引来准确识别用户询问的产品和品牌。\n"

        return indexPrompt
    }
}

// MARK: - 成本追踪器
class CostTracker: ObservableObject {
    @Published var totalCost: Double = 0.0
    @Published var monthlyBudget: Double = 50.0
    @Published var usageHistory: [AIUsageRecord] = []

    var isOverBudget: Bool {
        return totalCost >= monthlyBudget
    }

    var remainingBudget: Double {
        return max(0, monthlyBudget - totalCost)
    }

    func addUsage(inputTokens: Int, outputTokens: Int, model: DeepSeekModel) {
        let cost = calculateCost(inputTokens: inputTokens, outputTokens: outputTokens, model: model)
        totalCost += cost

        let record = AIUsageRecord(
            timestamp: Date(),
            inputTokens: inputTokens,
            outputTokens: outputTokens,
            cost: cost,
            model: model
        )
        usageHistory.append(record)
    }

    func setBudgetLimit(_ limit: Double) {
        monthlyBudget = limit
    }

    func reset() {
        totalCost = 0.0
        usageHistory.removeAll()
    }

    func getStatistics() -> CostStatistics {
        return CostStatistics(
            totalCost: totalCost,
            totalRequests: usageHistory.count,
            averageCostPerRequest: usageHistory.isEmpty ? 0 : totalCost / Double(usageHistory.count),
            budgetUtilization: totalCost / monthlyBudget
        )
    }

    private func calculateCost(inputTokens: Int, outputTokens: Int, model: DeepSeekModel) -> Double {
        let inputCostPer1M: Double
        let outputCostPer1M: Double

        switch model {
        case .chat:
            inputCostPer1M = 2.0
            outputCostPer1M = 8.0
        case .reasoner:
            inputCostPer1M = 4.0
            outputCostPer1M = 16.0
        }

        let inputCost = Double(inputTokens) * inputCostPer1M / 1_000_000
        let outputCost = Double(outputTokens) * outputCostPer1M / 1_000_000

        return inputCost + outputCost
    }
}

// MARK: - AI使用记录
struct AIUsageRecord: Identifiable {
    let id = UUID()
    let timestamp: Date
    let inputTokens: Int
    let outputTokens: Int
    let cost: Double
    let model: DeepSeekModel
}

// MARK: - 成本统计
struct CostStatistics {
    let totalCost: Double
    let totalRequests: Int
    let averageCostPerRequest: Double
    let budgetUtilization: Double

    var budgetUtilizationPercentage: String {
        return String(format: "%.1f%%", budgetUtilization * 100)
    }
}

// MARK: - 离线助手
class OfflineAssistant {

    func processQuestion(_ question: String, context: AnalysisContext?) -> AssistantResponse {
        // 基于规则的简单回答
        let content = generateOfflineResponse(question: question, context: context)

        return AssistantResponse(
            content: content,
            confidence: 0.6,
            suggestions: [],
            relatedInsights: [],
            followUpQuestions: [
                "您可以查看详细的分析数据获取更多信息",
                "建议配置AI助手以获得更专业的建议"
            ]
        )
    }

    func generateBasicReport(for month: Date) -> MonthlyReport {
        // 生成基础报告
        let summary = ReportSummary(
            totalProducts: 0,
            totalSpending: 0.0,
            averageWorthiness: 0.0,
            topPerformingCategory: "暂无数据",
            keyHighlight: "请配置AI助手以生成详细报告",
            overallTrend: .stable,
            monthlyGrowth: 0.0
        )

        return MonthlyReport(
            month: month,
            summary: summary,
            sections: [],
            insights: [],
            recommendations: [],
            metrics: ReportMetrics(
                totalProducts: 0,
                newProducts: 0,
                disposedProducts: 0,
                totalSpending: 0.0,
                averageProductValue: 0.0,
                topCategory: "暂无",
                averageWorthiness: 0.0,
                satisfactionScore: 0.0,
                usageEfficiency: 0.0,
                monthlyGrowthRate: 0.0,
                portfolioHealth: 0.0
            )
        )
    }

    private func generateOfflineResponse(question: String, context: AnalysisContext?) -> String {
        let lowercaseQuestion = question.lowercased()

        if lowercaseQuestion.contains("价值") || lowercaseQuestion.contains("值得") {
            return "根据您的使用数据，建议查看产品的三曲线分析图表，重点关注价值实现曲线的趋势。"
        } else if lowercaseQuestion.contains("使用") || lowercaseQuestion.contains("频率") {
            return "使用频率是评估产品价值的重要指标。建议查看使用活力分析，了解产品的活跃度变化。"
        } else if lowercaseQuestion.contains("建议") || lowercaseQuestion.contains("推荐") {
            return "基于当前数据，建议定期回顾产品使用情况，及时处置低价值产品，优化消费决策。"
        } else {
            return "抱歉，离线模式下功能有限。建议配置AI助手以获得更专业和个性化的分析建议。"
        }
    }
}

// MARK: - 对话上下文管理器
class ConversationContextManager {
    private var contextHistory: [AnalysisContext] = []

    func addContext(_ context: AnalysisContext) {
        contextHistory.append(context)

        // 保持最近10个上下文
        if contextHistory.count > 10 {
            contextHistory.removeFirst()
        }
    }

    func getRecentContexts() -> [AnalysisContext] {
        return Array(contextHistory.suffix(5))
    }

    func clearHistory() {
        contextHistory.removeAll()
    }
}

// MARK: - 月度数据
struct MonthlyData {
    let totalProducts: Int
    let totalSpending: Double
    let averageWorthiness: Double
    let topCategories: [String]
    let usagePatterns: [String: Double]
    let satisfactionScores: [Double]
}

// MARK: - 月度报告生成器
class MonthlyReportGenerator {

    // MARK: - 依赖注入
    private let productRepository: ProductRepository
    private let usageRepository: UsageRecordRepository
    private let expenseRepository: RelatedExpenseRepository
    private let overviewEngine: AdvancedOverviewEngine

    // MARK: - 初始化
    init(
        productRepository: ProductRepository,
        usageRepository: UsageRecordRepository,
        expenseRepository: RelatedExpenseRepository,
        overviewEngine: AdvancedOverviewEngine
    ) {
        self.productRepository = productRepository
        self.usageRepository = usageRepository
        self.expenseRepository = expenseRepository
        self.overviewEngine = overviewEngine
    }

    func collectMonthlyData(for month: Date) async -> MonthlyData {
        // 获取所有产品数据
        let products = productRepository.fetchAll()

        // 使用AdvancedOverviewEngine生成概览数据
        do {
            let overviewMetrics = try await overviewEngine.generateOverview(for: products)

            // 计算平均价值度
            let averageWorthiness = calculateAverageWorthiness(products)

            // 提取主要类别
            let topCategories = extractTopCategories(overviewMetrics.categoryDistribution)

            // 提取使用模式
            let usagePatterns = extractUsagePatterns(products)

            // 提取满意度评分
            let satisfactionScores = extractSatisfactionScores(products)

            return MonthlyData(
                totalProducts: overviewMetrics.basicStats.totalProductsCount,
                totalSpending: overviewMetrics.basicStats.totalInvestment,
                averageWorthiness: averageWorthiness,
                topCategories: topCategories,
                usagePatterns: usagePatterns,
                satisfactionScores: satisfactionScores
            )

        } catch {
            // 如果AdvancedOverviewEngine失败，返回基础统计数据
            print("AdvancedOverviewEngine failed: \(error), falling back to basic stats")
            return await collectBasicMonthlyData(products: products)
        }
    }

    // MARK: - 辅助方法

    /// 计算平均价值度
    private func calculateAverageWorthiness(_ products: [Product]) -> Double {
        guard !products.isEmpty else { return 0.0 }

        let worthIndexValues = products.map { $0.worthItIndex() }
        return worthIndexValues.reduce(0, +) / Double(worthIndexValues.count)
    }

    /// 提取主要类别
    private func extractTopCategories(_ categoryDistribution: [UUID: CategoryStats]) -> [String] {
        let sortedCategories = categoryDistribution.values
            .sorted { $0.productCount > $1.productCount }
            .prefix(3)
            .map { $0.categoryName }

        return Array(sortedCategories)
    }

    /// 提取使用模式
    private func extractUsagePatterns(_ products: [Product]) -> [String: Double] {
        var patterns: [String: Double] = [:]
        let calendar = Calendar.current
        let now = Date()

        var dailyUsage = 0
        var weeklyUsage = 0
        var monthlyUsage = 0
        var totalUsage = 0

        for product in products {
            let usageRecords = (product.usageRecords?.allObjects as? [UsageRecord]) ?? []
            totalUsage += usageRecords.count

            for record in usageRecords {
                guard let date = record.date else { continue }
                let daysSince = calendar.dateComponents([.day], from: date, to: now).day ?? 0

                if daysSince <= 1 {
                    dailyUsage += 1
                } else if daysSince <= 7 {
                    weeklyUsage += 1
                } else if daysSince <= 30 {
                    monthlyUsage += 1
                }
            }
        }

        if totalUsage > 0 {
            patterns["daily"] = Double(dailyUsage) / Double(totalUsage)
            patterns["weekly"] = Double(weeklyUsage) / Double(totalUsage)
            patterns["monthly"] = Double(monthlyUsage) / Double(totalUsage)
        } else {
            patterns["daily"] = 0.0
            patterns["weekly"] = 0.0
            patterns["monthly"] = 0.0
        }

        return patterns
    }

    /// 提取满意度评分
    private func extractSatisfactionScores(_ products: [Product]) -> [Double] {
        var scores: [Double] = []

        for product in products {
            let usageRecords = (product.usageRecords?.allObjects as? [UsageRecord]) ?? []
            let satisfactionRatings = usageRecords.compactMap { record in
                record.satisfaction > 0 ? Double(record.satisfaction) : nil
            }

            if !satisfactionRatings.isEmpty {
                let averageSatisfaction = satisfactionRatings.reduce(0, +) / Double(satisfactionRatings.count)
                scores.append(averageSatisfaction)
            }
        }

        return scores
    }

    /// 收集基础月度数据（降级方案）
    private func collectBasicMonthlyData(products: [Product]) async -> MonthlyData {
        let totalProducts = products.count
        let totalSpending = products.reduce(0) { $0 + $1.price }
        let averageWorthiness = calculateAverageWorthiness(products)

        // 基础类别统计
        let categoryGroups = Dictionary(grouping: products) { $0.category?.name ?? "未分类" }
        let topCategories = categoryGroups
            .sorted { $0.value.count > $1.value.count }
            .prefix(3)
            .map { $0.key }

        let usagePatterns = extractUsagePatterns(products)
        let satisfactionScores = extractSatisfactionScores(products)

        return MonthlyData(
            totalProducts: totalProducts,
            totalSpending: totalSpending,
            averageWorthiness: averageWorthiness,
            topCategories: Array(topCategories),
            usagePatterns: usagePatterns,
            satisfactionScores: satisfactionScores
        )
    }
}
