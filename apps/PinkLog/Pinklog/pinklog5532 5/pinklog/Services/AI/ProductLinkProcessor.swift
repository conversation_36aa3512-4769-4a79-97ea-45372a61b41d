import Foundation
import SwiftUI

// MARK: - 产品链接匹配结果
struct ProductLinkMatch {
    let product: Product
    let range: Range<String.Index>
    let matchedText: String
    let confidence: Double
}

// MARK: - 产品链接处理器
class ProductLinkProcessor {
    
    // MARK: - 配置
    private struct Config {
        static let minimumMatchLength = 2
        static let minimumConfidence = 0.6
        static let maxLinksPerMessage = 10
    }
    
    // MARK: - 公共方法
    
    /// 处理文本中的产品名称，将其转换为可点击的链接
    /// - Parameters:
    ///   - text: 原始文本
    ///   - products: 产品列表
    /// - Returns: 处理后的Markdown文本
    static func processProductLinks(in text: String, with products: [Product]) -> String {
        guard !text.isEmpty && !products.isEmpty else { return text }
        
        // 1. 找到所有可能的产品匹配
        let matches = findProductMatches(in: text, with: products)
        
        // 2. 过滤和排序匹配结果
        let filteredMatches = filterAndSortMatches(matches)
        
        // 3. 应用链接转换
        return applyProductLinks(to: text, with: filteredMatches)
    }
    
    /// 解析产品链接URL
    /// - Parameter url: 产品链接URL
    /// - Returns: 产品的objectID字符串（如果是有效的产品链接）
    static func parseProductURL(_ url: URL) -> String? {
        guard url.scheme == "pinklog",
              url.host == "product",
              url.pathComponents.count >= 2 else {
            return nil
        }
        return url.pathComponents[1] // 获取产品ID部分
    }

    /// 创建产品链接URL
    /// - Parameter product: 产品对象
    /// - Returns: 产品链接URL
    static func createProductURL(for product: Product) -> URL {
        let productId = product.objectID.uriRepresentation().absoluteString.replacingOccurrences(of: "/", with: "_")
        return URL(string: "pinklog://product/\(productId)")!
    }
    
    // MARK: - 私有方法
    
    /// 在文本中查找产品匹配
    private static func findProductMatches(in text: String, with products: [Product]) -> [ProductLinkMatch] {
        var matches: [ProductLinkMatch] = []
        let lowercaseText = text.lowercased()
        
        for product in products {
            guard let productName = product.name, !productName.isEmpty else { continue }
            
            // 精确匹配
            if let exactMatch = findExactMatch(productName: productName, in: text, lowercaseText: lowercaseText) {
                matches.append(ProductLinkMatch(
                    product: product,
                    range: exactMatch.range,
                    matchedText: exactMatch.text,
                    confidence: 1.0
                ))
            }
            
            // 模糊匹配（如果没有精确匹配）
            else if let fuzzyMatch = findFuzzyMatch(productName: productName, in: text, lowercaseText: lowercaseText) {
                matches.append(ProductLinkMatch(
                    product: product,
                    range: fuzzyMatch.range,
                    matchedText: fuzzyMatch.text,
                    confidence: fuzzyMatch.confidence
                ))
            }
        }
        
        return matches
    }
    
    /// 精确匹配产品名称
    private static func findExactMatch(productName: String, in text: String, lowercaseText: String) -> (range: Range<String.Index>, text: String)? {
        let lowercaseProductName = productName.lowercased()
        
        guard let range = lowercaseText.range(of: lowercaseProductName) else { return nil }
        
        // 检查边界，确保是完整的词
        let isValidMatch = isWordBoundary(at: range.lowerBound, in: lowercaseText, direction: .before) &&
                          isWordBoundary(at: range.upperBound, in: lowercaseText, direction: .after)
        
        guard isValidMatch else { return nil }
        
        let matchedText = String(text[range])
        return (range: range, text: matchedText)
    }
    
    /// 模糊匹配产品名称
    private static func findFuzzyMatch(productName: String, in text: String, lowercaseText: String) -> (range: Range<String.Index>, text: String, confidence: Double)? {
        let lowercaseProductName = productName.lowercased()
        let words = lowercaseProductName.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        
        guard words.count > 1 else { return nil } // 只对多词产品名称进行模糊匹配
        
        // 尝试匹配主要词汇
        let mainWords = words.filter { $0.count >= Config.minimumMatchLength }
        guard !mainWords.isEmpty else { return nil }
        
        var bestMatch: (range: Range<String.Index>, text: String, confidence: Double)?
        
        for word in mainWords {
            if let range = lowercaseText.range(of: word) {
                let isValidMatch = isWordBoundary(at: range.lowerBound, in: lowercaseText, direction: .before) &&
                                  isWordBoundary(at: range.upperBound, in: lowercaseText, direction: .after)
                
                if isValidMatch {
                    let confidence = calculateFuzzyConfidence(word: word, productName: lowercaseProductName)
                    if confidence >= Config.minimumConfidence {
                        let matchedText = String(text[range])
                        let match = (range: range, text: matchedText, confidence: confidence)
                        
                        if bestMatch == nil || confidence > bestMatch!.confidence {
                            bestMatch = match
                        }
                    }
                }
            }
        }
        
        return bestMatch
    }
    
    /// 计算模糊匹配的置信度
    private static func calculateFuzzyConfidence(word: String, productName: String) -> Double {
        let wordLength = Double(word.count)
        let productNameLength = Double(productName.count)
        
        // 基础置信度：匹配词长度 / 产品名称长度
        let baseConfidence = wordLength / productNameLength
        
        // 调整因子：较长的词获得更高的置信度
        let lengthBonus = min(wordLength / 10.0, 0.3)
        
        return min(baseConfidence + lengthBonus, 1.0)
    }
    
    /// 边界方向枚举
    private enum BoundaryDirection {
        case before, after
    }

    /// 检查词边界
    private static func isWordBoundary(at index: String.Index, in text: String, direction: BoundaryDirection) -> Bool {
        
        let checkIndex: String.Index
        
        switch direction {
        case .before:
            guard index > text.startIndex else { return true }
            checkIndex = text.index(before: index)
        case .after:
            guard index < text.endIndex else { return true }
            checkIndex = index
        }
        
        let character = text[checkIndex]
        return character.isWhitespace || character.isPunctuation || character.isNewline
    }
    
    /// 过滤和排序匹配结果
    private static func filterAndSortMatches(_ matches: [ProductLinkMatch]) -> [ProductLinkMatch] {
        // 1. 按置信度过滤
        let filteredMatches = matches.filter { $0.confidence >= Config.minimumConfidence }
        
        // 2. 解决重叠冲突（选择置信度最高的）
        let nonOverlappingMatches = resolveOverlaps(in: filteredMatches)
        
        // 3. 限制数量并按位置排序
        return Array(nonOverlappingMatches
            .sorted { $0.range.lowerBound < $1.range.lowerBound }
            .prefix(Config.maxLinksPerMessage))
    }
    
    /// 解决重叠的匹配
    private static func resolveOverlaps(in matches: [ProductLinkMatch]) -> [ProductLinkMatch] {
        guard matches.count > 1 else { return matches }
        
        let sortedMatches = matches.sorted { $0.confidence > $1.confidence }
        var result: [ProductLinkMatch] = []
        
        for match in sortedMatches {
            let hasOverlap = result.contains { existingMatch in
                match.range.overlaps(existingMatch.range)
            }
            
            if !hasOverlap {
                result.append(match)
            }
        }
        
        return result
    }
    
    /// 应用产品链接到文本
    private static func applyProductLinks(to text: String, with matches: [ProductLinkMatch]) -> String {
        guard !matches.isEmpty else { return text }
        
        var result = text
        
        // 按位置顺序处理匹配（从后往前，避免索引偏移问题）
        let sortedMatches = matches.sorted { $0.range.lowerBound > $1.range.lowerBound }
        
        for match in sortedMatches {
            let productURL = createProductURL(for: match.product)
            let linkText = "[\(match.matchedText)](\(productURL.absoluteString))"
            
            // 调整范围以考虑之前的修改
            let adjustedRange = Range(
                uncheckedBounds: (
                    lower: result.index(result.startIndex, offsetBy: result.distance(from: text.startIndex, to: match.range.lowerBound)),
                    upper: result.index(result.startIndex, offsetBy: result.distance(from: text.startIndex, to: match.range.upperBound))
                )
            )
            
            result.replaceSubrange(adjustedRange, with: linkText)
        }
        
        return result
    }
}

// MARK: - 扩展方法
extension ProductLinkProcessor {
    
    /// 从产品列表中查找指定ID的产品
    static func findProduct(by id: String, in products: [Product]) -> Product? {
        // 通过objectID的字符串表示匹配
        let searchId = id.replacingOccurrences(of: "_", with: "/")
        return products.first { product in
            product.objectID.uriRepresentation().absoluteString == searchId
        }
    }
    
    /// 获取产品的显示名称
    static func getDisplayName(for product: Product) -> String {
        return product.name ?? "未知产品"
    }
}
