import Foundation
import CoreData
import Combine

/// AI数据上下文构建器
/// 专门负责为AI助手提供结构化的用户数据上下文
@MainActor
class AIDataContextBuilder: ObservableObject {
    
    // MARK: - 依赖注入
    private let productRepository: ProductRepository
    private let usageRepository: UsageRecordRepository
    private let expenseRepository: RelatedExpenseRepository
    private let overviewEngine: AdvancedOverviewEngine
    
    // MARK: - 缓存
    private var cachedUserContext: UserDataContext?
    private var cacheTimestamp: Date?
    private let cacheExpirationInterval: TimeInterval = 300 // 5分钟缓存
    
    // MARK: - 初始化
    init(
        productRepository: ProductRepository,
        usageRepository: UsageRecordRepository,
        expenseRepository: RelatedExpenseRepository,
        overviewEngine: AdvancedOverviewEngine
    ) {
        self.productRepository = productRepository
        self.usageRepository = usageRepository
        self.expenseRepository = expenseRepository
        self.overviewEngine = overviewEngine
    }
    
    // MARK: - 核心方法
    
    /// 构建用户数据上下文
    /// - Returns: 完整的用户数据上下文
    func buildUserDataContext() async -> UserDataContext {
        // 检查缓存
        if let cachedContext = getCachedUserContext() {
            return cachedContext
        }
        
        do {
            let products = productRepository.fetchAll()
            
            // 使用AdvancedOverviewEngine生成概览数据
            let overviewMetrics = try await overviewEngine.generateOverview(for: products)
            
            // 获取表现最佳产品
            let topPerformingProducts = getTopPerformingProducts(products)
            
            // 获取最近购买
            let recentPurchases = getRecentPurchases(products)
            
            // 提取消费趋势
            let consumptionTrends = extractConsumptionTrends(overviewMetrics, products: products)
            
            // 提取主要类别
            let topCategories = extractTopCategories(overviewMetrics.categoryDistribution)
            
            let context = UserDataContext(
                totalProducts: overviewMetrics.basicStats.totalProductsCount,
                totalInvestment: overviewMetrics.basicStats.totalInvestment,
                topCategories: topCategories,
                topPerformingProducts: topPerformingProducts,
                recentPurchases: recentPurchases,
                consumptionTrends: consumptionTrends
            )
            
            // 缓存结果
            cacheUserContext(context)
            
            return context
            
        } catch {
            print("AdvancedOverviewEngine failed, using fallback: \(error)")
            return await buildFallbackUserContext()
        }
    }
    
    /// 获取特定类型的相关数据上下文
    /// - Parameter contextType: 数据上下文类型
    /// - Returns: 相关数据上下文
    func buildRelevantDataContext(for contextType: DataContextType) async -> RelevantDataContext {
        let products = productRepository.fetchAll()
        
        switch contextType {
        case .productPerformance:
            return await buildProductPerformanceContext(products: products)
        case .consumptionPattern:
            return await buildConsumptionPatternContext(products: products)
        case .categoryAnalysis:
            return await buildCategoryAnalysisContext(products: products)
        case .valueAnalysis:
            return await buildValueAnalysisContext(products: products)
        case .general:
            return await buildGeneralContext(products: products)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取表现最佳产品（前5名）
    private func getTopPerformingProducts(_ products: [Product]) -> [ProductSummary] {
        return products
            .sorted { $0.worthItIndex() > $1.worthItIndex() }
            .prefix(5)
            .map { $0.toSummary() }
    }
    
    /// 获取最近购买的产品（30天内，最多5个）
    private func getRecentPurchases(_ products: [Product]) -> [ProductSummary] {
        let calendar = Calendar.current
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        
        return products
            .filter { product in
                guard let purchaseDate = product.purchaseDate else { return false }
                return purchaseDate >= thirtyDaysAgo
            }
            .sorted { ($0.purchaseDate ?? Date()) > ($1.purchaseDate ?? Date()) }
            .prefix(5)
            .map { $0.toSummary() }
    }
    
    /// 提取主要类别（前3名）
    private func extractTopCategories(_ categoryDistribution: [UUID: CategoryStats]) -> [String] {
        return categoryDistribution.values
            .sorted { $0.productCount > $1.productCount }
            .prefix(3)
            .map { $0.categoryName }
    }
    
    /// 提取消费趋势
    private func extractConsumptionTrends(_ overviewMetrics: OverviewMetrics, products: [Product]) -> ConsumptionTrends {
        // 分析月度支出趋势
        let monthlySpendingTrend = analyzeSpendingTrend(products: products)
        
        // 分析类别偏好
        let categoryPreferences = analyzeCategoryPreferences(overviewMetrics.categoryDistribution)
        
        // 分析季节性模式
        let seasonalPatterns = overviewMetrics.timeDistribution.seasonalTrends
        let seasonalPattern = SeasonalPattern(
            peakSeason: seasonalPatterns.peakSeason,
            lowSeason: seasonalPatterns.lowSeason,
            seasonalVariation: calculateSeasonalVariation(seasonalPatterns)
        )
        
        // 计算消费速度
        let spendingVelocity = analyzeSpendingVelocity(
            averageInterval: overviewMetrics.timeDistribution.averagePurchaseInterval
        )
        
        return ConsumptionTrends(
            monthlySpendingTrend: monthlySpendingTrend,
            categoryPreferences: categoryPreferences,
            seasonalPatterns: seasonalPattern,
            averagePurchaseInterval: overviewMetrics.timeDistribution.averagePurchaseInterval,
            spendingVelocity: spendingVelocity
        )
    }
    
    /// 分析支出趋势
    private func analyzeSpendingTrend(products: [Product]) -> SpendingTrend {
        // 简化的趋势分析，基于最近3个月的数据
        let calendar = Calendar.current
        let now = Date()
        let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) ?? now
        
        let recentProducts = products.filter { product in
            guard let purchaseDate = product.purchaseDate else { return false }
            return purchaseDate >= threeMonthsAgo
        }
        
        if recentProducts.count < 3 {
            return .stable
        }
        
        // 按月分组计算支出
        let monthlySpending = Dictionary(grouping: recentProducts) { product in
            let components = calendar.dateComponents([.year, .month], from: product.purchaseDate ?? Date())
            return components
        }.mapValues { products in
            products.reduce(0) { $0 + $1.price }
        }
        
        let spendingValues = Array(monthlySpending.values).sorted()
        
        if spendingValues.count >= 2 {
            let trend = (spendingValues.last! - spendingValues.first!) / spendingValues.first!
            
            switch trend {
            case 0.2...: return .increasing
            case -0.2..<0.2: return .stable
            case ..<(-0.2): return .decreasing
            default: return .volatile
            }
        }
        
        return .stable
    }
    
    /// 分析类别偏好
    private func analyzeCategoryPreferences(_ categoryDistribution: [UUID: CategoryStats]) -> [CategoryPreference] {
        return categoryDistribution.values
            .map { stats in
                // 计算偏好评分（基于产品数量、支出和价值指数）
                let countScore = min(Double(stats.productCount) * 10, 40) // 最多40分
                let spendingScore = min(stats.totalValue / 1000 * 10, 30) // 最多30分
                let worthScore = stats.averageWorthIndex * 0.3 // 最多30分
                let preferenceScore = countScore + spendingScore + worthScore

                return CategoryPreference(
                    categoryName: stats.categoryName,
                    productCount: stats.productCount,
                    totalSpending: stats.totalValue,
                    averageWorthIndex: stats.averageWorthIndex,
                    preferenceScore: min(preferenceScore, 100)
                )
            }
            .sorted { $0.preferenceScore > $1.preferenceScore }
    }
    
    /// 计算季节性变化程度
    private func calculateSeasonalVariation(_ seasonalTrends: SeasonalTrends) -> Double {
        let purchases = [
            seasonalTrends.springPurchases,
            seasonalTrends.summerPurchases,
            seasonalTrends.autumnPurchases,
            seasonalTrends.winterPurchases
        ]
        
        let total = purchases.reduce(0, +)
        guard total > 0 else { return 0.0 }
        
        let average = Double(total) / 4.0
        let variance = purchases.reduce(0.0) { result, count in
            let diff = Double(count) - average
            return result + (diff * diff)
        } / 4.0
        
        let standardDeviation = sqrt(variance)
        return min(standardDeviation / average, 1.0)
    }
    
    /// 分析消费速度
    private func analyzeSpendingVelocity(averageInterval: TimeInterval) -> SpendingVelocity {
        let days = averageInterval / 86400 // 转换为天数
        
        switch days {
        case 0..<30: return .fast
        case 30..<90: return .moderate
        default: return .slow
        }
    }
    
    // MARK: - 缓存管理
    
    /// 获取缓存的用户上下文
    private func getCachedUserContext() -> UserDataContext? {
        guard let timestamp = cacheTimestamp,
              Date().timeIntervalSince(timestamp) < cacheExpirationInterval,
              let context = cachedUserContext else {
            return nil
        }
        
        return context
    }
    
    /// 缓存用户上下文
    private func cacheUserContext(_ context: UserDataContext) {
        cachedUserContext = context
        cacheTimestamp = Date()
    }
    
    /// 清除缓存
    func clearCache() {
        cachedUserContext = nil
        cacheTimestamp = nil
    }

    // MARK: - 降级方案

    /// 构建降级用户上下文（当AdvancedOverviewEngine失败时）
    private func buildFallbackUserContext() async -> UserDataContext {
        let products = productRepository.fetchAll()

        let totalProducts = products.count
        let totalInvestment = products.reduce(0) { $0 + $1.price }

        // 基础类别统计
        let categoryGroups = Dictionary(grouping: products) { $0.category?.name ?? "未分类" }
        let topCategories = categoryGroups
            .sorted { $0.value.count > $1.value.count }
            .prefix(3)
            .map { $0.key }

        let topPerformingProducts = getTopPerformingProducts(products)
        let recentPurchases = getRecentPurchases(products)

        // 基础消费趋势
        let basicTrends = ConsumptionTrends(
            monthlySpendingTrend: .stable,
            categoryPreferences: [],
            seasonalPatterns: SeasonalPattern(
                peakSeason: "春季",
                lowSeason: "冬季",
                seasonalVariation: 0.3
            ),
            averagePurchaseInterval: 2592000, // 30天
            spendingVelocity: .moderate
        )

        return UserDataContext(
            totalProducts: totalProducts,
            totalInvestment: totalInvestment,
            topCategories: Array(topCategories),
            topPerformingProducts: topPerformingProducts,
            recentPurchases: recentPurchases,
            consumptionTrends: basicTrends
        )
    }

    // MARK: - 特定上下文构建方法

    /// 构建产品表现上下文
    private func buildProductPerformanceContext(products: [Product]) async -> RelevantDataContext {
        let topProducts = getTopPerformingProducts(products)
        let metrics = [
            "averageWorthIndex": products.isEmpty ? 0.0 : products.map { $0.worthItIndex() }.reduce(0, +) / Double(products.count),
            "topProductWorthIndex": topProducts.first?.worthIndex ?? 0.0,
            "performanceSpread": calculatePerformanceSpread(products)
        ]

        let insights = [
            "共有\(products.count)个产品",
            "平均价值度为\(String(format: "%.1f", metrics["averageWorthIndex"] ?? 0.0))分",
            "表现最佳的产品是\(topProducts.first?.name ?? "无")"
        ]

        return RelevantDataContext(
            contextType: .productPerformance,
            products: topProducts,
            metrics: metrics,
            insights: insights
        )
    }

    /// 构建消费模式上下文
    private func buildConsumptionPatternContext(products: [Product]) async -> RelevantDataContext {
        let recentProducts = getRecentPurchases(products)
        let totalSpending = products.reduce(0) { $0 + $1.price }
        let averagePrice = products.isEmpty ? 0.0 : totalSpending / Double(products.count)

        let metrics = [
            "totalSpending": totalSpending,
            "averagePrice": averagePrice,
            "recentPurchaseCount": Double(recentProducts.count)
        ]

        let insights = [
            "总投资金额为¥\(String(format: "%.0f", totalSpending))",
            "平均单品价格为¥\(String(format: "%.0f", averagePrice))",
            "最近30天购买了\(recentProducts.count)个产品"
        ]

        return RelevantDataContext(
            contextType: .consumptionPattern,
            products: recentProducts,
            metrics: metrics,
            insights: insights
        )
    }

    /// 构建类别分析上下文
    private func buildCategoryAnalysisContext(products: [Product]) async -> RelevantDataContext {
        let categoryGroups = Dictionary(grouping: products) { $0.category?.name ?? "未分类" }
        let topCategory = categoryGroups.max { $0.value.count < $1.value.count }

        let topCategoryProducts = (topCategory?.value ?? []).prefix(5).map { $0.toSummary() }

        let metrics = [
            "categoryCount": Double(categoryGroups.count),
            "topCategoryProductCount": Double(topCategory?.value.count ?? 0),
            "topCategorySpending": topCategory?.value.reduce(0) { $0 + $1.price } ?? 0.0
        ]

        let insights = [
            "共有\(categoryGroups.count)个产品类别",
            "最大类别是\(topCategory?.key ?? "无")，有\(topCategory?.value.count ?? 0)个产品",
            "该类别总投资¥\(String(format: "%.0f", metrics["topCategorySpending"] ?? 0.0))"
        ]

        return RelevantDataContext(
            contextType: .categoryAnalysis,
            products: Array(topCategoryProducts),
            metrics: metrics,
            insights: insights
        )
    }

    /// 构建价值分析上下文
    private func buildValueAnalysisContext(products: [Product]) async -> RelevantDataContext {
        let highValueProducts = products
            .filter { $0.worthItIndex() >= 60 }
            .sorted { $0.worthItIndex() > $1.worthItIndex() }
            .prefix(5)
            .map { $0.toSummary() }

        let lowValueProducts = products
            .filter { $0.worthItIndex() < 40 }
            .sorted { $0.worthItIndex() < $1.worthItIndex() }
            .prefix(3)
            .map { $0.toSummary() }

        let metrics = [
            "highValueCount": Double(products.filter { $0.worthItIndex() >= 60 }.count),
            "lowValueCount": Double(products.filter { $0.worthItIndex() < 40 }.count),
            "averageWorthIndex": products.isEmpty ? 0.0 : products.map { $0.worthItIndex() }.reduce(0, +) / Double(products.count)
        ]

        let insights = [
            "有\(Int(metrics["highValueCount"] ?? 0))个高价值产品（价值度≥60分）",
            "有\(Int(metrics["lowValueCount"] ?? 0))个低价值产品（价值度<40分）",
            "整体平均价值度为\(String(format: "%.1f", metrics["averageWorthIndex"] ?? 0.0))分"
        ]

        return RelevantDataContext(
            contextType: .valueAnalysis,
            products: highValueProducts + lowValueProducts,
            metrics: metrics,
            insights: insights
        )
    }

    /// 构建综合分析上下文
    private func buildGeneralContext(products: [Product]) async -> RelevantDataContext {
        let allProducts = products.prefix(10).map { $0.toSummary() }
        let totalSpending = products.reduce(0) { $0 + $1.price }
        let averageWorthIndex = products.isEmpty ? 0.0 : products.map { $0.worthItIndex() }.reduce(0, +) / Double(products.count)

        let metrics = [
            "totalProducts": Double(products.count),
            "totalSpending": totalSpending,
            "averageWorthIndex": averageWorthIndex
        ]

        let insights = [
            "总共有\(products.count)个产品",
            "总投资金额¥\(String(format: "%.0f", totalSpending))",
            "平均价值度\(String(format: "%.1f", averageWorthIndex))分"
        ]

        return RelevantDataContext(
            contextType: .general,
            products: Array(allProducts),
            metrics: metrics,
            insights: insights
        )
    }

    /// 计算产品表现差异度
    private func calculatePerformanceSpread(_ products: [Product]) -> Double {
        guard products.count > 1 else { return 0.0 }

        let worthIndexes = products.map { $0.worthItIndex() }
        let maxWorth = worthIndexes.max() ?? 0.0
        let minWorth = worthIndexes.min() ?? 0.0

        return maxWorth - minWorth
    }

    // MARK: - 完整数据上下文构建

    /// 构建完整的用户数据上下文（包含所有字段）
    func buildCompleteUserDataContext() async -> CompleteUserDataContext {
        // 获取所有产品的完整数据
        let products = productRepository.fetchAll()
        let completeProducts = products.map { $0.toCompleteData() }

        // 获取所有使用记录
        let allUsageRecords = usageRepository.fetchAll()
        let completeUsageRecords = allUsageRecords.map { record in
            CompleteUsageRecordData(
                id: record.id ?? UUID(),
                date: record.date ?? Date(),
                satisfaction: Int(record.satisfaction),
                emotionalValue: Int(record.emotionalValue),
                duration: record.duration,
                usageType: record.usageType ?? "personal",
                scenario: record.scenario,
                title: record.title,
                notes: record.notes,
                memories: record.memories,
                isLoanedOut: record.isLoanedOut,
                borrowerName: record.borrowerName,
                contactInfo: record.contactInfo,
                dueDate: record.dueDate,
                returnDate: record.returnDate,
                recipient: record.recipient,
                transferPrice: record.transferPrice,
                transferType: record.transferTypeString,
                wearCondition: record.wearCondition,
                isStory: record.isStory,
                hasImages: record.images != nil,
                hasAudioRecordings: record.audioRecordings != nil
            )
        }.sorted { $0.date > $1.date }

        // 获取所有费用记录
        let allExpenses = expenseRepository.fetchAll()
        let completeExpenses = allExpenses.map { expense in
            CompleteExpenseData(
                id: expense.id ?? UUID(),
                amount: expense.amount,
                date: expense.date ?? Date(),
                notes: expense.notes,
                typeName: expense.type?.name ?? "其他",
                typeIcon: expense.type?.icon
            )
        }.sorted { $0.date > $1.date }

        // 获取类别统计
        let categoryStats = await getCategoryStatistics(products)

        return CompleteUserDataContext(
            products: completeProducts,
            usageRecords: completeUsageRecords,
            expenses: completeExpenses,
            categories: categoryStats,
            totalProducts: products.count,
            totalInvestment: products.reduce(0) { $0 + $1.totalCostOfOwnership },
            totalUsageRecords: allUsageRecords.count,
            totalExpenses: allExpenses.reduce(0) { $0 + $1.amount },
            averageWorthIndex: products.isEmpty ? 0 : products.map { $0.worthItIndex() }.reduce(0, +) / Double(products.count),
            averageSatisfaction: products.isEmpty ? 0 : products.map { $0.averageSatisfaction }.reduce(0, +) / Double(products.count),
            generatedAt: Date()
        )
    }

    /// 获取类别统计信息
    private func getCategoryStatistics(_ products: [Product]) async -> [CompleteCategoryData] {
        let categoryGroups = Dictionary(grouping: products) { product in
            product.category?.id ?? UUID()
        }

        return categoryGroups.compactMap { (categoryId, products) in
            guard let firstProduct = products.first,
                  let category = firstProduct.category else { return nil }

            let totalValue = products.reduce(0) { $0 + $1.totalCostOfOwnership }
            let averageWorthIndex = products.isEmpty ? 0 : products.map { $0.worthItIndex() }.reduce(0, +) / Double(products.count)

            return CompleteCategoryData(
                id: categoryId,
                name: category.name ?? "",
                icon: category.icon,
                productCount: products.count,
                totalValue: totalValue,
                averageWorthIndex: averageWorthIndex
            )
        }.sorted { $0.productCount > $1.productCount }
    }
}
