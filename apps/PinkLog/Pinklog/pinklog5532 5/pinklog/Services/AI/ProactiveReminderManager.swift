import Foundation
import SwiftUI
import Combine

// MARK: - 主动提醒状态
enum ProactiveReminderStatus: String, CaseIterable, Codable {
    case unread = "未读"
    case read = "已读"
    case dismissed = "已忽略"
    case actioned = "已处理"
    
    var displayColor: Color {
        switch self {
        case .unread:
            return .red
        case .read:
            return .blue
        case .dismissed:
            return .gray
        case .actioned:
            return .green
        }
    }
}

// MARK: - 主动提醒数据模型
struct ProactiveReminder: Identifiable, Codable {
    let id = UUID()
    let type: ProactiveMessageType
    let priority: ProactiveMessagePriority
    let title: String
    let content: String
    let actionButtons: [String]
    let relatedProductIds: [UUID]
    let confidence: Double
    let timestamp: Date
    let expiresAt: Date?
    var status: ProactiveReminderStatus = .unread
    var readAt: Date?
    var dismissedAt: Date?
    
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var isHighPriority: Bool {
        return priority.rawValue >= ProactiveMessagePriority.high.rawValue
    }
    
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
}

// MARK: - 主动提醒管理器
@MainActor
class ProactiveReminderManager: ObservableObject {
    
    // MARK: - 发布属性
    @Published var reminders: [ProactiveReminder] = []
    @Published var unreadCount: Int = 0
    @Published var isLoading: Bool = false
    
    // MARK: - 私有属性
    private let storage = UserDefaults.standard
    private let storageKey = "ProactiveReminders"
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 单例
    static let shared = ProactiveReminderManager()
    
    // MARK: - 初始化
    private init() {
        loadReminders()
        setupSubscriptions()
    }
    
    // MARK: - 公共方法
    
    /// 添加新提醒
    func addReminder(_ reminder: ProactiveReminder) {
        reminders.insert(reminder, at: 0) // 插入到最前面
        updateUnreadCount()
        saveReminders()
        
        // 发送本地通知（如果是高优先级）
        if reminder.isHighPriority {
            sendLocalNotification(for: reminder)
        }
    }
    
    /// 批量添加提醒
    func addReminders(_ newReminders: [ProactiveReminder]) {
        let sortedReminders = newReminders.sorted { $0.timestamp > $1.timestamp }
        reminders.insert(contentsOf: sortedReminders, at: 0)
        updateUnreadCount()
        saveReminders()
        
        // 发送高优先级提醒的通知
        let highPriorityReminders = newReminders.filter { $0.isHighPriority }
        for reminder in highPriorityReminders {
            sendLocalNotification(for: reminder)
        }
    }
    
    /// 标记提醒为已读
    func markAsRead(_ reminderId: UUID) {
        if let index = reminders.firstIndex(where: { $0.id == reminderId }) {
            reminders[index].status = .read
            reminders[index].readAt = Date()
            updateUnreadCount()
            saveReminders()
        }
    }
    
    /// 标记提醒为已忽略
    func dismissReminder(_ reminderId: UUID) {
        if let index = reminders.firstIndex(where: { $0.id == reminderId }) {
            reminders[index].status = .dismissed
            reminders[index].dismissedAt = Date()
            updateUnreadCount()
            saveReminders()
        }
    }
    
    /// 标记提醒为已处理
    func markAsActioned(_ reminderId: UUID) {
        if let index = reminders.firstIndex(where: { $0.id == reminderId }) {
            reminders[index].status = .actioned
            updateUnreadCount()
            saveReminders()
        }
    }
    
    /// 删除提醒
    func deleteReminder(_ reminderId: UUID) {
        reminders.removeAll { $0.id == reminderId }
        updateUnreadCount()
        saveReminders()
    }
    
    /// 清除过期提醒
    func clearExpiredReminders() {
        let beforeCount = reminders.count
        reminders.removeAll { $0.isExpired }
        
        if reminders.count != beforeCount {
            updateUnreadCount()
            saveReminders()
        }
    }
    
    /// 获取未读提醒
    func getUnreadReminders() -> [ProactiveReminder] {
        return reminders.filter { $0.status == .unread && !$0.isExpired }
    }
    
    /// 获取按状态筛选的提醒
    func getReminders(status: ProactiveReminderStatus? = nil) -> [ProactiveReminder] {
        let filteredReminders = reminders.filter { !$0.isExpired }
        
        if let status = status {
            return filteredReminders.filter { $0.status == status }
        }
        
        return filteredReminders
    }
    
    /// 获取按类型筛选的提醒
    func getReminders(type: ProactiveMessageType) -> [ProactiveReminder] {
        return reminders.filter { $0.type == type && !$0.isExpired }
    }
    
    /// 搜索提醒
    func searchReminders(_ searchText: String) -> [ProactiveReminder] {
        guard !searchText.isEmpty else { return getReminders() }
        
        let lowercasedSearch = searchText.lowercased()
        return reminders.filter { reminder in
            !reminder.isExpired &&
            (reminder.title.lowercased().contains(lowercasedSearch) ||
             reminder.content.lowercased().contains(lowercasedSearch))
        }
    }
    
    /// 清除所有提醒
    func clearAllReminders() {
        reminders.removeAll()
        unreadCount = 0
        saveReminders()
    }
    
    // MARK: - 私有方法
    
    private func setupSubscriptions() {
        // 监听提醒变化，自动清理过期提醒
        Timer.publish(every: 3600, on: .main, in: .common) // 每小时清理一次
            .autoconnect()
            .sink { [weak self] _ in
                self?.clearExpiredReminders()
            }
            .store(in: &cancellables)
    }
    
    private func updateUnreadCount() {
        unreadCount = reminders.filter { $0.status == .unread && !$0.isExpired }.count
    }
    
    private func loadReminders() {
        isLoading = true
        
        if let data = storage.data(forKey: storageKey),
           let decodedReminders = try? JSONDecoder().decode([ProactiveReminder].self, from: data) {
            reminders = decodedReminders.sorted { $0.timestamp > $1.timestamp }
        }
        
        updateUnreadCount()
        clearExpiredReminders() // 加载时清理过期提醒
        isLoading = false
    }
    
    private func saveReminders() {
        if let encoded = try? JSONEncoder().encode(reminders) {
            storage.set(encoded, forKey: storageKey)
        }
    }
    
    private func sendLocalNotification(for reminder: ProactiveReminder) {
        let content = UNMutableNotificationContent()
        content.title = reminder.title
        content.body = reminder.content
        content.sound = .default
        content.badge = NSNumber(value: unreadCount)
        
        // 设置通知图标
        if let iconName = getNotificationIcon(for: reminder.type) {
            content.attachments = [try? UNNotificationAttachment(
                identifier: "icon",
                url: Bundle.main.url(forResource: iconName, withExtension: "png") ?? Bundle.main.bundleURL,
                options: nil
            )].compactMap { $0 }
        }
        
        let request = UNNotificationRequest(
            identifier: reminder.id.uuidString,
            content: content,
            trigger: nil // 立即发送
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    private func getNotificationIcon(for type: ProactiveMessageType) -> String? {
        // 返回对应的图标文件名（如果有的话）
        switch type {
        case .reminder:
            return "reminder-icon"
        case .warning:
            return "warning-icon"
        case .opportunity:
            return "opportunity-icon"
        default:
            return nil
        }
    }
}

// MARK: - 扩展：从 ProactiveMessage 转换
extension ProactiveReminder {
    init(from message: ProactiveMessage) {
        self.type = message.type
        self.priority = message.priority
        self.title = message.title
        self.content = message.content
        self.actionButtons = message.actionButtons
        self.relatedProductIds = message.relatedProductIds
        self.confidence = message.confidence
        self.timestamp = message.timestamp
        self.expiresAt = message.expiresAt
        self.status = .unread
        self.readAt = nil
        self.dismissedAt = nil
    }
} 