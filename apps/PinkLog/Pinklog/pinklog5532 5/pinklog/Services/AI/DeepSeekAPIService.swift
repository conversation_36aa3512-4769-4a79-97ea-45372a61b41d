import Foundation
import Security
import Combine

/// DeepSeek API 模型类型
enum DeepSeekModel: String, CaseIterable, Identifiable {
    case chat = "deepseek-chat"
    case reasoner = "deepseek-reasoner"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .chat:
            return "DeepSeek Chat (对话模型)"
        case .reasoner:
            return "DeepSeek Reasoner (推理模型)"
        }
    }
    
    var description: String {
        switch self {
        case .chat:
            return "适用于日常对话和快速回答"
        case .reasoner:
            return "适用于复杂分析和深度推理"
        }
    }
}

/// API 响应格式
enum ResponseFormat: String {
    case text = "text"
    case jsonObject = "json_object"
}

/// 流式响应数据
struct StreamResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [StreamChoice]

    struct StreamChoice: Codable {
        let index: Int
        let delta: StreamDelta
        let finishReason: String?

        enum CodingKeys: String, CodingKey {
            case index, delta
            case finishReason = "finish_reason"
        }
    }

    struct StreamDelta: Codable {
        let role: String?
        let content: String?
    }
}

/// 聊天消息
struct ChatMessage: Codable {
    let role: String
    let content: String
    
    init(role: String, content: String) {
        self.role = role
        self.content = content
    }
}

/// API 响应
struct ChatResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [Choice]
    let usage: Usage
    
    struct Choice: Codable {
        let index: Int
        let message: Message
        let finishReason: String?
        
        enum CodingKeys: String, CodingKey {
            case index, message
            case finishReason = "finish_reason"
        }
    }
    
    struct Message: Codable {
        let role: String
        let content: String
    }
    
    struct Usage: Codable {
        let promptTokens: Int
        let completionTokens: Int
        let totalTokens: Int
        
        enum CodingKeys: String, CodingKey {
            case promptTokens = "prompt_tokens"
            case completionTokens = "completion_tokens"
            case totalTokens = "total_tokens"
        }
    }
}

/// API 错误类型
enum DeepSeekAPIError: LocalizedError {
    case invalidAPIKey
    case networkError(String)
    case invalidResponse
    case rateLimitExceeded
    case insufficientBalance
    case serverError(Int)
    
    var errorDescription: String? {
        switch self {
        case .invalidAPIKey:
            return "API Key 无效或未配置"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .invalidResponse:
            return "API 响应格式错误"
        case .rateLimitExceeded:
            return "API 调用频率超限，请稍后重试"
        case .insufficientBalance:
            return "账户余额不足"
        case .serverError(let code):
            return "服务器错误 (代码: \(code))"
        }
    }
}

/// DeepSeek API 服务
@MainActor
class DeepSeekAPIService: ObservableObject {
    
    // MARK: - 发布属性
    @Published var isConfigured: Bool = false
    @Published var selectedModel: DeepSeekModel = .chat
    @Published var isLoading: Bool = false
    @Published var lastError: DeepSeekAPIError?
    @Published var totalTokensUsed: Int = 0
    @Published var estimatedCost: Double = 0.0
    
    // MARK: - 私有属性
    private let baseURL = "https://api.deepseek.com"
    private let keychainService = "com.pinklog.deepseek"
    private let apiKeyAccount = "deepseek_api_key"
    
    // MARK: - 初始化
    init() {
        loadConfiguration()
    }
    
    // MARK: - 公共方法
    
    /// 配置 API Key
    func configure(apiKey: String) async {
        isLoading = true
        defer { isLoading = false }
        
        // 验证 API Key 格式
        guard !apiKey.isEmpty, apiKey.hasPrefix("sk-") else {
            lastError = .invalidAPIKey
            return
        }
        
        // 测试 API Key 有效性
        do {
            let testMessages = [ChatMessage(role: "user", content: "Hello")]
            _ = try await chatCompletion(messages: testMessages, apiKey: apiKey)
            
            // 保存到 Keychain
            saveAPIKey(apiKey)
            isConfigured = true
            lastError = nil
            
        } catch {
            if let apiError = error as? DeepSeekAPIError {
                lastError = apiError
            } else {
                lastError = .networkError(error.localizedDescription)
            }
            isConfigured = false
        }
    }
    
    /// 聊天完成 API 调用
    func chatCompletion(
        messages: [ChatMessage],
        model: DeepSeekModel? = nil,
        responseFormat: ResponseFormat = .text,
        maxTokens: Int? = nil,
        temperature: Double = 0.7,
        stream: Bool = false
    ) async throws -> ChatResponse {

        let apiKey = getAPIKey()
        return try await chatCompletion(
            messages: messages,
            model: model,
            responseFormat: responseFormat,
            maxTokens: maxTokens,
            temperature: temperature,
            stream: stream,
            apiKey: apiKey
        )
    }

    /// 流式聊天完成 API 调用
    func chatCompletionStream(
        messages: [ChatMessage],
        model: DeepSeekModel? = nil,
        responseFormat: ResponseFormat = .text,
        maxTokens: Int? = nil,
        temperature: Double = 0.7,
        onUpdate: @escaping (String) -> Void,
        onComplete: @escaping (ChatResponse.Usage?) -> Void,
        onError: @escaping (Error) -> Void
    ) {
        let apiKey = getAPIKey()

        Task {
            do {
                try await chatCompletionStream(
                    messages: messages,
                    model: model,
                    responseFormat: responseFormat,
                    maxTokens: maxTokens,
                    temperature: temperature,
                    apiKey: apiKey,
                    onUpdate: onUpdate,
                    onComplete: onComplete,
                    onError: onError
                )
            } catch {
                onError(error)
            }
        }
    }
    
    /// 清除配置
    func clearConfiguration() {
        deleteAPIKey()
        isConfigured = false
        selectedModel = .chat
        totalTokensUsed = 0
        estimatedCost = 0.0
        lastError = nil
    }
    
    /// 获取成本估算
    func estimateCost(inputTokens: Int, outputTokens: Int, model: DeepSeekModel) -> Double {
        // 基于 DeepSeek 定价计算（标准时段价格）
        let inputCostPer1M: Double
        let outputCostPer1M: Double
        
        switch model {
        case .chat:
            inputCostPer1M = 2.0  // 2元/百万tokens
            outputCostPer1M = 8.0 // 8元/百万tokens
        case .reasoner:
            inputCostPer1M = 4.0  // 4元/百万tokens
            outputCostPer1M = 16.0 // 16元/百万tokens
        }
        
        let inputCost = Double(inputTokens) * inputCostPer1M / 1_000_000
        let outputCost = Double(outputTokens) * outputCostPer1M / 1_000_000
        
        return inputCost + outputCost
    }
}

// MARK: - 私有方法扩展
private extension DeepSeekAPIService {
    
    /// 实际的 API 调用方法
    func chatCompletion(
        messages: [ChatMessage],
        model: DeepSeekModel? = nil,
        responseFormat: ResponseFormat = .text,
        maxTokens: Int? = nil,
        temperature: Double = 0.7,
        stream: Bool = false,
        apiKey: String
    ) async throws -> ChatResponse {
        
        guard !apiKey.isEmpty else {
            throw DeepSeekAPIError.invalidAPIKey
        }
        
        let url = URL(string: "\(baseURL)/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        // 构建请求体
        var requestBody: [String: Any] = [
            "model": (model ?? selectedModel).rawValue,
            "messages": messages.map { ["role": $0.role, "content": $0.content] },
            "temperature": temperature,
            "stream": stream
        ]
        
        if responseFormat == .jsonObject {
            requestBody["response_format"] = ["type": "json_object"]
        }
        
        if let maxTokens = maxTokens {
            requestBody["max_tokens"] = maxTokens
        }
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查 HTTP 状态码
        if let httpResponse = response as? HTTPURLResponse {
            switch httpResponse.statusCode {
            case 200:
                break
            case 401:
                throw DeepSeekAPIError.invalidAPIKey
            case 429:
                throw DeepSeekAPIError.rateLimitExceeded
            case 402:
                throw DeepSeekAPIError.insufficientBalance
            case 500...599:
                throw DeepSeekAPIError.serverError(httpResponse.statusCode)
            default:
                throw DeepSeekAPIError.networkError("HTTP \(httpResponse.statusCode)")
            }
        }
        
        // 解析响应
        do {
            let chatResponse = try JSONDecoder().decode(ChatResponse.self, from: data)
            
            // 更新使用统计
            await updateUsageStats(chatResponse.usage)
            
            return chatResponse
        } catch {
            throw DeepSeekAPIError.invalidResponse
        }
    }
    
    /// 流式聊天完成 API 调用实现
    func chatCompletionStream(
        messages: [ChatMessage],
        model: DeepSeekModel? = nil,
        responseFormat: ResponseFormat = .text,
        maxTokens: Int? = nil,
        temperature: Double = 0.7,
        apiKey: String,
        onUpdate: @escaping (String) -> Void,
        onComplete: @escaping (ChatResponse.Usage?) -> Void,
        onError: @escaping (Error) -> Void
    ) async throws {

        guard !apiKey.isEmpty else {
            throw DeepSeekAPIError.invalidAPIKey
        }

        let url = URL(string: "\(baseURL)/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        // 构建请求体（启用流式传输）
        var requestBody: [String: Any] = [
            "model": (model ?? selectedModel).rawValue,
            "messages": messages.map { ["role": $0.role, "content": $0.content] },
            "temperature": temperature,
            "stream": true  // 关键：启用流式传输
        ]

        if responseFormat == .jsonObject {
            requestBody["response_format"] = ["type": "json_object"]
        }

        if let maxTokens = maxTokens {
            requestBody["max_tokens"] = maxTokens
        }

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        // 创建真正的流式传输会话
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60
        config.timeoutIntervalForResource = 300

        // 创建自定义的URLSessionDataDelegate来处理流式数据
        let streamDelegate = StreamingDelegate(
            onUpdate: onUpdate,
            onComplete: onComplete,
            onError: onError
        )

        let session = URLSession(configuration: config, delegate: streamDelegate, delegateQueue: nil)

        // 创建流式数据任务
        let task = session.dataTask(with: request)
        streamDelegate.task = task

        task.resume()
    }

    /// 处理流式数据 - 简化版本，符合DeepSeek API格式
    func processStreamData(
        _ data: Data,
        onUpdate: @escaping (String) -> Void,
        onComplete: @escaping (ChatResponse.Usage?) -> Void,
        onError: @escaping (Error) -> Void
    ) {
        let dataString = String(data: data, encoding: .utf8) ?? ""
        let lines = dataString.components(separatedBy: .newlines)

        var totalContent = ""

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // 跳过空行
            guard !trimmedLine.isEmpty else { continue }

            // 处理Server-Sent Events格式：data: {...}
            if trimmedLine.hasPrefix("data: ") {
                let jsonString = String(trimmedLine.dropFirst(6)) // 移除 "data: " 前缀

                // 检查是否为结束标记
                if jsonString == "[DONE]" {
                    // 估算token使用量
                    let estimatedPromptTokens = 100 // 基础估算
                    let estimatedCompletionTokens = max(totalContent.count / 4, 1) // 大约4个字符=1个token

                    let usage = ChatResponse.Usage(
                        promptTokens: estimatedPromptTokens,
                        completionTokens: estimatedCompletionTokens,
                        totalTokens: estimatedPromptTokens + estimatedCompletionTokens
                    )

                    Task { @MainActor in
                        self.updateUsageStats(usage)
                        onComplete(usage)
                    }
                    return
                }

                // 解析JSON数据
                guard let jsonData = jsonString.data(using: .utf8) else { continue }

                do {
                    let streamResponse = try JSONDecoder().decode(StreamResponse.self, from: jsonData)

                    // 提取内容更新
                    if let choice = streamResponse.choices.first,
                       let content = choice.delta.content {
                        totalContent += content

                        // 在主线程更新UI
                        Task { @MainActor in
                            onUpdate(content)
                        }
                    }

                } catch {
                    // 忽略解析错误，继续处理下一行
                    print("流式数据解析错误: \(error)")
                    continue
                }
            }
        }
    }

    /// 更新使用统计
    func updateUsageStats(_ usage: ChatResponse.Usage) {
        totalTokensUsed += usage.totalTokens
        let cost = estimateCost(
            inputTokens: usage.promptTokens,
            outputTokens: usage.completionTokens,
            model: selectedModel
        )
        estimatedCost += cost
    }
    
    /// 加载配置
    func loadConfiguration() {
        let apiKey = getAPIKey()
        isConfigured = !apiKey.isEmpty
    }
    
    /// 保存 API Key 到 Keychain
    func saveAPIKey(_ apiKey: String) {
        let data = apiKey.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: apiKeyAccount,
            kSecValueData as String: data
        ]
        
        // 删除现有项目
        SecItemDelete(query as CFDictionary)
        
        // 添加新项目
        SecItemAdd(query as CFDictionary, nil)
    }
    
    /// 从 Keychain 获取 API Key
    func getAPIKey() -> String {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: apiKeyAccount,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let data = result as? Data,
           let apiKey = String(data: data, encoding: .utf8) {
            return apiKey
        }
        
        return ""
    }
    
    /// 删除 API Key
    func deleteAPIKey() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: apiKeyAccount
        ]

        SecItemDelete(query as CFDictionary)
    }
}

// MARK: - 流式传输代理
class StreamingDelegate: NSObject, URLSessionDataDelegate {
    private let onUpdate: (String) -> Void
    private let onComplete: (ChatResponse.Usage?) -> Void
    private let onError: (Error) -> Void
    private var dataBuffer = Data()
    private var totalContent = ""

    weak var task: URLSessionDataTask?

    init(
        onUpdate: @escaping (String) -> Void,
        onComplete: @escaping (ChatResponse.Usage?) -> Void,
        onError: @escaping (Error) -> Void
    ) {
        self.onUpdate = onUpdate
        self.onComplete = onComplete
        self.onError = onError
        super.init()
    }

    // 接收到响应
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {

        if let httpResponse = response as? HTTPURLResponse {
            switch httpResponse.statusCode {
            case 200:
                completionHandler(.allow)
            case 401:
                onError(DeepSeekAPIError.invalidAPIKey)
                completionHandler(.cancel)
            case 429:
                onError(DeepSeekAPIError.rateLimitExceeded)
                completionHandler(.cancel)
            case 402:
                onError(DeepSeekAPIError.insufficientBalance)
                completionHandler(.cancel)
            case 500...599:
                onError(DeepSeekAPIError.serverError(httpResponse.statusCode))
                completionHandler(.cancel)
            default:
                onError(DeepSeekAPIError.networkError("HTTP \(httpResponse.statusCode)"))
                completionHandler(.cancel)
            }
        } else {
            completionHandler(.allow)
        }
    }

    // 接收到数据 - 这里是真正的流式处理
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        dataBuffer.append(data)
        processStreamBuffer()
    }

    // 任务完成
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            onError(DeepSeekAPIError.networkError(error.localizedDescription))
        } else {
            // 处理剩余数据
            processStreamBuffer(isComplete: true)

            // 估算token使用量
            let estimatedPromptTokens = 100
            let estimatedCompletionTokens = max(totalContent.count / 4, 1)

            let usage = ChatResponse.Usage(
                promptTokens: estimatedPromptTokens,
                completionTokens: estimatedCompletionTokens,
                totalTokens: estimatedPromptTokens + estimatedCompletionTokens
            )

            onComplete(usage)
        }
    }

    // 处理流式数据缓冲区
    private func processStreamBuffer(isComplete: Bool = false) {
        let dataString = String(data: dataBuffer, encoding: .utf8) ?? ""
        let lines = dataString.components(separatedBy: .newlines)

        // 保留最后一行（可能不完整），除非是完成状态
        let linesToProcess = isComplete ? lines : Array(lines.dropLast())

        // 更新缓冲区，保留未处理的数据
        if !isComplete && !lines.isEmpty {
            let lastLine = lines.last ?? ""
            dataBuffer = lastLine.data(using: .utf8) ?? Data()
        } else {
            dataBuffer = Data()
        }

        // 处理每一行
        for line in linesToProcess {
            processStreamLine(line)
        }
    }

    // 处理单行流式数据
    private func processStreamLine(_ line: String) {
        let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

        // 跳过空行
        guard !trimmedLine.isEmpty else { return }

        // 处理Server-Sent Events格式：data: {...}
        if trimmedLine.hasPrefix("data: ") {
            let jsonString = String(trimmedLine.dropFirst(6)) // 移除 "data: " 前缀

            // 检查是否为结束标记
            if jsonString == "[DONE]" {
                return
            }

            // 解析JSON数据
            guard let jsonData = jsonString.data(using: .utf8) else { return }

            do {
                let streamResponse = try JSONDecoder().decode(StreamResponse.self, from: jsonData)

                // 提取内容更新
                if let choice = streamResponse.choices.first,
                   let content = choice.delta.content {
                    totalContent += content

                    // 在主线程更新UI
                    DispatchQueue.main.async {
                        self.onUpdate(content)
                    }
                }

            } catch {
                // 忽略解析错误，继续处理下一行
                print("流式数据解析错误: \(error)")
            }
        }
    }
}
