import Foundation
import SwiftUI
import UIKit

/// 用户基本信息管理器
/// 负责管理用户昵称、头像等基本信息
class UserProfileManager: ObservableObject {
    static let shared = UserProfileManager()
    
    // MARK: - Published Properties
    @Published var nickname: String = ""
    @Published var avatarImage: UIImage?
    @Published var isOnboardingCompleted: Bool = false
    
    // MARK: - Private Properties
    private let userDefaults = UserDefaults.standard
    private let avatarFileName = "user_avatar.png"
    
    // MARK: - Keys
    private enum Keys {
        static let nickname = "user_nickname"
        static let onboardingCompleted = "onboarding_completed"
    }
    
    // MARK: - Initialization
    private init() {
        loadProfile()
    }
    
    // MARK: - Public Methods
    
    /// 保存用户资料
    func saveProfile() {
        userDefaults.set(nickname, forKey: Keys.nickname)
        userDefaults.set(isOnboardingCompleted, forKey: Keys.onboardingCompleted)
        
        if let avatarImage = avatarImage {
            saveAvatarImage(avatarImage)
        }
        
        print("用户资料保存成功: 昵称=\(nickname), 引导完成=\(isOnboardingCompleted)")
    }
    
    /// 加载用户资料
    func loadProfile() {
        nickname = userDefaults.string(forKey: Keys.nickname) ?? ""
        isOnboardingCompleted = userDefaults.bool(forKey: Keys.onboardingCompleted)
        avatarImage = loadAvatarImage()
        
        print("用户资料加载完成: 昵称=\(nickname), 引导完成=\(isOnboardingCompleted)")
    }
    
    /// 完成引导设置
    func completeOnboarding() {
        isOnboardingCompleted = true
        saveProfile()
    }
    
    /// 重置用户资料（用于测试）
    func resetProfile() {
        nickname = ""
        avatarImage = nil
        isOnboardingCompleted = false
        
        // 删除保存的数据
        userDefaults.removeObject(forKey: Keys.nickname)
        userDefaults.removeObject(forKey: Keys.onboardingCompleted)
        deleteAvatarImage()
        
        print("用户资料已重置")
    }
    
    /// 更新昵称
    func updateNickname(_ newNickname: String) {
        nickname = newNickname.trimmingCharacters(in: .whitespacesAndNewlines)
        saveProfile()
    }
    
    /// 更新头像
    func updateAvatar(_ newAvatar: UIImage) {
        avatarImage = newAvatar
        saveProfile()
    }
    
    // MARK: - Private Methods
    
    /// 保存头像图片到Documents目录
    private func saveAvatarImage(_ image: UIImage) {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            print("无法获取Documents目录")
            return
        }
        
        let fileURL = documentsDirectory.appendingPathComponent(avatarFileName)
        
        guard let imageData = image.pngData() else {
            print("无法转换头像为PNG数据")
            return
        }
        
        do {
            try imageData.write(to: fileURL)
            print("头像保存成功: \(fileURL.path)")
        } catch {
            print("头像保存失败: \(error.localizedDescription)")
        }
    }
    
    /// 从Documents目录加载头像图片
    private func loadAvatarImage() -> UIImage? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            print("无法获取Documents目录")
            return nil
        }
        
        let fileURL = documentsDirectory.appendingPathComponent(avatarFileName)
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            print("头像文件不存在")
            return nil
        }
        
        guard let imageData = try? Data(contentsOf: fileURL),
              let image = UIImage(data: imageData) else {
            print("无法加载头像图片")
            return nil
        }
        
        print("头像加载成功")
        return image
    }
    
    /// 删除头像文件
    private func deleteAvatarImage() {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let fileURL = documentsDirectory.appendingPathComponent(avatarFileName)
        
        if FileManager.default.fileExists(atPath: fileURL.path) {
            try? FileManager.default.removeItem(at: fileURL)
            print("头像文件已删除")
        }
    }
}

// MARK: - Computed Properties
extension UserProfileManager {
    /// 获取显示用的昵称（如果为空则返回默认值）
    var displayNickname: String {
        return nickname.isEmpty ? "用户" : nickname
    }
    
    /// 获取显示用的头像（如果为空则返回默认头像）
    var displayAvatar: UIImage {
        return avatarImage ?? UIImage(systemName: "person.circle.fill") ?? UIImage()
    }
    
    /// 检查是否有完整的用户信息
    var hasCompleteProfile: Bool {
        return !nickname.isEmpty && avatarImage != nil
    }
}
