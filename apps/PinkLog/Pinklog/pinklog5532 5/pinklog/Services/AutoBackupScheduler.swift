import Foundation
import BackgroundTasks
import UserNotifications
import UIKit
import Network

// MARK: - 自动备份调度器

/// 自动备份调度器 - 管理自动备份的调度和执行
@MainActor
class AutoBackupScheduler: ObservableObject {
    
    // MARK: - 单例
    static let shared = AutoBackupScheduler()
    
    // MARK: - 发布属性
    @Published var isScheduled: Bool = false
    @Published var nextScheduledTime: Date?
    @Published var lastAutoBackupTime: Date?
    @Published var autoBackupEnabled: Bool = false
    
    // MARK: - 私有属性
    private let backupManager = BackupManager.shared
    private let networkMonitor: NetworkMonitor = NetworkMonitor.shared
    private let userDefaults = UserDefaults.standard
    
    // 定时器
    private var backupTimer: Timer?
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    
    // 后台任务标识符
    private let backgroundTaskIdentifier = "top.trysapp.pinklog.backup"
    
    // UserDefaults键
    private let lastAutoBackupKey = "last_auto_backup_time"
    private let autoBackupEnabledKey = "auto_backup_enabled"
    
    // MARK: - 初始化
    private init() {
        loadSettings()
        setupNotifications()
        registerBackgroundTask()
        print("📋 AutoBackupScheduler初始化完成，BGTaskScheduler已注册")
    }
    
    // MARK: - 公共方法
    
    /// 启动自动备份调度
    func startScheduling() async {
        guard !isScheduled else {
            print("⚠️ 自动备份调度已经启动，跳过重复启动")
            return
        }

        print("🚀 开始启动自动备份调度...")

        autoBackupEnabled = true
        isScheduled = true
        saveSettings()

        // 不立即执行备份检查，改为正常的间隔调度
        scheduleNextBackup(immediateCheck: false)
        scheduleBackgroundTask()

        print("✅ 自动备份调度已启动")
        
        // 延迟5秒后执行首次备份检查，确保所有服务已就绪
        Task.detached(priority: .background) { [weak self] in
            try? await Task.sleep(nanoseconds: 5_000_000_000) // 5秒
            await self?.checkAndPerformBackup()
        }
    }
    
    /// 停止自动备份调度
    func stopScheduling() {
        guard isScheduled else { return }
        
        autoBackupEnabled = false
        isScheduled = false
        nextScheduledTime = nil
        saveSettings()
        
        cancelTimer()
        cancelBackgroundTask()
        
        print("自动备份调度已停止")
    }
    
    /// 立即执行自动备份检查
    func checkAndPerformBackup() async {
        guard autoBackupEnabled else { return }
        
        // 检查是否需要备份
        guard shouldPerformBackup() else {
            print("当前不需要执行自动备份")
            return
        }
        
        // 检查网络条件
        guard isNetworkSuitable() else {
            print("网络条件不适合备份，稍后重试")
            scheduleRetry()
            return
        }
        
        // 检查设备条件
        guard await isDeviceConditionSuitable() else {
            print("设备条件不适合备份，稍后重试")
            scheduleRetry()
            return
        }
        
        await performAutoBackup()
    }
    
    /// 更新备份配置
    func updateConfiguration(_ configuration: BackupConfiguration) {
        let wasEnabled = autoBackupEnabled
        autoBackupEnabled = configuration.autoBackupEnabled
        
        if autoBackupEnabled != wasEnabled {
            if autoBackupEnabled {
                Task {
                    await startScheduling()
                }
            } else {
                stopScheduling()
            }
        } else if autoBackupEnabled {
            // 重新调度以应用新的频率设置
            scheduleNextBackup()
        }
    }
    
    // MARK: - 私有方法
    
    /// 加载设置
    private func loadSettings() {
        autoBackupEnabled = userDefaults.bool(forKey: autoBackupEnabledKey)
        lastAutoBackupTime = userDefaults.object(forKey: lastAutoBackupKey) as? Date
        
        // 注意：只加载配置，不立即启动调度
        // 调度启动将由外部在适当时机调用startScheduling()
        print("📋 自动备份配置已加载: enabled=\(autoBackupEnabled)")
    }
    
    /// 保存设置（优化版本）
    private func saveSettings() {
        // 捕获当前值，避免异步访问问题
        let enabled = autoBackupEnabled
        let lastTime = lastAutoBackupTime
        
        // 在后台线程保存设置，避免主线程I/O阻塞
        Task.detached(priority: .background) { [weak self] in
            guard let self = self else { return }
            
            // 后台线程执行UserDefaults写入
            self.userDefaults.set(enabled, forKey: self.autoBackupEnabledKey)
            if let lastTime = lastTime {
                self.userDefaults.set(lastTime, forKey: self.lastAutoBackupKey)
            }
            
            // 同步到磁盘
            self.userDefaults.synchronize()
            
            await MainActor.run {
                print("✅ 自动备份设置已保存")
            }
        }
    }
    
    /// 设置通知
    private func setupNotifications() {
        // 请求通知权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("通知权限请求失败: \(error)")
            }
        }
        
        // 监听应用生命周期
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    /// 注册后台任务
    private func registerBackgroundTask() {
        BGTaskScheduler.shared.register(forTaskWithIdentifier: backgroundTaskIdentifier, using: nil) { [weak self] task in
            self?.handleBackgroundTask(task as! BGAppRefreshTask)
        }
    }
    
    /// 调度下次备份
    private func scheduleNextBackup(immediateCheck: Bool = false) {
        cancelTimer()
        
        let configuration = backupManager.configuration
        guard configuration.autoBackupEnabled && configuration.backupFrequency != .manual else {
            nextScheduledTime = nil
            return
        }
        
        let interval = immediateCheck ? 0 : configuration.backupFrequency.interval
        let nextTime = Date().addingTimeInterval(interval)
        nextScheduledTime = nextTime
        
        backupTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: false) { [weak self] _ in
            Task {
                await self?.checkAndPerformBackup()
            }
        }
        
        if immediateCheck {
            print("立即执行备份检查")
        } else {
            print("下次自动备份时间: \(nextTime)")
        }
    }
    
    /// 调度后台任务
    private func scheduleBackgroundTask() {
        let request = BGAppRefreshTaskRequest(identifier: backgroundTaskIdentifier)
        request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // 15分钟后
        
        do {
            try BGTaskScheduler.shared.submit(request)
            print("后台任务已调度")
        } catch {
            print("后台任务调度失败: \(error)")
        }
    }
    
    /// 取消定时器
    private func cancelTimer() {
        backupTimer?.invalidate()
        backupTimer = nil
    }
    
    /// 取消后台任务
    private func cancelBackgroundTask() {
        BGTaskScheduler.shared.cancel(taskRequestWithIdentifier: backgroundTaskIdentifier)
    }
    
    /// 处理后台任务
    private func handleBackgroundTask(_ task: BGAppRefreshTask) {
        print("执行后台备份任务")
        
        // 设置任务过期处理
        task.expirationHandler = {
            task.setTaskCompleted(success: false)
        }
        
        // 执行备份
        Task {
            await checkAndPerformBackup()
            
            // 调度下一个后台任务
            scheduleBackgroundTask()
            
            // 标记任务完成
            task.setTaskCompleted(success: true)
        }
    }
    
    /// 判断是否应该执行备份
    private func shouldPerformBackup() -> Bool {
        let configuration = backupManager.configuration
        
        // 检查自动备份是否启用
        guard configuration.autoBackupEnabled else { return false }
        
        // 检查频率设置
        guard configuration.backupFrequency != .manual else { return false }
        
        // 检查时间间隔
        if let lastBackup = lastAutoBackupTime {
            let timeSinceLastBackup = Date().timeIntervalSince(lastBackup)
            return timeSinceLastBackup >= configuration.backupFrequency.interval
        }
        
        // 如果从未备份过，则需要备份
        return true
    }
    
    /// 检查网络条件是否适合
    private func isNetworkSuitable() -> Bool {
        let configuration = backupManager.configuration
        
        // 检查网络连接
        guard networkMonitor.isConnected else { return false }
        
        // 如果设置了仅WiFi备份
        if configuration.wifiOnlyBackup {
            return networkMonitor.connectionType == .wifi
        }
        
        return true
    }
    
    /// 检查设备条件是否适合（异步版本）
    private func isDeviceConditionSuitable() async -> Bool {
        let configuration = backupManager.configuration
        
        // 在后台线程检查设备状态，避免主线程阻塞
        return await Task.detached(priority: .userInitiated) {
            // 检查低电量模式设置
            if !configuration.lowPowerModeBackup && ProcessInfo.processInfo.isLowPowerModeEnabled {
                await MainActor.run {
                    print("🔋 设备处于低电量模式，跳过备份")
                }
                return false
            }
            
            // 检查设备是否正在充电或电量充足
            await MainActor.run {
                // 启用电池监控
                UIDevice.current.isBatteryMonitoringEnabled = true
            }
            
            let batteryLevel = await MainActor.run { UIDevice.current.batteryLevel }
            let batteryState = await MainActor.run { UIDevice.current.batteryState }
            
            if batteryState == .charging || batteryState == .full {
                await MainActor.run {
                    print("🔌 设备正在充电或电量充足，可以执行备份")
                }
                return true
            }
            
            // 如果未充电，检查电量是否充足（至少30%）
            let isLevelSuitable = batteryLevel > 0.3 || batteryLevel == -1.0 // -1.0表示无法获取电量信息
            
            if !isLevelSuitable {
                await MainActor.run {
                    print("🪫 电量不足(\(Int(batteryLevel * 100))%)，跳过备份")
                }
            }
            
            return isLevelSuitable
        }.value
    }
    
    /// 执行自动备份
    private func performAutoBackup() async {
        print("开始执行自动备份")
        
        let result = await backupManager.performCrossDeviceBackup(description: "自动备份")
        
        if result.success {
            lastAutoBackupTime = Date()
            saveSettings()
            scheduleNextBackup()
            
            // 发送成功通知
            await sendNotification(
                title: "备份完成",
                body: "数据已成功备份到iCloud",
                isSuccess: true
            )
            
            print("自动备份完成")
        } else {
            // 发送失败通知
            await sendNotification(
                title: "备份失败",
                body: result.error?.localizedDescription ?? "备份过程中发生错误",
                isSuccess: false
            )
            
            // 调度重试
            scheduleRetry()
            
            print("自动备份失败: \(result.error?.localizedDescription ?? "未知错误")")
        }
    }
    
    /// 调度重试
    private func scheduleRetry() {
        // 30分钟后重试
        let retryInterval: TimeInterval = 30 * 60
        
        backupTimer = Timer.scheduledTimer(withTimeInterval: retryInterval, repeats: false) { [weak self] _ in
            Task {
                await self?.checkAndPerformBackup()
            }
        }
        
        nextScheduledTime = Date().addingTimeInterval(retryInterval)
        print("将在30分钟后重试备份")
    }
    
    /// 发送通知（后台线程优化）
    private func sendNotification(title: String, body: String, isSuccess: Bool) async {
        // 在后台线程创建通知内容，避免主线程阻塞
        await Task.detached(priority: .background) {
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = body
            content.sound = .default
            
            if isSuccess {
                content.categoryIdentifier = "BACKUP_SUCCESS"
            } else {
                content.categoryIdentifier = "BACKUP_FAILURE"
            }
            
            let request = UNNotificationRequest(
                identifier: UUID().uuidString,
                content: content,
                trigger: nil
            )
            
            do {
                try await UNUserNotificationCenter.current().add(request)
                await MainActor.run {
                    print("✅ 通知发送成功")
                }
            } catch {
                await MainActor.run {
                    print("❌ 发送通知失败: \(error)")
                }
            }
        }.value
    }
    
    // MARK: - 通知处理
    
    @objc private func appDidEnterBackground() {
        // 应用进入后台时，开始后台任务
        backgroundTaskID = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }
        
        // 如果需要，执行快速备份检查
        if autoBackupEnabled && shouldPerformBackup() {
            Task {
                await checkAndPerformBackup()
                endBackgroundTask()
            }
        } else {
            endBackgroundTask()
        }
    }
    
    @objc private func appWillEnterForeground() {
        // 应用回到前台时，检查是否需要备份
        if autoBackupEnabled {
            Task {
                await checkAndPerformBackup()
            }
        }
    }
    
    private func endBackgroundTask() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        Task { @MainActor in
            cancelTimer()
            endBackgroundTask()
        }
    }
}
