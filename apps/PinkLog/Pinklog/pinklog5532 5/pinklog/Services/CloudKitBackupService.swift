import Foundation
import CloudKit
import Network
import UIKit
import os.log

// MARK: - CloudKit错误类型

/// CloudKit备份错误类型
enum CloudKitBackupError: Error, LocalizedError {
    case containerNotAvailable
    case accountNotAvailable
    case networkUnavailable
    case quotaExceeded
    case recordNotFound(String)
    case assetUploadFailed(String)
    case batchOperationFailed([String])
    case invalidData(String)
    case dataEncodingFailed
    case dataSizeExceeded(Int)
    case requestRateLimited(retryAfter: TimeInterval)
    case recordConflict
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .containerNotAvailable:
            return "CloudKit容器不可用"
        case .accountNotAvailable:
            return "iCloud账户不可用，请检查登录状态"
        case .networkUnavailable:
            return "网络连接不可用"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .recordNotFound(let id):
            return "记录未找到: \(id)"
        case .assetUploadFailed(let reason):
            return "资源上传失败: \(reason)"
        case .batchOperationFailed(let errors):
            return "批量操作失败: \(errors.joined(separator: ", "))"
        case .invalidData(let reason):
            return "数据无效: \(reason)"
        case .dataEncodingFailed:
            return "数据编码失败"
        case .dataSizeExceeded(let size):
            return "数据大小超过限制: \(size) 字节"
        case .requestRateLimited(let retryAfter):
            return "请求频率过高，请等待 \(retryAfter) 秒后重试"
        case .recordConflict:
            return "记录冲突，服务器上的记录已被修改"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - CloudKit操作结果

/// CloudKit操作结果
struct CloudKitOperationResult<T> {
    let success: Bool
    let data: T?
    let error: CloudKitBackupError?
    let retryAfter: TimeInterval?
    
    static func success(_ data: T) -> CloudKitOperationResult<T> {
        return CloudKitOperationResult(success: true, data: data, error: nil, retryAfter: nil)
    }
    
    static func failure(_ error: CloudKitBackupError, retryAfter: TimeInterval? = nil) -> CloudKitOperationResult<T> {
        return CloudKitOperationResult(success: false, data: nil, error: error, retryAfter: retryAfter)
    }
}



// MARK: - CloudKit备份服务

/// CloudKit备份服务 - 管理与CloudKit的所有交互
@MainActor
class CloudKitBackupService: ObservableObject {
    
    // MARK: - 单例
    static let shared = CloudKitBackupService()
    
    // MARK: - 发布属性
    @Published var isAvailable: Bool = false
    @Published var accountStatus: CKAccountStatus = .couldNotDetermine
    @Published var isOperating: Bool = false
    @Published var lastError: CloudKitBackupError?
    @Published var operationProgress: Double = 0.0
    
    // MARK: - 私有属性
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let networkMonitor = NetworkMonitor.shared
    private let operationQueue = OperationQueue()

    // 日志记录器
    private let logger = Logger(subsystem: "com.pinklog.backup", category: "CloudKit")

    // 重试配置
    private let maxRetryAttempts = 3
    private let baseRetryDelay: TimeInterval = 2.0

    // 限流控制
    private var lastRequestTime: Date = Date.distantPast
    private let minimumRequestInterval: TimeInterval = 1.0
    
    // 临时文件管理
    private var temporaryAssetURLs: Set<URL> = []
    
    // MARK: - 初始化
    private init() {
        // 使用已配置的iCloud容器
        self.container = CKContainer(identifier: "iCloud.top.trysapp.pinklog")
        self.privateDatabase = container.privateCloudDatabase
        
        // 配置操作队列
        operationQueue.maxConcurrentOperationCount = 3
        operationQueue.qualityOfService = .userInitiated
        
        // 初始化检查
        Task {
            await checkAvailability()
        }
    }
    
    // MARK: - 可用性检查
    
    /// 检查CloudKit可用性
    func checkAvailability() async {
        logger.info("🔍 开始检查CloudKit可用性...")

        do {
            // 检查网络连接，如果网络未连接则等待一段时间
            var networkAvailable = networkMonitor.isConnected
            logger.info("📶 网络连接状态: \(networkAvailable ? "已连接" : "未连接")")

            if !networkAvailable {
                logger.info("⏳ 网络未连接，等待网络连接...")

                // 等待最多3秒钟网络连接
                for attempt in 1...6 {
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                    networkAvailable = networkMonitor.isConnected

                    if networkAvailable {
                        logger.info("✅ 网络已连接")
                        break
                    }

                    if attempt == 6 {
                        logger.warning("⚠️ 等待3秒后网络仍未连接，CloudKit不可用")
                        await MainActor.run {
                            self.isAvailable = false
                            self.accountStatus = .couldNotDetermine
                            self.lastError = .networkUnavailable
                        }
                        return
                    }
                }
            }

            // 检查账户状态
            logger.info("🔐 正在检查iCloud账户状态...")
            let status = try await container.accountStatus()
            logger.info("📋 iCloud账户状态: \(self.accountStatusDescription(status))")

            await MainActor.run {
                self.accountStatus = status
                self.isAvailable = (status == .available) && networkMonitor.isConnected
            }

            if status != .available {
                let error: CloudKitBackupError = status == .noAccount ? .accountNotAvailable : .containerNotAvailable
                logger.error("❌ iCloud账户不可用: \(error.localizedDescription)")
                await MainActor.run {
                    self.lastError = error
                }
                return
            }



            // 基于账户状态判断可用性（避免额外的网络请求）
            logger.info("✅ CloudKit基础检查完成，基于账户状态判断可用性")

            logger.info("✅ CloudKit可用性检查完成，状态: 可用")

        } catch {
            logger.error("❌ CloudKit可用性检查失败: \(error.localizedDescription)")
            await MainActor.run {
                self.lastError = .unknownError(error)
                self.isAvailable = false
            }
        }
    }

    /// 获取账户状态描述
    private func accountStatusDescription(_ status: CKAccountStatus) -> String {
        switch status {
        case .available:
            return "可用"
        case .noAccount:
            return "未登录iCloud账户"
        case .restricted:
            return "受限制"
        case .couldNotDetermine:
            return "无法确定"
        case .temporarilyUnavailable:
            return "暂时不可用"
        @unknown default:
            return "未知状态"
        }
    }

    // MARK: - CKRecord操作
    
    /// 保存单个记录
    func saveRecord(_ record: CKRecord) async -> CloudKitOperationResult<CKRecord> {
        logger.info("💾 准备保存记录: \(record.recordType) - \(record.recordID.recordName)")

        guard isAvailable else {
            logger.error("❌ CloudKit不可用，无法保存记录")
            return .failure(.containerNotAvailable)
        }

        logger.info("🔄 开始保存记录到CloudKit...")

        return await withRetry(maxAttempts: maxRetryAttempts) {
            do {
                let savedRecord = try await self.privateDatabase.save(record)
                self.logger.info("✅ 记录保存成功: \(record.recordType)")
                return .success(savedRecord)
            } catch let error as CKError {
                // 特殊处理限流错误
                if error.code == .requestRateLimited {
                    let retryAfter = error.retryAfterSeconds ?? 30.0
                    self.logger.warning("⚠️ CloudKit请求被限流，需要等待 \(retryAfter) 秒")
                    return .failure(.requestRateLimited(retryAfter: retryAfter))
                }

                let mappedError = self.mapCKError(error)
                self.logger.error("❌ CloudKit错误: \(error.localizedDescription) (代码: \(error.code.rawValue))")
                return .failure(mappedError)
            } catch {
                self.logger.error("❌ 未知错误: \(error.localizedDescription)")
                return .failure(.unknownError(error))
            }
        }
    }
    
    /// 批量保存记录
    func saveRecords(_ records: [CKRecord]) async -> CloudKitOperationResult<[CKRecord]> {
        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }
        
        guard !records.isEmpty else {
            return .success([])
        }
        
        return await withRetry(maxAttempts: maxRetryAttempts) {
            await self.performBatchSave(records)
        }
    }
    
    /// 执行批量保存操作
    private func performBatchSave(_ records: [CKRecord]) async -> CloudKitOperationResult<[CKRecord]> {
        return await withCheckedContinuation { continuation in
            let operation = CKModifyRecordsOperation(recordsToSave: records, recordIDsToDelete: nil)
            
            operation.savePolicy = .changedKeys
            operation.qualityOfService = .userInitiated
            
            var savedRecords: [CKRecord] = []
            var errors: [String] = []
            
            operation.perRecordSaveBlock = { recordID, result in
                switch result {
                case .success(let record):
                    savedRecords.append(record)
                case .failure(let error):
                    errors.append("Record \(recordID): \(error.localizedDescription)")
                }
            }
            
            operation.modifyRecordsResultBlock = { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        continuation.resume(returning: .success(savedRecords))
                    case .failure(let error):
                        let backupError = self.mapCKError(error as! CKError)
                        continuation.resume(returning: .failure(backupError))
                    }
                }
            }
            
            privateDatabase.add(operation)
        }
    }
    
    /// 获取记录
    func fetchRecord(with recordID: CKRecord.ID) async -> CloudKitOperationResult<CKRecord> {
        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }
        
        return await withRetry(maxAttempts: maxRetryAttempts) {
            do {
                let record = try await self.privateDatabase.record(for: recordID)
                return .success(record)
            } catch let error as CKError {
                if error.code == .unknownItem {
                    return .failure(.recordNotFound(recordID.recordName))
                }
                return .failure(self.mapCKError(error))
            } catch {
                return .failure(.unknownError(error))
            }
        }
    }
    
    /// 查询记录
    func queryRecords(with query: CKQuery) async -> CloudKitOperationResult<[CKRecord]> {
        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }
        
        return await withRetry(maxAttempts: maxRetryAttempts) {
            do {
                let (matchResults, _) = try await self.privateDatabase.records(matching: query)
                let records = matchResults.compactMap { _, result in
                    try? result.get()
                }
                return .success(records)
            } catch let error as CKError {
                return .failure(self.mapCKError(error))
            } catch {
                return .failure(.unknownError(error))
            }
        }
    }
    
    /// 删除记录
    func deleteRecord(with recordID: CKRecord.ID) async -> CloudKitOperationResult<CKRecord.ID> {
        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }

        return await withRetry(maxAttempts: maxRetryAttempts) {
            do {
                let deletedID = try await self.privateDatabase.deleteRecord(withID: recordID)
                return .success(deletedID)
            } catch let error as CKError {
                return .failure(self.mapCKError(error))
            } catch {
                return .failure(.unknownError(error))
            }
        }
    }

    /// 清空所有备份记录（危险操作）
    func clearAllBackupRecords() async -> CloudKitOperationResult<Int> {
        logger.warning("🚨 开始清空所有CloudKit备份记录...")

        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }

        var totalDeleted = 0

        // 删除所有备份相关的记录类型
        let recordTypesToClear = [
            CloudKitRecordTypes.backupProduct,
            CloudKitRecordTypes.backupUsageRecord,
            CloudKitRecordTypes.backupRelatedExpense,
            CloudKitRecordTypes.backupCategory,
            CloudKitRecordTypes.backupTag,
            CloudKitRecordTypes.backupLoanRecord,
            CloudKitRecordTypes.backupExpenseType,
            CloudKitRecordTypes.backupPurchaseChannel,
            CloudKitRecordTypes.backupReminder,
            CloudKitRecordTypes.backupProductLink,
            CloudKitRecordTypes.backupConversation,
            CloudKitRecordTypes.backupMessage,
            CloudKitRecordTypes.backupMetadata
        ]

        for recordType in recordTypesToClear {
            logger.info("🗑️ 清理记录类型: \(recordType)")

            // 查询该类型的所有记录
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))

            do {
                let (matchResults, _) = try await privateDatabase.records(matching: query)
                let recordIDs = matchResults.compactMap { (recordID, result) -> CKRecord.ID? in
                    switch result {
                    case .success:
                        return recordID
                    case .failure:
                        return nil
                    }
                }

                if !recordIDs.isEmpty {
                    logger.info("📋 找到 \(recordIDs.count) 个 \(recordType) 记录，准备删除...")

                    // 批量删除记录
                    let deleteResult = await deleteRecords(recordIDs)
                    if deleteResult.success {
                        let deletedCount = deleteResult.data?.count ?? 0
                        totalDeleted += deletedCount
                        logger.info("✅ 成功删除 \(deletedCount) 个 \(recordType) 记录")
                    } else {
                        logger.error("❌ 删除 \(recordType) 记录失败: \(deleteResult.error?.localizedDescription ?? "未知错误")")
                    }
                } else {
                    logger.info("ℹ️ 没有找到 \(recordType) 记录")
                }

            } catch {
                logger.error("❌ 查询 \(recordType) 记录失败: \(error.localizedDescription)")
            }
        }

        logger.info("🎉 清空操作完成，总共删除了 \(totalDeleted) 个记录")
        return .success(totalDeleted)
    }

    /// 批量删除记录
    func deleteRecords(_ recordIDs: [CKRecord.ID]) async -> CloudKitOperationResult<[CKRecord.ID]> {
        guard isAvailable else {
            return .failure(.containerNotAvailable)
        }

        guard !recordIDs.isEmpty else {
            return .success([])
        }

        return await withRetry(maxAttempts: maxRetryAttempts) {
            await self.performBatchDelete(recordIDs)
        }
    }

    /// 执行批量删除操作
    private func performBatchDelete(_ recordIDs: [CKRecord.ID]) async -> CloudKitOperationResult<[CKRecord.ID]> {
        return await withCheckedContinuation { continuation in
            let operation = CKModifyRecordsOperation(recordsToSave: nil, recordIDsToDelete: recordIDs)

            operation.qualityOfService = .userInitiated

            var deletedRecordIDs: [CKRecord.ID] = []
            var errors: [String] = []

            operation.perRecordDeleteBlock = { recordID, result in
                switch result {
                case .success:
                    deletedRecordIDs.append(recordID)
                case .failure(let error):
                    errors.append("Record \(recordID): \(error.localizedDescription)")
                }
            }

            operation.modifyRecordsResultBlock = { result in
                switch result {
                case .success:
                    if !errors.isEmpty {
                        continuation.resume(returning: .failure(.batchOperationFailed(errors)))
                    } else {
                        continuation.resume(returning: .success(deletedRecordIDs))
                    }
                case .failure(let error):
                    if let ckError = error as? CKError {
                        continuation.resume(returning: .failure(self.mapCKError(ckError)))
                    } else {
                        continuation.resume(returning: .failure(.unknownError(error)))
                    }
                }
            }

            self.privateDatabase.add(operation)
        }
    }

    // MARK: - CKAsset操作

    /// 创建CKAsset从Binary数据
    func createAsset(from binaryContainer: BinaryDataContainer, fileName: String) async -> CloudKitOperationResult<CKAsset> {
        guard let data = binaryContainer.data else {
            return .failure(.invalidData("Binary数据为空"))
        }

        do {
            // 创建临时文件
            let tempURL = try createTemporaryFile(fileName: fileName)

            // 根据策略处理数据
            switch binaryContainer.strategy {
            case .base64:
                guard let binaryData = Data(base64Encoded: data) else {
                    return .failure(.invalidData("Base64数据解码失败"))
                }
                try binaryData.write(to: tempURL)
            case .fileReference:
                // 如果是文件引用，直接复制文件
                let sourceURL = URL(fileURLWithPath: data)
                try FileManager.default.copyItem(at: sourceURL, to: tempURL)
            case .excluded:
                return .failure(.invalidData("Binary数据被排除"))
            }

            // 创建CKAsset
            let asset = CKAsset(fileURL: tempURL)

            // 记录临时文件用于后续清理
            temporaryAssetURLs.insert(tempURL)

            return .success(asset)
        } catch {
            return .failure(.assetUploadFailed(error.localizedDescription))
        }
    }

    /// 从CKAsset提取Binary数据
    func extractBinaryData(from asset: CKAsset, strategy: BinaryDataStrategy) async -> CloudKitOperationResult<BinaryDataContainer> {
        guard let fileURL = asset.fileURL else {
            return .failure(.invalidData("Asset文件URL为空"))
        }

        do {
            let data = try Data(contentsOf: fileURL)
            let fileName = fileURL.lastPathComponent
            let mimeType = getMimeType(for: fileURL)

            let processedData: String
            switch strategy {
            case .base64:
                processedData = data.base64EncodedString()
            case .fileReference:
                // 保存到应用文档目录
                let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let savedURL = documentsURL.appendingPathComponent(fileName)
                try data.write(to: savedURL)
                processedData = savedURL.path
            case .excluded:
                return .failure(.invalidData("不应该处理被排除的数据"))
            }

            let container = BinaryDataContainer(
                strategy: strategy,
                data: processedData,
                fileName: fileName,
                mimeType: mimeType,
                size: Int64(data.count),
                checksum: data.base64EncodedString().sha256
            )

            return .success(container)
        } catch {
            return .failure(.assetUploadFailed(error.localizedDescription))
        }
    }

    // MARK: - 临时文件管理

    /// 创建临时文件
    private func createTemporaryFile(fileName: String) throws -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let tempURL = tempDir.appendingPathComponent(fileName)

        // 如果文件已存在，删除它
        if FileManager.default.fileExists(atPath: tempURL.path) {
            try FileManager.default.removeItem(at: tempURL)
        }

        return tempURL
    }

    /// 清理临时文件
    func cleanupTemporaryFiles() {
        for url in temporaryAssetURLs {
            try? FileManager.default.removeItem(at: url)
        }
        temporaryAssetURLs.removeAll()
    }

    /// 获取文件MIME类型
    private func getMimeType(for url: URL) -> String {
        let pathExtension = url.pathExtension.lowercased()
        switch pathExtension {
        case "jpg", "jpeg":
            return "image/jpeg"
        case "png":
            return "image/png"
        case "mp3":
            return "audio/mpeg"
        case "m4a":
            return "audio/mp4"
        case "json":
            return "application/json"
        default:
            return "application/octet-stream"
        }
    }

    // MARK: - 重试机制

    /// 带重试的操作执行
    private func withRetry<T>(
        maxAttempts: Int,
        operation: @escaping () async -> CloudKitOperationResult<T>
    ) async -> CloudKitOperationResult<T> {
        var lastResult: CloudKitOperationResult<T>?

        for attempt in 1...maxAttempts {
            let result = await operation()
            lastResult = result

            if result.success {
                return result
            }

            // 特殊处理限流错误
            if case .requestRateLimited(let retryAfter) = result.error {
                logger.warning("⚠️ 遇到限流，等待 \(retryAfter) 秒后重试 (尝试 \(attempt)/\(maxAttempts))")

                if attempt < maxAttempts {
                    do {
                        try await Task.sleep(nanoseconds: UInt64(retryAfter * 1_000_000_000))
                        logger.info("⏰ 限流等待完成，继续重试...")
                        continue
                    } catch {
                        logger.warning("⚠️ 限流等待被中断: \(error.localizedDescription)")
                        break
                    }
                } else {
                    logger.error("❌ 达到最大重试次数，限流错误无法解决")
                    break
                }
            }

            // 检查是否应该重试其他错误
            guard shouldRetry(error: result.error, attempt: attempt, maxAttempts: maxAttempts) else {
                break
            }

            // 计算重试延迟（指数退避）
            let delay = calculateRetryDelay(attempt: attempt, baseDelay: baseRetryDelay)
            logger.info("🔄 等待 \(delay) 秒后重试 (尝试 \(attempt)/\(maxAttempts))")

            // 等待重试
            do {
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            } catch {
                logger.warning("⚠️ 重试等待被中断: \(error.localizedDescription)")
                break
            }
        }

        return lastResult ?? .failure(.unknownError(NSError(domain: "RetryFailed", code: -1)))
    }

    /// 判断是否应该重试
    private func shouldRetry(error: CloudKitBackupError?, attempt: Int, maxAttempts: Int) -> Bool {
        guard attempt < maxAttempts, let error = error else {
            return false
        }

        switch error {
        case .networkUnavailable, .unknownError:
            return true
        case .quotaExceeded, .accountNotAvailable, .containerNotAvailable:
            return false
        case .recordNotFound, .invalidData, .dataEncodingFailed, .dataSizeExceeded:
            return false
        case .assetUploadFailed, .batchOperationFailed:
            return true
        case .requestRateLimited:
            return true // 限流错误应该重试
        case .recordConflict:
            return false // 记录冲突不应该简单重试，需要特殊处理
        }
    }

    /// 计算重试延迟（指数退避）
    private func calculateRetryDelay(attempt: Int, baseDelay: TimeInterval) -> TimeInterval {
        let exponentialDelay = baseDelay * pow(2.0, Double(attempt - 1))
        let jitter = Double.random(in: 0.8...1.2) // 添加抖动避免雷群效应
        return exponentialDelay * jitter
    }

    // MARK: - CKRecord映射

    /// 从备份实体创建CKRecord（优化版本 - 分离大型数据）
    func createCKRecord<T: BackupEntity>(from entity: T) async -> CloudKitOperationResult<CKRecord> {
        let recordType = "Backup\(entity.entityType)"
        let recordID = CKRecord.ID(recordName: entity.id.uuidString)
        let record = CKRecord(recordType: recordType, recordID: recordID)

        do {
            // 使用反射或编码器将实体数据转换为CKRecord字段
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(entity)

            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                for (key, value) in json {
                    // 跳过系统字段
                    if key == "id" || key == "entityType" {
                        continue
                    }

                    // 检查是否是二进制数据容器
                    if let binaryDict = value as? [String: Any],
                       let strategy = binaryDict["strategy"] as? String,
                       let data = binaryDict["data"] as? String,
                       let fileName = binaryDict["fileName"] as? String {

                        // 处理二进制数据：创建CKAsset而不是直接存储
                        let binaryContainer = BinaryDataContainer(
                            strategy: BinaryDataStrategy(rawValue: strategy) ?? .base64,
                            data: data,
                            fileName: fileName,
                            mimeType: binaryDict["mimeType"] as? String ?? "application/octet-stream",
                            size: binaryDict["size"] as? Int64 ?? 0,
                            checksum: binaryDict["checksum"] as? String ?? ""
                        )

                        let assetResult = await createAsset(from: binaryContainer, fileName: fileName)
                        if assetResult.success, let asset = assetResult.data {
                            record[key] = asset
                            logger.info("📎 二进制数据已转换为CKAsset: \(fileName)")
                        } else {
                            logger.warning("⚠️ 二进制数据转换失败，跳过: \(fileName)")
                            // 跳过这个字段，不存储失败的二进制数据
                            continue
                        }
                    } else {
                        // 普通字段：智能处理大型数据
                        let processedValue = await processLargeFieldValue(value, fieldName: key, recordId: recordID.recordName)
                        record[key] = processedValue
                    }
                }
            }

            return .success(record)
        } catch {
            return .failure(.invalidData("实体序列化失败: \(error.localizedDescription)"))
        }
    }

    /// 从CKRecord创建备份实体（优化版本 - 处理CKAsset）
    func createBackupEntity<T: BackupEntity>(from record: CKRecord, entityType: T.Type) async -> CloudKitOperationResult<T> {
        do {
            var jsonDict: [String: Any] = [:]

            // 添加系统字段
            jsonDict["id"] = record.recordID.recordName
            jsonDict["entityType"] = String(describing: entityType).replacingOccurrences(of: "Backup", with: "")
            jsonDict["lastModified"] = record.modificationDate?.iso8601String ?? Date().iso8601String

            // 转换CKRecord字段
            for key in record.allKeys() {
                if let value = record[key] {
                    // 检查是否是CKAsset
                    if let asset = value as? CKAsset {
                        // 判断是否是文本CKAsset还是二进制CKAsset
                        let fileName = asset.fileURL?.lastPathComponent ?? ""

                        if fileName.hasSuffix(".txt") {
                            // 处理大型文本字段的CKAsset
                            do {
                                if let fileURL = asset.fileURL {
                                    let textContent = try String(contentsOf: fileURL, encoding: .utf8)
                                    jsonDict[key] = textContent
                                    logger.info("📝 大型文本CKAsset已恢复: \(fileName)")
                                } else {
                                    logger.warning("⚠️ 文本CKAsset文件URL为空，跳过字段: \(key)")
                                    jsonDict[key] = nil
                                }
                            } catch {
                                logger.warning("⚠️ 文本CKAsset读取失败，跳过字段: \(key) - \(error.localizedDescription)")
                                jsonDict[key] = nil
                            }
                        } else {
                            // 处理二进制数据CKAsset
                            let binaryResult = await extractBinaryData(from: asset, strategy: .base64)
                            if binaryResult.success, let binaryContainer = binaryResult.data {
                                // 将二进制容器转换为字典格式
                                let binaryDict: [String: Any] = [
                                    "strategy": binaryContainer.strategy.rawValue,
                                    "data": binaryContainer.data ?? "",
                                    "fileName": binaryContainer.fileName,
                                    "mimeType": binaryContainer.mimeType,
                                    "size": binaryContainer.size,
                                    "checksum": binaryContainer.checksum
                                ]
                                jsonDict[key] = binaryDict
                                logger.info("📎 二进制CKAsset已转换回数据: \(binaryContainer.fileName ?? "unknown")")
                            } else {
                                logger.warning("⚠️ 二进制CKAsset转换失败，跳过字段: \(key)")
                                jsonDict[key] = nil
                            }
                        }
                    } else {
                        jsonDict[key] = convertFromCKRecordValue(value)
                    }
                }
            }

            // 序列化为JSON数据
            let jsonData = try JSONSerialization.data(withJSONObject: jsonDict)

            // 反序列化为实体
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let entity = try decoder.decode(entityType, from: jsonData)

            return .success(entity)
        } catch {
            return .failure(.invalidData("CKRecord转换失败: \(error.localizedDescription)"))
        }
    }

    /// 转换值为CKRecord兼容类型
    private func convertToCKRecordValue(_ value: Any) -> CKRecordValue? {
        switch value {
        case let string as String:
            return string as CKRecordValue
        case let number as NSNumber:
            return number as CKRecordValue
        case let date as Date:
            return date as CKRecordValue
        case let data as Data:
            return data as CKRecordValue
        case let array as [Any]:
            return array.compactMap { convertToCKRecordValue($0) } as CKRecordValue
        case let dict as [String: Any]:
            // 对于复杂对象，转换为JSON字符串
            if let jsonData = try? JSONSerialization.data(withJSONObject: dict),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                return jsonString as CKRecordValue
            }
            return nil
        default:
            // 尝试转换为字符串
            return String(describing: value) as CKRecordValue
        }
    }

    /// 从CKRecord值转换
    private func convertFromCKRecordValue(_ value: CKRecordValue) -> Any {
        switch value {
        case let string as String:
            // 尝试解析JSON字符串
            if let data = string.data(using: .utf8),
               let json = try? JSONSerialization.jsonObject(with: data) {
                return json
            }
            return string
        case let number as NSNumber:
            return number
        case let date as Date:
            return date
        case let data as Data:
            return data
        case let array as [CKRecordValue]:
            return array.map { convertFromCKRecordValue($0) }
        default:
            return value
        }
    }

    // MARK: - 错误处理
    
    /// 诊断CloudKit错误的详细信息（用于调试）
    private func diagnoseCloudKitError(_ error: CKError) {
        logger.info("🏥 CloudKit错误诊断报告:")
        logger.info("   • 错误代码: \(error.code.rawValue)")
        logger.info("   • 枚举表示: \(String(describing: error.code))")
        logger.info("   • 本地描述: \(error.localizedDescription)")
        logger.info("   • 错误域: \((error as NSError).domain)")
        
        // 检查特殊的UserInfo键
        if let retryAfter = error.retryAfterSeconds {
            logger.info("   • 重试等待时间: \(retryAfter)秒")
        }
        
        if let httpStatus = error.userInfo["NSHTTPStatusCode"] as? Int {
            logger.info("   • HTTP状态码: \(httpStatus)")
        }
        
        if let serverRequestID = error.userInfo["CKErrorServerRequestID"] as? String {
            logger.info("   • 服务器请求ID: \(serverRequestID)")
        }
        
        // 完整UserInfo（用于深度调试）
        logger.info("   • 完整UserInfo: \(error.userInfo)")
        
        // 特殊标记错误15的诊断
        if error.code.rawValue == 15 {
            logger.warning("🚨 这是错误15！文档说它是constraintViolation，但运行时行为不同")
            logger.warning("🔬 请检查HTTP状态码和其他UserInfo信息以确认实际错误类型")
        }
    }

    /// 映射CKError到CloudKitBackupError
    private func mapCKError(_ error: CKError) -> CloudKitBackupError {
        // 使用专门的诊断函数进行详细分析
        diagnoseCloudKitError(error)

        // 特殊处理：错误15的运行时行为与文档不符
        // 实际表现为临时性服务器错误(503)，应该重试而不是当作约束冲突
        if error.code.rawValue == 15 {
            logger.warning("🚨 检测到错误15：虽然文档说是constraintViolation，但实际表现为服务器临时不可用(503)")
            logger.warning("🔄 将错误15作为可重试的网络错误处理")
            
            // 检查是否包含限流信息
            if let retryAfter = error.retryAfterSeconds {
                return .requestRateLimited(retryAfter: retryAfter)
            } else {
                return .networkUnavailable // 作为临时网络问题处理，允许重试
            }
        }

        // 标准的枚举匹配处理
        switch error.code {
        case .notAuthenticated:
            return .accountNotAvailable
        case .networkUnavailable, .networkFailure, .serviceUnavailable:
            return .networkUnavailable
        case .quotaExceeded:
            return .quotaExceeded
        case .unknownItem:
            return .recordNotFound(error.localizedDescription)
        case .assetFileNotFound, .assetFileModified:
            return .assetUploadFailed(error.localizedDescription)
        case .limitExceeded:
            // 检查是否是请求大小超限
            if error.localizedDescription.contains("request bytes exceeds limit") {
                logger.error("📊 请求大小超限错误，需要减少批量大小")
                return .dataSizeExceeded(0)
            } else {
                return .batchOperationFailed(["操作限制超出"])
            }
        case .invalidArguments:
            return .invalidData(error.localizedDescription)
        case .requestRateLimited:
            let retryAfter = error.retryAfterSeconds ?? 30.0
            logger.warning("⚠️ CloudKit限流错误，建议等待 \(retryAfter) 秒")
            return .requestRateLimited(retryAfter: retryAfter)
        case .serverRecordChanged:
            logger.warning("⚠️ 服务器记录已更改，这是同步冲突")
            return .recordConflict
        case .constraintViolation:
            logger.error("🚫 CloudKit约束冲突：记录类型不存在或字段约束违反")
            return .invalidData("记录类型不存在或约束冲突，请检查CloudKit Schema配置")
        case .batchRequestFailed:
            return .batchOperationFailed(["批量请求失败"])
        default:
            // 检查错误描述中是否包含大小相关的关键词
            let errorDescription = error.localizedDescription.lowercased()
            if errorDescription.contains("request bytes exceeds limit") ||
               errorDescription.contains("too large") ||
               errorDescription.contains("exceeds limit") ||
               errorDescription.contains("size limit") {
                logger.error("📊 检测到大小相关错误: \(error.localizedDescription)")
                return .dataSizeExceeded(0)
            }

            logger.warning("⚠️ 未处理的CloudKit错误: RawValue=\(error.code.rawValue), Enum=\(String(describing: error.code))")
            logger.warning("⚠️ 如果这是错误15，可能是枚举映射问题，已在上面特殊处理")
            return .unknownError(error)
        }
    }

    // MARK: - 进度跟踪

    /// 更新操作进度
    private func updateProgress(_ progress: Double) {
        DispatchQueue.main.async {
            self.operationProgress = progress
        }
    }

    /// 设置操作状态
    private func setOperating(_ operating: Bool) {
        DispatchQueue.main.async {
            self.isOperating = operating
        }
    }

    /// 设置错误
    private func setError(_ error: CloudKitBackupError?) {
        DispatchQueue.main.async {
            self.lastError = error
        }
    }
}

// MARK: - 扩展工具

extension Date {
    /// ISO8601字符串表示
    var iso8601String: String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}

extension String {
    /// 从ISO8601字符串创建Date
    var iso8601Date: Date? {
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: self)
    }
}

// MARK: - CloudKit记录类型常量

/// CloudKit记录类型常量
struct CloudKitRecordTypes {
    static let backupProduct = "BackupProduct"
    static let backupUsageRecord = "BackupUsageRecord"
    static let backupRelatedExpense = "BackupRelatedExpense"
    static let backupCategory = "BackupCategory"
    static let backupTag = "BackupTag"
    static let backupLoanRecord = "BackupLoanRecord"
    static let backupExpenseType = "BackupExpenseType"
    static let backupPurchaseChannel = "BackupPurchaseChannel"
    static let backupReminder = "BackupReminder"
    static let backupProductLink = "BackupProductLink"
    static let backupConversation = "BackupConversation"
    static let backupMessage = "BackupMessage"
    static let backupMetadata = "BackupMetadata"
    
    // 新增：简化的备份元数据记录类型，确保CloudKit自动schema创建兼容性
    static let simpleBackupMetadata = "BackupMetadata"
}

// MARK: - CloudKit备份服务扩展

extension CloudKitBackupService {

    // MARK: - 高级批量操作

    /// 智能批量保存（自动分批处理，优化批量大小）
    func saveLargeRecordBatch(_ records: [CKRecord], batchSize: Int = 50) async -> CloudKitOperationResult<[CKRecord]> {
        guard !records.isEmpty else {
            return .success([])
        }

        setOperating(true)
        defer { setOperating(false) }

        var allSavedRecords: [CKRecord] = []

        // 动态调整批量大小：根据记录类型和估算大小
        let adjustedBatchSize = calculateOptimalBatchSize(for: records, defaultSize: batchSize)
        logger.info("📊 使用优化批量大小: \(adjustedBatchSize) (原始: \(batchSize))")

        let batches = records.chunked(into: adjustedBatchSize)

        for (index, batch) in batches.enumerated() {
            // 更新进度
            let progress = Double(index) / Double(batches.count)
            updateProgress(progress)

            logger.info("📤 上传批次 \(index + 1)/\(batches.count): \(batch.count) 条记录")
            let result = await saveRecordsWithRetry(batch)

            if result.success, let savedRecords = result.data {
                allSavedRecords.append(contentsOf: savedRecords)
                logger.info("✅ 批次 \(index + 1) 上传成功")
            } else if let error = result.error {
                logger.error("❌ 批次 \(index + 1) 上传失败: \(error.localizedDescription)")
                setError(error)
                return .failure(error)
            }

            // 批次间添加延迟，避免过载和限流
            if index < batches.count - 1 {
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        }

        updateProgress(1.0)
        logger.info("✅ 所有批次上传完成: \(allSavedRecords.count) 条记录")
        return .success(allSavedRecords)
    }

    /// 计算最优批量大小
    private func calculateOptimalBatchSize(for records: [CKRecord], defaultSize: Int) -> Int {
        guard !records.isEmpty else { return defaultSize }

        // 估算单个记录的平均大小
        let sampleRecord = records.first!
        var estimatedSize = 0

        // 计算记录字段大小
        for key in sampleRecord.allKeys() {
            if let value = sampleRecord[key] {
                switch value {
                case let stringValue as String:
                    estimatedSize += stringValue.utf8.count
                case let dataValue as Data:
                    estimatedSize += dataValue.count
                case is CKAsset:
                    // CKAsset不计入记录大小，但会增加请求复杂度
                    estimatedSize += 100 // 只计算引用开销
                default:
                    estimatedSize += 50 // 其他类型的估算开销
                }
            }
        }

        // CloudKit单次请求限制约为1MB，保守估计使用800KB
        let maxRequestSize = 800 * 1024 // 800KB
        let estimatedRecordSize = max(estimatedSize, 1024) // 最小1KB
        let calculatedBatchSize = maxRequestSize / estimatedRecordSize

        // 限制批量大小范围
        let minBatchSize = 10
        let maxBatchSize = min(defaultSize, 100)
        let optimalSize = max(minBatchSize, min(calculatedBatchSize, maxBatchSize))

        logger.info("📊 批量大小计算: 估算记录大小=\(estimatedRecordSize)字节, 建议批量=\(optimalSize)")
        return optimalSize
    }

    /// 带重试和错误处理的记录保存
    private func saveRecordsWithRetry(_ records: [CKRecord]) async -> CloudKitOperationResult<[CKRecord]> {
        var currentBatchSize = records.count
        var attempts = 0
        let maxAttempts = 3

        while attempts < maxAttempts {
            attempts += 1

            let result = await saveRecords(records)

            if result.success {
                return result
            }

            // 检查是否是请求大小超限错误
            if let error = result.error,
               error.localizedDescription.contains("request bytes exceeds limit") {

                // 如果批量大小已经很小，无法再减少
                if currentBatchSize <= 5 {
                    logger.error("❌ 批量大小已降至最小(\(currentBatchSize))，仍然超限")
                    return result
                }

                // 减少批量大小并重试
                currentBatchSize = max(currentBatchSize / 2, 5)
                logger.warning("⚠️ 请求超限，减少批量大小到 \(currentBatchSize) 并重试 (尝试 \(attempts)/\(maxAttempts))")

                // 重新分批处理
                let smallerBatches = records.chunked(into: currentBatchSize)
                var allResults: [CKRecord] = []

                for batch in smallerBatches {
                    let batchResult = await saveRecords(batch)
                    if batchResult.success, let savedRecords = batchResult.data {
                        allResults.append(contentsOf: savedRecords)
                    } else {
                        return batchResult // 返回失败结果
                    }

                    // 小批次间短暂延迟
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                }

                return .success(allResults)
            } else {
                // 其他错误，直接返回
                return result
            }
        }

        return .failure(.unknownError(NSError(domain: "BatchSaveRetryFailed", code: -1)))
    }

    /// 智能处理大型字段值
    private func processLargeFieldValue(_ value: Any, fieldName: String, recordId: String) async -> CKRecordValue? {
        let convertedValue = convertToCKRecordValue(value)

        // 检查是否是字符串类型
        guard let stringValue = convertedValue as? String else {
            return convertedValue
        }

        // 定义大型文本字段的阈值
        let maxFieldSize = 8000 // CloudKit字段建议最大大小
        let largeTextThreshold = 5000 // 超过此大小考虑使用CKAsset

        // 如果字符串不大，直接返回
        if stringValue.count <= maxFieldSize {
            return convertedValue
        }

        // 对于特定的大型文本字段，使用CKAsset存储
        let largeTextFields = ["notes", "memories", "purchaseNotes", "warrantyDetails", "text", "description"]
        let isLargeTextField = largeTextFields.contains { fieldName.lowercased().contains($0.lowercased()) }

        if isLargeTextField && stringValue.count > largeTextThreshold {
            // 将大型文本转换为CKAsset
            logger.info("📝 大型文本字段 \(fieldName) (\(stringValue.count) 字符) 转换为CKAsset")

            do {
                // 创建临时文件
                let fileName = "\(fieldName)_\(recordId).txt"
                let tempURL = try createTemporaryFile(fileName: fileName)

                // 写入文本数据
                try stringValue.write(to: tempURL, atomically: true, encoding: .utf8)

                // 创建CKAsset
                let asset = CKAsset(fileURL: tempURL)

                // 记录临时文件用于后续清理
                temporaryAssetURLs.insert(tempURL)

                logger.info("✅ 大型文本字段已转换为CKAsset: \(fileName)")
                return asset
            } catch {
                logger.warning("⚠️ 大型文本字段转换CKAsset失败，使用截断: \(error.localizedDescription)")
                // 转换失败，使用截断处理
                return String(stringValue.prefix(maxFieldSize)) + "...[截断]" as CKRecordValue
            }
        } else {
            // 非大型文本字段或不够大，使用截断处理
            logger.warning("⚠️ 字段 \(fieldName) 过大 (\(stringValue.count) 字符)，截断处理")
            return String(stringValue.prefix(maxFieldSize)) + "...[截断]" as CKRecordValue
        }
    }

    /// 增量同步查询
    func fetchRecordsSince(_ date: Date, recordType: String) async -> CloudKitOperationResult<[CKRecord]> {
        let predicate = NSPredicate(format: "modificationDate > %@", date as NSDate)
        let query = CKQuery(recordType: recordType, predicate: predicate)
        query.sortDescriptors = [NSSortDescriptor(key: "modificationDate", ascending: true)]

        return await queryRecords(with: query)
    }

    /// 获取所有记录类型的最新修改时间
    func getLastModificationDates() async -> CloudKitOperationResult<[String: Date]> {
        var lastModificationDates: [String: Date] = [:]

        let recordTypes = [
            CloudKitRecordTypes.backupProduct,
            CloudKitRecordTypes.backupUsageRecord,
            CloudKitRecordTypes.backupRelatedExpense,
            CloudKitRecordTypes.backupCategory,
            CloudKitRecordTypes.backupTag,
            CloudKitRecordTypes.backupLoanRecord,
            CloudKitRecordTypes.backupExpenseType,
            CloudKitRecordTypes.backupPurchaseChannel,
            CloudKitRecordTypes.backupReminder,
            CloudKitRecordTypes.backupProductLink,
            CloudKitRecordTypes.backupConversation,
            CloudKitRecordTypes.backupMessage
        ]

        for recordType in recordTypes {
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
            query.sortDescriptors = [NSSortDescriptor(key: "modificationDate", ascending: false)]

            let result = await queryRecords(with: query)
            if result.success, let records = result.data {
                if let latestRecord = records.first {
                    lastModificationDates[recordType] = latestRecord.modificationDate
                }
            } else {
                // 如果某个类型查询失败，继续处理其他类型
                continue
            }
        }

        return .success(lastModificationDates)
    }

    // MARK: - 备份元数据管理

    /// 保存备份元数据
    func saveBackupMetadata(_ container: BackupDataContainer) async -> CloudKitOperationResult<CKRecord> {
        logger.info("💾 开始保存备份元数据...")
        logger.info("📊 备份统计: \(container.totalEntities)个实体, \(container.totalBinaryAssets)个二进制资源")
        logger.info("📱 设备信息: \(container.deviceInfo.deviceModel), iOS \(container.deviceInfo.systemVersion)")

        // 使用时间戳确保记录ID唯一性，避免冲突
        let timestamp = Int64(container.createdAt.timeIntervalSince1970)
        let recordID = CKRecord.ID(recordName: "backup_\(timestamp)")

        // 使用标准命名约定，确保CloudKit能自动创建schema
        logger.info("🧪 使用标准记录类型确保schema兼容...")
        let record = CKRecord(recordType: "BackupMetadata", recordID: recordID)

        logger.info("🆔 记录ID: \(recordID.recordName)")
        logger.info("📅 备份时间戳: \(timestamp)")

        // 设置基础元数据字段（使用简单类型确保schema兼容）
        record["backupVersion"] = container.version as CKRecordValue
        record["timestamp"] = timestamp as CKRecordValue
        record["backupTypeString"] = container.backupType.rawValue as CKRecordValue
        record["entityCount"] = container.totalEntities as CKRecordValue
        record["binaryAssetCount"] = container.totalBinaryAssets as CKRecordValue
        record["estimatedSizeBytes"] = container.estimatedSize as CKRecordValue
        record["checksum"] = container.dataChecksum as CKRecordValue
        record["deviceModel"] = container.deviceInfo.deviceModel as CKRecordValue
        record["systemVersion"] = container.deviceInfo.systemVersion as CKRecordValue
        record["appVersion"] = container.deviceInfo.appVersion as CKRecordValue
        record["deviceId"] = container.deviceInfo.deviceId as CKRecordValue

        // 使用CKAsset方式保存完整备份数据
        do {
            logger.info("📦 将完整备份数据打包为文件...")

            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted

            // 编码完整的备份容器
            let backupData = try encoder.encode(container)
            logger.info("📊 完整备份数据大小: \(backupData.count) 字节 (\(String(format: "%.2f", Double(backupData.count) / 1024.0 / 1024.0)) MB)")

            // 检查是否超过CKAsset限制（250MB）
            let maxAssetSize = 250 * 1024 * 1024 // 250MB
            if backupData.count > maxAssetSize {
                logger.error("❌ 备份数据过大 (\(backupData.count) 字节)，超过CKAsset 250MB限制")
                return .failure(.dataSizeExceeded(backupData.count))
            }

            // 创建临时文件
            let tempDirectory = FileManager.default.temporaryDirectory
            let backupFileName = "backup_\(container.createdAt.timeIntervalSince1970).json"
            let tempFileURL = tempDirectory.appendingPathComponent(backupFileName)

            // 写入临时文件
            try backupData.write(to: tempFileURL)
            logger.info("📁 备份文件已创建: \(tempFileURL.lastPathComponent)")

            // 创建CKAsset
            let asset = CKAsset(fileURL: tempFileURL)
            record["backupDataAsset"] = asset
            record["backupFileName"] = backupFileName
            record["backupFileSize"] = backupData.count

            logger.info("☁️ 备份数据将作为CKAsset上传...")

        } catch {
            logger.error("❌ 备份数据打包失败: \(error.localizedDescription)")
            return .failure(.dataEncodingFailed)
        }

        logger.info("☁️ 正在上传备份数据到CloudKit...")
        let result = await saveRecord(record)

        // 清理临时文件
        if let backupFileName = record["backupFileName"] as? String {
            let tempDirectory = FileManager.default.temporaryDirectory
            let tempFileURL = tempDirectory.appendingPathComponent(backupFileName)

            do {
                try FileManager.default.removeItem(at: tempFileURL)
                logger.info("🗑️ 临时备份文件已清理")
            } catch {
                logger.warning("⚠️ 清理临时文件失败: \(error.localizedDescription)")
            }
        }

        if result.success {
            logger.info("✅ 备份数据上传成功（包含CKAsset文件）")
        } else {
            logger.error("❌ 备份数据上传失败: \(result.error?.localizedDescription ?? "未知错误")")
        }

        return result
    }

    /// 获取备份历史元数据
    func fetchBackupHistory(limit: Int = 50) async -> CloudKitOperationResult<[CKRecord]> {
        let query = CKQuery(recordType: CloudKitRecordTypes.backupMetadata, predicate: NSPredicate(value: true))
        // 使用CloudKit默认支持的modificationDate字段进行排序，避免自定义字段查询问题
        query.sortDescriptors = [NSSortDescriptor(key: "modificationDate", ascending: false)]

        return await queryRecords(with: query)
    }

    // MARK: - 冲突解决

    /// 解决记录冲突
    func resolveConflict(localRecord: CKRecord, serverRecord: CKRecord) -> CKRecord {
        // 简单的冲突解决策略：使用最新修改时间的记录
        let localModificationDate = localRecord.modificationDate ?? Date.distantPast
        let serverModificationDate = serverRecord.modificationDate ?? Date.distantPast

        if localModificationDate > serverModificationDate {
            // 本地记录更新，使用本地记录
            return localRecord
        } else {
            // 服务器记录更新，使用服务器记录
            return serverRecord
        }
    }

    // MARK: - Schema验证

    /// 验证和创建必要的CloudKit记录类型
    func validateAndCreateSchema() async -> CloudKitOperationResult<Bool> {
        logger.info("🔍 验证CloudKit连接性和权限...")

        // 生产环境：只检查账户状态和权限，不创建测试记录
        guard isAvailable && accountStatus == .available else {
            logger.error("❌ CloudKit不可用或账户未登录")
            return .failure(.accountNotAvailable)
        }

        // 简化的连接性测试：使用基本查询避免Schema配置问题
        // 使用简单的predicate，不使用可能未配置的排序字段
        let query = CKQuery(recordType: CloudKitRecordTypes.backupMetadata, predicate: NSPredicate(value: true))
        // 移除排序描述符，避免字段查询配置问题

        let result = await queryRecords(with: query)

        if result.success {
            logger.info("✅ CloudKit连接和Schema验证成功")
            return .success(true)
        } else {
            // 检查是否是Schema相关错误
            if let error = result.error {
                logger.info("ℹ️ Schema验证查询失败: \(error.localizedDescription)")

                // 如果是字段查询相关错误，这是正常的，CloudKit会在首次保存时自动创建Schema
                if error.localizedDescription.contains("not marked queryable") ||
                   error.localizedDescription.contains("unknown item") {
                    logger.info("ℹ️ 未找到现有备份记录或Schema未配置，将在首次备份时自动创建")
                    return .success(true)
                }
            }

            // 其他错误可能表示真正的连接问题
            logger.warning("⚠️ CloudKit连接测试失败，但继续尝试备份操作")
            return .success(true) // 仍然返回成功，让备份操作继续
        }
    }

    // MARK: - 健康检查

    /// 执行CloudKit健康检查
    func performHealthCheck() async -> CloudKitOperationResult<CloudKitHealthStatus> {
        var healthStatus = CloudKitHealthStatus()

        // 检查账户状态
        await checkAvailability()
        healthStatus.accountAvailable = (accountStatus == .available)

        // 检查网络连接
        healthStatus.networkAvailable = networkMonitor.isConnected

        // 检查容器访问
        do {
            let _ = try await container.accountStatus()
            healthStatus.containerAccessible = true
        } catch {
            healthStatus.containerAccessible = false
            healthStatus.lastError = mapCKError(error as? CKError ?? CKError(.internalError))
        }

        // 检查数据库操作 - 使用简化查询避免Schema问题
        let testQuery = CKQuery(recordType: CloudKitRecordTypes.backupMetadata, predicate: NSPredicate(value: true))
        let queryResult = await queryRecords(with: testQuery)

        // 即使查询失败也不一定表示数据库不可用，可能只是Schema未配置
        if queryResult.success {
            healthStatus.databaseOperational = true
        } else if let error = queryResult.error,
                  error.localizedDescription.contains("not marked queryable") ||
                  error.localizedDescription.contains("unknown item") {
            // Schema相关错误不影响数据库可用性判断
            healthStatus.databaseOperational = true
            logger.info("ℹ️ 健康检查：Schema未配置但数据库可用")
        } else {
            healthStatus.databaseOperational = false
            healthStatus.lastError = queryResult.error
        }

        return .success(healthStatus)
    }
}

// MARK: - 辅助结构

/// CloudKit健康状态
struct CloudKitHealthStatus {
    var accountAvailable: Bool = false
    var networkAvailable: Bool = false
    var containerAccessible: Bool = false
    var databaseOperational: Bool = false
    var lastError: CloudKitBackupError?

    var isHealthy: Bool {
        return accountAvailable && networkAvailable && containerAccessible && databaseOperational
    }

    var statusDescription: String {
        if isHealthy {
            return "CloudKit服务正常"
        } else {
            var issues: [String] = []
            if !accountAvailable { issues.append("账户不可用") }
            if !networkAvailable { issues.append("网络不可用") }
            if !containerAccessible { issues.append("容器不可访问") }
            if !databaseOperational { issues.append("数据库操作异常") }
            return "CloudKit服务异常: \(issues.joined(separator: ", "))"
        }
    }
}

// MARK: - 数组扩展

extension Array {
    /// 将数组分块
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

// MARK: - CKError扩展

extension CKError {
    var retryAfterSeconds: TimeInterval? {
        if let retryAfter = userInfo[CKErrorRetryAfterKey] as? TimeInterval {
            return retryAfter
        }
        return nil
    }
}
