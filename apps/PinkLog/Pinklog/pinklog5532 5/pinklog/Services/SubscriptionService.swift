//
//  SubscriptionService.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 核心服务层
//

import Foundation
import CoreData
import SwiftUI
import Combine
import UserNotifications

// MARK: - 订阅服务协议
protocol SubscriptionServiceProtocol {
    func createSubscription(_ subscriptionData: SubscriptionData) async throws -> Product
    func updateSubscription(_ product: Product, with data: SubscriptionData) async throws
    func deleteSubscription(_ product: Product) async throws
    func getAllSubscriptions() -> [Product]
    func getActiveSubscriptions() -> [Product]
    func getSubscriptionsNearRenewal() -> [Product]
    func calculateSubscriptionAnalytics(for product: Product) -> SubscriptionAnalytics
    func generateRecommendations(for product: Product) -> [SubscriptionRecommendation]
    func recordUsageSession(_ session: UsageSession) async throws
    func getUsageSessions(for subscriptionId: UUID) -> [UsageSession]
}

// MARK: - 订阅服务实现
class SubscriptionService: ObservableObject, SubscriptionServiceProtocol {
    private let context: NSManagedObjectContext
    private let coreDataRepository: CoreDataRepository<Product>
    
    @Published var subscriptions: [Product] = []
    @Published var activeSubscriptions: [Product] = []
    @Published var renewalAlerts: [Product] = []
    @Published var recommendations: [SubscriptionRecommendation] = []
    
    private var cancellables = Set<AnyCancellable>()
    private lazy var reminderService = SubscriptionReminderService(context: context)
    
    init(context: NSManagedObjectContext, coreDataRepository: CoreDataRepository<Product>) {
        self.context = context
        self.coreDataRepository = coreDataRepository
        
        loadSubscriptions()
        setupNotifications()
    }
    
    // MARK: - 数据加载
    private func loadSubscriptions() {
        subscriptions = getAllSubscriptions()
        activeSubscriptions = getActiveSubscriptions()
        renewalAlerts = getSubscriptionsNearRenewal()
        generateAllRecommendations()
    }
    
    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.loadSubscriptions()
                }
                // 重新调度提醒
                Task {
                    await self?.reminderService.scheduleAllReminders()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 订阅管理
    func createSubscription(_ subscriptionData: SubscriptionData) async throws -> Product {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let product = Product(context: self.context)
                    
                    // 基本产品信息
                    product.id = subscriptionData.id
                    product.name = subscriptionData.name
                    product.brand = subscriptionData.serviceProvider
                    product.price = subscriptionData.annualCost
                    product.purchaseDate = subscriptionData.startDate
                    product.expiryDate = subscriptionData.nextRenewalDate
                    product.isVirtualProduct = true
                    product.purchaseNotes = (product.purchaseNotes ?? "") + "\n" + (subscriptionData.description ?? "")
                    product.purchaseNotes = subscriptionData.notes
                    
                    // 订阅特有信息（存储在notes或自定义字段中）
                    let subscriptionInfo = [
                        "billingCycle": subscriptionData.billingCycle.rawValue,
                        "costPerCycle": String(subscriptionData.costPerCycle),
                        "currency": subscriptionData.currency,
                        "status": subscriptionData.status.rawValue,
                        "isShared": String(subscriptionData.isShared),
                        "familyMemberCount": String(subscriptionData.familyMemberCount),
                        "customCycleDays": String(subscriptionData.customCycleDays ?? 0)
                    ]
                    
                    if let jsonData = try? JSONSerialization.data(withJSONObject: subscriptionInfo),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        product.purchaseNotes = (product.purchaseNotes ?? "") + "\n[SUBSCRIPTION_DATA]\n" + jsonString
                    }
                    
                    // 设置分类
                    if let categoryName = subscriptionData.category {
                        let category = self.findOrCreateCategory(name: categoryName)
                        product.category = category
                    }
                    
                    try self.context.save()
                    
                    // 调度提醒
                    Task {
                        await self.reminderService.scheduleRenewalReminders(for: product)
                    }
                    
                    continuation.resume(returning: product)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func updateSubscription(_ product: Product, with data: SubscriptionData) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    // 更新基本信息
                    product.name = data.name
                    product.brand = data.serviceProvider
                    product.price = data.annualCost
                    product.expiryDate = data.nextRenewalDate
                    product.purchaseNotes = (product.purchaseNotes ?? "") + "\n" + (data.description ?? "")
                    
                    // 更新订阅特有信息
                    let subscriptionInfo = [
                        "billingCycle": data.billingCycle.rawValue,
                        "costPerCycle": String(data.costPerCycle),
                        "currency": data.currency,
                        "status": data.status.rawValue,
                        "isShared": String(data.isShared),
                        "familyMemberCount": String(data.familyMemberCount),
                        "customCycleDays": String(data.customCycleDays ?? 0)
                    ]
                    
                    if let jsonData = try? JSONSerialization.data(withJSONObject: subscriptionInfo),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        // 移除旧的订阅数据并添加新的
                        var notes = product.purchaseNotes ?? ""
                        if let range = notes.range(of: "[SUBSCRIPTION_DATA]") {
                            let searchRange = range.upperBound..<notes.endIndex
                            let endRange = notes.range(of: "\n", range: searchRange) ?? notes.endIndex..<notes.endIndex
                            notes.removeSubrange(range.lowerBound..<endRange.upperBound)
                        }
                        product.purchaseNotes = notes + "\n[SUBSCRIPTION_DATA]\n" + jsonString
                    }
                    
                    try self.context.save()
                    
                    // 重新调度提醒
                    Task {
                        await self.reminderService.cancelReminders(for: product.id ?? UUID())
                        if product.reminderEnabled {
                            await self.reminderService.scheduleRenewalReminders(for: product)
                        }
                    }
                    
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func deleteSubscription(_ product: Product) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    // 取消相关提醒
                    Task {
                        await self.reminderService.cancelReminders(for: product.id ?? UUID())
                    }
                    
                    self.context.delete(product)
                    try self.context.save()
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - 查询方法
    func getAllSubscriptions() -> [Product] {
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "isVirtualProduct == YES")
        request.sortDescriptors = [NSSortDescriptor(keyPath: \Product.name, ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取订阅列表失败: \(error)")
            return []
        }
    }
    
    func getActiveSubscriptions() -> [Product] {
        return getAllSubscriptions().filter { product in
            let status = product.currentSubscriptionStatus
            return status == .subscriptionActive || status == .subscriptionTrial
        }
    }
    
    func getSubscriptionsNearRenewal() -> [Product] {
        return getAllSubscriptions().filter { $0.isSubscriptionNearExpiry }
    }
    
    // MARK: - 分析功能
    func calculateSubscriptionAnalytics(for product: Product) -> SubscriptionAnalytics {
        guard product.isVirtualProduct,
              let context = product.managedObjectContext else {
            return SubscriptionAnalytics(
                subscriptionId: product.id ?? UUID(),
                totalUsageSessions: 0,
                totalUsageTime: 0,
                averageSessionDuration: 0,
                averageSatisfactionRating: 0,
                averageEmotionalValue: 0,
                costPerSession: 0,
                costPerHour: 0,
                usageFrequency: 0,
                valueScore: 0,
                lastUsedDate: nil,
                daysSinceLastUse: 0,
                usageTrend: 0,
                satisfactionTrend: 0,
                familyUsageDistribution: nil
            )
        }

        var usageRecords: [UsageRecord] = []
        var totalSessions = 0
        var totalTime: Double = 0
        var averageDuration: Double = 0
        var averageEmotional: Double = 0

        context.performAndWait {
            usageRecords = product.usageRecords?.allObjects as? [UsageRecord] ?? []
            totalSessions = usageRecords.count
            totalTime = usageRecords.reduce(0) { $0 + ($1.duration ?? 0) }
            averageDuration = totalSessions > 0 ? totalTime / Double(totalSessions) : 0
            averageEmotional = usageRecords.isEmpty ? 0 : usageRecords.reduce(0) { $0 + Double($1.emotionalValue) } / Double(usageRecords.count)
        }

        let averageSatisfaction = product.averageSatisfaction
        
        let monthlyCost = product.price / 12.0
        let monthlyUsage = product.monthlyUsageFrequency
        let costPerSession = monthlyUsage > 0 ? monthlyCost / monthlyUsage : monthlyCost
        let costPerHour = totalTime > 0 ? monthlyCost / (totalTime / 3600) : 0
        
        let valueScore = product.worthItIndex()
        let lastUsed = product.lastUsageDate
        let daysSinceLastUse = lastUsed != nil ? Calendar.current.dateComponents([.day], from: lastUsed!, to: Date()).day ?? 0 : 0
        
        // 计算使用趋势（最近30天 vs 之前30天）
        let calendar = Calendar.current
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let sixtyDaysAgo = calendar.date(byAdding: .day, value: -60, to: Date()) ?? Date()

        var recentUsage = 0
        var previousUsage = 0
        var recentAvgSatisfaction: Double = 0
        var previousAvgSatisfaction: Double = 0

        context.performAndWait {
            let recentRecords = usageRecords.filter { record in
                guard let date = record.date else { return false }
                return date >= thirtyDaysAgo
            }
            recentUsage = recentRecords.count

            let previousRecords = usageRecords.filter { record in
                guard let date = record.date else { return false }
                return date >= sixtyDaysAgo && date < thirtyDaysAgo
            }
            previousUsage = previousRecords.count

            // 计算满意度趋势
            recentAvgSatisfaction = recentRecords.isEmpty ? 0 : recentRecords.reduce(0) { $0 + Double($1.satisfaction) } / Double(recentRecords.count)
            previousAvgSatisfaction = previousRecords.isEmpty ? 0 : previousRecords.reduce(0) { $0 + Double($1.satisfaction) } / Double(previousRecords.count)
        }

        let usageTrend = previousUsage > 0 ? (Double(recentUsage - previousUsage) / Double(previousUsage)) : 0
        let satisfactionTrend = previousAvgSatisfaction > 0 ? (recentAvgSatisfaction - previousAvgSatisfaction) / previousAvgSatisfaction : 0
        
        return SubscriptionAnalytics(
            subscriptionId: product.id ?? UUID(),
            totalUsageSessions: totalSessions,
            totalUsageTime: totalTime,
            averageSessionDuration: averageDuration,
            averageSatisfactionRating: averageSatisfaction,
            averageEmotionalValue: averageEmotional,
            costPerSession: costPerSession,
            costPerHour: costPerHour,
            usageFrequency: monthlyUsage,
            valueScore: valueScore,
            lastUsedDate: lastUsed,
            daysSinceLastUse: daysSinceLastUse,
            usageTrend: max(-1.0, min(1.0, usageTrend)),
            satisfactionTrend: max(-1.0, min(1.0, satisfactionTrend)),
            familyUsageDistribution: nil // 暂时不实现家庭使用分布
        )
    }
    
    // MARK: - 推荐系统
    func generateRecommendations(for product: Product) -> [SubscriptionRecommendation] {
        guard product.isVirtualProduct else { return [] }
        
        var recommendations: [SubscriptionRecommendation] = []
        let analytics = calculateSubscriptionAnalytics(for: product)
        
        // 使用不足推荐
        if analytics.usageFrequency < 5 && analytics.costPerSession > 10 {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: product.id ?? UUID(),
                type: .underutilized,
                title: "使用频率较低",
                description: "您的\(product.name ?? "订阅")使用频率较低，每月仅使用\(Int(analytics.usageFrequency))次，每次使用成本为¥\(String(format: "%.2f", analytics.costPerSession))。",
                potentialSavings: analytics.costPerSession * (5 - analytics.usageFrequency),
                priority: .medium,
                actionRequired: "考虑增加使用频率或寻找更便宜的替代方案",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        // 价格过高推荐
        if analytics.costPerHour > 50 {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: product.id ?? UUID(),
                type: .overpriced,
                title: "使用成本较高",
                description: "每小时使用成本为¥\(String(format: "%.2f", analytics.costPerHour))，建议寻找更经济的替代方案。",
                potentialSavings: analytics.costPerHour * 10, // 假设每月使用10小时
                priority: .high,
                actionRequired: "比较其他服务提供商的价格",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        // 年付折扣推荐
        if analytics.valueScore > 60 && analytics.usageFrequency > 10 {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: product.id ?? UUID(),
                type: .annualDiscount,
                title: "考虑年付优惠",
                description: "您经常使用此服务且满意度较高，年付通常可以节省15-20%的费用。",
                potentialSavings: product.price * 0.15,
                priority: .medium,
                actionRequired: "联系服务提供商了解年付优惠",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        // 取消推荐
        if analytics.valueScore < 30 && analytics.daysSinceLastUse > 30 {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: product.id ?? UUID(),
                type: .cancel,
                title: "考虑取消订阅",
                description: "已经\(analytics.daysSinceLastUse)天未使用，价值评分较低(\(String(format: "%.1f", analytics.valueScore)))。",
                potentialSavings: product.price,
                priority: .high,
                actionRequired: "取消订阅以避免不必要的支出",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        return recommendations
    }
    
    private func generateAllRecommendations() {
        recommendations = getAllSubscriptions().flatMap { generateRecommendations(for: $0) }
    }
    
    // MARK: - 使用记录
    func recordUsageSession(_ session: UsageSession) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    let usageRecord = UsageRecord(context: self.context)
                    usageRecord.id = session.id
                    usageRecord.date = session.startTime
                    usageRecord.duration = session.duration
                    usageRecord.satisfaction = Int16(session.satisfactionRating)
                    usageRecord.emotionalValue = Int16(session.emotionalValue)
                    usageRecord.notes = session.notes
                    usageRecord.scenario = session.activityDescription
                    
                    // 关联到对应的订阅产品
                    let request: NSFetchRequest<Product> = Product.fetchRequest()
                    request.predicate = NSPredicate(format: "id == %@", session.subscriptionId as CVarArg)
                    
                    if let product = try self.context.fetch(request).first {
                        usageRecord.product = product
                    }
                    
                    try self.context.save()
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func getUsageSessions(for subscriptionId: UUID) -> [UsageSession] {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        request.predicate = NSPredicate(format: "product.id == %@", subscriptionId as CVarArg)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: false)]
        
        do {
            let records = try context.fetch(request)
            return records.compactMap { record in
                guard let id = record.id,
                      let date = record.date else { return nil }
                
                return UsageSession(
                    id: id,
                    subscriptionId: subscriptionId,
                    startTime: date,
                    duration: record.duration ?? 0,
                    sessionType: .active, // 默认类型
                    activityDescription: record.scenario,
                    satisfactionRating: Int(record.satisfaction),
                    emotionalValue: Int(record.emotionalValue),
                    notes: record.notes
                )
            }
        } catch {
            print("获取使用记录失败: \(error)")
            return []
        }
    }
    
    // MARK: - 辅助方法
    private func findOrCreateCategory(name: String) -> Category {
        let request: NSFetchRequest<Category> = Category.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", name)
        
        do {
            if let existingCategory = try context.fetch(request).first {
                return existingCategory
            } else {
                let newCategory = Category(context: context)
                newCategory.name = name
                newCategory.id = UUID()
                return newCategory
            }
        } catch {
            let newCategory = Category(context: context)
            newCategory.name = name
            newCategory.id = UUID()
            return newCategory
        }
    }
    
    // MARK: - 订阅数据解析
    func extractSubscriptionData(from product: Product) -> SubscriptionData? {
        guard product.isVirtualProduct,
              let notes = product.purchaseNotes,
              let range = notes.range(of: "[SUBSCRIPTION_DATA]") else {
            return nil
        }
        
        let startIndex = notes.index(after: range.upperBound)
        let endIndex = notes.range(of: "\n", range: startIndex..<notes.endIndex)?.lowerBound ?? notes.endIndex
        let jsonString = String(notes[startIndex..<endIndex])
        
        guard let jsonData = jsonString.data(using: .utf8),
              let dict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: String] else {
            return nil
        }
        
        let billingCycle = BillingCycle(rawValue: dict["billingCycle"] ?? "monthly") ?? .monthly
        let costPerCycle = Double(dict["costPerCycle"] ?? "0") ?? 0
        let currency = dict["currency"] ?? "CNY"
        let status = SubscriptionStatus(rawValue: dict["status"] ?? "active") ?? .active
        let isShared = dict["isShared"] == "true"
        let familyMemberCount = Int(dict["familyMemberCount"] ?? "1") ?? 1
        let customCycleDays = Int(dict["customCycleDays"] ?? "0")
        
        return SubscriptionData(
            id: product.id ?? UUID(),
            name: product.name ?? "",
            serviceProvider: product.brand ?? "",
            billingCycle: billingCycle,
            costPerCycle: costPerCycle,
            currency: currency,
            startDate: product.purchaseDate ?? Date(),
            status: status,
            description: nil, // Product模型没有productDescription属性
            category: product.category?.name,
            customCycleDays: customCycleDays == 0 ? nil : customCycleDays,
            isShared: isShared,
            familyMemberCount: familyMemberCount,
            notes: product.purchaseNotes
        )
    }
}

// MARK: - 扩展：批量操作
extension SubscriptionService {
    func batchUpdateRenewalDates() async {
        let subscriptions = getAllSubscriptions()
        
        for subscription in subscriptions {
            if let subscriptionData = extractSubscriptionData(from: subscription) {
                let newRenewalDate = subscriptionData.billingCycle.nextRenewalDate(
                    from: subscriptionData.startDate,
                    customDays: subscriptionData.customCycleDays ?? 0
                )
                
                subscription.expiryDate = newRenewalDate
            }
        }
        
        do {
            try context.save()
        } catch {
            print("批量更新续订日期失败: \(error)")
        }
    }
    
    func calculateTotalMonthlySubscriptionCost() -> Double {
        return getAllSubscriptions().reduce(0) { total, subscription in
            if let subscriptionData = extractSubscriptionData(from: subscription) {
                return total + subscriptionData.monthlyCost
            }
            return total + (subscription.price / 12.0)
        }
    }
    
    func calculateTotalAnnualSubscriptionCost() -> Double {
        return getAllSubscriptions().reduce(0) { $0 + $1.price }
    }
    
    func getSubscriptionsByCategory() -> [String: [Product]] {
        let subscriptions = getAllSubscriptions()
        return Dictionary(grouping: subscriptions) { subscription in
            subscription.category?.name ?? "未分类"
        }
    }
    
    func getUnderutilizedSubscriptions() -> [Product] {
        return getAllSubscriptions().filter { subscription in
            let analytics = calculateSubscriptionAnalytics(for: subscription)
            return analytics.usageFrequency < 5 && analytics.costPerSession > 10
        }
    }
    
    func getAllActiveSubscriptions() -> [Product] {
        return getAllSubscriptions().filter { subscription in
            subscription.currentSubscriptionStatus == .subscriptionActive ||
            subscription.currentSubscriptionStatus == .subscriptionTrial
        }
    }
    
    func calculateAverageWorthItIndex() -> Double {
        let subscriptions = getAllSubscriptions()
        guard !subscriptions.isEmpty else { return 0 }
        
        let totalIndex = subscriptions.reduce(0) { $0 + $1.worthItIndex() }
        return totalIndex / Double(subscriptions.count)
    }
}