import StoreKit
import Foundation

private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
    switch result {
    case .unverified:
        throw StoreError.failedVerification
    case .verified(let safe):
        return safe
    }
}

@MainActor
class PurchaseManager: ObservableObject {
    static let shared = PurchaseManager()
    
    @Published var products: [StoreKit.Product] = []
    @Published var purchasedProducts: Set<String> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let productIds = ["top.trysapp.pinklog.premium"]
    private var updateListenerTask: Task<Void, Error>?
    
    init() {
        print("🏪 PurchaseManager 初始化开始")
        isLoading = true  // 明确设置初始化状态
        updateListenerTask = listenForTransactions()
        Task {
            print("📦 开始加载产品信息")
            await requestProducts()
            print("🔄 开始更新已购买产品")
            await updatePurchasedProducts()

            // 确保初始化完成后设置isLoading = false
            await MainActor.run {
                self.isLoading = false
                print("✅ PurchaseManager 初始化完成，isLoading设置为false")
            }

            // 发送初始化完成通知，触发Premium状态同步
            await MainActor.run {
                NotificationCenter.default.post(
                    name: .purchaseManagerInitialized,
                    object: nil,
                    userInfo: [
                        "isPremiumPurchased": self.isPremiumPurchased
                    ]
                )
                print("📢 已发送PurchaseManager初始化完成通知")
            }
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    var isPremiumPurchased: Bool {
        purchasedProducts.contains("top.trysapp.pinklog.premium")
    }
    
    func requestProducts() async {
        print("🛍️ 开始请求产品信息")
        print("🆔 产品ID列表: \(productIds)")

        // 注意：不在这里设置isLoading，因为在init中已经设置了
        errorMessage = nil

        do {
            let storeProducts = try await StoreKit.Product.products(for: productIds)
            products = storeProducts
            print("✅ 成功加载 \(storeProducts.count) 个产品")
            for product in storeProducts {
                print("📦 产品: \(product.id) - \(product.displayName) - \(product.displayPrice)")
            }
        } catch {
            print("❌ 加载产品信息失败: \(error)")
            errorMessage = "无法加载产品信息: \(error.localizedDescription)"
        }

        print("🏁 产品信息请求完成")
    }
    
    func purchase(_ product: StoreKit.Product) async {
        print("💰 PurchaseManager.purchase() 开始")
        print("📱 产品ID: \(product.id)")
        print("💵 产品价格: \(product.displayPrice)")

        isLoading = true
        errorMessage = nil

        do {
            print("🔄 调用 product.purchase()")
            let result = try await product.purchase()
            print("📋 购买结果: \(result)")

            switch result {
            case .success(let verification):
                print("✅ 购买成功，开始验证交易")
                let transaction = try checkVerified(verification)
                print("🔐 交易验证成功: \(transaction.productID)")

                await updatePurchasedProducts()
                await transaction.finish()

                // 发送购买成功通知，包含Premium状态更新指令
                await MainActor.run {
                    NotificationCenter.default.post(
                        name: .purchaseCompleted,
                        object: nil,
                        userInfo: [
                            "productId": transaction.productID,
                            "shouldSyncPremiumStatus": true
                        ]
                    )
                }

                print("🎉 购买流程完成，已发送成功通知，Premium状态: \(isPremiumPurchased)")

            case .pending:
                print("⏳ 购买处理中")
                errorMessage = "购买正在处理中，请稍后"

            case .userCancelled:
                print("❌ 用户取消购买")
                break

            @unknown default:
                print("❓ 未知购买结果")
                break
            }
        } catch {
            print("💥 购买异常: \(error)")
            print("🔍 错误详情: \(error.localizedDescription)")
            errorMessage = "购买失败: \(error.localizedDescription)"
        }

        isLoading = false
        print("🏁 PurchaseManager.purchase() 结束")
    }
    
    func restorePurchases() async {
        print("🔄 开始恢复购买")
        isLoading = true
        errorMessage = nil

        do {
            print("📱 调用 AppStore.sync()")
            try await AppStore.sync()
            print("✅ AppStore.sync() 完成")

            await updatePurchasedProducts()

            // 如果恢复购买后发现有Premium，发送通知
            if isPremiumPurchased {
                print("🎉 恢复购买成功，发现Premium购买")
                await MainActor.run {
                    NotificationCenter.default.post(
                        name: .purchaseCompleted,
                        object: nil,
                        userInfo: [
                            "productId": "top.trysapp.pinklog.premium",
                            "shouldSyncPremiumStatus": true,
                            "isRestore": true
                        ]
                    )
                }
                print("📢 已发送恢复购买成功通知")
            } else {
                print("ℹ️ 恢复购买完成，但没有发现Premium购买")
            }

        } catch {
            print("❌ 恢复购买失败: \(error)")
            errorMessage = "恢复购买失败: \(error.localizedDescription)"
        }

        isLoading = false
        print("🏁 恢复购买流程结束")
    }
    
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try checkVerified(result)
                    await PurchaseManager.shared.updatePurchasedProducts()
                    await transaction.finish()
                } catch {
                    print("Transaction failed verification")
                }
            }
        }
    }
    
    private func updatePurchasedProducts() async {
        print("🔄 开始更新已购买产品列表")
        print("⏰ 注意：这个过程可能需要一些时间，PurchaseManager仍在加载中...")

        var purchased: Set<String> = []
        var transactionCount = 0

        // 这个循环可能是耗时的根源
        let startTime = Date()
        for await result in Transaction.currentEntitlements {
            transactionCount += 1
            print("📋 处理交易 #\(transactionCount)")
            do {
                let transaction = try checkVerified(result)
                purchased.insert(transaction.productID)
                print("✅ 验证成功的交易: \(transaction.productID)")
                print("📅 交易日期: \(transaction.purchaseDate)")
                print("🔢 交易ID: \(transaction.id)")
            } catch {
                print("❌ 交易验证失败: \(error)")
            }
        }

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        print("⏱️ 交易验证耗时: \(String(format: "%.2f", duration))秒")

        print("📊 交易处理完成: 总共\(transactionCount)个交易，验证成功\(purchased.count)个")

        let oldPurchased = purchasedProducts
        purchasedProducts = purchased

        print("📋 已购买产品更新: \(oldPurchased) -> \(purchased)")
        print("💎 Premium状态: \(isPremiumPurchased)")
    }
}

enum StoreError: Error {
    case failedVerification
}

extension StoreError: LocalizedError {
    var errorDescription: String? {
        switch self {
        case .failedVerification:
            return "用户交易验证失败"
        }
    }
}