import Foundation
import CoreData
import Combine

/// 分析错误类型
enum AnalysisError: LocalizedError {
    case insufficientData(String)
    case timeout
    case invalidData(String)

    var errorDescription: String? {
        switch self {
        case .insufficientData(let message):
            return message
        case .timeout:
            return "分析超时"
        case .invalidData(let message):
            return "数据无效: \(message)"
        }
    }
}

/// 高级概览分析引擎
/// 生成全面的统计概览数据，包括基础统计、类别分布、价格分布、时间分布等
@MainActor
class AdvancedOverviewEngine: ObservableObject {
    
    // MARK: - 单例实例
    static let shared = AdvancedOverviewEngine()
    
    // MARK: - 发布属性
    @Published var isAnalyzing = false
    @Published var analysisProgress: Double = 0.0
    @Published var lastAnalysisDate: Date?
    @Published var errorMessage: String?
    
    // 防重复注册标记
    private var isRegistered = false
    
    // MARK: - 缓存系统
    private var overviewCache: OverviewMetrics?
    private var cacheTimestamp: Date?
    private let cacheExpirationInterval: TimeInterval = 300 // 5分钟缓存
    
    // MARK: - 配置
    private struct Configuration {
        static let analysisTimeout: TimeInterval = 15.0
        static let minimumProductsForAnalysis = 1
    }
    
    // MARK: - 私有属性
    private var analysisQueue = DispatchQueue(label: "com.pinklog.overview", qos: .userInitiated)
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    private init() {
        registerCacheCleanup()
    }
    
    // MARK: - 主要分析方法
    
    /// 生成完整的概览统计数据
    /// - Parameters:
    ///   - products: 要分析的产品列表
    ///   - forceRefresh: 是否强制刷新缓存
    /// - Returns: 完整的概览度量数据
    func generateOverview(
        for products: [Product],
        forceRefresh: Bool = false
    ) async throws -> OverviewMetrics {
        
        guard products.count >= Configuration.minimumProductsForAnalysis else {
            throw AnalysisError.insufficientData("至少需要\(Configuration.minimumProductsForAnalysis)个产品才能进行概览分析")
        }
        
        // 检查缓存
        if !forceRefresh, let cachedResult = getCachedOverview() {
            return cachedResult
        }
        
        // 开始分析
        await MainActor.run {
            isAnalyzing = true
            analysisProgress = 0.0
            errorMessage = nil
        }
        
        do {
            let result = try await performOverviewAnalysis(products: products)
            
            // 缓存结果
            cacheOverview(result)
            
            await MainActor.run {
                isAnalyzing = false
                analysisProgress = 1.0
                lastAnalysisDate = Date()
            }
            
            return result
            
        } catch {
            await MainActor.run {
                isAnalyzing = false
                errorMessage = error.localizedDescription
            }
            throw error
        }
    }
    
    // MARK: - 私有分析方法
    
    /// 执行实际的概览分析计算
    private func performOverviewAnalysis(products: [Product]) async throws -> OverviewMetrics {
        // 阶段1：基础统计分析 (25%)
        await self.updateProgress(0.25)
        let basicStats = self.generateBasicStats(products: products)

        // 阶段2：类别分布分析 (25%)
        await self.updateProgress(0.50)
        let categoryDistribution = self.generateCategoryDistribution(products: products)

        // 阶段3：价格分布分析 (25%)
        await self.updateProgress(0.75)
        let priceDistribution = self.generatePriceDistribution(products: products)

        // 阶段4：时间分布分析 (25%)
        await self.updateProgress(1.0)
        let timeDistribution = self.generateTimeDistribution(products: products)

        // 创建完整的概览度量
        return OverviewMetrics(
            basicStats: basicStats,
            categoryDistribution: categoryDistribution,
            priceDistribution: priceDistribution,
            timeDistribution: timeDistribution
        )
    }
    
    /// 生成基础统计信息
    private func generateBasicStats(products: [Product]) -> BasicStats {
        let totalCount = products.count
        let totalInvestment = products.reduce(0) { $0 + $1.price }
        let averagePrice = totalCount > 0 ? totalInvestment / Double(totalCount) : 0.0
        
        // 计算使用记录统计
        let allUsageRecords = products.flatMap { product in
            (product.usageRecords?.allObjects as? [UsageRecord]) ?? []
        }
        let totalUsageRecords = allUsageRecords.count
        let averageUsagePerProduct = totalCount > 0 ? Double(totalUsageRecords) / Double(totalCount) : 0.0
        
        // 计算时间跨度
        let purchaseDates = products.compactMap { $0.purchaseDate }
        let oldestDate = purchaseDates.min()
        let newestDate = purchaseDates.max()
        let timespan = oldestDate != nil && newestDate != nil ? 
            newestDate!.timeIntervalSince(oldestDate!) : 0.0
        
        return BasicStats(
            totalProductsCount: totalCount,
            totalInvestment: totalInvestment,
            averageProductPrice: averagePrice,
            totalUsageRecords: totalUsageRecords,
            averageUsagePerProduct: averageUsagePerProduct,
            oldestProductDate: oldestDate,
            newestProductDate: newestDate,
            ownershipTimespan: timespan
        )
    }
    
    /// 生成类别分布统计
    private func generateCategoryDistribution(products: [Product]) -> [UUID: CategoryStats] {
        var categoryStats: [UUID: CategoryStats] = [:]
        
        // 按类别分组产品
        let productsByCategory = Dictionary(grouping: products) { $0.category }
        let totalProducts = products.count
        
        for (category, categoryProducts) in productsByCategory {
            guard let category = category, let categoryId = category.id else { continue }
            
            let productCount = categoryProducts.count
            let totalValue = categoryProducts.reduce(0) { $0 + $1.price }
            let averagePrice = productCount > 0 ? totalValue / Double(productCount) : 0.0
            
            // 计算使用统计
            let allUsageRecords = categoryProducts.flatMap { product in
                (product.usageRecords?.allObjects as? [UsageRecord]) ?? []
            }
            let totalUsageCount = allUsageRecords.count
            let averageUsagePerProduct = productCount > 0 ? Double(totalUsageCount) / Double(productCount) : 0.0

            // 计算满意度
            let satisfactionRatings = allUsageRecords.compactMap { $0.satisfaction > 0 ? Double($0.satisfaction) : nil }
            let averageSatisfaction = satisfactionRatings.isEmpty ? 0.0 : satisfactionRatings.reduce(0, +) / Double(satisfactionRatings.count)

            // 计算价值指数（简化版本）
            let worthIndexValues = categoryProducts.map { $0.worthItIndex() }
            let averageWorthIndex = worthIndexValues.isEmpty ? 0.0 : worthIndexValues.reduce(0, +) / Double(worthIndexValues.count)
            
            let percentageOfTotal = totalProducts > 0 ? Double(productCount) / Double(totalProducts) * 100.0 : 0.0
            
            categoryStats[categoryId] = CategoryStats(
                categoryId: categoryId,
                categoryName: category.name ?? "未知类别",
                categoryIcon: category.icon,
                productCount: productCount,
                totalValue: totalValue,
                averagePrice: averagePrice,
                totalUsageCount: totalUsageCount,
                averageUsagePerProduct: averageUsagePerProduct,
                averageSatisfaction: averageSatisfaction,
                averageWorthIndex: averageWorthIndex,
                percentageOfTotal: percentageOfTotal
            )
        }
        
        return categoryStats
    }

    /// 生成价格分布统计
    private func generatePriceDistribution(products: [Product]) -> PriceDistribution {
        var distributionByRange: [PriceRange: PriceRangeStats] = [:]

        // 计算基础价格统计
        let prices = products.map { $0.price }
        let sortedPrices = prices.sorted()
        let averagePrice = prices.isEmpty ? 0.0 : prices.reduce(0, +) / Double(prices.count)
        let medianPrice = sortedPrices.isEmpty ? 0.0 :
            (sortedPrices.count % 2 == 0 ?
                (sortedPrices[sortedPrices.count/2 - 1] + sortedPrices[sortedPrices.count/2]) / 2.0 :
                sortedPrices[sortedPrices.count/2])

        // 计算标准差
        let variance = prices.isEmpty ? 0.0 : prices.reduce(0) { result, price in
            let diff = price - averagePrice
            return result + (diff * diff)
        } / Double(prices.count)
        let standardDeviation = sqrt(variance)

        // 找到极值产品
        let mostExpensiveProduct = products.max { $0.price < $1.price }
        let cheapestProduct = products.min { $0.price < $1.price }

        // 按价格区间分组统计
        for range in PriceRange.allCases {
            let rangeProducts = products.filter { range.contains($0.price) }
            let productCount = rangeProducts.count

            if productCount > 0 {
                let totalValue = rangeProducts.reduce(0) { $0 + $1.price }
                let averagePrice = totalValue / Double(productCount)
                let percentageOfTotal = Double(productCount) / Double(products.count) * 100.0

                // 计算使用统计
                let allUsageRecords = rangeProducts.flatMap { product in
                    (product.usageRecords?.allObjects as? [UsageRecord]) ?? []
                }
                let averageUsageCount = Double(allUsageRecords.count) / Double(productCount)

                // 计算满意度
                let satisfactionRatings = allUsageRecords.compactMap { $0.satisfaction > 0 ? Double($0.satisfaction) : nil }
                let averageSatisfaction = satisfactionRatings.isEmpty ? 0.0 : satisfactionRatings.reduce(0, +) / Double(satisfactionRatings.count)

                distributionByRange[range] = PriceRangeStats(
                    range: range,
                    productCount: productCount,
                    totalValue: totalValue,
                    averagePrice: averagePrice,
                    percentageOfTotal: percentageOfTotal,
                    averageUsageCount: averageUsageCount,
                    averageSatisfaction: averageSatisfaction
                )
            }
        }

        return PriceDistribution(
            distributionByRange: distributionByRange,
            medianPrice: medianPrice,
            averagePrice: averagePrice,
            priceStandardDeviation: standardDeviation,
            mostExpensiveProduct: mostExpensiveProduct?.toSummary(),
            cheapestProduct: cheapestProduct?.toSummary()
        )
    }

    /// 生成时间分布统计
    private func generateTimeDistribution(products: [Product]) -> TimeDistribution {
        var distributionByPeriod: [TimePeriod: TimePeriodStats] = [:]
        var purchaseFrequencyByMonth: [String: Int] = [:]

        let currentDate = Date()
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM"

        // 按时间周期分组统计
        for period in TimePeriod.allCases {
            let periodProducts = products.filter { product in
                guard let purchaseDate = product.purchaseDate else { return false }
                return period.contains(purchaseDate, referenceDate: currentDate)
            }

            let productCount = periodProducts.count

            if productCount > 0 {
                let totalValue = periodProducts.reduce(0) { $0 + $1.price }
                let averagePrice = totalValue / Double(productCount)
                let percentageOfTotal = Double(productCount) / Double(products.count) * 100.0

                // 找到最活跃的类别
                let categoryGroups = Dictionary(grouping: periodProducts) { $0.category?.name ?? "未分类" }
                let mostActiveCategory = categoryGroups.max { $0.value.count < $1.value.count }?.key

                distributionByPeriod[period] = TimePeriodStats(
                    period: period,
                    productCount: productCount,
                    totalValue: totalValue,
                    averagePrice: averagePrice,
                    percentageOfTotal: percentageOfTotal,
                    mostActiveCategory: mostActiveCategory
                )
            }
        }

        // 按月份统计购买频率
        for product in products {
            guard let purchaseDate = product.purchaseDate else { continue }
            let monthKey = dateFormatter.string(from: purchaseDate)
            purchaseFrequencyByMonth[monthKey, default: 0] += 1
        }

        // 计算季节性趋势
        let seasonalTrends = generateSeasonalTrends(products: products)

        // 计算平均购买间隔
        let purchaseDates = products.compactMap { $0.purchaseDate }.sorted()
        let averageInterval = calculateAveragePurchaseInterval(dates: purchaseDates)

        return TimeDistribution(
            distributionByPeriod: distributionByPeriod,
            purchaseFrequencyByMonth: purchaseFrequencyByMonth,
            seasonalTrends: seasonalTrends,
            averagePurchaseInterval: averageInterval
        )
    }

    /// 生成季节性趋势分析
    private func generateSeasonalTrends(products: [Product]) -> SeasonalTrends {
        let calendar = Calendar.current
        var springCount = 0, summerCount = 0, autumnCount = 0, winterCount = 0

        for product in products {
            guard let purchaseDate = product.purchaseDate else { continue }
            let month = calendar.component(.month, from: purchaseDate)

            switch month {
            case 3...5: springCount += 1
            case 6...8: summerCount += 1
            case 9...11: autumnCount += 1
            case 12, 1, 2: winterCount += 1
            default: break
            }
        }

        // 确定高峰和低谷季节
        let seasonCounts = [
            ("春季", springCount),
            ("夏季", summerCount),
            ("秋季", autumnCount),
            ("冬季", winterCount)
        ]

        let peakSeason = seasonCounts.max { $0.1 < $1.1 }?.0 ?? "春季"
        let lowSeason = seasonCounts.min { $0.1 < $1.1 }?.0 ?? "冬季"

        return SeasonalTrends(
            springPurchases: springCount,
            summerPurchases: summerCount,
            autumnPurchases: autumnCount,
            winterPurchases: winterCount,
            peakSeason: peakSeason,
            lowSeason: lowSeason
        )
    }

    /// 计算平均购买间隔
    private func calculateAveragePurchaseInterval(dates: [Date]) -> TimeInterval {
        guard dates.count > 1 else { return 0.0 }

        var totalInterval: TimeInterval = 0.0
        for i in 1..<dates.count {
            totalInterval += dates[i].timeIntervalSince(dates[i-1])
        }

        return totalInterval / Double(dates.count - 1)
    }

    /// 更新分析进度
    private func updateProgress(_ progress: Double) async {
        await MainActor.run {
            self.analysisProgress = progress
        }
    }
    
    // MARK: - 缓存管理
    
    /// 获取缓存的概览结果
    private func getCachedOverview() -> OverviewMetrics? {
        guard let timestamp = cacheTimestamp,
              Date().timeIntervalSince(timestamp) < cacheExpirationInterval,
              let overview = overviewCache else {
            return nil
        }
        
        return overview
    }
    
    /// 缓存概览结果
    private func cacheOverview(_ overview: OverviewMetrics) {
        overviewCache = overview
        cacheTimestamp = Date()
    }
    
    /// 清理过期缓存
    private func cleanupExpiredCache() {
        guard let timestamp = cacheTimestamp else { return }
        
        if Date().timeIntervalSince(timestamp) > cacheExpirationInterval {
            overviewCache = nil
            cacheTimestamp = nil
        }
    }
    
    /// 注册到全局缓存管理器（替代独立定时器）
    private func registerCacheCleanup() {
        guard !isRegistered else {
            print("⚠️ AdvancedOverviewEngine已注册过，跳过重复注册")
            return
        }
        
        GlobalCacheManager.shared.registerCache(
            name: "AdvancedOverviewEngine",
            priority: .medium
        ) { [weak self] in
            self?.cleanupExpiredCache()
        }
        isRegistered = true
        print("📝 已注册到全局缓存管理器: AdvancedOverviewEngine")
    }
    
    /// 清空所有缓存
    func clearCache() {
        overviewCache = nil
        cacheTimestamp = nil
    }
}

// MARK: - 支持扩展

/// Product扩展，支持转换为ProductSummary
extension Product {
    func toSummary() -> ProductSummary {
        let calendar = Calendar.current
        let daysSincePurchase = purchaseDate != nil ?
            calendar.dateComponents([.day], from: purchaseDate!, to: Date()).day ?? 0 : 0

        return ProductSummary(
            id: id ?? UUID(),
            name: name ?? "未知产品",
            brand: brand,
            price: price,
            purchaseDate: purchaseDate ?? Date(),
            categoryName: category?.name,
            usageCount: (usageRecords?.allObjects as? [UsageRecord])?.count ?? 0,
            averageSatisfaction: calculateAverageSatisfaction(),
            worthIndex: worthItIndex(),
            daysSincePurchase: daysSincePurchase
        )
    }

    private func calculateAverageSatisfaction() -> Double {
        let usageRecordsArray = (usageRecords?.allObjects as? [UsageRecord]) ?? []
        let satisfactionRatings = usageRecordsArray.compactMap { record in
            record.satisfaction > 0 ? Double(record.satisfaction) : nil
        }

        return satisfactionRatings.isEmpty ? 0.0 : satisfactionRatings.reduce(0, +) / Double(satisfactionRatings.count)
    }
}


