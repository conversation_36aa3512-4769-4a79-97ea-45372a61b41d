//
//  UnifiedLayoutPreview.swift
//  统一布局设计预览
//
//  Created by Assistant on 2025-01-26.
//

import SwiftUI

/**
 * 🎯 统一布局设计预览
 * 
 * 设计理念：
 * 参考用户提供的图片，重新设计产品卡片布局，
 * 统一字体大小，建立清晰的主次关系，优化视觉层次。
 *
 * 📐 新布局结构：
 * 
 * ┌─────────────────────────────────────────────┐
 * │ [图片] 产品名称 (16pt, semibold)            │
 * │        品牌·型号 (13pt, regular)           │
 * │        ¥价格 (15pt)  使用X次 值度XX ●      │
 * │        ● 库存量                            │
 * │        ████████████████ 100%              │
 * └─────────────────────────────────────────────┘
 *
 * 🎨 设计改进：
 * 
 * 1. 统一字体系统
 *    - 产品名称：16pt semibold（主标题）
 *    - 品牌型号：13pt regular（副标题）
 *    - 价格：15pt medium（重要信息）
 *    - 使用次数：12pt regular（辅助信息）
 *    - 值度指数：12pt regular + 14pt medium（数值突出）
 *    - 库存信息：12pt regular（一致性）
 * 
 * 2. 清晰的信息层次
 *    - 第一层：产品名称（最重要）
 *    - 第二层：品牌型号（产品识别）
 *    - 第三层：价格、使用、值度（核心数据）
 *    - 第四层：库存信息（状态信息）
 * 
 * 3. 优化的布局结构
 *    - 水平信息排列：价格 | 使用次数 | 值度指数
 *    - 垂直间距统一：6pt 主间距，4pt 子间距
 *    - 百分比位置：库存条右侧（符合用户要求）
 * 
 * 4. 视觉平衡改进
 *    - 移除过度装饰（阴影、渐变）
 *    - 简化颜色使用
 *    - 统一圆角和间距
 *    - 提升可读性
 *
 * 🔧 技术实现要点：
 * 
 * ```swift
 * VStack(alignment: .leading, spacing: 6) {
 *     // 主标题
 *     Text(product.name)
 *         .font(.system(size: 16, weight: .semibold))
 *     
 *     // 副标题
 *     Text(brand)
 *         .font(.system(size: 13, weight: .regular))
 *     
 *     // 核心信息行
 *     HStack(alignment: .center, spacing: 12) {
 *         Text("¥\(price)")
 *             .font(.system(size: 15, weight: .medium))
 *         
 *         Spacer()
 *         
 *         Text("使用\(count)次")
 *             .font(.system(size: 12, weight: .regular))
 *         
 *         HStack(spacing: 3) {
 *             Text("值度")
 *                 .font(.system(size: 12, weight: .regular))
 *             Text("\(value)")
 *                 .font(.system(size: 14, weight: .medium))
 *         }
 *     }
 *     
 *     // 库存信息（消耗品）
 *     if isConsumable {
 *         VStack(spacing: 4) {
 *             HStack {
 *                 Circle().frame(width: 6, height: 6)
 *                 Text("\(quantity)\(unit)")
 *                     .font(.system(size: 12, weight: .regular))
 *                 Spacer()
 *             }
 *             
 *             HStack(spacing: 8) {
 *                 // 进度条
 *                 Capsule().frame(height: 4)
 *                 
 *                 // 百分比
 *                 Text("\(percentage)%")
 *                     .font(.system(size: 12, weight: .medium))
 *                     .frame(width: 35, alignment: .trailing)
 *             }
 *         }
 *     }
 * }
 * ```
 * 
 * 📱 用户体验提升：
 * 
 * • 视觉一致性：统一的字体大小和权重系统
 * • 信息层次：清晰的主次关系，重要信息突出
 * • 阅读流畅：合理的间距和对齐方式
 * • 空间利用：紧凑但不拥挤的布局
 * • 功能导向：百分比位置符合用户习惯
 * • 简洁美观：去除冗余装饰，专注内容
 */

struct UnifiedLayoutPreview: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("统一布局设计")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("统一字体大小，建立清晰主次关系")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("设计改进要点：")
                    .font(.headline)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("•")
                        Text("统一字体系统：16pt/13pt/15pt/12pt")
                    }
                    HStack {
                        Text("•")
                        Text("清晰信息层次：名称 > 品牌 > 核心数据 > 状态")
                    }
                    HStack {
                        Text("•")
                        Text("百分比位置：库存条右侧")
                    }
                    HStack {
                        Text("•")
                        Text("简化装饰：专注内容可读性")
                    }
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
    }
}

#Preview {
    UnifiedLayoutPreview()
}