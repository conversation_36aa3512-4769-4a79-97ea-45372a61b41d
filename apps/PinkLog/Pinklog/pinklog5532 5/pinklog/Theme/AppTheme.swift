import SwiftUI

// MARK: - 应用颜色
struct AppColors {
    static let primary: Color = {
        if let color = UIColor(named: "PrimaryColor") {
            return Color(color)
        }
        return Color.pink
    }()

    static let secondary: Color = {
        if let color = UIColor(named: "SecondaryColor") {
            return Color(color)
        }
        return Color.purple
    }()

    static let background: Color = {
        if let color = UIColor(named: "BackgroundColor") {
            return Color(color)
        }
        return Color(UIColor.systemBackground)
    }()

    static let cardBackground: Color = {
        if let color = UIColor(named: "CardBackgroundColor") {
            return Color(color)
        }
        return Color(UIColor.secondarySystemBackground)
    }()

    static let text: Color = {
        if let color = UIColor(named: "TextColor") {
            return Color(color)
        }
        return Color(UIColor.label)
    }()

    static let secondaryText: Color = {
        if let color = UIColor(named: "SecondaryTextColor") {
            return Color(color)
        }
        return Color(UIColor.secondaryLabel)
    }()

    // 界面元素颜色
    static let surface: Color = {
        if let color = UIColor(named: "SurfaceColor") {
            return Color(color)
        }
        return Color(UIColor.secondarySystemBackground)
    }()

    static let border: Color = {
        if let color = UIColor(named: "BorderColor") {
            return Color(color)
        }
        return Color(UIColor.separator)
    }()

    // 状态颜色
    static let success = Color.green
    static let warning = Color.orange
    static let error = Color.red
    static let info = Color.blue

    // 产品状态颜色
    static let core = Color.green
    static let worthwhile = Color.blue
    static let potential = Color.orange
    static let reconsider = Color.yellow
    static let sellRecommended = Color.red

    // 阴影颜色
    static let shadow = Color.black.opacity(0.1)

    // 获取产品状态对应的颜色
    static func statusColor(for status: Product.ProductStatus) -> Color {
        switch status {
        case .core: return core
        case .worthwhile: return worthwhile
        case .potential: return potential
        case .reconsider: return reconsider
        case .sellRecommended: return sellRecommended
        case .subscriptionActive: return success
        case .subscriptionPaused: return warning
        case .subscriptionCancelled: return error
        case .subscriptionExpired: return error
        case .subscriptionTrial: return info
        }
    }
}

// MARK: - 应用字体
struct AppFonts {
    static let largeTitle = Font.largeTitle
    static let title = Font.title
    static let title2 = Font.title2
    static let title3 = Font.title3
    static let headline = Font.headline
    static let subheadline = Font.subheadline
    static let body = Font.body
    static let callout = Font.callout
    static let footnote = Font.footnote
    static let caption = Font.caption
    static let caption2 = Font.caption2
}

// MARK: - 应用尺寸
struct AppSizes {
    static let cornerRadius: CGFloat = 12
    static let smallCornerRadius: CGFloat = 8
    static let largeCornerRadius: CGFloat = 16
    static let padding: CGFloat = 16
    static let smallPadding: CGFloat = 8
    static let largePadding: CGFloat = 24
    static let iconSize: CGFloat = 24
    static let smallIconSize: CGFloat = 16
    static let largeIconSize: CGFloat = 32
    static let cardHeight: CGFloat = 120
    static let smallCardHeight: CGFloat = 80
    static let largeCardHeight: CGFloat = 160
    static let shadowRadius: CGFloat = 5
}

// MARK: - 应用主题
enum AppThemeType: String, CaseIterable, Identifiable {
    case system = "系统"
    case light = "浅色"
    case dark = "深色"
    case pink = "粉色"
    case blue = "蓝色"
    case green = "绿色"

    var id: String { self.rawValue }

    var colorScheme: ColorScheme? {
        switch self {
        case .system: return nil
        case .light: return .light
        case .dark: return .dark
        case .pink, .blue, .green: return nil
        }
    }

    var primaryColor: Color {
        switch self {
        case .system, .light, .dark: return Color.pink
        case .pink: return Color.pink
        case .blue: return Color.blue
        case .green: return Color.green
        }
    }

    var secondaryColor: Color {
        switch self {
        case .system, .light, .dark: return Color.purple
        case .pink: return Color.purple
        case .blue: return Color.teal
        case .green: return Color.mint
        }
    }
    
    // 新增：为增强分析视图添加的颜色属性
    var textColor: Color {
        return Color.primary
    }
    
    var secondaryTextColor: Color {
        return Color.secondary
    }
    
    var backgroundColor: Color {
        return AppColors.background
    }
    
    var cardBackgroundColor: Color {
        return AppColors.cardBackground
    }
    
    var accentColor: Color {
        return primaryColor
    }
}

// MARK: - 主题管理器
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppThemeType {
        didSet {
            UserDefaults.standard.set(currentTheme.rawValue, forKey: "appTheme")
        }
    }

    init() {
        if let savedTheme = UserDefaults.standard.string(forKey: "appTheme"),
           let theme = AppThemeType(rawValue: savedTheme) {
            self.currentTheme = theme
        } else {
            self.currentTheme = .system
        }
    }
}

// MARK: - 视图修饰符
extension View {
    func cardStyle() -> some View {
        self
            .padding(AppSizes.padding)
            .background(AppColors.cardBackground)
            .cornerRadius(AppSizes.cornerRadius)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    func primaryButtonStyle() -> some View {
        self
            .padding(.vertical, AppSizes.smallPadding)
            .padding(.horizontal, AppSizes.padding)
            .background(AppColors.primary)
            .foregroundColor(.white)
            .cornerRadius(AppSizes.smallCornerRadius)
    }

    func secondaryButtonStyle() -> some View {
        self
            .padding(.vertical, AppSizes.smallPadding)
            .padding(.horizontal, AppSizes.padding)
            .background(AppColors.cardBackground)
            .foregroundColor(AppColors.primary)
            .cornerRadius(AppSizes.smallCornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppSizes.smallCornerRadius)
                    .stroke(AppColors.primary, lineWidth: 1)
            )
    }
}
