import Foundation
import CoreData

class LoanRecordRepository: CoreDataRepository<LoanRecord> {
    // 获取特定产品的借阅记录
    func fetchByProduct(productId: UUID) -> [LoanRecord] {
        let predicate = NSPredicate(format: "product.id == %@", productId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取活跃的借阅记录（未归还）
    func fetchActiveLoans() -> [LoanRecord] {
        let predicate = NSPredicate(format: "returnDate == nil AND status != %@", "lost")
        let sortDescriptors = [NSSortDescriptor(key: "dueDate", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取逾期的借阅记录
    func fetchOverdueLoans() -> [LoanRecord] {
        let predicate = NSPredicate(format: "returnDate == nil AND dueDate < %@ AND status != %@", Date() as NSDate, "lost")
        let sortDescriptors = [NSSortDescriptor(key: "dueDate", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取即将到期的借阅记录（7天内）
    func fetchNearDueLoans() -> [LoanRecord] {
        let calendar = Calendar.current
        let now = Date()
        let sevenDaysLater = calendar.date(byAdding: .day, value: 7, to: now)!
        
        let predicate = NSPredicate(format: "returnDate == nil AND dueDate >= %@ AND dueDate <= %@ AND status != %@", 
                                   now as NSDate, 
                                   sevenDaysLater as NSDate,
                                   "lost")
        let sortDescriptors = [NSSortDescriptor(key: "dueDate", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取已归还的借阅记录
    func fetchReturnedLoans() -> [LoanRecord] {
        let predicate = NSPredicate(format: "returnDate != nil OR status == %@", "returned")
        let sortDescriptors = [NSSortDescriptor(key: "returnDate", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取丢失的借阅记录
    func fetchLostLoans() -> [LoanRecord] {
        let predicate = NSPredicate(format: "status == %@", "lost")
        let sortDescriptors = [NSSortDescriptor(key: "dueDate", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 搜索借阅记录
    func search(query: String) -> [LoanRecord] {
        let predicate = NSPredicate(format: "borrowerName CONTAINS[cd] %@ OR notes CONTAINS[cd] %@ OR contactInfo CONTAINS[cd] %@", 
                                   query, query, query)
        let sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
}
