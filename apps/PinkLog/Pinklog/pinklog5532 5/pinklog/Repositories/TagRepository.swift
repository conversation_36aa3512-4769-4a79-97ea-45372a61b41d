import Foundation
import CoreData

class TagRepository: CoreDataRepository<Tag> {
    // 共享实例
    static let shared = TagRepository(context: PersistenceController.shared.container.viewContext)
    // 获取指定类型的标签
    func fetchByType(type: String, sortDescriptors: [NSSortDescriptor]? = nil) -> [Tag] {
        let predicate = NSPredicate(format: "type == %@", type)
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 通过名称搜索标签
    func searchByName(query: String, sortDescriptors: [NSSortDescriptor]? = nil) -> [Tag] {
        let predicate = NSPredicate(format: "name CONTAINS[cd] %@", query)
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    // 获取产品的标签
    func fetchByProduct(productId: UUID, sortDescriptors: [NSSortDescriptor]? = nil) -> [Tag] {
        let predicate = NSPredicate(format: "ANY products.id == %@", productId as CVarArg)
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
}
