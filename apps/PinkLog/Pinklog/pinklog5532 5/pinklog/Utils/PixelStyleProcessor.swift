import Foundation
import UIKit
import CoreImage
import CoreImage.CIFilterBuiltins

enum PixelArtStyle: String, CaseIterable {
    case popArt = "波普艺术"
    case retroPixel = "复古像素"
    case sticker = "贴纸风格"
    case comicBook = "漫画书"

    var description: String {
        switch self {
        case .popArt:
            return "安迪·沃霍尔风格的鲜艳对比色"
        case .retroPixel:
            return "经典8位游戏机调色板"
        case .sticker:
            return "扣出主体配随机纯色背景"
        case .comicBook:
            return "漫画书风格轮廓线"
        }
    }
}

/// 像素风格化处理器
/// 将图片转换为像素艺术风格
class PixelStyleProcessor {
    static let shared = PixelStyleProcessor()
    
    private let context = CIContext()
    
    private init() {}
    
    /// 将图片转换为像素风格
    /// - Parameters:
    ///   - image: 原始图片
    ///   - pixelSize: 像素块大小 (默认8)
    ///   - completion: 完成回调
    func pixelizeImage(_ image: UIImage, pixelSize: Int = 8, completion: @escaping (UIImage?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let result = self.createPixelArt(from: image, blockSize: pixelSize)
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    /// 创建像素风格头像（专门用于用户头像）
    /// - Parameters:
    ///   - image: 原始图片
    ///   - style: 指定的风格
    ///   - completion: 完成回调
    func createPixelAvatar(_ image: UIImage, style: PixelArtStyle, completion: @escaping (UIImage?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            // 1. 先压缩图片到合理尺寸（避免内存问题）
            let compressedImage = self.compressImageForProcessing(image)

            // 2. 调整图片大小为正方形，并缩放到合适的像素化尺寸
            let squareImage = self.makeSquareImageForPixelArt(compressedImage)

            // 3. 应用指定的风格
            let stylizedImage: UIImage?

            switch style {
            case .popArt:
                stylizedImage = self.createPopArtStyle(squareImage)
            case .retroPixel:
                stylizedImage = self.createRetroPixelStyle(squareImage)
            case .sticker:
                stylizedImage = self.createStickerStyle(squareImage)
            case .comicBook:
                stylizedImage = self.createComicBookStyle(squareImage)
            }

            // 4. 添加圆角效果
            let finalImage = self.addRoundedCorners(stylizedImage ?? squareImage)

            DispatchQueue.main.async {
                completion(finalImage)
            }
        }
    }
    
    // MARK: - Private Methods

    /// 压缩图片到合理尺寸（避免内存问题）
    private func compressImageForProcessing(_ image: UIImage) -> UIImage {
        let maxSize: CGFloat = 512 // 最大尺寸512x512，足够用于头像处理
        let size = image.size

        // 如果图片已经足够小，直接返回
        if max(size.width, size.height) <= maxSize {
            return image
        }

        // 计算压缩比例
        let scale = maxSize / max(size.width, size.height)
        let newSize = CGSize(width: size.width * scale, height: size.height * scale)

        // 使用高质量压缩
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    /// 创建波普艺术风格 - 安迪·沃霍尔风格
    private func createPopArtStyle(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 波普艺术调色板 - 鲜艳对比色
        let popColors: [(UInt8, UInt8, UInt8)] = [
            (255, 0, 255),   // 亮紫
            (0, 255, 255),   // 亮青
            (255, 255, 0),   // 亮黄
            (255, 0, 0),     // 亮红
            (0, 255, 0),     // 亮绿
            (0, 0, 255),     // 亮蓝
            (255, 128, 0),   // 橙色
            (128, 0, 255),   // 紫色
            (255, 192, 203), // 粉红
            (0, 0, 0),       // 黑色
            (255, 255, 255), // 白色
        ]

        // 应用波普艺术效果
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = pixelData[i]
            let g = pixelData[i + 1]
            let b = pixelData[i + 2]
            let a = pixelData[i + 3]

            // 计算亮度
            let brightness = Float(r) * 0.299 + Float(g) * 0.587 + Float(b) * 0.114

            // 根据亮度映射到波普色彩
            let colorIndex = Int(brightness / 25.5) % popColors.count
            let popColor = popColors[colorIndex]

            // 增强饱和度
            pixelData[i] = popColor.0
            pixelData[i + 1] = popColor.1
            pixelData[i + 2] = popColor.2
            pixelData[i + 3] = a
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 创建复古像素风格 - 8位游戏风格
    private func createRetroPixelStyle(_ image: UIImage) -> UIImage? {
        // 1. 先进行强烈的像素化
        guard let pixelizedImage = createPixelArt(from: image, blockSize: 8) else { return image }

        guard let cgImage = pixelizedImage.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 经典8位游戏调色板
        let retroColors: [(UInt8, UInt8, UInt8)] = [
            (0, 0, 0),       // 黑色
            (255, 255, 255), // 白色
            (136, 0, 0),     // 深红
            (170, 255, 238), // 浅青
            (204, 68, 204),  // 紫色
            (0, 204, 85),    // 绿色
            (0, 0, 170),     // 蓝色
            (238, 238, 119), // 黄色
            (221, 136, 85),  // 橙色
            (102, 68, 0),    // 棕色
            (255, 119, 119), // 浅红
            (51, 51, 51),    // 深灰
            (119, 119, 119), // 中灰
            (170, 255, 102), // 浅绿
            (0, 136, 255),   // 浅蓝
            (187, 187, 187)  // 浅灰
        ]

        // 应用复古调色板
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = pixelData[i]
            let g = pixelData[i + 1]
            let b = pixelData[i + 2]
            let a = pixelData[i + 3]

            var minDistance = Float.infinity
            var closestColor = retroColors[0]

            for color in retroColors {
                let distance = colorDistance(r1: r, g1: g, b1: b, r2: color.0, g2: color.1, b2: color.2)
                if distance < minDistance {
                    minDistance = distance
                    closestColor = color
                }
            }

            pixelData[i] = closestColor.0
            pixelData[i + 1] = closestColor.1
            pixelData[i + 2] = closestColor.2
            pixelData[i + 3] = a
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 创建贴纸风格 - 扣出主体 + 随机纯色背景 + 白色描边
    private func createStickerStyle(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        // 随机选择背景颜色
        let backgroundColors: [(UInt8, UInt8, UInt8)] = [
            (255, 182, 193), // 浅粉红
            (173, 216, 230), // 浅蓝
            (144, 238, 144), // 浅绿
            (255, 218, 185), // 桃色
            (221, 160, 221), // 梅红
            (255, 255, 224), // 浅黄
            (230, 230, 250), // 薰衣草
            (255, 228, 225), // 雾玫瑰
            (240, 248, 255), // 爱丽丝蓝
            (250, 240, 230), // 亚麻色
        ]

        let backgroundColor = backgroundColors.randomElement() ?? (255, 255, 255)

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 第一步：检测边缘并创建粗描边和阴影
        var edgeMap = Array(repeating: 0, count: width * height) // 0=背景, 1=阴影, 2=描边, 3=主体
        let borderWidth = 3 // 描边宽度
        let shadowOffset = 2 // 阴影偏移

        // 首先标记所有不透明像素为主体
        for y in 0..<height {
            for x in 0..<width {
                let index = y * width + x
                let pixelIndex = index * 4
                let currentAlpha = pixelData[pixelIndex + 3]

                if currentAlpha > 128 {
                    edgeMap[index] = 3 // 主体
                }
            }
        }

        // 创建粗描边：在主体周围扩展borderWidth像素
        var tempEdgeMap = edgeMap
        for y in 0..<height {
            for x in 0..<width {
                let index = y * width + x

                if edgeMap[index] == 3 { // 如果是主体像素
                    // 在周围borderWidth范围内创建描边
                    for dy in -borderWidth...borderWidth {
                        for dx in -borderWidth...borderWidth {
                            let newY = y + dy
                            let newX = x + dx

                            if newY >= 0 && newY < height && newX >= 0 && newX < width {
                                let newIndex = newY * width + newX
                                let distance = sqrt(Double(dx * dx + dy * dy))

                                if distance <= Double(borderWidth) && tempEdgeMap[newIndex] == 0 {
                                    tempEdgeMap[newIndex] = 2 // 描边
                                }
                            }
                        }
                    }
                }
            }
        }

        // 创建阴影：在描边和主体的右下方创建阴影
        for y in 0..<height {
            for x in 0..<width {
                let index = y * width + x

                if tempEdgeMap[index] >= 2 { // 如果是描边或主体
                    let shadowY = y + shadowOffset
                    let shadowX = x + shadowOffset

                    if shadowY < height && shadowX < width {
                        let shadowIndex = shadowY * width + shadowX
                        if tempEdgeMap[shadowIndex] == 0 {
                            tempEdgeMap[shadowIndex] = 1 // 阴影
                        }
                    }
                }
            }
        }

        edgeMap = tempEdgeMap

        // 第二步：应用效果
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let pixelIndex = i / 4
            let edgeType = edgeMap[pixelIndex]

            switch edgeType {
            case 0: // 背景区域
                pixelData[i] = backgroundColor.0
                pixelData[i + 1] = backgroundColor.1
                pixelData[i + 2] = backgroundColor.2
                pixelData[i + 3] = 255

            case 1: // 阴影区域
                pixelData[i] = 100      // 深灰色阴影
                pixelData[i + 1] = 100
                pixelData[i + 2] = 100
                pixelData[i + 3] = 180  // 半透明阴影

            case 2: // 描边区域
                pixelData[i] = 255      // 白色描边
                pixelData[i + 1] = 255
                pixelData[i + 2] = 255
                pixelData[i + 3] = 255

            case 3: // 主体区域
                // 保持原色不变
                break

            default:
                break
            }
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 创建漫画书风格
    private func createComicBookStyle(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 漫画书调色板 - 鲜艳但不刺眼
        let comicColors: [(UInt8, UInt8, UInt8)] = [
            (255, 255, 255), // 白色
            (0, 0, 0),       // 黑色
            (255, 0, 0),     // 红色
            (0, 0, 255),     // 蓝色
            (255, 255, 0),   // 黄色
            (0, 255, 0),     // 绿色
            (255, 165, 0),   // 橙色
            (128, 0, 128),   // 紫色
            (255, 192, 203), // 粉红
            (165, 42, 42),   // 棕色
            (128, 128, 128), // 灰色
            (255, 20, 147),  // 深粉红
        ]

        // 第一步：应用漫画调色板
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = pixelData[i]
            let g = pixelData[i + 1]
            let b = pixelData[i + 2]
            let a = pixelData[i + 3]

            var minDistance = Float.infinity
            var closestColor = comicColors[0]

            for color in comicColors {
                let distance = colorDistance(r1: r, g1: g, b1: b, r2: color.0, g2: color.1, b2: color.2)
                if distance < minDistance {
                    minDistance = distance
                    closestColor = color
                }
            }

            pixelData[i] = closestColor.0
            pixelData[i + 1] = closestColor.1
            pixelData[i + 2] = closestColor.2
            pixelData[i + 3] = a
        }

        // 第二步：添加漫画风格的轮廓线
        let originalData = Array(UnsafeBufferPointer(start: pixelData, count: width * height * 4))

        for y in 1..<(height-1) {
            for x in 1..<(width-1) {
                let index = (y * width + x) * 4

                let centerR = originalData[index]
                let centerG = originalData[index + 1]
                let centerB = originalData[index + 2]

                // 检查周围像素的颜色差异
                var hasEdge = false

                for dy in -1...1 {
                    for dx in -1...1 {
                        if dx == 0 && dy == 0 { continue }

                        let neighborIndex = ((y + dy) * width + (x + dx)) * 4
                        let neighborR = originalData[neighborIndex]
                        let neighborG = originalData[neighborIndex + 1]
                        let neighborB = originalData[neighborIndex + 2]

                        let diff = colorDistance(r1: centerR, g1: centerG, b1: centerB,
                                                r2: neighborR, g2: neighborG, b2: neighborB)
                        if diff > 100 {
                            hasEdge = true
                            break
                        }
                    }
                    if hasEdge { break }
                }

                // 如果是边缘，添加黑色轮廓
                if hasEdge {
                    pixelData[index] = 0     // R
                    pixelData[index + 1] = 0 // G
                    pixelData[index + 2] = 0 // B
                }
            }
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 创建自适应像素艺术 - 面部区域保留更多细节
    private func createAdaptivePixelArt(from image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        // 创建像素数据
        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 定义面部区域（中心80%）
        let faceMargin = 0.1
        let faceStartX = Int(Double(width) * faceMargin)
        let faceEndX = Int(Double(width) * (1.0 - faceMargin))
        let faceStartY = Int(Double(height) * faceMargin)
        let faceEndY = Int(Double(height) * (1.0 - faceMargin))

        // 分区像素化处理 - 使用更高效的方法
        // 先处理面部区域（3x3像素块）
        for y in stride(from: faceStartY, to: faceEndY, by: 3) {
            for x in stride(from: faceStartX, to: faceEndX, by: 3) {
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: 3,
                                                   width: width, height: height)

                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: 3,
                         width: width, height: height,
                         color: avgColor)
            }
        }

        // 处理背景区域（5x5像素块）
        // 上边缘
        for y in stride(from: 0, to: faceStartY, by: 5) {
            for x in stride(from: 0, to: width, by: 5) {
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: 5,
                                                   width: width, height: height)

                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: 5,
                         width: width, height: height,
                         color: avgColor)
            }
        }

        // 下边缘
        for y in stride(from: faceEndY, to: height, by: 5) {
            for x in stride(from: 0, to: width, by: 5) {
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: 5,
                                                   width: width, height: height)

                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: 5,
                         width: width, height: height,
                         color: avgColor)
            }
        }

        // 左右边缘
        for y in stride(from: faceStartY, to: faceEndY, by: 5) {
            // 左边缘
            for x in stride(from: 0, to: faceStartX, by: 5) {
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: 5,
                                                   width: width, height: height)

                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: 5,
                         width: width, height: height,
                         color: avgColor)
            }

            // 右边缘
            for x in stride(from: faceEndX, to: width, by: 5) {
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: 5,
                                                   width: width, height: height)

                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: 5,
                         width: width, height: height,
                         color: avgColor)
            }
        }

        // 创建新的CGImage
        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 创建真正的像素艺术效果（保留原方法作为备用）
    private func createPixelArt(from image: UIImage, blockSize: Int) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        // 创建像素数据
        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        // 绘制原图到context
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 像素化处理
        for y in stride(from: 0, to: height, by: blockSize) {
            for x in stride(from: 0, to: width, by: blockSize) {
                // 计算块的平均颜色
                let avgColor = calculateAverageColor(pixelData: pixelData,
                                                   x: x, y: y,
                                                   blockSize: blockSize,
                                                   width: width, height: height)

                // 量化颜色（减少颜色层次）
                let quantizedColor = quantizeColor(avgColor)

                // 填充整个块
                fillBlock(pixelData: pixelData,
                         x: x, y: y,
                         blockSize: blockSize,
                         width: width, height: height,
                         color: quantizedColor)
            }
        }

        // 创建新的CGImage
        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }
    
    /// 计算块的平均颜色
    private func calculateAverageColor(pixelData: UnsafeMutablePointer<UInt8>,
                                     x: Int, y: Int,
                                     blockSize: Int,
                                     width: Int, height: Int) -> (r: UInt8, g: UInt8, b: UInt8, a: UInt8) {
        var totalR: Int = 0, totalG: Int = 0, totalB: Int = 0, totalA: Int = 0
        var pixelCount = 0

        let endX = min(x + blockSize, width)
        let endY = min(y + blockSize, height)

        for py in y..<endY {
            for px in x..<endX {
                let index = (py * width + px) * 4
                totalR += Int(pixelData[index])
                totalG += Int(pixelData[index + 1])
                totalB += Int(pixelData[index + 2])
                totalA += Int(pixelData[index + 3])
                pixelCount += 1
            }
        }

        guard pixelCount > 0 else { return (0, 0, 0, 0) }

        return (
            r: UInt8(totalR / pixelCount),
            g: UInt8(totalG / pixelCount),
            b: UInt8(totalB / pixelCount),
            a: UInt8(totalA / pixelCount)
        )
    }

    /// 量化颜色 - 使用像素艺术风格的调色板
    private func quantizeColor(_ color: (r: UInt8, g: UInt8, b: UInt8, a: UInt8)) -> (r: UInt8, g: UInt8, b: UInt8, a: UInt8) {
        // 使用6级量化，创建更丰富但仍然有限的调色板
        let levels = 6
        let step = 255 / (levels - 1)

        // 量化每个颜色通道
        let quantizedR = UInt8((Int(color.r) / step) * step)
        let quantizedG = UInt8((Int(color.g) / step) * step)
        let quantizedB = UInt8((Int(color.b) / step) * step)

        // 增强对比度，让颜色更鲜明
        let enhancedR = enhanceContrast(quantizedR)
        let enhancedG = enhanceContrast(quantizedG)
        let enhancedB = enhanceContrast(quantizedB)

        return (r: enhancedR, g: enhancedG, b: enhancedB, a: color.a)
    }

    /// 增强对比度，让像素艺术颜色更鲜明
    private func enhanceContrast(_ value: UInt8) -> UInt8 {
        let normalized = Float(value) / 255.0

        // 应用S曲线增强对比度
        let enhanced = pow(normalized, 0.8) // 稍微提亮
        let result = enhanced * 255.0

        return UInt8(max(0, min(255, result)))
    }

    /// 填充像素块
    private func fillBlock(pixelData: UnsafeMutablePointer<UInt8>,
                          x: Int, y: Int,
                          blockSize: Int,
                          width: Int, height: Int,
                          color: (r: UInt8, g: UInt8, b: UInt8, a: UInt8)) {
        let endX = min(x + blockSize, width)
        let endY = min(y + blockSize, height)

        for py in y..<endY {
            for px in x..<endX {
                let index = (py * width + px) * 4
                pixelData[index] = color.r
                pixelData[index + 1] = color.g
                pixelData[index + 2] = color.b
                pixelData[index + 3] = color.a
            }
        }
    }
    
    /// 专门为像素艺术调整图片为正方形
    private func makeSquareImageForPixelArt(_ image: UIImage) -> UIImage {
        let originalSize = image.size
        let size = min(originalSize.width, originalSize.height)

        // 像素艺术最佳尺寸：128x128（足够细节，处理速度快）
        let targetSize: CGFloat = 128
        let squareSize = CGSize(width: targetSize, height: targetSize)

        let renderer = UIGraphicsImageRenderer(size: squareSize)
        return renderer.image { _ in
            // 计算居中裁剪的源区域
            let scale = targetSize / size
            let scaledOriginalSize = CGSize(
                width: originalSize.width * scale,
                height: originalSize.height * scale
            )

            let drawRect = CGRect(
                x: (squareSize.width - scaledOriginalSize.width) / 2,
                y: (squareSize.height - scaledOriginalSize.height) / 2,
                width: scaledOriginalSize.width,
                height: scaledOriginalSize.height
            )
            image.draw(in: drawRect)
        }
    }

    /// 将图片调整为正方形（通用方法）
    private func makeSquareImage(_ image: UIImage) -> UIImage {
        let originalSize = image.size
        let size = min(originalSize.width, originalSize.height)

        // 确保正方形尺寸不超过256x256（头像用途足够）
        let maxSquareSize: CGFloat = 256
        let finalSize = min(size, maxSquareSize)
        let squareSize = CGSize(width: finalSize, height: finalSize)

        let renderer = UIGraphicsImageRenderer(size: squareSize)
        return renderer.image { _ in
            // 计算居中裁剪的源区域
            let scale = finalSize / size
            let scaledOriginalSize = CGSize(
                width: originalSize.width * scale,
                height: originalSize.height * scale
            )

            let drawRect = CGRect(
                x: (squareSize.width - scaledOriginalSize.width) / 2,
                y: (squareSize.height - scaledOriginalSize.height) / 2,
                width: scaledOriginalSize.width,
                height: scaledOriginalSize.height
            )
            image.draw(in: drawRect)
        }
    }
    
    /// 添加圆角效果
    private func addRoundedCorners(_ image: UIImage?) -> UIImage? {
        guard let image = image else { return nil }

        let size = image.size

        // 确保尺寸合理，避免内存问题
        guard size.width > 0 && size.height > 0 && size.width <= 1024 && size.height <= 1024 else {
            print("图片尺寸异常: \(size)")
            return image
        }

        let cornerRadius = size.width * 0.2 // 20%的圆角

        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            let rect = CGRect(origin: .zero, size: size)
            let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
            path.addClip()
            image.draw(in: rect)
        }
    }

    /// 应用扩展调色板 - 32色调色板，特别优化肌肤色彩
    private func applyExtendedPalette(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 应用扩展32色调色板
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = pixelData[i]
            let g = pixelData[i + 1]
            let b = pixelData[i + 2]
            let a = pixelData[i + 3]

            let mappedColor = mapToExtendedPalette(r: r, g: g, b: b, a: a)
            pixelData[i] = mappedColor.r
            pixelData[i + 1] = mappedColor.g
            pixelData[i + 2] = mappedColor.b
            pixelData[i + 3] = mappedColor.a
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 映射到扩展32色调色板 - 特别优化肌肤色彩
    private func mapToExtendedPalette(r: UInt8, g: UInt8, b: UInt8, a: UInt8) -> (r: UInt8, g: UInt8, b: UInt8, a: UInt8) {
        // 扩展32色调色板，特别为人脸优化
        let palette: [(UInt8, UInt8, UInt8)] = [
            // 肌肤色系列（8色）
            (255, 220, 177), // 极浅肤色
            (241, 194, 125), // 浅肤色
            (224, 172, 105), // 中浅肤色
            (198, 134, 66),  // 中肤色
            (161, 102, 94),  // 中深肤色
            (110, 84, 94),   // 深肤色
            (92, 64, 51),    // 阴影色
            (255, 239, 213), // 高光色

            // 头发色系列（6色）
            (0, 0, 0),       // 黑色
            (101, 67, 33),   // 深棕
            (160, 102, 28),  // 浅棕
            (218, 165, 32),  // 金色
            (139, 69, 19),   // 红棕色
            (128, 128, 128), // 灰色

            // 眼睛色系列（4色）
            (25, 25, 25),    // 深黑
            (101, 67, 33),   // 深棕
            (70, 130, 180),  // 蓝色
            (34, 139, 34),   // 绿色

            // 基础色系列（8色）
            (0, 0, 0),       // 纯黑
            (255, 255, 255), // 纯白
            (64, 64, 64),    // 深灰
            (192, 192, 192), // 浅灰
            (255, 0, 0),     // 红色
            (0, 255, 0),     // 绿色
            (0, 0, 255),     // 蓝色
            (255, 255, 0),   // 黄色

            // 特殊色系列（6色）
            (255, 182, 193), // 粉红（嘴唇）
            (255, 165, 0),   // 橙色
            (128, 0, 128),   // 紫色
            (0, 255, 255),   // 青色
            (139, 0, 0),     // 深红
            (0, 0, 139)      // 深蓝
        ]

        var minDistance = Float.infinity
        var closestColor = palette[0]

        for color in palette {
            let distance = colorDistance(r1: r, g1: g, b1: b, r2: color.0, g2: color.1, b2: color.2)
            if distance < minDistance {
                minDistance = distance
                closestColor = color
            }
        }

        return (r: closestColor.0, g: closestColor.1, b: closestColor.2, a: a)
    }

    /// 计算颜色距离
    private func colorDistance(r1: UInt8, g1: UInt8, b1: UInt8, r2: UInt8, g2: UInt8, b2: UInt8) -> Float {
        let dr = Float(r1) - Float(r2)
        let dg = Float(g1) - Float(g2)
        let db = Float(b1) - Float(b2)
        return sqrt(dr*dr + dg*dg + db*db)
    }

    /// 添加像素艺术轮廓
    private func addPixelOutline(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 创建副本用于边缘检测
        let originalData = Array(UnsafeBufferPointer(start: pixelData, count: width * height * 4))

        // 简单的边缘检测和轮廓增强
        for y in 1..<(height-1) {
            for x in 1..<(width-1) {
                let index = (y * width + x) * 4

                // 检查周围像素的颜色差异
                let centerR = originalData[index]
                let centerG = originalData[index + 1]
                let centerB = originalData[index + 2]

                var maxDiff: Float = 0

                // 检查8个方向的邻居
                for dy in -1...1 {
                    for dx in -1...1 {
                        if dx == 0 && dy == 0 { continue }

                        let neighborIndex = ((y + dy) * width + (x + dx)) * 4
                        let neighborR = originalData[neighborIndex]
                        let neighborG = originalData[neighborIndex + 1]
                        let neighborB = originalData[neighborIndex + 2]

                        let diff = colorDistance(r1: centerR, g1: centerG, b1: centerB,
                                                r2: neighborR, g2: neighborG, b2: neighborB)
                        maxDiff = max(maxDiff, diff)
                    }
                }

                // 如果颜色差异大，增强对比度
                if maxDiff > 50 {
                    pixelData[index] = min(255, UInt8(Float(centerR) * 1.2))
                    pixelData[index + 1] = min(255, UInt8(Float(centerG) * 1.2))
                    pixelData[index + 2] = min(255, UInt8(Float(centerB) * 1.2))
                }
            }
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 应用复古滤镜
    private func applyRetroFilter(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 应用复古色调
        for i in stride(from: 0, to: width * height * 4, by: 4) {
            let r = Float(pixelData[i]) / 255.0
            let g = Float(pixelData[i + 1]) / 255.0
            let b = Float(pixelData[i + 2]) / 255.0

            // 应用复古色调矩阵（偏暖色调）
            let newR = r * 1.1 + g * 0.1 + b * 0.0
            let newG = r * 0.0 + g * 1.0 + b * 0.1
            let newB = r * 0.1 + g * 0.0 + b * 0.9

            pixelData[i] = UInt8(max(0, min(255, newR * 255)))
            pixelData[i + 1] = UInt8(max(0, min(255, newG * 255)))
            pixelData[i + 2] = UInt8(max(0, min(255, newB * 255)))
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 轻度风格化增强 - 只增强对比度，不破坏面部特征
    private func applyGentleEnhancement(_ image: UIImage?) -> UIImage? {
        guard let image = image else { return nil }

        // 只应用轻微的对比度和饱和度增强
        guard let ciImage = CIImage(image: image) else { return image }

        // 轻微增强对比度和饱和度
        let colorControls = CIFilter.colorControls()
        colorControls.inputImage = ciImage
        colorControls.saturation = 1.1    // 轻微增加饱和度
        colorControls.contrast = 1.05     // 轻微增加对比度
        colorControls.brightness = 0.02   // 轻微增加亮度

        guard let output = colorControls.outputImage,
              let cgImage = context.createCGImage(output, from: output.extent) else {
            return image
        }

        return UIImage(cgImage: cgImage)
    }

    /// 添加扫描线效果
    private func addScanlines(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 每隔2行添加轻微的暗化效果
        for y in stride(from: 0, to: height, by: 2) {
            for x in 0..<width {
                let index = (y * width + x) * 4
                pixelData[index] = UInt8(Float(pixelData[index]) * 0.9)
                pixelData[index + 1] = UInt8(Float(pixelData[index + 1]) * 0.9)
                pixelData[index + 2] = UInt8(Float(pixelData[index + 2]) * 0.9)
            }
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }

    /// 添加CRT效果
    private func addCRTEffect(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height

        guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
              let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: 8,
                                    bytesPerRow: width * 4,
                                    space: colorSpace,
                                    bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixelData = data.bindMemory(to: UInt8.self, capacity: width * height * 4)

        // 添加轻微的边缘暗化效果（模拟CRT显示器的边缘暗化）
        let centerX = Float(width) / 2
        let centerY = Float(height) / 2
        let maxDistance = sqrt(centerX * centerX + centerY * centerY)

        for y in 0..<height {
            for x in 0..<width {
                let index = (y * width + x) * 4

                let dx = Float(x) - centerX
                let dy = Float(y) - centerY
                let distance = sqrt(dx * dx + dy * dy)
                let factor = 1.0 - (distance / maxDistance) * 0.2 // 最多暗化20%

                pixelData[index] = UInt8(Float(pixelData[index]) * factor)
                pixelData[index + 1] = UInt8(Float(pixelData[index + 1]) * factor)
                pixelData[index + 2] = UInt8(Float(pixelData[index + 2]) * factor)
            }
        }

        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }


}

// MARK: - Pinkbot头像生成器
class PinkbotAvatarGenerator {
    static let shared = PinkbotAvatarGenerator()
    
    private init() {}
    
    /// 生成Pinkbot头像
    /// - Parameter size: 头像尺寸
    /// - Returns: Pinkbot头像
    func generatePinkbotAvatar(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let cgContext = context.cgContext
            
            // 背景圆形
            cgContext.setFillColor(UIColor.systemPink.cgColor)
            cgContext.fillEllipse(in: rect)
            
            // 机器人眼睛
            let eyeSize = size.width * 0.15
            let eyeY = size.height * 0.35
            
            // 左眼
            let leftEyeRect = CGRect(
                x: size.width * 0.25 - eyeSize/2,
                y: eyeY - eyeSize/2,
                width: eyeSize,
                height: eyeSize
            )
            cgContext.setFillColor(UIColor.white.cgColor)
            cgContext.fillEllipse(in: leftEyeRect)
            
            // 右眼
            let rightEyeRect = CGRect(
                x: size.width * 0.75 - eyeSize/2,
                y: eyeY - eyeSize/2,
                width: eyeSize,
                height: eyeSize
            )
            cgContext.fillEllipse(in: rightEyeRect)
            
            // 眼珠
            let pupilSize = eyeSize * 0.6
            cgContext.setFillColor(UIColor.black.cgColor)
            
            // 左眼珠
            let leftPupilRect = CGRect(
                x: leftEyeRect.midX - pupilSize/2,
                y: leftEyeRect.midY - pupilSize/2,
                width: pupilSize,
                height: pupilSize
            )
            cgContext.fillEllipse(in: leftPupilRect)
            
            // 右眼珠
            let rightPupilRect = CGRect(
                x: rightEyeRect.midX - pupilSize/2,
                y: rightEyeRect.midY - pupilSize/2,
                width: pupilSize,
                height: pupilSize
            )
            cgContext.fillEllipse(in: rightPupilRect)
            
            // 嘴巴（微笑弧线）
            let mouthPath = UIBezierPath()
            let mouthCenter = CGPoint(x: size.width/2, y: size.height * 0.65)
            let mouthRadius = size.width * 0.2
            
            mouthPath.addArc(
                withCenter: mouthCenter,
                radius: mouthRadius,
                startAngle: 0,
                endAngle: .pi,
                clockwise: false
            )
            
            cgContext.setStrokeColor(UIColor.white.cgColor)
            cgContext.setLineWidth(size.width * 0.05)
            cgContext.setLineCap(.round)
            cgContext.addPath(mouthPath.cgPath)
            cgContext.strokePath()
            
            // 天线
            let antennaHeight = size.height * 0.15
            let antennaWidth = size.width * 0.03
            
            // 左天线
            let leftAntennaRect = CGRect(
                x: size.width * 0.3 - antennaWidth/2,
                y: -antennaHeight * 0.3,
                width: antennaWidth,
                height: antennaHeight
            )
            cgContext.setFillColor(UIColor.white.cgColor)
            cgContext.fill(leftAntennaRect)
            
            // 右天线
            let rightAntennaRect = CGRect(
                x: size.width * 0.7 - antennaWidth/2,
                y: -antennaHeight * 0.3,
                width: antennaWidth,
                height: antennaHeight
            )
            cgContext.fill(rightAntennaRect)
            
            // 天线球
            let ballSize = antennaWidth * 2
            cgContext.fillEllipse(in: CGRect(
                x: leftAntennaRect.midX - ballSize/2,
                y: leftAntennaRect.minY - ballSize/2,
                width: ballSize,
                height: ballSize
            ))
            cgContext.fillEllipse(in: CGRect(
                x: rightAntennaRect.midX - ballSize/2,
                y: rightAntennaRect.minY - ballSize/2,
                width: ballSize,
                height: ballSize
            ))
        }
    }
}
