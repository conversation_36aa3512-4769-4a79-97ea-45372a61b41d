//
//  SubjectLiftManager.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import Vision
import CoreImage

class SubjectLiftManager {
    static let shared = SubjectLiftManager()
    
    // 🔥 共享的CIContext，避免重复创建造成内存泄漏
    private let sharedCIContext: CIContext

    // 🔥 依赖注入: 允许外部提供StickerStyleProcessor实例
    private let stickerProcessor: StickerStyleProcessor

    private init(stickerProcessor: StickerStyleProcessor = .shared) {
        self.stickerProcessor = stickerProcessor
        
        // 🔥 创建一次CIContext并重用，大幅减少内存占用
        self.sharedCIContext = CIContext(options: [
            .cacheIntermediates: false,  // 不缓存中间结果
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
    }

    /// 从图片中抠出主体
    /// - Parameters:
    ///   - image: 原始图片
    ///   - completion: 完成回调，返回抠图结果或错误
    func liftSubject(from image: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        guard let ciImage = CIImage(image: image) else {
            completion(.failure(.invalidImage))
            return
        }

        DispatchQueue.global(qos: .userInitiated).async {
            if #available(iOS 16.0, *) {
                self.liftSubjectIOS16(from: ciImage, originalImage: image, completion: completion)
            } else {
                self.liftSubjectLegacy(from: ciImage, originalImage: image, completion: completion)
            }
        }
    }

    /// 自动处理：抠图 + 贴纸化
    /// - Parameters:
    ///   - image: 原始图片
    ///   - completion: 完成回调，返回(抠图结果, 贴纸结果)或错误
    func autoProcessImage(from image: UIImage, completion: @escaping (Result<(liftedImage: UIImage, stickerImage: UIImage), SubjectLiftError>) -> Void) {
        // 🔥 使用更激进的内存管理策略
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                // 记录内存使用情况
                self.logMemoryUsage("SubjectLiftManager开始自动处理")

                // 🔥 关键修复：传入的图片已经在CameraController中压缩过，直接使用
                let processedImage = image
                self.logMemoryUsage("原图压缩完成")

                // 首先进行抠图（使用压缩后的图片）
                self.liftSubject(from: processedImage) { result in
                    autoreleasepool {
                        switch result {
                        case .success(let liftedImage):
                            self.logMemoryUsage("抠图完成，开始贴纸化")

                            // 🔥 在贴纸化前强制清理Vision框架的内存占用
                            autoreleasepool {
                                ImageManager.shared.clearTemporaryCache()
                            }
                            
                            // 🔥 关键优化：传递Data而非UIImage，打断引用链，降低内存峰值
                            if let liftedImageData = liftedImage.pngData(),
                               let stickerImage = self.stickerProcessor.createStickerStyle(from: liftedImageData) {
                                
                                // 🔥 立即强制清理所有临时内存
                                autoreleasepool {
                                    ImageManager.shared.clearTemporaryCache()
                                }
                                
                                // 🔥 强制垃圾回收
                                DispatchQueue.global(qos: .utility).async {
                                    autoreleasepool {
                                        ImageManager.shared.clearCache()
                                        // 多次触发内存清理
                                        for _ in 0..<5 {
                                            ImageManager.shared.clearTemporaryCache()
                                        }
                                    }
                                }
                                
                                DispatchQueue.main.async {
                                    self.logMemoryUsage("贴纸化完成")
                                    completion(.success((liftedImage: liftedImage, stickerImage: stickerImage)))
                                    
                                    // 🔥 完成后再次清理
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                        autoreleasepool {
                                            ImageManager.shared.clearTemporaryCache()
                                            ImageManager.shared.cleanupAfterImageProcessing()
                                        }
                                    }
                                }
                            } else {
                                // 贴纸化失败，但抠图成功，返回抠图结果作为贴纸
                                autoreleasepool {
                                    ImageManager.shared.clearTemporaryCache()
                                }
                                
                                DispatchQueue.main.async {
                                    self.logMemoryUsage("贴纸化失败，使用抠图作为贴纸")
                                    completion(.success((liftedImage: liftedImage, stickerImage: liftedImage)))
                                }
                            }
                        case .failure(let error):
                            self.logMemoryUsage("抠图失败")
                            // 🔥 即使失败也要清理内存
                            autoreleasepool {
                                ImageManager.shared.clearTemporaryCache()
                            }
                            completion(.failure(error))
                        }
                    }
                }
            }
        }
    }

    @available(iOS 16.0, *)
    private func liftSubjectIOS16(from ciImage: CIImage, originalImage: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage, options: [:])
        
        do {
            try handler.perform([request])
            
            guard let result = request.results?.first else {
                DispatchQueue.main.async {
                    completion(.failure(.noSubjectDetected))
                }
                return
            }
            
            // 🔥 CVPixelBuffer和CIImage的内存管理优化
            autoreleasepool {
                do {
                    // 生成蒙版
                    let maskPixelBuffer = try result.generateScaledMaskForImage(
                        forInstances: result.allInstances,
                        from: handler
                    )
                    
                    let maskImage = CIImage(cvPixelBuffer: maskPixelBuffer)
                    
                    // 应用蒙版创建抠图效果
                    guard let filter = CIFilter(name: "CIBlendWithMask") else {
                        DispatchQueue.main.async {
                            completion(.failure(.processingFailed))
                        }
                        return
                    }

                    filter.setValue(ciImage, forKey: kCIInputImageKey)
                    filter.setValue(maskImage, forKey: kCIInputMaskImageKey)
                    filter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)

                    guard let outputImage = filter.outputImage else {
                        DispatchQueue.main.async {
                            completion(.failure(.processingFailed))
                        }
                        return
                    }
                    
                    // 🔥 使用共享的CIContext，避免重复创建
                    autoreleasepool {
                        guard let cgImage = self.sharedCIContext.createCGImage(outputImage, from: outputImage.extent) else {
                            DispatchQueue.main.async {
                                completion(.failure(.renderingFailed))
                            }
                            return
                        }

                        // 创建UIImage时保持原始图片的方向信息
                        let resultImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

                        DispatchQueue.main.async {
                            completion(.success(resultImage))
                        }
                    }
                    
                    // 🔥 立即清理CVPixelBuffer相关内存
                    // CVPixelBuffer会在autoreleasepool结束时自动释放
                } catch {
                    DispatchQueue.main.async {
                        completion(.failure(.visionRequestFailed(error)))
                    }
                }
            }
            
        } catch {
            DispatchQueue.main.async {
                completion(.failure(.visionRequestFailed(error)))
            }
        }
    }
    
    private func liftSubjectLegacy(from ciImage: CIImage, originalImage: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        let request = VNGeneratePersonSegmentationRequest { request, error in
            guard error == nil,
                  let results = request.results as? [VNPixelBufferObservation],
                  let observation = results.first else {
                DispatchQueue.main.async {
                    completion(.failure(.legacyVersionNotSupported))
                }
                return
            }
            
            // 🔥 处理人像分割结果 - 内存优化版本
            autoreleasepool {
                let maskImage = CIImage(cvPixelBuffer: observation.pixelBuffer)
                let subjectImage = self.applyMatte(originalCIImage: ciImage, matteCIImage: maskImage)

                // 🔥 使用共享的CIContext，避免重复创建
                autoreleasepool {
                    guard let cgImage = self.sharedCIContext.createCGImage(subjectImage, from: subjectImage.extent) else {
                        DispatchQueue.main.async {
                            completion(.failure(.renderingFailed))
                        }
                        return
                    }

                    let finalUIImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

                    DispatchQueue.main.async {
                        completion(.success(finalUIImage))
                    }
                }
                
                // 🔥 CVPixelBuffer在autoreleasepool结束时自动释放
            }
        }
        
        let handler = VNImageRequestHandler(ciImage: ciImage)
        do {
            try handler.perform([request])
        } catch {
            DispatchQueue.main.async {
                completion(.failure(.visionRequestFailed(error)))
            }
        }
    }
    
    private func applyMatte(originalCIImage: CIImage, matteCIImage: CIImage) -> CIImage {
        guard let filter = CIFilter(name: "CIBlendWithMask") else {
            return originalCIImage
        }

        filter.setValue(originalCIImage, forKey: kCIInputImageKey)
        filter.setValue(matteCIImage, forKey: kCIInputMaskImageKey)
        filter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)

        return filter.outputImage ?? originalCIImage
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("SubjectLiftManager: \(context)")
    }
}

// MARK: - 错误类型定义
enum SubjectLiftError: LocalizedError {
    case invalidImage
    case noSubjectDetected
    case processingFailed
    case renderingFailed
    case visionRequestFailed(Error)
    case legacyVersionNotSupported
    
    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无法处理图片数据"
        case .noSubjectDetected:
            return "未检测到主体对象，请尝试其他图片或使用原图"
        case .processingFailed:
            return "抠图处理失败，请重试"
        case .renderingFailed:
            return "图像渲染失败"
        case .visionRequestFailed(let error):
            return "抠图失败: \(error.localizedDescription)"
        case .legacyVersionNotSupported:
            return "该功能需要iOS 16或更高版本以获得最佳抠图效果。\n您可以选择使用原图创建物品。"
        }
    }
}
