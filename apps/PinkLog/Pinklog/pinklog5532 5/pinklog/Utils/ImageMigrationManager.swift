import Foundation
import UIKit
import CoreData

/// 图片迁移管理器，负责将旧格式的图片数据迁移为新格式
class ImageMigrationManager {
    // MARK: - 单例
    static let shared = ImageMigrationManager()
    
    // MARK: - 属性
    
    /// 是否正在迁移
    private(set) var isMigrating = false
    
    /// 迁移进度 (0.0-1.0)
    private(set) var migrationProgress: Float = 0.0
    
    /// 迁移状态回调
    var migrationStatusCallback: ((Float, Bool, Error?) -> Void)?
    
    // MARK: - 初始化
    
    private init() {}
    
    // MARK: - 迁移方法
    
    /// 开始迁移所有图片
    /// - Parameter completion: 完成回调
    func startMigration(completion: @escaping (Bool, Error?) -> Void) {
        // 如果已经在迁移中，直接返回
        if isMigrating {
            completion(false, NSError(domain: "ImageMigration", code: 1, userInfo: [NSLocalizedDescriptionKey: "迁移已在进行中"]))
            return
        }
        
        // 设置迁移状态
        isMigrating = true
        migrationProgress = 0.0
        
        // 在后台线程执行迁移
        DispatchQueue.global(qos: .utility).async {
            // 获取所有使用记录
            let usageRecords = self.fetchAllUsageRecords()
            
            // 如果没有记录，直接完成
            if usageRecords.isEmpty {
                DispatchQueue.main.async {
                    self.isMigrating = false
                    self.migrationProgress = 1.0
                    self.migrationStatusCallback?(1.0, true, nil)
                    completion(true, nil)
                }
                return
            }
            
            // 记录总数
            let totalRecords = usageRecords.count
            var processedRecords = 0
            var migratedRecords = 0
            var error: Error? = nil
            
            // 处理每个记录
            for record in usageRecords {
                autoreleasepool {
                    do {
                        // 迁移记录中的图片
                        let migrated = try self.migrateImagesInRecord(record)
                        if migrated {
                            migratedRecords += 1
                        }
                    } catch let migrationError {
                        error = migrationError
                    }
                    
                    // 更新进度
                    processedRecords += 1
                    let progress = Float(processedRecords) / Float(totalRecords)
                    
                    DispatchQueue.main.async {
                        self.migrationProgress = progress
                        self.migrationStatusCallback?(progress, false, error)
                    }
                }
            }
            
            // 完成迁移
            DispatchQueue.main.async {
                self.isMigrating = false
                self.migrationProgress = 1.0
                self.migrationStatusCallback?(1.0, true, error)
                completion(error == nil, error)
            }
        }
    }
    
    /// 迁移单个记录中的图片
    /// - Parameter record: 使用记录
    /// - Returns: 是否成功迁移
    private func migrateImagesInRecord(_ record: UsageRecord) throws -> Bool {
        // 如果没有图片数据，直接返回
        guard let imageData = record.images else {
            return false
        }
        
        // 尝试解码图片数据
        do {
            // 尝试解码为新格式（Data数组）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                // 数据已经是新格式，无需迁移
                return false
            }
            
            // 尝试解码为旧格式（UIImage数组）
            let unarchiver = try NSKeyedUnarchiver(forReadingFrom: imageData)
            unarchiver.requiresSecureCoding = false
            if let images = unarchiver.decodeObject(forKey: "images") as? [UIImage] {
                // 迁移图片数据
                var compressedImageDatas: [Data] = []

                for image in images {
                    // 压缩图片
                    let compressedImage = ImageManager.shared.compressImage(image, maxSize: 1024, quality: 0.7)
                    if let data = compressedImage.jpegData(compressionQuality: 0.7) {
                        compressedImageDatas.append(data)
                    }
                }

                // 保存迁移后的数据
                if !compressedImageDatas.isEmpty {
                    let context = PersistenceController.shared.container.viewContext
                    let imageDataArray = try NSKeyedArchiver.archivedData(withRootObject: compressedImageDatas, requiringSecureCoding: false)
                    record.images = imageDataArray
                    try context.save()
                    return true
                }
            }
        } catch {
            throw error
        }
        
        return false
    }
    
    /// 获取所有使用记录
    /// - Returns: 使用记录数组
    private func fetchAllUsageRecords() -> [UsageRecord] {
        let context = PersistenceController.shared.container.viewContext
        let fetchRequest: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        
        // 只获取有图片的记录
        fetchRequest.predicate = NSPredicate(format: "images != nil")
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("获取使用记录失败: \(error)")
            return []
        }
    }
}
