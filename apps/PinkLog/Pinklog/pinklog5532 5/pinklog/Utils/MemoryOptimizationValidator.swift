import Foundation
import UIKit

/// 内存优化验证工具，用于在真机上验证图片处理优化的效果
class MemoryOptimizationValidator {
    static let shared = MemoryOptimizationValidator()

    private let memoryMonitor = MemoryMonitor.shared
    private let imageManager = ImageManager.shared

    private init() {}

    // MARK: - Validation Results

    struct OptimizationReport {
        let timestamp: Date
        let initialMemory: Double
        let finalMemory: Double
        let maxMemoryUsage: Double
        let memoryOptimizationEffective: Bool
        let compressionWorking: Bool
        let cleanupWorking: Bool

        var description: String {
            return """
            内存优化验证报告 (\(DateFormatter.localizedString(from: timestamp, dateStyle: .short, timeStyle: .medium)))
            ==========================================
            初始内存使用: \(String(format: "%.1f", initialMemory)) MB
            最终内存使用: \(String(format: "%.1f", finalMemory)) MB
            最大内存使用: \(String(format: "%.1f", maxMemoryUsage)) MB
            内存增长: \(String(format: "%.1f", finalMemory - initialMemory)) MB

            优化效果评估:
            - 内存优化有效: \(memoryOptimizationEffective ? "✅" : "❌")
            - 图片压缩正常: \(compressionWorking ? "✅" : "❌")
            - 内存清理正常: \(cleanupWorking ? "✅" : "❌")

            建议:
            \(getRecommendations())
            """
        }

        private func getRecommendations() -> String {
            var recommendations: [String] = []

            if !memoryOptimizationEffective {
                recommendations.append("- 内存使用仍然过高，建议进一步优化")
            }

            if !compressionWorking {
                recommendations.append("- 图片压缩功能异常，需要检查压缩算法")
            }

            if !cleanupWorking {
                recommendations.append("- 内存清理不够及时，需要加强清理机制")
            }

            if finalMemory > 500.0 {
                recommendations.append("- 最终内存使用超过500MB，建议优化")
            }

            if recommendations.isEmpty {
                recommendations.append("- 内存优化效果良好，继续保持")
            }

            return recommendations.joined(separator: "\n")
        }
    }
    
    // MARK: - Validation Methods

    /// 运行内存优化验证（适用于真机测试）
    func validateOptimization() -> OptimizationReport {
        let initialMemory = memoryMonitor.getCurrentMemoryUsage()
        memoryMonitor.recordMemoryUsage("开始内存优化验证")

        var maxMemory = initialMemory
        var compressionWorking = false
        var cleanupWorking = false

        // 1. 验证图片压缩功能
        let testImage = createTestImage(size: CGSize(width: 2000, height: 2000))

        if let compressedData = imageManager.compressToTargetSize(
            testImage,
            maxSize: 512,
            quality: 0.3,
            targetBytes: 100 * 1024
        ) {
            compressionWorking = compressedData.count <= 100 * 1024
            memoryMonitor.recordMemoryUsage("图片压缩完成")
        }

        maxMemory = max(maxMemory, memoryMonitor.getCurrentMemoryUsage())

        // 2. 验证内存清理功能
        let memoryBeforeCleanup = memoryMonitor.getCurrentMemoryUsage()

        // 创建一些临时对象然后清理
        autoreleasepool {
            var tempImages: [UIImage?] = []
            for _ in 0..<3 {
                tempImages.append(createTestImage(size: CGSize(width: 800, height: 800)))
            }

            // 清理
            imageManager.clearImageReferences(&tempImages)
        }

        // 强制内存回收
        imageManager.clearTemporaryCache()

        let memoryAfterCleanup = memoryMonitor.getCurrentMemoryUsage()
        cleanupWorking = (memoryAfterCleanup - memoryBeforeCleanup) < 50.0

        memoryMonitor.recordMemoryUsage("内存清理完成")
        maxMemory = max(maxMemory, memoryAfterCleanup)

        let finalMemory = memoryMonitor.getCurrentMemoryUsage()

        // 判断内存优化是否有效
        let memoryOptimizationEffective = finalMemory < 500.0 && compressionWorking && cleanupWorking

        memoryMonitor.recordMemoryUsage("验证完成")

        return OptimizationReport(
            timestamp: Date(),
            initialMemory: initialMemory,
            finalMemory: finalMemory,
            maxMemoryUsage: maxMemory,
            memoryOptimizationEffective: memoryOptimizationEffective,
            compressionWorking: compressionWorking,
            cleanupWorking: cleanupWorking
        )
    }

    /// 模拟完整的添加物品流程并监控内存
    func simulateCompleteFlow() -> OptimizationReport {
        let initialMemory = memoryMonitor.getCurrentMemoryUsage()
        memoryMonitor.recordMemoryUsage("开始模拟完整流程")

        var maxMemory = initialMemory

        // 1. 模拟拍照
        let originalImage = createTestImage(size: CGSize(width: 1800, height: 1800))
        memoryMonitor.recordMemoryUsage("模拟拍照完成")
        maxMemory = max(maxMemory, memoryMonitor.getCurrentMemoryUsage())

        // 2. 模拟抠图处理（创建模拟的抠图和贴纸）
        let liftedImage = createTestImage(size: CGSize(width: 1800, height: 1800))
        let stickerImage = createTestImage(size: CGSize(width: 1800, height: 1800))
        memoryMonitor.recordMemoryUsage("模拟抠图处理完成")
        maxMemory = max(maxMemory, memoryMonitor.getCurrentMemoryUsage())

        // 3. 模拟用户选择贴纸风格并压缩
        var compressionWorking = false
        if let finalData = imageManager.compressToTargetSize(
            stickerImage,
            maxSize: 512,
            quality: 0.3,
            targetBytes: 100 * 1024
        ) {
            compressionWorking = finalData.count <= 100 * 1024
            memoryMonitor.recordMemoryUsage("最终图片压缩完成")
        }
        maxMemory = max(maxMemory, memoryMonitor.getCurrentMemoryUsage())

        // 4. 模拟清理其他图片引用
        autoreleasepool {
            // 在实际应用中，这里会清理originalImage和liftedImage的引用
            // 这里我们模拟这个过程
        }

        memoryMonitor.recordMemoryUsage("清理其他图片引用完成")

        let finalMemory = memoryMonitor.getCurrentMemoryUsage()
        let cleanupWorking = (finalMemory - initialMemory) < 100.0

        // 判断优化效果
        let memoryOptimizationEffective = finalMemory < 500.0 && compressionWorking && cleanupWorking

        memoryMonitor.recordMemoryUsage("完整流程模拟结束")

        return OptimizationReport(
            timestamp: Date(),
            initialMemory: initialMemory,
            finalMemory: finalMemory,
            maxMemoryUsage: maxMemory,
            memoryOptimizationEffective: memoryOptimizationEffective,
            compressionWorking: compressionWorking,
            cleanupWorking: cleanupWorking
        )
    }

    /// 获取当前内存统计信息
    func getCurrentMemoryStatistics() -> String {
        let stats = memoryMonitor.getMemoryStatistics()
        return stats.description
    }

    /// 清除所有监控记录
    func clearMonitoringData() {
        memoryMonitor.clearReadings()
    }

    
    // MARK: - Helper Methods
    
    private func createTestImage(size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 添加一些细节
            UIColor.white.setFill()
            let detailSize = min(size.width, size.height) * 0.3
            let detailRect = CGRect(
                x: (size.width - detailSize) / 2,
                y: (size.height - detailSize) / 2,
                width: detailSize,
                height: detailSize
            )
            context.fill(detailRect)
        }
    }
}
