//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    // 🔥 依赖注入: 允许外部提供ImageManager实例
    private let imageManager: ImageManager

    private init(imageManager: ImageManager = .shared) {
        self.imageManager = imageManager
    }
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImageData: 抠出的主体图片的数据（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImageData: Data) -> UIImage? {
        guard let subjectImage = UIImage(data: subjectImageData) else {
            return nil
        }
        
        return autoreleasepool {
            let totalStartTime = CFAbsoluteTimeGetCurrent()
            print("🚀 开始快速创建贴纸风格")

            // 🔥 确保贴纸化在合理尺寸上进行，避免内存占用过大
            let processedSubject = autoreleasepool {
                return imageManager.compressImage(subjectImage, maxSize: 1024, quality: 0.8)
            }

            // 1. 快速提取主要颜色
            let dominantColor = autoreleasepool {
                return extractMainColor(from: processedSubject)
            }
            print("✅ 提取的颜色: \(dominantColor)")

            // 2. 快速创建贴纸效果
            let result = autoreleasepool {
                return createSticker(subject: processedSubject, backgroundColor: dominantColor)
            }

            let totalTime = CFAbsoluteTimeGetCurrent() - totalStartTime
            print("🎉 贴纸创建完成，总耗时: \(Int(totalTime * 1000))ms")

            // 🔥 立即清理临时数据
            DispatchQueue.global(qos: .utility).async {
                autoreleasepool {
                    self.imageManager.cleanupAfterImageProcessing()
                }
            }

            return result
        }
    }

    /// 快速提取图片主要颜色
    private func extractMainColor(from image: UIImage) -> UIColor {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 大幅缩小图片以提高速度，保持原始方向
        let smallSize = CGSize(width: 32, height: 32)

        // 🔥 使用ImageManager的压缩方法，保持图片方向
        let smallImage = imageManager.compressImage(image, maxSize: 32, quality: 0.5)

        guard let cgImage = smallImage.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return UIColor.systemPink
        }

        var totalR: Int = 0
        var totalG: Int = 0
        var totalB: Int = 0
        var pixelCount: Int = 0

        let bytesPerPixel = 4
        let width = cgImage.width
        let height = cgImage.height

        // 只采样中心区域的每4个像素
        let centerX = width / 2
        let centerY = height / 2
        let radius = min(width, height) / 3

        for y in stride(from: 0, to: height, by: 2) {
            for x in stride(from: 0, to: width, by: 2) {
                // 检查是否在中心区域
                let dx = x - centerX
                let dy = y - centerY
                if dx * dx + dy * dy > radius * radius {
                    continue
                }

                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = bytes[pixelIndex + 3]

                if alpha > 100 {
                    let r = Int(bytes[pixelIndex])
                    let g = Int(bytes[pixelIndex + 1])
                    let b = Int(bytes[pixelIndex + 2])

                    // 简单亮度检查
                    if r + g + b > 150 {
                        totalR += r
                        totalG += g
                        totalB += b
                        pixelCount += 1
                    }
                }
            }
        }

        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        print("颜色提取耗时: \(Int(processingTime * 1000))ms")

        if pixelCount > 0 {
            let avgR = totalR / pixelCount
            let avgG = totalG / pixelCount
            let avgB = totalB / pixelCount

            print("快速提取颜色: R:\(avgR) G:\(avgG) B:\(avgB)")

            // 简化的粉色调整
            let finalColor = quickAdjustForPink(r: avgR, g: avgG, b: avgB)

            return UIColor(
                red: CGFloat(finalColor.r) / 255.0,
                green: CGFloat(finalColor.g) / 255.0,
                blue: CGFloat(finalColor.b) / 255.0,
                alpha: 1.0
            )
        }

        return UIColor.systemPink
    }

    /// 快速粉色调整
    private func quickAdjustForPink(r: Int, g: Int, b: Int) -> (r: Int, g: Int, b: Int) {
        // 简单检测粉色
        if r > g && r > 100 && b > 60 {
            let adjustedR = min(255, r + 20)
            let adjustedG = max(80, g - 10)
            let adjustedB = max(100, b - 15)
            print("粉色调整: R:\(adjustedR) G:\(adjustedG) B:\(adjustedB)")
            return (r: adjustedR, g: adjustedG, b: adjustedB)
        }
        return (r: r, g: g, b: b)
    }
    
    /// 极速贴纸 - 无描边版本，保持图片方向
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let stickerStartTime = CFAbsoluteTimeGetCurrent()

        let borderWidth: CGFloat = 30.0

        // 🔥 使用图片的实际显示尺寸，而不是像素尺寸
        let imageSize = subject.size
        let finalSize = CGSize(
            width: imageSize.width + borderWidth * 2,
            height: imageSize.height + borderWidth * 2
        )

        // 🔥 使用更安全的图片绘制方式，保持方向信息
        UIGraphicsBeginImageContextWithOptions(finalSize, false, subject.scale)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        // 1. 填充背景
        backgroundColor.setFill()
        context.fill(CGRect(origin: .zero, size: finalSize))

        // 2. 绘制主体图片，保持原始方向
        let subjectRect = CGRect(
            x: borderWidth,
            y: borderWidth,
            width: imageSize.width,
            height: imageSize.height
        )

        subject.draw(in: subjectRect)

        let result = UIGraphicsGetImageFromCurrentImageContext()

        let stickerTime = CFAbsoluteTimeGetCurrent() - stickerStartTime
        print("贴纸创建耗时: \(Int(stickerTime * 1000))ms")

        return result
    }


}
