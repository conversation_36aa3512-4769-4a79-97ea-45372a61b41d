<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23605" systemVersion="23H222" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Category" representedClassName="Category" syncable="YES" codeGenerationType="class">
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <relationship name="childCategories" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Category" inverseName="parentCategory" inverseEntity="Category"/>
        <relationship name="parentCategory" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Category" inverseName="childCategories" inverseEntity="Category"/>
        <relationship name="products" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="category" inverseEntity="Product"/>
    </entity>
    <entity name="Conversation" representedClassName="Conversation" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" attributeType="String"/>
        <attribute name="startDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastMessageDate" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="messages" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Message" inverseName="conversation" inverseEntity="Message"/>
    </entity>
    <entity name="ExpenseType" representedClassName="ExpenseType" syncable="YES" codeGenerationType="class">
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <relationship name="expenses" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="RelatedExpense" inverseName="type" inverseEntity="RelatedExpense"/>
    </entity>
    <entity name="Item" representedClassName="Item" syncable="YES" codeGenerationType="class">
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="LoanRecord" representedClassName="LoanRecord" syncable="YES" codeGenerationType="class">
        <attribute name="borrowerName" attributeType="String"/>
        <attribute name="contactInfo" optional="YES" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="dueDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isLoanedOut" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="returnDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="status" attributeType="String" defaultValueString="active"/>
        <relationship name="product" maxCount="1" deletionRule="Nullify" destinationEntity="Product" inverseName="loanRecords" inverseEntity="Product"/>
    </entity>
    <entity name="Message" representedClassName="Message" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="text" attributeType="String"/>
        <attribute name="timestamp" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isFromUser" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="role" attributeType="String"/>
        <attribute name="analysisContextData" optional="YES" attributeType="Binary"/>
        <attribute name="attachmentsData" optional="YES" attributeType="Binary"/>
        <relationship name="conversation" maxCount="1" deletionRule="Nullify" destinationEntity="Conversation" inverseName="messages" inverseEntity="Conversation"/>
    </entity>
    <entity name="Product" representedClassName="Product" syncable="YES" codeGenerationType="class">
        <attribute name="brand" optional="YES" attributeType="String"/>
        <attribute name="expectedLifespan" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="expectedUsageFrequency" optional="YES" attributeType="String"/>
        <attribute name="expiryDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="images" optional="YES" attributeType="Binary"/>
        <attribute name="initialSatisfaction" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isVirtualProduct" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="model" optional="YES" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="price" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="purchaseChannel" optional="YES" attributeType="String"/>
        <attribute name="purchaseDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="purchaseMotivation" optional="YES" attributeType="String"/>
        <attribute name="purchaseNotes" optional="YES" attributeType="String"/>
        <attribute name="quantity" optional="YES" attributeType="Integer 16" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="isConsumable" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="currentQuantity" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="unitType" optional="YES" attributeType="String"/>
        <attribute name="minStockAlert" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="consumptionRate" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="consumptionUnitType" optional="YES" attributeType="String"/>
        <attribute name="unitConversionRatio" optional="YES" attributeType="Double" defaultValueString="1.0" usesScalarValueType="YES"/>
        <attribute name="valuationMethod" optional="YES" attributeType="String" defaultValueString="usage"/>
        <attribute name="warrantyDetails" optional="YES" attributeType="String"/>
        <attribute name="warrantyEndDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="warrantyImage" optional="YES" attributeType="String"/>
        <attribute name="subscriptionReminderDays" optional="YES" attributeType="Integer 16" defaultValueString="7" usesScalarValueType="YES"/>
        <attribute name="lastReminderDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="reminderEnabled" optional="YES" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="autoRenewal" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="category" maxCount="1" deletionRule="Nullify" destinationEntity="Category" inverseName="products" inverseEntity="Category"/>
        <relationship name="incomingLinks" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="ProductLink" inverseName="targetProduct" inverseEntity="ProductLink"/>
        <relationship name="loanRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="LoanRecord" inverseName="product" inverseEntity="LoanRecord"/>
        <relationship name="outgoingLinks" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="ProductLink" inverseName="sourceProduct" inverseEntity="ProductLink"/>
        <relationship name="parentProducts" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="relatedProducts" inverseEntity="Product"/>
        <relationship name="purchaseChannelRelation" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="PurchaseChannel" inverseName="products" inverseEntity="PurchaseChannel"/>
        <relationship name="relatedExpenses" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="RelatedExpense" inverseName="product" inverseEntity="RelatedExpense"/>
        <relationship name="relatedProducts" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="parentProducts" inverseEntity="Product"/>
        <relationship name="reminders" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Reminder" inverseName="product" inverseEntity="Reminder"/>
        <relationship name="tags" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Tag" inverseName="products" inverseEntity="Tag"/>
        <relationship name="usageRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="UsageRecord" inverseName="product" inverseEntity="UsageRecord"/>
    </entity>
    <entity name="ProductLink" representedClassName="ProductLink" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="groupID" optional="YES" attributeType="String"/>
        <attribute name="groupName" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isBidirectional" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="lastUsedTogether" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="relationshipType" attributeType="String" defaultValueString="other"/>
        <attribute name="strength" attributeType="Integer 16" defaultValueString="3" usesScalarValueType="YES"/>
        <attribute name="usageCount" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="usageScenario" optional="YES" attributeType="String"/>
        <relationship name="sourceProduct" maxCount="1" deletionRule="Nullify" destinationEntity="Product" inverseName="outgoingLinks" inverseEntity="Product"/>
        <relationship name="targetProduct" maxCount="1" deletionRule="Nullify" destinationEntity="Product" inverseName="incomingLinks" inverseEntity="Product"/>
    </entity>
    <entity name="PurchaseChannel" representedClassName="PurchaseChannel" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="location" optional="YES" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="url" optional="YES" attributeType="String"/>
        <attribute name="usageCount" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="category" maxCount="1" deletionRule="Nullify" destinationEntity="PurchaseChannelCategory" inverseName="channels" inverseEntity="PurchaseChannelCategory"/>
        <relationship name="products" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="purchaseChannelRelation" inverseEntity="Product"/>
    </entity>
    <entity name="PurchaseChannelCategory" representedClassName="PurchaseChannelCategory" syncable="YES" codeGenerationType="class">
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <relationship name="channels" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="PurchaseChannel" inverseName="category" inverseEntity="PurchaseChannel"/>
    </entity>
    <entity name="RelatedExpense" representedClassName="RelatedExpense" syncable="YES" codeGenerationType="class">
        <attribute name="amount" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <relationship name="product" maxCount="1" deletionRule="Nullify" destinationEntity="Product" inverseName="relatedExpenses" inverseEntity="Product"/>
        <relationship name="type" maxCount="1" deletionRule="Nullify" destinationEntity="ExpenseType" inverseName="expenses" inverseEntity="ExpenseType"/>
    </entity>
    <entity name="Reminder" representedClassName="Reminder" syncable="YES" codeGenerationType="class">
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isActive" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="message" attributeType="String"/>
        <attribute name="reminderType" attributeType="String"/>
        <relationship name="product" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="reminders" inverseEntity="Product"/>
    </entity>
    <entity name="Tag" representedClassName="Tag" syncable="YES" codeGenerationType="class">
        <attribute name="color" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="type" optional="YES" attributeType="String"/>
        <relationship name="products" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Product" inverseName="tags" inverseEntity="Product"/>
    </entity>
    <entity name="UsageRecord" representedClassName="UsageRecord" syncable="YES" codeGenerationType="class">
        <attribute name="audioRecordings" optional="YES" attributeType="Binary"/>
        <attribute name="borrowerName" optional="YES" attributeType="String"/>
        <attribute name="contactInfo" optional="YES" attributeType="String"/>
        <attribute name="date" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="dueDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="duration" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="emotionalValue" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="images" optional="YES" attributeType="Binary"/>
        <attribute name="isLoanedOut" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isStory" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="memories" optional="YES" attributeType="String"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="recipient" optional="YES" attributeType="String"/>
        <attribute name="returnDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="satisfaction" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="scenario" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="transferPrice" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="transferTypeString" optional="YES" attributeType="String"/>
        <attribute name="usageType" attributeType="String" defaultValueString="personal"/>
        <attribute name="consumedQuantity" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="remainingQuantity" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="consumptionUnit" optional="YES" attributeType="String"/>
        <attribute name="wearCondition" optional="YES" attributeType="String"/>
        <relationship name="product" maxCount="1" deletionRule="Nullify" destinationEntity="Product" inverseName="usageRecords" inverseEntity="Product"/>
    </entity>
</model>