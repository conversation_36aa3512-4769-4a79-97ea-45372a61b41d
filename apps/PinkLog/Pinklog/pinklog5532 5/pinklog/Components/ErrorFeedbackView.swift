import SwiftUI

// MARK: - 错误反馈视图

struct ErrorFeedbackView: View {
    let error: BackupError
    let onRetry: (() -> Void)?
    let onDismiss: () -> Void
    
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var errorHandler = BackupErrorHandler.shared
    
    @State private var showingDetails = false
    @State private var showingDiagnostics = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 错误图标和标题
            errorHeaderSection
            
            // 错误描述
            errorDescriptionSection
            
            // 恢复建议
            recoverySuggestionSection
            
            // 操作按钮
            actionButtonsSection
            
            // 详细信息（可展开）
            if showingDetails {
                errorDetailsSection
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
    
    // MARK: - 错误头部区域
    
    private var errorHeaderSection: some View {
        VStack(spacing: 12) {
            // 错误图标
            Image(systemName: errorIcon)
                .font(.system(size: 48))
                .foregroundColor(errorColor)
            
            // 错误标题
            Text(errorTitle)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 错误描述区域
    
    private var errorDescriptionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(error.errorDescription ?? "发生了未知错误")
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            // 严重程度标签
            HStack {
                Text("严重程度:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(error.severity.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(severityColor.opacity(0.2))
                    .foregroundColor(severityColor)
                    .cornerRadius(8)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 恢复建议区域
    
    private var recoverySuggestionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "lightbulb")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                
                Text("解决建议")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            Text(error.recoverySuggestion ?? "请重试或联系技术支持")
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(Color(.systemBlue).opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemBlue), lineWidth: 1)
        )
    }
    
    // MARK: - 操作按钮区域
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // 重试按钮
                if error.isRetryable, let onRetry = onRetry {
                    Button(action: onRetry) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("重试")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(themeManager.currentTheme.primaryColor)
                        .cornerRadius(12)
                    }
                    .disabled(errorHandler.isRetrying)
                }
                
                // 关闭按钮
                Button(action: onDismiss) {
                    HStack {
                        Image(systemName: "xmark")
                        Text("关闭")
                    }
                    .font(.headline)
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray5))
                    .cornerRadius(12)
                }
            }
            
            // 详细信息和诊断按钮
            HStack(spacing: 12) {
                Button(action: {
                    showingDetails.toggle()
                }) {
                    HStack {
                        Image(systemName: showingDetails ? "chevron.up" : "chevron.down")
                        Text("详细信息")
                    }
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                }
                
                Spacer()
                
                Button(action: {
                    showingDiagnostics = true
                }) {
                    HStack {
                        Image(systemName: "stethoscope")
                        Text("系统诊断")
                    }
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                }
            }
        }
        .sheet(isPresented: $showingDiagnostics) {
            SystemDiagnosticsView()
                .environmentObject(themeManager)
        }
    }
    
    // MARK: - 错误详细信息区域
    
    private var errorDetailsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("技术详情")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: 8) {
                ErrorDetailRow(title: "错误类型", value: String(describing: error))
                ErrorDetailRow(title: "可重试", value: error.isRetryable ? "是" : "否")
                ErrorDetailRow(title: "重试次数", value: "\(errorHandler.retryCount)/3")
                ErrorDetailRow(title: "时间", value: Date().formatted(date: .abbreviated, time: .shortened))
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .transition(.opacity.combined(with: .scale))
        .animation(.easeInOut(duration: 0.3), value: showingDetails)
    }
    
    // MARK: - 计算属性
    
    private var errorIcon: String {
        switch error.severity {
        case .info:
            return "info.circle"
        case .warning:
            return "exclamationmark.triangle"
        case .error:
            return "xmark.circle"
        case .critical:
            return "exclamationmark.octagon"
        }
    }
    
    private var errorColor: Color {
        switch error.severity {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
    
    private var severityColor: Color {
        switch error.severity {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
    
    private var errorTitle: String {
        switch error.severity {
        case .info:
            return "提示信息"
        case .warning:
            return "警告"
        case .error:
            return "操作失败"
        case .critical:
            return "严重错误"
        }
    }
}

// MARK: - 详情行组件

struct ErrorDetailRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - Toast错误提示

struct ErrorToast: View {
    let error: BackupError
    let onDismiss: () -> Void
    
    @EnvironmentObject var themeManager: ThemeManager
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.title3)
                .foregroundColor(.white)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("备份错误")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(error.errorDescription ?? "未知错误")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.9))
                    .lineLimit(2)
            }
            
            Spacer()
            
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.caption)
                    .foregroundColor(.white)
            }
        }
        .padding()
        .background(Color(.systemRed))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                isVisible = true
            }
            
            // 3秒后自动消失
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                withAnimation(.easeOut(duration: 0.3)) {
                    isVisible = false
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    onDismiss()
                }
            }
        }
    }
}

// MARK: - 系统诊断视图

struct SystemDiagnosticsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var networkMonitor: NetworkMonitor = {
        return NetworkMonitor.shared
    }()
    @StateObject private var errorHandler = BackupErrorHandler.shared
    
    @State private var diagnosticResults: [DiagnosticResult] = []
    @State private var isRunningDiagnostics = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 诊断状态
                    diagnosticStatusSection
                    
                    // 诊断结果
                    diagnosticResultsSection
                    
                    // 错误统计
                    errorStatisticsSection
                }
                .padding()
            }
            .navigationTitle("系统诊断")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("重新诊断") {
                        runDiagnostics()
                    }
                    .disabled(isRunningDiagnostics)
                }
            }
            .onAppear {
                runDiagnostics()
            }
        }
    }
    
    private var diagnosticStatusSection: some View {
        VStack(spacing: 12) {
            if isRunningDiagnostics {
                ProgressView("正在运行系统诊断...")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                    
                    Text("诊断完成")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
    }
    
    private var diagnosticResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("诊断结果")
                .font(.headline)
                .foregroundColor(.primary)
            
            ForEach(diagnosticResults) { result in
                DiagnosticResultRow(result: result)
            }
        }
    }
    
    private var errorStatisticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("错误统计")
                .font(.headline)
                .foregroundColor(.primary)
            
            let statistics = errorHandler.getErrorStatistics()
            
            VStack(spacing: 8) {
                ErrorDetailRow(title: "总错误数", value: "\(statistics.totalErrors)")
                ErrorDetailRow(title: "24小时内错误", value: "\(statistics.recentErrors)")

                if let lastError = statistics.lastErrorTime {
                    ErrorDetailRow(title: "最后错误时间", value: lastError.formatted(date: .abbreviated, time: .shortened))
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    private func runDiagnostics() {
        isRunningDiagnostics = true
        diagnosticResults.removeAll()
        
        Task {
            // 模拟诊断过程
            try? await Task.sleep(nanoseconds: 1_000_000_000)
            
            await MainActor.run {
                diagnosticResults = [
                    DiagnosticResult(
                        name: "网络连接",
                        status: networkMonitor.isConnected ? .passed : .failed,
                        details: networkMonitor.statusDescription
                    ),
                    DiagnosticResult(
                        name: "iCloud状态",
                        status: CloudKitBackupService.shared.isAvailable ? .passed : .failed,
                        details: "iCloud服务可用性检查"
                    ),
                    DiagnosticResult(
                        name: "存储空间",
                        status: getAvailableStorage() > 100 * 1024 * 1024 ? .passed : .warning,
                        details: "可用存储空间: \(formatBytes(getAvailableStorage()))"
                    ),
                    DiagnosticResult(
                        name: "应用权限",
                        status: .passed,
                        details: "所有必要权限已授予"
                    )
                ]
                
                isRunningDiagnostics = false
            }
        }
    }
    
    private func getAvailableStorage() -> Int64 {
        do {
            let fileURL = URL(fileURLWithPath: NSHomeDirectory())
            let values = try fileURL.resourceValues(forKeys: [.volumeAvailableCapacityForImportantUsageKey])
            return values.volumeAvailableCapacityForImportantUsage ?? 0
        } catch {
            return 0
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - 诊断结果

struct DiagnosticResult: Identifiable {
    let id = UUID()
    let name: String
    let status: DiagnosticStatus
    let details: String
}

enum DiagnosticStatus {
    case passed
    case warning
    case failed
    
    var icon: String {
        switch self {
        case .passed: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .failed: return "xmark.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .passed: return .green
        case .warning: return .orange
        case .failed: return .red
        }
    }
}

struct DiagnosticResultRow: View {
    let result: DiagnosticResult
    
    var body: some View {
        HStack {
            Image(systemName: result.status.icon)
                .font(.title3)
                .foregroundColor(result.status.color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(result.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(result.details)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    ErrorFeedbackView(
        error: .networkUnavailable,
        onRetry: {},
        onDismiss: {}
    )
    .environmentObject(ThemeManager())
}
