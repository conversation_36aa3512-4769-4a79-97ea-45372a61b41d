//
//  ConsumableManagementView.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI
import Charts

struct ConsumableManagementView: View {
    @StateObject private var viewModel = ConsumableViewModel()
    @State private var showingAddConsumable = false
    @State private var selectedProduct: Product?
    @State private var showingConsumptionSheet = false
    @State private var showingReplenishSheet = false
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 库存管理标签页
            stockManagementTab
                .tabItem {
                    Image(systemName: "cube.box")
                    Text("库存管理")
                }
                .tag(0)
            
            // 生命周期管理标签页
            ConsumableLifecycleView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("生命周期")
                }
                .tag(1)
        }
        .sheet(isPresented: $showingAddConsumable) {
            AddConsumableView()
        }
        .sheet(item: $selectedProduct) { product in
            if showingConsumptionSheet {
                ConsumptionRecordSheet(product: product, viewModel: viewModel)
            } else if showingReplenishSheet {
                ReplenishStockSheet(product: product, viewModel: viewModel)
            }
        }
    }
    
    // MARK: - 库存管理标签页
    
    private var stockManagementTab: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // 生命周期状态概览
                    lifecycleOverviewSection
                    
                    // 低库存警告区域
                    if !viewModel.lowStockProducts.isEmpty {
                        lowStockSection
                    }
                    
                    // 所有消耗品列表
                    consumableProductsSection
                }
                .padding()
            }
            .navigationTitle("消耗品管理")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加消耗品") {
                        showingAddConsumable = true
                    }
                }
            }
            .refreshable {
                viewModel.loadConsumableProducts()
            }
        }
        .onAppear {
            viewModel.loadConsumableProducts()
        }
    }
    
    // MARK: - 生命周期状态概览
    
    private var lifecycleOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 简洁标题
            HStack {
                Text("库存概览")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("详情") {
                    selectedTab = 1
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            // 核心数据卡片
            summaryCardsSection
            
            // 状态分布
            statusOverviewSection
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }
    
    // 核心数据卡片
    private var summaryCardsSection: some View {
        HStack(spacing: 12) {
            // 总数卡片
            summaryCard(
                title: "总计",
                value: "\(viewModel.consumableProducts.count)",
                icon: "cube.box",
                color: .blue
            )
            
            // 活跃卡片
            summaryCard(
                title: "活跃",
                value: "\(viewModel.activeProducts.count)",
                icon: "checkmark.circle",
                color: .green
            )
            
            // 需要关注卡片
            let attentionCount = viewModel.depletedProducts.count + viewModel.criticalProducts.count + viewModel.lowStockProductsLifecycle.count + viewModel.expiredProducts.count
            summaryCard(
                title: "关注",
                value: "\(attentionCount)",
                icon: "exclamationmark.triangle",
                color: attentionCount > 0 ? .orange : .gray
            )
        }
    }
    
    // 状态概览
    private var statusOverviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("状态分布")
                .font(.headline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8) {
                statusMiniCard(.depleted, count: viewModel.depletedProducts.count)
                statusMiniCard(.critical, count: viewModel.criticalProducts.count)
                statusMiniCard(.lowStock, count: viewModel.lowStockProductsLifecycle.count)
                statusMiniCard(.expired, count: viewModel.expiredProducts.count)
            }
        }
    }
    
    // 简洁的数据卡片
    private func summaryCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // 状态迷你卡片
    private func statusMiniCard(_ status: Product.LifecycleStatus, count: Int) -> some View {
        HStack(spacing: 8) {
            Image(systemName: status.icon)
                .font(.subheadline)
                .foregroundColor(status.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("\(count)")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(status.rawValue)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(6)
        .shadow(color: .black.opacity(0.03), radius: 1, x: 0, y: 1)
    }
    

    
    // 获取状态副标题
    private func getStatusSubtitle(_ status: Product.LifecycleStatus) -> String {
        switch status {
        case .depleted:
            return "需要立即补充库存"
        case .critical:
            return "库存即将耗尽"
        case .lowStock:
            return "建议及时补充"
        case .expired:
            return "已超过有效期"
        case .discontinued:
            return "已停止使用"
        case .active:
            return "正常使用中"
        default:
            return status.description
        }
    }
    
    // MARK: - 低库存警告区域
    
    private var lowStockSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("低库存警告")
                    .font(.headline)
                    .foregroundColor(.orange)
                Spacer()
            }
            
            LazyVStack(spacing: 8) {
                ForEach(viewModel.lowStockProducts, id: \.id) { product in
                    LowStockWarningCard(product: product) {
                        selectedProduct = product
                        showingReplenishSheet = true
                    }
                }
            }
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 消耗品列表区域
    
    private var consumableProductsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("我的消耗品")
                    .font(.headline)
                Spacer()
                Text("\(viewModel.consumableProducts.count) 个")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if viewModel.consumableProducts.isEmpty {
                EmptyConsumableView {
                    showingAddConsumable = true
                }
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.consumableProducts, id: \.id) { product in
                        ConsumableProductCard(
                            product: product,
                            onConsume: {
                                selectedProduct = product
                                showingConsumptionSheet = true
                            },
                            onReplenish: {
                                selectedProduct = product
                                showingReplenishSheet = true
                            },
                            onReactivate: {
                                viewModel.reactivateProduct(product)
                            },
                            onUnarchive: {
                                viewModel.unarchiveProduct(product)
                            }
                        )
                    }
                }
            }
        }
    }
}

// MARK: - 低库存警告卡片

struct LowStockWarningCard: View {
    let product: Product
    let onReplenish: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name ?? "未知产品")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Image(systemName: product.stockStatus.icon)
                        .foregroundColor(product.stockStatus.color)
                    Text("剩余 \(formatQuantity(product.actualCurrentQuantity))\(product.unitType ?? "")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button("补充") {
                onReplenish()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.small)
        }
        .padding(12)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
}

// MARK: - 消耗品产品卡片

struct ConsumableProductCard: View {
    let product: Product
    let onConsume: () -> Void
    let onReplenish: () -> Void
    let onReactivate: (() -> Void)?
    let onUnarchive: (() -> Void)?
    
    init(product: Product, onConsume: @escaping () -> Void, onReplenish: @escaping () -> Void, onReactivate: (() -> Void)? = nil, onUnarchive: (() -> Void)? = nil) {
        self.product = product
        self.onConsume = onConsume
        self.onReplenish = onReplenish
        self.onReactivate = onReactivate
        self.onUnarchive = onUnarchive
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // 产品信息头部
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未知产品")
                        .font(.headline)
                    
                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(formatQuantity(product.actualCurrentQuantity))\(product.unitType ?? "")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(product.lifecycleStatus == .depleted ? .secondary : .primary)
                }
            }
            
            // 库存滑杆显示
            VStack(spacing: 6) {
                HStack {
                    Text("剩余量")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(formatQuantity(product.actualCurrentQuantity) + (product.unitType ?? ""))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(product.lifecycleStatus.color)
                }
                
                // 滑杆式库存显示
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景轨道
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 16)
                        
                        // 库存进度
                        RoundedRectangle(cornerRadius: 8)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        product.lifecycleStatus.color,
                                        product.lifecycleStatus.color.opacity(0.8)
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: max(16, geometry.size.width * product.stockPercentage), height: 16)
                        
                        // 百分比标签
                        HStack {
                            Spacer()
                            Text("\(Int(product.stockPercentage * 100))%")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.trailing, 6)
                        }
                        .frame(width: max(16, geometry.size.width * product.stockPercentage), height: 16)
                    }
                }
                .frame(height: 16)
            }
            
            // 状态信息（合并显示，避免重复）
            HStack {
                Image(systemName: product.lifecycleStatus.icon)
                    .font(.caption)
                    .foregroundColor(product.lifecycleStatus.color)
                
                Text(product.lifecycleStatus.description)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(product.lifecycleStatus.color)
                
                Spacer()
                
                // 预计用完时间
                if let estimatedDays = product.estimatedDaysUntilEmpty, estimatedDays > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text("\(estimatedDays)天")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // 操作按钮
            HStack(spacing: 12) {
                switch product.lifecycleStatus {
                case .active, .lowStock, .critical:
                    Button("记录使用") {
                        onConsume()
                    }
                    .buttonStyle(.bordered)
                    .frame(maxWidth: .infinity)
                    
                    Button("补充库存") {
                        onReplenish()
                    }
                    .buttonStyle(.borderedProminent)
                    .frame(maxWidth: .infinity)
                    
                case .depleted:
                    Button("补充库存") {
                        onReplenish()
                    }
                    .buttonStyle(.borderedProminent)
                    .frame(maxWidth: .infinity)
                    
                case .expired, .discontinued:
                     Button("重新激活") {
                         onReactivate?()
                     }
                     .buttonStyle(.bordered)
                     .frame(maxWidth: .infinity)
                     .disabled(onReactivate == nil)
                     
                     Button("补充库存") {
                         onReplenish()
                     }
                     .buttonStyle(.borderedProminent)
                     .frame(maxWidth: .infinity)
                     
                 case .archived:
                     Button("取消归档") {
                         onUnarchive?()
                     }
                     .buttonStyle(.bordered)
                     .frame(maxWidth: .infinity)
                     .disabled(onUnarchive == nil)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
}

// MARK: - 空状态视图

struct EmptyConsumableView: View {
    let onAddConsumable: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "cube.box")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("还没有消耗品")
                    .font(.headline)
                
                Text("添加您的第一个消耗品，开始智能库存管理")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("添加消耗品") {
                onAddConsumable()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding(32)
        .frame(maxWidth: .infinity)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    ConsumableManagementView()
}