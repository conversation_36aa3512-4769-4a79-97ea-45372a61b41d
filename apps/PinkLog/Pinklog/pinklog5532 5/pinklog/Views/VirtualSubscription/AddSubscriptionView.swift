//
//  AddSubscriptionView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 添加订阅界面
//

import SwiftUI
import CoreData

struct AddSubscriptionView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    
    @StateObject private var subscriptionService: SubscriptionService
    
    // 表单数据
    @State private var name: String = ""
    @State private var serviceProvider: String = ""
    @State private var billingCycle: BillingCycle = .monthly
    @State private var costPerCycle: String = ""
    @State private var currency: String = "CNY"
    @State private var startDate: Date = Date()
    @State private var status: SubscriptionStatus = .active
    @State private var description: String = ""
    @State private var category: String = ""
    @State private var customCycleDays: String = ""
    @State private var isShared: Bool = false
    @State private var familyMemberCount: Int = 1
    @State private var notes: String = ""
    
    // UI状态
    @State private var showingCustomCycleDays: Bool = false
    @State private var isLoading: Bool = false
    @State private var showingError: Bool = false
    @State private var errorMessage: String = ""
    
    // 预设分类
    private let predefinedCategories = ["娱乐", "工作", "学习", "健康", "音乐", "视频", "办公", "生产力", "游戏", "新闻"]
    
    init(context: NSManagedObjectContext, coreDataRepository: CoreDataRepository<Product>) {
        self._subscriptionService = StateObject(wrappedValue: SubscriptionService(context: context, coreDataRepository: coreDataRepository))
    }
    
    var body: some View {
        NavigationView {
            Form {
                basicInfoSection
                billingSection
                sharingSection
                additionalInfoSection
            }
            .navigationTitle("添加订阅")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveSubscription()
                    }
                    .disabled(isLoading || name.isEmpty || serviceProvider.isEmpty || costPerCycle.isEmpty)
                }
            }
            .alert("错误", isPresented: $showingError) {
                Button("确定") { }
            } message: {
                Text(errorMessage)
            }
        }
        .onChange(of: billingCycle) { newValue in
            showingCustomCycleDays = newValue == .custom
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            HStack {
                Image(systemName: "app.fill")
                    .foregroundColor(.blue)
                    .frame(width: 20)
                TextField("订阅名称", text: $name)
            }
            
            HStack {
                Image(systemName: "building.2.fill")
                    .foregroundColor(.green)
                    .frame(width: 20)
                TextField("服务提供商", text: $serviceProvider)
            }
            
            HStack {
                Image(systemName: "folder.fill")
                    .foregroundColor(.orange)
                    .frame(width: 20)
                
                Menu {
                    ForEach(predefinedCategories, id: \.self) { cat in
                        Button(cat) {
                            category = cat
                        }
                    }
                    Divider()
                    Button("自定义") {
                        // 允许用户输入自定义分类
                    }
                } label: {
                    HStack {
                        Text(category.isEmpty ? "选择分类" : category)
                            .foregroundColor(category.isEmpty ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                }
            }
            
            if category.isEmpty || !predefinedCategories.contains(category) {
                HStack {
                    Image(systemName: "pencil")
                        .foregroundColor(.purple)
                        .frame(width: 20)
                    TextField("自定义分类", text: $category)
                }
            }
            
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.red)
                    .frame(width: 20)
                DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
            }
            
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(status.color)
                    .frame(width: 20)
                
                Picker("状态", selection: $status) {
                    ForEach(SubscriptionStatus.allCases) { status in
                        Label(status.displayName, systemImage: status.icon)
                            .tag(status)
                    }
                }
                .pickerStyle(.menu)
            }
        }
    }
    
    // MARK: - 计费信息部分
    private var billingSection: some View {
        Section("计费信息") {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                Picker("计费周期", selection: $billingCycle) {
                    ForEach(BillingCycle.allCases) { cycle in
                        Label(cycle.displayName, systemImage: cycle.icon)
                            .tag(cycle)
                    }
                }
                .pickerStyle(.menu)
            }
            
            if showingCustomCycleDays {
                HStack {
                    Image(systemName: "number")
                        .foregroundColor(.orange)
                        .frame(width: 20)
                    TextField("自定义天数", text: $customCycleDays)
                        .keyboardType(.numberPad)
                }
            }
            
            HStack {
                Image(systemName: "dollarsign.circle.fill")
                    .foregroundColor(.green)
                    .frame(width: 20)
                TextField("每周期费用", text: $costPerCycle)
                    .keyboardType(.decimalPad)
                Text(currency)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Image(systemName: "banknote")
                    .foregroundColor(.yellow)
                    .frame(width: 20)
                
                Picker("货币", selection: $currency) {
                    Text("人民币 (CNY)").tag("CNY")
                    Text("美元 (USD)").tag("USD")
                    Text("欧元 (EUR)").tag("EUR")
                    Text("日元 (JPY)").tag("JPY")
                }
                .pickerStyle(.menu)
            }
            
            // 显示计算出的费用信息
            if let cost = Double(costPerCycle), cost > 0 {
                VStack(alignment: .leading, spacing: 4) {
                    let subscriptionData = SubscriptionData(
                        name: name,
                        serviceProvider: serviceProvider,
                        billingCycle: billingCycle,
                        costPerCycle: cost,
                        currency: currency,
                        customCycleDays: Int(customCycleDays)
                    )
                    
                    HStack {
                        Image(systemName: "calendar.badge.clock")
                            .foregroundColor(.blue)
                            .frame(width: 20)
                        Text("月费用: ¥\(String(format: "%.2f", subscriptionData.monthlyCost))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Image(systemName: "calendar.circle")
                            .foregroundColor(.purple)
                            .frame(width: 20)
                        Text("年费用: ¥\(String(format: "%.2f", subscriptionData.annualCost))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 4)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
    }
    
    // MARK: - 共享信息部分
    private var sharingSection: some View {
        Section("共享设置") {
            Toggle(isOn: $isShared) {
                HStack {
                    Image(systemName: "person.2.fill")
                        .foregroundColor(.green)
                        .frame(width: 20)
                    Text("家庭共享")
                }
            }
            
            if isShared {
                HStack {
                    Image(systemName: "person.3.fill")
                        .foregroundColor(.blue)
                        .frame(width: 20)
                    Stepper("家庭成员: \(familyMemberCount)人", value: $familyMemberCount, in: 1...10)
                }
                
                if let cost = Double(costPerCycle), cost > 0 {
                    HStack {
                        Image(systemName: "person.crop.circle.badge.dollarsign")
                            .foregroundColor(.orange)
                            .frame(width: 20)
                        Text("人均费用: ¥\(String(format: "%.2f", cost / Double(familyMemberCount)))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 附加信息部分
    private var additionalInfoSection: some View {
        Section("附加信息") {
            HStack(alignment: .top) {
                Image(systemName: "text.alignleft")
                    .foregroundColor(.gray)
                    .frame(width: 20)
                    .padding(.top, 8)
                
                VStack(alignment: .leading) {
                    Text("描述")
                        .font(.headline)
                    TextEditor(text: $description)
                        .frame(minHeight: 60)
                }
            }
            
            HStack(alignment: .top) {
                Image(systemName: "note.text")
                    .foregroundColor(.yellow)
                    .frame(width: 20)
                    .padding(.top, 8)
                
                VStack(alignment: .leading) {
                    Text("备注")
                        .font(.headline)
                    TextEditor(text: $notes)
                        .frame(minHeight: 60)
                }
            }
        }
    }
    
    // MARK: - 保存订阅
    private func saveSubscription() {
        guard !name.isEmpty,
              !serviceProvider.isEmpty,
              let cost = Double(costPerCycle),
              cost > 0 else {
            errorMessage = "请填写完整的订阅信息"
            showingError = true
            return
        }
        
        isLoading = true
        
        let customDays = billingCycle == .custom ? Int(customCycleDays) : nil
        
        let subscriptionData = SubscriptionData(
            name: name,
            serviceProvider: serviceProvider,
            billingCycle: billingCycle,
            costPerCycle: cost,
            currency: currency,
            startDate: startDate,
            status: status,
            description: description.isEmpty ? nil : description,
            category: category.isEmpty ? nil : category,
            customCycleDays: customDays,
            isShared: isShared,
            familyMemberCount: familyMemberCount,
            notes: notes.isEmpty ? nil : notes
        )
        
        Task {
            do {
                _ = try await subscriptionService.createSubscription(subscriptionData)
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "保存订阅失败: \(error.localizedDescription)"
                    showingError = true
                }
            }
        }
    }
}

// MARK: - 预览
struct AddSubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        
        AddSubscriptionView(context: context, coreDataRepository: repository)
    }
}