//
//  AddUsageSessionView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 添加使用会话界面
//

import SwiftUI
import CoreData

struct AddUsageSessionView: View {
    let subscription: Product
    let subscriptionService: SubscriptionService
    
    @Environment(\.dismiss) private var dismiss
    
    // 使用会话信息
    @State private var sessionType: UsageSessionType = .active
    @State private var startTime: Date = Date()
    @State private var endTime: Date = Date()
    @State private var duration: TimeInterval = 0
    @State private var useDuration: Bool = false
    @State private var activityDescription: String = ""
    @State private var satisfactionRating: Int = 5
    @State private var emotionalValue: Int = 3
    @State private var notes: String = ""
    
    // UI状态
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingDurationPicker = false
    
    private var isValidSession: Bool {
        if useDuration {
            return duration > 0 && !activityDescription.isEmpty
        } else {
            return startTime < endTime && !activityDescription.isEmpty
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息部分
                basicInfoSection
                
                // 时间设置部分
                timeSettingsSection
                
                // 评价部分
                ratingSection
                
                // 备注部分
                notesSection
            }
            .navigationTitle("记录使用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        Task {
                            await saveUsageSession()
                        }
                    }
                    .disabled(!isValidSession || isLoading)
                }
            }
            .sheet(isPresented: $showingDurationPicker) {
                DurationPickerView(duration: $duration)
            }
            .onAppear {
                setupDefaultValues()
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("使用信息") {
            HStack {
                Text("订阅服务")
                Spacer()
                Text(subscription.name ?? "未知服务")
                    .foregroundColor(.secondary)
            }
            
            Picker("使用类型", selection: $sessionType) {
                ForEach(UsageSessionType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("活动描述")
                    .foregroundColor(.secondary)
                
                TextField(sessionType.placeholder, text: $activityDescription)
                    .textFieldStyle(.roundedBorder)
            }
        }
    }
    
    // MARK: - 时间设置部分
    private var timeSettingsSection: some View {
        Section("时间设置") {
            Toggle("直接输入时长", isOn: $useDuration)
            
            if useDuration {
                HStack {
                    Text("使用时长")
                    Spacer()
                    Button(formatDuration(duration)) {
                        showingDurationPicker = true
                    }
                    .foregroundColor(.blue)
                }
            } else {
                DatePicker("开始时间", selection: $startTime, displayedComponents: [.date, .hourAndMinute])
                
                DatePicker("结束时间", selection: $endTime, in: startTime..., displayedComponents: [.date, .hourAndMinute])
                
                if startTime < endTime {
                    HStack {
                        Text("使用时长")
                        Spacer()
                        Text(formatDuration(endTime.timeIntervalSince(startTime)))
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 评价部分
    private var ratingSection: some View {
        Section("使用评价") {
            VStack(alignment: .leading, spacing: 12) {
                Text("满意度评分")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    ForEach(1...5, id: \.self) { rating in
                        Button {
                            satisfactionRating = rating
                        } label: {
                            Image(systemName: rating <= satisfactionRating ? "star.fill" : "star")
                                .foregroundColor(rating <= satisfactionRating ? .yellow : .gray)
                                .font(.title2)
                        }
                    }
                    
                    Spacer()
                    
                    Text(satisfactionRatingText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            VStack(alignment: .leading, spacing: 12) {
                Text("情感价值")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    ForEach(1...5, id: \.self) { value in
                        Button {
                            emotionalValue = value
                        } label: {
                            Image(systemName: value <= emotionalValue ? "heart.fill" : "heart")
                                .foregroundColor(value <= emotionalValue ? .red : .gray)
                                .font(.title2)
                        }
                    }
                    
                    Spacer()
                    
                    Text(emotionalValueText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - 备注部分
    private var notesSection: some View {
        Section {
            TextField("添加使用体验或备注...", text: $notes, axis: .vertical)
                .lineLimit(3...6)
        } header: {
            Text("备注")
        } footer: {
            Text("记录您的使用体验，有助于分析订阅价值。")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 计算属性
    private var satisfactionRatingText: String {
        switch satisfactionRating {
        case 1: return "很不满意"
        case 2: return "不满意"
        case 3: return "一般"
        case 4: return "满意"
        case 5: return "非常满意"
        default: return ""
        }
    }
    
    private var emotionalValueText: String {
        switch emotionalValue {
        case 1: return "无价值"
        case 2: return "价值较低"
        case 3: return "一般价值"
        case 4: return "价值较高"
        case 5: return "极高价值"
        default: return ""
        }
    }
    
    // MARK: - 辅助方法
    private func setupDefaultValues() {
        // 设置默认结束时间为1小时后
        endTime = Calendar.current.date(byAdding: .hour, value: 1, to: startTime) ?? Date()
        duration = 3600 // 1小时
        
        // 根据订阅类型设置默认活动描述
        if let category = subscription.category?.name {
            switch category.lowercased() {
            case "视频流媒体", "影视":
                activityDescription = "观看视频内容"
            case "音乐", "音频":
                activityDescription = "收听音乐"
            case "游戏":
                activityDescription = "游戏娱乐"
            case "学习", "教育":
                activityDescription = "在线学习"
            case "工具", "效率":
                activityDescription = "使用工具功能"
            default:
                activityDescription = "使用服务"
            }
        }
    }
    
    private func saveUsageSession() async {
        guard isValidSession else {
            errorMessage = "请填写完整的使用信息"
            return
        }
        
        isLoading = true
        
        let sessionDuration: TimeInterval
        let sessionStartTime: Date
        let sessionEndTime: Date
        
        if useDuration {
            sessionDuration = duration
            sessionStartTime = startTime
            sessionEndTime = startTime.addingTimeInterval(duration)
        } else {
            sessionDuration = endTime.timeIntervalSince(startTime)
            sessionStartTime = startTime
            sessionEndTime = endTime
        }
        
        let usageSession = UsageSession(
            subscriptionId: subscription.id ?? UUID(),
            startTime: sessionStartTime,
            duration: sessionDuration,
            sessionType: sessionType,
            activityDescription: activityDescription.isEmpty ? nil : activityDescription,
            satisfactionRating: satisfactionRating,
            emotionalValue: emotionalValue,
            notes: notes.isEmpty ? nil : notes
        )
        
        do {
            // 保存使用会话
            try await subscriptionService.recordUsageSession(usageSession)
            
            // 保存到CoreData
            try subscription.managedObjectContext?.save()
            
            await MainActor.run {
                isLoading = false
                dismiss()
            }
        } catch {
            await MainActor.run {
                isLoading = false
                errorMessage = "保存失败: \(error.localizedDescription)"
            }
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
}

// MARK: - 时长选择器
struct DurationPickerView: View {
    @Binding var duration: TimeInterval
    @Environment(\.dismiss) private var dismiss
    
    @State private var hours: Int = 0
    @State private var minutes: Int = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("选择使用时长")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                HStack(spacing: 20) {
                    // 小时选择器
                    VStack {
                        Text("小时")
                            .font(.headline)
                        
                        Picker("小时", selection: $hours) {
                            ForEach(0...23, id: \.self) { hour in
                                Text("\(hour)").tag(hour)
                            }
                        }
                        .pickerStyle(.wheel)
                        .frame(width: 100, height: 150)
                    }
                    
                    // 分钟选择器
                    VStack {
                        Text("分钟")
                            .font(.headline)
                        
                        Picker("分钟", selection: $minutes) {
                            ForEach(Array(stride(from: 0, through: 59, by: 5)), id: \.self) { minute in
                                Text("\(minute)").tag(minute)
                            }
                        }
                        .pickerStyle(.wheel)
                        .frame(width: 100, height: 150)
                    }
                }
                
                // 快速选择按钮
                VStack(spacing: 12) {
                    Text("快速选择")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                        QuickDurationButton(title: "15分钟", hours: 0, minutes: 15, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "30分钟", hours: 0, minutes: 30, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "1小时", hours: 1, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "1.5小时", hours: 1, minutes: 30, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "2小时", hours: 2, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "3小时", hours: 3, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("使用时长")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        duration = TimeInterval(hours * 3600 + minutes * 60)
                        dismiss()
                    }
                    .disabled(hours == 0 && minutes == 0)
                }
            }
            .onAppear {
                // 初始化当前时长
                hours = Int(duration) / 3600
                minutes = Int(duration) % 3600 / 60
            }
        }
    }
}

struct QuickDurationButton: View {
    let title: String
    let hours: Int
    let minutes: Int
    @Binding var selectedHours: Int
    @Binding var selectedMinutes: Int
    
    var body: some View {
        Button(title) {
            selectedHours = hours
            selectedMinutes = minutes
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.blue.opacity(0.1))
        .foregroundColor(.blue)
        .cornerRadius(8)
        .font(.caption)
    }
}

// MARK: - 扩展
extension UsageSessionType {
    var placeholder: String {
        switch self {
        case .active: return "描述您的主动使用活动..."
        case .background: return "后台运行的相关描述..."
        case .shared: return "共享使用的活动描述..."
        case .family: return "家庭成员使用的活动..."
        }
    }
}

// MARK: - 预览
struct AddUsageSessionView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        let service = SubscriptionService(context: context, coreDataRepository: repository)
        
        // 创建示例订阅
        let subscription = Product(context: context)
        subscription.name = "Netflix"
        subscription.brand = "Netflix Inc."
        subscription.isVirtualProduct = true
        
        return AddUsageSessionView(subscription: subscription, subscriptionService: service)
    }
}