//
//  SubscriptionDetailView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 订阅详情界面
//

import SwiftUI
import CoreData
import Charts

struct SubscriptionDetailView: View {
    let subscription: Product
    let subscriptionService: SubscriptionService
    
    @State private var showingEditView = false
    @State private var showingUsageSession = false
    @State private var showingFamilyStats = false
    @State private var showingReminderSettings = false
    @State private var selectedTimeRange: TimeRange = .month
    @State private var analytics: SubscriptionAnalytics?
    @State private var recommendations: [SubscriptionRecommendation] = []
    @State private var familySharingService: FamilySharingService?
    
    init(subscription: Product, subscriptionService: SubscriptionService) {
        self.subscription = subscription
        self.subscriptionService = subscriptionService
        
        // 初始化家庭共享服务
        if let context = subscription.managedObjectContext {
            self._familySharingService = State(initialValue: FamilySharingService(
                context: context,
                subscriptionService: subscriptionService
            ))
        }
    }
    
    enum TimeRange: String, CaseIterable {
        case week = "本周"
        case month = "本月"
        case quarter = "本季度"
        case year = "本年"
        
        var days: Int {
            switch self {
            case .week: return 7
            case .month: return 30
            case .quarter: return 90
            case .year: return 365
            }
        }
    }
    
    private var subscriptionData: SubscriptionData? {
        subscriptionService.extractSubscriptionData(from: subscription)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 订阅概览卡片
                subscriptionOverviewCard
                
                // 成本分析
                costAnalysisSection
                
                // 使用统计
                usageStatisticsSection
                
                // 家庭共享部分（如果是共享订阅）
                if let data = subscriptionData, data.isShared {
                    familySharingSection
                }
                
                // 值度分析
                valueAnalysisSection
                
                // 推荐建议
                recommendationsSection
                
                // 使用记录
                usageHistorySection
            }
            .padding()
        }
        .navigationTitle(subscription.name ?? "订阅详情")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button("编辑订阅") {
                        showingEditView = true
                    }
                    
                    Button("提醒设置") {
                        showingReminderSettings = true
                    }
                    
                    Button("记录使用") {
                        showingUsageSession = true
                    }
                    
                    if let data = subscriptionData, data.isShared {
                        Button("家庭使用统计") {
                            showingFamilyStats = true
                        }
                    }
                    
                    Divider()
                    
                    Button("分享订阅信息") {
                        shareSubscriptionInfo()
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditSubscriptionView(subscription: subscription, subscriptionService: subscriptionService)
        }
        .sheet(isPresented: $showingReminderSettings) {
            if let context = subscription.managedObjectContext {
                SubscriptionReminderSettingsView(product: subscription, context: context)
            }
        }
        .sheet(isPresented: $showingUsageSession) {
            AddUsageSessionView(subscription: subscription, subscriptionService: subscriptionService)
        }
        .sheet(isPresented: $showingFamilyStats) {
            if let familyService = familySharingService {
                FamilySharingStatsView(familySharingService: familyService)
            }
        }
        .onAppear {
            loadAnalytics()
            loadRecommendations()
        }
    }
    
    // MARK: - 订阅概览卡片
    private var subscriptionOverviewCard: some View {
        VStack(spacing: 16) {
            // 头部信息
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(subscription.name ?? "未知订阅")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(subscription.brand ?? "未知提供商")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // 状态标签
                    HStack(spacing: 4) {
                        Image(systemName: subscription.currentSubscriptionStatus.icon)
                            .font(.caption)
                        Text(subscription.currentSubscriptionStatus.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(subscription.currentSubscriptionStatus.color.opacity(0.2))
                    .foregroundColor(subscription.currentSubscriptionStatus.color)
                    .cornerRadius(8)
                }
                
                Spacer()
                
                // 值度指数
                VStack {
                    Text("\(Int(subscription.worthItIndex()))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(worthItIndexColor(subscription.worthItIndex()))
                    
                    Text("值度指数")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // 关键指标
            if let data = subscriptionData {
                HStack {
                    MetricView(
                        title: "月费用",
                        value: "¥\(String(format: "%.2f", data.monthlyCost))",
                        icon: "dollarsign.circle.fill",
                        color: .green
                    )
                    
                    Spacer()
                    
                    MetricView(
                        title: "计费周期",
                        value: data.billingCycle.displayName,
                        icon: "clock.fill",
                        color: .blue
                    )
                    
                    Spacer()
                    
                    MetricView(
                        title: "下次续费",
                        value: "\(subscription.subscriptionDaysRemaining ?? 0)天",
                        icon: "calendar.badge.clock",
                        color: subscription.isSubscriptionNearExpiry ? .red : .orange
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 成本分析部分
    private var costAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("成本分析")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let data = subscriptionData, let analytics = analytics {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    CostMetricCard(
                        title: "年度总成本",
                        value: "¥\(String(format: "%.2f", data.annualCost))",
                        subtitle: "包含所有费用",
                        color: .purple
                    )
                    
                    CostMetricCard(
                        title: "每次使用成本",
                        value: "¥\(String(format: "%.2f", analytics.costPerSession))",
                        subtitle: "基于使用频率",
                        color: .orange
                    )
                    
                    CostMetricCard(
                        title: "每小时成本",
                        value: "¥\(String(format: "%.2f", analytics.costPerHour))",
                        subtitle: "基于使用时长",
                        color: .blue
                    )
                    
                    if data.isShared {
                        CostMetricCard(
                            title: "人均成本",
                            value: "¥\(String(format: "%.2f", data.costPerPerson))",
                            subtitle: "\(data.familyMemberCount)人共享",
                            color: .green
                        )
                    } else {
                        CostMetricCard(
                            title: "使用效率",
                            value: "\(Int(subscription.subscriptionUsageEfficiency * 100))%",
                            subtitle: "相对预期使用",
                            color: .cyan
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 使用统计部分
    private var usageStatisticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("使用统计")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("时间范围", selection: $selectedTimeRange) {
                    ForEach(TimeRange.allCases, id: \.self) { range in
                        Text(range.rawValue).tag(range)
                    }
                }
                .pickerStyle(.segmented)
                .frame(width: 200)
            }
            
            if let analytics = analytics {
                VStack(spacing: 16) {
                    // 使用频率图表
                    usageFrequencyChart
                    
                    // 统计数据
                    HStack {
                        StatisticItem(
                            title: "总使用次数",
                            value: "\(analytics.totalUsageSessions)",
                            icon: "number.circle.fill",
                            color: .blue
                        )
                        
                        Spacer()
                        
                        StatisticItem(
                            title: "总使用时长",
                            value: formatDuration(analytics.totalUsageTime),
                            icon: "clock.fill",
                            color: .green
                        )
                        
                        Spacer()
                        
                        StatisticItem(
                            title: "平均时长",
                            value: formatDuration(analytics.averageSessionDuration),
                            icon: "timer",
                            color: .orange
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 家庭共享部分
    private var familySharingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("家庭共享")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("管理成员") {
                    if familySharingService != nil {
                        // 这里可以导航到家庭成员管理页面
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if let data = subscriptionData, data.isShared {
                VStack(spacing: 16) {
                    // 家庭概览
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("共享成员")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(data.familyMemberCount)人")
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("人均成本")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("¥\(String(format: "%.2f", data.costPerPerson))")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                        }
                    }
                    
                    // 家庭成员列表预览
                    if let familyService = familySharingService {
                        let members = Array(familyService.getFamilyMembers().prefix(3))
                        if !members.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("活跃成员")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                                
                                ForEach(members, id: \.id) { member in
                                    HStack {
                                        Circle()
                                            .fill(Color.blue.opacity(0.2))
                                            .frame(width: 32, height: 32)
                                            .overlay(
                                                Image(systemName: member.avatar ?? "person.fill")
                                                    .font(.caption)
                                                    .foregroundColor(.blue)
                                            )
                                        
                                        VStack(alignment: .leading, spacing: 2) {
                                            Text(member.displayName)
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                            
                                            Text("\(member.totalUsageSessions)次使用")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        
                                        Spacer()
                                        
                                        Image(systemName: member.role.icon)
                                            .font(.caption)
                                            .foregroundColor(member.role.color)
                                    }
                                }
                                
                                if familyService.getFamilyMembers().count > 3 {
                                    Button("查看全部成员") {
                                        showingFamilyStats = true
                                    }
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                }
                            }
                        }
                    }
                    
                    // 成本节省提示
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("家庭共享节省")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            let individualCost = data.monthlyCost * Double(data.familyMemberCount)
                            let savings = individualCost - data.monthlyCost
                            Text("每月节省 ¥\(String(format: "%.2f", savings))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color.green.opacity(0.05))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 值度分析部分
    private var valueAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("价值分析")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let analytics = analytics {
                VStack(spacing: 16) {
                    // 价值评分环形图
                    HStack {
                        VStack {
                            ZStack {
                                Circle()
                                    .stroke(Color(.systemGray5), lineWidth: 8)
                                    .frame(width: 100, height: 100)
                                
                                Circle()
                                    .trim(from: 0, to: analytics.valueScore / 100)
                                    .stroke(worthItIndexColor(analytics.valueScore), lineWidth: 8)
                                    .frame(width: 100, height: 100)
                                    .rotationEffect(.degrees(-90))
                                
                                VStack {
                                    Text("\(Int(analytics.valueScore))")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                    Text("分")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Text("综合价值评分")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .leading, spacing: 8) {
                            ValueMetricRow(
                                title: "满意度",
                                value: analytics.averageSatisfactionRating,
                                maxValue: 5,
                                color: .blue
                            )
                            
                            ValueMetricRow(
                                title: "情感价值",
                                value: analytics.averageEmotionalValue,
                                maxValue: 5,
                                color: .purple
                            )
                            
                            ValueMetricRow(
                                title: "使用频率",
                                value: analytics.usageFrequency / 30 * 5, // 转换为5分制
                                maxValue: 5,
                                color: .green
                            )
                        }
                    }
                    
                    // 趋势指标
                    HStack {
                        TrendIndicator(
                            title: "使用趋势",
                            value: analytics.usageTrend,
                            icon: "chart.line.uptrend.xyaxis"
                        )
                        
                        Spacer()
                        
                        TrendIndicator(
                            title: "满意度趋势",
                            value: analytics.satisfactionTrend,
                            icon: "heart.fill"
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 推荐建议部分
    private var recommendationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("智能建议")
                .font(.headline)
                .fontWeight(.semibold)
            
            if recommendations.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("暂无优化建议，您的订阅使用状况良好！")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)
            } else {
                ForEach(recommendations.prefix(3), id: \.id) { recommendation in
                    SubscriptionRecommendationCard(recommendation: recommendation)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 使用记录部分
    private var usageHistorySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("最近使用")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("记录使用") {
                    showingUsageSession = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            let recentSessions = subscriptionService.getUsageSessions(for: subscription.id ?? UUID()).prefix(5)
            
            if recentSessions.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "clock.badge.plus")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("暂无使用记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button("记录第一次使用") {
                        showingUsageSession = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            } else {
                ForEach(Array(recentSessions), id: \.id) { session in
                    UsageSessionRow(session: session)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 使用频率图表
    private var usageFrequencyChart: some View {
        VStack(alignment: .leading) {
            Text("使用频率趋势")
                .font(.subheadline)
                .fontWeight(.medium)
            
            // 这里应该是一个真实的图表，暂时用占位符
            Rectangle()
                .fill(Color.blue.opacity(0.1))
                .frame(height: 120)
                .overlay(
                    Text("使用频率图表\n(需要Charts框架)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                )
                .cornerRadius(8)
        }
    }
    
    // MARK: - 辅助方法
    private func loadAnalytics() {
        analytics = subscriptionService.calculateSubscriptionAnalytics(for: subscription)
    }
    
    private func loadRecommendations() {
        recommendations = subscriptionService.generateRecommendations(for: subscription)
    }
    
    private func shareSubscriptionInfo() {
        // 实现分享功能
    }
    
    private func worthItIndexColor(_ index: Double) -> Color {
        if index >= 70 {
            return .green
        } else if index >= 40 {
            return .orange
        } else {
            return .red
        }
    }
    
    private func formatDuration(_ seconds: TimeInterval) -> String {
        let hours = Int(seconds) / 3600
        let minutes = Int(seconds) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h\(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - 辅助视图组件
struct MetricView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct CostMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Circle()
                    .fill(color)
                    .frame(width: 8, height: 8)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct StatisticItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

struct ValueMetricRow: View {
    let title: String
    let value: Double
    let maxValue: Double
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .leading)
            
            ProgressView(value: value, total: maxValue)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
            
            Text(String(format: "%.1f", value))
                .font(.caption)
                .fontWeight(.medium)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

struct TrendIndicator: View {
    let title: String
    let value: Double
    let icon: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(value >= 0 ? .green : .red)
                .font(.caption)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text("\(value >= 0 ? "+" : "")\(String(format: "%.1f", value * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(value >= 0 ? .green : .red)
            }
        }
    }
}

struct SubscriptionRecommendationCard: View {
    let recommendation: SubscriptionRecommendation
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: recommendation.type.icon)
                .foregroundColor(recommendation.type.color)
                .font(.title2)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(recommendation.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(recommendation.priority.displayName)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(recommendation.priority.color.opacity(0.2))
                        .foregroundColor(recommendation.priority.color)
                        .cornerRadius(4)
                }
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                if let savings = recommendation.potentialSavings {
                    Text("潜在节省: ¥\(String(format: "%.2f", savings))")
                        .font(.caption2)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(recommendation.type.color.opacity(0.05))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(recommendation.type.color.opacity(0.2), lineWidth: 1)
        )
    }
}

struct UsageSessionRow: View {
    let session: UsageSession
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(session.activityDescription ?? "使用会话")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(formatDate(session.startTime))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(session.formattedDuration)
                    .font(.caption)
                    .fontWeight(.medium)
                
                HStack(spacing: 2) {
                    ForEach(1...session.satisfactionRating, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption2)
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - 预览
struct SubscriptionDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        let service = SubscriptionService(context: context, coreDataRepository: repository)
        
        // 创建示例订阅
        let subscription = Product(context: context)
        subscription.name = "Netflix"
        subscription.brand = "Netflix Inc."
        subscription.isVirtualProduct = true
        
        return NavigationView {
            SubscriptionDetailView(subscription: subscription, subscriptionService: service)
        }
    }
}