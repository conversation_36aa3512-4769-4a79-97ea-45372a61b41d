//
//  EditSubscriptionView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 编辑订阅界面
//

import SwiftUI
import CoreData

struct EditSubscriptionView: View {
    let subscription: Product
    let subscriptionService: SubscriptionService
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext
    
    // 基本信息
    @State private var name: String = ""
    @State private var brand: String = ""
    @State private var category: String = ""
    @State private var notes: String = ""
    
    // 计费信息
    @State private var price: String = ""
    @State private var billingCycle: BillingCycle = .monthly
    @State private var nextBillingDate: Date = Date()
    @State private var autoRenewal: Bool = true
    
    // 共享设置
    @State private var isShared: Bool = false
    @State private var familyMemberCount: Int = 1
    @State private var sharedWith: String = ""
    
    // 附加信息
    @State private var website: String = ""
    @State private var customerServiceContact: String = ""
    @State private var accountEmail: String = ""
    
    // UI状态
    @State private var showingDeleteAlert = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    private var subscriptionData: SubscriptionData? {
        subscriptionService.extractSubscriptionData(from: subscription)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息部分
                basicInfoSection
                
                // 计费信息部分
                billingInfoSection
                
                // 共享设置部分
                sharingSection
                
                // 附加信息部分
                additionalInfoSection
                
                // 危险操作部分
                dangerZoneSection
            }
            .navigationTitle("编辑订阅")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveSubscription()
                    }
                    .disabled(name.isEmpty || price.isEmpty || isLoading)
                }
            }
            .onAppear {
                loadSubscriptionData()
            }
            .alert("删除订阅", isPresented: $showingDeleteAlert) {
                Button("删除", role: .destructive) {
                    deleteSubscription()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("确定要删除这个订阅吗？此操作无法撤销。")
            }
        }
    }
    
    // MARK: - 基本信息部分
    private var basicInfoSection: some View {
        Section("基本信息") {
            HStack {
                Text("服务名称")
                Spacer()
                TextField("如：Netflix", text: $name)
                    .multilineTextAlignment(.trailing)
            }
            
            HStack {
                Text("提供商")
                Spacer()
                TextField("如：Netflix Inc.", text: $brand)
                    .multilineTextAlignment(.trailing)
            }
            
            HStack {
                Text("分类")
                Spacer()
                TextField("如：视频流媒体", text: $category)
                    .multilineTextAlignment(.trailing)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("备注")
                    .foregroundColor(.secondary)
                
                TextField("添加备注信息...", text: $notes, axis: .vertical)
                    .lineLimit(3...6)
            }
        }
    }
    
    // MARK: - 计费信息部分
    private var billingInfoSection: some View {
        Section("计费信息") {
            HStack {
                Text("价格")
                Spacer()
                TextField("0.00", text: $price)
                    .keyboardType(.decimalPad)
                    .multilineTextAlignment(.trailing)
                Text("元")
                    .foregroundColor(.secondary)
            }
            
            Picker("计费周期", selection: $billingCycle) {
                ForEach(BillingCycle.allCases, id: \.self) { cycle in
                    Text(cycle.displayName).tag(cycle)
                }
            }
            
            DatePicker("下次计费日期", selection: $nextBillingDate, displayedComponents: .date)
            
            Toggle("自动续费", isOn: $autoRenewal)
        }
    }
    
    // MARK: - 共享设置部分
    private var sharingSection: some View {
        Section("共享设置") {
            Toggle("家庭共享", isOn: $isShared)
            
            if isShared {
                Stepper("家庭成员数量: \(familyMemberCount)", value: $familyMemberCount, in: 1...10)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("共享成员")
                        .foregroundColor(.secondary)
                    
                    TextField("输入共享成员信息...", text: $sharedWith, axis: .vertical)
                        .lineLimit(2...4)
                }
                
                // 显示人均成本
                if let priceValue = Double(price), priceValue > 0 {
                    HStack {
                        Text("人均成本")
                        Spacer()
                        Text("¥\(String(format: "%.2f", priceValue / Double(familyMemberCount)))")
                            .foregroundColor(.green)
                            .fontWeight(.medium)
                    }
                }
            }
        }
    }
    
    // MARK: - 附加信息部分
    private var additionalInfoSection: some View {
        Section("附加信息") {
            HStack {
                Text("官方网站")
                Spacer()
                TextField("https://...", text: $website)
                    .keyboardType(.URL)
                    .autocapitalization(.none)
                    .multilineTextAlignment(.trailing)
            }
            
            HStack {
                Text("客服联系方式")
                Spacer()
                TextField("电话或邮箱", text: $customerServiceContact)
                    .multilineTextAlignment(.trailing)
            }
            
            HStack {
                Text("账户邮箱")
                Spacer()
                TextField("登录邮箱", text: $accountEmail)
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .multilineTextAlignment(.trailing)
            }
        }
    }
    
    // MARK: - 危险操作部分
    private var dangerZoneSection: some View {
        Section {
            Button("删除订阅") {
                showingDeleteAlert = true
            }
            .foregroundColor(.red)
            .frame(maxWidth: .infinity, alignment: .center)
        } footer: {
            Text("删除订阅将永久移除所有相关数据，包括使用记录和分析数据。")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 数据处理方法
    private func loadSubscriptionData() {
        // 加载现有订阅数据
        name = subscription.name ?? ""
        brand = subscription.brand ?? ""
        category = subscription.category?.name ?? ""
        notes = subscription.purchaseNotes ?? ""
        
        if let data = subscriptionData {
            price = String(format: "%.2f", data.costPerCycle)
            billingCycle = data.billingCycle
            nextBillingDate = data.nextRenewalDate
            autoRenewal = true // SubscriptionData doesn't have autoRenewal, use default
            isShared = data.isShared
            familyMemberCount = data.familyMemberCount
            sharedWith = "" // SubscriptionData doesn't have sharedWith
            website = "" // SubscriptionData doesn't have website
            customerServiceContact = "" // SubscriptionData doesn't have customerServiceContact
            accountEmail = "" // SubscriptionData doesn't have accountEmail
        } else {
            // 如果没有订阅数据，使用默认值
            price = String(format: "%.2f", subscription.price)
            nextBillingDate = subscription.expiryDate ?? Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
        }
    }
    
    private func saveSubscription() {
        guard !name.isEmpty, !price.isEmpty,
              let priceValue = Double(price) else {
            errorMessage = "请填写完整的订阅信息"
            return
        }
        
        isLoading = true
        
        Task {
            do {
                try await saveSubscriptionAsync(priceValue: priceValue)
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "保存失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func saveSubscriptionAsync(priceValue: Double) async throws {
        
        // 更新基本产品信息
        subscription.name = name
        subscription.brand = brand
        // subscription.notes = notes // Product 类型没有 notes 属性
        subscription.price = priceValue
        subscription.expiryDate = nextBillingDate
        // subscription.lastModified = Date() // Product 类型没有 lastModified 属性
        
        // 处理分类
        if !category.isEmpty {
            // 查找或创建分类
            let request: NSFetchRequest<Category> = Category.fetchRequest()
            request.predicate = NSPredicate(format: "name == %@", category)
            
            do {
                if let existingCategory = try viewContext.fetch(request).first {
                    subscription.category = existingCategory
                } else {
                    let newCategory = Category(context: viewContext)
                    newCategory.name = category
                    newCategory.id = UUID()
                    subscription.category = newCategory
                }
            } catch {
                let newCategory = Category(context: viewContext)
                newCategory.name = category
                newCategory.id = UUID()
                subscription.category = newCategory
            }
        }
        
        // 创建或更新订阅数据
        let newSubscriptionData = SubscriptionData(
            id: subscription.id ?? UUID(),
            name: name,
            serviceProvider: brand,
            billingCycle: billingCycle,
            costPerCycle: priceValue,
            currency: "CNY",
            startDate: subscription.purchaseDate ?? Date(),
            status: .active,
            description: subscription.purchaseNotes ?? "",
            category: category.isEmpty ? nil : category,
            customCycleDays: nil,
            isShared: isShared,
            familyMemberCount: familyMemberCount,
            notes: notes.isEmpty ? nil : notes
        )
        
        // 保存到CoreData
        try viewContext.save()
        
        // 更新订阅服务中的数据
        try await subscriptionService.updateSubscription(subscription, with: newSubscriptionData)
        
        await MainActor.run {
            isLoading = false
            dismiss()
        }
    }
    
    private func deleteSubscription() {
        isLoading = true
        
        Task {
            do {
                // 删除订阅数据
                try await subscriptionService.deleteSubscription(subscription)
                
                // 删除产品
                await MainActor.run {
                    viewContext.delete(subscription)
                }
                try viewContext.save()
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "删除失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

// MARK: - 预览
struct EditSubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        let service = SubscriptionService(context: context, coreDataRepository: repository)
        
        // 创建示例订阅
        let subscription = Product(context: context)
        subscription.name = "Netflix"
        subscription.brand = "Netflix Inc."
        subscription.price = 68.0
        subscription.isVirtualProduct = true
        
        return EditSubscriptionView(subscription: subscription, subscriptionService: service)
    }
}