//
//  SubscriptionListView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 订阅列表界面
//

import SwiftUI
import CoreData

struct SubscriptionListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var subscriptionService: SubscriptionService
    @StateObject private var familySharingService: FamilySharingService
    let viewContextRef: NSManagedObjectContext
    
    @State private var showingAddSubscription = false
    @State private var selectedFilter: SubscriptionFilter = .all
    @State private var searchText = ""
    @State private var showingAnalytics = false
    @State private var showingFamilyMembers = false
    @State private var showingReminderManagement = false
    @State private var showingReminderTest = false
    
    enum SubscriptionFilter: String, CaseIterable {
        case all = "全部"
        case active = "活跃"
        case nearRenewal = "即将续费"
        case paused = "暂停"
        case expired = "已过期"
        
        var icon: String {
            switch self {
            case .all: return "list.bullet"
            case .active: return "checkmark.circle.fill"
            case .nearRenewal: return "clock.fill"
            case .paused: return "pause.circle.fill"
            case .expired: return "xmark.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .all: return .blue
            case .active: return .green
            case .nearRenewal: return .orange
            case .paused: return .yellow
            case .expired: return .red
            }
        }
    }
    
    init(context: NSManagedObjectContext, coreDataRepository: CoreDataRepository<Product>) {
        let subscriptionService = SubscriptionService(context: context, coreDataRepository: coreDataRepository)
        self._subscriptionService = StateObject(wrappedValue: subscriptionService)
        
        self._familySharingService = StateObject(wrappedValue: FamilySharingService(
            context: context,
            subscriptionService: subscriptionService
        ))
        
        self.viewContextRef = context
    }
    
    var filteredSubscriptions: [Product] {
        let subscriptions: [Product]
        
        switch selectedFilter {
        case .all:
            subscriptions = subscriptionService.subscriptions
        case .active:
            subscriptions = subscriptionService.activeSubscriptions
        case .nearRenewal:
            subscriptions = subscriptionService.renewalAlerts
        case .paused:
            subscriptions = subscriptionService.subscriptions.filter { $0.currentSubscriptionStatus == .subscriptionPaused }
        case .expired:
            subscriptions = subscriptionService.subscriptions.filter { $0.currentSubscriptionStatus == .subscriptionExpired }
        }
        
        if searchText.isEmpty {
            return subscriptions
        } else {
            return subscriptions.filter { subscription in
                (subscription.name?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (subscription.brand?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (subscription.category?.name?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 统计概览
                statisticsOverview
                
                // 筛选器
                filterSection
                
                // 订阅列表
                subscriptionList
            }
            .navigationTitle("虚拟订阅")
            .searchable(text: $searchText, prompt: "搜索订阅")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button("订阅分析") {
                            showingAnalytics = true
                        }
                        
                        Button("提醒管理") {
                            showingReminderManagement = true
                        }
                        
                        Button("家庭成员") {
                            showingFamilyMembers = true
                        }
                        
                        Button("提醒测试") {
                            showingReminderTest = true
                        }
                    } label: {
                        Image(systemName: "chart.bar.fill")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddSubscription = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddSubscription) {
                AddSubscriptionView(context: viewContext, coreDataRepository: CoreDataRepository(context: viewContext))
            }
            .sheet(isPresented: $showingAnalytics) {
                SubscriptionAnalyticsView(subscriptionService: subscriptionService)
            }
            .sheet(isPresented: $showingReminderManagement) {
                SubscriptionReminderManagementView(context: viewContext)
            }
            .sheet(isPresented: $showingReminderTest) {
                SubscriptionReminderTestView(context: viewContext)
            }
            .sheet(isPresented: $showingFamilyMembers) {
                FamilyMembersView(familySharingService: familySharingService)
            }
        }
    }
    
    // MARK: - 统计概览
    private var statisticsOverview: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                StatisticCard(
                    title: "总订阅",
                    value: "\(subscriptionService.subscriptions.count)",
                    icon: "app.fill",
                    color: .blue
                )
                
                StatisticCard(
                    title: "活跃订阅",
                    value: "\(subscriptionService.activeSubscriptions.count)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
                
                StatisticCard(
                    title: "月度支出",
                    value: "¥\(String(format: "%.0f", subscriptionService.calculateTotalMonthlySubscriptionCost()))",
                    icon: "dollarsign.circle.fill",
                    color: .orange
                )
                
                StatisticCard(
                    title: "即将续费",
                    value: "\(subscriptionService.renewalAlerts.count)",
                    icon: "clock.fill",
                    color: .red
                )
                
                // 家庭共享统计
                let sharedSubscriptions = subscriptionService.subscriptions.filter { 
                    subscriptionService.extractSubscriptionData(from: $0)?.isShared == true 
                }
                if !sharedSubscriptions.isEmpty {
                    StatisticCard(
                        title: "家庭共享",
                        value: "\(sharedSubscriptions.count)",
                        icon: "person.2.fill",
                        color: .purple
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - 筛选器部分
    private var filterSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(SubscriptionFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.rawValue,
                        isSelected: selectedFilter == filter,
                        count: getFilterCount(for: filter),
                        color: filter.color
                    ) {
                        selectedFilter = filter
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - 订阅列表
    private var subscriptionList: some View {
        Group {
            if filteredSubscriptions.isEmpty {
                emptyStateView
            } else {
                List {
                    ForEach(filteredSubscriptions, id: \.id) { subscription in
                        NavigationLink(destination: SubscriptionDetailView(subscription: subscription, subscriptionService: subscriptionService)) {
                            SubscriptionRowView(subscription: subscription, subscriptionService: subscriptionService)
                        }
                    }
                    .onDelete(perform: deleteSubscriptions)
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "app.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("暂无订阅")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("点击右上角的 + 按钮添加您的第一个虚拟订阅")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button("添加订阅") {
                showingAddSubscription = true
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    // MARK: - 删除订阅
    private func deleteSubscriptions(offsets: IndexSet) {
        for index in offsets {
            let subscription = filteredSubscriptions[index]
            Task {
                do {
                    try await subscriptionService.deleteSubscription(subscription)
                } catch {
                    print("删除订阅失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 获取筛选器数量
    private func getFilterCount(for filter: SubscriptionFilter) -> Int {
        switch filter {
        case .all:
            return subscriptionService.subscriptions.count
        case .active:
            return subscriptionService.activeSubscriptions.count
        case .paused:
            return subscriptionService.subscriptions.filter { $0.currentSubscriptionStatus == .subscriptionPaused }.count
        case .expired:
            return subscriptionService.subscriptions.filter { $0.currentSubscriptionStatus == .subscriptionExpired }.count
        case .nearRenewal:
            return subscriptionService.renewalAlerts.count
        }
    }
}

// MARK: - 统计卡片
struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                Spacer()
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(width: 120, height: 80)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// FilterChip 已在 Components/FilterChip.swift 中定义

// MARK: - 订阅行视图
struct SubscriptionRowView: View {
    let subscription: Product
    let subscriptionService: SubscriptionService
    
    private var subscriptionData: SubscriptionData? {
        subscriptionService.extractSubscriptionData(from: subscription)
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // 订阅图标
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(subscription.currentSubscriptionStatus.color.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: "app.fill")
                    .font(.title2)
                    .foregroundColor(subscription.currentSubscriptionStatus.color)
            }
            
            // 订阅信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(subscription.name ?? "未知订阅")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    // 状态指示器
                    HStack(spacing: 4) {
                        Image(systemName: subscription.currentSubscriptionStatus.icon)
                            .font(.caption)
                        Text(subscription.currentSubscriptionStatus.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(subscription.currentSubscriptionStatus.color.opacity(0.2))
                    .foregroundColor(subscription.currentSubscriptionStatus.color)
                    .cornerRadius(8)
                }
                
                Text(subscription.brand ?? "未知提供商")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack {
                    if let data = subscriptionData {
                        Text("¥\(String(format: "%.2f", data.monthlyCost))/月")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                        
                        Text("•")
                            .foregroundColor(.secondary)
                        
                        Text(data.billingCycle.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 续费提醒
                    if subscription.isSubscriptionNearExpiry {
                        HStack(spacing: 2) {
                            Image(systemName: "clock.fill")
                                .font(.caption2)
                            Text("\(subscription.subscriptionDaysRemaining ?? 0)天")
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.orange)
                    }
                }
            }
            
            // 值度指数
            VStack {
                Text("\(Int(subscription.worthItIndex()))")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(worthItIndexColor(subscription.worthItIndex()))
                
                Text("值度")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private func worthItIndexColor(_ index: Double) -> Color {
        if index >= 70 {
            return .green
        } else if index >= 40 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - 预览
struct SubscriptionListView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        
        SubscriptionListView(context: context, coreDataRepository: repository)
    }
}