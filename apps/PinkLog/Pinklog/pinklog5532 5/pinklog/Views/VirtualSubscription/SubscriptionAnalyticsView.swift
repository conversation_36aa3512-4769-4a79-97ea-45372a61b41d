//
//  SubscriptionAnalyticsView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 订阅分析界面
//

import SwiftUI
import CoreData
import Charts

struct SubscriptionAnalyticsView: View {
    let subscriptionService: SubscriptionService
    
    @State private var selectedTimeRange: AnalyticsTimeRange = .month
    @State private var selectedMetric: AnalyticsMetric = .cost
    @State private var subscriptions: [Product] = []
    @State private var analyticsData: [SubscriptionAnalytics] = []
    @State private var totalCost: Double = 0
    @State private var totalUsageTime: TimeInterval = 0
    @State private var averageWorthItIndex: Double = 0
    
    enum AnalyticsTimeRange: String, CaseIterable {
        case week = "本周"
        case month = "本月"
        case quarter = "本季度"
        case year = "本年"
        
        var days: Int {
            switch self {
            case .week: return 7
            case .month: return 30
            case .quarter: return 90
            case .year: return 365
            }
        }
    }
    
    enum AnalyticsMetric: String, CaseIterable {
        case cost = "成本分析"
        case usage = "使用分析"
        case satisfaction = "满意度分析"
        case efficiency = "效率分析"
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 时间范围和指标选择器
                controlsSection
                
                // 总览卡片
                overviewSection
                
                // 主要图表
                mainChartSection
                
                // 分类分析
                categoryAnalysisSection
                
                // 订阅排行
                subscriptionRankingSection
                
                // 趋势分析
                trendAnalysisSection
                
                // 优化建议
                optimizationSection
            }
            .padding()
        }
        .navigationTitle("订阅分析")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            loadAnalyticsData()
        }
        .onChange(of: selectedTimeRange) { _ in
            loadAnalyticsData()
        }
    }
    
    // MARK: - 控制器部分
    private var controlsSection: some View {
        VStack(spacing: 12) {
            // 时间范围选择器
            Picker("时间范围", selection: $selectedTimeRange) {
                ForEach(AnalyticsTimeRange.allCases, id: \.self) { range in
                    Text(range.rawValue).tag(range)
                }
            }
            .pickerStyle(.segmented)
            
            // 指标选择器
            Picker("分析指标", selection: $selectedMetric) {
                ForEach(AnalyticsMetric.allCases, id: \.self) { metric in
                    Text(metric.rawValue).tag(metric)
                }
            }
            .pickerStyle(.segmented)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 总览部分
    private var overviewSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
            OverviewCard(
                title: "总订阅数",
                value: "\(subscriptions.count)",
                subtitle: "个活跃订阅",
                icon: "app.badge",
                color: .blue
            )
            
            OverviewCard(
                title: "月度总成本",
                value: "¥\(String(format: "%.0f", totalCost))",
                subtitle: "当前月费用",
                icon: "dollarsign.circle",
                color: .green
            )
            
            OverviewCard(
                title: "总使用时长",
                value: formatTotalTime(totalUsageTime),
                subtitle: selectedTimeRange.rawValue,
                icon: "clock",
                color: .orange
            )
            
            OverviewCard(
                title: "平均值度指数",
                value: "\(Int(averageWorthItIndex))",
                subtitle: "综合评分",
                icon: "star.circle",
                color: worthItIndexColor(averageWorthItIndex)
            )
        }
    }
    
    // MARK: - 主要图表部分
    private var mainChartSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(selectedMetric.rawValue)
                .font(.headline)
                .fontWeight(.semibold)
            
            switch selectedMetric {
            case .cost:
                costChart
            case .usage:
                usageChart
            case .satisfaction:
                satisfactionChart
            case .efficiency:
                efficiencyChart
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 成本图表
    private var costChart: some View {
        VStack(spacing: 16) {
            // 成本分布饼图
            if !subscriptions.isEmpty {
                Chart(subscriptions.prefix(8), id: \.id) { subscription in
                    SectorMark(
                        angle: .value("成本", subscription.price),
                        innerRadius: .ratio(0.5),
                        angularInset: 1
                    )
                    .foregroundStyle(by: .value("订阅", subscription.name ?? "未知"))
                    .opacity(0.8)
                }
                .frame(height: 200)
                .chartLegend(position: .bottom, alignment: .center, spacing: 8)
            }
            
            // 成本统计
            HStack {
                CostStatItem(title: "最高月费", value: subscriptions.map { $0.price }.max() ?? 0, color: .red)
                Spacer()
                CostStatItem(title: "最低月费", value: subscriptions.map { $0.price }.min() ?? 0, color: .green)
                Spacer()
                CostStatItem(title: "平均月费", value: totalCost / Double(max(subscriptions.count, 1)), color: .blue)
            }
        }
    }
    
    // MARK: - 使用图表
    private var usageChart: some View {
        VStack(spacing: 16) {
            // 使用时长条形图
            if !analyticsData.isEmpty {
                Chart(analyticsData.prefix(10), id: \.subscriptionId) { analytics in
                    BarMark(
                        x: .value("使用时长", analytics.totalUsageTime / 3600), // 转换为小时
                        y: .value("订阅", getSubscriptionName(analytics.subscriptionId))
                    )
                    .foregroundStyle(.blue.gradient)
                }
                .frame(height: 250)
                .chartXAxis {
                    AxisMarks(position: .bottom) {
                        AxisValueLabel()
                        AxisGridLine()
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .leading) {
                        AxisValueLabel()
                    }
                }
            }
            
            // 使用统计
            HStack {
                UsageStatItem(title: "最活跃", value: getMostActiveSubscription(), color: .green)
                Spacer()
                UsageStatItem(title: "使用最少", value: getLeastActiveSubscription(), color: .orange)
            }
        }
    }
    
    // MARK: - 满意度图表
    private var satisfactionChart: some View {
        VStack(spacing: 16) {
            // 满意度散点图
            if !analyticsData.isEmpty {
                Chart(analyticsData, id: \.subscriptionId) { analytics in
                    PointMark(
                        x: .value("使用频率", analytics.usageFrequency),
                        y: .value("满意度", analytics.averageSatisfactionRating)
                    )
                    .foregroundStyle(by: .value("订阅", getSubscriptionName(analytics.subscriptionId)))
                    .symbolSize(100)
                }
                .frame(height: 200)
                .chartXAxisLabel("使用频率 (次/月)")
                .chartYAxisLabel("满意度评分")
            }
            
            // 满意度统计
            HStack {
                SatisfactionStatItem(title: "最满意", rating: analyticsData.map { $0.averageSatisfactionRating }.max() ?? 0)
                Spacer()
                SatisfactionStatItem(title: "平均满意度", rating: analyticsData.map { $0.averageSatisfactionRating }.reduce(0, +) / Double(max(analyticsData.count, 1)))
                Spacer()
                SatisfactionStatItem(title: "需改进", rating: analyticsData.map { $0.averageSatisfactionRating }.min() ?? 0)
            }
        }
    }
    
    // MARK: - 效率图表
    private var efficiencyChart: some View {
        VStack(spacing: 16) {
            // 效率雷达图（简化版）
            if !analyticsData.isEmpty {
                Chart(analyticsData.prefix(5), id: \.subscriptionId) { analytics in
                    LineMark(
                        x: .value("订阅", getSubscriptionName(analytics.subscriptionId)),
                        y: .value("效率分数", analytics.valueScore)
                    )
                    .foregroundStyle(.blue)
                    .lineStyle(StrokeStyle(lineWidth: 2))
                    
                    AreaMark(
                        x: .value("订阅", getSubscriptionName(analytics.subscriptionId)),
                        y: .value("效率分数", analytics.valueScore)
                    )
                    .foregroundStyle(.blue.opacity(0.3))
                }
                .frame(height: 200)
            }
            
            // 效率指标
            HStack {
                EfficiencyIndicator(title: "高效订阅", count: analyticsData.filter { $0.valueScore >= 70 }.count, color: .green)
                Spacer()
                EfficiencyIndicator(title: "中等效率", count: analyticsData.filter { $0.valueScore >= 40 && $0.valueScore < 70 }.count, color: .orange)
                Spacer()
                EfficiencyIndicator(title: "低效订阅", count: analyticsData.filter { $0.valueScore < 40 }.count, color: .red)
            }
        }
    }
    
    // MARK: - 分类分析部分
    private var categoryAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分类分析")
                .font(.headline)
                .fontWeight(.semibold)
            
            let categoryData = getCategoryAnalysis()
            
            ForEach(categoryData, id: \.category) { data in
                CategoryAnalysisRow(
                    category: data.category,
                    count: data.count,
                    totalCost: data.totalCost,
                    averageRating: data.averageRating
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 订阅排行部分
    private var subscriptionRankingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("订阅排行")
                .font(.headline)
                .fontWeight(.semibold)
            
            let rankedSubscriptions = subscriptions.sorted { $0.worthItIndex() > $1.worthItIndex() }
            
            ForEach(Array(rankedSubscriptions.prefix(5).enumerated()), id: \.element.id) { index, subscription in
                SubscriptionRankRow(
                    rank: index + 1,
                    subscription: subscription,
                    worthItIndex: subscription.worthItIndex()
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 趋势分析部分
    private var trendAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("趋势分析")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                TrendCard(
                    title: "成本趋势",
                    value: calculateCostTrend(),
                    icon: "chart.line.uptrend.xyaxis",
                    isPositive: calculateCostTrend() <= 0
                )
                
                TrendCard(
                    title: "使用趋势",
                    value: calculateUsageTrend(),
                    icon: "clock.arrow.circlepath",
                    isPositive: calculateUsageTrend() >= 0
                )
                
                TrendCard(
                    title: "满意度趋势",
                    value: calculateSatisfactionTrend(),
                    icon: "heart.fill",
                    isPositive: calculateSatisfactionTrend() >= 0
                )
                
                TrendCard(
                    title: "效率趋势",
                    value: calculateEfficiencyTrend(),
                    icon: "speedometer",
                    isPositive: calculateEfficiencyTrend() >= 0
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 优化建议部分
    private var optimizationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("优化建议")
                .font(.headline)
                .fontWeight(.semibold)
            
            let recommendations = generateOptimizationRecommendations()
            
            if recommendations.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("您的订阅组合已经很优化了！")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)
            } else {
                ForEach(recommendations.prefix(3), id: \.id) { recommendation in
                    OptimizationRecommendationCard(recommendation: recommendation)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 数据加载和计算方法
    private func loadAnalyticsData() {
        subscriptions = subscriptionService.getAllActiveSubscriptions()
        analyticsData = subscriptions.compactMap { subscriptionService.calculateSubscriptionAnalytics(for: $0) }
        
        totalCost = subscriptions.reduce(0) { $0 + $1.price }
        totalUsageTime = analyticsData.reduce(0) { $0 + $1.totalUsageTime }
        averageWorthItIndex = subscriptions.isEmpty ? 0 : subscriptions.reduce(0) { $0 + $1.worthItIndex() } / Double(subscriptions.count)
    }
    
    private func getSubscriptionName(_ productId: UUID) -> String {
        return subscriptions.first { $0.id == productId }?.name ?? "未知订阅"
    }
    
    private func getMostActiveSubscription() -> String {
        guard let mostActive = analyticsData.max(by: { $0.totalUsageTime < $1.totalUsageTime }) else {
            return "无数据"
        }
        return getSubscriptionName(mostActive.subscriptionId)
    }
    
    private func getLeastActiveSubscription() -> String {
        guard let leastActive = analyticsData.min(by: { $0.totalUsageTime < $1.totalUsageTime }) else {
            return "无数据"
        }
        return getSubscriptionName(leastActive.subscriptionId)
    }
    
    private func getCategoryAnalysis() -> [CategoryAnalysisData] {
        let groupedByCategory = Dictionary(grouping: subscriptions) { $0.category?.name ?? "未分类" }
        
        return groupedByCategory.map { category, subs in
            let totalCost = subs.reduce(0) { $0 + $1.price }
            let averageRating = subs.isEmpty ? 0 : subs.reduce(0) { $0 + $1.worthItIndex() } / Double(subs.count)
            
            return CategoryAnalysisData(
                category: category,
                count: subs.count,
                totalCost: totalCost,
                averageRating: averageRating
            )
        }.sorted { $0.totalCost > $1.totalCost }
    }
    
    private func calculateCostTrend() -> Double {
        // 简化的趋势计算，实际应该基于历史数据
        return Double.random(in: -10...10)
    }
    
    private func calculateUsageTrend() -> Double {
        return Double.random(in: -15...15)
    }
    
    private func calculateSatisfactionTrend() -> Double {
        return Double.random(in: -5...5)
    }
    
    private func calculateEfficiencyTrend() -> Double {
        return Double.random(in: -8...8)
    }
    
    private func generateOptimizationRecommendations() -> [SubscriptionRecommendation] {
        var recommendations: [SubscriptionRecommendation] = []
        
        // 检查低效订阅
        let lowEfficiencySubscriptions = subscriptions.filter { $0.worthItIndex() < 40 }
        if !lowEfficiencySubscriptions.isEmpty {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: lowEfficiencySubscriptions.first?.id ?? UUID(),
                type: .cancel,
                title: "考虑取消低效订阅",
                description: "您有\(lowEfficiencySubscriptions.count)个订阅的值度指数较低，建议重新评估其必要性。",
                potentialSavings: lowEfficiencySubscriptions.reduce(0) { $0 + $1.price },
                priority: .high,
                actionRequired: "重新评估订阅必要性",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        // 检查重复订阅
        let categoryGroups = Dictionary(grouping: subscriptions) { $0.category?.name ?? "" }
        for (category, subs) in categoryGroups where subs.count > 1 && !category.isEmpty {
            recommendations.append(SubscriptionRecommendation(
                id: UUID(),
                subscriptionId: subs.first?.id ?? UUID(),
                type: .duplicate,
                title: "发现重复类型订阅",
                description: "在\(category)分类中有\(subs.count)个订阅，考虑整合以节省成本。",
                potentialSavings: subs.dropFirst().reduce(0) { $0 + $1.price },
                priority: .medium,
                actionRequired: "考虑整合重复订阅",
                createdDate: Date(),
                isRead: false
            ))
        }
        
        return recommendations
    }
    
    private func formatTotalTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        if hours > 24 {
            let days = hours / 24
            return "\(days)天"
        } else {
            return "\(hours)小时"
        }
    }
    
    private func worthItIndexColor(_ index: Double) -> Color {
        if index >= 70 {
            return .green
        } else if index >= 40 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - 数据模型
struct CategoryAnalysisData {
    let category: String
    let count: Int
    let totalCost: Double
    let averageRating: Double
}

// MARK: - 辅助视图组件
struct OverviewCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                Spacer()
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct CostStatItem: View {
    let title: String
    let value: Double
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text("¥\(String(format: "%.0f", value))")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct UsageStatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct SatisfactionStatItem: View {
    let title: String
    let rating: Double
    
    var body: some View {
        VStack(spacing: 4) {
            HStack(spacing: 2) {
                ForEach(1...Int(rating), id: \.self) { _ in
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)
                }
            }
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct EfficiencyIndicator: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text("\(count)")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

struct CategoryAnalysisRow: View {
    let category: String
    let count: Int
    let totalCost: Double
    let averageRating: Double
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(category)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(count)个订阅")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("¥\(String(format: "%.0f", totalCost))")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
                
                HStack(spacing: 2) {
                    ForEach(1...Int(averageRating / 20), id: \.self) { _ in // 转换为5星制
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption2)
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct SubscriptionRankRow: View {
    let rank: Int
    let subscription: Product
    let worthItIndex: Double
    
    var body: some View {
        HStack {
            // 排名
            Text("#\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)
            
            // 订阅信息
            VStack(alignment: .leading, spacing: 2) {
                Text(subscription.name ?? "未知订阅")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subscription.brand ?? "")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 值度指数
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(Int(worthItIndex))")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(worthItIndexColor(worthItIndex))
                
                Text("值度指数")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .primary
        }
    }
    
    private func worthItIndexColor(_ index: Double) -> Color {
        if index >= 70 {
            return .green
        } else if index >= 40 {
            return .orange
        } else {
            return .red
        }
    }
}

struct TrendCard: View {
    let title: String
    let value: Double
    let icon: String
    let isPositive: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(isPositive ? .green : .red)
                    .font(.title3)
                Spacer()
            }
            
            Text("\(value >= 0 ? "+" : "")\(String(format: "%.1f", value))%")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(isPositive ? .green : .red)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct OptimizationRecommendationCard: View {
    let recommendation: SubscriptionRecommendation
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: recommendation.type.icon)
                .foregroundColor(recommendation.type.color)
                .font(.title2)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(recommendation.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(recommendation.priority.displayName)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(recommendation.priority.color.opacity(0.2))
                        .foregroundColor(recommendation.priority.color)
                        .cornerRadius(4)
                }
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                if let savings = recommendation.potentialSavings {
                    Text("潜在节省: ¥\(String(format: "%.0f", savings))/月")
                        .font(.caption2)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(recommendation.type.color.opacity(0.05))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(recommendation.type.color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - 预览
struct SubscriptionAnalyticsView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let repository = CoreDataRepository<Product>(context: context)
        let service = SubscriptionService(context: context, coreDataRepository: repository)
        
        return NavigationView {
            SubscriptionAnalyticsView(subscriptionService: service)
        }
    }
}