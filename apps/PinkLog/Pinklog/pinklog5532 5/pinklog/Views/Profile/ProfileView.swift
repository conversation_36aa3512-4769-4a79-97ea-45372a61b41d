import SwiftUI
import UIKit
import CoreData

struct ProfileView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var userProfileManager: UserProfileManager
    @StateObject private var reminderManager = ProactiveReminderManager.shared
    @State private var showingSettings = false
    @State private var showingNotificationSettings = false
    @State private var showingAPISettings = false
    @State private var showingBackupSettings = false

    // 动画状态
    @State private var crownRotation: Double = 0
    @State private var glowAnimation: Bool = false
    @State private var shimmerOffset: CGFloat = -200
    @State private var memorySparkle: Bool = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 用户信息
                userInfoSection

                // Pro升级横幅（仅对免费用户显示）
                if !productViewModel.isPremiumUser {
                    premiumBannerSection
                }

                // 应用统计
                appStatsSection

                // 功能与设置
                featuresSection

                // 数据管理
                dataManagementSection

                // 关于应用
                aboutSection
            }
            .padding()
        }
        .navigationTitle("我的")
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(themeManager) // 确保 SettingsView 也能访问 themeManager
        }
        .sheet(isPresented: $showingNotificationSettings) {
            NotificationSettingsView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingAPISettings) {
            APISettingsView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingBackupSettings) {
            BackupSettingsView()
                .environmentObject(themeManager)
        }
    }

    // Pro升级横幅
    private var premiumBannerSection: some View {
        Button {
            productViewModel.showPremiumSheet = true
        } label: {
            ZStack {
                // 渐变背景
                LinearGradient(
                    colors: [
                        Color.purple.opacity(0.8),
                        Color.blue.opacity(0.9),
                        themeManager.currentTheme.primaryColor.opacity(0.8)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .cornerRadius(16)
                
                // 背景装饰图案
                HStack {
                    Spacer()
                    VStack {
                        Spacer()
                        Image(systemName: "crown.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.white)
                            .opacity(0.1)
                            .rotationEffect(.degrees(15))
                    }
                }
                .padding(.trailing, 20)
                
                // 主要内容
                HStack(spacing: 16) {
                    // 左侧图标
                    VStack {
                        ZStack {
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 50, height: 50)
                            
                            Image(systemName: "crown.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.yellow)
                        }
                        Spacer()
                    }
                    
                    // 中间文字内容
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("升级到")
                                .font(.headline)
                                .foregroundColor(.white)
                            Text("Pro")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.yellow)
                        }
                        
                        Text("解锁无限物品添加和高级功能")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.leading)
                        
                        HStack(spacing: 12) {
                            FeatureBadge(icon: "infinity", text: "无限添加")
                            FeatureBadge(icon: "chart.line.uptrend.xyaxis", text: "高级分析")
                            FeatureBadge(icon: "icloud", text: "云同步")
                        }
                        .padding(.top, 8)
                    }
                    
                    Spacer()
                    
                    // 右侧箭头
                    VStack {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                        Spacer()
                    }
                }
                .padding(20)
            }
        }
        .frame(height: 120)
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.spring(response: 0.3), value: productViewModel.showPremiumSheet)
    }

    // 功能标签组件
    private struct FeatureBadge: View {
        let icon: String
        let text: String
        
        var body: some View {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 10))
                Text(text)
                    .font(.caption2)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.white.opacity(0.2))
            .cornerRadius(8)
        }
    }

    // 用户信息
    private var userInfoSection: some View {
        VStack(spacing: 16) {
            ZStack {
                // 用户头像
                if let avatarImage = userProfileManager.avatarImage {
                    Image(uiImage: avatarImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    productViewModel.isPremiumUser ?
                                    LinearGradient(
                                        colors: [.yellow, .orange, .purple],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ) :
                                    LinearGradient(
                                        colors: [themeManager.currentTheme.primaryColor],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: productViewModel.isPremiumUser ? 4 : 3
                                )
                        )
                        .shadow(
                            color: productViewModel.isPremiumUser ? .yellow.opacity(0.3) : .clear,
                            radius: productViewModel.isPremiumUser ? 8 : 0,
                            x: 0,
                            y: 0
                        )
                } else {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }

                if productViewModel.isPremiumUser {
                    HStack {
                        Spacer()
                        VStack {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [.yellow, .orange],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 28, height: 28)
                                    .shadow(color: .yellow.opacity(0.5), radius: 4, x: 0, y: 2)

                                Image(systemName: "crown.fill")
                                    .font(.system(size: 14, weight: .bold))
                                    .foregroundColor(.white)
                                    .rotationEffect(.degrees(crownRotation))
                                    .scaleEffect(glowAnimation ? 1.1 : 1.0)
                            }
                            Spacer()
                        }
                    }
                    .frame(width: 80, height: 80)
                }
            }

            VStack(spacing: 8) {
                HStack {
                    Text(userProfileManager.displayNickname)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(
                            productViewModel.isPremiumUser ?
                            Color.white :
                            Color.primary
                        )
                        .shadow(
                            color: productViewModel.isPremiumUser ? .yellow.opacity(0.3) : .clear,
                            radius: productViewModel.isPremiumUser ? 2 : 0,
                            x: 0,
                            y: 1
                        )

                    if productViewModel.isPremiumUser {
                        Text("Pro")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 10)
                            .padding(.vertical, 4)
                            .background(
                                ZStack {
                                    LinearGradient(
                                        colors: [.yellow, .orange, .purple],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )

                                    // 添加光泽效果
                                    LinearGradient(
                                        colors: [.white.opacity(0.3), .clear],
                                        startPoint: .top,
                                        endPoint: .bottom
                                    )

                                    // 闪光动效
                                    Rectangle()
                                        .fill(
                                            LinearGradient(
                                                colors: [.clear, .white.opacity(0.6), .clear],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: 30)
                                        .offset(x: shimmerOffset)
                                        .mask(
                                            RoundedRectangle(cornerRadius: 10)
                                        )
                                }
                            )
                            .cornerRadius(10)
                            .shadow(color: .yellow.opacity(0.4), radius: 4, x: 0, y: 2)
                            .scaleEffect(glowAnimation ? 1.05 : 1.0)
                    }
                }

                Text(productViewModel.isPremiumUser ?
                     "感谢您的支持，尽享Pro功能" :
                     "开始记录您的物品价值之旅")
                    .font(.subheadline)
                    .foregroundColor(
                        productViewModel.isPremiumUser ?
                        Color.white.opacity(0.8) :
                        Color.secondary
                    )
            }
            
            // 免费用户限制提示
            if !productViewModel.isPremiumUser {
                VStack(spacing: 8) {
                    Text("已添加 \(10 - productViewModel.getRemainingFreeItems())/10 个物品")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if productViewModel.getRemainingFreeItems() <= 3 {
                        Button {
                            productViewModel.showPremiumSheet = true
                        } label: {
                            HStack {
                                Image(systemName: "crown.fill")
                                    .foregroundColor(.yellow)
                                Text("升级到Pro")
                            }
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                LinearGradient(
                                    colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.primaryColor.opacity(0.8)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(16)
                            .shadow(color: themeManager.currentTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)
                        }
                    }
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(
            Group {
                if productViewModel.isPremiumUser {
                    // Pro用户的尊贵背景
                    ZStack {
                        // 主背景渐变
                        LinearGradient(
                            colors: [
                                Color.black.opacity(0.8),
                                Color.purple.opacity(0.3),
                                Color.blue.opacity(0.2)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )

                        // 动态光效背景
                        LinearGradient(
                            colors: [
                                Color.yellow.opacity(0.1),
                                Color.clear,
                                Color.purple.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )

                        // 微妙的纹理效果
                        Color.white.opacity(0.05)
                    }
                } else {
                    // 普通用户的简洁背景
                    Color(UIColor.secondarySystemBackground)
                }
            }
        )
        .overlay(
            // Pro用户的边框光效
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    productViewModel.isPremiumUser ?
                    LinearGradient(
                        colors: [.yellow.opacity(0.6), .purple.opacity(0.4), .blue.opacity(0.3)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ) :
                    LinearGradient(colors: [Color.clear], startPoint: .top, endPoint: .bottom),
                    lineWidth: productViewModel.isPremiumUser ? 1.5 : 0
                )
        )
        .cornerRadius(12)
        .shadow(
            color: productViewModel.isPremiumUser ? .purple.opacity(glowAnimation ? 0.3 : 0.2) : .clear,
            radius: productViewModel.isPremiumUser ? (glowAnimation ? 16 : 12) : 0,
            x: 0,
            y: productViewModel.isPremiumUser ? 4 : 0
        )
        .scaleEffect(productViewModel.isPremiumUser ? (glowAnimation ? 1.02 : 1.0) : 1.0)
        .onAppear {
            startAnimations()
        }
    }

    // 应用统计
    private var appStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("应用统计")
                .font(.headline)
                .padding(.horizontal)

            HStack(spacing: 16) {
                // 记录天数
                VStack(spacing: 8) {
                    if productViewModel.isPremiumUser {
                        Text("\(recordingDays)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.purple, .blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    } else {
                        Text("\(recordingDays)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }

                    Text("记录天数")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 产品数量
                VStack(spacing: 8) {
                    if productViewModel.isPremiumUser {
                        Text("\(productViewModel.getTotalProductsCount())")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.purple, .blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    } else {
                        Text("\(productViewModel.getTotalProductsCount())")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }

                    Text("产品数量")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                // 使用记录
                VStack(spacing: 8) {
                    if productViewModel.isPremiumUser {
                        Text("\(totalUsageRecords)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.purple, .blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    } else {
                        Text("\(totalUsageRecords)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }

                    Text("使用记录")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            .padding()
            .background(
                productViewModel.isPremiumUser ?
                LinearGradient(
                    colors: [
                        Color(UIColor.secondarySystemBackground),
                        Color.purple.opacity(0.05),
                        Color(UIColor.secondarySystemBackground)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ) :
                LinearGradient(
                    colors: [Color(UIColor.secondarySystemBackground)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        productViewModel.isPremiumUser ?
                        LinearGradient(
                            colors: [.purple.opacity(0.2), .clear, .purple.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(colors: [Color.clear], startPoint: .top, endPoint: .bottom),
                        lineWidth: productViewModel.isPremiumUser ? 0.5 : 0
                    )
            )
            .cornerRadius(12)
        }
    }

    // 计算记录天数
    private var recordingDays: Int {
        // 获取最早的产品购买日期
        let earliestDate = productViewModel.getEarliestProductDate()

        if let date = earliestDate {
            let calendar = Calendar.current
            let components = calendar.dateComponents([.day], from: date, to: Date())
            return max(1, components.day ?? 0)
        }

        return 0
    }

    // 计算总使用记录数
    private var totalUsageRecords: Int {
        return usageViewModel.getTotalUsageRecordsCount()
    }

    // 功能与设置
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("功能与设置")
                .font(.headline)
                .padding(.horizontal)

            VStack(spacing: 0) {
                // 已借出物品
                NavigationLink(destination: LoanedItemsView()
                    .environmentObject(productViewModel)
                    .environmentObject(themeManager)
                    .environmentObject(usageViewModel)) {
                    HStack {
                        Image(systemName: "arrow.up.forward.circle")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("已借出物品")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        // 显示当前借出物品数量
                        let loanedCount = productViewModel.products.filter { $0.loanStatus == .loanedOut || $0.loanStatus == .overdue }.count
                        if loanedCount > 0 {
                            Text("\(loanedCount)")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(themeManager.currentTheme.primaryColor)
                                .clipShape(Capsule())
                        }

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 提醒中心
                NavigationLink(destination: RemindersView()
                    .environmentObject(themeManager)
                    .environmentObject(productViewModel)
                    .environmentObject(usageViewModel)) {
                    HStack {
                        Image(systemName: "bell.badge")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("提醒中心")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        // 显示未读提醒数量
                        if reminderManager.unreadCount > 0 {
                            Text("\(reminderManager.unreadCount)")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.red)
                                .clipShape(Capsule())
                        }

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 消耗品管理
                NavigationLink(destination: ConsumableManagementView()
                    .environmentObject(themeManager)) {
                    HStack {
                        Image(systemName: "archivebox")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("消耗品管理")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 通知设置
                Button(action: {
                    showingNotificationSettings = true
                }) {
                    HStack {
                        Image(systemName: "bell")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("通知设置")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 回忆录
                NavigationLink(destination: MemoirsView(context: PersistenceController.shared.container.viewContext)
                    .environmentObject(themeManager)) {
                    HStack {
                        // 自定义回忆录图标
                        ZStack {
                            // 背景光晕
                            Circle()
                                .fill(
                                    RadialGradient(
                                        colors: [
                                            themeManager.currentTheme.primaryColor.opacity(0.15),
                                            Color.clear
                                        ],
                                        center: .center,
                                        startRadius: 2,
                                        endRadius: 15
                                    )
                                )
                                .frame(width: 30, height: 30)

                            // 主图标 - 优雅的书本与心形结合
                            ZStack {
                                // 书本底层
                                RoundedRectangle(cornerRadius: 3)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                themeManager.currentTheme.primaryColor.opacity(0.8),
                                                themeManager.currentTheme.primaryColor.opacity(0.6)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 16, height: 12)
                                    .offset(x: -1, y: 1)

                                // 书本主体
                                RoundedRectangle(cornerRadius: 3)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                themeManager.currentTheme.primaryColor,
                                                themeManager.currentTheme.primaryColor.opacity(0.8)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 16, height: 12)

                                // 书脊线条
                                Rectangle()
                                    .fill(Color.white.opacity(0.3))
                                    .frame(width: 0.5, height: 10)
                                    .offset(x: -6)

                                Rectangle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 0.5, height: 8)
                                    .offset(x: -4)

                                // 心形回忆符号
                                Image(systemName: "heart.fill")
                                    .font(.system(size: 6, weight: .medium))
                                    .foregroundColor(.white.opacity(0.9))
                                    .offset(x: 2, y: -1)

                                // 闪烁的记忆星点
                                Circle()
                                    .fill(Color.white.opacity(memorySparkle ? 0.9 : 0.4))
                                    .frame(width: 2, height: 2)
                                    .offset(x: 6, y: -4)
                                    .scaleEffect(memorySparkle ? 1.2 : 0.8)

                                Circle()
                                    .fill(themeManager.currentTheme.primaryColor.opacity(memorySparkle ? 0.8 : 0.3))
                                    .frame(width: 1.5, height: 1.5)
                                    .offset(x: 8, y: -2)
                                    .scaleEffect(memorySparkle ? 1.3 : 0.7)

                                Circle()
                                    .fill(Color.white.opacity(memorySparkle ? 0.8 : 0.2))
                                    .frame(width: 1, height: 1)
                                    .offset(x: 7, y: -6)
                                    .scaleEffect(memorySparkle ? 1.4 : 0.6)
                            }
                        }
                        .frame(width: 30)
                        .animation(
                            .easeInOut(duration: 2.5)
                            .repeatForever(autoreverses: true),
                            value: memorySparkle
                        )

                        Text("回忆录")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // AI 助手设置
                Button(action: {
                    showingAPISettings = true
                }) {
                    HStack {
                        Image(systemName: "brain")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("AI 智能助手")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Text("DeepSeek")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 主题设置
                Button(action: {
                    showingSettings = true
                }) {
                    HStack {
                        Image(systemName: "paintbrush")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("主题与值度设置")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Text(themeManager.currentTheme.rawValue)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 数据管理
    private var dataManagementSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据管理")
                .font(.headline)
                .padding(.horizontal)

            VStack(spacing: 0) {
                // 数据备份
                Button(action: {
                    showingBackupSettings = true
                }) {
                    HStack {
                        Image(systemName: "icloud.and.arrow.up")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("数据备份")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 关于应用
    private var aboutSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("关于")
                .font(.headline)
                .padding(.horizontal)

            VStack(spacing: 0) {
                // 关于应用
                Button(action: {
                    // 关于应用功能
                }) {
                    HStack {
                        Image(systemName: "info.circle")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("关于应用")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 隐私政策
                Button(action: {
                    // 隐私政策功能
                }) {
                    HStack {
                        Image(systemName: "hand.raised")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("隐私政策")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                Divider()
                    .padding(.leading, 46)

                // 用户协议
                Button(action: {
                    // 用户协议功能
                }) {
                    HStack {
                        Image(systemName: "doc.text")
                            .font(.title3)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text("用户协议")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()

                        Text("版本 1.0.0")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // MARK: - 动画控制
    private func startAnimations() {
        // 皇冠旋转动画
        withAnimation(.linear(duration: 8).repeatForever(autoreverses: false)) {
            crownRotation = 360
        }

        // 脉冲发光动画
        withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            glowAnimation.toggle()
        }

        // 闪光动画
        withAnimation(.linear(duration: 3).repeatForever(autoreverses: false)) {
            shimmerOffset = 200
        }

        // 回忆录星点闪烁动画
        withAnimation(.easeInOut(duration: 2.5).repeatForever(autoreverses: true)) {
            memorySparkle.toggle()
        }
    }
}

#Preview {
    NavigationView {
        ProfileView()
            .environmentObject(ThemeManager())
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
                expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
            ))
    }
}
