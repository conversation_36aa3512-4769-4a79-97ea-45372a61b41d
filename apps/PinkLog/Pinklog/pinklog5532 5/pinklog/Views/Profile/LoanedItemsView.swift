import SwiftUI

// 添加触感反馈
import CoreHaptics

// 自定义动画修饰符
struct AnimatedAppearance: ViewModifier {
    var delay: Double
    @State private var opacity: Double = 0
    @State private var offset: CGFloat = 20
    
    func body(content: Content) -> some View {
        content
            .opacity(opacity)
            .offset(y: offset)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(delay)) {
                    opacity = 1
                    offset = 0
                }
            }
    }
}

// 磨砂玻璃效果
struct GlassMorphicBackground: ViewModifier {
    var cornerRadius: CGFloat = 12
    
    func body(content: Content) -> some View {
        content
            .background(
                ZStack {
                    Color.white.opacity(0.1)
                    
                    // 添加轻微噪点
                    Color.white
                        .opacity(0.08)
                        .blur(radius: 10)
                }
                .background(.ultraThinMaterial)
            )
            .cornerRadius(cornerRadius)
    }
}

// 呼吸动画效果
struct BreathingAnimation: ViewModifier {
    @State private var scale: CGFloat = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .onAppear {
                let animation = Animation.easeInOut(duration: 3).repeatForever(autoreverses: true)
                withAnimation(animation) {
                    scale = 1.03
                }
            }
    }
}

// 卡片悬浮效果
struct CardHoverEffect: ViewModifier {
    @State private var isHovering = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isHovering ? 1.02 : 1)
            .shadow(color: .black.opacity(isHovering ? 0.12 : 0.06), 
                   radius: isHovering ? 8 : 4, 
                   x: 0, 
                   y: isHovering ? 5 : 2)
            .onTapGesture {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isHovering = true
                    // 触发触感反馈
                    HapticManager.shared.playLightImpact()
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                            isHovering = false
                        }
                    }
                }
            }
    }
}

// 颜色渐变边框
struct ColoredBorderModifier: ViewModifier {
    var color: Color
    var width: CGFloat = 3
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [color.opacity(0.8), color.opacity(0.4)]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: width)
                    .mask(
                        RoundedRectangle(cornerRadius: 12, style: .continuous)
                            .stroke(lineWidth: width)
                    ),
                alignment: .leading
            )
    }
}

// 环形进度指示器
struct CircularProgressView: View {
    var progress: Double
    var color: Color
    var size: CGFloat = 30
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(lineWidth: 2)
                .opacity(0.3)
                .foregroundColor(color)
            
            Circle()
                .trim(from: 0.0, to: CGFloat(min(self.progress, 1.0)))
                .stroke(style: StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                .foregroundColor(color)
                .rotationEffect(Angle(degrees: 270.0))
                .animation(.linear, value: progress)
            
            Text("\(Int(progress * 100))%")
                .font(.system(size: size * 0.35, weight: .semibold, design: .rounded))
                .foregroundColor(color)
        }
        .frame(width: size, height: size)
    }
}

// 触感反馈管理器
class HapticManager {
    static let shared = HapticManager()
    
    private var engine: CHHapticEngine?
    
    init() {
        prepareHapticEngine()
    }
    
    private func prepareHapticEngine() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else { return }
        
        do {
            engine = try CHHapticEngine()
            try engine?.start()
        } catch {
            print("Haptic engine error: \(error)")
        }
    }
    
    func playLightImpact() {
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
    }
    
    func playMediumImpact() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }
    
    func playSuccessFeedback() {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
    }
}

// 主视图扩展
extension View {
    func animatedAppearance(delay: Double = 0) -> some View {
        modifier(AnimatedAppearance(delay: delay))
    }
    
    func glassMorphic(cornerRadius: CGFloat = 12) -> some View {
        modifier(GlassMorphicBackground(cornerRadius: cornerRadius))
    }
    
    func breathing() -> some View {
        modifier(BreathingAnimation())
    }
    
    
    func coloredBorder(color: Color, width: CGFloat = 3) -> some View {
        modifier(ColoredBorderModifier(color: color, width: width))
    }
}

struct LoanedItemsView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    // 视图状态
    @State private var isRefreshing = false
    @State private var activeAnimation = false
    @State private var hasPulledToRefresh = false
    @State private var selectedRecord: UsageRecord?
    @State private var isNavigating = false
    
    // 筛选状态
    enum FilterStatus: String, CaseIterable, Identifiable {
        case all = "全部"
        case active = "借出中"
        case upcoming = "即将到期"
        case overdue = "已逾期"
        case returned = "已归还"

        var id: String { self.rawValue }

        var icon: String {
            switch self {
            case .all: return "list.bullet"
            case .active: return "arrow.up.forward.circle"
            case .upcoming: return "clock"
            case .overdue: return "exclamationmark.circle"
            case .returned: return "checkmark.circle"
            }
        }

        var color: Color {
            switch self {
            case .all: return .gray
            case .active: return .blue
            case .upcoming: return .orange
            case .overdue: return .red
            case .returned: return .green
            }
        }
    }

    @State private var selectedFilter: FilterStatus = .active
    @State private var searchText = ""
    @State private var previousFilter: FilterStatus? = nil

    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏
            searchBar
                .glassMorphic(cornerRadius: 10)
                .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
                .padding(.vertical, 12)
                .animatedAppearance(delay: 0.1)
                .zIndex(10)

            // 筛选选项
            filterBar
                .glassMorphic()
                .shadow(color: .black.opacity(0.03), radius: 3, x: 0, y: 1)
                .animatedAppearance(delay: 0.2)
                .zIndex(5)

            // 借出物品列表
            if filteredRecords.isEmpty {
                emptyStateView
                    .animatedAppearance(delay: 0.3)
            } else {
                loanedItemsList
                    .refreshable {
                        withAnimation {
                            isRefreshing = true
                            hasPulledToRefresh = true
                        }
                        
                        // 触感反馈
                        HapticManager.shared.playLightImpact()
                        
                        // 模拟刷新操作
                        await refreshData()
                        
                        withAnimation {
                            isRefreshing = false
                        }
                    }
            }
        }
        .background(
            ZStack {
                Color(UIColor.systemBackground)
                    .opacity(0.5)
                
                // 添加轻微的几何图形装饰
                Circle()
                    .fill(Color.accentColor.opacity(0.03))
                    .frame(width: 200, height: 200)
                    .blur(radius: 50)
                    .offset(x: 150, y: -120)
                
                Circle()
                    .fill(Color.purple.opacity(0.03))
                    .frame(width: 250, height: 250)
                    .blur(radius: 60)
                    .offset(x: -150, y: 200)
            }
            .ignoresSafeArea()
        )
        .navigationTitle("已借出物品")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            productViewModel.loadProducts()
            usageViewModel.loadAllLoanRecords()
            animateActive()
        }
        .onChange(of: selectedFilter) { newFilter in
            previousFilter = newFilter
            
            // 触感反馈
            HapticManager.shared.playLightImpact()
        }
    }
    
    // 滚动列表视图
    private var loanedItemsList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(Array(filteredRecords.enumerated()), id: \.element.id) { index, record in
                    loanedItemCard(record, index: index)
                        .animatedAppearance(delay: Double(index) * 0.05 + 0.3)
                        .onTapGesture {
                            HapticManager.shared.playLightImpact()
                            selectedRecord = record
                            isNavigating = true
                        }
                }
            }
            .padding(.horizontal)
            .padding(.top, 16)
            .padding(.bottom, 24)
        }
        .background(
            NavigationLink(
                destination: Group {
                    if let record = selectedRecord, let product = record.product {
                        UsageRecordDetailView(usageRecord: record, product: product, sourceType: .other)
                            .environmentObject(themeManager)
                            .environmentObject(usageViewModel)
                    }
                },
                isActive: $isNavigating,
                label: { EmptyView() }
            )
        )
    }

    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.system(size: 16, weight: .medium))

            TextField("搜索物品或借出对象", text: $searchText)
                .foregroundColor(.primary)
                .font(.system(size: 16))
                .autocapitalization(.none)
                .disableAutocorrection(true)

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    // 触感反馈
                    HapticManager.shared.playLightImpact()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                }
            }
        }
        .padding(12)
        .padding(.horizontal, 4)
        .background(Color.clear)
        .cornerRadius(10)
        .padding(.horizontal)
    }

    // 筛选选项栏
    private var filterBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(FilterStatus.allCases) { status in
                    filterButton(status)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
        }
        .background(Color.clear)
    }
    
    // 筛选按钮
    private func filterButton(_ status: FilterStatus) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                selectedFilter = status
            }
        }) {
            HStack {
                Image(systemName: status.icon)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(selectedFilter == status ? .white : status.color)
                
                Text(status.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(selectedFilter == status ? .white : status.color)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(selectedFilter == status 
                          ? status.color 
                          : status.color.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(status.color.opacity(selectedFilter == status ? 0 : 0.6), lineWidth: 1)
            )
        }
        .scaleEffect(selectedFilter == status ? 1.05 : 1.0)
        .animation(.spring(response: 0.3), value: selectedFilter)
    }
    
    // 自定义动画函数
    private func animateActive() {
        withAnimation(Animation.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            activeAnimation = true
        }
    }
    
    // 模拟刷新数据
    private func refreshData() async {
        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_500_000_000)
        
        // 重新加载数据
        productViewModel.loadProducts()
        usageViewModel.loadAllLoanRecords()
        
        // 成功反馈
        HapticManager.shared.playSuccessFeedback()
    }

    // 借出物品卡片
    private func loanedItemCard(_ record: UsageRecord, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            // 卡片内容
            HStack(spacing: 12) {
                // 产品图片
                productImageView(record.product!)
                    .frame(width: 80, height: 80)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                
                // 产品和借出信息
                VStack(alignment: .leading, spacing: 5) {
                    // 产品名称
                    Text(record.product?.name ?? "未命名产品")
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    // 借出对象
                    HStack(spacing: 4) {
                        Image(systemName: "person")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                        Text(record.borrowerName ?? "未知")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                    
                    // 借出日期
                    if let date = record.date {
                        HStack(spacing: 4) {
                            Image(systemName: "calendar")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                            Text(date.formatted(date: .abbreviated, time: .omitted))
                                .font(.system(size: 13))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // 归还日期信息
                    if let returnDate = record.returnDate {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.green)
                            Text("已归还: \(returnDate.formatted(date: .abbreviated, time: .omitted))")
                                .font(.system(size: 13))
                                .foregroundColor(.green)
                        }
                    } else if let dueDate = record.dueDate {
                        HStack(spacing: 6) {
                            // 时间图标
                            Image(systemName: record.isOverdue ? "exclamationmark.circle.fill" : "clock.fill")
                                .font(.system(size: 12))
                                .foregroundColor(record.isOverdue ? .red : .orange)
                            
                            // 日期文本
                            Text(dueDate.formatted(date: .abbreviated, time: .omitted))
                                .font(.system(size: 13))
                                .foregroundColor(record.isOverdue ? .red : .orange)
                            
                            // 日期进度或状态
                            if let daysRemaining = record.daysRemaining {
                                if record.isOverdue {
                                    Text("已逾期\(abs(daysRemaining))天")
                                        .font(.system(size: 11, weight: .bold))
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(record.isOverdue ? 
                                                    Capsule().fill(Color.red) :
                                                    Capsule().fill(Color.orange))
                                } else {
                                    // 环形进度指示器
                                    CircularProgressView(
                                        progress: max(0, min(1, Double(record.initialDaysRemaining - daysRemaining) / Double(record.initialDaysRemaining))),
                                        color: daysRemaining <= 3 ? .orange : .blue,
                                        size: 24
                                    )
                                }
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // 状态标记
                statusBadge(for: record)
                    .offset(y: -30)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 14)
        }
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.secondarySystemBackground).opacity(0.95))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.secondary.opacity(0.1), lineWidth: 0.5)
        )
        .coloredBorder(color: statusColor(for: record))
        .contentShape(Rectangle()) // 确保整个区域可点击
    }
    
    // 获取状态颜色
    private func statusColor(for record: UsageRecord) -> Color {
        if let _ = record.returnDate {
            return .green
        } else if record.isOverdue {
            return .red
        } else if let daysRemaining = record.daysRemaining, daysRemaining <= 3 {
            return .orange
        } else {
            return .blue
        }
    }

    // 产品图片视图
    private func productImageView(_ product: Product) -> some View {
        Group {
            if let imageData = product.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 80, height: 80)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            } else {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.gray.opacity(0.2),
                                    Color.gray.opacity(0.3)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    Image(systemName: "cube.fill")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding(20)
                        .foregroundColor(.gray.opacity(0.6))
                }
                .frame(width: 80, height: 80)
                .cornerRadius(12)
            }
        }
    }

    // 状态标记
    private func statusBadge(for record: UsageRecord) -> some View {
        let status = record.loanStatus
        
        return Text(status.rawValue)
            .font(.system(size: 12, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .padding(.horizontal, 10)
            .padding(.vertical, 5)
            .background(
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [status.color, status.color.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: status.color.opacity(0.3), radius: 2, x: 0, y: 1)
            )
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 25) {
            // 动态图标
            ZStack {
                Circle()
                    .fill(selectedFilter.color.opacity(0.1))
                    .frame(width: 110, height: 110)
                    .breathing()
                
                Circle()
                    .fill(selectedFilter.color.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: selectedFilter.icon)
                    .font(.system(size: 40, weight: .light))
                    .foregroundColor(selectedFilter.color)
            }
            .padding(.bottom, 5)

            Text(emptyStateTitle)
                .font(.system(size: 22, weight: .semibold, design: .rounded))
                .foregroundColor(.primary)

            Text(emptyStateMessage)
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .padding(.bottom, 10)
            
            // 如果进行了搜索但没有结果，显示清除搜索按钮
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    HapticManager.shared.playLightImpact()
                }) {
                    Text("清除搜索")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            Capsule()
                                .fill(selectedFilter.color)
                        )
                }
                .padding(.top, 5)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground).opacity(0.01))
    }

    // 空状态标题
    private var emptyStateTitle: String {
        switch selectedFilter {
        case .all:
            return "暂无借出记录"
        case .active:
            return "暂无借出中物品"
        case .upcoming:
            return "暂无即将到期物品"
        case .overdue:
            return "暂无逾期物品"
        case .returned:
            return "暂无已归还记录"
        }
    }

    // 空状态消息
    private var emptyStateMessage: String {
        switch selectedFilter {
        case .all:
            return searchText.isEmpty ? "您还没有任何借出记录" : "没有找到匹配的借出记录"
        case .active:
            return searchText.isEmpty ? "您当前没有借出的物品" : "没有找到匹配的借出中物品"
        case .upcoming:
            return "您没有即将到期的借出物品"
        case .overdue:
            return "您没有逾期未归还的物品，太棒了！"
        case .returned:
            return "您还没有已归还的借出记录"
        }
    }

    // 筛选后的借出记录列表
    private var filteredRecords: [UsageRecord] {
        // 首先获取所有借出类型的记录
        let allLoanRecords = usageViewModel.allLoanRecords

        // 根据搜索文本筛选
        let searchFiltered = searchText.isEmpty ? allLoanRecords : allLoanRecords.filter { record in
            let productName = record.product?.name?.lowercased() ?? ""
            let borrowerName = record.borrowerName?.lowercased() ?? ""
            let searchLower = searchText.lowercased()

            return productName.contains(searchLower) || borrowerName.contains(searchLower)
        }

        // 根据状态筛选
        switch selectedFilter {
        case .all:
            return searchFiltered
        case .active:
            return searchFiltered.filter { $0.returnDate == nil }
        case .upcoming:
            return searchFiltered.filter { record in
                guard let daysRemaining = record.daysRemaining, record.returnDate == nil else {
                    return false
                }
                return daysRemaining <= 3 && daysRemaining >= 0
            }
        case .overdue:
            return searchFiltered.filter { $0.isOverdue }
        case .returned:
            return searchFiltered.filter { $0.returnDate != nil }
        }
    }
}

// 为UsageRecord扩展一个计算属性
extension UsageRecord {
    var initialDaysRemaining: Int {
        guard let dueDate = self.dueDate, let date = self.date else { return 30 }
        let components = Calendar.current.dateComponents([.day], from: date, to: dueDate)
        return components.day ?? 30
    }
}

#Preview {
    NavigationView {
        LoanedItemsView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
                expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}
