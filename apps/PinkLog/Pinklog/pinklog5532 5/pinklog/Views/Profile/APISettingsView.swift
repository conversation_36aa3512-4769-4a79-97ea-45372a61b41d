import SwiftUI

/// API 设置界面
struct APISettingsView: View {
    @StateObject private var apiService = DeepSeekAPIService()
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var apiKey: String = ""
    @State private var showingAPIKeyInput = false
    @State private var isConfiguring = false
    @State private var showingCostInfo = false
    @State private var showingHelpSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 配置状态卡片
                    configurationStatusCard
                    
                    // API Key 配置
                    if !apiService.isConfigured {
                        apiKeyConfigurationCard
                    } else {
                        configuredAPICard
                    }
                    
                    // 模型选择
                    if apiService.isConfigured {
                        modelSelectionCard
                    }
                    
                    // 使用统计
                    if apiService.isConfigured {
                        usageStatsCard
                    }
                    
                    // 帮助信息
                    helpCard
                }
                .padding()
            }
            .navigationTitle("AI 助手设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                if apiService.isConfigured {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            dismiss()
                        }
                        .fontWeight(.semibold)
                    }
                }
            }
            .sheet(isPresented: $showingHelpSheet) {
                APIHelpView()
            }
        }
    }
    
    // MARK: - 配置状态卡片
    private var configurationStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: apiService.isConfigured ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(apiService.isConfigured ? .green : .orange)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(apiService.isConfigured ? "AI 助手已配置" : "AI 助手未配置")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(apiService.isConfigured ? "您可以使用智能分析和月度报告功能" : "配置 DeepSeek API 以启用 AI 功能")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            if let error = apiService.lastError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    
                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundColor(.red)
                    
                    Spacer()
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - API Key 配置卡片
    private var apiKeyConfigurationCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("配置 DeepSeek API")
                .font(.headline)
            
            Text("请输入您的 DeepSeek API Key 以启用 AI 智能助手功能。")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                SecureField("请输入 API Key (sk-...)", text: $apiKey)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                
                Button(action: configureAPI) {
                    HStack {
                        if apiService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "key.fill")
                        }
                        
                        Text(apiService.isLoading ? "验证中..." : "配置 API Key")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(themeManager.currentTheme.primaryColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(apiKey.isEmpty || apiService.isLoading)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 已配置 API 卡片
    private var configuredAPICard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("API 配置")
                    .font(.headline)
                
                Spacer()
                
                Button("重新配置") {
                    apiService.clearConfiguration()
                    apiKey = ""
                }
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.primaryColor)
            }
            
            HStack {
                Image(systemName: "key.fill")
                    .foregroundColor(.green)
                
                Text("API Key 已配置")
                    .font(.subheadline)
                
                Spacer()
                
                Text("••••••••")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 模型选择卡片
    private var modelSelectionCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("模型选择")
                .font(.headline)
            
            VStack(spacing: 8) {
                ForEach(DeepSeekModel.allCases) { model in
                    Button(action: {
                        apiService.selectedModel = model
                    }) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(model.displayName)
                                    .font(.subheadline)
                                    .foregroundColor(.primary)
                                
                                Text(model.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            if apiService.selectedModel == model {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(apiService.selectedModel == model ? 
                                      themeManager.currentTheme.primaryColor.opacity(0.1) : 
                                      Color.clear)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(apiService.selectedModel == model ? 
                                        themeManager.currentTheme.primaryColor : 
                                        Color.gray.opacity(0.3), lineWidth: 1)
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 使用统计卡片
    private var usageStatsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("使用统计")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    showingCostInfo = true
                }) {
                    Image(systemName: "info.circle")
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
            }
            
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("总 Token 数")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(apiService.totalTokensUsed)")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("预估费用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("¥\(String(format: "%.4f", apiService.estimatedCost))")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
        .alert("费用说明", isPresented: $showingCostInfo) {
            Button("确定") { }
        } message: {
            Text("费用基于 DeepSeek 官方定价计算，实际费用以 DeepSeek 平台为准。优惠时段（00:30-08:30）享受折扣价格。")
        }
    }
    
    // MARK: - 帮助卡片
    private var helpCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("需要帮助？")
                .font(.headline)
            
            Button(action: {
                showingHelpSheet = true
            }) {
                HStack {
                    Image(systemName: "questionmark.circle")
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                    
                    Text("如何获取 DeepSeek API Key")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(UIColor.tertiarySystemBackground))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 私有方法
    private func configureAPI() {
        Task {
            await apiService.configure(apiKey: apiKey)
        }
    }
}

// MARK: - API 帮助视图
struct APIHelpView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("如何获取 DeepSeek API Key")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("1. 访问 DeepSeek 平台")
                            .font(.headline)
                        
                        Text("前往 https://platform.deepseek.com/ 注册账户")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("2. 创建 API Key")
                            .font(.headline)
                        
                        Text("在控制台中点击「API Keys」，然后点击「Create API Key」")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("3. 复制 API Key")
                            .font(.headline)
                        
                        Text("复制生成的 API Key（以 sk- 开头），粘贴到上方输入框中")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("4. 充值账户")
                            .font(.headline)
                        
                        Text("在 DeepSeek 平台充值以使用 API 服务。建议先充值少量金额进行测试。")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("定价信息")
                            .font(.headline)
                        
                        Text("• DeepSeek Chat: 输入 ¥2/百万tokens，输出 ¥8/百万tokens")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("• DeepSeek Reasoner: 输入 ¥4/百万tokens，输出 ¥16/百万tokens")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("• 优惠时段（00:30-08:30）享受折扣价格")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("API 帮助")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    APISettingsView()
        .environmentObject(ThemeManager())
}
