import SwiftUI

struct AddExpenseView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let product: Product

    @State private var amount: String = ""
    @State private var date = Date()
    @State private var selectedExpenseType: ExpenseType?
    @State private var notes: String = ""

    @State private var showingExpenseTypeSelector = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 费用类型选项
    @State private var expenseTypes: [ExpenseType] = []

    var body: some View {
        NavigationView {
            Form {
                // 产品信息
                Section(header: Text("产品信息")) {
                    HStack {
                        Text(product.name ?? "未命名产品")
                            .font(.headline)

                        Spacer()

                        if let brand = product.brand, !brand.isEmpty {
                            Text(brand)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                // 费用信息
                Section(header: Text("费用信息")) {
                    TextField("金额 *", text: $amount)
                        .keyboardType(.decimalPad)

                    DatePicker("日期 *", selection: $date, displayedComponents: [.date])

                    Button(action: {
                        showingExpenseTypeSelector = true
                    }) {
                        HStack {
                            Text("费用类型 *")
                                .foregroundColor(.primary)

                            Spacer()

                            Text(selectedExpenseType?.name ?? "请选择")
                                .foregroundColor(selectedExpenseType == nil ? .secondary : .primary)

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                // 备注
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }

                // 保存按钮
                Section {
                    Button(action: saveExpense) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.primaryColor)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("添加费用")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .sheet(isPresented: $showingExpenseTypeSelector) {
                ExpenseTypeSelectorView(selectedExpenseType: $selectedExpenseType)
                    .environment(\.managedObjectContext, viewContext)
                    .environmentObject(themeManager)
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                loadExpenseTypes()
            }
        }
    }

    private func loadExpenseTypes() {
        // 这里应该从数据库加载费用类型
        // 暂时使用模拟数据
        let repository = ExpenseTypeRepository(context: viewContext)
        expenseTypes = repository.fetchAll()

        if expenseTypes.isEmpty {
            // 如果没有费用类型，创建一些默认类型
            let types = ["维修", "保养", "配件", "耗材", "电池更换", "软件订阅", "干洗", "其他"]
            let icons = ["wrench.and.screwdriver", "sparkles", "puzzlepiece", "cart", "battery.100", "app.badge", "bubbles.and.sparkles", "ellipsis.circle"]

            for (index, typeName) in types.enumerated() {
                let expenseType = ExpenseType(context: viewContext)
                expenseType.id = UUID()
                expenseType.name = typeName
                expenseType.icon = icons[index]
                expenseTypes.append(expenseType)
            }

            try? viewContext.save()
        }
    }

    private func saveExpense() {
        // 验证输入
        if amount.isEmpty {
            alertMessage = "请输入金额"
            showingAlert = true
            return
        }

        guard let amountValue = Double(amount) else {
            alertMessage = "金额格式不正确"
            showingAlert = true
            return
        }

        if selectedExpenseType == nil {
            alertMessage = "请选择费用类型"
            showingAlert = true
            return
        }

        // 保存费用记录
        let success = usageViewModel.addExpense(
            amount: amountValue,
            date: date,
            expenseType: selectedExpenseType!,
            notes: notes.isEmpty ? nil : notes
        )

        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = usageViewModel.errorMessage ?? "添加费用记录失败"
            showingAlert = true
        }
    }
}

struct AddExpenseView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.name = "MacBook Pro"
        product.brand = "Apple"
        product.model = "M1 Pro"
        product.price = 14999
        product.purchaseDate = Date()

        return AddExpenseView(product: product)
            .environment(\.managedObjectContext, context)
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: context),
                expenseRepository: RelatedExpenseRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}
