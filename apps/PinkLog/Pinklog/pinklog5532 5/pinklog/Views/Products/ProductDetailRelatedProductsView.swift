import SwiftUI
import CoreData

struct ProductDetailRelatedProductsView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel
    
    let product: Product
    @StateObject private var linkViewModel: ProductLinkViewModel
    
    @State private var showingRelatedProductsSheet = false
    @State private var selectedLink: ProductLink?
    @State private var isShowingJointUsage = false
    
    init(product: Product, context: NSManagedObjectContext) {
        self.product = product
        self._linkViewModel = StateObject(wrappedValue: ProductLinkViewModel(context: context))
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和管理按钮
            HStack {
                Text("关联产品")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    showingRelatedProductsSheet = true
                }) {
                    Label("管理", systemImage: "link")
                        .font(.caption)
                }
            }
            
            // 关联产品展示
            if linkViewModel.isLoading {
                ProgressView()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else if linkViewModel.links.isEmpty {
                Text("暂无关联产品")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(12)
            } else {
                // 展示按类型分组的关联产品
                relatedProductsByTypeView
            }
        }
        .onAppear {
            // 确保关联数据在视图显示时就加载
            linkViewModel.setCurrentProduct(product)
            linkViewModel.loadLinks(for: product)
        }
        .sheet(isPresented: $showingRelatedProductsSheet) {
            EnhancedRelatedProductsView(product: product, context: product.managedObjectContext!) {
                // 当关联组创建成功后，刷新关联产品列表
                linkViewModel.loadLinks(for: product)
            }
                .environmentObject(productViewModel)
                .environmentObject(tagViewModel)
                .environmentObject(themeManager)
        }
        .sheet(item: $selectedLink) { link in
            ZStack {
                if isShowingJointUsage {
                    AddJointUsageRecordView(link: link, onReturn: {
                        withAnimation(.easeInOut(duration: 0.4)) {
                            isShowingJointUsage = false
                        }
                    })
                        .environment(\.managedObjectContext, product.managedObjectContext!)
                        .environmentObject(usageViewModel)
                        .transition(.opacity)
                } else {
                    linkDetailView(link: link)
                        .transition(.opacity)
                }
            }
            .animation(.easeInOut(duration: 0.4), value: isShowingJointUsage)
        }
    }
    
    // 按类型分组的关联产品视图
    private var relatedProductsByTypeView: some View {
        let allLinks = linkViewModel.links
        
        // 1. 将链接分为带groupID和不带groupID的
        let linksWithGroupID = allLinks.filter { $0.groupID != nil && !($0.groupID!.isEmpty) }
        let linksWithoutGroupID = allLinks.filter { $0.groupID == nil || $0.groupID!.isEmpty }

        return ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 2. 按 groupID 对链接进行分组，并准备排序
                let groupedByGroupID = Dictionary(grouping: linksWithGroupID, by: { $0.groupID! })
                
                // 将字典转换为可排序的数组，(groupID, creationDate)
                let sortedGroupedLinks = groupedByGroupID.map { (groupID, links) -> (String, Date) in
                    // 使用组内最新的链接创建时间作为排序依据
                    let latestDate = links.map { $0.createdAt ?? Date.distantPast }.max() ?? Date.distantPast
                    return (groupID, latestDate)
                }.sorted { $0.1 > $1.1 } // 按日期降序排序

                // 3. 显示按 groupID 分组的链接
                ForEach(sortedGroupedLinks, id: \.0) { groupID, _ in
                    if let linksInGroup = groupedByGroupID[groupID], let firstLink = linksInGroup.first {
                        let groupName = firstLink.groupName ?? "未命名组"
                        let relationshipType = firstLink.relationshipTypeEnum
                        
                        VStack(alignment: .leading, spacing: 8) {
                            // 关系类型标题（显示为"组名|类型"）
                            HStack {
                                Image(systemName: relationshipType.icon)
                                    .foregroundColor(relationshipType.color)
                                
                                Text("\(groupName) | \(relationshipType.rawValue)")
                                    .font(.subheadline)
                                    .foregroundColor(relationshipType.color)
                            }
                            
                            // 该类型的关联产品
                            ForEach(linksInGroup, id: \.id) { link in
                                linkRow(link)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 10)
                        .background(relationshipType.color.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                
                // 4. 为没有 groupID 的链接提供更完善的回退显示逻辑
                let linksWithOldGroupName = linksWithoutGroupID.filter { $0.groupName != nil && !($0.groupName!.isEmpty) }
                let singleLinks = linksWithoutGroupID.filter { $0.groupName == nil || $0.groupName!.isEmpty }

                // 4a. 显示旧的、按名称分组的链接
                let groupedByOldName = Dictionary(grouping: linksWithOldGroupName) { link in
                    return "\(link.groupName!)||\(link.relationshipTypeEnum.rawValue)"
                }

                let sortedOldGroups = groupedByOldName.map { (key, links) -> (String, Date) in
                    let latestDate = links.map { $0.createdAt ?? Date.distantPast }.max() ?? Date.distantPast
                    return (key, latestDate)
                }.sorted { $0.1 > $1.1 }

                ForEach(sortedOldGroups, id: \.0) { combinedKey, _ in
                    if let links = groupedByOldName[combinedKey], let firstLink = links.first {
                        let parts = combinedKey.split(separator: "||")
                        let groupName = String(parts[0])
                        let relationshipType = firstLink.relationshipTypeEnum
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: relationshipType.icon)
                                    .foregroundColor(relationshipType.color)
                                Text("\(groupName) | \(relationshipType.rawValue)")
                                    .font(.subheadline)
                                    .foregroundColor(relationshipType.color)
                            }
                            ForEach(links, id: \.id) { link in
                                linkRow(link)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 10)
                        .background(relationshipType.color.opacity(0.1))
                        .cornerRadius(12)
                    }
                }

                // 4b. 显示单个链接，按类型分组
                if !singleLinks.isEmpty {
                    let groupedByType = Dictionary(grouping: singleLinks) { $0.relationshipTypeEnum }
                    
                    ForEach(groupedByType.keys.sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { relationshipType in
                        if let links = groupedByType[relationshipType] {
                            VStack(alignment: .leading, spacing: 8) {
                                // 关系类型标题
                                HStack {
                                    Image(systemName: relationshipType.icon)
                                        .foregroundColor(relationshipType.color)
                                    
                                    Text(relationshipType.rawValue)
                                        .font(.subheadline)
                                        .foregroundColor(relationshipType.color)
                                }
                                
                                // 该类型的关联产品
                                ForEach(links, id: \.id) { link in
                                    linkRow(link)
                                }
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 10)
                            .background(relationshipType.color.opacity(0.1))
                            .cornerRadius(12)
                        }
                    }
                }
            }
        }
    }
    
    // 链接行视图
    private func linkRow(_ link: ProductLink) -> some View {
        let relatedProduct = link.sourceProduct == product ? link.targetProduct : link.sourceProduct
        
        return HStack {
            // 产品图片或图标
            if let rProduct = relatedProduct, let imageData = rProduct.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .cornerRadius(8)
            } else {
                Image(systemName: "cube.box")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 40, height: 40)
                    .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                    .cornerRadius(8)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // 产品名称和品牌
                if let rProduct = relatedProduct {
                    Text(rProduct.name ?? "未命名产品")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    if let brand = rProduct.brand, !brand.isEmpty {
                        Text(brand + (rProduct.model != nil ? " · \(rProduct.model!)" : ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 关系信息
            VStack(alignment: .trailing, spacing: 2) {
                // 关系强度
                HStack(spacing: 2) {
                    if link.strength > 0 {
                        ForEach(1...Int(link.strength), id: \.self) { _ in
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                        }
                    }
                }
                
                // 使用次数
                if link.usageCount > 0 {
                    Text("已一起使用\(link.usageCount)次")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // 双向关系指示器
                if link.isBidirectional {
                    Image(systemName: "arrow.left.arrow.right")
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(8)
        .onTapGesture {
            selectedLink = link
        }
    }
    
    // 链接详情视图
    private func linkDetailView(link: ProductLink) -> some View {
        // 确保使用最新的链接数据
        let relatedProduct = link.sourceProduct == product ? link.targetProduct : link.sourceProduct
        
        return NavigationView {
            Form {
                // 关联产品信息
                Section(header: Text("关联产品信息")) {
                    if let rProduct = relatedProduct {
                        NavigationLink(destination: ProductDetailView(product: rProduct)) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(rProduct.name ?? "未命名产品")
                                    .font(.headline)
                                
                                if let brand = rProduct.brand, !brand.isEmpty {
                                    Text(brand + (rProduct.model != nil ? " · \(rProduct.model!)" : ""))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }
                
                // 关系类型和组名
                Section(header: Text("关系类型")) {
                    HStack {
                        Image(systemName: link.relationshipTypeEnum.icon)
                            .foregroundColor(link.relationshipTypeEnum.color)
                        Text(link.relationshipTypeEnum.rawValue)
                            
                        Spacer()
                            
                        if link.isBidirectional {
                            Text("双向关系")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.2))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                        }
                    }
                        
                    Text(link.relationshipTypeEnum.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                    
                // 关联组名称（如果有）
                if let groupName = link.groupName, !groupName.isEmpty {
                    Section(header: Text("关联组名称")) {
                        Text(groupName)
                            .font(.body)
                    }
                }
                
                // 关系强度
                Section(header: Text("关系强度")) {
                    HStack {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= link.strength ? "star.fill" : "star")
                                .foregroundColor(index <= link.strength ? .yellow : .gray)
                        }
                    }
                }
                
                // 使用统计
                Section(header: Text("使用统计")) {
                    HStack {
                        Text("共同使用次数")
                        Spacer()
                        Text("\(link.usageCount)")
                            .fontWeight(.bold)
                    }
                    
                    if let lastUsed = link.lastUsedTogether {
                        HStack {
                            Text("最后一次共同使用")
                            Spacer()
                            Text(lastUsed, style: .relative)
                                .fontWeight(.bold)
                        }
                    }
                    
                    Button(action: {
                        // 添加动画效果
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isShowingJointUsage = true
                        }
                    }) {
                        Label("记录共同使用", systemImage: "checkmark.circle")
                            .contentTransition(.symbolEffect(.replace))
                    }
                }
                
                // 备注
                if let notes = link.notes, !notes.isEmpty {
                    Section(header: Text("备注")) {
                        Text(notes)
                    }
                }
            }
            .navigationTitle("关联关系详情")
            .navigationBarItems(trailing: Button("完成") {
                selectedLink = nil
            })
        }
    }
}