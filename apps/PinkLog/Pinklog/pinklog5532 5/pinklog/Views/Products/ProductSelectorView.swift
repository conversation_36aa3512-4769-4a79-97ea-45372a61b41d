import SwiftUI

struct ProductSelectorView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    var excludedProductId: UUID? = nil
    var onSelect: ((Product) -> Void)?
    var multiSelection: Bool = false
    
    @State private var searchText = ""
    @State private var selectedCategory: Category?
    @State private var selectedProducts: Set<Product> = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 类别筛选
                categoryFilterBar
                
                // 已选产品
                if multiSelection && !selectedProducts.isEmpty {
                    selectedProductsSection
                }
                
                // 产品列表
                if filteredProducts.isEmpty {
                    emptyStateView
                } else {
                    List {
                        ForEach(filteredProducts) { product in
                            productRow(product)
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("选择产品")
            .navigationBarItems(
                leading: But<PERSON>("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: multiSelection ? Button("完成") {
                    // 处理多选完成
                    presentationMode.wrappedValue.dismiss()
                } : nil
            )
            .onAppear {
                productViewModel.loadProducts()
                productViewModel.loadCategories()
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索产品", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 类别筛选栏
    private var categoryFilterBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 全部类别
                Button(action: {
                    selectedCategory = nil
                }) {
                    Text("全部")
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(selectedCategory == nil ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground))
                        .foregroundColor(selectedCategory == nil ? .white : .primary)
                        .cornerRadius(8)
                }
                
                // 各个类别
                ForEach(productViewModel.categories) { category in
                    Button(action: {
                        selectedCategory = category
                    }) {
                        Text(category.name ?? "")
                            .padding(.vertical, 6)
                            .padding(.horizontal, 12)
                            .background(selectedCategory?.id == category.id ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground))
                            .foregroundColor(selectedCategory?.id == category.id ? .white : .primary)
                            .cornerRadius(8)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
    }
    
    // 已选产品部分
    private var selectedProductsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("已选产品")
                .font(.headline)
                .padding(.horizontal)
                .padding(.top, 8)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Array(selectedProducts), id: \.self) { product in
                        HStack(spacing: 4) {
                            Text(product.name ?? "")
                                .font(.subheadline)
                            
                            Button(action: {
                                selectedProducts.remove(product)
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.caption)
                            }
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(Color(UIColor.tertiarySystemBackground))
                        .cornerRadius(8)
                    }
                }
                .padding(.horizontal)
            }
            .padding(.bottom, 8)
            
            Divider()
        }
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "cube.box")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("未找到产品")
                .font(.title2)
                .foregroundColor(.primary)
            
            Text("尝试更改搜索条件或添加新产品")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // 产品行
    private func productRow(_ product: Product) -> some View {
        HStack {
            // 产品图片
            if let imageData = product.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 50, height: 50)
                    .cornerRadius(8)
            } else {
                Image(systemName: "photo")
                    .font(.title2)
                    .foregroundColor(.secondary)
                    .frame(width: 50, height: 50)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
            }
            
            // 产品信息
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name ?? "未命名产品")
                    .font(.headline)
                
                if let brand = product.brand, !brand.isEmpty {
                    Text(brand + (product.model != nil ? " · \(product.model!)" : ""))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 多选模式下显示复选框
            if multiSelection {
                Image(systemName: selectedProducts.contains(product) ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedProducts.contains(product) ? themeManager.currentTheme.primaryColor : .secondary)
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            if multiSelection {
                if selectedProducts.contains(product) {
                    selectedProducts.remove(product)
                } else {
                    selectedProducts.insert(product)
                }
            } else {
                onSelect?(product)
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
    
    // 筛选后的产品
    private var filteredProducts: [Product] {
        var products = productViewModel.products
        
        // 排除指定产品
        if let excludedId = excludedProductId {
            products = products.filter { $0.id != excludedId }
        }
        
        // 按类别筛选
        if let category = selectedCategory {
            products = products.filter { $0.category?.id == category.id }
        }
        
        // 按搜索文本筛选
        if !searchText.isEmpty {
            products = products.filter {
                ($0.name?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                ($0.brand?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                ($0.model?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
        
        return products
    }
}

struct ProductSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        ProductSelectorView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}
