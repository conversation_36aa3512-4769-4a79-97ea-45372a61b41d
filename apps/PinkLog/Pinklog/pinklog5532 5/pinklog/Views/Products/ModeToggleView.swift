import SwiftUI

// This file contains the improved mode toggle view implementation
// We'll use this to replace the existing mode toggle in AddProductView.swift

// Sample implementation for modeButton helper function
func modeButton(title: String, icon: String, description: String, isSelected: Bool, themeColor: Color, action: @escaping () -> Void) -> some View {
    Button(action: action) {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .font(.headline)
                Text(title)
                    .font(.headline)
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(isSelected ? themeColor.opacity(0.15) : Color.gray.opacity(0.1))
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(isSelected ? themeColor : Color.clear, lineWidth: 2)
        )
    }
    .buttonStyle(PlainButtonStyle())
}

// Sample implementation for improved mode toggle view
func improvedModeToggleView(isQuickMode: Binding<Bool>, currentStep: Int, themeColor: Color, showAlert: @escaping (String) -> Void, onModeChange: @escaping () -> Void) -> some View {
    VStack(spacing: 8) {
        HStack {
            Text("录入模式")
                .font(.headline)
            Spacer()
        }
        
        HStack(spacing: 12) {
            modeButton(title: "快速录入", icon: "bolt.fill", description: "仅填写核心字段", isSelected: isQuickMode.wrappedValue, themeColor: themeColor) {
                if currentStep > 1 {
                    // 如果不是第一步，需要提示用户
                    showAlert("切换录入模式会重置当前表单，确定继续吗？")
                } else if !isQuickMode.wrappedValue {
                    // 只有当当前不是快速模式时才切换
                    withAnimation {
                        isQuickMode.wrappedValue = true
                        onModeChange()
                    }
                }
            }
            
            modeButton(title: "详细录入", icon: "list.bullet.clipboard", description: "填写所有字段", isSelected: !isQuickMode.wrappedValue, themeColor: themeColor) {
                if currentStep > 1 {
                    // 如果不是第一步，需要提示用户
                    showAlert("切换录入模式会重置当前表单，确定继续吗？")
                } else if isQuickMode.wrappedValue {
                    // 只有当当前是快速模式时才切换
                    withAnimation {
                        isQuickMode.wrappedValue = false
                        onModeChange()
                    }
                }
            }
        }
    }
    .padding(.horizontal)
    .padding(.top, 16)
    .padding(.bottom, 8)
}