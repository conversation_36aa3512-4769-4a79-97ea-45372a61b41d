import SwiftUI

struct RelatedProductsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    let product: Product
    
    var body: some View {
        // 直接调用增强版关联产品视图
        EnhancedRelatedProductsView(product: product, context: product.managedObjectContext!)
            .environmentObject(productViewModel)
            .environmentObject(themeManager)
    }
}

struct RelatedProductsView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        
        // 创建主产品（如外套）
        let product = Product(context: context)
        product.id = UUID()
        product.name = "冬季羽绒外套"
        product.brand = "北面"
        product.price = 1999
        product.purchaseDate = Date()
        
        // 创建关联产品1（裤子）
        let relatedProduct1 = Product(context: context)
        relatedProduct1.id = UUID()
        relatedProduct1.name = "户外保暖长裤"
        relatedProduct1.brand = "Columbia"
        relatedProduct1.price = 699
        relatedProduct1.purchaseDate = Date()
        
        // 创建关联产品2（鞋子）
        let relatedProduct2 = Product(context: context)
        relatedProduct2.id = UUID()
        relatedProduct2.name = "防水徒步鞋"
        relatedProduct2.brand = "Salomon"
        relatedProduct2.price = 899
        relatedProduct2.purchaseDate = Date()
        
        // 创建类别
        let category = Category(context: context)
        category.id = UUID()
        category.name = "户外装备"
        product.category = category
        relatedProduct1.category = category
        relatedProduct2.category = category
        
        // 保存上下文以确保所有对象都有有效的ID
        try? context.save()
        
        // 创建产品链接（一对多关系）
        let link1 = ProductLink(context: context)
        link1.id = UUID()
        link1.sourceProduct = product
        link1.targetProduct = relatedProduct1
        link1.relationshipType = "complement" // 互补品
        link1.strength = 4
        link1.isBidirectional = true
        link1.createdAt = Date()
        
        let link2 = ProductLink(context: context)
        link2.id = UUID()
        link2.sourceProduct = product
        link2.targetProduct = relatedProduct2
        link2.relationshipType = "recommended_with" // 推荐一起使用
        link2.strength = 5
        link2.isBidirectional = true
        link2.createdAt = Date()
        
        try? context.save()
        
        return RelatedProductsView(product: product)
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: context),
                categoryRepository: CategoryRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}
