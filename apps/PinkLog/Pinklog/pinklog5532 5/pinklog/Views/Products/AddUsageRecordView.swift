import SwiftUI
import AVFoundation
import PhotosUI
import MarkdownUI

struct AddUsageRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let product: Product

    @State private var date = Date()
    @State private var scenario = ""
    @State private var satisfaction = 3
    @State private var notes = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 整合记录类型和使用类型选择
    @State private var recordType: RecordType = .personalSimple

    // 多媒体状态
    @State private var selectedImages: [UIImage] = []
    @State private var showingImagePicker = false
    @State private var audioRecordings: [URL] = []
    @State private var isRecording = false
    @State private var audioRecorder: AVAudioRecorder?
    @State private var audioPlayer: AVAudioPlayer?
    @State private var isPlaying = false
    @State private var playingRecordingIndex: Int?
    @State private var title = ""
    @State private var showMarkdownPreview = false
    @State private var emotionalValue = 0 // 情感价值评分
    @State private var memories = ""  // 关联回忆
    @State private var selectedPhotosItems: [PhotosPickerItem] = []

    // 预览图片相关状态
    @State private var previewingImage: UIImage? = nil

    // 借出相关状态
    @State private var borrowerName = ""
    @State private var contactInfo = ""
    @State private var dueDate = Date().addingTimeInterval(60*60*24*7) // 默认一周后
    
    // 消耗记录相关状态
    @State private var consumedQuantity: String = ""
    @State private var selectedUnit: String = "个"
    @State private var consumptionScenario: String = ""
    
    // 滑杆估算相关状态
    @AppStorage("preferredInputMethod") private var useSliderEstimation: Bool = false
    @State private var remainingPercentage: Double = 100.0

    // 计算当前库存百分比和滑杆范围
    private var currentStockPercentage: Double {
        guard product.isConsumable == true else { return 100.0 }
        return product.stockPercentage * 100.0
    }



    // 场景选项
    private let scenarios = ["日常使用", "工作", "学习", "旅行", "聚会", "运动", "其他"]

    // 记录类型枚举 - 整合了记录模式和使用类型
    enum RecordType: String, CaseIterable, Identifiable {
        case personalSimple = "简单记录"
        case personalStory = "物品故事"
        case consumption = "消耗记录"
        case loan = "借出记录"

        var id: String { self.rawValue }

        var icon: String {
            switch self {
            case .personalSimple: return "doc.text"
            case .personalStory: return "book.fill"
            case .consumption: return "minus.circle"
            case .loan: return "person.2"
            }
        }

        var color: Color {
            switch self {
            case .personalSimple: return .blue
            case .personalStory: return .orange
            case .consumption: return .cyan
            case .loan: return .green
            }
        }

        var isPersonal: Bool {
            self == .personalSimple || self == .personalStory || self == .consumption
        }

        var isStory: Bool {
            self == .personalStory
        }
        
        var isConsumption: Bool {
            self == .consumption
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部选择记录类型
                recordTypeSelector
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color(UIColor.systemBackground))
                    .shadow(color: Color.black.opacity(0.05), radius: 3, y: 2)

                ScrollView {
                    VStack(spacing: 16) {
                        // 添加顶部间距
                        Color.clear.frame(height: 12)

                        // 标题（仅在物品故事模式下显示）
                        if recordType.isStory {
                            customCard {
                                TextField("给这个使用体验起个标题", text: $title)
                                    .font(.headline)
                                    .padding(.vertical, 8)
                            }
                            .padding(.horizontal)
                        }

                        // 基本信息
                        basicInfoCard
                            .padding(.horizontal)

                        // 满意度评价（仅物品故事模式时显示独立卡片）
                        if recordType == .personalStory {
                            satisfactionCard
                                .padding(.horizontal)
                        }

                        // 情感连接（仅在物品故事模式下显示）
                        if recordType.isStory {
                            emotionalConnectionCard
                                .padding(.horizontal)
                        }

                        // 多媒体内容（仅在物品故事模式下显示）
                        if recordType.isStory {
                            mediaContentCards
                                .padding(.horizontal)
                        }

                        // 备注（支持Markdown）
                        markdownNotesCard
                            .padding(.horizontal)

                        // 保存按钮
                        saveButton
                            .padding(.horizontal)
                            .padding(.bottom, 20)
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
            .navigationBarTitle(recordType.rawValue, displayMode: .inline)
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                usageViewModel.setCurrentProduct(product)
                setupAudioRecorder()

                // 根据物品类型设置默认记录类型
                if product.isConsumable == true {
                    recordType = .consumption
                    // 初始化滑杆位置为当前库存百分比
                    remainingPercentage = currentStockPercentage
                    // 智能同步单位：优先使用消耗单位，否则使用购买单位
                    if product.usesDualUnitSystem {
                        selectedUnit = product.consumptionUnit
                    } else if let productUnit = product.unitType, !productUnit.isEmpty {
                        selectedUnit = productUnit
                    }
                } else {
                    recordType = .personalSimple
                }
            }
            .sheet(isPresented: $showingImagePicker) {
                if let imageToPreview = previewingImage {
                    ImagePreviewView(image: imageToPreview) {
                        showingImagePicker = false
                    }
                }
            }
        }
    }

    // 根据物品类型过滤可用的记录类型
    private var availableRecordTypes: [RecordType] {
        if product.isConsumable == true {
            // 消耗型物品：消耗记录、物品故事、借出记录
            return [.consumption, .personalStory, .loan]
        } else {
            // 非消耗型物品：简单记录、物品故事、借出记录
            return [.personalSimple, .personalStory, .loan]
        }
    }
    
    // 记录类型选择器
    private var recordTypeSelector: some View {
        HStack(spacing: 12) {
            ForEach(availableRecordTypes) { type in
                Button {
                    withAnimation {
                        recordType = type
                    }
                } label: {
                    VStack(spacing: 6) {
                        Image(systemName: type.icon)
                            .font(.system(size: 22))
                            .frame(height: 24)

                        Text(type.rawValue)
                            .font(.system(size: 12))
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(recordType == type ? type.color.opacity(0.1) : Color.clear)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(recordType == type ? type.color : Color.clear, lineWidth: 2)
                    )
                    .foregroundColor(recordType == type ? type.color : .gray)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    // 基本信息卡片
    private var basicInfoCard: some View {
        customCard {
            VStack(spacing: 12) {
                if recordType.isPersonal {
                    // 个人使用基本信息
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 24)
                        DatePicker("使用日期", selection: $date, displayedComponents: .date)
                    }

                    // 消耗记录专用界面 - 使用 ConsumptionRecordSheet 的实现
                    if recordType == .consumption {
                        Divider()
                        
                        // 产品信息卡片
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Group {
                                    if let imageData = product.images,
                                       let uiImage = UIImage(data: imageData) {
                                        Image(uiImage: uiImage)
                                            .resizable()
                                            .aspectRatio(contentMode: .fill)
                                    } else {
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.gray.opacity(0.3))
                                    }
                                }
                                .frame(width: 50, height: 50)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(product.name ?? "未知产品")
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                    
                                    if product.isConsumable {
                                        Text("当前库存: \(formatQuantity(product.currentQuantity)) \(product.unitType ?? "")")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                
                                Spacer()
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                        
                        Divider()
                        
                        // 消耗量输入方式选择
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("消耗量输入方式")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Spacer()
                                
                                Button(action: {
                                    useSliderEstimation.toggle()
                                    if useSliderEstimation {
                                        // 切换到滑杆模式时，清空直接输入的数量，并重置滑杆到当前库存
                                        consumedQuantity = ""
                                        remainingPercentage = currentStockPercentage
                                    } else {
                                        // 切换到直接输入模式时，重置滑杆到当前库存
                                        remainingPercentage = currentStockPercentage
                                    }
                                }) {
                                    HStack(spacing: 6) {
                                        Image(systemName: useSliderEstimation ? "slider.horizontal.3" : "keyboard")
                                            .font(.caption)
                                        Text(useSliderEstimation ? "滑杆估算" : "直接输入")
                                            .font(.caption)
                                    }
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 6)
                                    .background(Color.blue.opacity(0.1))
                                    .foregroundColor(.blue)
                                    .cornerRadius(8)
                                }
                            }
                            
                            if useSliderEstimation {
                                // 滑杆估算模式
                                VStack(alignment: .leading, spacing: 12) {
                                    Text("使用后预估剩余量")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    VStack(spacing: 8) {
                                        // 重新设计的优雅库存控制界面
                                        VStack(spacing: 20) {
                                            // 环形进度条显示库存状态
                                            ZStack {
                                                // 背景圆环
                                                Circle()
                                                    .stroke(Color.gray.opacity(0.2), lineWidth: 12)
                                                    .frame(width: 120, height: 120)

                                                // 已消耗部分（红色）
                                                Circle()
                                                    .trim(from: 0, to: (100 - currentStockPercentage) / 100)
                                                    .stroke(Color.red.opacity(0.3), lineWidth: 12)
                                                    .rotationEffect(.degrees(-90))
                                                    .frame(width: 120, height: 120)

                                                // 当前库存部分（蓝色）
                                                Circle()
                                                    .trim(from: (100 - currentStockPercentage) / 100, to: (100 - remainingPercentage) / 100)
                                                    .stroke(Color.blue, lineWidth: 12)
                                                    .rotationEffect(.degrees(-90))
                                                    .frame(width: 120, height: 120)
                                                    .animation(.easeInOut(duration: 0.3), value: remainingPercentage)

                                                // 中心数据显示
                                                VStack(spacing: 4) {
                                                    Text("\(String(format: "%.0f", remainingPercentage))%")
                                                        .font(.title2)
                                                        .fontWeight(.bold)
                                                        .foregroundColor(.primary)

                                                    Text("剩余")
                                                        .font(.caption)
                                                        .foregroundColor(.secondary)

                                                    let consumedPercent = currentStockPercentage - remainingPercentage
                                                    if consumedPercent > 0 {
                                                        Text("-\(String(format: "%.0f", consumedPercent))%")
                                                            .font(.caption)
                                                            .foregroundColor(.orange)
                                                    }
                                                }
                                            }

                                            // 简化的滑杆控制
                                            VStack(spacing: 8) {
                                                HStack {
                                                    Text("调整用量")
                                                        .font(.subheadline)
                                                        .fontWeight(.medium)
                                                        .foregroundColor(.primary)

                                                    Spacer()

                                                    // 计算消耗量（考虑双单位系统和用户选择的单位）
                                                    let consumedAmount: Double = {
                                                        let consumedPercent = currentStockPercentage - remainingPercentage
                                                        let consumedRatio = consumedPercent / currentStockPercentage

                                                        if product.usesDualUnitSystem && selectedUnit == product.consumptionUnit {
                                                            // 如果用户选择消耗单位，基于消耗单位的库存计算
                                                            return product.currentQuantityInConsumptionUnit * consumedRatio
                                                        } else {
                                                            // 如果用户选择购买单位，基于购买单位的库存计算
                                                            return product.actualCurrentQuantity * consumedRatio
                                                        }
                                                    }()

                                                    if consumedAmount > 0 {
                                                        Text("\(formatQuantity(consumedAmount)) \(selectedUnit)")
                                                            .font(.subheadline)
                                                            .fontWeight(.medium)
                                                            .foregroundColor(.orange)
                                                    }
                                                }

                                                Slider(value: $remainingPercentage, in: 0...currentStockPercentage, step: 1) {
                                                    Text("剩余百分比")
                                                }
                                                .accentColor(.blue)
                                                .onChange(of: remainingPercentage) { oldValue, newValue in
                                                    // 根据滑杆值自动计算消耗量（考虑双单位系统）
                                                    let currentPercent = currentStockPercentage
                                                    let consumedPercent = currentPercent - newValue
                                                    let consumedRatio = consumedPercent / currentPercent

                                                    let consumedAmount: Double = {
                                                        if product.usesDualUnitSystem && selectedUnit == product.consumptionUnit {
                                                            // 如果用户选择消耗单位，基于消耗单位的库存计算
                                                            return product.currentQuantityInConsumptionUnit * consumedRatio
                                                        } else {
                                                            // 如果用户选择购买单位，基于购买单位的库存计算
                                                            return product.actualCurrentQuantity * consumedRatio
                                                        }
                                                    }()

                                                    consumedQuantity = String(format: "%.2f", max(0, consumedAmount))
                                                }
                                            }
                                        }



                                    }
                                    .padding()
                                    .background(Color(.systemGray6))
                                    .cornerRadius(12)
                                }
                            } else {
                                // 直接输入模式
                                HStack {
                                    Image(systemName: "minus.circle")
                                        .foregroundColor(.cyan)
                                        .frame(width: 24)
                                    TextField("消耗数量", text: $consumedQuantity)
                                        .keyboardType(.decimalPad)
                                    
                                    // 单位选择
                                    Menu {
                                        ForEach(["个", "毫升", "克", "片", "粒", "滴", "勺", "杯", "瓶", "包", "盒", "袋", "米", "升", "千克", "剂", "份", "张", "卷", "管"], id: \.self) { unit in
                                            Button(unit) {
                                                selectedUnit = unit
                                            }
                                        }
                                    } label: {
                                        HStack {
                                            Text(selectedUnit)
                                                .foregroundColor(.primary)
                                            Image(systemName: "chevron.down")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(Color(UIColor.secondarySystemBackground))
                                        )
                                    }
                                }
                            }
                        }
                        
                        // 快速选择按钮（仅在直接输入模式下显示）
                        if product.isConsumable && !useSliderEstimation {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("快速选择")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                                    ForEach([0.25, 0.5, 1.0, 2.0], id: \.self) { amount in
                                        Button(action: {
                                            consumedQuantity = String(amount)
                                        }) {
                                            Text(formatQuantity(amount))
                                                .font(.caption)
                                                .foregroundColor(.blue)
                                                .padding(.horizontal, 12)
                                                .padding(.vertical, 6)
                                                .background(Color.blue.opacity(0.1))
                                                .cornerRadius(8)
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 滑杆模式下的快速百分比选择
                        if product.isConsumable && useSliderEstimation {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("快速选择剩余量")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)

                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                                    // 动态生成快速选择按钮，基于当前库存百分比
                                    ForEach(generateQuickSelectionPercentages(), id: \.self) { percentage in
                                        Button(action: {
                                            remainingPercentage = Double(percentage)
                                        }) {
                                            Text("\(percentage)%")
                                                .font(.caption)
                                                .foregroundColor(.blue)
                                                .padding(.horizontal, 12)
                                                .padding(.vertical, 6)
                                                .background(Color.blue.opacity(0.1))
                                                .cornerRadius(8)
                                        }
                                        .disabled(Double(percentage) > currentStockPercentage)
                                        .opacity(Double(percentage) > currentStockPercentage ? 0.3 : 1.0)
                                    }
                                }
                            }
                        }
                        
                        // 预览信息
                        if product.isConsumable, let quantity = Double(consumedQuantity), quantity > 0 {
                            Divider()

                            VStack(alignment: .leading, spacing: 12) {
                                Text("本次消耗预览")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                VStack(spacing: 8) {
                                    HStack {
                                        Text("本次消耗量:")
                                            .foregroundColor(.secondary)
                                        Spacer()
                                        Text("\(formatQuantity(quantity)) \(selectedUnit)")
                                            .fontWeight(.medium)
                                            .foregroundColor(.primary)
                                    }

                                    // 计算剩余量（需要考虑单位转换）
                                    let (remainingQuantity, displayUnit): (Double, String) = {
                                        if product.usesDualUnitSystem && selectedUnit == product.consumptionUnit {
                                            // 如果使用消耗单位，需要转换
                                            let currentInConsumptionUnit = product.currentQuantityInConsumptionUnit
                                            return (max(0, currentInConsumptionUnit - quantity), product.consumptionUnit)
                                        } else {
                                            // 使用购买单位
                                            return (max(0, product.currentQuantity - quantity), product.unitType ?? "")
                                        }
                                    }()

                                    HStack {
                                        Text("使用后剩余:")
                                            .foregroundColor(.secondary)
                                        Spacer()
                                        Text("\(formatQuantity(remainingQuantity)) \(displayUnit)")
                                            .fontWeight(.medium)
                                            .foregroundColor(remainingQuantity <= (product.minStockAlert) ? .red : .primary)
                                    }

                                    // 如果是双单位系统，显示转换信息
                                    if product.usesDualUnitSystem {
                                        HStack {
                                            Text("库存转换:")
                                                .foregroundColor(.secondary)
                                            Spacer()
                                            Text(product.formattedStockDisplay)
                                                .font(.caption)
                                                .foregroundColor(.blue)
                                        }
                                    }

                                    if let estimatedDays = calculateEstimatedDays(remaining: remainingQuantity) {
                                        HStack {
                                            Text("预计剩余天数:")
                                                .foregroundColor(.secondary)
                                            Spacer()
                                            Text("\(estimatedDays) 天")
                                                .fontWeight(.medium)
                                                .foregroundColor(estimatedDays <= 7 ? .red : (estimatedDays <= 14 ? .orange : .primary))
                                        }
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                        }

                        Divider()

                        // 满意度评价
                        VStack(alignment: .leading, spacing: 12) {
                            Text("使用体验")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            VStack(alignment: .leading, spacing: 12) {
                                Text("满意度")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 8) {
                                    ForEach(1...5, id: \.self) { index in
                                        Button(action: {
                                            satisfaction = index
                                            let haptic = UIImpactFeedbackGenerator(style: .light)
                                            haptic.impactOccurred()
                                        }) {
                                            Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                                .foregroundColor(index <= satisfaction ? .yellow : .gray)
                                                .font(.title2)
                                        }
                                    }
                                    
                                    Spacer()
                                    
                                    Text("\(satisfaction)/5")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        
                        Divider()
                        
                        // 使用场景
                        VStack(alignment: .leading, spacing: 8) {
                            Text("使用场景")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            TextField("描述使用场景...", text: $consumptionScenario)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            // 常用场景快速选择
                            let commonScenarios = ["日常使用", "特殊场合", "紧急情况", "测试试用"]
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(commonScenarios, id: \.self) { scenario in
                                    Button(action: {
                                        consumptionScenario = scenario
                                    }) {
                                        Text(scenario)
                                            .font(.caption)
                                            .foregroundColor(consumptionScenario == scenario ? .white : .blue)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(consumptionScenario == scenario ? Color.blue : Color.blue.opacity(0.1))
                                            .cornerRadius(8)
                                    }
                                }
                            }
                        }

                    }
                    // 在物品故事模式下，使用场景会与满意度一起显示在满意度卡片中
                    else if recordType == .personalSimple {
                        Divider()

                        // 满意度和使用场景水平排列
                        HStack(alignment: .top, spacing: 16) {
                            // 满意度评价
                            VStack(alignment: .leading, spacing: 8) {
                                Text("使用满意度")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)

                                HStack {
                                    ForEach(1...5, id: \.self) { index in
                                        Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                            .foregroundColor(index <= satisfaction ? .yellow : .gray)
                                            .font(.title2)
                                            .onTapGesture {
                                                satisfaction = index
                                                let haptic = UIImpactFeedbackGenerator(style: .light)
                                                haptic.impactOccurred()
                                            }
                                    }
                                }
                            }
                            .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)

                            // 使用场景
                            VStack(alignment: .trailing, spacing: 8) { // 整个VStack右对齐
                                Text("使用场景")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .trailing) // 确保标题始终右对齐

                                // 使用圆角矩形样式
                                Menu {
                                    Button("请选择") {
                                        scenario = ""
                                    }
                                    ForEach(scenarios, id: \.self) { scene in
                                        Button(scene) {
                                            scenario = scene
                                        }
                                    }
                                } label: {
                                    HStack {
                                        Text(scenario.isEmpty ? "请选择" : scenario)
                                            .foregroundColor(.primary)
                                        Image(systemName: "chevron.down")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color(UIColor.secondarySystemBackground))
                                    )
                                }
                            }
                            .frame(minWidth: 0, maxWidth: .infinity, alignment: .trailing)
                        }
                        .padding(.top, 4)
                    }
                } else {
                    // 借出信息
                    HStack {
                        Image(systemName: "person")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 24)
                        TextField("借出对象姓名", text: $borrowerName)
                    }

                    Divider()

                    HStack {
                        Image(systemName: "phone")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 24)
                        TextField("联系方式", text: $contactInfo)
                            .keyboardType(.phonePad)
                    }

                    Divider()

                    HStack {
                        Image(systemName: "calendar.badge.clock")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 24)
                        DatePicker("预计归还日期", selection: $dueDate, displayedComponents: .date)
                    }
                }
            }
        }
    }

    // 满意度评价卡片
    private var satisfactionCard: some View {
        customCard {
            if recordType == .personalStory {
                // 物品故事模式：满意度和使用场景水平排列
                HStack(alignment: .top, spacing: 16) {
                    // 上半部分：使用满意度
                        VStack(alignment: .leading, spacing: 8) {
                            Text("使用满意度")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)

                            HStack {
                                ForEach(1...5, id: \.self) { index in
                                    Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                        .foregroundColor(index <= satisfaction ? .yellow : .gray)
                                        .font(.title2)
                                        .onTapGesture {
                                            satisfaction = index
                                        let haptic = UIImpactFeedbackGenerator(style: .light)
                                        haptic.impactOccurred()
                                    }
                            }
                        }
                    }
                    .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)

                    // 下半部分：使用场景
                    VStack(alignment: .trailing, spacing: 8) { // 整个VStack右对齐
                        Text("使用场景")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .trailing) // 确保标题始终右对齐

                        // 圆角矩形样式
                        Menu {
                            Button("请选择") {
                                scenario = ""
                            }
                            ForEach(scenarios, id: \.self) { scene in
                                Button(scene) {
                                    scenario = scene
                                }
                            }
                        } label: {
                            HStack {
                                Text(scenario.isEmpty ? "请选择" : scenario)
                                    .foregroundColor(.primary)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(UIColor.secondarySystemBackground))
                            )
                        }
                    }
                    .frame(minWidth: 0, maxWidth: .infinity, alignment: .trailing)
                }
            } else {
                // 简单记录模式下，满意度已在基本信息卡片中显示
                EmptyView()
            }
        }
    }

    // 情感连接卡片
    private var emotionalConnectionCard: some View {
        customCard {
            VStack(alignment: .leading, spacing: 12) {
                // 删除"情感连接"标题

                // 情感价值评分
                VStack(alignment: .leading, spacing: 8) {
                    Text("情感价值")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    HStack {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= emotionalValue ? "heart.fill" : "heart")
                                .foregroundColor(index <= emotionalValue ? .red : .gray)
                                .font(.title2)
                                .onTapGesture {
                                    emotionalValue = index
                                    let haptic = UIImpactFeedbackGenerator(style: .light)
                                    haptic.impactOccurred()
                                }
                        }
                        Spacer()
                    }
                }

                // 回忆关联
                VStack(alignment: .leading, spacing: 8) {
                    Text("关联回忆")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    TextEditor(text: $memories)
                        .frame(minHeight: 100)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3))
                        )
                        .overlay(
                            Group {
                                if memories.isEmpty {
                                    Text("这个物品让你想起了什么？")
                                        .foregroundColor(.gray)
                                        .padding(.leading, 5)
                                        .padding(.top, 8)
                                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                }
                            }
                        )
                }
            }
        }
    }

    // 多媒体内容卡片
    private var mediaContentCards: some View {
        Group {
            // 图片区域
            customCard {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "photo")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                        Text("照片记忆")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                    }

                    if selectedImages.isEmpty {
                        // 使用PhotosPicker替换原来的按钮
                        PhotosPicker(
                            selection: $selectedPhotosItems,
                            matching: .images,
                            photoLibrary: .shared()
                        ) {
                            ZStack {
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [5]))
                                    .frame(height: 180)

                                VStack(spacing: 8) {
                                    Image(systemName: "plus.circle")
                                        .font(.largeTitle)
                                    Text("添加图片")
                                }
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                            }
                        }
                        .onChange(of: selectedPhotosItems) { _, newItems in
                            loadImages(from: newItems)
                        }
                    } else {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 10) {
                                ForEach(0..<selectedImages.count, id: \.self) { index in
                                    ZStack(alignment: .topTrailing) {
                                        Image(uiImage: selectedImages[index])
                                            .resizable()
                                            .scaledToFill()
                                            .frame(width: 140, height: 180)
                                            .clipShape(RoundedRectangle(cornerRadius: 12))
                                            .onTapGesture {
                                                // 点击预览图片
                                                previewImage(index)
                                            }

                                        Button(action: {
                                            selectedImages.remove(at: index)
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .font(.title3)
                                                .foregroundColor(.white)
                                                .background(Circle().fill(Color.black.opacity(0.6)))
                                                .clipShape(Circle())
                                        }
                                        .padding(6)
                                    }
                                }

                                // 添加更多图片的按钮
                                PhotosPicker(
                                    selection: $selectedPhotosItems,
                                    matching: .images,
                                    photoLibrary: .shared()
                                ) {
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.gray.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [5]))
                                            .frame(width: 140, height: 180)

                                        Image(systemName: "plus")
                                            .font(.largeTitle)
                                            .foregroundColor(themeManager.currentTheme.primaryColor)
                                    }
                                }
                                .onChange(of: selectedPhotosItems) { _, newItems in
                                    loadImages(from: newItems)
                                }
                            }
                        }
                    }
                }
            }

            // 音频区域
            customCard {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "mic")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                        Text("语音回忆")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                    }

                    // 录音按钮
                    Button(action: {
                        if isRecording {
                            stopRecording()
                        } else {
                            startRecording()
                        }
                    }) {
                        HStack {
                            Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                                .font(.title)
                                .foregroundColor(isRecording ? .red : themeManager.currentTheme.primaryColor)

                            Text(isRecording ? "停止录音" : "开始录音")
                                .fontWeight(.medium)
                        }
                            .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(UIColor.secondarySystemBackground))
                        .cornerRadius(12)
                    }

                    // 已录制的音频列表
                    if !audioRecordings.isEmpty {
                        ForEach(audioRecordings.indices, id: \.self) { index in
                            HStack {
                                Button(action: {
                                    togglePlayRecording(at: index)
                                }) {
                                    HStack {
                                        Image(systemName: (playingRecordingIndex == index && isPlaying) ? "pause.circle.fill" : "play.circle.fill")
                                            .foregroundColor(themeManager.currentTheme.primaryColor)
                                            .font(.title3)

                                        Text("录音 \(index + 1)")
                                            .foregroundColor(.primary)
                                    }
                                }

                                Spacer()

                                Button(action: {
                                    deleteRecording(at: index)
                                }) {
                                    Image(systemName: "trash")
                                        .foregroundColor(.red)
                                }
                            }
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(Color(UIColor.secondarySystemBackground))
                            .cornerRadius(8)
                        }
                    }
                }
            }
        }
        .onDisappear {
            // 视图消失时停止录音和播放
            if isRecording {
                stopRecording()
            }
            stopPlayback()
        }
    }

    // Markdown备注卡片
    private var markdownNotesCard: some View {
        customCard {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "note.text")
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                    Text("备注")
                        .font(.headline)

                    Spacer()

                    Button(action: {
                        showMarkdownPreview.toggle()
                    }) {
                        Image(systemName: showMarkdownPreview ? "doc.text" : "eye")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                }

                if showMarkdownPreview {
                    // Markdown预览
                    ScrollView {
                        VStack(alignment: .leading) {
                            MarkdownView(text: notes)
                        }
                        .frame(minHeight: 200)
                    }
                    .padding(8)
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(8)
                } else {
                    // Markdown编辑器
                    VStack(alignment: .leading) {
                        TextEditor(text: $notes)
                            .font(.system(.body, design: .monospaced))
                            .frame(minHeight: 200)
                            .padding(8)
                            .background(Color(UIColor.secondarySystemBackground))
                            .cornerRadius(8)
                            .overlay(
                                Group {
                                    if notes.isEmpty {
                                        Text("支持Markdown格式，记录使用体验...")
                                            .foregroundColor(.gray)
                                            .padding(.horizontal, 13)
                                            .padding(.top, 16)
                                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                    }
                                }
                            )

                        // Markdown快捷按钮
                        HStack(spacing: 12) {
                            markdownButton("# ", tooltip: "标题")
                            markdownButton("**", tooltip: "粗体")
                            markdownButton("*", tooltip: "斜体")
                            markdownButton("> ", tooltip: "引用")
                            markdownButton("- ", tooltip: "列表")
                            markdownButton("```", tooltip: "代码")
                            Spacer()
                        }
                        .padding(.top, 8)
                    }
                }
            }
        }
    }

    // 保存按钮
    private var saveButton: some View {
        Button(action: saveUsageRecord) {
            HStack {
                Spacer()
                Text("保存\(recordType.rawValue)")
                    .fontWeight(.semibold)
                Spacer()
            }
            .foregroundColor(.white)
            .padding(.vertical, 14)
            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
            .cornerRadius(12)
            .shadow(color: isFormValid ? themeManager.currentTheme.primaryColor.opacity(0.3) : Color.clear, radius: 5, x: 0, y: 3)
        }
        .disabled(!isFormValid)
    }

    // 卡片视图辅助函数
    private func customCard<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            content()
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // Markdown按钮
    private func markdownButton(_ markup: String, tooltip: String) -> some View {
        Button(action: {
            insertMarkdown(markup)
        }) {
            Text(markup.count <= 2 ? markup : String(markup.prefix(1)))
                .font(.system(size: 16, weight: .medium, design: .monospaced))
                .padding(.horizontal, 10)
                .padding(.vertical, 5)
                .background(Color(UIColor.tertiarySystemBackground))
                .cornerRadius(6)
        }
        .help(tooltip)
    }

    // 插入Markdown
    private func insertMarkdown(_ markup: String) {
        if markup == "```" {
            notes.append("```\n\n```")
            // 这里无法直接控制光标位置，但可以在将来以原生方式实现
        } else if markup == "**" {
            notes.append("**  **")
            // 理想情况下，光标应该放在中间的两个空格处
        } else if markup == "*" {
            notes.append("* *")
            // 理想情况下，光标应该放在中间的空格处
        } else {
            notes.append(markup)
        }
    }

    // 表单验证
    private var isFormValid: Bool {
        switch recordType {
        case .personalSimple, .personalStory:
            return true // 简单记录模式下个人使用记录没有必填字段
        case .consumption:
            return !consumedQuantity.isEmpty // 消耗记录必须填写消耗量
        case .loan:
            return !borrowerName.isEmpty // 借出记录必须填写借出对象
        }
    }

    // 预览图片方法
    private func previewImage(_ index: Int) {
        guard index < selectedImages.count else { return }
        previewingImage = selectedImages[index]
        showingImagePicker = true
    }

    // 加载图片方法
    private func loadImages(from items: [PhotosPickerItem]) {
        Task {
            for item in items {
                if let data = try? await item.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    DispatchQueue.main.async {
                        self.selectedImages.append(image)
                    }
                }
            }
            // 清空选择，以便用户可以再次选择相同的图片
            DispatchQueue.main.async {
                self.selectedPhotosItems = []
            }
        }
    }

    // 设置音频录制器
    private func setupAudioRecorder() {
        let audioSession = AVAudioSession.sharedInstance()

        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("无法设置音频会话: \(error.localizedDescription)")
        }
    }

    // 开始录音
    private func startRecording() {
        // 停止任何正在播放的音频
        stopPlayback()

        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.record, mode: .default)
            try audioSession.setActive(true)

            let documentPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let audioFilename = documentPath.appendingPathComponent("\(Date().timeIntervalSince1970).m4a")

            print("录音将保存到: \(audioFilename.path)")

            let settings = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]

            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.prepareToRecord()
            audioRecorder?.record()
            isRecording = true
        } catch {
            print("无法开始录音: \(error.localizedDescription)")
            alertMessage = "无法开始录音"
            showingAlert = true
        }
    }

    // 停止录音
    private func stopRecording() {
        audioRecorder?.stop()
        isRecording = false

        if let recorder = audioRecorder, let url = recorder.url as URL? {
            audioRecordings.append(url)
            print("录音已保存: \(url.path)")

            // 重新设置音频会话为播放模式
            do {
                let audioSession = AVAudioSession.sharedInstance()
                try audioSession.setCategory(.playback, mode: .default)
                try audioSession.setActive(true)
            } catch {
                print("重置音频会话失败: \(error)")
            }
        }

        audioRecorder = nil
    }

    // 播放/暂停录音
    private func togglePlayRecording(at index: Int) {
        guard index < audioRecordings.count else { return }

        if playingRecordingIndex == index && isPlaying {
            // 如果当前正在播放该录音，则暂停
            stopPlayback()
        } else {
            // 如果没有播放该录音，则开始播放
            playRecording(at: index)
        }
    }

    // 播放录音
    private func playRecording(at index: Int) {
        // 先停止之前的播放
        stopPlayback()

        let url = audioRecordings[index]
        print("尝试播放录音: \(url.path)")

        do {
            let audioSession = AVAudioSession.sharedInstance()
            // 修复：defaultToSpeaker选项只能与playAndRecord类别一起使用
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker])
            try audioSession.setActive(true)

            audioPlayer = try AVAudioPlayer(contentsOf: url)
            if let player = audioPlayer {
                player.delegate = PlayerDelegate.shared
                player.prepareToPlay()

                // 设置播放完成的回调
                PlayerDelegate.shared.onFinish = { [self] in
                    DispatchQueue.main.async {
                        self.isPlaying = false
                        self.playingRecordingIndex = nil
                    }
                }

                let playSuccess = player.play()
                if playSuccess {
                    isPlaying = true
                    playingRecordingIndex = index
                    print("开始播放录音")
                } else {
                    print("播放失败: player.play()返回false")
                    alertMessage = "播放录音失败"
                    showingAlert = true
                }
            } else {
                print("播放失败: audioPlayer为nil")
                alertMessage = "播放录音失败"
                showingAlert = true
            }
        } catch {
            print("播放录音失败: \(error.localizedDescription)")
            alertMessage = "播放录音失败"
            showingAlert = true
        }
    }

    // 停止播放
    private func stopPlayback() {
        if let player = audioPlayer, player.isPlaying {
            player.stop()
        }
        audioPlayer = nil
        isPlaying = false
        playingRecordingIndex = nil
    }

    // 删除录音
    private func deleteRecording(at index: Int) {
        // 如果正在播放该录音，先停止播放
        if playingRecordingIndex == index {
            stopPlayback()
        }

        let fileManager = FileManager.default
        do {
            try fileManager.removeItem(at: audioRecordings[index])
            audioRecordings.remove(at: index)
            print("已删除录音")
        } catch {
            print("无法删除录音: \(error.localizedDescription)")
            alertMessage = "无法删除录音"
            showingAlert = true
        }
    }

    // 格式化数量显示
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return String(format: "%.0f", quantity)
        } else {
            return String(format: "%.1f", quantity)
        }
    }

    // 将String单位转换为ConsumptionUnit枚举
    private func stringToConsumptionUnit(_ unitString: String) -> UsageRecord.ConsumptionUnit {
        switch unitString {
        case "个": return .piece
        case "毫升": return .ml
        case "克": return .gram
        case "片": return .tablet
        case "粒": return .capsule
        case "滴": return .drop
        case "勺": return .spoon
        case "杯": return .cup
        case "瓶": return .bottle
        case "包": return .pack
        case "盒": return .box
        case "袋": return .bag
        case "米": return .meter
        case "升": return .liter
        case "千克": return .kilogram
        case "剂": return .dose
        case "份": return .serving
        case "张": return .sheet
        case "卷": return .roll
        case "管": return .tube
        default: return .piece
        }
    }

    // 保存使用记录
    private func saveUsageRecord() {
        switch recordType {
        case .personalSimple, .personalStory:
            savePersonalUsageRecord()
        case .loan:
            saveLoanRecord()
        case .consumption:
            saveConsumptionRecord()
        }
    }

    // 保存个人使用记录
    private func savePersonalUsageRecord() {
        if recordType.isStory {
            // 保存物品故事记录
            let success = usageViewModel.addStoryRecord(
                date: date,
                title: title.isEmpty ? nil : title,
                satisfaction: Int16(satisfaction),
                emotionalValue: Int16(emotionalValue),
                memories: memories.isEmpty ? nil : memories,
                notes: notes.isEmpty ? nil : notes,
                scenario: scenario.isEmpty ? nil : scenario,
                images: selectedImages.isEmpty ? nil : selectedImages,
                audioRecordings: audioRecordings.isEmpty ? nil : audioRecordings
            )

            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = "添加物品故事记录失败"
                showingAlert = true
            }
        } else {
            // 保存简单使用记录
            let success = usageViewModel.addUsageRecord(
                date: date,
                satisfaction: Int16(satisfaction),
                notes: notes.isEmpty ? nil : notes,
                duration: nil,
                scenario: scenario.isEmpty ? nil : scenario,
                usageType: "personal"
            )

            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = "添加使用记录失败"
                showingAlert = true
            }
        }
    }

    // 保存借出记录
    private func saveLoanRecord() {
        // 在借出模式下使用当前日期作为借出日期
        let loanDate = Date()

        let success = usageViewModel.addLoanRecord(
            date: loanDate,  // 使用当前日期作为借出日期
            borrowerName: borrowerName,
            contactInfo: contactInfo.isEmpty ? nil : contactInfo,
            dueDate: dueDate,
            notes: notes.isEmpty ? nil : notes
        )

        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "添加借出记录失败"
            showingAlert = true
        }
    }

    // MARK: - 辅助方法
    
    private func calculateEstimatedDays(remaining: Double) -> Int? {
        guard product.isConsumable, remaining > 0 else { return nil }
        
        // 基于历史消耗记录计算平均日消耗量
        let consumptionRecords: [Double] = product.usageRecords?.compactMap { record in
            guard let usageRecord = record as? UsageRecord,
                  usageRecord.consumedQuantity > 0 else { return nil }
            return usageRecord.consumedQuantity
        } ?? []
        
        if consumptionRecords.isEmpty {
            // 如果没有历史记录，使用产品设置的消耗率
            let dailyRate = product.consumptionRate > 0 ? product.consumptionRate : 1.0
            return Int(remaining / dailyRate)
        }
        
        // 计算平均消耗量
        let totalConsumption = consumptionRecords.reduce(0.0) { $0 + $1 }
        let averageConsumption = totalConsumption / Double(consumptionRecords.count)
        
        // 假设平均每天使用一次
        return Int(remaining / averageConsumption)
    }
    
    // 保存消耗记录
    private func saveConsumptionRecord() {
        // 验证消耗量输入
        guard let quantity = Double(consumedQuantity), quantity > 0 else {
            alertMessage = "请输入有效的消耗数量"
            showingAlert = true
            return
        }
        
        // 验证库存是否充足（考虑双单位系统）
        let availableQuantity: Double = {
            if product.usesDualUnitSystem && selectedUnit == product.consumptionUnit {
                // 如果使用消耗单位，使用转换后的库存量
                return product.currentQuantityInConsumptionUnit
            } else {
                // 使用购买单位的库存量
                return product.actualCurrentQuantity
            }
        }()

        guard quantity <= availableQuantity else {
            let unitName = product.usesDualUnitSystem && selectedUnit == product.consumptionUnit ?
                          product.consumptionUnit : (product.unitType ?? "")
            alertMessage = "消耗量不能超过当前库存（当前库存：\(String(format: "%.1f", availableQuantity))\(unitName)）"
            showingAlert = true
            return
        }
        
        Task {
            do {
                // 创建 ConsumableViewModel 实例
                let consumableViewModel = ConsumableViewModel()
                
                try await consumableViewModel.recordConsumption(
                    for: product,
                    consumedQuantity: quantity,
                    unit: stringToConsumptionUnit(selectedUnit),
                    satisfaction: Int16(satisfaction),
                    notes: notes.isEmpty ? "" : notes,
                    scenario: consumptionScenario.isEmpty ? "" : consumptionScenario
                )
                
                await MainActor.run {
                    presentationMode.wrappedValue.dismiss()
                }
            } catch {
                await MainActor.run {
                    alertMessage = "记录消耗失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }

    // MARK: - 辅助方法

    /// 生成快速选择百分比数组，基于当前库存百分比
    private func generateQuickSelectionPercentages() -> [Int] {
        let currentStock = Int(currentStockPercentage)
        var percentages: [Int] = []

        // 根据当前库存百分比生成合适的快速选择选项
        if currentStock >= 90 {
            percentages = [currentStock, 75, 50, 25]
        } else if currentStock >= 75 {
            percentages = [currentStock, 50, 25, 10]
        } else if currentStock >= 50 {
            percentages = [currentStock, 25, 10, 0]
        } else if currentStock >= 25 {
            percentages = [currentStock, 10, 5, 0]
        } else {
            percentages = [currentStock, Int(Double(currentStock) * 0.5), Int(Double(currentStock) * 0.25), 0]
        }

        // 过滤掉重复值和无效值，并排序
        return Array(Set(percentages))
            .filter { $0 >= 0 && $0 <= currentStock }
            .sorted(by: >)
    }
}

// MARK: - 图片预览视图
struct ImagePreviewView: View {
    let image: UIImage
    let onDismiss: () -> Void

    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset = CGSize.zero
    @State private var lastOffset = CGSize.zero

    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)

            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .scaleEffect(scale)
                .offset(offset)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            let delta = value / lastScale
                            lastScale = value
                            scale *= delta
                        }
                        .onEnded { _ in
                            lastScale = 1.0
                            if scale < 1.0 {
                                withAnimation {
                                    scale = 1.0
                                }
                            } else if scale > 5.0 {
                                withAnimation {
                                    scale = 5.0
                                }
                            }
                        }
                )
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            offset = CGSize(
                                width: lastOffset.width + value.translation.width,
                                height: lastOffset.height + value.translation.height
                            )
                        }
                        .onEnded { _ in
                            lastOffset = offset
                        }
                )
                .gesture(
                    TapGesture(count: 2)
                        .onEnded {
                            withAnimation {
                                if scale > 1.0 {
                                    scale = 1.0
                                    offset = .zero
                                    lastOffset = .zero
                                } else {
                                    scale = 2.0
                                }
                            }
                        }
                )
                .onTapGesture {
                    onDismiss()
                }

            VStack {
                HStack {
                    Spacer()
                    Button(action: onDismiss) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(12)
                            .background(Circle().fill(Color.black.opacity(0.5)))
                    }
                    .padding()
                }
                Spacer()
            }
        }
    }
}

// MARK: - AVAudioPlayerDelegate
// 使用单例类作为代理，避免因为结构体不能作为代理导致的问题
class PlayerDelegate: NSObject, AVAudioPlayerDelegate {
    static let shared = PlayerDelegate()

    var onFinish: (() -> Void)?

    private override init() {
        super.init()
    }

    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        print("播放完成，成功: \(flag)")
        onFinish?()
    }

    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            print("音频解码错误: \(error.localizedDescription)")
        }
    }
}

// MARK: - Markdown View
struct MarkdownView: View {
    let text: String

    var body: some View {
        // 使用swift-markdown-ui库提供的Markdown组件
        MarkdownUI.Markdown(text)
            .frame(maxWidth: .infinity, alignment: .leading)
    }
}

struct AddUsageRecordView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.id = UUID()
        product.name = "MacBook Pro"
        product.brand = "Apple"
        product.price = 9999
        product.purchaseDate = Date()

        return AddUsageRecordView(product: product)
            .environment(\.managedObjectContext, context)
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: context),
                expenseRepository: RelatedExpenseRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}
