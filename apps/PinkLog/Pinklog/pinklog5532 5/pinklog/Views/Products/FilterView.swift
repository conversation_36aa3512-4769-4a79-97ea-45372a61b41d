import SwiftUI

struct FilterView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var purchaseChannelViewModel: PurchaseChannelViewModel

    // 临时存储筛选条件的状态，只有点击应用后才会更新到 ViewModel
    @State private var selectedCategories: Set<Category> = []
    @State private var selectedStatuses: Set<Product.ProductStatus> = []
    @State private var selectedTags: Set<UUID> = []
    @State private var selectedChannels: Set<UUID> = []

    // 日期范围
    @State private var dateFilterEnabled = false
    @State private var startDate = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
    @State private var endDate = Date()

    // 价格范围
    @State private var priceFilterEnabled = false
    @State private var minPrice: Double = 0
    @State private var maxPrice: Double = 10000

    // 满意度范围
    @State private var satisfactionFilterEnabled = false
    @State private var minSatisfaction: Double = 1
    @State private var maxSatisfaction: Double = 5

    // 值度范围
    @State private var worthFilterEnabled = false
    @State private var minWorth: Double = 0
    @State private var maxWorth: Double = 100

    // 保修状态
    @State private var warrantyFilterEnabled = false
    @State private var warrantyStatus: WarrantyStatus = .all

    // 使用频率
    @State private var usageFilterEnabled = false
    @State private var usageFrequency: UsageFrequency = .all

    enum WarrantyStatus: String, CaseIterable, Identifiable {
        case all = "全部"
        case inWarranty = "在保修期内"
        case expiringSoon = "即将过保"
        case expired = "已过保"

        var id: String { self.rawValue }
    }

    enum UsageFrequency: String, CaseIterable, Identifiable {
        case all = "全部"
        case high = "高频使用"
        case medium = "中频使用"
        case low = "低频使用"
        case unused = "未使用"

        var id: String { self.rawValue }
    }

    var body: some View {
        NavigationView {
            Form {
                // 类别筛选
                categoryFilterSection

                // 产品状态筛选
                statusFilterSection

                // 购买渠道筛选
                channelFilterSection

                // 日期筛选
                dateFilterSection

                // 价格筛选
                priceFilterSection

                // 满意度筛选
                satisfactionFilterSection

                // 值度筛选
                worthFilterSection

                // 标签筛选
                tagFilterSection

                // 保修状态筛选
                warrantyFilterSection

                // 使用频率筛选
                usageFilterSection
            }
            .navigationTitle("高级筛选")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetAllFilters()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        applyFilters()
                        dismiss()
                    }
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .fontWeight(.bold)
                }
            }
            .onAppear {
                loadCurrentFilters()
            }
        }
    }

    // MARK: - 各筛选部分

    // 类别筛选部分
    private var categoryFilterSection: some View {
        Section(header: Text("按类别筛选")) {
            if productViewModel.categories.isEmpty {
                Text("暂无类别")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ForEach(productViewModel.categories) { category in
                    Button(action: {
                        // 如果已选中，则移除；否则，先清空再添加
                        if selectedCategories.contains(category) {
                            selectedCategories.remove(category)
                        } else {
                            selectedCategories.removeAll()
                            selectedCategories.insert(category)
                        }
                    }) {
                        HStack {
                            Text(category.name ?? "未分类")
                                .foregroundColor(.primary)

                            Spacer()

                            if selectedCategories.contains(category) {
                                Image(systemName: "checkmark")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                            }
                        }
                    }
                }
            }
        }
    }

    // 状态筛选部分
    private var statusFilterSection: some View {
        Section(header: Text("按产品状态筛选")) {
            // 实物产品状态
            Group {
                Text("实物产品状态")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)
                
                ForEach([Product.ProductStatus.core, .worthwhile, .potential, .reconsider, .sellRecommended]) { status in
                    statusFilterRow(for: status)
                }
            }
            
            // 虚拟订阅状态
            Group {
                Text("虚拟订阅状态")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)
                    .padding(.top, 8)
                
                ForEach([Product.ProductStatus.subscriptionActive, .subscriptionPaused, .subscriptionCancelled, .subscriptionExpired, .subscriptionTrial]) { status in
                    statusFilterRow(for: status)
                }
            }
        }
    }
    
    // 状态筛选行
    private func statusFilterRow(for status: Product.ProductStatus) -> some View {
        Button(action: {
            // 如果已选中，则移除；否则，先清空再添加
            if selectedStatuses.contains(status) {
                selectedStatuses.remove(status)
            } else {
                selectedStatuses.removeAll()
                selectedStatuses.insert(status)
            }
        }) {
            HStack {
                Text(status.rawValue)
                    .foregroundColor(.primary)

                Spacer()

                if selectedStatuses.contains(status) {
                    Image(systemName: "checkmark")
                        .foregroundColor(status.color)
                }
            }
        }
    }

    // 购买渠道筛选部分
    private var channelFilterSection: some View {
        Section(header: Text("按购买渠道筛选")) {
            let allChannels = purchaseChannelViewModel.getAllChannels()
            if allChannels.isEmpty {
                Text("暂无购买渠道")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ForEach(allChannels, id: \.self) { channel in
                    Button(action: {
                        if let id = channel.id {
                            // 如果已选中，则移除；否则，先清空再添加
                            if selectedChannels.contains(id) {
                                selectedChannels.remove(id)
                            } else {
                                selectedChannels.removeAll()
                                selectedChannels.insert(id)
                            }
                        }
                    }) {
                        HStack {
                            Text(channel.name ?? "未命名")
                                .foregroundColor(.primary)

                            if let category = channel.category?.name {
                                Text(category)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            if let id = channel.id, selectedChannels.contains(id) {
                                Image(systemName: "checkmark")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                            }
                        }
                    }
                }
            }
        }
    }

    // 日期筛选部分
    private var dateFilterSection: some View {
        Section(header: Text("按购买日期筛选")) {
            Toggle("启用日期筛选", isOn: $dateFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if dateFilterEnabled {
                DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
                DatePicker("结束日期", selection: $endDate, displayedComponents: .date)
            }
        }
    }

    // 价格筛选部分
    private var priceFilterSection: some View {
        Section(header: Text("按价格筛选")) {
            Toggle("启用价格筛选", isOn: $priceFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if priceFilterEnabled {
                HStack {
                    Text("¥\(Int(minPrice))")
                    Slider(value: $minPrice, in: 0...maxPrice)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }

                HStack {
                    Text("¥\(Int(maxPrice))")
                    Slider(value: $maxPrice, in: minPrice...10000)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }
            }
        }
    }

    // 满意度筛选部分
    private var satisfactionFilterSection: some View {
        Section(header: Text("按满意度筛选")) {
            Toggle("启用满意度筛选", isOn: $satisfactionFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if satisfactionFilterEnabled {
                HStack {
                    Text("\(Int(minSatisfaction))星")
                    Slider(value: $minSatisfaction, in: 1...maxSatisfaction, step: 1)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }

                HStack {
                    Text("\(Int(maxSatisfaction))星")
                    Slider(value: $maxSatisfaction, in: minSatisfaction...5, step: 1)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }
            }
        }
    }

    // 值度筛选部分
    private var worthFilterSection: some View {
        Section(header: Text("按值度指数筛选")) {
            Toggle("启用值度筛选", isOn: $worthFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if worthFilterEnabled {
                HStack {
                    Text("\(Int(minWorth))分")
                    Slider(value: $minWorth, in: 0...maxWorth)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }

                HStack {
                    Text("\(Int(maxWorth))分")
                    Slider(value: $maxWorth, in: minWorth...100)
                        .accentColor(themeManager.currentTheme.primaryColor)
                }
            }
        }
    }

    // 标签筛选部分
    private var tagFilterSection: some View {
        Section(header: Text("按标签筛选")) {
            if tagViewModel.tags.isEmpty {
                Text("暂无标签")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ForEach(tagViewModel.tags) { tag in
                    Button(action: {
                        if let id = tag.id {
                            // 如果已选中，则移除；否则，先清空再添加
                            if selectedTags.contains(id) {
                                selectedTags.remove(id)
                            } else {
                                selectedTags.removeAll()
                                selectedTags.insert(id)
                            }
                        }
                    }) {
                        HStack {
                            Text(tag.name ?? "")
                                .foregroundColor(.primary)

                            Spacer()

                            if let id = tag.id, selectedTags.contains(id) {
                                Image(systemName: "checkmark")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                            }
                        }
                    }
                }
            }
        }
    }

    // 保修状态筛选部分
    private var warrantyFilterSection: some View {
        Section(header: Text("按保修状态筛选")) {
            Toggle("启用保修状态筛选", isOn: $warrantyFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if warrantyFilterEnabled {
                CustomSegmentedControl(selection: $warrantyStatus) { status in
                    Text(status.rawValue)
                }
                .accentColor(themeManager.currentTheme.primaryColor)
            }
        }
    }

    // 使用频率筛选部分
    private var usageFilterSection: some View {
        Section(header: Text("按使用频率筛选")) {
            Toggle("启用使用频率筛选", isOn: $usageFilterEnabled)
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))

            if usageFilterEnabled {
                CustomSegmentedControl(selection: $usageFrequency) { frequency in
                    Text(frequency.rawValue)
                }
                .accentColor(themeManager.currentTheme.primaryColor)
            }
        }
    }

    // MARK: - 辅助方法

    // 加载当前筛选条件
    private func loadCurrentFilters() {
        // 从 ViewModel 加载当前筛选条件
        selectedCategories = productViewModel.advancedFilterCategories
        selectedStatuses = productViewModel.advancedFilterStatuses
        selectedTags = productViewModel.advancedFilterTags
        selectedChannels = productViewModel.advancedFilterChannels

        dateFilterEnabled = productViewModel.dateFilterEnabled
        startDate = productViewModel.startDate
        endDate = productViewModel.endDate

        priceFilterEnabled = productViewModel.priceFilterEnabled
        minPrice = productViewModel.minPrice
        maxPrice = productViewModel.maxPrice

        satisfactionFilterEnabled = productViewModel.satisfactionFilterEnabled
        minSatisfaction = productViewModel.minSatisfaction
        maxSatisfaction = productViewModel.maxSatisfaction

        worthFilterEnabled = productViewModel.worthFilterEnabled
        minWorth = productViewModel.minWorth
        maxWorth = productViewModel.maxWorth

        warrantyFilterEnabled = productViewModel.warrantyFilterEnabled
        warrantyStatus = productViewModel.warrantyStatus

        usageFilterEnabled = productViewModel.usageFilterEnabled
        usageFrequency = productViewModel.usageFrequency
    }

    // 应用筛选条件
    private func applyFilters() {
        // 将筛选条件应用到 ViewModel
        productViewModel.advancedFilterCategories = selectedCategories
        productViewModel.advancedFilterStatuses = selectedStatuses
        productViewModel.advancedFilterTags = selectedTags
        productViewModel.advancedFilterChannels = selectedChannels

        productViewModel.dateFilterEnabled = dateFilterEnabled
        productViewModel.startDate = startDate
        productViewModel.endDate = endDate

        productViewModel.priceFilterEnabled = priceFilterEnabled
        productViewModel.minPrice = minPrice
        productViewModel.maxPrice = maxPrice

        productViewModel.satisfactionFilterEnabled = satisfactionFilterEnabled
        productViewModel.minSatisfaction = minSatisfaction
        productViewModel.maxSatisfaction = maxSatisfaction

        productViewModel.worthFilterEnabled = worthFilterEnabled
        productViewModel.minWorth = minWorth
        productViewModel.maxWorth = maxWorth

        productViewModel.warrantyFilterEnabled = warrantyFilterEnabled
        productViewModel.warrantyStatus = warrantyStatus

        productViewModel.usageFilterEnabled = usageFilterEnabled
        productViewModel.usageFrequency = usageFrequency

        // 应用高级筛选
        productViewModel.advancedFilterEnabled = true
        productViewModel.loadProducts()
    }

    // 重置所有筛选条件
    private func resetAllFilters() {
        selectedCategories.removeAll()
        selectedStatuses.removeAll()
        selectedTags.removeAll()
        selectedChannels.removeAll()

        dateFilterEnabled = false
        startDate = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
        endDate = Date()

        priceFilterEnabled = false
        minPrice = 0
        maxPrice = 10000

        satisfactionFilterEnabled = false
        minSatisfaction = 1
        maxSatisfaction = 5

        worthFilterEnabled = false
        minWorth = 0
        maxWorth = 100

        warrantyFilterEnabled = false
        warrantyStatus = .all

        usageFilterEnabled = false
        usageFrequency = .all
    }
}

// MARK: - 预览
struct FilterView_Previews: PreviewProvider {
    static var previews: some View {
        let themeManager = ThemeManager()
        let context = PersistenceController.preview.container.viewContext
        return FilterView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: context),
                categoryRepository: CategoryRepository(context: context)
            ))
            .environmentObject(TagViewModel(
                repository: TagRepository(context: context)
            ))
            .environmentObject(PurchaseChannelViewModel(context: context))
            .environmentObject(themeManager)
    }
}