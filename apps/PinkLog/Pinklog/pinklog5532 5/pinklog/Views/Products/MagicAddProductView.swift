//
//  MagicAddProductView.swift
//  PinkLog
//
//  Created by World-Class Designer on 2025/1/17.
//  终极震撼的魔法添加产品体验
//

import SwiftUI
import SceneKit

struct MagicAddProductView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    
    // 初始化参数
    let presetImage: UIImage?
    let presetLiftedImage: UIImage?
    let presetStickerImage: UIImage?
    
    // 状态管理
    @State private var currentStage: MagicStage = .preparation
    @State private var capturedImage: UIImage?
    @State private var productName = ""
    @State private var price: Double = 0
    @State private var selectedCategory: String = ""
    @State private var satisfaction: Float = 0.8
    @State private var product3DRotation: Float = 0
    @State private var showCamera = false
    @State private var showMagicEffects = false
    
    // AI助手
    @StateObject private var pixieManager = AIPixieManager()
    
    // 初始化方法
    init(presetImage: UIImage? = nil, presetLiftedImage: UIImage? = nil, presetStickerImage: UIImage? = nil) {
        self.presetImage = presetImage
        self.presetLiftedImage = presetLiftedImage
        self.presetStickerImage = presetStickerImage
    }
    
    var body: some View {
        ZStack {
            // 背景魔法环境
            MagicEnvironmentBackground(stage: currentStage)
            
            // 主要内容
            switch currentStage {
            case .preparation:
                MagicPreparationView(
                    onCameraCapture: { showCamera = true },
                    onPhotoSelect: { /* 相册选择 */ }
                )
                
            case .imageCapture:
                EmptyView() // 相机界面由sheet展示
                
            case .productRecognition:
                MagicRecognitionView(
                    image: capturedImage!,
                    onRecognitionComplete: { name in
                        productName = name
                        withAnimation(.spring(response: 1.0, dampingFraction: 0.7)) {
                            currentStage = .product3DDisplay
                        }
                    }
                )
                
            case .product3DDisplay:
                Magic3DProductView(
                    image: capturedImage!,
                    productName: $productName,
                    price: $price,
                    rotation: $product3DRotation,
                    onComplete: {
                        withAnimation(.spring(response: 1.0, dampingFraction: 0.7)) {
                            currentStage = .satisfactionRating
                        }
                    }
                )
                
            case .satisfactionRating:
                MagicSatisfactionStage(
                    satisfaction: $satisfaction,
                    productImage: capturedImage!,
                    onComplete: {
                        withAnimation(.spring(response: 1.0, dampingFraction: 0.7)) {
                            currentStage = .collectionRitual
                        }
                    }
                )
                
            case .collectionRitual:
                MagicCollectionRitualView(
                    productImage: capturedImage!,
                    productName: productName,
                    price: price,
                    satisfaction: satisfaction,
                    onComplete: {
                        saveProduct()
                        dismiss()
                    }
                )
            }
            
            // AI小精灵助手
            AIPixieAssistant(suggestion: $pixieManager.currentSuggestion)
                .zIndex(1000)
            
            // 魔法控制界面
            MagicControlInterface(
                currentStage: currentStage,
                onBack: handleBackAction,
                onSkip: handleSkipAction
            )
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showCamera) {
            MagicCameraView(capturedImage: $capturedImage)
                .onDisappear {
                    if capturedImage != nil {
                        withAnimation(.spring(response: 1.0, dampingFraction: 0.7)) {
                            currentStage = .productRecognition
                        }
                        pixieManager.generateSuggestion(for: capturedImage)
                    }
                }
        }
        .onAppear {
            setupMagicExperience()
            
            // 如果有预设图片，直接进入识别阶段
            if let preset = presetImage {
                capturedImage = preset
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation(.spring(response: 1.0, dampingFraction: 0.7)) {
                        currentStage = .productRecognition
                    }
                }
            }
        }
    }
    
    private func setupMagicExperience() {
        // 初始化魔法环境
        showMagicEffects = true
        
        // 延迟显示小精灵
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            pixieManager.currentSuggestion = "欢迎来到魔法商店！让我们一起收藏你的宝物吧！✨"
        }
    }
    
    private func handleBackAction() {
        switch currentStage {
        case .preparation:
            dismiss()
        case .productRecognition:
            withAnimation {
                currentStage = .preparation
            }
        case .product3DDisplay:
            withAnimation {
                currentStage = .productRecognition
            }
        case .satisfactionRating:
            withAnimation {
                currentStage = .product3DDisplay
            }
        case .collectionRitual:
            withAnimation {
                currentStage = .satisfactionRating
            }
        default:
            break
        }
    }
    
    private func handleSkipAction() {
        switch currentStage {
        case .productRecognition:
            productName = "神秘宝物"
            withAnimation {
                currentStage = .product3DDisplay
            }
        case .product3DDisplay:
            withAnimation {
                currentStage = .satisfactionRating
            }
        case .satisfactionRating:
            withAnimation {
                currentStage = .collectionRitual
            }
        default:
            break
        }
    }
    
    private func saveProduct() {
        // 保存产品逻辑
        guard let image = capturedImage else { return }
        
        // 创建新产品使用ProductViewModel的方法
        let createdProduct = productViewModel.addProduct(
            name: productName.isEmpty ? "神秘宝物" : productName,
            brand: nil,
            model: nil,
            price: price,
            purchaseDate: Date(),
            category: nil,
            images: image.jpegData(compressionQuality: 0.8),
            expiryDate: nil
        )
        
        // 添加初始满意度记录
        if let product = createdProduct {
            // 这里需要添加UsageRecord，但先注释掉直到我们有UsageViewModel
            /*
            let _ = usageViewModel.addUsageRecord(
                for: product,
                date: Date(),
                satisfaction: Int(satisfaction * 10),
                scenario: "初始收藏",
                notes: "初始收藏记录"
            )
            */
        }
        
        /*
        let _ = productViewModel.repository.save { context in
            let newProduct = Product(context: context)
            newProduct.id = UUID()
            newProduct.name = productName.isEmpty ? "神秘宝物" : productName
            newProduct.price = price
            newProduct.purchaseDate = Date()
            
            // 查找或创建类别
            if !selectedCategory.isEmpty {
                // 这里需要找到对应的Category对象
                // 暂时先用字符串存储
            }
            
            // 保存图片
            if let imageData = image.jpegData(compressionQuality: 0.8) {
                // 这里应该使用ImageManager保存图片
                // newProduct.images = imageData
            }
            
            // 添加初始满意度记录
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.product = newProduct
            usageRecord.satisfaction = Int16(satisfaction * 10)
            usageRecord.date = Date()
            usageRecord.notes = "初始收藏记录"
        }
        */
    }
}

// MARK: - 魔法阶段枚举
enum MagicStage {
    case preparation        // 准备阶段
    case imageCapture      // 图像捕获
    case productRecognition // 产品识别
    case product3DDisplay  // 3D产品展示
    case satisfactionRating // 满意度评级
    case collectionRitual  // 收藏仪式
}

// MARK: - 魔法环境背景
struct MagicEnvironmentBackground: View {
    let stage: MagicStage
    @State private var particleAnimation = false
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                colors: backgroundColors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 动态粒子背景
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 5...15))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .scaleEffect(particleAnimation ? 1.5 : 0.5)
                    .opacity(particleAnimation ? 0 : 1)
                    .animation(
                        .easeInOut(duration: Double.random(in: 2...4))
                        .repeatForever(autoreverses: false)
                        .delay(Double.random(in: 0...2)),
                        value: particleAnimation
                    )
            }
        }
        .onAppear {
            particleAnimation = true
        }
    }
    
    private var backgroundColors: [Color] {
        switch stage {
        case .preparation:
            return [.purple.opacity(0.3), .blue.opacity(0.2), .black.opacity(0.1)]
        case .imageCapture, .productRecognition:
            return [.blue.opacity(0.4), .cyan.opacity(0.3), .purple.opacity(0.2)]
        case .product3DDisplay:
            return [.cyan.opacity(0.3), .blue.opacity(0.4), .purple.opacity(0.3)]
        case .satisfactionRating:
            return [.pink.opacity(0.3), .purple.opacity(0.4), .blue.opacity(0.3)]
        case .collectionRitual:
            return [.yellow.opacity(0.2), .orange.opacity(0.3), .pink.opacity(0.4)]
        }
    }
}

// MARK: - 魔法准备视图
struct MagicPreparationView: View {
    let onCameraCapture: () -> Void
    let onPhotoSelect: () -> Void
    
    var body: some View {
        VStack(spacing: 40) {
            // 标题
            VStack(spacing: 16) {
                Text("✨ 魔法商店 ✨")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .shadow(color: .purple, radius: 10)
                
                Text("让我们一起收藏你的珍贵宝物")
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // 魔法按钮组
            VStack(spacing: 24) {
                // 魔法拍照按钮
                MagicActionButton(
                    title: "🔮 施展拍照魔法",
                    subtitle: "用魔法阵捕获宝物",
                    colors: [.blue, .purple, .pink],
                    action: onCameraCapture
                )
                
                // 魔法相册按钮
                MagicActionButton(
                    title: "📸 从魔法相册选择",
                    subtitle: "从已有的宝物中挑选",
                    colors: [.purple, .pink, .orange],
                    action: onPhotoSelect
                )
            }
            
            Spacer()
        }
        .padding(.horizontal, 30)
    }
}

// MARK: - 魔法动作按钮
struct MagicActionButton: View {
    let title: String
    let subtitle: String
    let colors: [Color]
    let action: () -> Void
    
    @State private var isPressed = false
    @State private var glowIntensity: Double = 0.5
    
    var body: some View {
        Button(action: {
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
            action()
        }) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.vertical, 20)
            .padding(.horizontal, 30)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(
                        LinearGradient(
                            colors: colors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: colors.first?.opacity(glowIntensity) ?? .clear, radius: 20)
                    .scaleEffect(isPressed ? 0.95 : 1.0)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                glowIntensity = 1.0
            }
        }
        .pressEvents {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
        } onRelease: {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = false
            }
        }
    }
}

// MARK: - 魔法识别视图
struct MagicRecognitionView: View {
    let image: UIImage
    let onRecognitionComplete: (String) -> Void
    
    @State private var recognitionProgress: Double = 0
    @State private var detectedName = ""
    @State private var magicSymbols: [MagicSymbol] = []
    
    var body: some View {
        VStack(spacing: 30) {
            Text("🔍 魔法识别中...")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            // 图片展示区域
            ZStack {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 300)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [.cyan, .blue, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 3
                            )
                    )
                
                // 识别扫描效果
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [.clear, .cyan.opacity(0.8), .clear],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(height: 4)
                    .offset(y: -100 + CGFloat(recognitionProgress) * 200)
                    .animation(.linear(duration: 2.0).repeatForever(autoreverses: false), value: recognitionProgress)
                
                // 魔法符号
                ForEach(magicSymbols.indices, id: \.self) { index in
                    if index < magicSymbols.count {
                        MagicSymbolView(symbol: magicSymbols[index])
                    }
                }
            }
            
            // 识别进度
            VStack(spacing: 12) {
                ProgressView(value: recognitionProgress)
                    .progressViewStyle(MagicProgressStyle())
                
                Text(recognitionProgress < 1.0 ? "分析中..." : "识别完成！")
                    .font(.headline)
                    .foregroundColor(.white)
                
                if !detectedName.isEmpty {
                    Text("发现：\(detectedName)")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.cyan)
                        .scaleEffect(1.2)
                        .animation(.spring(response: 0.5, dampingFraction: 0.6), value: detectedName)
                }
            }
        }
        .padding(.horizontal, 30)
        .onAppear {
            startRecognition()
        }
    }
    
    private func startRecognition() {
        // 启动识别动画
        withAnimation(.linear(duration: 2.0)) {
            recognitionProgress = 1.0
        }
        
        // 生成魔法符号
        generateMagicSymbols()
        
        // 模拟识别过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            detectedName = ["神秘宝物", "珍贵物品", "魔法道具", "收藏品"].randomElement() ?? "神秘宝物"
            
            // 延迟完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                onRecognitionComplete(detectedName)
            }
        }
    }
    
    private func generateMagicSymbols() {
        for i in 0..<8 {
            let symbol = MagicSymbol(
                x: CGFloat.random(in: 50...300),
                y: CGFloat.random(in: 100...400),
                rotation: Double.random(in: 0...360),
                type: i % 4
            )
            magicSymbols.append(symbol)
        }
    }
}

// MARK: - 魔法3D产品视图
struct Magic3DProductView: View {
    let image: UIImage
    @Binding var productName: String
    @Binding var price: Double
    @Binding var rotation: Float
    let onComplete: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Text("🌟 产品具现化 🌟")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            // 3D产品展示
            Product3DView(productImage: image, rotation: $rotation)
                .frame(height: 300)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.black.opacity(0.2))
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(
                                    LinearGradient(
                                        colors: [.cyan, .blue, .purple],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 2
                                )
                        )
                )
            
            // 产品信息输入
            VStack(spacing: 16) {
                MagicTextField(
                    title: "产品名称",
                    text: $productName,
                    placeholder: "给你的宝物起个名字..."
                )
                
                MagicPriceField(
                    title: "价格",
                    price: $price
                )
            }
            
            // 继续按钮
            MagicActionButton(
                title: "✨ 继续魔法之旅",
                subtitle: "前往满意度评级",
                colors: [.green, .cyan, .blue],
                action: onComplete
            )
        }
        .padding(.horizontal, 30)
    }
}

// MARK: - 魔法满意度阶段
struct MagicSatisfactionStage: View {
    @Binding var satisfaction: Float
    let productImage: UIImage
    let onComplete: () -> Void
    
    var body: some View {
        VStack(spacing: 30) {
            Text("💖 感受魔法 💖")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("用心感受这个宝物带给你的满意度")
                .font(.headline)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
            
            // 心跳满意度视图
            HeartbeatSatisfactionView(satisfaction: $satisfaction)
                .frame(height: 400)
            
            // 完成按钮
            MagicActionButton(
                title: "🎭 开始收藏仪式",
                subtitle: "将宝物加入你的收藏",
                colors: [.pink, .purple, .blue],
                action: onComplete
            )
        }
        .padding(.horizontal, 30)
    }
}

// MARK: - 收藏仪式视图
struct MagicCollectionRitualView: View {
    let productImage: UIImage
    let productName: String
    let price: Double
    let satisfaction: Float
    let onComplete: () -> Void
    
    @State private var ritualStage = 0
    @State private var showParticles = false
    
    var body: some View {
        ZStack {
            VStack(spacing: 40) {
                Text("🎊 收藏仪式 🎊")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .scaleEffect(ritualStage >= 1 ? 1.2 : 1.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.6), value: ritualStage)
                
                // 产品信息卡片
                VStack(spacing: 16) {
                    Image(uiImage: productImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 200)
                        .cornerRadius(20)
                        .scaleEffect(ritualStage >= 2 ? 1.1 : 1.0)
                        .animation(.spring(response: 0.8, dampingFraction: 0.5), value: ritualStage)
                    
                    VStack(spacing: 8) {
                        Text(productName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("¥\(price, specifier: "%.2f")")
                            .font(.headline)
                            .foregroundColor(.cyan)
                        
                        Text("满意度: \(Int(satisfaction * 100))%")
                            .font(.subheadline)
                            .foregroundColor(.pink)
                    }
                    .opacity(ritualStage >= 3 ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.5), value: ritualStage)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.black.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(
                                    LinearGradient(
                                        colors: [.gold, .orange, .pink],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 3
                                )
                        )
                )
                
                if ritualStage >= 4 {
                    Text("🎉 收藏成功！你的宝物已加入魔法收藏库！")
                        .font(.headline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(
                            Capsule()
                                .fill(Color.green.opacity(0.3))
                        )
                        .transition(.scale.combined(with: .opacity))
                }
            }
            
            // 粒子效果
            if showParticles {
                ForEach(0..<50, id: \.self) { index in
                    MagicRitualParticleView(index: index)
                }
            }
        }
        .onAppear {
            startRitual()
        }
    }
    
    private func startRitual() {
        // 阶段1: 标题出现
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation {
                ritualStage = 1
            }
        }
        
        // 阶段2: 产品图片缩放
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation {
                ritualStage = 2
            }
        }
        
        // 阶段3: 信息显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation {
                ritualStage = 3
            }
        }
        
        // 阶段4: 粒子爆炸
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            showParticles = true
            
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
            
            withAnimation {
                ritualStage = 4
            }
        }
        
        // 完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 4.0) {
            onComplete()
        }
    }
}

// MARK: - 辅助组件和样式

struct MagicTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
            
            TextField(placeholder, text: $text)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(Color.cyan.opacity(0.5), lineWidth: 1)
                        )
                )
                .foregroundColor(.white)
        }
    }
}

struct MagicPriceField: View {
    let title: String
    @Binding var price: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
            
            HStack {
                Text("¥")
                    .foregroundColor(.white)
                    .font(.headline)
                
                TextField("0.00", value: $price, format: .number.precision(.fractionLength(2)))
                    .keyboardType(.decimalPad)
                    .foregroundColor(.white)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 15)
                            .stroke(Color.cyan.opacity(0.5), lineWidth: 1)
                    )
            )
        }
    }
}

struct MagicProgressStyle: ProgressViewStyle {
    func makeBody(configuration: Configuration) -> some View {
        ZStack(alignment: .leading) {
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.2))
                .frame(height: 8)
            
            RoundedRectangle(cornerRadius: 10)
                .fill(
                    LinearGradient(
                        colors: [.cyan, .blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: CGFloat(configuration.fractionCompleted ?? 0) * 300, height: 8)
                .animation(.easeInOut, value: configuration.fractionCompleted)
        }
        .frame(width: 300)
    }
}

struct MagicSymbol {
    let x: CGFloat
    let y: CGFloat
    let rotation: Double
    let type: Int
}

struct MagicSymbolView: View {
    let symbol: MagicSymbol
    @State private var opacity: Double = 0
    @State private var scale: CGFloat = 0.5
    
    var body: some View {
        Image(systemName: symbolName)
            .foregroundColor(.cyan)
            .font(.title)
            .opacity(opacity)
            .scaleEffect(scale)
            .rotationEffect(.degrees(symbol.rotation))
            .position(x: symbol.x, y: symbol.y)
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(Double.random(in: 0...1))) {
                    opacity = 1
                    scale = 1
                }
                
                withAnimation(.easeOut(duration: 2).delay(2)) {
                    opacity = 0
                }
            }
    }
    
    private var symbolName: String {
        switch symbol.type {
        case 0: return "star.fill"
        case 1: return "sparkle"
        case 2: return "diamond.fill"
        default: return "circle.fill"
        }
    }
}

// MARK: - 魔法仪式粒子视图
struct MagicRitualParticleView: View {
    let index: Int
    @State private var opacity: Double = 0
    @State private var scale: CGFloat = 0
    
    private let symbolNames = ["star.fill", "sparkle", "heart.fill"]
    private let colors: [Color] = [.yellow, .pink, .cyan, .orange]
    
    var body: some View {
        Image(systemName: symbolNames.randomElement() ?? "star.fill")
            .foregroundColor(colors.randomElement() ?? .yellow)
            .font(.system(size: CGFloat.random(in: 10...25)))
            .position(
                x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
            )
            .opacity(opacity)
            .scaleEffect(scale)
            .onAppear {
                withAnimation(.easeOut(duration: 2.0)) {
                    opacity = 1
                    scale = 1
                }
                
                withAnimation(.easeOut(duration: 3.0).delay(1.0)) {
                    opacity = 0
                }
            }
    }
}

struct MagicControlInterface: View {
    let currentStage: MagicStage
    let onBack: () -> Void
    let onSkip: () -> Void
    
    var body: some View {
        VStack {
            HStack {
                // 返回按钮
                Button(action: onBack) {
                    Image(systemName: "arrow.left.circle.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 44, height: 44)
                        )
                }
                
                Spacer()
                
                // 跳过按钮（适当阶段显示）
                if shouldShowSkip {
                    Button("跳过", action: onSkip)
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            Capsule()
                                .fill(Color.white.opacity(0.2))
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Spacer()
        }
    }
    
    private var shouldShowSkip: Bool {
        switch currentStage {
        case .productRecognition, .product3DDisplay, .satisfactionRating:
            return true
        default:
            return false
        }
    }
}

// MARK: - 按钮事件扩展
extension View {
    func pressEvents(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in onPress() }
                .onEnded { _ in onRelease() }
        )
    }
}

// MARK: - 颜色扩展
extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let productRepository = ProductRepository(context: context)
    let categoryRepository = CategoryRepository(context: context)
    let tagRepository = TagRepository(context: context)
    
    return MagicAddProductView()
        .environmentObject(ProductViewModel(repository: productRepository, categoryRepository: categoryRepository))
        .environmentObject(TagViewModel(repository: tagRepository))
}