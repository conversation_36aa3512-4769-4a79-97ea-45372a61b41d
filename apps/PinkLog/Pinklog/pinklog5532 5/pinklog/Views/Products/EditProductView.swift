import SwiftUI
import PhotosUI

struct EditProductView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var purchaseChannelViewModel: PurchaseChannelViewModel
    
    let product: Product
    
    // 基本信息
    @State private var name: String = ""
    @State private var brand: String = ""
    @State private var model: String = ""
    @State private var price: String = ""
    @State private var purchaseDate: Date = Date()
    @State private var selectedCategory: Category?
    @State private var purchaseChannel: String = ""
    @State private var quantity: Int = 1
    
    // 图片
    @State private var selectedImage: UIImage?
    @State private var isShowingImagePicker = false
    @State private var photoPickerItems: [PhotosPickerItem] = []
    
    // 关键日期
    @State private var expiryDate: Date?
    // @State private var warrantyEndDate: Date? // Removed
    @State private var showExpiryDate = false
    // @State private var showWarrantyDate = false // Removed
    
    // 扩展信息
    @State private var purchaseMotivation: String = ""
    @State private var initialSatisfaction: Int = 3
    @State private var purchaseNotes: String = ""
    @State private var expectedLifespan: String = "" // 新增
    @State private var expectedUsageFrequency: String = "" // 新增

    // 标签
    @State private var showingTagSelector = false
    
    // 其他
    @State private var showingCategorySelector = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    // @State private var currentStep = 1 // Removed for single-page form
    @State private var userClearedExistingWarrantyImage: Bool = false // New state to track explicit clear
    
    // 新增购买渠道选择器相关状态
    @State private var selectedPurchaseChannel: PurchaseChannel?
    @State private var showingPurchaseChannelPicker = false
    
    // 动机选项
    private let motivations = ["必需品", "提升效率", "兴趣爱好", "冲动消费", "礼物", "替代旧物", "其他"]
    
    var body: some View {
        NavigationView {
            Form {
                // All sections will be displayed directly
                basicInfoSection
                imageSection
                keyDatesSection
                warrantyInfoSection
                extendedInfoSection // This group contains multiple sections like motivation, satisfaction, tags, etc.
                saveButtonSection // New save button section
            }
            .navigationTitle("编辑产品") // Static title
            .navigationBarItems(leading: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .sheet(isPresented: $showingCategorySelector) {
                CategorySelectorView(selectedCategory: $selectedCategory)
            }
            .sheet(isPresented: $showingTagSelector) {
                TagSelectorView()
            }
            .sheet(isPresented: $showingPurchaseChannelPicker) {
                PurchaseChannelPickerView(viewModel: purchaseChannelViewModel, selectedChannel: $selectedPurchaseChannel)
                    .environmentObject(themeManager)
            }
            .photosPicker(isPresented: $isShowingImagePicker, selection: $photoPickerItems, maxSelectionCount: 1, matching: .images)
            .onChange(of: photoPickerItems) { _, newItems in
                guard let item = newItems.first else { return }
                
                Task {
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        DispatchQueue.main.async {
                            self.selectedImage = image
                        }
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                loadProductData() // Loads basic product data into @State variables
                productViewModel.setupForExistingProduct(product) // Loads warranty and sets up hasWarranty in ViewModel
                
                // Ensure tags are correctly loaded for the product being edited
                if let tags = product.tags?.allObjects as? [Tag] {
                    tagViewModel.clearSelection() // Clear previous selections
                    for tag in tags {
                        tagViewModel.selectTag(tag)
                    }
                } else {
                    tagViewModel.clearSelection()
                }
                self.userClearedExistingWarrantyImage = false // Reset flag on appear
            }
            .fileImporter( // For warranty document
                isPresented: $productViewModel.showWarrantyFileImporter,
                allowedContentTypes: productViewModel.allowedWarrantyContentTypes,
                allowsMultipleSelection: false
            ) { result_in_array in
                let singleResult: Result<URL, Error>
                switch result_in_array {
                case .success(let urls):
                    if let firstUrl = urls.first {
                        singleResult = .success(firstUrl)
                    } else {
                        // This case should ideally not happen if a file was selected
                        // and allowsMultipleSelection is false.
                        // Handle as an error or a specific case if necessary.
                        // For now, let's assume an error if no URL is present.
                        singleResult = .failure(NSError(domain: "EditProductView", code: 0, userInfo: [NSLocalizedDescriptionKey: "No file URL found after selection."]))
                    }
                case .failure(let error):
                    singleResult = .failure(error)
                }
                productViewModel.handleWarrantyFileSelection(result: singleResult)
            }
        }
    }
    
    // private var stepIndicator: some View { ... } // Removed
    
    // 基本信息部分
    private var basicInfoSection: some View {
        Section(header: Text("基本信息")) {
            TextField("产品名称 *", text: $name)
            
            TextField("品牌", text: $brand)
            
            TextField("型号/款式", text: $model)
            
            TextField("价格 *", text: $price)
                .keyboardType(.decimalPad)
            
            DatePicker("购买日期 *", selection: $purchaseDate, displayedComponents: .date)
            
            Button(action: {
                showingCategorySelector = true
            }) {
                HStack {
                    Text("类别 *")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text(selectedCategory?.name ?? "请选择")
                        .foregroundColor(selectedCategory == nil ? .secondary : .primary)
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Button(action: {
                showingPurchaseChannelPicker = true
            }) {
                HStack {
                    Text("购买渠道")
                        .foregroundColor(.primary)
                    Spacer()
                    Text(selectedPurchaseChannel?.name ?? "请选择")
                        .foregroundColor(selectedPurchaseChannel == nil ? .secondary : .primary)
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 对于消耗品，不显示数量编辑器，因为库存应该通过专门的库存管理界面修改
            if !(product.isConsumable == true) {
                Stepper("数量: \(quantity)", value: $quantity, in: 1...99)
            } else {
                HStack {
                    Text("初始库存")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(Int(product.quantity))\(product.unitType ?? "")")
                        .foregroundColor(.secondary)
                    Text("(通过库存管理修改)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // 图片部分
    private var imageSection: some View {
        Section(header: Text("产品图片")) {
            Button(action: {
                isShowingImagePicker = true
            }) {
                HStack {
                    Text("选择图片")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if let image = selectedImage {
                        Image(uiImage: image)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 60, height: 60)
                            .cornerRadius(8)
                    } else {
                        Image(systemName: "photo")
                            .font(.title)
                            .foregroundColor(.secondary)
                            .frame(width: 60, height: 60)
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // 关键日期部分
    private var keyDatesSection: some View {
        Section(header: Text("关键日期")) {
            Toggle("设置有效期", isOn: $showExpiryDate)
            
            if showExpiryDate {
                DatePicker("有效期至", selection: Binding(
                    get: { expiryDate ?? Date().addingTimeInterval(86400 * 30) },
                    set: { expiryDate = $0 }
                ), displayedComponents: .date)
            }
            // Warranty Toggle and DatePicker moved to warrantyInfoSection and controlled by productViewModel.hasWarranty
        }
    }

    // 保修信息部分 (New Section for EditView)
    private var warrantyInfoSection: some View {
        Section(header: Text("保修信息")) {
            Toggle("提供保修信息", isOn: $productViewModel.hasWarranty)
                .padding(.bottom, productViewModel.hasWarranty ? 0 : 8)

            if productViewModel.hasWarranty {
                DatePicker("保修截止日期", selection: Binding(
                    get: { productViewModel.warrantyEndDateInput ?? Date().addingTimeInterval(86400 * 365) },
                    set: { productViewModel.warrantyEndDateInput = $0 }
                ), displayedComponents: .date)

                VStack(alignment: .leading) {
                    Text("保修范围摘要")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    TextEditor(text: $productViewModel.warrantyDetailsInput)
                        .frame(minHeight: 80)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }

                Button(action: {
                    productViewModel.showWarrantyFileImporter = true
                }) {
                    HStack {
                        // Display newly selected file name if available
                        if let fileName = productViewModel.selectedWarrantyFileName, !fileName.isEmpty {
                            Text("新凭证: \(fileName)")
                            Spacer()
                            Image(systemName: "doc.badge.plus")
                        // Else, display current persisted file name if available and no new file selected
                        } else if let existingPath = productViewModel.currentWarrantyImagePath, !existingPath.isEmpty {
                            Text("现有凭证: \(URL(fileURLWithPath: existingPath).lastPathComponent)")
                            Spacer()
                            Image(systemName: "doc.text.fill")
                        } else {
                            Text("上传保修凭证")
                            Spacer()
                            Image(systemName: "icloud.and.arrow.up")
                        }
                    }
                }

                // Show clear button if a new file is selected OR an existing one is loaded
                if productViewModel.selectedWarrantyFileURL != nil || (productViewModel.currentWarrantyImagePath != nil && !productViewModel.currentWarrantyImagePath!.isEmpty) {
                    Button(action: {
                        let hadExistingImage = productViewModel.currentWarrantyImagePath != nil && !productViewModel.currentWarrantyImagePath!.isEmpty
                        
                        productViewModel.clearSelectedWarrantyFile() // Clears selectedWarrantyFileURL and selectedWarrantyFileName in VM

                        if hadExistingImage && productViewModel.selectedWarrantyFileURL == nil {
                            // If there was an existing image and now no new file is selected (after clear),
                            // it means user wants to remove the existing persisted image.
                            self.userClearedExistingWarrantyImage = true
                            productViewModel.currentWarrantyImagePath = nil // Update UI to show no image
                            // productViewModel.selectedWarrantyFileName is already nil from clearSelectedWarrantyFile
                        }
                    }) {
                        HStack {
                            Spacer()
                            Text("清除凭证")
                                .foregroundColor(.red)
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                        }
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
            }
        }
    }
    
    // 扩展信息部分
    private var extendedInfoSection: some View {
        Group {
            Section(header: Text("购买动机")) {
                Picker("购买动机", selection: $purchaseMotivation) {
                    Text("请选择").tag("")
                    ForEach(motivations, id: \.self) { motivation in
                        Text(motivation).tag(motivation)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            Section(header: Text("初始满意度")) {
                HStack {
                    ForEach(1...5, id: \.self) { index in
                        Image(systemName: index <= initialSatisfaction ? "star.fill" : "star")
                            .foregroundColor(index <= initialSatisfaction ? .yellow : .gray)
                            .font(.title2)
                            .onTapGesture {
                                initialSatisfaction = index
                            }
                    }
                }
                .frame(maxWidth: .infinity)
            }
            
            Section(header: Text("标签")) {
                Button(action: {
                    showingTagSelector = true
                }) {
                    HStack {
                        Text("选择标签")
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if tagViewModel.selectedTags.isEmpty {
                            Text("未选择")
                                .foregroundColor(.secondary)
                        } else {
                            Text("\(tagViewModel.selectedTags.count)个标签")
                                .foregroundColor(.primary)
                        }
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 已选标签
                if !tagViewModel.selectedTags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            ForEach(Array(tagViewModel.selectedTags), id: \.self) { tag in
                                Text(tag.name ?? "")
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(tagViewModel.getTagColor(tag).opacity(0.2))
                                    .foregroundColor(tagViewModel.getTagColor(tag))
                                    .cornerRadius(8)
                            }
                        }
                    }
                }
            }

            Section(header: Text("预期")) {
                TextField("预期使用寿命 (例如: 3年, 1000小时)", text: $expectedLifespan)
                TextField("预期使用频率 (例如: 每天, 每周3次)", text: $expectedUsageFrequency)
            }

            Section(header: Text("备注")) {
                TextEditor(text: $purchaseNotes)
                    .frame(minHeight: 100)
            }
        }
    }
    
    // private var navigationButtons: some View { ... } // Removed

    private var saveButtonSection: some View {
        Section {
            Button(action: saveEditedProduct) {
                Text("保存更改")
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.white)
                    .padding(.vertical, 8)
                    .background(themeManager.currentTheme.primaryColor)
                    .cornerRadius(8)
            }
        }
    }
    
    // 加载产品数据
    private func loadProductData() {
        name = product.name ?? ""
        brand = product.brand ?? ""
        model = product.model ?? ""
        price = "\(product.price)"
        purchaseDate = product.purchaseDate ?? Date()
        selectedCategory = product.category
        purchaseChannel = product.purchaseChannel ?? ""
        quantity = Int(product.quantity)
        
        // 加载购买渠道
        selectedPurchaseChannel = product.purchaseChannelRelation
        
        // 加载图片
        if let imageData = product.images,
           let image = UIImage(data: imageData) {
            selectedImage = image
        }
        
        // 加载有效期
        if let expiry = product.expiryDate {
            expiryDate = expiry
            showExpiryDate = true
        } else {
            showExpiryDate = false
        }
        
        // 加载扩展信息
        purchaseMotivation = product.purchaseMotivation ?? ""
        initialSatisfaction = Int(product.initialSatisfaction)
        purchaseNotes = product.purchaseNotes ?? ""
        expectedLifespan = product.expectedLifespan > 0 ? "\(product.expectedLifespan)" : ""
        expectedUsageFrequency = product.expectedUsageFrequency ?? ""

        // 加载标签
        if let tags = product.tags?.allObjects as? [Tag] {
            tagViewModel.clearSelection()
            for tag in tags {
                tagViewModel.selectTag(tag)
            }
        }
    }
    
    // Renamed and simplified validation for a single-page form
    private func validateForm() -> Bool {
        if name.isEmpty {
            alertMessage = "请输入产品名称"
            showingAlert = true
            return false
        }
        
        if price.isEmpty {
            alertMessage = "请输入价格"
            showingAlert = true
            return false
        }
        guard let _ = Double(price) else {
            alertMessage = "价格格式不正确"
            showingAlert = true
            return false
        }
        
        if selectedCategory == nil {
            alertMessage = "请选择类别"
            showingAlert = true
            return false
        }
        return true
    }
    
    // 更新产品
    private func saveEditedProduct() {
        guard !name.isEmpty else {
            alertMessage = "请输入产品名称"
            showingAlert = true
            return
        }
        
        guard let priceValue = Double(price), priceValue > 0 else {
            alertMessage = "请输入有效的价格"
            showingAlert = true
            return
        }
        
        guard selectedCategory != nil else {
            alertMessage = "请选择产品类别"
            showingAlert = true
            return
        }
        
        withAnimation {
            // 更新基本信息
            product.name = name
            product.brand = brand
            product.model = model
            product.price = Double(price) ?? 0
            product.purchaseDate = purchaseDate
            product.category = selectedCategory

            // 对于消耗品，不修改quantity字段，因为它代表初始库存量
            // 应该通过专门的库存管理界面来修改
            if !(product.isConsumable == true) {
                product.quantity = Int16(quantity)
            }
            
            // 购买渠道处理
            if let channel = selectedPurchaseChannel {
                // 使用新的关系模型
                product.purchaseChannelRelation = channel
                purchaseChannelViewModel.incrementChannelUsageCount(channel)
                // 同时保留字符串值以确保兼容性
                product.purchaseChannel = channel.name
            } else if !purchaseChannel.isEmpty {
                // 如果直接输入了渠道名称但没有选择渠道对象，尝试查找或创建对应的渠道
                product.purchaseChannel = purchaseChannel
                purchaseChannelViewModel.migrateStringChannelToRelation(product)
            }
            
            // 图片
            if let imageData = selectedImage?.jpegData(compressionQuality: 0.8) {
                product.images = imageData
            }
            
            // 关键日期
            if showExpiryDate, let expiryDate = expiryDate {
                product.expiryDate = expiryDate
            } else {
                product.expiryDate = nil
            }
            
            // 保修信息更新
            if productViewModel.hasWarranty, let warrantyEndDate = productViewModel.warrantyEndDateInput {
                product.warrantyEndDate = warrantyEndDate
                product.warrantyDetails = productViewModel.warrantyDetailsInput
                
                // 处理保修凭证
                if userClearedExistingWarrantyImage {
                    // 用户明确清除了现有凭证
                    product.warrantyImage = nil
                    productViewModel.removeWarrantyFile(for: product)
                } else if let sourceURL = productViewModel.selectedWarrantyFileURL {
                    // 用户选择了新的凭证文件
                    product.warrantyImage = productViewModel.selectedWarrantyFileName
                    productViewModel.saveWarrantyFile(from: sourceURL, for: product)
                }
                // 否则保持现有凭证不变
            } else {
                // 用户取消了保修信息
                product.warrantyEndDate = nil
                product.warrantyDetails = nil
                product.warrantyImage = nil
                productViewModel.removeWarrantyFile(for: product)
            }
            
            // 扩展信息
            product.purchaseMotivation = purchaseMotivation
            product.initialSatisfaction = Int16(initialSatisfaction)
            product.purchaseNotes = purchaseNotes
            product.expectedLifespan = expectedLifespan.isEmpty ? 0 : Int16(expectedLifespan) ?? 0
            product.expectedUsageFrequency = expectedUsageFrequency
            
            // 标签
            // 先移除所有标签
            if let tags = product.tags as? Set<Tag> {
                for tag in tags {
                    product.removeFromTags(tag)
                }
            }
            
            // 再添加新的标签
            for tag in tagViewModel.selectedTags {
                product.addToTags(tag)
            }
            
            // 保存
            do {
                try viewContext.save()
            presentationMode.wrappedValue.dismiss()
            } catch {
                let nsError = error as NSError
                print("无法保存产品: \(nsError), \(nsError.userInfo)")
                alertMessage = "保存失败：\(nsError.localizedDescription)"
            showingAlert = true
            }
        }
    }
}

struct EditProductView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.id = UUID()
        product.name = "MacBook Pro"
        product.brand = "Apple"
        product.price = 9999
        product.purchaseDate = Date()
        
        return EditProductView(product: product)
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: context),
                categoryRepository: CategoryRepository(context: context)
            ))
            .environmentObject(TagViewModel(
                repository: TagRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
} 