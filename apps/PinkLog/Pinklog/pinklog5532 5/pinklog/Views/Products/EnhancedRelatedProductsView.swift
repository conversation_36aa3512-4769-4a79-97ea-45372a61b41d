import SwiftUI
import CoreData

struct EnhancedRelatedProductsView: View {
    // MARK: - 环境和状态
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @StateObject private var linkViewModel: ProductLinkViewModel
    @State private var showingProductPicker = false
    @State private var selectedRelationshipType: ProductRelationshipType = .complement
    @State private var relationshipStrength: Int16 = 3
    @State private var relationshipNotes: String = ""
    @State private var isBidirectional: Bool = false
    
    // 一对多关联关系状态
    @State private var isCreatingGroup = false
    @State private var selectedProducts: [Product] = []
    @State private var groupName: String = ""
    
    // 状态管理
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    // 当前产品
    let product: Product
    let context: NSManagedObjectContext
    var onGroupCreated: (() -> Void)? = nil
    
    // MARK: - 初始化
    init(product: Product, context: NSManagedObjectContext, onGroupCreated: (() -> Void)? = nil) {
        self.product = product
        self.context = context
        self._linkViewModel = StateObject(wrappedValue: ProductLinkViewModel(context: context))
        self.onGroupCreated = onGroupCreated
    }
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 创建分组关联按钮
                createGroupButton
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                
                if isCreatingGroup {
                    // 显示创建关联组的界面
                    groupCreationView
                } else {
                    // 显示现有关联产品
                    existingRelationshipsView
                }
            }
            .navigationTitle("关联产品")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: isCreatingGroup ? Button("完成") {
                    createRelationshipGroup()
                }
                .disabled(selectedProducts.isEmpty)
                : nil
            )
            .onAppear {
                linkViewModel.setCurrentProduct(product)
            }
            .sheet(isPresented: $showingProductPicker) {
                productPicker
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"), action: {
                        // 如果创建成功，则关闭视图
                        if alertTitle == "关联组创建成功" {
                            onGroupCreated?()
                            presentationMode.wrappedValue.dismiss()
                        }
                    })
                )
            }
        }
    }
    
    // MARK: - 组件视图
    
    // 创建分组关联按钮
    private var createGroupButton: some View {
        Button(action: {
            isCreatingGroup.toggle()
            if isCreatingGroup {
                // 重置选择的产品列表
                selectedProducts = []
                // 重置关系信息
                selectedRelationshipType = .complement
                relationshipStrength = 3
                relationshipNotes = ""
                isBidirectional = false
                groupName = ""
            }
        }) {
            HStack {
                Image(systemName: isCreatingGroup ? "xmark.circle" : "plus.circle")
                Text(isCreatingGroup ? "取消创建" : "创建关联组")
                    .fontWeight(.medium)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isCreatingGroup ? Color.red.opacity(0.1) : themeManager.currentTheme.primaryColor.opacity(0.1))
            )
            .foregroundColor(isCreatingGroup ? .red : themeManager.currentTheme.primaryColor)
        }
    }
    
    // 创建关联组的视图
    private var groupCreationView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 关联组基本信息
                GroupInfoSection(
                    selectedRelationshipType: $selectedRelationshipType,
                    relationshipStrength: $relationshipStrength,
                    isBidirectional: $isBidirectional,
                    relationshipNotes: $relationshipNotes,
                    groupName: $groupName
                )
                
                // 已选择的产品
                selectedProductsSection
                
                // 添加产品按钮
                addProductButton
                    .padding(.top)
            }
            .padding()
        }
    }
    
    // 已选择的产品列表
    private var selectedProductsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("已选择的产品")
                .font(.headline)
                .padding(.bottom, 4)
            
            if selectedProducts.isEmpty {
                Text("请选择至少一个关联产品")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(8)
            } else {
                ForEach(selectedProducts, id: \.id) { selectedProduct in
                    HStack {
                        // 产品图片
                        if let imageData = selectedProduct.images, let uiImage = UIImage(data: imageData) {
                            Image(uiImage: uiImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 40, height: 40)
                                .cornerRadius(8)
                        } else {
                            Image(systemName: "cube.box")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                                .frame(width: 40, height: 40)
                                .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                                .cornerRadius(8)
                        }
                        
                        // 产品信息
                        VStack(alignment: .leading, spacing: 2) {
                            Text(selectedProduct.name ?? "未命名产品")
                                .font(.body)
                                .lineLimit(1)
                            
                            if let brand = selectedProduct.brand, !brand.isEmpty {
                                Text(brand + (selectedProduct.model != nil ? " · \(selectedProduct.model!)" : ""))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        // 删除按钮
                        Button(action: {
                            withAnimation {
                                selectedProducts.removeAll { $0.id == selectedProduct.id }
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                        }
                    }
                    .padding(10)
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(8)
                }
            }
        }
    }
    
    // 添加产品按钮
    private var addProductButton: some View {
        Button(action: {
            showingProductPicker = true
        }) {
            HStack {
                Image(systemName: "plus")
                Text("添加产品")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(themeManager.currentTheme.primaryColor, lineWidth: 1)
            )
            .foregroundColor(themeManager.currentTheme.primaryColor)
        }
    }
    
    // 产品选择器
    private var productPicker: some View {
        MultiProductPickerView(selectedProducts: Binding(
            get: { Set(selectedProducts) },
            set: { newSet in
                // 过滤掉当前产品，防止自我关联
                selectedProducts = Array(newSet.filter { $0.id != product.id })
            }
        ))
        .environmentObject(productViewModel)
        .environmentObject(tagViewModel)
        .environmentObject(themeManager)
    }
    
    // 现有关联产品视图
    private var existingRelationshipsView: some View {
        ScrollView {
            if linkViewModel.isLoading {
                ProgressView()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else if linkViewModel.links.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "link.badge.plus")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                        .padding()
                    
                    Text("暂无关联产品")
                        .font(.headline)
                    
                    Text("点击上方按钮创建关联组或添加单个关联产品")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
            } else {
                // 按组展示关联产品
                groupedRelationshipsView
            }
        }
    }
    
    // 按组展示关联产品
    private var groupedRelationshipsView: some View {
        VStack(spacing: 16) {
            let allLinks = linkViewModel.links
            
            // 1. 将链接分为带groupID和不带groupID的
            let linksWithGroupID = allLinks.filter { $0.groupID != nil && !($0.groupID?.isEmpty ?? true) }
            let linksWithoutGroupID = allLinks.filter { $0.groupID == nil || ($0.groupID?.isEmpty ?? true) }

            // 2. 按 groupID 对链接进行分组
            let groupedByGroupID = Dictionary(grouping: linksWithGroupID) { $0.groupID! }

            // 3. 显示按 groupID 分组的链接
            ForEach(groupedByGroupID.keys.sorted(), id: \.self) { groupID in
                if let linksInGroup = groupedByGroupID[groupID], let firstLink = linksInGroup.first {
                    let groupName = firstLink.groupName ?? "未命名组"
                    let relationshipType = firstLink.relationshipTypeEnum
                    
                    // 创建组卡片
                    VStack(alignment: .leading, spacing: 8) {
                        // 组名和类型标题
                        HStack {
                            Image(systemName: relationshipType.icon)
                                .foregroundColor(relationshipType.color)
                            
                            Text("\(groupName) | \(relationshipType.rawValue)")
                                .font(.headline)
                                .foregroundColor(relationshipType.color)
                            
                            Spacer()
                            
                            Text("\(linksInGroup.count)个产品")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        
                        Divider()
                            .padding(.horizontal, 8)
                        
                        // 组内链接列表
                        ForEach(linksInGroup, id: \.id) { link in
                            linkRow(link)
                        }
                    }
                    .padding(.vertical, 12)
                    .background(relationshipType.color.opacity(0.05))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(relationshipType.color.opacity(0.2), lineWidth: 1)
                    )
                    .padding(.horizontal)
                }
            }

            // 4. 为没有 groupID 的链接提供回退显示逻辑
            let ungroupedLinksContent = Group {
                if !linksWithoutGroupID.isEmpty {
                    // (此处可以沿用旧的、按groupName或relationshipType的分组逻辑)
                    // 为了简化，我们暂时只按类型分组
                    let groupedByType = Dictionary(grouping: linksWithoutGroupID) { $0.relationshipTypeEnum }
                    
                    ForEach(groupedByType.keys.sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { relationshipType in
                        if let links = groupedByType[relationshipType] {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: relationshipType.icon)
                                        .foregroundColor(relationshipType.color)
                                    Text(relationshipType.rawValue)
                                        .font(.headline)
                                        .foregroundColor(relationshipType.color)
                                    Spacer()
                                    Text("\(links.count)个产品")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.horizontal, 12)
                                Divider().padding(.horizontal, 8)
                                ForEach(links, id: \.id) { link in
                                    linkRow(link)
                                }
                            }
                            .padding(.vertical, 12)
                            .background(relationshipType.color.opacity(0.05))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(relationshipType.color.opacity(0.2), lineWidth: 1)
                            )
                            .padding(.horizontal)
                        }
                    }
                }
            }
            ungroupedLinksContent
        }
        .padding(.vertical)
    }
    
    // 单个链接行视图
    private func linkRow(_ link: ProductLink) -> some View {
        let relatedProduct = link.sourceProduct == product ? link.targetProduct : link.sourceProduct
        
        return HStack {
            // 产品图片
            if let rProduct = relatedProduct, let imageData = rProduct.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .cornerRadius(8)
            } else {
                Image(systemName: "cube.box")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 40, height: 40)
                    .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                    .cornerRadius(8)
            }
            
            // 产品信息
            VStack(alignment: .leading, spacing: 4) {
                if let rProduct = relatedProduct {
                    Text(rProduct.name ?? "未命名产品")
                        .font(.body)
                        .fontWeight(.medium)
                    
                    if let brand = rProduct.brand, !brand.isEmpty {
                        Text(brand + (rProduct.model != nil ? " · \(rProduct.model!)" : ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 关系信息
            VStack(alignment: .trailing, spacing: 2) {
                // 关系强度
                HStack(spacing: 2) {
                    if link.strength > 0 {
                        ForEach(1...Int(link.strength), id: \.self) { _ in
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                        }
                    }
                }
                
                // 使用次数或双向关系标记
                if link.usageCount > 0 {
                    Text("\(link.usageCount)次")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                if link.isBidirectional {
                    Image(systemName: "arrow.left.arrow.right")
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
            }
            
            // 删除按钮
            Button(action: {
                // 确认删除
                alertTitle = "确认删除"
                alertMessage = "确定要删除此关联关系吗？此操作不可恢复。"
                showingAlert = true
                
                // 在确认后执行删除
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    linkViewModel.deleteLink(link)
                }
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
                    .font(.caption)
                    .padding(8)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(8)
        .padding(.horizontal, 8)
    }
    
    // MARK: - 逻辑方法
    
    // 创建关联组的方法
    private func createRelationshipGroup() {
        guard !selectedProducts.isEmpty else {
            alertTitle = "无法创建关联组"
            alertMessage = "请至少选择一个关联产品"
            showingAlert = true
            return
        }
        
        // 检查具有相同名称和产品组合的组是否已存在
        if linkViewModel.checkIfGroupExists(product: product, groupName: groupName, products: selectedProducts) {
            alertTitle = "创建失败"
            alertMessage = "具有相同名称和产品组合的关联组已存在。"
            showingAlert = true
            return
        }
        
        // 为这个新组创建一个唯一的ID
        let groupID = UUID().uuidString
        
        // 添加所有选择的产品作为关联产品
        var successCount = 0
        var actualErrors = false

        for selectedProduct in selectedProducts {
            if !linkViewModel.createLink(
                sourceProduct: product,
                targetProduct: selectedProduct,
                relationshipType: selectedRelationshipType,
                notes: relationshipNotes.isEmpty ? nil : relationshipNotes,
                strength: relationshipStrength,
                isBidirectional: isBidirectional,
                groupName: groupName.isEmpty ? nil : groupName,
                groupID: groupID
            ) {
                // 如果创建失败，检查错误消息
                if let errorMessage = linkViewModel.errorMessage, errorMessage != "这两个产品已有关联" {
                    // 如果是真正的错误，则标记
                    actualErrors = true
                }
                // 如果仅仅是"已存在关联"，则不视为失败
            } else {
                successCount += 1
            }
        }
        
        // 显示结果提示
        if !actualErrors {
            alertTitle = "关联组创建成功"
            alertMessage = "成功关联 \(successCount) 个新产品。"
        } else {
            alertTitle = "部分产品关联失败"
            alertMessage = "创建过程中遇到一些问题，请检查错误日志"
        }
        showingAlert = true
        
        // 无论成功与否，都重置状态
        isCreatingGroup = false
        selectedProducts = []
        
        // 延迟稍长时间并更彻底地刷新数据，确保所有数据库更改都已完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 强制刷新产品及其关联关系
            self.context.refresh(self.product, mergeChanges: true)
            // 重新加载所有链接数据
            self.linkViewModel.loadLinks(for: self.product)
        }
    }
}

// MARK: - 辅助视图

// 关联组信息设置区
struct GroupInfoSection: View {
    @Binding var selectedRelationshipType: ProductRelationshipType
    @Binding var relationshipStrength: Int16
    @Binding var isBidirectional: Bool
    @Binding var relationshipNotes: String
    @Binding var groupName: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 组名称
            VStack(alignment: .leading, spacing: 4) {
                Text("关联组名称（可选）")
                    .font(.headline)
                
                TextField("例如：全套冬装搭配", text: $groupName)
                    .padding()
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(8)
            }
            
            // 关系类型
            VStack(alignment: .leading, spacing: 4) {
                Text("关系类型")
                    .font(.headline)
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(ProductRelationshipType.allCases, id: \.self) { type in
                            VStack {
                                Image(systemName: type.icon)
                                    .font(.system(size: 20))
                                    .foregroundColor(selectedRelationshipType == type ? .white : type.color)
                                    .frame(width: 44, height: 44)
                                    .background(
                                        Circle()
                                            .fill(selectedRelationshipType == type ? type.color : type.color.opacity(0.1))
                                    )
                                
                                Text(type.rawValue)
                                    .font(.caption)
                                    .foregroundColor(selectedRelationshipType == type ? type.color : .primary)
                            }
                            .onTapGesture {
                                selectedRelationshipType = type
                                // 设置默认强度
                                relationshipStrength = Int16(type.defaultStrength)
                                // 设置默认双向性
                                isBidirectional = type.isBidirectionalByDefault
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                // 关系说明
                Text(selectedRelationshipType.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
            
            // 关系强度
            VStack(alignment: .leading, spacing: 8) {
                Text("关系强度")
                    .font(.headline)
                
                HStack {
                    ForEach(1...5, id: \.self) { index in
                        Image(systemName: index <= relationshipStrength ? "star.fill" : "star")
                            .foregroundColor(index <= relationshipStrength ? .yellow : .gray)
                            .font(.title3)
                            .onTapGesture {
                                relationshipStrength = Int16(index)
                            }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 8)
            }
            
            // 双向关系
            Toggle(isOn: $isBidirectional) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("双向关系")
                        .font(.headline)
                    
                    Text("两个产品互相关联，形成双向关系")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .toggleStyle(SwitchToggleStyle(tint: selectedRelationshipType.color))
            
            // 备注
            VStack(alignment: .leading, spacing: 4) {
                Text("备注（可选）")
                    .font(.headline)
                
                TextEditor(text: $relationshipNotes)
                    .frame(height: 100)
                    .padding(4)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            }
        }
    }
}