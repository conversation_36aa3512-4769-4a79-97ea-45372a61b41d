import SwiftUI

struct ProductPickerView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @Binding var selectedProduct: Product?
    @State private var searchText = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 产品列表
                List {
                    ForEach(filteredProducts) { product in
                        productRow(product)
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("选择产品")
            .navigationBarItems(leading: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .onAppear {
                productViewModel.loadProducts()
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索产品", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 产品行
    private func productRow(_ product: Product) -> some View {
        HStack {
            if let imageData = product.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .cornerRadius(8)
            } else {
                Image(systemName: "cube.box")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 40, height: 40)
                    .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                    .cornerRadius(8)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name ?? "未命名产品")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                if let brand = product.brand, !brand.isEmpty {
                    Text(brand + (product.model != nil ? " · \(product.model!)" : ""))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 已选中状态
            if selectedProduct?.id == product.id {
                Image(systemName: "checkmark")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            selectedProduct = product
            presentationMode.wrappedValue.dismiss()
        }
    }
    
    // 筛选后的产品列表
    private var filteredProducts: [Product] {
        if searchText.isEmpty {
            return productViewModel.products
        } else {
            return productViewModel.products.filter { product in
                let nameMatch = product.name?.localizedCaseInsensitiveContains(searchText) ?? false
                let brandMatch = product.brand?.localizedCaseInsensitiveContains(searchText) ?? false
                let modelMatch = product.model?.localizedCaseInsensitiveContains(searchText) ?? false
                
                return nameMatch || brandMatch || modelMatch
            }
        }
    }
}

struct ProductPickerView_Previews: PreviewProvider {
    static var previews: some View {
        ProductPickerView(selectedProduct: .constant(nil))
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
} 