import SwiftUI
import PhotosUI
import Combine
import AVFoundation

struct AddProductView: View {
    // 预设图片参数
    let presetImage: UIImage?
    let presetLiftedImage: UIImage?
    let presetStickerImage: UIImage?

    // 快速录入模式切换
    @State private var isQuickEntryMode: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var purchaseChannelViewModel: PurchaseChannelViewModel

    let showCancelButton: Bool
    let onSaveCompleted: (() -> Void)?

    // 初始化方法
    init(presetImage: UIImage? = nil, presetLiftedImage: UIImage? = nil, presetStickerImage: UIImage? = nil, showCancelButton: Bool = true, onSaveCompleted: (() -> Void)? = nil) {
        self.presetImage = presetImage
        self.presetLiftedImage = presetLiftedImage
        self.presetStickerImage = presetStickerImage
        self.showCancelButton = showCancelButton
        self.onSaveCompleted = onSaveCompleted
    }

    // 分步表单控制
    @State var currentStep: Int = 1
    @State var totalSteps: Int = 4 // u4e3au4e86u517cu5bb9u6027u4fdd u7559u8be5u5c5eu6027uff0cu5b9eu9645u4f7fu7528actualTotalSteps
    
    // u6839u636eu5f53u524du6a21u5f0fu8ba1u7b97u5b9eu9645u7684u6b65u9aa4u6570
    private var actualTotalSteps: Int {
        if isQuickMode {
            // 快速模式：有预设图片时1步（全合一），无预设图片时2步（基本信息+满意度）
            return hasPresetImage ? 1 : 2
        } else {
            return 4 // 详细模式四步
        }
    }

    // 是否有预设图片
    private var hasPresetImage: Bool {
        return presetImage != nil
    }
    
    // 录入模式选择
    @State var isQuickMode: Bool = true // 默认使用快速录入模式

    // 基本信息
    @State var name: String = ""
    @State var brand: String = ""
    @State private var model: String = ""
    @State var price: String = ""
    @State var purchaseDate: Date = Date()
    @State var selectedCategory: Category?
    @State private var purchaseChannel: String = ""
    @State private var quantity: Int = 1
    @State private var valuationMethod: String = "usage" // 默认为按使用次数计算
    @State private var isVirtualProduct: Bool = false // 新增：是否为虚拟商品
    @State private var isConsumable: Bool = false // 新增：是否为消耗品
    
    // 消耗品相关状态
    @State private var currentQuantity: String = ""
    @State private var selectedUnit: String = "个"
    @State private var minStockAlert: String = ""
    @State private var consumptionRate: String = ""

    // 包装规格模式状态
    @State private var packageSize: String = ""
    @State private var packageUnit: String = "个"

    // 图片
    @State var selectedImage: UIImage?
    @State var liftedImage: UIImage?
    @State var stickerImage: UIImage?
    @State var finalImageData: Data? // 新增：最终选择的压缩图片数据
    @State var isShowingImagePicker = false
    @State var isShowingCamera = false
    @State var isShowingImageOptions = false
    @State var isShowingSubjectLiftHelp = false
    @State private var photoPickerItems: [PhotosPickerItem] = []

    // 关键日期
    @State private var expiryDate: Date?
    @State private var showExpiryDate = false

    // 扩展信息
    @State private var purchaseMotivation: String = ""
    @State private var initialSatisfaction: Int = 3
    @State private var purchaseNotes: String = ""
    @State private var expectedLifespan: String = ""
    @State private var expectedUsageFrequency: String = ""

    // 标签
    @State private var showingTagSelector = false

    // 虚拟订阅相关状态
    @State private var subscriptionCycle: String = "monthly" // 订阅周期
    @State private var renewalDate: Date = Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
    @State private var maxUsers: String = "1" // 最大用户数
    @State private var isSharedSubscription: Bool = false // 是否为共享订阅
    @State private var subscriptionNotes: String = "" // 订阅备注
    
    // 其他
    @State var showingCategorySelector = false
    @State var showingAlert = false
    @State var alertMessage = ""
    @State private var isFormValid: Bool = false
    @State private var isSaving: Bool = false

    // 动机选项
    private let motivations = ["必需品", "提升效率", "兴趣爱好", "冲动消费", "礼物", "替代旧物", "其他"]

    // 计算方式选项
    private let valuationMethods = [
        ("usage", "按使用次数"),
        ("daily", "按使用天数")
    ]
    
    // 订阅周期选项
    private let subscriptionCycles = [
        ("monthly", "月付"),
        ("quarterly", "季付"),
        ("yearly", "年付"),
        ("lifetime", "终身")
    ]

    @State private var selectedPurchaseChannel: PurchaseChannel?
    @State private var showingPurchaseChannelPicker = false

    // 快速模式步骤标题
    private var quickModeStepTitles: [String] {
        return hasPresetImage ? ["快速录入"] : ["快速录入", "预览与保存"]
    }
    
    // 详细模式步骤标题
    private let detailedModeStepTitles = [
        "基本信息",
        "图片与计算方式",
        "日期与保修",
        "标签与扩展信息"
    ]
    
    // 获取当前模式的步骤标题
    private var stepTitles: [String] {
        return isQuickMode ? quickModeStepTitles : detailedModeStepTitles
    }
    
    // 模式切换视图
    private var modeToggleView: some View {
        VStack(spacing: 8) {
            HStack {
                Text("录入模式")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 12) {
                // 快速录入按钮
                modeButton(title: "快速录入", icon: "bolt.fill", description: "仅填写核心字段", isSelected: isQuickMode) {
                    if currentStep > 1 {
                        // 如果不是第一步，需要提示用户
                        alertMessage = "切换录入模式会重置当前表单，确定继续吗？"
                        showingAlert = true
                    } else if !isQuickMode {
                        // 只有当当前不是快速模式时才切换
                        withAnimation {
                            isQuickMode = true
                            currentStep = 1
                            validateCurrentStep()
                        }
                    }
                }
                
                // 详细录入按钮
                modeButton(title: "详细录入", icon: "list.bullet.clipboard", description: "填写所有字段", isSelected: !isQuickMode) {
                    if currentStep > 1 {
                        // 如果不是第一步，需要提示用户
                        alertMessage = "切换录入模式会重置当前表单，确定继续吗？"
                        showingAlert = true
                    } else if isQuickMode {
                        // 只有当当前是快速模式时才切换
                        withAnimation {
                            isQuickMode = false
                            currentStep = 1
                            validateCurrentStep()
                        }
                    }
                }
            }
        }
        .padding(.horizontal)
        .padding(.top, 16)
        .padding(.bottom, 8)
    }
    
    // 产品类型选择按钮
    private func productTypeButton(title: String, subtitle: String, icon: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : themeManager.currentTheme.primaryColor)
                    .frame(width: 32, height: 32)
                    .background(isSelected ? themeManager.currentTheme.primaryColor : themeManager.currentTheme.primaryColor.opacity(0.1))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .font(.title2)
                }
            }
            .padding(12)
            .background(isSelected ? themeManager.currentTheme.primaryColor.opacity(0.05) : Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? themeManager.currentTheme.primaryColor : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // 日期格式化工具
    // u6a21u5f0fu9009u62e9u6309u94ae
    private func modeButton(title: String, icon: String, description: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: icon)
                        .font(.headline)
                    Text(title)
                        .font(.headline)
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(12)
            .background(isSelected ? themeManager.currentTheme.primaryColor.opacity(0.15) : Color.gray.opacity(0.1))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? themeManager.currentTheme.primaryColor : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var purchaseDateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
    
    // 信息行
    private func infoRow(title: String, value: String, icon: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.gray)
                .frame(width: 24)
            
            Text(title)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.medium)
        }
    }
    
    // 🎯 世界级快速录入流程 - 卡片式分步体验
    private var quickEntrySectionView: some View {
        VStack(spacing: 0) {
            // 流动进度指示器
            FluidStepIndicator(
                currentStep: currentStep,
                totalSteps: actualTotalSteps,
                accentColor: themeManager.currentTheme.primaryColor
            )
            .padding(.bottom, 20)
            
            // 分步卡片内容
            if hasPresetImage {
                // 有预设图片时的单步流程
                premiumAllInOneCard
            } else {
                // 无预设图片时的分步流程
                switch currentStep {
                case 1:
                    premiumBasicInfoCard
                case 2:
                    premiumSatisfactionCard
                default:
                    premiumBasicInfoCard
                }
            }
        }
    }
    
    // 全合一卡片（有预设图片时）
    private var premiumAllInOneCard: some View {
        PremiumCard(
            title: "快速添加产品",
            subtitle: "图片已就绪，完善基本信息即可",
            accentColor: themeManager.currentTheme.primaryColor
        ) {
            VStack(spacing: 24) {
                // 图片预览区域
                if let image = selectedImage {
                    VStack(spacing: 12) {
                        SubjectLiftView(
                            image: $selectedImage,
                            liftedImage: $liftedImage,
                            stickerImage: $stickerImage
                        ) { compressedData, displayImage in
                            finalImageData = compressedData
                            logMemoryUsage("快速模式图片选择完成")
                        }
                        
                        // 更换图片按钮
                        Button(action: replaceImage) {
                            Label("更换图片", systemImage: "arrow.triangle.2.circlepath")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }
                
                // 初始满意度（前置到快速录入）
                PremiumSatisfactionRatingView(satisfaction: $initialSatisfaction)
                
                // 智能产品名称输入
                SmartProductNameInput(
                    name: $name,
                    brand: $brand,
                    productImage: selectedImage
                )
                .onChange(of: name) { _, _ in
                    // 🔥 修复：实时验证表单状态
                    validateCurrentStep()
                }
                
                // 价格输入（简化版）
                premiumPriceInput
                
                // 类别选择（简化版）
                premiumCategorySelector
            }
        }
    }
    
    // 基本信息卡片（第1步）
    private var premiumBasicInfoCard: some View {
        PremiumCard(
            title: "基本信息",
            subtitle: "让我们了解您的新产品",
            accentColor: themeManager.currentTheme.primaryColor
        ) {
            VStack(spacing: 24) {
                // 图片选择区域
                if selectedImage == nil {
                    premiumImageSelector
                } else {
                    VStack(spacing: 12) {
                        SubjectLiftView(
                            image: $selectedImage,
                            liftedImage: $liftedImage,
                            stickerImage: $stickerImage
                        ) { compressedData, displayImage in
                            finalImageData = compressedData
                            logMemoryUsage("快速模式图片选择完成")
                        }
                        
                        Button(action: replaceImage) {
                            Label("更换图片", systemImage: "arrow.triangle.2.circlepath")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }
                
                // 智能产品名称输入
                SmartProductNameInput(
                    name: $name,
                    brand: $brand,
                    productImage: selectedImage
                )
                .onChange(of: name) { _, _ in
                    // 🔥 修复：实时验证表单状态
                    validateCurrentStep()
                }
                
                // 价格输入
                premiumPriceInput
                
                // 类别选择
                premiumCategorySelector
            }
        }
    }
    
    // 满意度评分卡片（第2步）
    private var premiumSatisfactionCard: some View {
        PremiumCard(
            title: "初始感受",
            subtitle: "这对我们的分析很重要",
            accentColor: themeManager.currentTheme.primaryColor
        ) {
            VStack(spacing: 24) {
                // 产品预览
                if let image = selectedImage {
                    VStack(spacing: 8) {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .shadow(radius: 5)
                        
                        Text(name.isEmpty ? "新产品" : name)
                            .font(.headline)
                            .foregroundColor(.primary)
                    }
                }
                
                // 满意度评分
                PremiumSatisfactionRatingView(satisfaction: $initialSatisfaction)
                
                // 快速购买动机（可选）
                VStack(alignment: .leading, spacing: 12) {
                    Text("购买原因（可选）")
                        .font(.headline)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                        ForEach(["必需品", "提升效率", "兴趣爱好", "冲动消费"], id: \.self) { motivation in
                            Button(action: {
                                purchaseMotivation = motivation
                            }) {
                                Text(motivation)
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(
                                        purchaseMotivation == motivation ?
                                        themeManager.currentTheme.primaryColor.opacity(0.2) :
                                        Color.gray.opacity(0.1)
                                    )
                                    .cornerRadius(8)
                                    .foregroundColor(
                                        purchaseMotivation == motivation ?
                                        themeManager.currentTheme.primaryColor : .secondary
                                    )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 奢华图片选择器
    private var premiumImageSelector: some View {
        Button(action: {
            isShowingImageOptions = true
        }) {
            ZStack {
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [
                                themeManager.currentTheme.primaryColor.opacity(0.1),
                                themeManager.currentTheme.primaryColor.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 200)
                
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(themeManager.currentTheme.primaryColor.opacity(0.2))
                            .frame(width: 60, height: 60)
                        
                        Image(systemName: "camera.fill")
                            .font(.system(size: 24))
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                    
                    VStack(spacing: 4) {
                        Text("添加产品图片")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("拍照或从相册选择")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // 奢华价格输入
    private var premiumPriceInput: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("价格")
                    .font(.headline)
                Text("*")
                    .foregroundColor(.red)
            }
            
            HStack {
                Text("¥")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                
                TextField("0.00", text: $price)
                    .font(.title2)
                    .fontWeight(.medium)
                    .keyboardType(.decimalPad)
                    .onChange(of: price) { _, _ in
                        // 🔥 修复：实时验证表单状态
                        validateCurrentStep()
                    }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(price.isEmpty ? Color.red.opacity(0.3) : themeManager.currentTheme.primaryColor.opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    // 奢华类别选择器
    private var premiumCategorySelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("类别")
                    .font(.headline)
                Text("*")
                    .foregroundColor(.red)
            }
            
            Button(action: {
                showingCategorySelector = true
            }) {
                HStack {
                    if let category = selectedCategory {
                        HStack {
                            Text(category.name ?? "未知类别")
                                .foregroundColor(.primary)
                                .font(.body)
                            
                            Spacer()
                            
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                        }
                    } else {
                        HStack {
                            Text("选择产品类别")
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(selectedCategory == nil ? Color.red.opacity(0.3) : themeManager.currentTheme.primaryColor.opacity(0.3), lineWidth: 1)
                )
            }
        }
    }
    
    // 更换图片操作
    private func replaceImage() {
        autoreleasepool {
            finalImageData = nil
            if let img = selectedImage {
                ImageManager.shared.clearImageReference(&selectedImage)
            }
            if let img = liftedImage {
                ImageManager.shared.clearImageReference(&liftedImage)
            }
            if let img = stickerImage {
                ImageManager.shared.clearImageReference(&stickerImage)
            }
        }
        
        ImageManager.shared.clearTemporaryCache()
        isShowingImageOptions = true
        logMemoryUsage("更换图片后清理完成")
        
        // 触觉反馈
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
    }
    
    // 快速预览表单
    private var quickPreviewSectionView: some View {
        Section {
            // 标题
            VStack(alignment: .leading) {
                Text("预览信息")
                    .font(.headline)
                    .padding(.bottom, 12)
                
                // 分割线
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.gray.opacity(0.2))
                    .padding(.bottom, 16)
            }
            .padding(.vertical, 8)
            
            // 快速预览内容
            Group {
                // 图片
                if let image = selectedImage {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFit()
                        .frame(height: 180)
                        .frame(maxWidth: .infinity)
                        .cornerRadius(12)
                        .padding(.bottom, 16)
                }
                
                // 信息汇总
                VStack(spacing: 16) {
                    // 标题和价格
                    HStack(alignment: .top) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(name)
                                .font(.title3)
                                .fontWeight(.bold)
                            
                            if !brand.isEmpty {
                                Text(brand)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Text("¥\(price)")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                    
                    // 其他信息
                    VStack(spacing: 12) {
                        infoRow(title: "类别", value: selectedCategory?.name ?? "未选择", icon: "folder")
                        infoRow(title: "购买日期", value: purchaseDateFormatter.string(from: purchaseDate), icon: "calendar")
                    }
                    
                    // 分割线
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color.gray.opacity(0.2))
                        .padding(.vertical, 8)
                    
                    // 提示信息
                    HStack(spacing: 16) {
                        Image(systemName: "info.circle")
                            .foregroundColor(.orange)
                        
                        Text("保存后可在产品详情页补充更多信息，如保修、标签、使用记录等")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 录入模式已使用按钮形式，此处留空
                    EmptyView()
                        .padding(.top, 16)
                    // 模式切换开关
                    modeToggleView
                    
                    // 进度指示器
                    progressIndicator

                    // 步骤标题
                    HStack {
                        Text(stepTitles[min(currentStep - 1, stepTitles.count - 1)])
                            .font(.headline)
                            .padding(.horizontal)
                            .padding(.top, 8)
                        Spacer()
                    }

                    // 表单内容 - 改为VStack而不是Form
                    VStack {
                        Group {
                            if isQuickMode {
                                // 快速模式表单
                                switch currentStep {
                                case 1:
                                    quickEntrySectionView
                                case 2:
                                    quickPreviewSectionView
                                default:
                                    EmptyView()
                                }
                            } else {
                                // 详细模式表单
                                switch currentStep {
                                case 1:
                                    basicInfoSection
                                case 2:
                                    Group {
                                        imageSection
                                        valuationMethodSection
                                    }
                                case 3:
                                    Group {
                                        keyDatesSection
                                        warrantyInfoSection
                                    }
                                case 4:
                                    Group {
                                        tagsSection
                                        extendedInfoSection
                                    }
                                default:
                                    EmptyView()
                                }
                            }
                        }
                        .padding(.horizontal)

                        // 导航按钮
                        navigationButtonsSection
                            .padding(.horizontal)
                            .padding(.bottom, 20)
                    }
                    .padding(.top, 10)
                }
            }
            .navigationTitle("添加产品")
            .navigationBarItems(
                leading: showCancelButton ? Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                } : nil
            )
            .onDisappear {
                // 🔥 视图销毁时强制清理内存
                autoreleasepool {
                    selectedImage = nil
                    liftedImage = nil
                    stickerImage = nil
                    finalImageData = nil
                }
                ImageManager.shared.clearTemporaryCache()
                logMemoryUsage("AddProductView销毁后清理")
            }
            .sheet(isPresented: $showingCategorySelector) {
                CategorySelectorView(selectedCategory: $selectedCategory)
            }
            .onChange(of: selectedCategory) { _, _ in
                // 🔥 修复：类别选择变化时实时验证表单状态
                validateCurrentStep()
            }
            .sheet(isPresented: $showingTagSelector) {
                TagSelectorView()
            }
            .sheet(isPresented: $showingPurchaseChannelPicker) {
                PurchaseChannelPickerView(viewModel: purchaseChannelViewModel, selectedChannel: $selectedPurchaseChannel)
                    .environmentObject(themeManager)
            }
            .photosPicker(isPresented: $isShowingImagePicker, selection: $photoPickerItems, maxSelectionCount: 1, matching: .images)
            .onChange(of: photoPickerItems) { _, newItems in
                guard let item = newItems.first else { return }
                Task {
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        DispatchQueue.main.async {
                            // 清理之前的图片数据
                            self.finalImageData = nil
                            self.selectedImage = image
                            self.liftedImage = nil // 重置抠图结果
                            self.stickerImage = nil // 重置贴纸结果
                        }
                    }
                }
            }
            .sheet(isPresented: $isShowingCamera) {
                AutoProcessCameraView { originalImage, liftedImg, stickerImg in
                    logMemoryUsage("拍照完成，设置图片前")

                    // 🔥 关键修复：接收的已经是压缩后的图片，直接设置
                    selectedImage = originalImage
                    liftedImage = liftedImg
                    stickerImage = stickerImg

                    logMemoryUsage("拍照完成，设置图片后")

                    // 延迟一点时间让SubjectLiftView完成初始化后自动处理
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        // SubjectLiftView会在onAppear中自动选择最佳图片并触发压缩
                        // 这里不需要额外处理，因为SubjectLiftView会自动优化
                    }
                }
            }
            .actionSheet(isPresented: $isShowingImageOptions) {
                ActionSheet(
                    title: Text("选择图片"),
                    message: Text("请选择获取图片的方式"),
                    buttons: [
                        .default(Text("拍照")) {
                            checkCameraPermission()
                        },
                        .default(Text("从相册选择")) {
                            isShowingImagePicker = true
                        },
                        .cancel(Text("取消"))
                    ]
                )
            }
            .sheet(isPresented: $isShowingSubjectLiftHelp) {
                SubjectLiftHelpView()
            }
            .fileImporter(
                isPresented: $productViewModel.showWarrantyFileImporter,
                allowedContentTypes: productViewModel.allowedWarrantyContentTypes,
                allowsMultipleSelection: false
            ) { result_in_array in
                let singleResult: Result<URL, Error>
                switch result_in_array {
                case .success(let urls):
                    if let firstUrl = urls.first {
                        singleResult = .success(firstUrl)
                    } else {
                        singleResult = .failure(NSError(domain: "AddProductView", code: 0, userInfo: [NSLocalizedDescriptionKey: "No file URL found after selection."]))
                    }
                case .failure(let error):
                    singleResult = .failure(error)
                }
                productViewModel.handleWarrantyFileSelection(result: singleResult)
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("提示"), 
                    message: Text(alertMessage), 
                    primaryButton: .default(Text("确定")) {
                        if alertMessage.contains("切换录入模式") {
                            withAnimation {
                                isQuickMode.toggle()
                                currentStep = 1
                                validateCurrentStep()
                            }
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .onAppear {
                productViewModel.resetWarrantyInputs()
                tagViewModel.clearSelection()

                // 设置预设图片
                if let preset = presetImage {
                    selectedImage = preset
                }
                if let presetLifted = presetLiftedImage {
                    liftedImage = presetLifted
                }
                if let presetSticker = presetStickerImage {
                    stickerImage = presetSticker
                }

                // 如果有预设图片，自动处理压缩
                if presetStickerImage != nil || presetLiftedImage != nil || presetImage != nil {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // 延迟一点时间让SubjectLiftView完成初始化
                        // SubjectLiftView会在onAppear中自动选择最佳图片并触发压缩
                    }
                }

                // 🔥 修复：基于实际表单内容验证初始状态，而不是硬编码为true
                validateCurrentStep()

                // 初始化智能推荐
                packageUnit = recommendPackageUnit(for: selectedUnit)
            }
            // 移除实时验证，只在保存时验证
        }
    }

    // 进度指示器
    private var progressIndicator: some View {
        VStack(spacing: 4) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景条
                    Rectangle()
                        .foregroundColor(Color.gray.opacity(0.2))
                        .frame(height: 8)
                        .cornerRadius(4)

                    // 进度条
                    Rectangle()
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .frame(width: geometry.size.width * CGFloat(currentStep) / CGFloat(actualTotalSteps), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
            .padding(.horizontal)

            // 步骤指示器
            HStack {
                ForEach(1...actualTotalSteps, id: \.self) { step in
                    Spacer()
                    VStack(spacing: 4) {
                        ZStack {
                            Circle()
                                .fill(step <= currentStep ? themeManager.currentTheme.primaryColor : Color.gray.opacity(0.3))
                                .frame(width: 24, height: 24)

                            Text("\(step)")
                                .font(.caption)
                                .foregroundColor(.white)
                        }

                        Text(stepTitles[step - 1])
                            .font(.caption)
                            .foregroundColor(step == currentStep ? themeManager.currentTheme.primaryColor : .gray)
                    }
                    Spacer()
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 8)
        }
        .padding(.top, 16)
    }

    // 导航按钮
    private var navigationButtonsSection: some View {
        Section {
            if currentStep == actualTotalSteps && hasPresetImage {
                // 拍照模式：保存按钮居中显示
                VStack {
                    Button(action: validateAndSaveProduct) {
                        HStack {
                            if isSaving {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("保存中...")
                            } else {
                                Text("保存")
                                Image(systemName: "checkmark")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .foregroundColor(.white)
                        .padding(.vertical, 12)
                        .background(isSaving ? Color.gray : themeManager.currentTheme.primaryColor)
                        .cornerRadius(10)
                    }
                    .disabled(isSaving)
                    .buttonStyle(BorderlessButtonStyle())
                }
                .padding(.horizontal)
            } else {
                // 普通模式：左右布局
                HStack {
                    // 上一步按钮
                    if currentStep > 1 {
                        Button(action: {
                            withAnimation {
                                currentStep -= 1
                                validateCurrentStep()
                            }
                        }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                Text("上一步")
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .padding(.vertical, 8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    } else {
                        Spacer()
                    }

                    Spacer()
                        .frame(width: 20)

                    // 下一步/保存按钮
                    if currentStep < actualTotalSteps {
                        Button(action: {
                            if validateCurrentStep() {
                                withAnimation {
                                    currentStep += 1
                                    validateCurrentStep()
                                }
                            }
                        }) {
                            HStack {
                                Text("下一步")
                                Image(systemName: "chevron.right")
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!isFormValid)
                        .buttonStyle(BorderlessButtonStyle())
                    } else {
                        Button(action: validateAndSaveProduct) {
                            HStack {
                                if isSaving {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                    Text("保存中...")
                                } else {
                                    Text("保存")
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background((isFormValid && !isSaving) ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!isFormValid || isSaving)
                        .buttonStyle(BorderlessButtonStyle())
                    }
                }
            }
        }
    }

    // MARK: - Form Sections

    private var basicInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("产品名称")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                TextField("请输入产品名称", text: $name)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(name.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
                    .onChange(of: name) { _, _ in
                        // 🔥 修复：实时验证表单状态
                        validateCurrentStep()
                    }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("品牌")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("请输入品牌（选填）", text: $brand)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("型号/款式")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("请输入型号或款式（选填）", text: $model)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("价格")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                TextField("请输入价格", text: $price)
                    .keyboardType(.decimalPad)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(price.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
                    .onChange(of: price) { _, _ in
                        // 🔥 修复：实时验证表单状态
                        validateCurrentStep()
                    }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("购买日期")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                DatePicker("", selection: $purchaseDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("类别")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                Button(action: {
                    showingCategorySelector = true
                }) {
                    HStack {
                        Text(selectedCategory?.name ?? "请选择类别")
                            .foregroundColor(selectedCategory == nil ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(selectedCategory == nil ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
                }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("购买渠道")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Button(action: {
                    showingPurchaseChannelPicker = true
                }) {
                    HStack {
                        Text(selectedPurchaseChannel?.name ?? "请选择购买渠道（选填）")
                            .foregroundColor(selectedPurchaseChannel == nil ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("数量")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Stepper("数量: \(quantity)", value: $quantity, in: 1...99)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            // 产品类型选择
            VStack(alignment: .leading, spacing: 12) {
                Text("产品类型")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    // 实物耐用品
                    productTypeButton(
                        title: "实物耐用品",
                        subtitle: "如电器、家具、工具等",
                        icon: "cube.box",
                        isSelected: !isVirtualProduct && !isConsumable
                    ) {
                        isVirtualProduct = false
                        isConsumable = false
                    }
                    
                    // 实物消耗品
                    productTypeButton(
                        title: "实物消耗品",
                        subtitle: "如食品、药品、日用品等",
                        icon: "drop.fill",
                        isSelected: !isVirtualProduct && isConsumable
                    ) {
                        isVirtualProduct = false
                        isConsumable = true
                    }
                    
                    // 虚拟订阅
                    productTypeButton(
                        title: "虚拟订阅",
                        subtitle: "如软件订阅、流媒体服务等",
                        icon: "icloud.fill",
                        isSelected: isVirtualProduct
                    ) {
                        isVirtualProduct = true
                        isConsumable = false
                    }
                }
                
                // 消耗品设置
                if isConsumable && !isVirtualProduct {
                    consumableSettingsSection
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
                
                // 虚拟订阅设置
                if isVirtualProduct {
                    virtualSubscriptionSettingsSection
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(.vertical, 4)


            // 必填项提示
            HStack {
                Text("*")
                    .foregroundColor(.red)
                Text("表示必填项")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 8)
        }
    }

    private var valuationMethodSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("价值计算方式")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                CustomSegmentedControl(
                    selection: $valuationMethod,
                    items: valuationMethods.map { $0.0 }
                ) { methodKey in
                    Text(valuationMethods.first { $0.0 == methodKey }?.1 ?? "")
                }
                .accentColor(themeManager.currentTheme.primaryColor)

                if valuationMethod == "daily" {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        Text("像冰箱、热水器等每天持续服役的产品，建议选择按天计算")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                } else {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        Text("像衣物、工具等间歇使用的产品，建议选择按使用次数计算")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)
        }
    }

    private var imageSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("产品图片")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()

                    Button(action: {
                        isShowingSubjectLiftHelp = true
                    }) {
                        Image(systemName: "questionmark.circle")
                            .foregroundColor(.blue)
                            .font(.caption)
                    }
                }

                if let image = selectedImage {
                    // 显示已选择的图片和抠图功能
                    SubjectLiftView(
                        image: $selectedImage,
                        liftedImage: $liftedImage,
                        stickerImage: $stickerImage
                    ) { compressedData, displayImage in
                        finalImageData = compressedData
                        // 确保图片能正确显示（SubjectLiftView已经更新了绑定的图片）
                        logMemoryUsage("详细模式图片选择完成")
                    }

                    // 更换图片按钮
                    Button(action: {
                        // 强制清理当前图片数据和内存
                        autoreleasepool {
                            finalImageData = nil
                            if let img = selectedImage {
                                ImageManager.shared.clearImageReference(&selectedImage)
                            }
                            if let img = liftedImage {
                                ImageManager.shared.clearImageReference(&liftedImage)
                            }
                            if let img = stickerImage {
                                ImageManager.shared.clearImageReference(&stickerImage)
                            }
                        }

                        // 触发内存清理
                        ImageManager.shared.clearTemporaryCache()

                        isShowingImageOptions = true
                        logMemoryUsage("更换图片后清理完成")
                    }) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("更换图片")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    // 选择图片按钮
                    Button(action: {
                        isShowingImageOptions = true
                    }) {
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(height: 200)
                                .cornerRadius(12)

                            VStack(spacing: 12) {
                                Image(systemName: "camera.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.gray)

                                Text("点击拍照或选择图片")
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.vertical, 4)
        }
    }

    private var keyDatesSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("有效期设置")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Toggle("设置产品有效期", isOn: $showExpiryDate)
                    .padding(.vertical, 4)
                    .tint(themeManager.currentTheme.primaryColor)

                if showExpiryDate {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("有效期至")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        DatePicker("", selection: Binding(
                            get: { expiryDate ?? Date().addingTimeInterval(86400 * 30) },
                            set: { expiryDate = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.orange)
                        Text("设置有效期后，系统将在产品即将过期时提醒您")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: showExpiryDate)
        }
    }

    private var warrantyInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("保修信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Toggle("提供保修信息", isOn: $productViewModel.hasWarranty)
                    .padding(.vertical, 4)
                    .tint(themeManager.currentTheme.primaryColor)

                if productViewModel.hasWarranty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修截止日期")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        DatePicker("", selection: Binding(
                            get: { productViewModel.warrantyEndDateInput ?? Date().addingTimeInterval(86400 * 365) },
                            set: { productViewModel.warrantyEndDateInput = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修范围摘要")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        TextEditor(text: $productViewModel.warrantyDetailsInput)
                            .frame(minHeight: 80)
                            .padding(4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修凭证")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Button(action: {
                            productViewModel.showWarrantyFileImporter = true
                        }) {
                            HStack {
                                if let fileName = productViewModel.selectedWarrantyFileName, !fileName.isEmpty {
                                    Image(systemName: "doc.text.fill")
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                    Text(fileName)
                                        .lineLimit(1)
                                        .truncationMode(.middle)
                                } else {
                                    Image(systemName: "icloud.and.arrow.up")
                                        .foregroundColor(.secondary)
                                    Text("上传保修凭证（选填）")
                                        .foregroundColor(.secondary)
                                }
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())

                        if productViewModel.selectedWarrantyFileURL != nil || (productViewModel.currentWarrantyImagePath != nil && !productViewModel.currentWarrantyImagePath!.isEmpty) {
                            Button(action: {
                                productViewModel.clearSelectedWarrantyFile()
                            }) {
                                HStack {
                                    Spacer()
                                    Text("清除已选凭证")
                                        .font(.caption)
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.caption)
                                }
                                .foregroundColor(.red)
                                .padding(.top, 4)
                            }
                            .buttonStyle(BorderlessButtonStyle())
                            .transition(.opacity)
                        }
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)
                }
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: productViewModel.hasWarranty)
        }
    }

    private var tagsSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("标签")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Button(action: {
                    showingTagSelector = true
                }) {
                    HStack {
                        if tagViewModel.selectedTags.isEmpty {
                            Text("选择标签（选填）")
                                .foregroundColor(.secondary)
                        } else {
                            Text("已选择 \(tagViewModel.selectedTags.count) 个标签")
                                .foregroundColor(.primary)
                        }
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())

                if !tagViewModel.selectedTags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(Array(tagViewModel.selectedTags), id: \.self) { tag in
                                HStack(spacing: 4) {
                                    Circle()
                                        .fill(tagViewModel.getTagColor(tag))
                                        .frame(width: 8, height: 8)

                                    Text(tag.name ?? "")
                                        .font(.caption)
                                }
                                .padding(.horizontal, 10)
                                .padding(.vertical, 6)
                                .background(tagViewModel.getTagColor(tag).opacity(0.1))
                                .foregroundColor(tagViewModel.getTagColor(tag))
                                .cornerRadius(20)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    .transition(.opacity)
                }

                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                    Text("标签可以帮助您更好地组织和筛选产品")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: tagViewModel.selectedTags.count)
        }
    }

    // 消耗品设置区域
    private var consumableSettingsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("消耗品设置")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .padding(.top, 8)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("当前库存量")
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    TextField("输入数量", text: $currentQuantity)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Picker("购买单位", selection: $selectedUnit) {
                        ForEach(["个", "毫升", "克", "片", "粒", "滴", "勺", "杯", "瓶", "包", "盒", "袋", "箱", "升", "千克"], id: \.self) { unit in
                            Text(unit).tag(unit)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 100)
                    .onChange(of: selectedUnit) { newUnit in
                        // 智能推荐包装单位
                        packageUnit = recommendPackageUnit(for: newUnit)
                    }
                }
            }

            // 包装规格设置（仅在需要时显示）
            if needsPackageSpec(for: selectedUnit) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("包装规格")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    HStack {
                        Text("每\(selectedUnit)包含:")
                            .font(.caption)

                        TextField("数量", text: $packageSize)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)

                        Picker("包装单位", selection: $packageUnit) {
                            ForEach(["片", "粒", "毫升", "克", "个", "滴", "勺", "杯"], id: \.self) { unit in
                                Text(unit).tag(unit)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .frame(width: 80)
                    }

                    // 智能提示
                    if !packageSize.isEmpty, let size = Double(packageSize), size > 0 {
                        HStack {
                            Image(systemName: "lightbulb.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text("库存按\"\(selectedUnit)\"管理，使用按\"\(packageUnit)\"计算")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .padding(.top, 4)

                        Text("设置：1\(selectedUnit) = \(formatQuantity(size))\(packageUnit)")
                            .font(.caption)
                            .foregroundColor(.green)
                            .fontWeight(.medium)
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("低库存预警阈值")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                TextField("输入预警数量（选填）", text: $minStockAlert)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("预期消耗速率")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                TextField("每天消耗量（选填）", text: $consumptionRate)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                Text("设置为消耗品后，可以记录使用量并跟踪库存变化")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(8)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var extendedInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("扩展信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                VStack(alignment: .leading, spacing: 4) {
                    Text("购买动机")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Picker("", selection: $purchaseMotivation) {
                        Text("请选择").tag("")
                        ForEach(motivations, id: \.self) { motivation in
                            Text(motivation).tag(motivation)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("初始满意度")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= initialSatisfaction ? "star.fill" : "star")
                                .foregroundColor(index <= initialSatisfaction ? .yellow : .gray)
                                .font(.title2)
                                .onTapGesture {
                                    withAnimation {
                                        initialSatisfaction = index
                                    }
                                }
                        }

                        Spacer()

                        Text(satisfactionText(for: initialSatisfaction))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("预期使用寿命")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextField("例如: 3年, 1000小时（选填）", text: $expectedLifespan)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("预期使用频率")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextField("例如: 每天, 每周3次（选填）", text: $expectedUsageFrequency)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("备注")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextEditor(text: $purchaseNotes)
                        .frame(minHeight: 100)
                        .padding(4)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)
            }
            .padding(.vertical, 4)
        }
    }

    // 根据满意度返回文字描述
    private func satisfactionText(for rating: Int) -> String {
        switch rating {
        case 1:
            return "非常不满意"
        case 2:
            return "不太满意"
        case 3:
            return "一般"
        case 4:
            return "比较满意"
        case 5:
            return "非常满意"
        default:
            return "未评价"
        }
    }

    // MARK: - Helper Methods

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("AddProductView: \(context)")
    }

    // 验证当前步骤（仅用于步骤切换，不再实时验证）
    func validateCurrentStep() -> Bool {
        if isQuickMode {
            // 快速模式验证
            if hasPresetImage {
                // 有预设图片的全合一验证
                if name.isEmpty || price.isEmpty || selectedCategory == nil {
                    isFormValid = false
                    return false
                }
                
                guard let priceValue = Double(price), priceValue > 0 else {
                    isFormValid = false
                    return false
                }
                
                isFormValid = true
                return true
            } else {
                // 无预设图片的分步验证
                switch currentStep {
                case 1:
                    // 验证基本信息
                    if name.isEmpty || price.isEmpty || selectedCategory == nil {
                        isFormValid = false
                        return false
                    }

                    // 验证价格格式
                    guard let priceValue = Double(price), priceValue > 0 else {
                        isFormValid = false
                        return false
                    }

                    isFormValid = true
                    return true

                case 2:
                    // 满意度步骤，验证初始满意度已设置
                    isFormValid = initialSatisfaction > 0
                    return isFormValid

                default:
                    isFormValid = false
                    return false
                }
            }
        } else {
            // 详细模式验证
            switch currentStep {
            case 1:
                // 验证基本信息
                if name.isEmpty || price.isEmpty || selectedCategory == nil {
                    isFormValid = false
                    return false
                }

                // 验证价格格式
                guard let priceValue = Double(price), priceValue > 0 else {
                    isFormValid = false
                    return false
                }

                isFormValid = true
                return true

            case 2:
                // 图片和计算方式步骤，没有必填项
                isFormValid = true
                return true

            case 3:
                // 日期和保修信息步骤，没有必填项
                isFormValid = true
                return true

            case 4:
                // 标签和扩展信息步骤，没有必填项
                isFormValid = true
                return true

            default:
                isFormValid = false
                return false
            }
        }
    }

    // 验证并保存产品
    private func validateAndSaveProduct() {
        // 防止重复保存
        guard !isSaving else {
            print("⚠️ 正在保存中，忽略重复请求")
            return
        }

        print("🚀 开始保存产品...")
        
        // 检查是否可以添加产品（Premium限制）
        guard productViewModel.canAddProduct() else {
            print("❌ 达到免费版本限制")
            productViewModel.showPremiumSheet = true
            isSaving = false
            return
        }
        
        isSaving = true

        // 最终验证
        guard !name.isEmpty else {
            print("❌ 产品名称为空")
            alertMessage = "请输入产品名称"
            showingAlert = true
            isSaving = false
            return
        }

        guard let priceValue = Double(price), priceValue > 0 else {
            print("❌ 价格无效: '\(price)'")
            alertMessage = "请输入有效的价格"
            showingAlert = true
            isSaving = false
            return
        }

        guard selectedCategory != nil else {
            print("❌ 未选择类别")
            alertMessage = "请选择产品类别"
            showingAlert = true
            isSaving = false
            return
        }

        print("✅ 最终验证通过，开始保存...")
        print("- 产品名称: \(name)")
        print("- 价格: \(priceValue)")
        print("- 类别: \(selectedCategory?.name ?? "nil")")
        print("- 图片: \(selectedImage != nil ? "有" : "无")")

        // 保存产品
        saveProduct()
    }

    // 检查相机权限
    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            isShowingCamera = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.isShowingCamera = true
                    } else {
                        self.alertMessage = "需要相机权限才能拍照，请在设置中允许访问相机"
                        self.showingAlert = true
                    }
                }
            }
        case .denied, .restricted:
            alertMessage = "需要相机权限才能拍照，请在设置中允许访问相机"
            showingAlert = true
        @unknown default:
            alertMessage = "相机权限状态未知"
            showingAlert = true
        }
    }

    // 保存产品
    private func saveProduct() {
        print("💾 开始保存产品到数据库...")
        withAnimation {
            let newProduct = Product(context: viewContext)

            // 基本信息
            newProduct.id = UUID()
            newProduct.name = name
            newProduct.brand = brand
            newProduct.model = model
            newProduct.price = Double(price) ?? 0
            newProduct.purchaseDate = purchaseDate
            newProduct.category = selectedCategory

            // 购买渠道处理
            if let channel = selectedPurchaseChannel {
                // 使用新的关系模型
                newProduct.purchaseChannelRelation = channel
                purchaseChannelViewModel.incrementChannelUsageCount(channel)
                // 同时保留字符串值以确保兼容性
                newProduct.purchaseChannel = channel.name
            } else if !purchaseChannel.isEmpty {
                // 如果直接输入了渠道名称但没有选择渠道对象，尝试查找或创建对应的渠道
                newProduct.purchaseChannel = purchaseChannel
                purchaseChannelViewModel.migrateStringChannelToRelation(newProduct)
            }

            newProduct.valuationMethod = valuationMethod
            newProduct.isVirtualProduct = isVirtualProduct // 保存虚拟商品状态
            
            // 虚拟订阅属性保存
            if isVirtualProduct {
                // 将续费日期设置为过期日期
                newProduct.expiryDate = renewalDate
                
                // 将订阅相关信息保存到备注字段
                var subscriptionInfo = "订阅周期: \(subscriptionCycles.first { $0.0 == subscriptionCycle }?.1 ?? subscriptionCycle)"
                if isSharedSubscription {
                    subscriptionInfo += "\n最大用户数: \(maxUsers)"
                }
                if !subscriptionNotes.isEmpty {
                    subscriptionInfo += "\n备注: \(subscriptionNotes)"
                }
                newProduct.purchaseNotes = subscriptionInfo
                
                // 设置预期使用频率（基于订阅类型的默认值）
                if expectedUsageFrequency.isEmpty {
                    newProduct.expectedUsageFrequency = "15" // 订阅服务默认每月15次
                }
            }

            // 消耗品属性保存
            newProduct.isConsumable = isConsumable
            if isConsumable {
                // 对于消耗品，将初始库存量设置到quantity字段
                let initialStock = Double(currentQuantity) ?? 0
                newProduct.quantity = Int16(initialStock)
                newProduct.currentQuantity = initialStock
                newProduct.unitType = selectedUnit
                newProduct.minStockAlert = Double(minStockAlert) ?? 0
                newProduct.consumptionRate = Double(consumptionRate) ?? 0

                // 包装规格自动转换为双单位系统
                if needsPackageSpec(for: selectedUnit) && !packageSize.isEmpty,
                   let packageSizeValue = Double(packageSize), packageSizeValue > 0 {
                    // 自动启用双单位系统
                    newProduct.consumptionUnitType = packageUnit
                    newProduct.unitConversionRatio = packageSizeValue
                } else {
                    // 使用单一单位系统
                    newProduct.consumptionUnitType = nil
                    newProduct.unitConversionRatio = 1.0
                }
            } else {
                // 对于普通产品，使用quantity字段
                newProduct.quantity = Int16(quantity)
            }

            // 🔥 图片数据保存 - 强制内存管理版本
            autoreleasepool {
                if let imageData = finalImageData {
                    print("✅ 使用压缩后的图片数据: \(imageData.count) bytes")
                    newProduct.images = imageData
                } else if let image = selectedImage {
                    // 降级处理：如果没有压缩数据，使用原有逻辑
                    print("⚠️ finalImageData为空，使用降级处理")
                    autoreleasepool {
                        let imageData = ImageManager.shared.processTransparentImage(image) ?? image.jpegData(compressionQuality: 0.8)
                        newProduct.images = imageData
                        print("✅ 降级处理完成: \(imageData?.count ?? 0) bytes")
                    }
                } else {
                    print("❌ 没有任何图片数据可保存")
                }
                
                // 🔥 立即清空图片引用，避免CoreData保存时的内存累积
                self.selectedImage = nil
                self.liftedImage = nil
                self.stickerImage = nil
                // 注意：不清理finalImageData，因为后面还需要用到
            }

            // 关键日期
            if showExpiryDate, let expiryDate = expiryDate {
                newProduct.expiryDate = expiryDate
            }

            if productViewModel.hasWarranty, let warrantyEndDate = productViewModel.warrantyEndDateInput {
                newProduct.warrantyEndDate = warrantyEndDate
                newProduct.warrantyDetails = productViewModel.warrantyDetailsInput
                newProduct.warrantyImage = productViewModel.selectedWarrantyFileName

                // Handle warranty file copying if needed
                if let sourceURL = productViewModel.selectedWarrantyFileURL {
                    productViewModel.saveWarrantyFile(from: sourceURL, for: newProduct)
                }
            }

            // 扩展信息
            newProduct.purchaseMotivation = purchaseMotivation
            newProduct.initialSatisfaction = Int16(initialSatisfaction)
            newProduct.purchaseNotes = purchaseNotes
            newProduct.expectedLifespan = expectedLifespan.isEmpty ? 0 : Int16(expectedLifespan) ?? 0
            newProduct.expectedUsageFrequency = expectedUsageFrequency

            // 标签
            for tag in tagViewModel.selectedTags {
                newProduct.addToTags(tag)
            }

            // 🔥 保存前强制清理内存，减少CoreData保存时的内存峰值
            autoreleasepool {
                ImageManager.shared.clearTemporaryCache()
            }
            
            // 保存
            do {
                print("💾 正在保存到Core Data...")
                // 🔥 CoreData保存也用autoreleasepool包装
                try autoreleasepool {
                    try viewContext.save()
                }
                print("✅ 保存成功！")
                
                // 🔥 重要：更新Keychain计数器保持同步
                productViewModel.keychainManager.incrementProductCount()
                print("🔄 已更新Keychain产品计数: \(productViewModel.keychainManager.productCount)")
                
                // 🔥 保存完成后立即清理所有相关内存
                autoreleasepool {
                    self.finalImageData = nil
                    ImageManager.shared.clearTemporaryCache()
                }

                // 平滑将新产品添加到列表
                DispatchQueue.main.async {
                    print("📱 更新UI并关闭页面...")
                    // 使用平滑添加方法避免刷新闪烁
                    productViewModel.smoothlyAddProduct(newProduct)

                    // 重置保存状态
                    self.isSaving = false

                    // 🔥 强制清理所有图片内存 - 已经在保存前清理过了
                    // 这里不需要再清理，避免重复操作

                    // 🔥 延迟强制垃圾回收，确保CoreData写入完成
                    DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 0.1) {
                        autoreleasepool {
                            // 多层清理确保内存释放
                            for _ in 0..<3 {
                                autoreleasepool {
                                    ImageManager.shared.clearCache()
                                }
                            }
                        }
                    }

                    self.logMemoryUsage("保存完成后清理")

                    // 如果有自定义关闭回调，使用它；否则使用默认的dismiss
                    if let onSaveCompleted = self.onSaveCompleted {
                        print("🔄 使用自定义关闭回调")
                        onSaveCompleted()
                    } else {
                        print("🔄 使用默认dismiss")
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            } catch {
                let nsError = error as NSError
                print("❌ 保存失败: \(nsError), \(nsError.userInfo)")
                alertMessage = "保存失败：\(nsError.localizedDescription)"
                showingAlert = true
                isSaving = false
            }
        }
    }

    // MARK: - 辅助方法

    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return String(format: "%.0f", quantity)
        } else {
            return String(format: "%.1f", quantity)
        }
    }

    // 智能单位推荐
    private func recommendPackageUnit(for purchaseUnit: String) -> String {
        let recommendations = [
            "盒": "片",
            "瓶": "毫升",
            "箱": "瓶",
            "包": "个",
            "袋": "个",
            "升": "毫升",
            "千克": "克"
        ]
        return recommendations[purchaseUnit] ?? "个"
    }

    // 检查是否需要包装规格
    private func needsPackageSpec(for unit: String) -> Bool {
        let unitsNeedingSpec = ["盒", "瓶", "箱", "包", "袋"]
        return unitsNeedingSpec.contains(unit)
    }
    
    // 虚拟订阅设置部分
    private var virtualSubscriptionSettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("订阅设置")
                .font(.headline)
                .foregroundColor(.primary)
            
            // 订阅周期
            VStack(alignment: .leading, spacing: 8) {
                Text("计费周期")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                CustomSegmentedControl(
                    selection: $subscriptionCycle,
                    items: subscriptionCycles.map { $0.0 }
                ) { cycleKey in
                    Text(subscriptionCycles.first { $0.0 == cycleKey }?.1 ?? "")
                }
                .accentColor(themeManager.currentTheme.primaryColor)
            }
            
            // 下次续费日期
            VStack(alignment: .leading, spacing: 8) {
                Text("下次续费日期")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                DatePicker("", selection: $renewalDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .onChange(of: subscriptionCycle) { _, newCycle in
                        // 根据订阅周期自动计算续费日期
                        updateRenewalDate(for: newCycle)
                    }
            }
            
            // 共享设置
            VStack(alignment: .leading, spacing: 8) {
                Toggle("共享订阅", isOn: $isSharedSubscription)
                    .tint(themeManager.currentTheme.primaryColor)
                
                if isSharedSubscription {
                    HStack {
                        Text("最大用户数")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        TextField("1", text: $maxUsers)
                            .keyboardType(.numberPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)
                    }
                    .transition(.opacity)
                }
            }
            
            // 订阅备注
            VStack(alignment: .leading, spacing: 8) {
                Text("备注（可选）")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                TextField("如：家庭套餐、学生优惠等", text: $subscriptionNotes, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(2...4)
            }
        }
        .padding(16)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // 根据订阅周期更新续费日期
    private func updateRenewalDate(for cycle: String) {
        let calendar = Calendar.current
        let baseDate = Date()
        
        switch cycle {
        case "monthly":
            renewalDate = calendar.date(byAdding: .month, value: 1, to: baseDate) ?? baseDate
        case "quarterly":
            renewalDate = calendar.date(byAdding: .month, value: 3, to: baseDate) ?? baseDate
        case "yearly":
            renewalDate = calendar.date(byAdding: .year, value: 1, to: baseDate) ?? baseDate
        case "lifetime":
            renewalDate = calendar.date(byAdding: .year, value: 99, to: baseDate) ?? baseDate
        default:
            renewalDate = calendar.date(byAdding: .month, value: 1, to: baseDate) ?? baseDate
        }
    }
}

struct AddProductView_Previews: PreviewProvider {
    static var previews: some View {
        AddProductView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(TagViewModel(
                repository: TagRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}