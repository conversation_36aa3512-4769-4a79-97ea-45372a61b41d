import SwiftUI

struct AddJointUsageRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    let link: ProductLink
    var onReturn: (() -> Void)?
    
    @State private var date = Date()
    @State private var scenario = ""
    @State private var satisfaction = 3
    @State private var notes = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    // 获取关联产品
    private var sourceProduct: Product? { link.sourceProduct }
    private var targetProduct: Product? { link.targetProduct }
    
    // 场景选项
    private let scenarios = ["日常使用", "工作", "学习", "旅行", "聚会", "运动", "共同使用", "其他"]
    
    var body: some View {
        NavigationView {
            Form {
                // 关联产品信息
                Section(header: Text("关联产品信息")) {
                    HStack {
                        Text("主产品")
                        Spacer()
                        Text(sourceProduct?.name ?? "未知产品")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("关联产品")
                        Spacer()
                        Text(targetProduct?.name ?? "未知产品")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("关系类型")
                        Spacer()
                        Text(link.relationshipType ?? "其他")
                            .foregroundColor(link.relationshipTypeEnum.color)
                    }
                }
                
                // 基本信息
                Section(header: Text("使用信息")) {
                    DatePicker("使用日期", selection: $date, displayedComponents: .date)
                    
                    Picker("使用场景", selection: $scenario) {
                        Text("请选择").tag("")
                        ForEach(scenarios, id: \.self) { scenario in
                            Text(scenario).tag(scenario)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // 满意度
                Section(header: Text("满意度评价")) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("使用满意度")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            ForEach(1...5, id: \.self) { index in
                                Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                    .foregroundColor(index <= satisfaction ? .yellow : .gray)
                                    .font(.title2)
                                    .onTapGesture {
                                        satisfaction = index
                                    }
                            }
                        }
                        .frame(maxWidth: .infinity)
                    }
                }
                
                // 备注
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                    
                    Text("此记录将同时添加到两个关联产品的使用记录中")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 保存按钮
                Section {
                    Button(action: saveJointUsageRecord) {
                        Text("保存共同使用记录")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.primaryColor)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("记录共同使用")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if let onReturn = onReturn {
                        Button("返回") {
                            onReturn()
                        }
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                // 设置默认场景为"共同使用"
                if scenario.isEmpty {
                    scenario = "共同使用"
                }
            }
        }
    }
    
    // 保存共同使用记录
    private func saveJointUsageRecord() {
        guard let source = sourceProduct, let target = targetProduct else {
            alertMessage = "关联产品信息不完整"
            showingAlert = true
            return
        }
        
        let repository = UsageRecordRepository(context: viewContext)
        var success = true
        
        // 为源产品添加使用记录
        success = repository.save { context in
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.product = source
            usageRecord.date = date
            usageRecord.scenario = scenario.isEmpty ? "共同使用" : scenario
            usageRecord.satisfaction = Int16(satisfaction)
            
            // 添加关联信息到备注
            let jointNote = "与【\(target.name ?? "")】共同使用" + (notes.isEmpty ? "" : "\n\n" + notes)
            usageRecord.notes = jointNote
        }
        
        if !success {
            alertMessage = "添加源产品使用记录失败"
            showingAlert = true
            return
        }
        
        // 为目标产品添加使用记录
        success = repository.save { context in
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.product = target
            usageRecord.date = date
            usageRecord.scenario = scenario.isEmpty ? "共同使用" : scenario
            usageRecord.satisfaction = Int16(satisfaction)
            
            // 添加关联信息到备注
            let jointNote = "与【\(source.name ?? "")】共同使用" + (notes.isEmpty ? "" : "\n\n" + notes)
            usageRecord.notes = jointNote
        }
        
        if !success {
            alertMessage = "添加目标产品使用记录失败"
            showingAlert = true
            return
        }
        
        // 更新关联关系的使用次数和最后使用时间
        link.incrementUsageCount()
        
        // 保存上下文
        do {
            try viewContext.save()
            
            // 刷新使用记录数据
            usageViewModel.loadProductData()
            
            // 关闭视图
            if let onReturn = onReturn {
                onReturn()
            } else {
                presentationMode.wrappedValue.dismiss()
            }
        } catch {
            alertMessage = "保存共同使用记录失败: \(error.localizedDescription)"
            showingAlert = true
        }
    }
}

struct AddJointUsageRecordView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        
        // 创建源产品
        let sourceProduct = Product(context: context)
        sourceProduct.id = UUID()
        sourceProduct.name = "MacBook Pro"
        sourceProduct.brand = "Apple"
        
        // 创建目标产品
        let targetProduct = Product(context: context)
        targetProduct.id = UUID()
        targetProduct.name = "Magic Mouse"
        targetProduct.brand = "Apple"
        
        // 创建链接
        let link = ProductLink(context: context)
        link.id = UUID()
        link.sourceProduct = sourceProduct
        link.targetProduct = targetProduct
        link.relationshipType = "配件"
        link.strength = 4
        link.usageCount = 2
        
        return AddJointUsageRecordView(link: link)
            .environment(\.managedObjectContext, context)
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: context),
                expenseRepository: RelatedExpenseRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}