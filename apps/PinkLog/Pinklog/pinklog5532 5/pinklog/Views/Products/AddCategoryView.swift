import SwiftUI
import CoreData

struct AddCategoryView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var themeManager: ThemeManager

    var onAdd: ((String, String, Category?) -> Void)?

    @State private var name: String = ""
    @State private var selectedIcon: String = "folder"
    @State private var selectedParentCategory: Category?
    @State private var showingParentCategorySelector = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 图标选项
    private let icons = [
        "folder", "laptopcomputer", "tshirt", "house", "fork.knife",
        "paintpalette", "heart", "leaf", "book", "briefcase",
        "car", "bicycle", "gamecontroller", "camera", "tv",
        "headphones", "speaker", "keyboard", "desktopcomputer", "printer",
        "bed.double", "sofa", "chair", "lamp", "bathtub",
        "refrigerator", "oven", "microwave", "wineglass", "cup.and.saucer",
        "pills", "cross", "bandage", "facemask", "stethoscope",
        "backpack", "case", "handbag", "shoeprints", "eyeglasses",
        "hammer", "wrench", "screwdriver", "paintbrush", "ruler",
        "gift", "creditcard", "dollarsign.circle", "cart", "bag",
        "airplane", "bus", "tram", "car.ferry", "bicycle",
        "basketball", "football", "baseball", "tennis.racket", "figure.run",
        "music.note", "guitars", "pianokeys", "film", "paintpalette",
        "ellipsis.circle"
    ]

    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section(header: Text("基本信息")) {
                    TextField("类别名称 *", text: $name)

                    Button(action: {
                        showingParentCategorySelector = true
                    }) {
                        HStack {
                            Text("父类别")
                                .foregroundColor(.primary)

                            Spacer()

                            Text(selectedParentCategory?.name ?? "无")
                                .foregroundColor(selectedParentCategory == nil ? .secondary : .primary)

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                // 图标选择
                Section(header: Text("图标")) {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 50))], spacing: 10) {
                        ForEach(icons, id: \.self) { icon in
                            iconButton(icon)
                        }
                    }
                }

                // 预览
                Section(header: Text("预览")) {
                    HStack {
                        Image(systemName: selectedIcon)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text(name.isEmpty ? "类别名称" : name)
                            .font(.body)

                        Spacer()
                    }
                }

                // 保存按钮
                Section {
                    Button(action: saveCategory) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.primaryColor)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("添加类别")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .sheet(isPresented: $showingParentCategorySelector) {
                ParentCategorySelectorView(selectedCategory: $selectedParentCategory)
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
        }
    }

    // 图标按钮
    private func iconButton(_ icon: String) -> some View {
        VStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(selectedIcon == icon ? themeManager.currentTheme.primaryColor : .primary)
                .frame(width: 40, height: 40)
                .background(selectedIcon == icon ? themeManager.currentTheme.primaryColor.opacity(0.2) : Color.clear)
                .cornerRadius(8)
                .onTapGesture {
                    selectedIcon = icon
                }
        }
    }

    // 保存类别
    private func saveCategory() {
        // 验证输入
        if name.isEmpty {
            alertMessage = "请输入类别名称"
            showingAlert = true
            return
        }

        // 调用回调函数
        onAdd?(name, selectedIcon, selectedParentCategory)

        // 如果没有回调函数，直接保存到数据库
        if onAdd == nil {
            let repository = CategoryRepository(context: viewContext)

            // 检查是否已存在同名类别
            let fetchRequest: NSFetchRequest<Category> = Category.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "name == %@", name)

            do {
                let count = try viewContext.count(for: fetchRequest)
                if count > 0 {
                    alertMessage = "已存在同名类别"
                    showingAlert = true
                    return
                }
            } catch {
                print("检查类别失败: \(error)")
            }

            // 创建类别
            let success = repository.save { context in
                let newCategory = Category(context: context)
                newCategory.id = UUID()
                newCategory.name = name
                newCategory.icon = selectedIcon
                newCategory.parentCategory = selectedParentCategory
            }

            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = "添加类别失败"
                showingAlert = true
            }
        } else {
            presentationMode.wrappedValue.dismiss()
        }
    }
}

// 父类别选择器
struct ParentCategorySelectorView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var themeManager: ThemeManager

    @Binding var selectedCategory: Category?

    @State private var categories: [Category] = []
    @State private var searchText = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("搜索类别", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())

                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(8)
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(10)
                .padding(.horizontal)
                .padding(.top, 8)

                // 类别列表
                List {
                    // 无父类别选项
                    Button(action: {
                        selectedCategory = nil
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Text("无")
                                .foregroundColor(.primary)

                            Spacer()

                            if selectedCategory == nil {
                                Image(systemName: "checkmark")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                            }
                        }
                    }

                    // 类别列表
                    ForEach(filteredCategories) { category in
                        Button(action: {
                            selectedCategory = category
                            presentationMode.wrappedValue.dismiss()
                        }) {
                            HStack {
                                if let iconName = category.icon {
                                    Image(systemName: iconName)
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                        .frame(width: 30)
                                }

                                Text(category.name ?? "")
                                    .foregroundColor(.primary)

                                Spacer()

                                if selectedCategory?.id == category.id {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                }
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("选择父类别")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .onAppear {
                loadCategories()
            }
        }
    }

    // 加载类别
    private func loadCategories() {
        let repository = CategoryRepository(context: viewContext)
        categories = repository.fetchAll(sortDescriptors: [NSSortDescriptor(key: "name", ascending: true)])
    }

    // 筛选后的类别
    private var filteredCategories: [Category] {
        if searchText.isEmpty {
            return categories
        } else {
            return categories.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
        }
    }
}

struct AddCategoryView_Previews: PreviewProvider {
    static var previews: some View {
        AddCategoryView()
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
            .environmentObject(ThemeManager())
    }
}
