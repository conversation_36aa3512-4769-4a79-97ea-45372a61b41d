import SwiftUI
import CoreData

struct ProductRelationshipGraphView: View {
    let product: Product
    @StateObject private var linkViewModel: ProductLinkViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel
    
    @State private var scale: CGFloat = 1.0
    @State private var offset = CGSize.zero
    @State private var lastScale: CGFloat = 1.0
    @State private var lastOffset = CGSize.zero
    @State private var showingNodeDetail = false
    @State private var selectedProduct: Product?
    @State private var selectedLink: ProductLink?
    @State private var showingLinkDetail = false
    @State private var isShowingJointUsage = false
    @State private var isAnimating = false
    @State private var hoveredProduct: Product? = nil
    
    // 节点布局半径
    private let nodeRadius: CGFloat = 150
    
    init(product: Product, context: NSManagedObjectContext) {
        self.product = product
        self._linkViewModel = StateObject(wrappedValue: ProductLinkViewModel(context: context))
    }
    
    var body: some View {
        ZStack {
            // 优雅的背景
            backgroundView
            
            // 图表视图
            graphView
                .scaleEffect(scale)
                .offset(x: offset.width, y: offset.height)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            let delta = value / lastScale
                            lastScale = value
                            scale = min(max(scale * delta, 0.5), 3.0)
                        }
                        .onEnded { _ in
                            lastScale = 1.0
                        }
                )
                .simultaneousGesture(
                    DragGesture()
                        .onChanged { value in
                            offset = CGSize(
                                width: lastOffset.width + value.translation.width,
                                height: lastOffset.height + value.translation.height
                            )
                        }
                        .onEnded { _ in
                            lastOffset = offset
                        }
                )
            
            // 控制面板
            controlPanelView
        }
        .navigationTitle("产品关系图")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: resetView) {
                    Label("重置视图", systemImage: "arrow.counterclockwise")
                        .font(.caption)
                }
            }
        }
        .onAppear {
            linkViewModel.setCurrentProduct(product)
            // 强制立即加载数据
            linkViewModel.loadLinks(for: product)
            withAnimation(.easeInOut(duration: 1.5)) {
                isAnimating = true
            }
        }
        .sheet(isPresented: $showingNodeDetail) {
            if let selectedProduct = selectedProduct {
                ProductDetailView(product: selectedProduct)
            }
        }
        .sheet(isPresented: $showingLinkDetail) {
            if let link = selectedLink,
               link.sourceProduct != nil,
               link.targetProduct != nil {
                ZStack {
                    if isShowingJointUsage {
                        // 使用记录添加视图 - 带有优雅的过渡动画
                        AddJointUsageRecordView(link: link, onReturn: {
                            withAnimation(.easeInOut(duration: 0.4)) {
                                isShowingJointUsage = false
                            }
                        })
                            .environment(\.managedObjectContext, product.managedObjectContext!)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                            .environmentObject(usageViewModel)
                            .transition(.opacity)
                    } else {
                        linkDetailView(link: link)
                            .transition(.opacity)
                    }
                }
                .animation(.easeInOut(duration: 0.4), value: isShowingJointUsage)
            } else {
                VStack {
                    ProgressView()
                    Text("加载中...")
                        .padding()
                    Button("关闭") {
                        showingLinkDetail = false
                    }
                    .padding()
                }
            }
        }
    }
    
    // 重置视图
    private func resetView() {
        withAnimation(.spring()) {
            scale = 1.0
            offset = .zero
            lastOffset = .zero
        }
    }
    
    // 优雅的背景视图
    private var backgroundView: some View {
        ZStack {
            // 渐变背景色
            LinearGradient(
                gradient: Gradient(colors: [
                    themeManager.currentTheme.primaryColor.opacity(0.05),
                    Color(UIColor.systemBackground),
                    Color(UIColor.systemBackground).opacity(0.97)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
            
            // 动态背景图案
            ZStack {
                // 柔和的波纹图案
                ForEach(0..<3) { index in
                    Circle()
                        .fill(themeManager.currentTheme.primaryColor.opacity(0.03))
                        .frame(width: 300 + CGFloat(index * 200))
                        .blur(radius: 30 + CGFloat(index * 15))
                        .offset(x: -120, y: -150)
                }
                
                // 点状图案
                ForEach(0..<80) { index in
                    Circle()
                        .fill(themeManager.currentTheme.primaryColor.opacity(0.03 + (Double(index % 5) * 0.01)))
                        .frame(width: 4 + CGFloat(index % 4))
                        .offset(x: CGFloat.random(in: -300...300), 
                                y: CGFloat.random(in: -500...500))
                        .blur(radius: 0.3)
                }
                
                // 动态背景图案
                ZStack {
                    // 柔和的波纹图案
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(themeManager.currentTheme.primaryColor.opacity(0.03))
                            .frame(width: 300 + CGFloat(index * 200))
                            .blur(radius: 30 + CGFloat(index * 15))
                            .offset(x: -120, y: -150)
                    }
                    
                    // 点状图案
                    ForEach(0..<80) { index in
                        Circle()
                            .fill(themeManager.currentTheme.primaryColor.opacity(0.03 + (Double(index % 5) * 0.01)))
                            .frame(width: 4 + CGFloat(index % 4))
                            .offset(x: CGFloat.random(in: -300...300), 
                                    y: CGFloat.random(in: -500...500))
                            .blur(radius: 0.3)
                    }
                    
                    // 柔和的网格背景
                    GridPattern(spacing: 35, lineWidth: 0.5)
                        .stroke(Color.gray.opacity(0.12), lineWidth: 0.5)
                        .scaleEffect(scale)
                        .offset(x: offset.width, y: offset.height)
                        .animation(.easeOut(duration: 0.2), value: scale)
                }
                    .animation(.easeOut(duration: 0.2), value: scale)
            }
        }
    }
    
    // 控制面板视图
    private var controlPanelView: some View {
        VStack(spacing: 16) {
            // 顶部信息面板 - 显示当前产品名称
            VStack(spacing: 4) {
                Text(product.name ?? "未命名产品")
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("\(linkViewModel.links.count) 个关联产品")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(UIColor.secondarySystemBackground).opacity(0.85))
                    .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(themeManager.currentTheme.primaryColor.opacity(0.2), lineWidth: 1)
                    )
            )
            .padding(.top, 12)
            
            Spacer()
            
            HStack {
                // 左侧图例说明
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(["配件", "互补品", "套装"].indices, id: \.self) { index in
                        let types = ["配件", "互补品", "套装"]
                        let type = ProductRelationshipType(rawValue: types[index]) ?? .other
                        
                        HStack(spacing: 6) {
                            Circle()
                                .fill(type.color)
                                .frame(width: 10, height: 10)
                            
                            Text(type.rawValue)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(UIColor.tertiarySystemBackground).opacity(0.8))
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                )
                .padding(.leading)
                
                Spacer()
                
                // 控制按钮面板
                HStack(spacing: 14) {
                    // 缩小按钮
                    Button(action: {
                        withAnimation(.spring(dampingFraction: 0.7)) {
                            scale = max(scale - 0.25, 0.5)
                        }
                        hapticFeedback(style: .light)
                    }) {
                        Image(systemName: "minus.magnifyingglass")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))
                            .frame(width: 38, height: 38)
                            .background(
                                Circle()
                                    .fill(Color(UIColor.tertiarySystemBackground))
                                    .overlay(
                                        Circle()
                                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                                    )
                                    .shadow(color: Color.black.opacity(0.08), radius: 3, x: 0, y: 2)
                            )
                    }
                    
                    // 居中按钮
                    Button(action: {
                        resetView()
                        hapticFeedback(style: .medium)
                    }) {
                        Image(systemName: "dot.viewfinder")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(Color.white)
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                themeManager.currentTheme.primaryColor,
                                                themeManager.currentTheme.primaryColor.opacity(0.8)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: themeManager.currentTheme.primaryColor.opacity(0.3), radius: 5, x: 0, y: 3)
                            )
                    }
                    
                    // 放大按钮
                    Button(action: {
                        withAnimation(.spring(dampingFraction: 0.7)) {
                            scale = min(scale + 0.25, 3.0)
                        }
                        hapticFeedback(style: .light)
                    }) {
                        Image(systemName: "plus.magnifyingglass")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(Color(UIColor.label))
                            .frame(width: 38, height: 38)
                            .background(
                                Circle()
                                    .fill(Color(UIColor.tertiarySystemBackground))
                                    .overlay(
                                        Circle()
                                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                                    )
                                    .shadow(color: Color.black.opacity(0.08), radius: 3, x: 0, y: 2)
                            )
                    }
                }
                .padding(.horizontal, 18)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(
                            Color(UIColor.secondarySystemBackground).opacity(0.9)
                        )
                        .shadow(color: Color.black.opacity(0.08), radius: 10, x: 0, y: 5)
                        .overlay(
                            Capsule()
                                .stroke(Color.gray.opacity(0.15), lineWidth: 0.5)
                        )
                )
                .padding()
            }
        }
    }
    
    // 图表视图
    private var graphView: some View {
        GeometryReader { geometry in
            let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
            
            ZStack {
                // 引导线
                ForEach(relatedProducts.indices, id: \.self) { index in
                    let angle = angleForProduct(relatedProducts[index])
                    let position = positionForAngle(angle, center: center)
                    
                    Line(from: center, to: position)
                        .stroke(Color.gray.opacity(0.15), style: StrokeStyle(lineWidth: 1, dash: [5, 5]))
                        .opacity(isAnimating ? 1 : 0)
                        .animation(.easeInOut(duration: 0.8).delay(0.1 * Double(index)), value: isAnimating)
                }
                
                // 连接线
                ForEach(linkViewModel.links.indices, id: \.self) { index in
                    let link = linkViewModel.links[index]
                    connectionLine(for: link, center: center, index: index)
                }
                
                // 相关产品节点
                ForEach(relatedProducts.indices, id: \.self) { index in
                    let relatedProduct = relatedProducts[index]
                    let angle = angleForProduct(relatedProduct)
                    let position = positionForAngle(angle, center: center)
                    
                    productNode(relatedProduct, position: position, isCenter: false, index: index)
                        .scaleEffect(hoveredProduct?.id == relatedProduct.id ? 1.1 : 1.0)
                        .zIndex(hoveredProduct?.id == relatedProduct.id ? 2 : 0)
                        .animation(.spring(response: 0.3), value: hoveredProduct?.id)
                }
                
                // 中心节点（当前产品）
                productNode(product, position: center, isCenter: true, index: -1)
                    .zIndex(3) // 确保中心节点在最上层
            }
        }
    }
    
    // 获取产品状态对应的图标
    private func getStatusIcon(for status: Product.ProductStatus) -> String {
        switch status {
        case .core: return "star.fill"
        case .worthwhile: return "checkmark.circle"
        case .potential: return "lightbulb"
        case .reconsider: return "exclamationmark.triangle"
        case .sellRecommended: return "arrow.down.circle"
        case .subscriptionActive: return "checkmark.circle.fill"
        case .subscriptionPaused: return "pause.circle.fill"
        case .subscriptionCancelled: return "xmark.circle.fill"
        case .subscriptionExpired: return "clock.fill"
        case .subscriptionTrial: return "timer"
        }
    }
    
    // 产品节点状态指示器
    private struct NodeStatusIndicator: View {
        let status: Product.ProductStatus
        let size: CGFloat
        
        func getStatusIcon(for status: Product.ProductStatus) -> String {
            switch status {
            case .core: return "star.fill"
            case .worthwhile: return "checkmark.circle"
            case .potential: return "lightbulb"
            case .reconsider: return "exclamationmark.triangle"
            case .sellRecommended: return "arrow.down.circle"
            case .subscriptionActive: return "checkmark.circle.fill"
            case .subscriptionPaused: return "pause.circle.fill"
            case .subscriptionCancelled: return "xmark.circle.fill"
            case .subscriptionExpired: return "clock.fill"
            case .subscriptionTrial: return "timer"
            }
        }
        
        var body: some View {
            ZStack {
                Circle()
                    .fill(AppColors.statusColor(for: status))
                    .frame(width: 18, height: 18)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                
                Image(systemName: getStatusIcon(for: status))
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
            }
            .offset(x: size / 2 - 5, y: -size / 2 + 5)
        }
    }
    
    // 产品节点图像视图
    private struct NodeImageView: View {
        let product: Product
        let nodeSize: CGFloat
        let isCenter: Bool
        let themeColor: Color
        
        var body: some View {
            ZStack {
                if let imageData = product.images, let uiImage = UIImage(data: imageData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: nodeSize - 14, height: nodeSize - 14)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                } else {
                    // 产品图标背景光晕
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    isCenter ? .white.opacity(0.3) : themeColor.opacity(0.1),
                                    .clear
                                ]),
                                center: .center,
                                startRadius: 0,
                                endRadius: nodeSize / 2
                            )
                        )
                        .frame(width: nodeSize - 25, height: nodeSize - 25)
                    
                    // 产品图标
                    Image(systemName: product.status == .core ? "star.square" : "cube.box.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: nodeSize / 2.5, height: nodeSize / 2.5)
                        .foregroundColor(isCenter ? .white : themeColor)
                        .shadow(color: isCenter ? .white.opacity(0.5) : themeColor.opacity(0.5), radius: 2, x: 0, y: 0)
                }
            }
        }
    }
    
    // 产品节点主视图
    private struct ProductNodeView: View {
        let product: Product
        let isCenter: Bool
        let nodeSize: CGFloat
        let isAnimating: Bool
        let themeManager: ThemeManager
        @Binding var hoveredProduct: Product?
        
        var body: some View {
            VStack(spacing: 10) {
                // 产品图片或图标容器
                ZStack {
                    // 发光效果
                    if isCenter {
                        Circle()
                            .fill(themeManager.currentTheme.primaryColor)
                            .frame(width: nodeSize + 8, height: nodeSize + 8)
                            .blur(radius: 15)
                            .opacity(0.3)
                    }
                    
                    // 背景形状
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    isCenter ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground),
                                    isCenter ? themeManager.currentTheme.primaryColor.opacity(0.8) : Color(UIColor.tertiarySystemBackground).opacity(0.9)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: nodeSize, height: nodeSize)
                        .shadow(color: Color.black.opacity(isCenter ? 0.25 : 0.1), radius: isCenter ? 10 : 6, x: 0, y: 4)
                    
                    // 阴影内环
                    if isCenter {
                        Circle()
                            .stroke(themeManager.currentTheme.primaryColor.opacity(0.5), lineWidth: 3)
                            .blur(radius: 3)
                            .frame(width: nodeSize - 2, height: nodeSize - 2)
                    }
                    
                    // 产品图像
                    NodeImageView(
                        product: product,
                        nodeSize: nodeSize,
                        isCenter: isCenter,
                        themeColor: themeManager.currentTheme.primaryColor
                    )
                    
                    // 状态指示器
                    if !isCenter {
                        NodeStatusIndicator(status: product.status, size: nodeSize)
                    }
                }
                .overlay(
                    Circle()
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    isCenter ? themeManager.currentTheme.primaryColor.opacity(0.9) : Color.gray.opacity(0.25),
                                    isCenter ? themeManager.currentTheme.primaryColor : Color.gray.opacity(0.4)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: isCenter ? 3 : 1.5
                        )
                )
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(isCenter ? 0.6 : 0.4), lineWidth: isCenter ? 1 : 0.5)
                        .padding(1.5)
                        .blur(radius: 0.5)
                )
                
                // 产品名称
                Text(product.name ?? "未命名产品")
                    .font(isCenter ? .subheadline.bold() : .caption)
                    .foregroundColor(isCenter ? .primary : .secondary)
                    .lineLimit(1)
                    .truncationMode(.tail)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color(UIColor.systemBackground).opacity(0.92))
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
                            .overlay(
                                Capsule()
                                    .stroke(
                                        isCenter ? 
                                            themeManager.currentTheme.primaryColor.opacity(0.3) : 
                                            Color.gray.opacity(0.2),
                                        lineWidth: 0.5
                                    )
                            )
                    )
                    .fixedSize()
                
                // 品牌信息
                if !isCenter, let brand = product.brand, !brand.isEmpty {
                    Text(brand)
                        .font(.caption2)
                        .foregroundColor(.secondary.opacity(0.8))
                        .lineLimit(1)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color(UIColor.systemBackground).opacity(0.7))
                        )
                        .fixedSize()
                }
            }
        }
    }
    
    // 产品节点视图包装函数
    private func productNode(_ product: Product, position: CGPoint, isCenter: Bool, index: Int) -> some View {
        let nodeSize: CGFloat = isCenter ? 100 : 76
        let delay = isCenter ? 0.0 : 0.2 + 0.1 * Double(index)
        
        return ProductNodeView(
            product: product,
            isCenter: isCenter,
            nodeSize: nodeSize,
            isAnimating: isAnimating,
            themeManager: themeManager,
            hoveredProduct: $hoveredProduct
        )
        .position(position)
        .opacity(isAnimating ? 1 : 0)
        .scaleEffect(isAnimating ? 1 : 0.3)
        .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(delay), value: isAnimating)
        .rotation3DEffect(
            .degrees(isCenter && isAnimating ? 360 : 0),
            axis: (x: 0, y: 1, z: 0),
            anchor: .center,
            anchorZ: 0,
            perspective: 1
        )
        .animation(.easeInOut(duration: isCenter ? 1.2 : 0).delay(isCenter ? 0.2 : 0), value: isAnimating)
        .onTapGesture {
            hapticFeedback(style: .medium)
            selectedProduct = product
            showingNodeDetail = true
        }
        .onLongPressGesture(minimumDuration: 0.5) {
            hapticFeedback(style: .rigid)
            // 长按显示快捷菜单逻辑可以在这里添加
        }
        .onHover { isHovered in
            withAnimation(.spring(response: 0.3)) {
                if isHovered {
                    hoveredProduct = product
                } else if hoveredProduct?.id == product.id {
                    hoveredProduct = nil
                }
            }
        }
        .blur(radius: hoveredProduct != nil && hoveredProduct?.id != product.id && !isCenter ? 1 : 0)
    }
    
    // 连接线图标视图
    private struct ConnectionIconView: View {
        let iconName: String
        let color: Color
        let action: () -> Void
        
        var body: some View {
            ZStack {
                // 图标背景
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                color.opacity(0.9),
                                color.opacity(0.7)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 42, height: 42)
                    .shadow(color: color.opacity(0.5), radius: 5, x: 0, y: 3)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                
                // 发光效果
                Circle()
                    .fill(color)
                    .frame(width: 28, height: 28)
                    .blur(radius: 8)
                    .opacity(0.3)
                
                // 图标
                Image(systemName: iconName)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
            }
            .onTapGesture(perform: action)
        }
    }
    
    // 使用频率指示器视图
    private struct UsageCountView: View {
        let count: Int32
        let color: Color
        let offsetX: CGFloat
        let offsetY: CGFloat
        
        var body: some View {
            Text("\(count)")
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(.white)
                .padding(5)
                .background(
                    Circle()
                        .fill(color.opacity(0.8))
                        .shadow(color: color.opacity(0.3), radius: 2, x: 0, y: 1)
                )
                .offset(x: offsetX * 0.7, y: offsetY * 0.7)
        }
    }
    
    // 连接线形状视图
    private struct ConnectionLineShape: View {
        let from: CGPoint
        let to: CGPoint
        let control: CGPoint
        let color: Color
        let strength: Int16
        let isAnimating: Bool
        let delay: Double
        
        var body: some View {
            Path { path in
                path.move(to: from)
                path.addQuadCurve(to: to, control: control)
            }
            .stroke(
                LinearGradient(
                    gradient: Gradient(colors: [
                        color.opacity(0.7),
                        color.opacity(0.9)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                style: StrokeStyle(
                    lineWidth: CGFloat(strength) * 1.5,
                    lineCap: .round,
                    lineJoin: .round
                )
            )
            .shadow(color: color.opacity(0.3), radius: 3, x: 0, y: 0)
            .opacity(isAnimating ? 1 : 0)
            .animation(.easeInOut(duration: 0.8).delay(delay), value: isAnimating)
        }
    }
    
    // 连接线
    private func connectionLine(for link: ProductLink, center: CGPoint, index: Int) -> some View {
        guard let sourceProduct = link.sourceProduct,
              let targetProduct = link.targetProduct else {
            return AnyView(EmptyView())
        }
        
        let isOutgoing = sourceProduct.id == product.id
        let relatedProduct = isOutgoing ? targetProduct : sourceProduct
        let angle = angleForProduct(relatedProduct)
        let endPosition = positionForAngle(angle, center: center)
        let lineColor = link.relationshipTypeEnum.color
        let delay = 0.3 + 0.05 * Double(index)
        
        // 计算曲线控制点
        let isSelected = selectedLink?.id == link.id
        let controlPointDistance: CGFloat = 30.0
        let midPoint = CGPoint(
            x: (center.x + endPosition.x) / 2,
            y: (center.y + endPosition.y) / 2
        )
        
        // 垂直于线的方向
        let dx = endPosition.x - center.x
        let dy = endPosition.y - center.y
        let distance = sqrt(dx * dx + dy * dy)
        
        // 根据使用频率调整曲线的弯曲程度
        let usageInfluence = min(CGFloat(Int(link.usageCount)) * 1.5, 15.0)
        let curveAmount = controlPointDistance + usageInfluence
        
        // 计算正交向量
        let orthogonalX = -dy / distance * curveAmount
        let orthogonalY = dx / distance * curveAmount
        
        // 曲线控制点
        let controlPoint = CGPoint(
            x: midPoint.x + orthogonalX,
            y: midPoint.y + orthogonalY
        )
        
        return AnyView(
            ZStack {
                // 优雅的曲线连接线
                ConnectionLineShape(
                    from: center,
                    to: endPosition,
                    control: controlPoint,
                    color: lineColor,
                    strength: link.strength,
                    isAnimating: isAnimating,
                    delay: delay
                )
                
                // 关系类型图标
                ConnectionIconView(
                    iconName: link.relationshipTypeEnum.icon,
                    color: lineColor,
                    action: {
                        hapticFeedback(style: .medium)
                        // 确保链接数据已完全加载
                        if link.sourceProduct != nil && link.targetProduct != nil {
                            selectedLink = link
                            showingLinkDetail = true
                        } else {
                            // 如果数据不完整，尝试重新加载
                            linkViewModel.loadLinks(for: product)
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                selectedLink = link
                                showingLinkDetail = true
                            }
                        }
                    }
                )
                .position(x: controlPoint.x, y: controlPoint.y)
                .opacity(isAnimating ? 1 : 0)
                .scaleEffect(isAnimating ? 1 : 0.3)
                .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(delay + 0.1), value: isAnimating)
                
                // 方向箭头
                if !link.isBidirectional {
                    directionArrow(from: center, to: endPosition, isOutgoing: isOutgoing, color: lineColor)
                        .opacity(isAnimating ? 1 : 0)
                        .animation(.easeInOut(duration: 0.8).delay(delay + 0.2), value: isAnimating)
                }
                
                // 使用频率指示器
                if link.usageCount > 2 {
                    UsageCountView(
                        count: link.usageCount,
                        color: lineColor,
                        offsetX: orthogonalX,
                        offsetY: orthogonalY
                    )
                    .position(
                        x: (controlPoint.x + endPosition.x) / 2,
                        y: (controlPoint.y + endPosition.y) / 2
                    )
                    .opacity(isAnimating ? 1 : 0)
                    .animation(.easeInOut(duration: 0.8).delay(delay + 0.3), value: isAnimating)
                }
            }
        )
    }
    
    // 方向箭头
    private func directionArrow(from start: CGPoint, to end: CGPoint, isOutgoing: Bool, color: Color) -> some View {
        let arrowLength: CGFloat = 14
        let arrowAngle: CGFloat = .pi / 6
        
        // 计算线段角度
        let angle = atan2(end.y - start.y, end.x - start.x)
        
        // 计算箭头点
        let pointPosition = isOutgoing ? 
            CGPoint(
                x: (start.x + end.x) / 2 + (end.x - start.x) / 4,
                y: (start.y + end.y) / 2 + (end.y - start.y) / 4
            ) : 
            CGPoint(
                x: (start.x + end.x) / 2 - (end.x - start.x) / 4,
                y: (start.y + end.y) / 2 - (end.y - start.y) / 4
            )
        
        // 箭头方向
        let arrowAngle1 = isOutgoing ? angle + .pi - arrowAngle : angle - arrowAngle
        let arrowAngle2 = isOutgoing ? angle + .pi + arrowAngle : angle + arrowAngle
        
        let arrowPoint1 = CGPoint(
            x: pointPosition.x + arrowLength * cos(arrowAngle1),
            y: pointPosition.y + arrowLength * sin(arrowAngle1)
        )
        
        let arrowPoint2 = CGPoint(
            x: pointPosition.x + arrowLength * cos(arrowAngle2),
            y: pointPosition.y + arrowLength * sin(arrowAngle2)
        )
        
        // 创建一个带填充的箭头
        return ZStack {
            // 箭头填充
            Path { path in
                path.move(to: pointPosition)
                path.addLine(to: arrowPoint1)
                path.addLine(to: arrowPoint2)
                path.closeSubpath()
            }
            .fill(color.opacity(0.9))
            .shadow(color: color.opacity(0.4), radius: 2, x: 0, y: 1)
            
            // 箭头边缘
            Path { path in
                path.move(to: pointPosition)
                path.addLine(to: arrowPoint1)
                path.addLine(to: arrowPoint2)
                path.closeSubpath()
            }
            .stroke(Color.white.opacity(0.5), lineWidth: 1)
        }
        .rotationEffect(.degrees(isOutgoing ? 0 : 180))
    }
    
    // 链接详情视图
    private func linkDetailView(link: ProductLink) -> some View {
        let relatedProduct = link.sourceProduct == product ? link.targetProduct : link.sourceProduct
        
        return NavigationView {
            Form {
                // 头部关系类型视觉展示
                HStack {
                    Spacer()
                    VStack(spacing: 8) {
                        ZStack {
                            // 背景光晕
                            Circle()
                                .fill(link.relationshipTypeEnum.color)
                                .frame(width: 70, height: 70)
                                .blur(radius: 20)
                                .opacity(0.3)
                            
                            // 图标背景
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            link.relationshipTypeEnum.color,
                                            link.relationshipTypeEnum.color.opacity(0.8)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 60, height: 60)
                                .shadow(color: link.relationshipTypeEnum.color.opacity(0.5), radius: 5, x: 0, y: 3)
                            
                            // 图标
                            Image(systemName: link.relationshipTypeEnum.icon)
                                .font(.system(size: 26, weight: .semibold))
                                .foregroundColor(.white)
                        }
                        .padding(.vertical, 10)
                        
                        Text(link.relationshipTypeEnum.rawValue)
                            .font(.headline)
                            .foregroundColor(link.relationshipTypeEnum.color)
                    }
                    Spacer()
                }
                .listRowBackground(Color.clear)
                .listRowInsets(EdgeInsets())
                .padding(.bottom, 10)
                // 关联产品信息
                Section(header: Text("关联产品信息")) {
                    if let rProduct = relatedProduct {
                        NavigationLink(destination: ProductDetailView(product: rProduct)) {
                            HStack {
                                // 产品图标
                                if let imageData = rProduct.images, let uiImage = UIImage(data: imageData) {
                                    Image(uiImage: uiImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: 40, height: 40)
                                        .clipShape(Circle())
                                } else {
                                    Circle()
                                        .fill(themeManager.currentTheme.primaryColor.opacity(0.2))
                                        .frame(width: 40, height: 40)
                                        .overlay(
                                            Image(systemName: "cube.box")
                                                .foregroundColor(themeManager.currentTheme.primaryColor)
                                        )
                                }
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(rProduct.name ?? "未命名产品")
                                        .font(.headline)
                                    
                                    if let brand = rProduct.brand, !brand.isEmpty {
                                        Text(brand + (rProduct.model != nil ? " · \(rProduct.model!)" : ""))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                        
                                    // 添加使用记录按钮
                                    Button(action: {
                                        hapticFeedback(style: .medium)
                                        isShowingJointUsage = true
                                    }) {
                                        HStack {
                                            ZStack {
                                                Circle()
                                                    .fill(themeManager.currentTheme.primaryColor.opacity(0.2))
                                                    .frame(width: 36, height: 36)
                                    
                                                Image(systemName: "plus.circle")
                                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                                            }
                                
                                            Text("记录共同使用")
                                                .font(.subheadline)
                                                .foregroundColor(themeManager.currentTheme.primaryColor)
                                
                                            Spacer()
                                
                                            Image(systemName: "chevron.right")
                                                .foregroundColor(.secondary)
                                                .font(.caption)
                                        }
                                        .padding(.vertical, 6)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    .padding(.top, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(themeManager.currentTheme.primaryColor.opacity(0.2), lineWidth: 1)
                                            .padding(-5)
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 关系类型
                Section(header: Text("关系类型说明")) {
                    if link.isBidirectional {
                        HStack {
                            Image(systemName: "arrow.right.arrow.left")
                                .foregroundColor(.blue)
                                .font(.system(size: 16, weight: .bold))
                                .padding(6)
                                .background(
                                    Circle()
                                        .fill(Color.blue.opacity(0.15))
                                )
                            
                            Text("双向关系")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text("两种产品互相关联")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 6)
                    }
                    
                    VStack(alignment: .leading, spacing: 10) {
                        Text(link.relationshipTypeEnum.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(12)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(UIColor.tertiarySystemBackground))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(link.relationshipTypeEnum.color.opacity(0.2), lineWidth: 1)
                            )
                    }
                }
                
                // 关系强度
                Section(header: Text("关系强度")) {
                    VStack(alignment: .leading, spacing: 12) {
                        // 可视化强度指示器
                        ZStack(alignment: .leading) {
                            // 背景轨道
                            RoundedRectangle(cornerRadius: 5)
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 10)
                            
                            // 强度填充
                            RoundedRectangle(cornerRadius: 5)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            link.relationshipTypeEnum.color.opacity(0.7),
                                            link.relationshipTypeEnum.color
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: CGFloat(link.strength) / 5.0 * UIScreen.main.bounds.width * 0.7, height: 10)
                                .shadow(color: link.relationshipTypeEnum.color.opacity(0.3), radius: 2, x: 0, y: 1)
                        }
                        .padding(.vertical, 8)
                        
                        // 星级
                        HStack {
                            ForEach(1...5, id: \.self) { index in
                                Image(systemName: index <= link.strength ? "star.fill" : "star")
                                    .foregroundColor(index <= link.strength ? .yellow : .gray)
                                    .font(.system(size: 20))
                            }
                            
                            Spacer()
                            
                            // 强度文字说明
                            Text(getStrengthDescription(for: link.strength))
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(link.relationshipTypeEnum.color)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    Capsule()
                                        .fill(link.relationshipTypeEnum.color.opacity(0.1))
                                )
                        }
                    }
                }
                
                // 使用统计
                Section(header: Text("使用统计")) {
                    VStack(spacing: 16) {
                        HStack {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color.green.opacity(0.7),
                                                Color.green.opacity(0.5)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 42, height: 42)
                                    .shadow(color: Color.green.opacity(0.2), radius: 3, x: 0, y: 2)
                                
                                Image(systemName: "chart.bar.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 18, weight: .semibold))
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("共同使用次数")
                                    .font(.subheadline)
                                
                                // 使用频率图示
                                if link.usageCount > 0 {
                                    HStack(spacing: 2) {
                                        ForEach(0..<min(5, Int(link.usageCount)), id: \.self) { _ in
                                            RoundedRectangle(cornerRadius: 2)
                                                .fill(Color.green)
                                                .frame(width: 8, height: 12)
                                        }
                                        
                                        if link.usageCount > 5 {
                                            Text("+\(Int(link.usageCount) - 5)")
                                                .font(.caption2)
                                                .foregroundColor(.green)
                                        }
                                    }
                                }
                            }
                            
                            Spacer()
                            
                            Text("\(link.usageCount)")
                                .font(.title2.bold())
                                .foregroundColor(.green)
                        }
                        
                        if let lastUsed = link.lastUsedTogether {
                            HStack {
                                ZStack {
                                    Circle()
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color.blue.opacity(0.7),
                                                    Color.blue.opacity(0.5)
                                                ]),
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .frame(width: 42, height: 42)
                                        .shadow(color: Color.blue.opacity(0.2), radius: 3, x: 0, y: 2)
                                    
                                    Image(systemName: "clock.fill")
                                        .foregroundColor(.white)
                                        .font(.system(size: 18, weight: .semibold))
                                }
                                
                                Text("最后一次共同使用")
                                Spacer()
                                Text(lastUsed, style: .relative)
                                    .font(.headline)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    
                    Button(action: {
                        hapticFeedback(style: .medium)
                        // 添加动画效果
                        withAnimation(.easeInOut(duration: 0.4)) {
                            isShowingJointUsage = true
                        }
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.white)
                            Text("记录共同使用")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.primaryColor.opacity(0.8)]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                    .padding(.top, 8)
                }
                
                // 备注
                if let notes = link.notes, !notes.isEmpty {
                    Section(header: Text("备注")) {
                        Text(notes)
                            .padding(.vertical, 8)
                    }
                }
            }
            .navigationTitle("关联关系详情")
            .navigationBarItems(trailing: Button("完成") {
                showingLinkDetail = false
            })
        }
    }
    
    // MARK: - 辅助方法
    
    // 获取相关产品列表
    private var relatedProducts: [Product] {
        let products = linkViewModel.links.compactMap { link -> Product? in
            if link.sourceProduct == product {
                return link.targetProduct
            } else if link.targetProduct == product {
                return link.sourceProduct
            }
            return nil
        }
        
        return Array(Set(products)) // 去重
    }
    
    // 计算产品的角度位置
    private func angleForProduct(_ product: Product) -> CGFloat {
        guard let index = relatedProducts.firstIndex(where: { $0.id == product.id }) else {
            return 0
        }
        
        let count = relatedProducts.count
        let angle = (2 * .pi / CGFloat(count)) * CGFloat(index)
        return angle
    }
    
    // 根据角度计算位置
    private func positionForAngle(_ angle: CGFloat, center: CGPoint) -> CGPoint {
        return CGPoint(
            x: center.x + nodeRadius * cos(angle),
            y: center.y + nodeRadius * sin(angle)
        )
    }
    
    // 获取强度描述
    private func getStrengthDescription(for strength: Int16) -> String {
        switch strength {
        case 1: return "微弱关联 - 产品之间偶尔有交集"
        case 2: return "轻度关联 - 产品之间有时互相搭配使用"
        case 3: return "中度关联 - 产品经常一起使用"
        case 4: return "紧密关联 - 产品之间形成了重要互补"
        case 5: return "密不可分 - 两产品通常总是配合使用"
        default: return "未知关联强度"
        }
    }
    
    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

// 网格图案
struct GridPattern: Shape {
    let spacing: CGFloat
    let lineWidth: CGFloat
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 绘制垂直线
        stride(from: 0, to: rect.width, by: spacing).forEach { x in
            path.move(to: CGPoint(x: x, y: 0))
            path.addLine(to: CGPoint(x: x, y: rect.height))
        }
        
        // 绘制水平线
        stride(from: 0, to: rect.height, by: spacing).forEach { y in
            path.move(to: CGPoint(x: 0, y: y))
            path.addLine(to: CGPoint(x: rect.width, y: y))
        }
        
        return path
    }
}

// 渐变线条
struct GradientLine: View {
    let from: CGPoint
    let to: CGPoint
    let lineWidth: CGFloat
    let startColor: Color
    let endColor: Color
    
    var body: some View {
        GeometryReader { geometry in
            let path = Path { path in
                path.move(to: from)
                path.addLine(to: to)
            }
            
            path.stroke(
                LinearGradient(
                    gradient: Gradient(colors: [startColor, endColor]),
                    startPoint: UnitPoint(x: from.x / geometry.size.width, y: from.y / geometry.size.height),
                    endPoint: UnitPoint(x: to.x / geometry.size.width, y: to.y / geometry.size.height)
                ),
                lineWidth: lineWidth
            )
        }
    }
}

// 简单线条
struct Line: Shape {
    let from: CGPoint
    let to: CGPoint
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: from)
        path.addLine(to: to)
        return path
    }
}

// MARK: - Preview

struct ProductRelationshipGraphView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.id = UUID()
        product.name = "MacBook Pro"
        product.brand = "Apple"
        product.price = 9999
        product.purchaseDate = Date()
        
        return NavigationView {
            ProductRelationshipGraphView(product: product, context: context)
                .environmentObject(ThemeManager())
        }
    }
}