import SwiftUI
import CoreData

struct ExpenseTypeSelectorView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var themeManager: ThemeManager

    @Binding var selectedExpenseType: ExpenseType?

    @State private var expenseTypes: [ExpenseType] = []
    @State private var searchText = ""
    @State private var showingAddExpenseTypeSheet = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar

                // 费用类型列表
                List {
                    ForEach(filteredExpenseTypes) { expenseType in
                        expenseTypeRow(expenseType)
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("选择费用类型")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: But<PERSON>(action: {
                    showingAddExpenseTypeSheet = true
                }) {
                    Image(systemName: "plus")
                }
            )
            .sheet(isPresented: $showingAddExpenseTypeSheet) {
                AddExpenseTypeView(onAdd: addExpenseType)
                    .environmentObject(themeManager)
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                loadExpenseTypes()
            }
        }
    }

    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索费用类型", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }

    // 费用类型行
    private func expenseTypeRow(_ expenseType: ExpenseType) -> some View {
        HStack {
            // 图标
            if let iconName = expenseType.icon {
                Image(systemName: iconName)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 30)
            } else {
                Image(systemName: "creditcard")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 30)
            }

            // 名称
            Text(expenseType.name ?? "未命名类型")
                .font(.body)

            Spacer()

            // 选中状态
            if selectedExpenseType?.id == expenseType.id {
                Image(systemName: "checkmark")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            selectedExpenseType = expenseType
            presentationMode.wrappedValue.dismiss()
        }
    }

    // 加载费用类型
    private func loadExpenseTypes() {
        let repository = ExpenseTypeRepository(context: viewContext)
        expenseTypes = repository.fetchAll(sortDescriptors: [NSSortDescriptor(key: "name", ascending: true)])

        if expenseTypes.isEmpty {
            // 如果没有费用类型，创建一些默认类型
            createDefaultExpenseTypes()
        }
    }

    // 创建默认费用类型
    private func createDefaultExpenseTypes() {
        let repository = ExpenseTypeRepository(context: viewContext)

        let defaultExpenseTypes = [
            ("维修", "wrench.and.screwdriver"),
            ("保养", "sparkles"),
            ("配件", "puzzlepiece"),
            ("耗材", "cart"),
            ("电池更换", "battery.100"),
            ("软件订阅", "app.badge"),
            ("干洗", "bubbles.and.sparkles"),
            ("其他", "ellipsis.circle")
        ]

        for (name, icon) in defaultExpenseTypes {
            let expenseType = ExpenseType(context: viewContext)
            expenseType.id = UUID()
            expenseType.name = name
            expenseType.icon = icon
            expenseTypes.append(expenseType)
        }

        do {
            try viewContext.save()
        } catch {
            print("创建默认费用类型失败: \(error)")
        }
    }

    // 添加费用类型
    private func addExpenseType(name: String, icon: String) {
        let repository = ExpenseTypeRepository(context: viewContext)

        // 检查是否已存在同名费用类型
        let existingType = expenseTypes.first { $0.name == name }
        if existingType != nil {
            alertMessage = "已存在同名费用类型"
            showingAlert = true
            return
        }

        // 创建费用类型
        var newType: ExpenseType?
        let success = repository.save { context in
            let expenseType = ExpenseType(context: context)
            expenseType.id = UUID()
            expenseType.name = name
            expenseType.icon = icon
            newType = expenseType
            expenseTypes.append(expenseType)
        }

        if success {
            // 自动选择新创建的费用类型
            selectedExpenseType = newType
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "添加费用类型失败"
            showingAlert = true
        }
    }

    // 筛选后的费用类型
    private var filteredExpenseTypes: [ExpenseType] {
        if searchText.isEmpty {
            return expenseTypes
        } else {
            return expenseTypes.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
        }
    }
}

// 添加费用类型视图
struct AddExpenseTypeView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager

    var onAdd: ((String, String) -> Void)?

    @State private var name: String = ""
    @State private var selectedIcon: String = "creditcard"
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 图标选项
    private let icons = [
        "creditcard", "cart", "bag", "dollarsign.circle", "wrench.and.screwdriver",
        "sparkles", "puzzlepiece", "battery.100", "app.badge", "bubbles.and.sparkles",
        "hammer", "screwdriver", "scissors", "paintbrush", "printer",
        "car", "bus", "airplane", "bicycle", "fuelpump",
        "fork.knife", "cup.and.saucer", "wineglass", "takeoutbag.and.cup.and.straw", "birthday.cake",
        "pills", "cross", "bandage", "facemask", "stethoscope",
        "book", "newspaper", "magazine", "books.vertical", "graduationcap",
        "tv", "gamecontroller", "headphones", "ticket", "popcorn",
        "phone", "wifi", "network", "antenna.radiowaves.left.and.right", "envelope",
        "house", "bed.double", "sofa", "lamp", "shower",
        "ellipsis.circle"
    ]

    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section(header: Text("基本信息")) {
                    TextField("费用类型名称 *", text: $name)
                }

                // 图标选择
                Section(header: Text("图标")) {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 50))], spacing: 10) {
                        ForEach(icons, id: \.self) { icon in
                            iconButton(icon)
                        }
                    }
                }

                // 预览
                Section(header: Text("预览")) {
                    HStack {
                        Image(systemName: selectedIcon)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 30)

                        Text(name.isEmpty ? "费用类型名称" : name)
                            .font(.body)

                        Spacer()
                    }
                }

                // 保存按钮
                Section {
                    Button(action: saveExpenseType) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.primaryColor)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("添加费用类型")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
        }
    }

    // 图标按钮
    private func iconButton(_ icon: String) -> some View {
        VStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(selectedIcon == icon ? themeManager.currentTheme.primaryColor : .primary)
                .frame(width: 40, height: 40)
                .background(selectedIcon == icon ? themeManager.currentTheme.primaryColor.opacity(0.2) : Color.clear)
                .cornerRadius(8)
                .onTapGesture {
                    selectedIcon = icon
                }
        }
    }

    // 保存费用类型
    private func saveExpenseType() {
        // 验证输入
        if name.isEmpty {
            alertMessage = "请输入费用类型名称"
            showingAlert = true
            return
        }

        // 调用回调函数
        onAdd?(name, selectedIcon)
        presentationMode.wrappedValue.dismiss()
    }
}

struct ExpenseTypeSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        ExpenseTypeSelectorView(selectedExpenseType: .constant(nil))
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
            .environmentObject(ThemeManager())
    }
}
