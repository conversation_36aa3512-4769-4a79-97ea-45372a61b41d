import SwiftUI

struct AddTransferRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    let product: Product
    
    @State private var date = Date()
    @State private var transferType: UsageRecord.TransferType = .sold
    @State private var recipient: String = ""
    @State private var price: Double = 0
    @State private var notes: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    DatePicker("转让日期", selection: $date, displayedComponents: .date)
                    
                    CustomSegmentedControl(selection: $transferType) { type in
                        HStack {
                            Image(systemName: type.icon)
                                .foregroundColor(transferType == type ? .white : type.color)
                            Text(type.rawValue)
                        }
                    }
                    .accentColor(.purple)
                }
                
                // 接收方信息
                Section(header: Text("接收方信息")) {
                    TextField("接收方姓名", text: $recipient)
                        .keyboardType(.default)
                }
                
                // 价格
                Section(header: Text("价格信息")) {
                    HStack {
                        Text("¥")
                        TextField("转让价格", value: $price, format: .number)
                            .keyboardType(.decimalPad)
                    }
                    
                    if transferType == .sold {
                        Text("填写0表示赠送")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 备注
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(height: 100)
                }
                
                // 提交按钮
                Section {
                    Button(action: saveTransferRecord) {
                        Text("保存转让记录")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                    }
                    .listRowBackground(themeManager.currentTheme.primaryColor)
                }
            }
            .navigationTitle("添加转让记录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("错误"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }
    
    private func saveTransferRecord() {
        let transferTypeString = transferType == .sold ? "sold" : "gifted"
        let success = usageViewModel.addTransferRecord(
            date: date,
            transferType: transferTypeString,
            recipient: recipient.isEmpty ? nil : recipient,
            price: price,
            notes: notes.isEmpty ? nil : notes
        )
        
        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = usageViewModel.errorMessage ?? "添加转让记录失败"
            showingAlert = true
        }
    }
}

struct AddTransferRecordView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = try! context.fetch(Product.fetchRequest()).first!
        
        return AddTransferRecordView(product: product)
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: context),
                expenseRepository: RelatedExpenseRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
} 