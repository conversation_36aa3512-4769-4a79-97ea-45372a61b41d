import SwiftUI
import CoreData

struct CategorySelectorView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var themeManager: ThemeManager
    
    @Binding var selectedCategory: Category?
    
    @State private var searchText = ""
    @State private var showingAddCategorySheet = false
    @State private var categories: [Category] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 类别列表 - 简单列表避免重复
                List {
                    ForEach(allCategoriesFlattened, id: \.id) { category in
                        categoryRow(category)
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("选择类别")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button(action: {
                    showingAddCategorySheet = true
                }) {
                    Image(systemName: "plus")
                }
            )
            .sheet(isPresented: $showingAddCategorySheet) {
                AddCategoryView(onAdd: addCategory)
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                loadCategories()
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索类别", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 类别行
    private func categoryRow(_ category: Category) -> some View {
        HStack {
            // 类别图标
            if let iconName = category.icon {
                Image(systemName: iconName)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 30)
            } else {
                Image(systemName: "folder")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 30)
            }
            
            // 类别名称
            Text(category.name ?? "未命名类别")
                .font(.body)
            
            Spacer()
            
            // 选中状态
            if selectedCategory?.id == category.id {
                Image(systemName: "checkmark")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            selectedCategory = category
            presentationMode.wrappedValue.dismiss()
        }
    }
    
    // 加载类别
    private func loadCategories() {
        let repository = CategoryRepository(context: viewContext)
        categories = repository.fetchAll(sortDescriptors: [NSSortDescriptor(key: "name", ascending: true)])
        
        if categories.isEmpty {
            // 如果没有类别，创建一些默认类别
            createDefaultCategories()
        }
    }
    

    
    // 创建默认类别
    private func createDefaultCategories() {
        let defaultCategories = [
            ("电子产品", "laptopcomputer"),
            ("服装", "tshirt"),
            ("家居", "house"),
            ("厨房", "fork.knife"),
            ("美妆", "paintpalette"),
            ("健康", "heart"),
            ("户外", "leaf"),
            ("书籍", "book"),
            ("办公", "briefcase"),
            ("其他", "ellipsis.circle")
        ]
        
        for (name, icon) in defaultCategories {
            let category = Category(context: viewContext)
            category.id = UUID()
            category.name = name
            category.icon = icon
            categories.append(category)
        }
        
        do {
            try viewContext.save()
        } catch {
            print("创建默认类别失败: \(error)")
        }
    }
    
    // 添加类别
    private func addCategory(name: String, icon: String, parentCategory: Category?) {
        let repository = CategoryRepository(context: viewContext)
        
        // 检查是否已存在同名类别
        let existingCategory = categories.first { $0.name == name }
        if existingCategory != nil {
            alertMessage = "已存在同名类别"
            showingAlert = true
            return
        }
        
        // 创建类别
        let success = repository.save { context in
            let newCategory = Category(context: context)
            newCategory.id = UUID()
            newCategory.name = name
            newCategory.icon = icon
            newCategory.parentCategory = parentCategory
            categories.append(newCategory)
        }
        
        if !success {
            alertMessage = "添加类别失败"
            showingAlert = true
        }
    }
    
    // 根类别（没有父类别的类别）
    private var rootCategories: [Category] {
        let filtered = categories.filter { $0.parentCategory == nil }
        
        if searchText.isEmpty {
            return filtered
        } else {
            return filtered.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
        }
    }
    
    // 只有根类别（没有父类别且没有子类别的独立类别）
    private var rootOnlyCategories: [Category] {
        let rootCats = rootCategories
        let parentCats = parentCategories
        
        // 从根类别中排除那些是父类别的
        return rootCats.filter { rootCategory in
            !parentCats.contains { $0.id == rootCategory.id }
        }
    }
    
    // 父类别（有子类别的类别）
    private var parentCategories: [Category] {
        let parents = categories.filter { category in
            categories.contains { $0.parentCategory?.id == category.id }
        }
        
        if searchText.isEmpty {
            return parents
        } else {
            return parents.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
        }
    }
    
    // 按父类别分组的子类别
    private var childrenByParent: [Category: [Category]] {
        var result = [Category: [Category]]()
        
        for parent in parentCategories {
            let children = categories.filter { $0.parentCategory?.id == parent.id }
            
            if searchText.isEmpty {
                result[parent] = children
            } else {
                let filteredChildren = children.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
                if !filteredChildren.isEmpty {
                    result[parent] = filteredChildren
                }
            }
        }
        
        return result
    }
    
    // 去重的类别列表 - 按名称去重
    private var allCategoriesFlattened: [Category] {
        var uniqueCategories: [Category] = []
        var seenNames = Set<String>()
        
        let categoriesToProcess = searchText.isEmpty ? categories : categories.filter { $0.name?.localizedCaseInsensitiveContains(searchText) ?? false }
        
        for category in categoriesToProcess {
            if let name = category.name, !seenNames.contains(name) {
                seenNames.insert(name)
                uniqueCategories.append(category)
            }
        }
        
        return uniqueCategories
    }
    

}

struct CategorySelectorView_Previews: PreviewProvider {
    static var previews: some View {
        CategorySelectorView(selectedCategory: .constant(nil))
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
            .environmentObject(ThemeManager())
    }
}
