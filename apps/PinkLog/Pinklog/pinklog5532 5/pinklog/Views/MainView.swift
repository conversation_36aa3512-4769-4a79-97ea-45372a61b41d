import SwiftUI
import CoreData

struct MainView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var analyticsAssistant: AnalyticsAssistant

    @EnvironmentObject var conversationHistoryViewModel: ConversationHistoryViewModel
    @EnvironmentObject var userProfileManager: UserProfileManager
    
    @StateObject private var consumableViewModel = ConsumableViewModel()
    @StateObject private var subscriptionService = SubscriptionService(
        context: PersistenceController.shared.container.viewContext,
        coreDataRepository: CoreDataRepository<Product>(context: PersistenceController.shared.container.viewContext)
    )

    @State private var selectedTab = 0

    var body: some View {
        ZStack {
            TabView(selection: $selectedTab.animation(.easeInOut(duration: 0.2))) {
                // 仪表盘
                NavigationStack {
                    DashboardView()
                        .environmentObject(productViewModel)
                        .environmentObject(themeManager)
                        .environmentObject(usageViewModel)
                }
                .opacity(selectedTab == 0 ? 1 : 0)
                .transition(.opacity)
                .tabItem {
                    Label("仪表盘", systemImage: "chart.bar.doc.horizontal")
                }
                .tag(0)

                // 产品
                NavigationStack {
                    ProductsView()
                        .environmentObject(productViewModel)
                        .environmentObject(tagViewModel)
                        .environmentObject(themeManager)
                        .environmentObject(usageViewModel)
                }
                .opacity(selectedTab == 1 ? 1 : 0)
                .transition(.opacity)
                .tabItem {
                    Label("产品", systemImage: "cube.box")
                }
                .tag(1)

                // 虚拟订阅
                NavigationStack {
                    SubscriptionListView(
                        context: viewContext,
                        coreDataRepository: CoreDataRepository<Product>(context: viewContext)
                    )
                    .environmentObject(themeManager)
                }
                .opacity(selectedTab == 2 ? 1 : 0)
                .transition(.opacity)
                .tabItem {
                    Label("订阅", systemImage: "app.fill")
                }
                .tag(2)

                // 我的
                NavigationStack {
                    ProfileView()
                        .environmentObject(themeManager)
                        .environmentObject(productViewModel)
                        .environmentObject(usageViewModel)
                        .environmentObject(analyticsAssistant)
    
                        .environmentObject(conversationHistoryViewModel)
                        .environmentObject(userProfileManager)
                }
                .opacity(selectedTab == 3 ? 1 : 0)
                .transition(.opacity)
                .tabItem {
                    Label("我的", systemImage: "person.circle")
                }
                .tag(3)
            }
            .accentColor(themeManager.currentTheme.primaryColor)
            .transition(.opacity)
            .animation(.easeInOut(duration: 0.3), value: selectedTab)

            // 全局悬浮AI助手按钮
            FloatingAIAssistantContainer()
                .environmentObject(themeManager)
                .environmentObject(productViewModel)
                .environmentObject(usageViewModel)
                .environmentObject(analyticsAssistant)
                
                .environmentObject(conversationHistoryViewModel)
        }
        .sheet(isPresented: $productViewModel.showPremiumSheet) {
            PremiumView()
                .environmentObject(themeManager)
        }
        .onAppear {
            // 设置全局Tab Bar外观
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()

            UITabBar.appearance().standardAppearance = appearance
            if #available(iOS 15.0, *) {
                UITabBar.appearance().scrollEdgeAppearance = appearance
            }

            // 确保加载所有标签
            tagViewModel.loadTags()

            // 移除重复的Premium状态同步 - 已在ProductViewModel初始化时处理
            // productViewModel.syncPremiumStatus() // 🚨 这是重复调用的根源！
        }
        .preferredColorScheme(themeManager.currentTheme.colorScheme)
    }
}

#Preview {
    MainView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(ProductViewModel(
            repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
            categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(TagViewModel(
            repository: TagRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(UsageViewModel(
            usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
            expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(ThemeManager())
        .environmentObject(AnalyticsAssistant())
        
        .environmentObject(ConversationHistoryViewModel(
            conversationRepository: ConversationRepository(context: PersistenceController.preview.container.viewContext),
            messageRepository: MessageRepository(context: PersistenceController.preview.container.viewContext)
        ))
}
