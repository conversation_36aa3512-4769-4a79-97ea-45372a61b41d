//
//  ConsumableLifecycleView.swift
//  pinklog
//
//  Created by AI Assistant
//

import SwiftUI

struct ConsumableLifecycleView: View {
    @StateObject private var viewModel = ConsumableViewModel()
    @State private var selectedStatus: Product.LifecycleStatus = .active
    @State private var showingStatusPicker = false
    @State private var showingProductDetail: Product? = nil
    @State private var showingActionSheet: Product? = nil
    @State private var showingAutoArchiveSettings = false
    @State private var showingArchiveSuggestions = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 状态选择器
                statusPickerSection
                
                // 产品列表
                productListSection
            }
            .navigationTitle("生命周期管理")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button("归档建议") {
                            showingArchiveSuggestions = true
                        }
                        
                        But<PERSON>("自动归档设置") {
                            showingAutoArchiveSettings = true
                        }
                        
                        Divider()
                        
                        But<PERSON>("批量自动归档") {
                            Task {
                                try? await viewModel.executeBatchAutoArchive()
                            }
                        }
                    } label: {
                        Image(systemName: "archivebox")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("刷新状态") {
                        Task {
                            await viewModel.performArchiveCheck()
                        }
                    }
                }
            }
        }
        .onAppear {
            viewModel.loadConsumableProducts()
        }
        .sheet(item: $showingProductDetail) { product in
            ProductDetailView(product: product)
        }
        .sheet(isPresented: $showingAutoArchiveSettings) {
            AutoArchiveSettingsView()
        }
        .sheet(isPresented: $showingArchiveSuggestions) {
            ArchiveSuggestionsView()
        }
        .actionSheet(item: $showingActionSheet) { product in
            productActionSheet(for: product)
        }
    }
    
    // MARK: - 状态选择器
    
    private var statusPickerSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(Product.LifecycleStatus.allCases, id: \.self) { status in
                    statusCard(for: status)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGroupedBackground))
    }
    
    private func statusCard(for status: Product.LifecycleStatus) -> some View {
        let count = viewModel.getProductCount(for: status)
        let isSelected = selectedStatus == status
        
        return Button {
            selectedStatus = status
        } label: {
            VStack(spacing: 4) {
                HStack(spacing: 4) {
                    Image(systemName: status.icon)
                        .font(.caption)
                    Text("\(count)")
                        .font(.caption.weight(.semibold))
                }
                .foregroundColor(isSelected ? .white : status.color)
                
                Text(status.description)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white : .secondary)
                    .lineLimit(1)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? status.color : Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 产品列表
    
    private var productListSection: some View {
        let products = viewModel.getProducts(for: selectedStatus)
        
        return Group {
            if products.isEmpty {
                emptyStateView
            } else {
                List {
                    ForEach(products, id: \.id) { product in
                        productRow(product)
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: selectedStatus.icon)
                .font(.system(size: 48))
                .foregroundColor(selectedStatus.color)
            
            Text("暂无\(selectedStatus.description)产品")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text(getEmptyStateMessage(for: selectedStatus))
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private func getEmptyStateMessage(for status: Product.LifecycleStatus) -> String {
        switch status {
        case .active:
            return "所有消耗品都处于非活跃状态"
        case .lowStock:
            return "没有库存不足的产品"
        case .critical:
            return "没有库存紧急的产品"
        case .depleted:
            return "没有已耗尽的产品"
        case .expired:
            return "没有已过期的产品"
        case .discontinued:
            return "没有已停用的产品"
        case .archived:
            return "没有已归档的产品"
        }
    }
    
    private func productRow(_ product: Product) -> some View {
        HStack(spacing: 12) {
            // 状态指示器
            Circle()
                .fill(product.lifecycleStatus.color)
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name ?? "未命名产品")
                    .font(.headline)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    if product.lifecycleStatus != .archived {
                        Text("库存: \(Int(product.actualCurrentQuantity))/\(Int(product.quantity))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if product.actualCurrentQuantity > 0 {
                            Text("\(Int(product.stockPercentage * 100))%")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(product.stockStatus.color.opacity(0.2))
                                .foregroundColor(product.stockStatus.color)
                                .cornerRadius(4)
                        }
                    }
                }
                
                // 状态建议
                if !viewModel.getLifecycleStatusSuggestions(for: product).isEmpty {
                    Text("有状态更新建议")
                        .font(.caption2)
                        .foregroundColor(.orange)
                }
            }
            
            Spacer()
            
            // 操作按钮
            Button {
                showingActionSheet = product
            } label: {
                Image(systemName: "ellipsis.circle")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            showingProductDetail = product
        }
    }
    
    // MARK: - 操作菜单
    
    private func productActionSheet(for product: Product) -> ActionSheet {
        var buttons: [ActionSheet.Button] = []
        
        // 查看详情
        buttons.append(.default(Text("查看详情")) {
            showingProductDetail = product
        })
        
        // 根据当前状态提供相应操作
        switch product.lifecycleStatus {
        case .active, .lowStock, .critical:
            buttons.append(.default(Text("停用")) {
                viewModel.discontinueProduct(product)
            })
            buttons.append(.default(Text("归档")) {
                viewModel.archiveProduct(product)
            })
            
        case .depleted:
            buttons.append(.default(Text("重新激活")) {
                viewModel.reactivateProduct(product)
            })
            buttons.append(.default(Text("归档")) {
                viewModel.archiveProduct(product)
            })
            
        case .expired:
            buttons.append(.default(Text("重新激活")) {
                viewModel.reactivateProduct(product)
            })
            buttons.append(.default(Text("归档")) {
                viewModel.archiveProduct(product)
            })
            
        case .discontinued:
            buttons.append(.default(Text("重新激活")) {
                viewModel.reactivateProduct(product)
            })
            buttons.append(.default(Text("归档")) {
                viewModel.archiveProduct(product)
            })
            
        case .archived:
            buttons.append(.default(Text("取消归档")) {
                viewModel.unarchiveProduct(product)
            })
        }
        
        buttons.append(.cancel(Text("取消")))
        
        return ActionSheet(
            title: Text(product.name ?? "未命名产品"),
            message: Text("选择操作"),
            buttons: buttons
        )
    }
}

// MARK: - 扩展

#Preview {
    ConsumableLifecycleView()
}