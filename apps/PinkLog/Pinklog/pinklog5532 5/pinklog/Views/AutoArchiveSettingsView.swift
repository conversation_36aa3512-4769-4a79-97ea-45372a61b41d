//
//  AutoArchiveSettingsView.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI

struct AutoArchiveSettingsView: View {
    @StateObject private var viewModel = ConsumableViewModel()
    @State private var configuration: AutoArchiveConfiguration
    @Environment(\.dismiss) private var dismiss
    
    init() {
        let vm = ConsumableViewModel()
        _configuration = State(initialValue: vm.getAutoArchiveConfiguration())
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("自动归档设置") {
                    Toggle("启用自动归档", isOn: $configuration.isEnabled)
                        .tint(.blue)
                    
                    if configuration.isEnabled {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("未使用天数阈值")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Stepper("\(configuration.unusedDaysThreshold) 天", 
                                   value: $configuration.unusedDaysThreshold, 
                                   in: 30...365, 
                                   step: 7)
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("耗尽后天数阈值")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Stepper("\(configuration.depletedDaysThreshold) 天", 
                                   value: $configuration.depletedDaysThreshold, 
                                   in: 7...180, 
                                   step: 7)
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("过期后天数阈值")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Stepper("\(configuration.expiredDaysThreshold) 天", 
                                   value: $configuration.expiredDaysThreshold, 
                                   in: 7...90, 
                                   step: 7)
                        }
                    }
                }
                
                Section("智能建议") {
                    Toggle("启用智能建议", isOn: $configuration.enableSmartSuggestions)
                        .tint(.blue)
                    
                    if configuration.enableSmartSuggestions {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("置信度阈值")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Slider(value: $configuration.confidenceThreshold, 
                                      in: 0.5...1.0, 
                                      step: 0.05) {
                                    Text("置信度")
                                } minimumValueLabel: {
                                    Text("50%")
                                        .font(.caption)
                                } maximumValueLabel: {
                                    Text("100%")
                                        .font(.caption)
                                }
                                
                                Text("\(Int(configuration.confidenceThreshold * 100))%")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(width: 40)
                            }
                        }
                    }
                }
                
                Section("检查频率") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("检查间隔")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Stepper("\(configuration.checkFrequencyDays) 天", 
                               value: $configuration.checkFrequencyDays, 
                               in: 1...30, 
                               step: 1)
                    }
                }
                
                Section {
                    Button("重置为默认设置") {
                        configuration = AutoArchiveConfiguration.default
                    }
                    .foregroundColor(.red)
                } footer: {
                    Text("自动归档功能会根据设置的条件自动识别可归档的消耗品，帮助您保持产品列表的整洁。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("自动归档设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        viewModel.updateAutoArchiveConfiguration(configuration)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

#Preview {
    AutoArchiveSettingsView()
}