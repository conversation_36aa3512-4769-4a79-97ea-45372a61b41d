import SwiftUI
import AVFoundation
import CoreData

struct DashboardView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel
    @Environment(\.managedObjectContext) private var viewContext

    @State private var showingAddProduct = false
    @State private var showingCamera = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var showingCameraToAdd = false
    @State private var showingSubscriptionList = false
    
    // 虚拟订阅服务
    @StateObject private var subscriptionService = SubscriptionService(
        context: PersistenceController.shared.container.viewContext,
        coreDataRepository: CoreDataRepository<Product>(context: PersistenceController.shared.container.viewContext)
    )

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 顶部统计卡片
                statsSection

                // 虚拟订阅概览
                subscriptionOverviewSection

                // 提醒区域
                remindersSection

                // 产品状态分布
                productStatusSection

                // 最近添加的产品
                recentProductsSection
            }
            .padding()
        }
        .navigationTitle("仪表盘")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    if productViewModel.checkLimitAndShowSheet() {
                        checkCameraPermissionForAdd()
                    }
                }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $showingAddProduct) {
            if let images = capturedImages {
                AddProductView(
                    presetImage: images.original,
                    presetLiftedImage: images.lifted,
                    presetStickerImage: images.sticker
                )
            } else {
                AddProductView()
            }
        }
        .fullScreenCover(isPresented: $showingCameraToAdd) {
            CameraToAddProductView()
        }
        .onAppear {
            if !productViewModel.isDataLoaded {
                productViewModel.loadProducts()
            }
        }
        .refreshable {
            productViewModel.loadProducts()
        }
        .onReceive(NotificationCenter.default.publisher(for: .dataRestoreCompleted)) { _ in
            // 数据恢复完成后刷新仪表盘数据
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                productViewModel.loadProducts()
                print("📊 仪表盘数据已刷新（备份恢复完成）")
            }
        }
    }

    // 统计卡片区域
    private var statsSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                // 总产品数
                StatCard(
                    title: "总产品数",
                    value: "\(productViewModel.getTotalProductsCount())",
                    icon: "cube.box.fill",
                    color: themeManager.currentTheme.primaryColor
                )

                // 总投资
                StatCard(
                    title: "总投资",
                    value: "¥\(Int(productViewModel.getTotalProductsValue()))",
                    icon: "yensign.circle.fill",
                    color: .green
                )
            }
            
            HStack(spacing: 16) {
                // 虚拟订阅数量
                StatCard(
                    title: "虚拟订阅",
                    value: "\(subscriptionService.subscriptions.count)",
                    icon: "app.fill",
                    color: .blue
                )

                // 月度订阅支出
                StatCard(
                    title: "月度支出",
                    value: "¥\(String(format: "%.0f", subscriptionService.calculateTotalMonthlySubscriptionCost()))",
                    icon: "dollarsign.circle.fill",
                    color: .orange
                )
            }
        }
    }

    // 虚拟订阅概览部分
    private var subscriptionOverviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("虚拟订阅概览")
                    .font(.headline)
                    .padding(.horizontal)
                
                Spacer()
                
                Button("查看全部") {
                    showingSubscriptionList = true
                }
                .font(.subheadline)
                .foregroundColor(themeManager.currentTheme.primaryColor)
                .padding(.horizontal)
            }
            
            if subscriptionService.subscriptions.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "app.dashed")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    
                    Text("暂无虚拟订阅")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("在添加产品时选择虚拟订阅类型来开始管理您的数字订阅")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(12)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        // 活跃订阅
                        SubscriptionStatCard(
                            title: "活跃订阅",
                            value: "\(subscriptionService.activeSubscriptions.count)",
                            icon: "checkmark.circle.fill",
                            color: .green
                        )
                        
                        // 即将续费
                        SubscriptionStatCard(
                            title: "即将续费",
                            value: "\(subscriptionService.renewalAlerts.count)",
                            icon: "clock.fill",
                            color: .red
                        )
                        
                        // 年度总支出
                        SubscriptionStatCard(
                            title: "年度支出",
                            value: "¥\(String(format: "%.0f", subscriptionService.calculateTotalAnnualSubscriptionCost()))",
                            icon: "calendar.circle.fill",
                            color: .purple
                        )
                        
                        // 平均价值指数
                        SubscriptionStatCard(
                            title: "平均价值",
                            value: String(format: "%.1f", subscriptionService.calculateAverageWorthItIndex()),
                            icon: "star.fill",
                            color: .yellow
                        )
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
            }
        }
        .sheet(isPresented: $showingSubscriptionList) {
            NavigationView {
                SubscriptionListView(
                    context: viewContext,
                    coreDataRepository: CoreDataRepository<Product>(context: viewContext)
                )
            }
        }
    }
    
    // 提醒区域已移除，因为依赖于已删除的分析功能
    @ViewBuilder
    private var remindersSection: some View {
        EmptyView()
    }

    // 产品状态分布
    private var productStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("产品状态分布")
                .font(.headline)
                .padding(.horizontal)

            let statusProducts = productViewModel.getProductsByStatus()

            HStack(spacing: 16) {
                ForEach(Product.ProductStatus.allCases) { status in
                    let count = statusProducts[status]?.count ?? 0

                    VStack(spacing: 8) {
                        Text("\(count)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(status.color)

                        Text(status.rawValue)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 最近添加的产品
    private var recentProductsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近添加")
                .font(.headline)
                .padding(.horizontal)

            ForEach(productViewModel.products.prefix(3)) { product in
                NavigationLink(destination: ProductDetailView(product: product)
                    .environmentObject(themeManager)
                    .environmentObject(usageViewModel)
                    .environmentObject(productViewModel)) {
                    ProductCard(product: product)
                }
            }

            if !productViewModel.products.isEmpty {
                HStack {
                    Button(action: {
                        // 查看全部产品
                    }) {
                        Text("查看全部\(productViewModel.products.count)个产品")
                            .font(.subheadline)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }

                    Spacer()

                    Button(action: {
                        if productViewModel.checkLimitAndShowSheet() {
                            capturedImages = nil
                            showingAddProduct = true
                        }
                    }) {
                        Text("手动添加")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // 提醒相关函数已移除，因为依赖于已删除的分析功能

    // MARK: - 相机权限检查
    private func checkCameraPermissionForAdd() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            showingCameraToAdd = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.showingCameraToAdd = true
                    }
                }
            }
        case .denied, .restricted:
            // 可以显示权限提示
            break
        @unknown default:
            break
        }
    }
}

#Preview {
    NavigationView {
        DashboardView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
                expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
            ))
    }
}
