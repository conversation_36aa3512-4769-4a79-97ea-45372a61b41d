//
//  SubscriptionReminderTestView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  订阅提醒功能测试界面
//

import SwiftUI
import CoreData
import UserNotifications

struct SubscriptionReminderTestView: View {
    @Environment(\.managedObjectContext) private var context
    @StateObject private var reminderService: SubscriptionReminderService
    @StateObject private var subscriptionService: SubscriptionService
    
    @State private var testResults: [TestResult] = []
    @State private var isRunningTests = false
    @State private var showingPermissionAlert = false
    
    init(context: NSManagedObjectContext) {
        let reminderService = SubscriptionReminderService(context: context)
        let subscriptionService = SubscriptionService(
            context: context,
            coreDataRepository: CoreDataRepository(context: context)
        )
        
        self._reminderService = StateObject(wrappedValue: reminderService)
        self._subscriptionService = StateObject(wrappedValue: subscriptionService)
    }
    
    struct TestResult {
        let id = UUID()
        let testName: String
        let success: Bool
        let message: String
        let timestamp: Date = Date()
    }
    
    var body: some View {
        NavigationView {
            List {
                // 权限状态
                Section("通知权限") {
                    HStack {
                        Image(systemName: reminderService.hasNotificationPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(reminderService.hasNotificationPermission ? .green : .red)
                        
                        VStack(alignment: .leading) {
                            Text("通知权限状态")
                                .font(.headline)
                            Text(reminderService.hasNotificationPermission ? "已授权" : "未授权")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if !reminderService.hasNotificationPermission {
                            Button("请求权限") {
                                requestPermission()
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                }
                
                // 测试控制
                Section("测试控制") {
                    Button("运行所有测试") {
                        runAllTests()
                    }
                    .disabled(isRunningTests || !reminderService.hasNotificationPermission)
                    
                    Button("清除测试结果") {
                        testResults.removeAll()
                    }
                    .disabled(testResults.isEmpty)
                    
                    Button("创建测试订阅") {
                        createTestSubscription()
                    }
                    .disabled(isRunningTests)
                    
                    Button("清理测试数据") {
                        cleanupTestData()
                    }
                    .disabled(isRunningTests)
                }
                
                // 快速测试
                Section("快速测试") {
                    Button("测试即时提醒 (5秒后)") {
                        testImmediateReminder()
                    }
                    .disabled(isRunningTests || !reminderService.hasNotificationPermission)
                    
                    Button("测试续订提醒调度") {
                        testRenewalReminderScheduling()
                    }
                    .disabled(isRunningTests || !reminderService.hasNotificationPermission)
                    
                    Button("测试提醒取消") {
                        testReminderCancellation()
                    }
                    .disabled(isRunningTests)
                }
                
                // 活跃提醒
                if !reminderService.activeReminders.isEmpty {
                    Section("活跃提醒 (\(reminderService.activeReminders.count))") {
                        ForEach(reminderService.activeReminders) { reminder in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(reminder.title)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                
                                Text(reminder.body)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Text("计划时间: \(reminder.scheduledDate, style: .date) \(reminder.scheduledDate, style: .time)")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                // 测试结果
                if !testResults.isEmpty {
                    Section("测试结果") {
                        ForEach(testResults, id: \.id) { result in
                            HStack {
                                Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                                    .foregroundColor(result.success ? .green : .red)
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(result.testName)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    Text(result.message)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text(result.timestamp, style: .time)
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                            }
                        }
                    }
                }
            }
            .navigationTitle("提醒功能测试")
            .alert("需要通知权限", isPresented: $showingPermissionAlert) {
                Button("去设置") {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("测试提醒功能需要通知权限。")
            }
            .overlay {
                if isRunningTests {
                    ProgressView("运行测试中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                }
            }
        }
    }
    
    // MARK: - 权限请求
    private func requestPermission() {
        Task {
            await reminderService.requestNotificationPermission()
        }
    }
    
    // MARK: - 测试方法
    private func runAllTests() {
        guard reminderService.hasNotificationPermission else {
            showingPermissionAlert = true
            return
        }
        
        isRunningTests = true
        testResults.removeAll()
        
        Task {
            // 测试1: 权限检查
            await testNotificationPermission()
            
            // 测试2: 创建测试订阅
            await testSubscriptionCreation()
            
            // 测试3: 提醒调度
            await testReminderScheduling()
            
            // 测试4: 提醒取消
            await testReminderCancellation()
            
            // 测试5: 批量操作
            await testBatchOperations()
            
            await MainActor.run {
                isRunningTests = false
            }
        }
    }
    
    private func testNotificationPermission() async {
        await MainActor.run {
            let hasPermission = reminderService.hasNotificationPermission
            addTestResult(
                testName: "通知权限检查",
                success: hasPermission,
                message: hasPermission ? "通知权限已授权" : "通知权限未授权"
            )
        }
    }
    
    private func testSubscriptionCreation() async {
        let testName = "测试订阅创建"
        
        do {
            let subscriptionData = SubscriptionData(
                name: "测试订阅",
                serviceProvider: "测试提供商",
                billingCycle: .monthly,
                costPerCycle: 9.99,
                currency: "CNY",
                startDate: Date(),
                status: .active
            )
            let subscription = try await subscriptionService.createSubscription(subscriptionData)
            
            await MainActor.run {
                if subscription != nil {
                    addTestResult(
                        testName: testName,
                        success: true,
                        message: "测试订阅创建成功"
                    )
                } else {
                    addTestResult(
                        testName: testName,
                        success: false,
                        message: "测试订阅创建失败"
                    )
                }
            }
        } catch {
            await MainActor.run {
                addTestResult(
                    testName: testName,
                    success: false,
                    message: "创建订阅时出错: \(error.localizedDescription)"
                )
            }
        }
    }
    
    private func testReminderScheduling() async {
        let testName = "提醒调度测试"
        
        // 获取测试订阅
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", "测试订阅")
        
        do {
            let products = try context.fetch(request)
            if let testProduct = products.first {
                await reminderService.scheduleRenewalReminders(for: testProduct)
                
                await MainActor.run {
                    addTestResult(
                        testName: testName,
                        success: true,
                        message: "提醒调度成功"
                    )
                }
            } else {
                await MainActor.run {
                    addTestResult(
                        testName: testName,
                        success: false,
                        message: "未找到测试订阅"
                    )
                }
            }
        } catch {
            await MainActor.run {
                addTestResult(
                    testName: testName,
                    success: false,
                    message: "调度提醒时出错: \(error.localizedDescription)"
                )
            }
        }
    }
    
    private func testReminderCancellation() async {
        let testName = "提醒取消测试"
        
        // 获取测试订阅
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", "测试订阅")
        
        do {
            let products = try context.fetch(request)
            if let testProduct = products.first {
                await reminderService.cancelReminders(for: testProduct.id ?? UUID())
                
                await MainActor.run {
                    addTestResult(
                        testName: testName,
                        success: true,
                        message: "提醒取消成功"
                    )
                }
            } else {
                await MainActor.run {
                    addTestResult(
                        testName: testName,
                        success: false,
                        message: "未找到测试订阅"
                    )
                }
            }
        } catch {
            await MainActor.run {
                addTestResult(
                    testName: testName,
                    success: false,
                    message: "取消提醒时出错: \(error.localizedDescription)"
                )
            }
        }
    }
    
    private func testBatchOperations() async {
        let testName = "批量操作测试"
        
        await reminderService.scheduleAllReminders()
        
        await MainActor.run {
            addTestResult(
                testName: testName,
                success: true,
                message: "批量调度所有提醒完成"
            )
        }
    }
    
    // MARK: - 快速测试
    private func testImmediateReminder() {
        Task {
            let testDate = Calendar.current.date(byAdding: .second, value: 5, to: Date()) ?? Date()
            
            let reminder = SubscriptionReminder(
                subscriptionId: UUID(),
                subscriptionName: "即时测试提醒",
                type: .renewal,
                scheduledDate: testDate,
                title: "测试提醒",
                body: "这是一个5秒后的测试提醒"
            )
            
            await reminderService.scheduleNotification(for: reminder)
            
            await MainActor.run {
                addTestResult(
                    testName: "即时提醒测试",
                    success: true,
                    message: "5秒后将收到测试提醒"
                )
            }
        }
    }
    
    private func testRenewalReminderScheduling() {
        Task {
            await testReminderScheduling()
        }
    }
    
    private func testReminderCancellation() {
        Task {
            await testReminderCancellation()
        }
    }
    
    // MARK: - 数据管理
    private func createTestSubscription() {
        Task {
            let subscriptionData = SubscriptionData(
                name: "测试订阅 \(Date().timeIntervalSince1970)",
                serviceProvider: "测试提供商",
                billingCycle: .monthly,
                costPerCycle: 9.99,
                currency: "CNY",
                startDate: Date(),
                status: .active
            )
            let _ = try? await subscriptionService.createSubscription(subscriptionData)
            
            await MainActor.run {
                addTestResult(
                    testName: "创建测试订阅",
                    success: true,
                    message: "测试订阅已创建"
                )
            }
        }
    }
    
    private func cleanupTestData() {
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "name CONTAINS %@", "测试订阅")
        
        do {
            let testProducts = try context.fetch(request)
            for product in testProducts {
                context.delete(product)
            }
            try context.save()
            
            addTestResult(
                testName: "清理测试数据",
                success: true,
                message: "已删除 \(testProducts.count) 个测试订阅"
            )
        } catch {
            addTestResult(
                testName: "清理测试数据",
                success: false,
                message: "清理失败: \(error.localizedDescription)"
            )
        }
    }
    
    // MARK: - 辅助方法
    private func addTestResult(testName: String, success: Bool, message: String) {
        let result = TestResult(
            testName: testName,
            success: success,
            message: message
        )
        testResults.append(result)
    }
}

#Preview {
    SubscriptionReminderTestView(context: PersistenceController.preview.container.viewContext)
}