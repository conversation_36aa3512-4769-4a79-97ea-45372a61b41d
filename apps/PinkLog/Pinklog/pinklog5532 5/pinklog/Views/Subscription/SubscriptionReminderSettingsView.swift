//
//  SubscriptionReminderSettingsView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  订阅提醒设置界面
//

import SwiftUI
import CoreData

struct SubscriptionReminderSettingsView: View {
    @Environment(\.managedObjectContext) private var context
    @StateObject private var reminderService: SubscriptionReminderService
    
    let product: Product
    
    @State private var reminderEnabled: Bool
    @State private var reminderDays: Int
    @State private var autoRenewal: Bool
    @State private var showingPermissionAlert = false
    @State private var isLoading = false
    
    private let reminderDayOptions = [1, 3, 7, 14, 30]
    
    init(product: Product, context: NSManagedObjectContext) {
        self.product = product
        self._reminderService = StateObject(wrappedValue: SubscriptionReminderService(context: context))
        self._reminderEnabled = State(initialValue: product.reminderEnabled)
        self._reminderDays = State(initialValue: Int(product.subscriptionReminderDays))
        self._autoRenewal = State(initialValue: product.autoRenewal)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本设置
                Section("提醒设置") {
                    Toggle("启用续订提醒", isOn: $reminderEnabled)
                        .onChange(of: reminderEnabled) { newValue in
                            if newValue && !reminderService.hasNotificationPermission {
                                showingPermissionAlert = true
                            }
                        }
                    
                    if reminderEnabled {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("提前提醒天数")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Picker("提醒天数", selection: $reminderDays) {
                                ForEach(reminderDayOptions, id: \.self) { days in
                                    Text("\(days)天前")
                                        .tag(days)
                                }
                            }
                            .pickerStyle(.segmented)
                        }
                        
                        Toggle("自动续订", isOn: $autoRenewal)
                    }
                }
                
                // 通知权限状态
                Section("通知权限") {
                    HStack {
                        Image(systemName: reminderService.hasNotificationPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(reminderService.hasNotificationPermission ? .green : .red)
                        
                        Text(reminderService.hasNotificationPermission ? "已授权" : "未授权")
                        
                        Spacer()
                        
                        if !reminderService.hasNotificationPermission {
                            Button("请求权限") {
                                requestNotificationPermission()
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                }
                
                // 当前活跃提醒
                if !reminderService.activeReminders.isEmpty {
                    Section("活跃提醒") {
                        ForEach(reminderService.activeReminders.filter { $0.subscriptionId == product.id }) { reminder in
                            ReminderRowView(reminder: reminder)
                        }
                    }
                }
                
                // 提醒类型说明
                Section("提醒类型") {
                    ForEach(ReminderType.allCases, id: \.self) { type in
                        HStack {
                            Image(systemName: type.icon)
                                .foregroundColor(.blue)
                                .frame(width: 20)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(type.displayName)
                                    .font(.subheadline)
                                
                                Text(getReminderDescription(for: type))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.vertical, 2)
                    }
                }
            }
            .navigationTitle("提醒设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveSettings()
                    }
                    .disabled(isLoading)
                }
            }
            .alert("需要通知权限", isPresented: $showingPermissionAlert) {
                Button("去设置") {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                Button("取消", role: .cancel) {
                    reminderEnabled = false
                }
            } message: {
                Text("为了发送续订提醒，需要您授权通知权限。")
            }
            .overlay {
                if isLoading {
                    ProgressView("保存中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                }
            }
        }
    }
    
    private func requestNotificationPermission() {
        Task {
            await reminderService.requestNotificationPermission()
        }
    }
    
    private func saveSettings() {
        isLoading = true
        
        Task {
            await reminderService.updateReminderSettings(
                for: product,
                enabled: reminderEnabled,
                reminderDays: reminderDays
            )
            
            // 更新自动续订设置
            await MainActor.run {
                product.autoRenewal = autoRenewal
                
                do {
                    try context.save()
                } catch {
                    print("保存自动续订设置失败: \(error)")
                }
                
                isLoading = false
            }
        }
    }
    
    private func getReminderDescription(for type: ReminderType) -> String {
        switch type {
        case .renewal:
            return "在订阅到期前提醒您续订"
        case .trialExpiry:
            return "在试用期结束前提醒您"
        case .priceChange:
            return "当订阅价格发生变化时通知您"
        case .underutilized:
            return "当使用频率较低时提醒您评估"
        case .costAlert:
            return "当订阅成本较高时提醒您关注"
        }
    }
}

// MARK: - 提醒行视图
struct ReminderRowView: View {
    let reminder: SubscriptionReminder
    
    var body: some View {
        HStack {
            Image(systemName: reminder.type.icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(reminder.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(reminder.body)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                Text("计划时间: \(reminder.scheduledDate, style: .date) \(reminder.scheduledDate, style: .time)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if reminder.isActive {
                Image(systemName: "bell.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 提醒管理视图
struct SubscriptionReminderManagementView: View {
    @Environment(\.managedObjectContext) private var context
    @StateObject private var reminderService: SubscriptionReminderService
    
    @State private var showingSettings = false
    @State private var selectedSubscription: Product?
    
    init(context: NSManagedObjectContext) {
        self._reminderService = StateObject(wrappedValue: SubscriptionReminderService(context: context))
    }
    
    var body: some View {
        NavigationView {
            List {
                // 权限状态
                Section {
                    HStack {
                        Image(systemName: reminderService.hasNotificationPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(reminderService.hasNotificationPermission ? .green : .red)
                        
                        VStack(alignment: .leading) {
                            Text("通知权限")
                                .font(.headline)
                            Text(reminderService.hasNotificationPermission ? "已授权，可以发送提醒" : "未授权，无法发送提醒")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if !reminderService.hasNotificationPermission {
                            Button("授权") {
                                Task {
                                    await reminderService.requestNotificationPermission()
                                }
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                }
                
                // 活跃提醒统计
                Section("提醒统计") {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("\(reminderService.activeReminders.count)")
                                .font(.title2)
                                .fontWeight(.bold)
                            Text("活跃提醒")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button("重新调度所有提醒") {
                            Task {
                                await reminderService.scheduleAllReminders()
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                }
                
                // 按订阅分组的提醒
                if !reminderService.activeReminders.isEmpty {
                    Section("活跃提醒") {
                        let groupedReminders = Dictionary(grouping: reminderService.activeReminders) { $0.subscriptionName }
                        
                        ForEach(groupedReminders.keys.sorted(), id: \.self) { subscriptionName in
                            DisclosureGroup(subscriptionName) {
                                ForEach(groupedReminders[subscriptionName] ?? []) { reminder in
                                    ReminderRowView(reminder: reminder)
                                }
                            }
                        }
                    }
                }
                
                // 订阅列表
                Section("订阅管理") {
                    ForEach(getVirtualSubscriptions(), id: \.id) { subscription in
                        SubscriptionReminderRowView(
                            subscription: subscription,
                            reminderService: reminderService
                        ) {
                            selectedSubscription = subscription
                            showingSettings = true
                        }
                    }
                }
            }
            .navigationTitle("提醒管理")
            .sheet(isPresented: $showingSettings) {
                if let subscription = selectedSubscription {
                    SubscriptionReminderSettingsView(product: subscription, context: context)
                }
            }
        }
    }
    
    private func getVirtualSubscriptions() -> [Product] {
        let request: NSFetchRequest<Product> = Product.fetchRequest()
        request.predicate = NSPredicate(format: "isVirtualProduct == YES")
        request.sortDescriptors = [NSSortDescriptor(keyPath: \Product.name, ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取虚拟订阅失败: \(error)")
            return []
        }
    }
}

// MARK: - 订阅提醒行视图
struct SubscriptionReminderRowView: View {
    let subscription: Product
    let reminderService: SubscriptionReminderService
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(subscription.name ?? "未知订阅")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if let expiryDate = subscription.expiryDate {
                        Text("到期: \(expiryDate, style: .date)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack {
                        Image(systemName: subscription.reminderEnabled ? "bell.fill" : "bell.slash.fill")
                            .foregroundColor(subscription.reminderEnabled ? .green : .gray)
                        
                        Text(subscription.reminderEnabled ? "已启用" : "已禁用")
                            .font(.caption)
                            .foregroundColor(subscription.reminderEnabled ? .green : .gray)
                    }
                    
                    if subscription.reminderEnabled {
                        Text("\(subscription.subscriptionReminderDays)天前")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    SubscriptionReminderManagementView(context: PersistenceController.preview.container.viewContext)
}