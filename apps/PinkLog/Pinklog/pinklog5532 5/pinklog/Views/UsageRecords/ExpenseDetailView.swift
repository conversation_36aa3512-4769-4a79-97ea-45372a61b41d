import SwiftUI

/// 费用详情视图
struct ExpenseDetailView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel

    let expense: RelatedExpense
    let product: Product

    @State private var showingDeleteAlert = false
    @State private var showingEditSheet = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 费用基本信息
                expenseInfoCard

                // 备注信息
                if let notes = expense.notes, !notes.isEmpty {
                    notesCard(notes)
                }

                // 操作按钮
                actionButtons
            }
            .padding()
        }
        .navigationTitle("费用详情")
        .navigationBarItems(
            trailing: Button(action: {
                showingDeleteAlert = true
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
        )
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除费用记录"),
                message: Text("确定要删除此费用记录吗？此操作无法撤销。"),
                primaryButton: .destructive(Text("删除")) {
                    deleteExpense()
                },
                secondaryButton: .cancel()
            )
        }
        .sheet(isPresented: $showingEditSheet) {
            EditExpenseView(expense: expense, product: product)
                .environmentObject(usageViewModel)
                .environmentObject(themeManager)
        }
    }

    // 费用基本信息卡片
    private var expenseInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 费用类型和金额
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("费用类型")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(expense.type?.name ?? "其他费用")
                        .font(.headline)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("金额")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("¥\(Int(expense.amount))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                }
            }

            Divider()

            // 日期信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("费用日期")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(expense.date?.formatted(date: .long, time: .omitted) ?? "未知")
                        .font(.subheadline)
                }

                Spacer()

                // RelatedExpense没有createdAt属性，显示ID信息代替
                if let id = expense.id {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("记录ID")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(id.uuidString.prefix(8) + "...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 备注卡片
    private func notesCard(_ notes: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("备注")
                .font(.caption)
                .foregroundColor(.secondary)

            Text(notes)
                .font(.body)
                .foregroundColor(.primary)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 操作按钮
    private var actionButtons: some View {
        Button(action: {
            showingEditSheet = true
        }) {
            HStack {
                Image(systemName: "pencil")
                Text("编辑费用记录")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(themeManager.currentTheme.primaryColor)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
    }

    // 删除费用记录
    private func deleteExpense() {
        usageViewModel.deleteExpense(expense)
        presentationMode.wrappedValue.dismiss()
    }
}

// 编辑费用视图
struct EditExpenseView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let expense: RelatedExpense
    let product: Product

    @State private var amount: String = ""
    @State private var date: Date = Date()
    @State private var selectedExpenseType: ExpenseType?
    @State private var notes: String = ""

    @State private var showingExpenseTypeSelector = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 费用类型选项
    @State private var expenseTypes: [ExpenseType] = []

    var body: some View {
        NavigationView {
            Form {
                // 金额
                Section(header: Text("金额")) {
                    HStack {
                        Text("¥")
                        TextField("输入金额", text: $amount)
                            .keyboardType(.decimalPad)
                    }
                }

                // 日期
                Section(header: Text("日期")) {
                    DatePicker("选择日期", selection: $date, displayedComponents: .date)
                }

                // 费用类型
                Section(header: Text("费用类型")) {
                    Button(action: {
                        showingExpenseTypeSelector = true
                    }) {
                        HStack {
                            Text("费用类型")
                            Spacer()
                            Text(selectedExpenseType?.name ?? "选择费用类型")
                                .foregroundColor(selectedExpenseType != nil ? .primary : .secondary)
                        }
                    }
                }

                // 备注
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }
            }
            .navigationTitle("编辑费用记录")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("保存") {
                    saveExpense()
                }
                .disabled(!isFormValid)
            )
            .sheet(isPresented: $showingExpenseTypeSelector) {
                ExpenseTypeSelectorView(selectedExpenseType: $selectedExpenseType)
            }
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                // 加载现有数据
                loadExpenseData()
            }
        }
    }

    // 表单验证
    private var isFormValid: Bool {
        guard let amountValue = Double(amount), amountValue > 0 else {
            return false
        }
        return true
    }

    // 加载费用数据
    private func loadExpenseData() {
        amount = String(format: "%.2f", expense.amount)
        date = expense.date ?? Date()
        selectedExpenseType = expense.type
        notes = expense.notes ?? ""
    }

    // 保存费用记录
    private func saveExpense() {
        guard let amountValue = Double(amount) else {
            alertMessage = "请输入有效金额"
            showingAlert = true
            return
        }

        let success = usageViewModel.updateExpense(
            expense: expense,
            amount: amountValue,
            date: date,
            type: selectedExpenseType,
            notes: notes.isEmpty ? nil : notes
        )

        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "更新费用记录失败"
            showingAlert = true
        }
    }
}
