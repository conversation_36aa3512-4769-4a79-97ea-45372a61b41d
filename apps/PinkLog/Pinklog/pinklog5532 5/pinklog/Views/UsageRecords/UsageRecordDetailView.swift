import SwiftUI
import CoreData
import AVFoundation
import MarkdownUI
import AVKit

struct UsageRecordDetailView: View {
    // MARK: - 来源类型
    enum SourceType {
        case memoirs      // 来自回忆录
        case products     // 来自产品页面
        case timeline     // 来自时间线
        case other        // 其他来源
    }
    
    // MARK: - 环境和状态
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) var viewContext
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel

    // MARK: - 属性
    let usageRecord: UsageRecord
    let product: Product
    let sourceType: SourceType // 新增来源参数

    @State private var showingDeleteAlert = false
    @State private var showingEditSheet = false
    @State private var animateHeart = false
    @State private var isFavorited = false

    // 多媒体相关状态
    @State private var images: [UIImage] = []
    @State private var audioRecordings: [URL] = []
    @State private var audioPlayer: AVAudioPlayer?
    @State private var avPlayer: AVPlayer?
    @State private var isPlaying = false
    @State private var playingRecordingIndex: Int?
    @State private var showingImagePreview = false
    @State private var previewingImageIndex: Int = 0
    @State private var isImageLoaded = false
    @State private var usingAVPlayer = false // 标记是否使用AVPlayer
    @State private var scrollProgress: CGFloat = 0.0 // 滚动进度
    @State private var scrollPosition: CGFloat = 0.0 // 滚动位置
    
    // 动态进度计算相关状态
    @State private var sectionHeights: [String: CGFloat] = [:] // 各区域高度
    @State private var sectionPositions: [String: CGFloat] = [:] // 各区域顶部位置
    @State private var totalContentHeight: CGFloat = 0 // 总内容高度
    @State private var currentScrollOffset: CGFloat = 0 // 当前滚动偏移量
    @State private var screenHeight: CGFloat = 0 // 屏幕高度
    @State private var isInitialLoad: Bool = true // 是否首次加载

    // MARK: - 计算属性
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .none
        return formatter
    }

    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter
    }

    private var satisfactionColor: Color {
        let satisfactionValue = Int(usageRecord.satisfaction)
        switch satisfactionValue {
        case 5:
            return .green
        case 4:
            return .mint
        case 3:
            return .yellow
        case 2:
            return .orange
        case 1:
            return .red
        default:
            return .gray
        }
    }

    // MARK: - 主视图
    var body: some View {
        if usageRecord.isStory && sourceType == .memoirs {
            // 来自回忆录的物品故事 - 使用新的沉浸式UI
            dreamlikeStoryDetailView
        } else if usageRecord.isStory {
            // 来自其他地方的物品故事 - 使用原有的物品故事布局
            originalStoryDetailView
        } else {
            // 普通使用记录 - 保持原有布局
            originalDetailView
        }
    }
    
    // MARK: - 梦幻故事详情视图
    private var dreamlikeStoryDetailView: some View {
        GeometryReader { geometry in
            ZStack {
                // 深度沉浸式背景
                storyDreamlikeBackground
                    .ignoresSafeArea(.all)
                    .onAppear {
                        screenHeight = geometry.size.height
                    }
                
                // 主要内容区域
                ScrollViewReader { scrollProxy in
                    ScrollView(.vertical, showsIndicators: false) {
                        LazyVStack(spacing: 0) {
                        // 英雄标题区域
                        storyHeroSection
                            .background(
                                GeometryReader { geo in
                                    Color.clear
                                        .preference(key: SectionPositionPreferenceKey.self, 
                                                  value: [SectionPositionData(id: "hero", frame: geo.frame(in: .named("scrollView")))])
                                }
                            )
                            .padding(.top, 100)
                        
                        // 故事内容核心区域
                        storyMainContentSection
                            .background(
                                GeometryReader { geo in
                                    Color.clear
                                        .preference(key: SectionPositionPreferenceKey.self, 
                                                  value: [SectionPositionData(id: "content", frame: geo.frame(in: .named("scrollView")))])
                                }
                            )
                            .padding(.top, 32)
                        
                        // 多媒体回忆区域
                        if !images.isEmpty || !audioRecordings.isEmpty {
                            storyMultimediaSection
                                .background(
                                    GeometryReader { geo in
                                        Color.clear
                                            .preference(key: SectionPositionPreferenceKey.self, 
                                                      value: [SectionPositionData(id: "multimedia", frame: geo.frame(in: .named("scrollView")))])
                                    }
                                )
                                .padding(.top, 40)
                        }
                        
                        // 产品关联区域
                        storyProductSection
                            .background(
                                GeometryReader { geo in
                                    Color.clear
                                        .preference(key: SectionPositionPreferenceKey.self, 
                                                  value: [SectionPositionData(id: "product", frame: geo.frame(in: .named("scrollView")))])
                                }
                            )
                            .padding(.top, 32)
                        
                        // 回忆探索区域
                        storyMemoryExplorationSection
                            .background(
                                GeometryReader { geo in
                                    Color.clear
                                        .preference(key: SectionPositionPreferenceKey.self, 
                                                  value: [SectionPositionData(id: "exploration", frame: geo.frame(in: .named("scrollView")))])
                                }
                            )
                            .padding(.top, 24)
                        
                        // 底部缓冲
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: 120)
                    }
                    }
                    .coordinateSpace(name: "scrollView")
                    .onPreferenceChange(SectionPositionPreferenceKey.self) { positions in
                        updateSectionPositions(positions)
                    }
                    .background(
                        GeometryReader { scrollGeo in
                            Color.clear
                                .onChange(of: scrollGeo.frame(in: .named("scrollView")).minY) { offset in
                                    currentScrollOffset = -offset
                                    updateScrollProgress()
                                }
                        }
                    )
                    .gesture(
                        // 添加手势控制
                        DragGesture()
                            .onEnded { value in
                                // 左右滑动切换回忆
                                if abs(value.translation.width) > abs(value.translation.height) {
                                    if value.translation.width > 50 {
                                        // 向右滑动 - 上一个回忆
                                        navigateToPreviousMemory()
                                    } else if value.translation.width < -50 {
                                        // 向左滑动 - 下一个回忆
                                        navigateToNextMemory()
                                    }
                                }
                            }
                    )
                }
                
                // 浮动导航栏带阅读进度
                VStack {
                    VStack(spacing: 0) {
                        storyFloatingHeader
                        
                        // 阅读进度指示器
                        ReadingProgressIndicator(progress: scrollProgress)
                            .padding(.horizontal, 20)
                    }
                    Spacer()
                }
                
                // 浮动操作栏
                VStack {
                    Spacer()
                    storyFloatingActionBar
                        .padding(.bottom, 34)
                }
            }
        }
        .navigationBarHidden(true)
        .alert("删除故事", isPresented: $showingDeleteAlert) {
            Button("删除", role: .destructive) {
                deleteStory()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("确定要删除这个故事吗？此操作不可恢复。")
        }
        .sheet(isPresented: $showingEditSheet) {
            EditUsageRecordView(record: usageRecord, product: product)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        .onAppear {
            loadMultimediaContent()
            // 初始化收藏状态（通过notes字段判断）
            isFavorited = usageRecord.notes?.contains("[收藏]") ?? false
            // 重置动态进度状态
            scrollProgress = 0.0
            sectionHeights.removeAll()
            sectionPositions.removeAll()
            totalContentHeight = 0
            currentScrollOffset = 0
            screenHeight = 0
            isInitialLoad = true
        }
    }
    
    // MARK: - 原始物品故事详情视图
    private var originalStoryDetailView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 顶部卡片：日期和类型
                dateAndTypeCard

                // 物品故事记录 - 原有布局

                // 1. 故事内容区域（整合标题、情感价值、内容）
                storyContentSection

                // 2. 多媒体内容区域（图片和音频）
                if !images.isEmpty || !audioRecordings.isEmpty {
                    multimediaSection
                }

                // 3. 使用详情卡片（放在内容后面）
                usageDetailsCard

                // 4. 使用统计卡片（放在最后）
                usageStatsCard

                // 操作按钮
                actionButtonsSection

                Spacer()
            }
            .padding()
        }
        .navigationTitle(getNavigationTitle())
        .navigationBarItems(
            trailing: Button(action: {
                showingDeleteAlert = true
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
        )
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除使用记录"),
                message: Text("确定要删除这条使用记录吗？此操作不可恢复。"),
                primaryButton: .destructive(Text("删除")) {
                    deleteUsageRecord()
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .sheet(isPresented: $showingEditSheet) {
            EditUsageRecordView(record: usageRecord, product: product)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        .onAppear {
            loadMultimediaContent()
        }
    }
    
    // MARK: - 原始详情视图
    private var originalDetailView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 顶部卡片：日期和类型
                dateAndTypeCard

                // 根据使用类型显示不同内容
                if usageRecord.type == .personal {
                    // 个人使用记录 - 保持原有布局

                    // 满意度卡片
                    satisfactionCard

                    // 使用详情卡片
                    usageDetailsCard

                    // 使用统计卡片
                    usageStatsCard

                    // 备注卡片
                    if let notes = usageRecord.notes, !notes.isEmpty {
                        notesCard(notes)
                    }
                } else {
                    // 借出记录 - 保持原有布局

                    // 借出详情卡片
                    loanDetailsCard

                    // 借出状态卡片
                    loanStatusCard

                    // 备注卡片
                    if let notes = usageRecord.notes, !notes.isEmpty {
                        notesCard(notes)
                    }
                }

                // 操作按钮
                actionButtonsSection

                Spacer()
            }
            .padding()
        }
        .navigationTitle(getNavigationTitle())
        .navigationBarItems(
            trailing: Button(action: {
                showingDeleteAlert = true
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
        )
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除使用记录"),
                message: Text("确定要删除这条使用记录吗？此操作不可恢复。"),
                primaryButton: .destructive(Text("删除")) {
                    deleteUsageRecord()
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .sheet(isPresented: $showingEditSheet) {
            EditUsageRecordView(record: usageRecord, product: product)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        // 移除图片预览，因为现在使用ImageGalleryView处理图片预览
        .onAppear {
            // 加载图片和音频数据
            loadMultimediaContent()
        }
    }

    // MARK: - 物品故事专用组件

    // 故事内容区域 - 整合标题、情感价值和内容
    private var storyContentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            if let title = usageRecord.title, !title.isEmpty {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }

            // 情感价值和满意度并排显示
            HStack(spacing: 20) {
                // 情感价值
                VStack(alignment: .leading, spacing: 8) {
                    Text("情感价值")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 4) {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= Int(usageRecord.emotionalValue) ? "heart.fill" : "heart")
                                .font(.body)
                                .foregroundColor(index <= Int(usageRecord.emotionalValue) ? .red : .gray)
                        }
                    }
                }

                Divider()
                    .frame(height: 40)

                // 满意度
                VStack(alignment: .leading, spacing: 8) {
                    Text("满意度")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 4) {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= Int(usageRecord.satisfaction) ? "star.fill" : "star")
                                .font(.body)
                                .foregroundColor(index <= Int(usageRecord.satisfaction) ? .yellow : .gray)
                        }
                    }
                }
            }
            .padding(.vertical, 8)

            // 分隔线
            Divider()

            // 故事内容（备注）
            if let notes = usageRecord.notes, !notes.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("故事内容")
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryColor)

                    MarkdownUI.Markdown(notes)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }

            // 关联回忆
            if let memories = usageRecord.memories, !memories.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("关联回忆")
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryColor)

                    Text(memories)
                        .font(.body)
                        .foregroundColor(.primary)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 多媒体内容区域 - 整合图片和音频
    private var multimediaSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("多媒体内容")
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryColor)

            // 图片展示 - 使用优化后的ImageGalleryView
            if let _ = usageRecord.images, let recordId = usageRecord.id?.uuidString {
                // 获取图片数据数组
                let imageDatas = usageViewModel.getImageDatasFromRecord(usageRecord)
                if !imageDatas.isEmpty {
                    // 使用优化后的图片集合视图
                    ImageGalleryView(
                        imageDatas: imageDatas,
                        baseCacheKey: "record_\(recordId)"
                    )
                }
            }

            // 音频展示
            if !audioRecordings.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "mic")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                        Text("语音回忆")
                            .font(.subheadline)
                        Spacer()
                    }

                    ForEach(0..<audioRecordings.count, id: \.self) { index in
                        Button(action: {
                            togglePlayRecording(at: index)
                        }) {
                            HStack {
                                Image(systemName: (playingRecordingIndex == index && isPlaying) ? "pause.circle.fill" : "play.circle.fill")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                                    .font(.title3)

                                Text("录音 \(index + 1)")
                                    .foregroundColor(.primary)

                                Spacer()

                                Text(audioRecordings[index].lastPathComponent)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                            .padding()
                            .background(Color(UIColor.tertiarySystemBackground))
                            .cornerRadius(10)
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(16)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // MARK: - 组件视图

    // 日期和类型卡片
    private var dateAndTypeCard: some View {
        VStack(spacing: 16) {
            // 日期
            if let date = usageRecord.date {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(dateFormatter.string(from: date))
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(timeFormatter.string(from: date))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "calendar")
                        .font(.title2)
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
            }

            Divider()

            // 使用类型
            HStack {
                // 物品故事特殊图标
                if usageRecord.isStory {
                    Image(systemName: "book.fill")
                        .font(.title3)
                        .foregroundColor(.orange)
                } else {
                    Image(systemName: usageRecord.type.icon)
                        .font(.title3)
                        .foregroundColor(usageRecord.type.color)
                }

                // 类型文本
                if usageRecord.isStory {
                    Text("物品故事")
                        .font(.headline)
                        .foregroundColor(.orange)
                } else {
                    Text(usageRecord.type.rawValue)
                        .font(.headline)
                        .foregroundColor(usageRecord.type.color)
                }

                Spacer()

                // 借出状态或物品故事标签
                if usageRecord.type == .loaned {
                    Text(usageRecord.loanStatus.rawValue)
                        .font(.subheadline)
                        .foregroundColor(usageRecord.loanStatus.color)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(usageRecord.loanStatus.color.opacity(0.1))
                        .cornerRadius(8)
                } else if usageRecord.isStory {
                    // 物品故事特殊标签
                    HStack(spacing: 4) {
                        Image(systemName: "pencil.and.outline")
                            .font(.caption)
                        Text("记录")
                    }
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(Color.orange)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 满意度卡片
    private var satisfactionCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("满意度评价")
                .font(.headline)

            HStack {
                ForEach(1...5, id: \.self) { index in
                    Image(systemName: index <= Int(usageRecord.satisfaction) ? "star.fill" : "star")
                        .font(.title3)
                        .foregroundColor(index <= Int(usageRecord.satisfaction) ? .yellow : .gray)
                }

                Spacer()

                Text("\(Int(usageRecord.satisfaction))/5")
                    .font(.headline)
                    .foregroundColor(satisfactionColor)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(satisfactionColor.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 使用详情卡片
    private var usageDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("使用详情")
                .font(.headline)

            // 使用场景
            DetailRow(icon: "theatermasks", title: "使用场景", value: usageRecord.scenario ?? "未记录")

            // 使用时长
            if usageRecord.duration > 0 {
                DetailRow(
                    icon: "clock",
                    title: "使用时长",
                    value: formatDuration(usageRecord.duration)
                )
            }

            // 使用环境
            if let wearCondition = usageRecord.wearCondition, !wearCondition.isEmpty {
                DetailRow(
                    icon: "wrench.and.screwdriver",
                    title: "磨损状况",
                    value: wearCondition
                )
            }

            // 产品信息
            DetailRow(
                icon: "cube.box",
                title: "产品",
                value: product.name ?? "未命名产品",
                valueColor: themeManager.currentTheme.primaryColor
            )
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 标题卡片
    private var titleCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            if let title = usageRecord.title, !title.isEmpty {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 情感价值卡片
    private var emotionalValueCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("情感价值")
                .font(.headline)

            HStack {
                ForEach(1...5, id: \.self) { index in
                    Image(systemName: index <= Int(usageRecord.emotionalValue) ? "heart.fill" : "heart")
                        .font(.title3)
                        .foregroundColor(index <= Int(usageRecord.emotionalValue) ? .red : .gray)
                }

                Spacer()

                Text("\(Int(usageRecord.emotionalValue))/5")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 图片展示卡片
    private var imagesCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "photo")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                Text("照片记忆")
                    .font(.headline)
                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<images.count, id: \.self) { index in
                        Image(uiImage: images[index])
                            .resizable()
                            .scaledToFill()
                            .frame(width: 160, height: 200)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .onTapGesture {
                                previewImage(images[index])
                            }
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 音频记录卡片
    private var audioRecordingsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "mic")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                Text("语音回忆")
                    .font(.headline)
                Spacer()
            }

            ForEach(0..<audioRecordings.count, id: \.self) { index in
                Button(action: {
                    togglePlayRecording(at: index)
                }) {
                    HStack {
                        Image(systemName: (playingRecordingIndex == index && isPlaying) ? "pause.circle.fill" : "play.circle.fill")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .font(.title2)

                        Text("录音 \(index + 1)")
                            .foregroundColor(.primary)

                        Spacer()

                        Text(audioRecordings[index].lastPathComponent)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                    .padding()
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(10)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 关联回忆卡片
    private func memoriesCard(_ memories: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                Text("关联回忆")
                    .font(.headline)

                Spacer()
            }

            Text(memories)
                .font(.body)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 备注卡片
    private func notesCard(_ notes: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "note.text")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                Text("备注")
                    .font(.headline)

                Spacer()
            }

            // 如果是物品故事，使用Markdown渲染
            if usageRecord.isStory {
                MarkdownUI.Markdown(notes)
                    .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                Text(notes)
                    .font(.body)
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 使用统计卡片
    private var usageStatsCard: some View {
        let productUsageCount = product.totalUsageCount
        let usageDate = usageRecord.date ?? Date()
        let timeSincePurchase = Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: usageDate).day ?? 0

        return VStack(alignment: .leading, spacing: 16) {
            Text("使用统计")
                .font(.headline)

            // 累计使用次数
            HStack {
                Image(systemName: "number")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 24, height: 24)

                Text("产品累计使用次数")
                    .font(.subheadline)

                Spacer()

                Text("\(productUsageCount)次")
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }

            // 距离购买天数
            HStack {
                Image(systemName: "calendar.badge.clock")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 24, height: 24)

                Text("记录时距购买天数")
                    .font(.subheadline)

                Spacer()

                Text("\(timeSincePurchase)天")
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }

            // 产品平均满意度
            HStack {
                Image(systemName: "star.circle")
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 24, height: 24)

                Text("产品平均满意度")
                    .font(.subheadline)

                Spacer()

                Text(String(format: "%.1f", product.averageSatisfaction))
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 借出详情卡片
    private var loanDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("借出详情")
                .font(.headline)

            // 借出对象
            DetailRow(
                icon: "person.fill",
                title: "借出对象",
                value: usageRecord.borrowerName ?? "未记录"
            )

            // 联系方式
            if let contactInfo = usageRecord.contactInfo, !contactInfo.isEmpty {
                DetailRow(
                    icon: "phone.fill",
                    title: "联系方式",
                    value: contactInfo
                )
            }

            // 产品信息
            DetailRow(
                icon: "cube.box",
                title: "产品",
                value: product.name ?? "未命名产品",
                valueColor: themeManager.currentTheme.primaryColor
            )
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 借出状态卡片
    private var loanStatusCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("借出状态")
                .font(.headline)

            // 借出日期
            if let date = usageRecord.date {
                DetailRow(
                    icon: "calendar",
                    title: "借出日期",
                    value: dateFormatter.string(from: date)
                )
            }

            // 预计归还日期
            if let dueDate = usageRecord.dueDate {
                DetailRow(
                    icon: "calendar.badge.clock",
                    title: "预计归还日期",
                    value: dateFormatter.string(from: dueDate),
                    valueColor: usageRecord.isOverdue ? .red : .primary
                )
            }

            // 实际归还日期
            if let returnDate = usageRecord.returnDate {
                DetailRow(
                    icon: "checkmark.circle",
                    title: "实际归还日期",
                    value: dateFormatter.string(from: returnDate),
                    valueColor: .green
                )
            }

            // 借出时长
            if let loanDuration = usageRecord.loanDuration {
                DetailRow(
                    icon: "clock",
                    title: "借出时长",
                    value: "\(loanDuration)天"
                )
            }

            // 剩余天数
            if let daysRemaining = usageRecord.daysRemaining {
                DetailRow(
                    icon: "timer",
                    title: daysRemaining >= 0 ? "剩余天数" : "已逾期",
                    value: daysRemaining >= 0 ? "\(daysRemaining)天" : "\(abs(daysRemaining))天",
                    valueColor: daysRemaining >= 0 ? .primary : .red
                )
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    // 操作按钮部分
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // 编辑按钮
            Button(action: {
                showingEditSheet = true
            }) {
                HStack {
                    Image(systemName: "pencil")
                    Text("编辑记录")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                .foregroundColor(themeManager.currentTheme.primaryColor)
                .cornerRadius(12)
            }

            // 借出记录特有的按钮
            if usageRecord.type == .loaned && usageRecord.returnDate == nil {
                Button(action: {
                    markAsReturned()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                        Text("标记为已归还")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .foregroundColor(.green)
                    .cornerRadius(12)
                }
            }

            // 收藏按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    animateHeart.toggle()
                }

                // 这里可以添加加入收藏或其他喜爱功能的逻辑
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    animateHeart = false
                }
            }) {
                HStack {
                    Image(systemName: animateHeart ? "heart.fill" : "heart")
                        .scaleEffect(animateHeart ? 1.3 : 1.0)
                    Text("收藏")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red.opacity(0.1))
                .foregroundColor(.red)
                .cornerRadius(12)
            }
        }
    }

    // MARK: - 辅助函数

    // 格式化使用时长
    private func formatDuration(_ duration: Double) -> String {
        let hours = Int(duration)
        let minutes = Int((duration - Double(hours)) * 60)

        if hours > 0 {
            return minutes > 0 ? "\(hours)小时\(minutes)分钟" : "\(hours)小时"
        } else {
            return "\(minutes)分钟"
        }
    }

    // 删除使用记录
    private func deleteUsageRecord() {
        let success = usageViewModel.deleteUsageRecord(usageRecord)
        if success {
            presentationMode.wrappedValue.dismiss()
        }
    }

    // 标记为已归还
    private func markAsReturned() {
        if usageRecord.type == .loaned {
            let success = usageViewModel.markAsReturned(record: usageRecord)
            if !success {
                print("标记为已归还失败")
            }
        }
    }

    // 加载多媒体内容
    private func loadMultimediaContent() {
        // 异步加载图片，避免阻塞主线程
        DispatchQueue.global(qos: .userInitiated).async {
            // 加载图片
            let loadedImages = self.usageViewModel.getImagesFromRecord(self.usageRecord)

            DispatchQueue.main.async {
                self.images = loadedImages
            }
        }

        // 加载音频 - 使用更新的方法支持新旧格式
        audioRecordings = usageRecord.getStoryAudioURLs()
    }

    // 播放/暂停录音
    private func togglePlayRecording(at index: Int) {
        guard index < audioRecordings.count else { return }

        if playingRecordingIndex == index && isPlaying {
            // 如果当前正在播放该录音，则暂停
            stopPlayback()
        } else {
            // 如果没有播放该录音，则开始播放
            playRecording(at: index)
        }
    }

    // 播放录音
    private func playRecording(at index: Int) {
        // 先停止之前的播放
        stopPlayback()

        guard index < audioRecordings.count else {
            print("播放失败: 索引超出范围")
            return
        }

        let url = audioRecordings[index]
        print("尝试播放录音: \(url.path)")

        // 检查文件是否存在
        let fileManager = FileManager.default
        guard fileManager.fileExists(atPath: url.path) else {
            print("播放失败: 文件不存在")
            return
        }

        // 获取文件信息用于调试
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            let fileSize = attributes[.size] as? UInt64 ?? 0
            print("文件大小: \(fileSize) 字节")
            print("文件扩展名: \(url.pathExtension)")
        } catch {
            print("获取文件信息失败: \(error.localizedDescription)")
        }

        // 尝试使用AVAudioPlayer播放
        tryPlayWithAVAudioPlayer(url: url, index: index) { success in
            if !success {
                print("AVAudioPlayer播放失败，尝试使用AVPlayer")
                self.tryPlayWithAVPlayer(url: url, index: index)
            }
        }
    }

    // 尝试使用AVAudioPlayer播放
    private func tryPlayWithAVAudioPlayer(url: URL, index: Int, completion: @escaping (Bool) -> Void) {
        do {
            // 配置音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true)

            // 尝试多种初始化方式
            do {
                // 方法1: 使用Data初始化
                let audioData = try Data(contentsOf: url)
                audioPlayer = try AVAudioPlayer(data: audioData)
                print("使用Data初始化AVAudioPlayer成功")
            } catch {
                print("使用Data初始化失败: \(error.localizedDescription)")

                // 方法2: 直接使用URL初始化
                do {
                    audioPlayer = try AVAudioPlayer(contentsOf: url)
                    print("使用URL初始化AVAudioPlayer成功")
                } catch {
                    print("使用URL初始化失败: \(error.localizedDescription)")
                    completion(false)
                    return
                }
            }

            guard let player = audioPlayer else {
                print("播放失败: 无法创建AVAudioPlayer")
                completion(false)
                return
            }

            // 配置播放器
            player.delegate = PlayerDelegate.shared
            player.volume = 1.0
            player.prepareToPlay()

            // 设置播放完成的回调
            PlayerDelegate.shared.onFinish = { [self] in
                DispatchQueue.main.async {
                    if !self.usingAVPlayer {
                        self.isPlaying = false
                        self.playingRecordingIndex = nil
                    }
                }
            }

            // 开始播放
            let playSuccess = player.play()
            if playSuccess {
                DispatchQueue.main.async {
                    self.usingAVPlayer = false
                    self.isPlaying = true
                    self.playingRecordingIndex = index
                }
                print("AVAudioPlayer开始播放录音")
                completion(true)
            } else {
                print("播放失败: AVAudioPlayer.play()返回false")
                completion(false)
            }
        } catch let error as NSError {
            print("AVAudioPlayer播放失败: \(error.localizedDescription), 错误代码: \(error.code)")
            completion(false)
        }
    }

    // 尝试使用AVPlayer播放
    private func tryPlayWithAVPlayer(url: URL, index: Int) {
        // 停止之前的AVPlayer
        avPlayer?.pause()

        // 创建AVPlayer
        let playerItem = AVPlayerItem(url: url)
        avPlayer = AVPlayer(playerItem: playerItem)

        // 添加播放完成通知
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [self] _ in
            DispatchQueue.main.async {
                if self.usingAVPlayer {
                    self.isPlaying = false
                    self.playingRecordingIndex = nil
                    self.usingAVPlayer = false
                }
            }
        }

        // 开始播放
        avPlayer?.play()

        DispatchQueue.main.async {
            self.usingAVPlayer = true
            self.isPlaying = true
            self.playingRecordingIndex = index
        }

        print("AVPlayer开始播放录音")
    }

    // 停止播放
    private func stopPlayback() {
        // 停止AVAudioPlayer
        if let player = audioPlayer, player.isPlaying {
            player.stop()
        }
        audioPlayer = nil

        // 停止AVPlayer
        avPlayer?.pause()
        avPlayer = nil

        // 移除通知
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemDidPlayToEndTime, object: nil)

        // 重置状态
        usingAVPlayer = false
        isPlaying = false
        playingRecordingIndex = nil
    }

    // 预览图片
    private func previewImage(_ image: UIImage) {
        // 查找图片索引
        if let index = images.firstIndex(where: { $0 === image }) {
            previewingImageIndex = index
        } else {
            // 如果找不到索引，使用第一张图片
            previewingImageIndex = 0
        }

        // 重置加载状态
        isImageLoaded = false

        // 显示预览
        DispatchQueue.main.async {
            self.showingImagePreview = true
        }
    }

    // 获取导航栏标题
    private func getNavigationTitle() -> String {
        if usageRecord.type == .personal {
            if usageRecord.isStory {
                return "物品故事详情"
            } else {
                return "使用记录详情"
            }
        } else {
            return "借出记录详情"
        }
    }
}

// MARK: - 辅助视图

// 详情行视图
struct DetailRow: View {
    let icon: String
    let title: String
    let value: String
    var valueColor: Color = .primary

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 24, height: 24)

            Text(title)
                .font(.subheadline)

            Spacer()

            Text(value)
                .font(.subheadline)
                .foregroundColor(valueColor)
                .fontWeight(.medium)
        }
    }
}

// 注意：PlayerDelegate已在AddUsageRecordView.swift中定义

// MARK: - 增强的图片预览视图
struct EnhancedImagePreviewView: View {
    let images: [UIImage]
    let initialIndex: Int
    @Binding var isLoaded: Bool
    let onDismiss: () -> Void

    @State private var selectedIndex: Int
    @State private var showControls = true

    // 初始化
    init(images: [UIImage], initialIndex: Int, isLoaded: Binding<Bool>, onDismiss: @escaping () -> Void) {
        self.images = images
        self.initialIndex = initialIndex
        self._isLoaded = isLoaded
        self.onDismiss = onDismiss
        self._selectedIndex = State(initialValue: initialIndex)
    }

    var body: some View {
        ZStack {
            // 背景
            Color.black.edgesIgnoringSafeArea(.all)

            // 使用TabView实现滑动画廊
            TabView(selection: $selectedIndex) {
                ForEach(0..<images.count, id: \.self) { index in
                    ZoomableImageView(image: images[index])
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle())
            .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .never))
            .onAppear {
                // 设置为已加载状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    isLoaded = true
                }
            }
            .onTapGesture {
                withAnimation {
                    showControls.toggle()
                }
            }

            // 控制按钮 (仅在showControls为true时显示)
            if showControls {
                // 顶部关闭按钮
                VStack {
                    HStack {
                        // 返回按钮
                        Button(action: onDismiss) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(Circle().fill(Color.black.opacity(0.5)))
                        }
                        .padding()

                        Spacer()
                    }
                    Spacer()
                }

                // 底部页码指示器（如果有多张图片）
                if images.count > 1 {
                    VStack {
                        Spacer()
                        HStack(spacing: 8) {
                            ForEach(0..<images.count, id: \.self) { index in
                                Circle()
                                    .fill(index == selectedIndex ? Color.white : Color.white.opacity(0.4))
                                    .frame(width: 8, height: 8)
                                    .scaleEffect(index == selectedIndex ? 1.2 : 1.0)
                                    .animation(.spring(), value: selectedIndex)
                            }
                        }
                        .padding(8)
                        .background(Capsule().fill(Color.black.opacity(0.5)))
                        .padding(.bottom)
                    }
                }
            }
        }
    }
}

// MARK: - 可缩放图片视图
struct ZoomableImageView: View {
    let image: UIImage

    @State private var currentScale: CGFloat = 1.0
    @State private var finalScale: CGFloat = 1.0
    @State private var currentOffset: CGSize = .zero
    @State private var finalOffset: CGSize = .zero
    @State private var isImageReady = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 加载指示器
                if !isImageReady {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                }

                // 图片
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(currentScale)
                    .offset(currentOffset)
                    .opacity(isImageReady ? 1.0 : 0.0)
                    .onAppear {
                        // 使用短暂延迟确保视图完全加载
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            withAnimation(.easeIn(duration: 0.2)) {
                                isImageReady = true
                            }
                        }
                    }
                    .onTapGesture(count: 2) {
                        // 双击缩放/重置
                        if currentScale > 1.0 {
                            withAnimation(.spring()) {
                                currentScale = 1.0
                                finalScale = 1.0
                                currentOffset = .zero
                                finalOffset = .zero
                            }
                        } else {
                            withAnimation(.spring()) {
                                currentScale = 2.0
                                finalScale = 2.0
                            }
                        }
                    }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .edgesIgnoringSafeArea(.all)
    }
}

// MARK: - 编辑使用记录视图
struct EditUsageRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let record: UsageRecord
    let product: Product

    @State private var date: Date
    @State private var scenario: String
    @State private var satisfaction: Int16
    @State private var notes: String
    @State private var duration: Double
    @State private var wearCondition: String

    // 借出相关状态
    @State private var borrowerName: String
    @State private var contactInfo: String
    @State private var dueDate: Date

    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 场景选项
    private let scenarios = ["日常使用", "工作", "学习", "旅行", "聚会", "运动", "其他"]

    // 初始化时加载记录的当前数据
    init(record: UsageRecord, product: Product) {
        self.record = record
        self.product = product

        _date = State(initialValue: record.date ?? Date())
        _scenario = State(initialValue: record.scenario ?? "")
        _satisfaction = State(initialValue: record.satisfaction)
        _notes = State(initialValue: record.notes ?? "")
        _duration = State(initialValue: record.duration ?? 0)
        _wearCondition = State(initialValue: record.wearCondition ?? "")

        // 借出相关数据
        _borrowerName = State(initialValue: record.borrowerName ?? "")
        _contactInfo = State(initialValue: record.contactInfo ?? "")
        _dueDate = State(initialValue: record.dueDate ?? Date().addingTimeInterval(60*60*24*7))
    }

    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section(header: Text("基本信息")) {
                    DatePicker("日期", selection: $date, displayedComponents: [.date, .hourAndMinute])

                    if record.type == .personal {
                        Picker("使用场景", selection: $scenario) {
                            Text("请选择").tag("")
                            ForEach(scenarios, id: \.self) { scenario in
                                Text(scenario).tag(scenario)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())

                        HStack {
                            Text("使用时长")
                            Spacer()
                            Text("\(Int(duration))小时\(Int((duration - Double(Int(duration))) * 60))分钟")
                                .foregroundColor(.secondary)
                        }
                    }
                }

                // 借出信息（仅当记录类型为借出时显示）
                if record.type == .loaned {
                    Section(header: Text("借出信息")) {
                        TextField("借出对象姓名", text: $borrowerName)
                        TextField("联系方式", text: $contactInfo)
                            .keyboardType(.phonePad)
                        DatePicker("预计归还日期", selection: $dueDate, displayedComponents: .date)
                    }
                }

                // 满意度（仅当记录类型为个人使用时显示）
                if record.type == .personal {
                    Section(header: Text("满意度评价")) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("使用满意度")
                                .font(.subheadline)
                                .foregroundColor(.secondary)

                            HStack {
                                ForEach(1...5, id: \.self) { index in
                                    Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                        .foregroundColor(index <= satisfaction ? .yellow : .gray)
                                        .font(.title2)
                                        .onTapGesture {
                                            satisfaction = Int16(index)
                                        }
                                }
                            }
                            .frame(maxWidth: .infinity)
                        }
                    }
                }

                // 备注
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }

                // 保存按钮
                Section {
                    Button(action: updateUsageRecord) {
                        Text("保存更新")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                    }
                    .disabled(!isFormValid)
                }
            }
            .navigationTitle(record.type == .personal ? "编辑使用记录" : "编辑借出记录")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
        }
    }

    // 表单验证
    private var isFormValid: Bool {
        if record.type == .personal {
            return true // 个人使用记录没有必填字段
        } else {
            return !borrowerName.isEmpty // 借出记录必须填写借出对象
        }
    }

    // 更新使用记录
    private func updateUsageRecord() {
        usageViewModel.setCurrentProduct(product)

        if record.type == .personal {
            updatePersonalUsageRecord()
        } else {
            updateLoanRecord()
        }
    }

    // 更新个人使用记录
    private func updatePersonalUsageRecord() {
        // 更新记录
        let success = usageViewModel.updateUsageRecord(
            record: record,
            date: date,
            satisfaction: satisfaction,
            notes: notes.isEmpty ? nil : notes,
            duration: duration > 0 ? duration : nil,
            scenario: scenario.isEmpty ? nil : scenario,
            wearCondition: wearCondition.isEmpty ? nil : wearCondition
        )

        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "更新使用记录失败"
            showingAlert = true
        }
    }

    // 更新借出记录
    private func updateLoanRecord() {
        // 更新记录
        let success = usageViewModel.updateLoanRecord(
            record: record,
            date: date,
            borrowerName: borrowerName,
            contactInfo: contactInfo.isEmpty ? nil : contactInfo,
            dueDate: dueDate,
            notes: notes.isEmpty ? nil : notes
        )

        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "更新借出记录失败"
            showingAlert = true
        }
    }
}

// MARK: - 故事详情专用组件
extension UsageRecordDetailView {
    
    // MARK: - 纯净沉浸式背景
    private var storyDreamlikeBackground: some View {
        ZStack {
            // 极简渐变背景
            LinearGradient(
                colors: [
                    Color(.systemBackground),
                    emotionalBackgroundColor.opacity(0.05),
                    Color(.systemBackground)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            
            // 单一情感光晕 - 更subtle
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            emotionalColor.opacity(0.06),
                            emotionalColor.opacity(0.02),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 100,
                        endRadius: 300
                    )
                )
                .frame(width: 400, height: 400)
                .offset(y: -150)
                .scaleEffect(animateHeart ? 1.05 : 1.0)
                .animation(
                    .easeInOut(duration: 8.0)
                    .repeatForever(autoreverses: true),
                    value: animateHeart
                )
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 2.0)) {
                animateHeart = true
            }
        }
    }
    
    // MARK: - 浮动导航栏
    private var storyFloatingHeader: some View {
        HStack {
            // 返回按钮
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                    )
            }
            
            Spacer()
            
            // 日期指示器
            HStack(spacing: 6) {
                Image(systemName: "calendar.badge.clock")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(emotionalColor)
                
                Text(formattedStoryDate)
                    .font(.system(size: 12, weight: .semibold, design: .rounded))
                    .foregroundColor(.primary)
                
                Text("·")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                
                Text(relativeTimeText)
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
            }
            .lineLimit(1)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(.ultraThinMaterial)
                    .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 3)
            )
            .frame(maxWidth: 200)
            
            Spacer()
            
            // 更多操作按钮
            Menu {
                Button(action: {
                    showingEditSheet = true
                }) {
                    Label("编辑故事", systemImage: "pencil")
                }
                
                Button(action: {
                    // 分享功能
                }) {
                    Label("分享故事", systemImage: "square.and.arrow.up")
                }
                
                Button(action: {
                    toggleFavorite()
                }) {
                    Label(isFavorited ? "取消收藏" : "收藏故事", 
                          systemImage: isFavorited ? "heart.fill" : "heart")
                }
                
                Divider()
                
                Button(role: .destructive, action: {
                    showingDeleteAlert = true
                }) {
                    Label("删除故事", systemImage: "trash")
                }
            } label: {
                Image(systemName: "ellipsis")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 50)
    }
    
    // MARK: - 英雄标题区域
    private var storyHeroSection: some View {
        VStack(spacing: 24) {
            // 主标题
            VStack(spacing: 12) {
                Text(storyTitle)
                    .font(.system(size: 28, weight: .black, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [themeManager.currentTheme.primaryColor, emotionalColor],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                
                // 情感价值展示
                emotionalValueDisplay
            }
            
            // 装饰分隔线
            HStack {
                ForEach(0..<5, id: \.self) { index in
                    Circle()
                        .fill(emotionalColor.opacity(0.6))
                        .frame(width: 6, height: 6)
                        .scaleEffect(animateHeart ? 1.0 : 0.5)
                        .animation(
                            .spring(response: 0.6, dampingFraction: 0.8)
                            .delay(Double(index) * 0.1),
                            value: animateHeart
                        )
                }
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 故事内容核心区域
    private var storyMainContentSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // 内容卡片
            storyContentCard
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 增强故事内容卡片
    private var storyContentCard: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 精致的引言标记
            HStack {
                VStack(spacing: 4) {
                    Circle()
                        .fill(emotionalColor.opacity(0.8))
                        .frame(width: 4, height: 4)
                    
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    emotionalColor.opacity(0.6),
                                    emotionalColor.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(width: 2, height: 40)
                        .clipShape(RoundedRectangle(cornerRadius: 1))
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    // 情感标签
                    HStack(spacing: 8) {
                        Text(emotionalLevelText)
                            .font(.system(size: 13, weight: .semibold, design: .rounded))
                            .foregroundColor(emotionalColor)
                            .padding(.horizontal, 10)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(emotionalColor.opacity(0.12))
                            )
                        
                        Spacer()
                        
                        // 微光装饰
                        Image(systemName: "sparkles")
                            .font(.system(size: 12, weight: .light))
                            .foregroundColor(emotionalColor.opacity(0.6))
                    }
                    
                    // 增强的回忆内容
                    VStack(alignment: .leading, spacing: 16) {
                        if let notes = usageRecord.notes, !notes.isEmpty {
                            Text(notes)
                                .font(.system(size: 17, weight: .regular, design: .rounded))
                                .lineSpacing(7)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        
                        if let memories = usageRecord.memories, !memories.isEmpty {
                            Text(memories)
                                .font(.system(size: 17, weight: .regular, design: .rounded))
                                .lineSpacing(7)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        
                        // 时间和阅读信息
                        HStack {
                            Label(formattedStoryDate, systemImage: "calendar")
                                .font(.system(size: 12, weight: .medium, design: .rounded))
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(storyContentLength) 字")
                                .font(.system(size: 12, weight: .medium, design: .rounded))
                                .foregroundColor(Color.secondary.opacity(0.7))
                        }
                    }
                }
                .padding(.leading, 12)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 24)
        }
        .background(storyCardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 28))
        .shadow(color: emotionalColor.opacity(0.12), radius: 16, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 28)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            Color.white.opacity(0.2),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
    }
    
    // MARK: - 故事卡片背景
    private var storyCardBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
            
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            emotionalColor.opacity(0.08),
                            emotionalColor.opacity(0.03),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }
    
    // MARK: - 多媒体回忆区域
    private var storyMultimediaSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 区域标题
            HStack {
                Image(systemName: "photo.stack")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                
                Text("珍贵回忆")
                    .font(.system(size: 22, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            
            // 图片展示
            if !images.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(Array(images.enumerated()), id: \.offset) { index, image in
                            StoryImageCell(
                                image: image,
                                index: index,
                                onTap: { selectedIndex in
                                    previewImage(image)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            
            // 音频展示
            if !audioRecordings.isEmpty {
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "waveform")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                        Text("语音回忆")
                            .font(.system(size: 18, weight: .semibold, design: .rounded))
                            .foregroundColor(.primary)
                        Spacer()
                        Text("\(audioRecordings.count)段")
                            .font(.system(size: 14, weight: .medium, design: .rounded))
                            .foregroundColor(.secondary)
                    }
                    
                    ForEach(0..<audioRecordings.count, id: \.self) { index in
                        Button(action: {
                            togglePlayRecording(at: index)
                        }) {
                            HStack {
                                Image(systemName: (playingRecordingIndex == index && isPlaying) ? "pause.circle.fill" : "play.circle.fill")
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                                    .font(.title2)
                                
                                Text("语音记录 \(index + 1)")
                                    .foregroundColor(.primary)
                                    .font(.system(size: 16, weight: .medium, design: .rounded))
                                
                                Spacer()
                                
                                Text(formatDuration(audioRecordings[index]))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                            .background(.ultraThinMaterial)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - 产品关联区域
    private var storyProductSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 产品情感关联卡片
            EmotionalProductCard(product: product, currentMemory: usageRecord)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - 回忆探索区域
    private var storyMemoryExplorationSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 相关回忆
            if let relatedMemories = getRelatedMemories(), !relatedMemories.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "memories")
                            .foregroundColor(.secondary)
                            .font(.system(size: 14))
                        
                        Text("相关回忆")
                            .font(.system(size: 15, weight: .medium))
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(relatedMemories.prefix(5), id: \.objectID) { memory in
                                NavigationLink(destination: UsageRecordDetailView(
                                    usageRecord: memory,
                                    product: memory.product ?? product,
                                    sourceType: .memoirs
                                )) {
                                    RelatedMemoryCard(memory: memory)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                }
            }
            
            // 时间导航
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "clock.arrow.circlepath")
                        .foregroundColor(.secondary)
                        .font(.system(size: 14))
                    
                    Text("时间线")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                
                // 完整的时间导航视图
                HStack(spacing: 16) {
                    // 上一个回忆
                    TimeNavButton(direction: "previous", currentMemory: usageRecord)
                    
                    Spacer()
                    
                    // 当前时间点
                    VStack(spacing: 4) {
                        Circle()
                            .fill(.primary)
                            .frame(width: 8, height: 8)
                        
                        Text("当前")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 下一个回忆
                    TimeNavButton(direction: "next", currentMemory: usageRecord)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.thinMaterial, in: RoundedRectangle(cornerRadius: 16))
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - 浮动操作栏
    private var storyFloatingActionBar: some View {
        HStack {
            Spacer()
            // 编辑按钮已移除
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 计算属性和辅助方法
    
    private var emotionalColor: Color {
        switch usageRecord.emotionalValue {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
    
    private var emotionalBackgroundColor: Color {
        emotionalColor.opacity(0.1)
    }
    
    private var storyTitle: String {
        if let title = usageRecord.title, !title.isEmpty {
            return title
        } else {
            return "珍贵的回忆"
        }
    }
    
    private var formattedStoryDate: String {
        guard let date = usageRecord.date else { return "未知日期" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
    }
    
    private var relativeTimeText: String {
        guard let date = usageRecord.date else { return "很久以前" }
        let calendar = Calendar.current
        let now = Date()
        
        if calendar.isDateInToday(date) {
            return "今天"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                if days < 7 {
                    return "\(days)天前"
                } else if days < 30 {
                    return "\(days / 7)周前"
                } else if days < 365 {
                    return "\(days / 30)个月前"
                } else {
                    return "\(days / 365)年前"
                }
            }
        }
        return "很久以前"
    }
    
    private var emotionalLevelText: String {
        switch usageRecord.emotionalValue {
        case 5: return "深深感动"
        case 4: return "非常满意"
        case 3: return "颇有感触"
        case 2: return "略有收获"
        default: return "平淡无奇"
        }
    }
    
    private var storyContentLength: Int {
        let notesLength = usageRecord.notes?.count ?? 0
        let memoriesLength = usageRecord.memories?.count ?? 0
        return notesLength + memoriesLength
    }
    
    // 情感价值展示
    private var emotionalValueDisplay: some View {
        HStack(spacing: 12) {
            // 主要情感宝石
            ZStack {
                // 外圈光环
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                emotionalColor.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 15,
                            endRadius: 30
                        )
                    )
                    .frame(width: 50, height: 50)
                    .scaleEffect(animateHeart ? 1.2 : 1.0)
                    .opacity(animateHeart ? 0.6 : 1.0)
                    .animation(
                        .easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true),
                        value: animateHeart
                    )
                
                EmotionalGem(emotionalValue: usageRecord.emotionalValue, size: .medium)
            }
            
            // 情感描述
            VStack(alignment: .leading, spacing: 4) {
                Text(emotionalLevelText)
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(emotionalColor)
                
                Text(emotionalDescription)
                    .font(.system(size: 13, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 4)
    }
    
    private var emotionalDescription: String {
        switch usageRecord.emotionalValue {
        case 5: return "这段回忆让我深深感动，是人生中的珍贵时刻"
        case 4: return "这是一段很棒的回忆，给我带来了很多快乐"
        case 3: return "还不错的经历，有一些值得回味的地方"
        case 2: return "普通的回忆，但也有它的意义所在"
        default: return "平常的经历，是生活的一部分"
        }
    }
    
    private func formatDuration(_ url: URL) -> String {
        // 简单的文件名显示，实际项目中可以获取真实时长
        return url.lastPathComponent
    }
    
    // MARK: - 回忆探索辅助方法
    
    // 更新区域位置数据
    private func updateSectionPositions(_ positions: [SectionPositionData]) {
        for positionData in positions {
            sectionPositions[positionData.id] = positionData.frame.minY
            sectionHeights[positionData.id] = positionData.frame.height
        }
        
        // 计算总内容高度
        if let lastSection = sectionPositions.max(by: { $0.value < $1.value }) {
            let lastSectionBottom = lastSection.value + (sectionHeights[lastSection.key] ?? 0)
            totalContentHeight = lastSectionBottom
        }
        
        if isInitialLoad {
            print("📏 区域位置初始化完成:")
            for (id, position) in sectionPositions.sorted(by: { $0.value < $1.value }) {
                print("  \(id): top=\(Int(position)), height=\(Int(sectionHeights[id] ?? 0))")
            }
            print("  总内容高度: \(Int(totalContentHeight))")
            print("  屏幕高度: \(Int(screenHeight))")
            
            // 计算并设置初始进度
            calculateInitialProgress()
            isInitialLoad = false
        } else {
            // 非初始加载时，正常更新滚动进度
            updateScrollProgress()
        }
    }
    
    // 基于屏幕可见内容的进度计算
    private func updateScrollProgress() {
        guard totalContentHeight > 0, screenHeight > 0 else { return }
        
        // 计算可见内容的范围
        let visibleTop = currentScrollOffset + 100 // 考虑头部偏移
        let visibleBottom = visibleTop + screenHeight - 200 // 减去头部和底部UI占用空间
        
        // 计算已阅读的内容高度
        let readHeight = max(0, min(visibleBottom, totalContentHeight))
        
        // 计算进度百分比
        let progress = readHeight / totalContentHeight
        
        // 限制进度范围
        let finalProgress = max(0, min(1.0, progress))
        
        // 只在进度有明显变化时更新
        if abs(finalProgress - scrollProgress) > 0.01 {
            withAnimation(.easeOut(duration: 0.2)) {
                scrollProgress = finalProgress
            }
            
            let percentage = Int(finalProgress * 100)
            print("📖 阅读进度: \(percentage)% (已读内容: \(Int(readHeight))/\(Int(totalContentHeight)))")
            print("   可见范围: \(Int(visibleTop)) ~ \(Int(visibleBottom))")
        }
    }
    
    // 计算初始进度（第一屏内容占总内容的比例）
    private func calculateInitialProgress() {
        guard totalContentHeight > 0, screenHeight > 0 else { return }
        
        // 第一屏可见内容高度（从顶部开始，减去头部占用）
        let firstScreenContentHeight = screenHeight - 200 // 减去头部和底部UI空间
        
        // 初始进度 = 第一屏内容高度 / 总内容高度
        let initialProgress = min(firstScreenContentHeight / totalContentHeight, 1.0)
        
        withAnimation(.easeOut(duration: 0.5)) {
            scrollProgress = initialProgress
        }
        
        let percentage = Int(initialProgress * 100)
        print("🎯 初始进度设定: \(percentage)% (第一屏: \(Int(firstScreenContentHeight))/\(Int(totalContentHeight)))")
    }
    
    private func getRelatedMemories() -> [UsageRecord]? {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        
        // 查找同一产品的其他回忆
        request.predicate = NSPredicate(format: "product == %@ AND isStory == true AND SELF != %@", product, usageRecord)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: false)]
        request.fetchLimit = 5
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取相关回忆失败: \(error)")
            return nil
        }
    }
    
    // MARK: - 阅读体验优化方法
    
    private func navigateToPreviousMemory() {
        // 获取上一个回忆并导航
        if let previousMemory = getPreviousMemory() {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 这里应该触发导航到上一个回忆
            // 在实际实现中，可以通过NavigationPath或其他导航机制实现
            print("导航到上一个回忆: \(previousMemory.title ?? "")")
        }
    }
    
    private func navigateToNextMemory() {
        // 获取下一个回忆并导航
        if let nextMemory = getNextMemory() {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 这里应该触发导航到下一个回忆
            print("导航到下一个回忆: \(nextMemory.title ?? "")")
        }
    }
    
    private func getPreviousMemory() -> UsageRecord? {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        request.predicate = NSPredicate(format: "product == %@ AND isStory == true AND date < %@", 
                                       product as CVarArg, usageRecord.date as CVarArg? ?? Date() as CVarArg)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: false)]
        request.fetchLimit = 1
        
        do {
            let memories = try viewContext.fetch(request)
            return memories.first
        } catch {
            return nil
        }
    }
    
    private func getNextMemory() -> UsageRecord? {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        request.predicate = NSPredicate(format: "product == %@ AND isStory == true AND date > %@", 
                                       product as CVarArg, usageRecord.date as CVarArg? ?? Date() as CVarArg)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: true)]
        request.fetchLimit = 1
        
        do {
            let memories = try viewContext.fetch(request)
            return memories.first
        } catch {
            return nil
        }
    }
    
    // MARK: - 菜单功能实现
    
    /// 切换收藏状态
    private func toggleFavorite() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            animateHeart.toggle()
            isFavorited.toggle()
            
            // 使用notes字段临时标记收藏状态（因为模型中可能没有isFavorited字段）
            let currentNotes = usageRecord.notes ?? ""
            if isFavorited {
                if !currentNotes.contains("[收藏]") {
                    usageRecord.notes = "[收藏] " + currentNotes
                }
            } else {
                usageRecord.notes = currentNotes.replacingOccurrences(of: "[收藏] ", with: "")
            }
            
            do {
                try viewContext.save()
                print("收藏状态已更新: \(isFavorited ? "已收藏" : "已取消收藏")")
            } catch {
                print("保存收藏状态失败: \(error)")
                // 回滚状态
                isFavorited.toggle()
            }
        }
    }
    
    /// 删除故事
    private func deleteStory() {
        // 先删除相关的多媒体文件
        deleteMultimediaFiles()
        
        // 从CoreData中删除记录
        viewContext.delete(usageRecord)
        
        do {
            try viewContext.save()
            print("故事已删除")
            
            // 返回上一页
            DispatchQueue.main.async {
                presentationMode.wrappedValue.dismiss()
            }
        } catch {
            print("删除故事失败: \(error)")
        }
    }
    
    /// 删除多媒体文件
    private func deleteMultimediaFiles() {
        // 删除图片文件
        if let imageData = usageRecord.images {
            do {
                if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                    // 这里可以添加删除本地缓存图片的逻辑
                    print("已清理 \(imageDatas.count) 个图片文件")
                }
            } catch {
                print("清理图片文件时出错: \(error)")
            }
        }
        
        // 删除音频文件
        if let audioData = usageRecord.audioRecordings {
            do {
                if let audioURLs = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [URL] {
                    for url in audioURLs {
                        try? FileManager.default.removeItem(at: url)
                    }
                    print("已清理 \(audioURLs.count) 个音频文件")
                }
            } catch {
                print("清理音频文件时出错: \(error)")
            }
        }
    }
}

// MARK: - 支持组件

// 情感宝石组件
struct EmotionalGem: View {
    let emotionalValue: Int16
    let size: GemSize
    
    enum GemSize {
        case small, medium, large
        
        var dimensions: CGFloat {
            switch self {
            case .small: return 24
            case .medium: return 32
            case .large: return 40
            }
        }
        
        var innerSize: CGFloat {
            dimensions * 0.75
        }
    }
    
    @State private var isGlowing: Bool = false
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        ZStack {
            // 外圈光环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [
                            gemColor.opacity(0.8),
                            gemColor.opacity(0.4),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
                .frame(width: size.dimensions, height: size.dimensions)
                .rotationEffect(.degrees(rotationAngle))
            
            // 主体宝石
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            gemColor.opacity(0.9),
                            gemColor.opacity(0.7),
                            gemColor.opacity(0.8)
                        ],
                        center: .center,
                        startRadius: 2,
                        endRadius: size.innerSize / 2
                    )
                )
                .frame(width: size.innerSize, height: size.innerSize)
                .scaleEffect(isGlowing ? 1.1 : 1.0)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.4), lineWidth: 1)
                )
                .shadow(color: gemColor.opacity(0.6), radius: 4, x: 0, y: 2)
        }
        .onAppear {
            withAnimation(.linear(duration: 4).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
            
            if emotionalValue >= 4 {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    isGlowing = true
                }
            }
        }
    }
    
    private var gemColor: Color {
        switch emotionalValue {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
}

// 情感化产品关联卡片
struct EmotionalProductCard: View {
    let product: Product
    let currentMemory: UsageRecord
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.managedObjectContext) var viewContext
    
    var body: some View {
        NavigationLink(destination: ProductDetailView(product: product)) {
            VStack(alignment: .leading, spacing: 16) {
                // 情感连接标题
                HStack {
                    Image(systemName: "heart.circle")
                        .foregroundColor(emotionalConnectionColor)
                        .font(.system(size: 16))
                    
                    Text("情感纽带")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    // 添加导航指示器
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 产品与回忆的情感关联
                HStack(spacing: 16) {
                // 产品头像
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    emotionalConnectionColor.opacity(0.2),
                                    emotionalConnectionColor.opacity(0.05)
                                ],
                                center: .center,
                                startRadius: 20,
                                endRadius: 35
                            )
                        )
                        .frame(width: 60, height: 60)
                    
                    // 产品图标或首字母
                    if let name = product.name, !name.isEmpty {
                        Text(String(name.prefix(1)))
                            .font(.system(size: 20, weight: .bold, design: .rounded))
                            .foregroundColor(emotionalConnectionColor)
                    } else {
                        Image(systemName: "cube.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(emotionalConnectionColor)
                    }
                }
                
                // 情感关系描述
                VStack(alignment: .leading, spacing: 8) {
                    // 产品名称和关系描述
                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.name ?? "珍贵物品")
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Text(emotionalRelationshipText)
                            .font(.system(size: 13, weight: .medium, design: .rounded))
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    // 回忆统计
                    HStack(spacing: 16) {
                        // 回忆数量
                        HStack(spacing: 4) {
                            Image(systemName: "memories")
                                .font(.caption)
                                .foregroundColor(emotionalConnectionColor)
                            
                            Text("\(memoryCount)段")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }
                        
                        // 情感价值
                        HStack(spacing: 4) {
                            Image(systemName: "heart.fill")
                                .font(.caption)
                                .foregroundColor(emotionalConnectionColor)
                            
                            Text(averageEmotionalText)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(.thinMaterial, in: RoundedRectangle(cornerRadius: 16))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 计算属性
    
    private var emotionalConnectionColor: Color {
        switch averageEmotionalValue {
        case 4...5: return .pink
        case 3..<4: return .orange
        case 2..<3: return .blue
        default: return .gray
        }
    }
    
    private var memoryCount: Int {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        request.predicate = NSPredicate(format: "product == %@ AND isStory == true", product)
        
        do {
            return try viewContext.count(for: request)
        } catch {
            return 0
        }
    }
    
    private var averageEmotionalValue: Double {
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        request.predicate = NSPredicate(format: "product == %@ AND isStory == true", product)
        
        do {
            let memories = try viewContext.fetch(request)
            if memories.isEmpty { return 0 }
            
            let total = memories.reduce(0) { $0 + Double($1.emotionalValue) }
            return total / Double(memories.count)
        } catch {
            return 0
        }
    }
    
    private var emotionalRelationshipText: String {
        let avgEmotion = averageEmotionalValue
        let count = memoryCount
        
        if count == 0 {
            return "等待与它创造第一段回忆"
        } else if count == 1 {
            return "已经有一段珍贵的回忆"
        } else if avgEmotion >= 4 {
            return "承载着\(count)段深刻的美好时光"
        } else if avgEmotion >= 3 {
            return "陪伴了\(count)段温暖的生活片段"
        } else {
            return "记录了\(count)段平凡而真实的时刻"
        }
    }
    
    private var averageEmotionalText: String {
        let avg = averageEmotionalValue
        if avg == 0 {
            return "待续"
        } else {
            return String(format: "%.1f", avg)
        }
    }
}

// 故事图片单元格
struct StoryImageCell: View {
    let image: UIImage
    let index: Int
    let onTap: (Int) -> Void
    
    @State private var isLoaded: Bool = false
    
    var body: some View {
        Button(action: {
            onTap(index)
        }) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 160, height: 200)
                
                // 实际图片
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 160, height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .opacity(isLoaded ? 1.0 : 0.0)
                
                // 加载状态
                if !isLoaded {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            withAnimation(.easeIn(duration: 0.3)) {
                isLoaded = true
            }
        }
    }
}

// 相关回忆卡片
struct RelatedMemoryCard: View {
    let memory: UsageRecord
    
    var body: some View {
        VStack(spacing: 8) {
            // 时间标签
            Text(relativeDate(memory.date))
                .font(.caption2)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(.thinMaterial, in: Capsule())
            
            // 回忆标题
            Text(memory.title ?? "珍贵回忆")
                .font(.caption)
                .fontWeight(.medium)
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .frame(width: 80)
        }
        .padding(8)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        .frame(width: 96, height: 80)
    }
    
    private func relativeDate(_ date: Date?) -> String {
        guard let date = date else { return "未知" }
        let calendar = Calendar.current
        let now = Date()
        
        if calendar.isDateInToday(date) {
            return "今天"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                if days < 7 {
                    return "\(days)天前"
                } else if days < 30 {
                    return "\(days / 7)周前"
                } else {
                    return "\(days / 30)月前"
                }
            }
        }
        return "很久前"
    }
}

// 时间导航功能 - 支持在同一产品的不同回忆之间切换

// 阅读进度指示器
struct ReadingProgressIndicator: View {
    let progress: CGFloat
    
    var body: some View {
        VStack(spacing: 6) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Capsule()
                        .fill(.ultraThinMaterial)
                        .frame(height: 2)
                    
                    // 进度
                    Capsule()
                        .fill(
                            LinearGradient(
                                colors: [.pink.opacity(0.8), .orange.opacity(0.6)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * progress, height: 2)
                        .animation(.smooth(duration: 0.2), value: progress)
                }
            }
            .frame(height: 2)
            
            // 阅读时间估计
            HStack {
                Text("阅读进度")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(readingProgressText)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var readingProgressText: String {
        let percentage = Int(progress * 100)
        return "\(percentage)%"
    }
    
    private var readingTimeText: String {
        // 简化的阅读时间估算
        let estimatedMinutes = Int(progress * 5) + 1 // 1-5分钟
        return "\(estimatedMinutes)分钟"
    }
}

// 时间导航按钮
struct TimeNavButton: View {
    let direction: String // "previous" 或 "next"
    let currentMemory: UsageRecord
    @Environment(\.managedObjectContext) private var viewContext
    @State private var targetMemory: UsageRecord?
    
    var body: some View {
        Group {
            if let targetMemory = targetMemory {
                NavigationLink(destination: UsageRecordDetailView(
                    usageRecord: targetMemory,
                    product: targetMemory.product ?? currentMemory.product!,
                    sourceType: .memoirs
                )) {
                    buttonContent
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                buttonContent
                    .opacity(0.5)
            }
        }
        .onAppear {
            loadTargetMemory()
        }
    }
    
    private var buttonContent: some View {
        VStack(spacing: 4) {
            Image(systemName: direction == "previous" ? "chevron.left" : "chevron.right")
                .font(.caption)
                .foregroundColor(targetMemory != nil ? .primary : .secondary)
            
            if let targetMemory = targetMemory {
                Text(formatDate(targetMemory.date))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            } else {
                Text("无")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func loadTargetMemory() {
        guard let currentDate = currentMemory.date,
              let product = currentMemory.product else { return }
        
        let request: NSFetchRequest<UsageRecord> = UsageRecord.fetchRequest()
        
        if direction == "previous" {
            request.predicate = NSPredicate(format: "product == %@ AND isStory == true AND date < %@ AND SELF != %@", 
                                          product, currentDate as NSDate, currentMemory)
            request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: false)]
        } else {
            request.predicate = NSPredicate(format: "product == %@ AND isStory == true AND date > %@ AND SELF != %@", 
                                          product, currentDate as NSDate, currentMemory)
            request.sortDescriptors = [NSSortDescriptor(keyPath: \UsageRecord.date, ascending: true)]
        }
        
        request.fetchLimit = 1
        
        do {
            let memories = try viewContext.fetch(request)
            targetMemory = memories.first
        } catch {
            print("查找回忆失败: \(error)")
            targetMemory = nil
        }
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "无" }
        let formatter = DateFormatter()
        formatter.dateFormat = "M/d"
        return formatter.string(from: date)
    }
}

// 交互按钮样式
struct InteractiveButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 动态进度计算相关
// 内容区域位置和高度数据
struct SectionPositionData: Equatable {
    let id: String
    let frame: CGRect
}

struct SectionPositionPreferenceKey: PreferenceKey {
    static var defaultValue: [SectionPositionData] = []
    static func reduce(value: inout [SectionPositionData], nextValue: () -> [SectionPositionData]) {
        value.append(contentsOf: nextValue())
    }
}