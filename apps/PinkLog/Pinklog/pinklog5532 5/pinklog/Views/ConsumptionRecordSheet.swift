//
//  ConsumptionRecordSheet.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI

struct ConsumptionRecordSheet: View {
    let product: Product
    let viewModel: ConsumableViewModel
    
    @Environment(\.dismiss) private var dismiss
    @State private var consumedQuantity: String = ""
    @State private var selectedUnit: UsageRecord.ConsumptionUnit = .piece
    @State private var satisfaction: Double = 5
    @State private var notes: String = ""
    @State private var scenario: String = ""
    @State private var isSubmitting = false
    @State private var errorMessage: String?
    
    // 预设场景
    private let commonScenarios = ["日常使用", "紧急使用", "外出使用", "睡前使用", "餐后使用", "运动后使用"]
    
    var body: some View {
        NavigationView {
            Form {
                // 产品信息
                productInfoSection
                
                // 消耗量输入
                consumptionInputSection
                
                // 使用体验
                experienceSection
                
                // 场景和备注
                detailsSection
                
                // 预览信息
                previewSection
            }
            .navigationTitle("记录使用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        submitConsumption()
                    }
                    .disabled(isSubmitting || consumedQuantity.isEmpty)
                }
            }
            .alert("错误", isPresented: .constant(errorMessage != nil)) {
                Button("确定") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
        .onAppear {
            setupInitialValues()
        }
    }
    
    // MARK: - 产品信息区域
    
    private var productInfoSection: some View {
        Section {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未知产品")
                        .font(.headline)
                    
                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack {
                        Image(systemName: product.stockStatus.icon)
                            .foregroundColor(product.stockStatus.color)
                        Text("剩余")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("\(formatQuantity(product.actualCurrentQuantity))\(product.unitType ?? "")")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
            .padding(.vertical, 4)
        } header: {
            Text("产品信息")
        }
    }
    
    // MARK: - 消耗量输入区域
    
    private var consumptionInputSection: some View {
        Section {
            // 消耗量输入
            HStack {
                Text("消耗量")
                Spacer()
                TextField("输入消耗量", text: $consumedQuantity)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 100)
                
                Picker("单位", selection: $selectedUnit) {
                    ForEach(UsageRecord.ConsumptionUnit.allCases) { unit in
                        Text(unit.symbol).tag(unit)
                    }
                }
                .pickerStyle(.menu)
            }
            
            // 智能推荐
            if let recommended = viewModel.getRecommendedConsumption(for: product) {
                HStack {
                    Text("推荐用量")
                        .foregroundColor(.secondary)
                    Spacer()
                    Button("\(formatQuantity(recommended))\(selectedUnit.symbol)") {
                        consumedQuantity = formatQuantity(recommended)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
            
            // 快速选择按钮
            quickSelectionButtons
            
        } header: {
            Text("使用量")
        } footer: {
            if let quantity = Double(consumedQuantity), quantity > 0 {
                let remaining = product.actualCurrentQuantity - quantity
                if remaining < 0 {
                    Text("⚠️ 消耗量超过当前库存")
                        .foregroundColor(.red)
                } else {
                    Text("使用后剩余: \(formatQuantity(remaining))\(selectedUnit.symbol)")
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var quickSelectionButtons: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("快速选择")
                .font(.caption)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                ForEach([0.5, 1, 2, 5], id: \.self) { amount in
                    Button("\(formatQuantity(amount))") {
                        consumedQuantity = formatQuantity(amount)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
        }
    }
    
    // MARK: - 使用体验区域
    
    private var experienceSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("满意度")
                    Spacer()
                    Text("\(Int(satisfaction))/10")
                        .foregroundColor(.secondary)
                }
                
                HStack(spacing: 4) {
                    ForEach(1...10, id: \.self) { rating in
                        Button {
                            satisfaction = Double(rating)
                        } label: {
                            Image(systemName: rating <= Int(satisfaction) ? "star.fill" : "star")
                                .foregroundColor(rating <= Int(satisfaction) ? .yellow : .gray)
                                .font(.title3)
                        }
                    }
                }
            }
        } header: {
            Text("使用体验")
        }
    }
    
    // MARK: - 详细信息区域
    
    private var detailsSection: some View {
        Section {
            // 使用场景
            VStack(alignment: .leading, spacing: 8) {
                TextField("使用场景（可选）", text: $scenario)
                    .textFieldStyle(.roundedBorder)
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(commonScenarios, id: \.self) { commonScenario in
                            Button(commonScenario) {
                                scenario = commonScenario
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.small)
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
            
            // 备注
            TextField("备注（可选）", text: $notes, axis: .vertical)
                .textFieldStyle(.roundedBorder)
                .lineLimit(3...6)
                
        } header: {
            Text("详细信息")
        }
    }
    
    // MARK: - 预览区域
    
    private var previewSection: some View {
        Section {
            if let quantity = Double(consumedQuantity), quantity > 0 {
                VStack(spacing: 8) {
                    HStack {
                        Text("本次消耗")
                        Spacer()
                        Text("\(formatQuantity(quantity))\(selectedUnit.symbol)")
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Text("使用后剩余")
                        Spacer()
                        let remaining = product.actualCurrentQuantity - quantity
                        Text("\(formatQuantity(max(0, remaining)))\(selectedUnit.symbol)")
                            .fontWeight(.medium)
                            .foregroundColor(remaining < 0 ? .red : .primary)
                    }
                    
                    if let estimatedDays = calculateEstimatedDays(afterConsuming: quantity) {
                        HStack {
                            Text("预计剩余天数")
                            Spacer()
                            Text("\(estimatedDays) 天")
                                .fontWeight(.medium)
                                .foregroundColor(estimatedDays <= 7 ? .orange : .primary)
                        }
                    }
                }
            }
        } header: {
            Text("使用预览")
        }
    }
    
    // MARK: - 辅助方法
    
    private func setupInitialValues() {
        // 设置默认单位（优先使用消耗单位）
        if product.usesDualUnitSystem {
            // 双单位系统：使用消耗单位
            if let unit = UsageRecord.ConsumptionUnit(rawValue: product.consumptionUnit) {
                selectedUnit = unit
            }
        } else {
            // 单一单位系统：使用购买单位
            if let unitType = product.unitType,
               let unit = UsageRecord.ConsumptionUnit(rawValue: unitType) {
                selectedUnit = unit
            }
        }

        // 设置推荐消耗量
        if let recommended = viewModel.getRecommendedConsumption(for: product) {
            consumedQuantity = formatQuantity(recommended)
        }
    }
    
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
    
    private func calculateEstimatedDays(afterConsuming quantity: Double) -> Int? {
        let remainingAfterUse = product.actualCurrentQuantity - quantity
        guard remainingAfterUse > 0 else { return nil }
        
        // 基于历史平均消耗量计算
        if let averageConsumption = viewModel.getRecommendedConsumption(for: product),
           averageConsumption > 0 {
            return Int(remainingAfterUse / averageConsumption)
        }
        
        return nil
    }
    
    private func submitConsumption() {
        guard let quantity = Double(consumedQuantity), quantity > 0 else {
            errorMessage = "请输入有效的消耗量"
            return
        }
        
        // 验证库存是否充足（考虑双单位系统）
        let availableQuantity: Double = {
            if product.usesDualUnitSystem && selectedUnit.rawValue == product.consumptionUnit {
                // 如果使用消耗单位，使用转换后的库存量
                return product.currentQuantityInConsumptionUnit
            } else {
                // 使用购买单位的库存量
                return product.actualCurrentQuantity
            }
        }()

        guard quantity <= availableQuantity else {
            let unitName = product.usesDualUnitSystem && selectedUnit.rawValue == product.consumptionUnit ?
                          product.consumptionUnit : (product.unitType ?? "")
            errorMessage = "消耗量不能超过当前库存（当前库存：\(String(format: "%.1f", availableQuantity))\(unitName)）"
            return
        }
        
        isSubmitting = true
        
        Task {
            do {
                try await viewModel.recordConsumption(
                    for: product,
                    consumedQuantity: quantity,
                    unit: selectedUnit,
                    satisfaction: Int16(satisfaction),
                    notes: notes,
                    scenario: scenario
                )
                
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isSubmitting = false
                }
            }
        }
    }
}

#Preview {
    ConsumptionRecordSheet(
        product: Product(),
        viewModel: ConsumableViewModel()
    )
}