import SwiftUI

/// 时间线过滤器视图，用于设置更详细的过滤条件
struct TimelineFilterView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @ObservedObject var viewModel: TimelineViewModel
    
    @State private var startDate: Date?
    @State private var endDate: Date?
    @State private var showingStartDatePicker = false
    @State private var showingEndDatePicker = false
    @State private var selectedTypes = Set<TimelineEventType>()
    
    // 日期格式化器
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()
    
    var body: some View {
        NavigationView {
            Form {
                // 事件类型过滤
                Section(header: Text("事件类型")) {
                    ForEach(TimelineEventType.allCases) { type in
                        Button(action: {
                            toggleEventType(type)
                        }) {
                            HStack {
                                Image(systemName: type.iconName)
                                    .foregroundColor(type.color)
                                
                                Text(type.rawValue)
                                
                                Spacer()
                                
                                if selectedTypes.contains(type) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                }
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }
                
                // 日期范围过滤
                Section(header: Text("日期范围")) {
                    // 开始日期
                    HStack {
                        Text("开始日期")
                        Spacer()
                        Button(action: {
                            showingStartDatePicker.toggle()
                        }) {
                            Text(startDate != nil ? dateFormatter.string(from: startDate!) : "不限")
                                .foregroundColor(startDate != nil ? .primary : .secondary)
                        }
                    }
                    
                    if showingStartDatePicker {
                        DatePicker("选择开始日期", selection: Binding(
                            get: { startDate ?? Date() },
                            set: { startDate = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(GraphicalDatePickerStyle())
                        
                        Button("清除开始日期") {
                            startDate = nil
                            showingStartDatePicker = false
                        }
                        .foregroundColor(.red)
                    }
                    
                    // 结束日期
                    HStack {
                        Text("结束日期")
                        Spacer()
                        Button(action: {
                            showingEndDatePicker.toggle()
                        }) {
                            Text(endDate != nil ? dateFormatter.string(from: endDate!) : "不限")
                                .foregroundColor(endDate != nil ? .primary : .secondary)
                        }
                    }
                    
                    if showingEndDatePicker {
                        DatePicker("选择结束日期", selection: Binding(
                            get: { endDate ?? Date() },
                            set: { endDate = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(GraphicalDatePickerStyle())
                        
                        Button("清除结束日期") {
                            endDate = nil
                            showingEndDatePicker = false
                        }
                        .foregroundColor(.red)
                    }
                }
                
                // 操作按钮
                Section {
                    Button(action: resetFilters) {
                        Text("重置所有筛选条件")
                            .foregroundColor(.red)
                    }
                }
            }
            .navigationTitle("筛选时间线")
            .navigationBarItems(
                trailing: Button("完成") {
                    applyFilters()
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .onAppear {
                // 初始化当前选择的状态
                selectedTypes = viewModel.selectedEventTypes
                startDate = viewModel.dateRange.start
                endDate = viewModel.dateRange.end
            }
        }
    }
    
    // 切换事件类型选择
    private func toggleEventType(_ type: TimelineEventType) {
        if selectedTypes.contains(type) {
            // 如果只剩下一个选中的类型，不允许取消选择
            if selectedTypes.count > 1 {
                selectedTypes.remove(type)
            }
        } else {
            selectedTypes.insert(type)
        }
    }
    
    // 应用过滤器
    private func applyFilters() {
        viewModel.selectedEventTypes = selectedTypes
        viewModel.dateRange = (startDate, endDate)
        viewModel.applyFilters()
    }
    
    // 重置过滤器
    private func resetFilters() {
        selectedTypes = Set(TimelineEventType.allCases)
        startDate = nil
        endDate = nil
        showingStartDatePicker = false
        showingEndDatePicker = false
    }
}
