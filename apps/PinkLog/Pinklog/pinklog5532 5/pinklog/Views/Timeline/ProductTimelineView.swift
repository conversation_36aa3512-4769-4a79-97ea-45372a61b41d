import SwiftUI

/// 产品时间线主视图，展示产品的完整时间线
struct ProductTimelineView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel
    @ObservedObject var viewModel: TimelineViewModel
    let product: Product

    @State private var showingFilterSheet = false
    @State private var searchText = ""
    @State private var selectedUsageRecord: UsageRecord?
    @State private var selectedExpense: RelatedExpense?

    var body: some View {
        VStack(spacing: 0) {
            // 过滤器工具栏
            filterToolbar

            if viewModel.isLoading {
                // 加载指示器
                ProgressView("加载中...")
                    .padding()
            } else if viewModel.filteredEvents.isEmpty {
                // 空状态
                emptyStateView
            } else {
                // 时间线列表
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 0) {
                        ForEach(Array(viewModel.filteredEvents.enumerated()), id: \.element.id) { index, event in
                            TimelineEventView(
                                event: event,
                                isLast: index == viewModel.filteredEvents.count - 1,
                                onTap: { handleEventTap(event) }
                            )
                            .padding(.horizontal)
                        }
                    }
                    .padding(.vertical)
                }
            }
        }
        .navigationTitle("产品时间线")
        .sheet(isPresented: $showingFilterSheet) {
            TimelineFilterView(viewModel: viewModel)
                .environmentObject(themeManager)
        }
        .onAppear {
            viewModel.loadTimelineForProduct(product)
        }
        .onChange(of: searchText) { newValue in
            viewModel.searchText = newValue
            viewModel.applyFilters()
        }
        .sheet(item: $selectedUsageRecord) { record in
            UsageRecordDetailView(usageRecord: record, product: product, sourceType: .timeline)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        .sheet(item: $selectedExpense) { expense in
            ExpenseDetailView(expense: expense, product: product)
                .environmentObject(themeManager)
        }
    }

    // 处理事件点击
    private func handleEventTap(_ event: TimelineEvent) {
        guard let sourceId = event.sourceId, let sourceType = event.sourceType else { return }

        switch sourceType {
        case "UsageRecord":
            // 查找并打开使用记录详情
            if let records = product.usageRecords?.allObjects as? [UsageRecord],
               let record = records.first(where: { $0.id == sourceId }) {
                selectedUsageRecord = record
            }

        case "RelatedExpense":
            // 查找并打开费用详情
            if let expenses = product.relatedExpenses?.allObjects as? [RelatedExpense],
               let expense = expenses.first(where: { $0.id == sourceId }) {
                selectedExpense = expense
            }

        case "Product":
            // 产品购买事件，不需要额外操作，因为已经在产品详情页
            break

        default:
            print("未知的源类型: \(sourceType)")
        }
    }

    // 过滤器工具栏
    private var filterToolbar: some View {
        VStack(spacing: 0) {
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)

                TextField("搜索事件", text: $searchText)
                    .font(.body)

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(8)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(8)
            .padding(.horizontal)
            .padding(.top, 8)

            // 事件类型过滤器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(TimelineEventType.allCases) { type in
                        Button(action: {
                            toggleEventType(type)
                        }) {
                            HStack {
                                Image(systemName: type.iconName)
                                Text(type.rawValue)
                            }
                            .padding(.vertical, 6)
                            .padding(.horizontal, 12)
                            .background(viewModel.selectedEventTypes.contains(type) ? type.color : Color.gray.opacity(0.2))
                            .foregroundColor(viewModel.selectedEventTypes.contains(type) ? .white : .primary)
                            .cornerRadius(16)
                        }
                    }

                    Button(action: {
                        showingFilterSheet = true
                    }) {
                        HStack {
                            Image(systemName: "slider.horizontal.3")
                            Text("更多筛选")
                        }
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color(UIColor.tertiarySystemBackground))
                        .foregroundColor(.primary)
                        .cornerRadius(16)
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }

            Divider()
        }
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.clock")
                .font(.system(size: 48))
                .foregroundColor(.gray)

            Text("暂无时间线事件")
                .font(.headline)

            Text("该产品还没有记录任何事件。\n添加使用记录、费用或其他互动来构建时间线。")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // 切换事件类型选择
    private func toggleEventType(_ type: TimelineEventType) {
        if viewModel.selectedEventTypes.contains(type) {
            // 如果只剩下一个选中的类型，不允许取消选择
            if viewModel.selectedEventTypes.count > 1 {
                viewModel.selectedEventTypes.remove(type)
            }
        } else {
            viewModel.selectedEventTypes.insert(type)
        }
        viewModel.applyFilters()
    }
}
