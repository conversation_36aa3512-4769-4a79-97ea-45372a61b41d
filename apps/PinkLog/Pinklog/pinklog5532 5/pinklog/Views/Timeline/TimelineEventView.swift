import SwiftUI

/// 时间线事件组件，用于展示单个事件
struct TimelineEventView: View {
    let event: TimelineEvent
    let isLast: Bool
    var onTap: () -> Void

    // 判断是否为物品故事事件
    private var isStoryEvent: Bool {
        return event.title.contains("【物品故事】")
    }
    
    // 判断是否为消耗记录事件
    private var isConsumptionEvent: Bool {
        return event.type == .consumption
    }

    var body: some View {
        Button(action: onTap) {
            HStack(alignment: .top, spacing: 16) {
                // 左侧时间线
                VStack(spacing: 0) {
                    // 事件图标
                    ZStack {
                        // 基础圆形
                        Circle()
                            .fill(event.type.color)
                            .frame(width: 32, height: 32)

                        // 主图标
                        Image(systemName: event.type.iconName)
                            .foregroundColor(.white)

                        // 如果是物品故事，添加一个小型书本标记
                        if isStoryEvent {
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Image(systemName: "book.fill")
                                        .font(.system(size: 6))
                                        .foregroundColor(.white)
                                )
                                .offset(x: 10, y: -10) // 右上角位置
                        }
                        
                        // 如果是消耗记录，添加一个小型消耗标记
                        if isConsumptionEvent {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Image(systemName: "drop.fill")
                                        .font(.system(size: 6))
                                        .foregroundColor(.white)
                                )
                                .offset(x: 10, y: 10) // 右下角位置
                        }
                    }

                    // 连接线（如果不是最后一项）
                    if !isLast {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 2)
                            .frame(maxHeight: .infinity)
                    }
                }

                // 右侧内容
                VStack(alignment: .leading, spacing: 8) {
                    // 日期
                    Text(event.date.formatted(date: .abbreviated, time: .shortened))
                        .font(.caption)
                        .foregroundColor(.secondary)

                    // 标题
                    if isStoryEvent {
                        // 物品故事标题 - 更加精致的设计
                        HStack(spacing: 6) {
                            // 标题内容（去掉"【物品故事】"前缀）
                            Text(event.title.replacingOccurrences(of: "【物品故事】", with: ""))
                                .font(.headline)
                                .foregroundColor(.primary)

                            // 小型书本图标作为物品故事的标识
                            Image(systemName: "book.fill")
                                .foregroundColor(.orange)
                                .font(.caption2)
                        }
                    } else if isConsumptionEvent {
                        // 消耗记录标题 - 特殊设计
                        HStack(spacing: 8) {
                            // 消耗标签
                            HStack(spacing: 4) {
                                Image(systemName: "minus.circle.fill")
                                    .foregroundColor(.cyan)
                                    .font(.caption)
                                Text("消耗记录")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.cyan)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(Color.cyan.opacity(0.1))
                                    .overlay(
                                        Capsule()
                                            .stroke(Color.cyan.opacity(0.3), lineWidth: 1)
                                    )
                            )
                            
                            Spacer()
                        }
                        
                        // 标题内容
                        Text(event.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .padding(.top, 2)
                    } else {
                        // 普通标题
                        Text(event.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                    }

                    // 描述（如果有）
                    if let description = event.description, !description.isEmpty {
                        if isConsumptionEvent {
                            // 消耗记录的描述 - 特殊样式
                            VStack(alignment: .leading, spacing: 6) {
                                let parts = description.components(separatedBy: " · ")
                                ForEach(Array(parts.enumerated()), id: \.offset) { index, part in
                                    HStack(spacing: 6) {
                                        // 根据内容类型显示不同的图标
                                        if part.contains("消耗:") {
                                            Image(systemName: "minus.circle")
                                                .foregroundColor(.red)
                                                .font(.caption2)
                                        } else if part.contains("剩余") || part.contains("已用完") {
                                            Image(systemName: "chart.pie")
                                                .foregroundColor(.orange)
                                                .font(.caption2)
                                        } else if part.contains("效率:") {
                                            Image(systemName: "speedometer")
                                                .foregroundColor(.blue)
                                                .font(.caption2)
                                        } else if part.contains("满意度:") {
                                            Image(systemName: "star.fill")
                                                .foregroundColor(.yellow)
                                                .font(.caption2)
                                        } else if part.contains("场景:") {
                                            Image(systemName: "location")
                                                .foregroundColor(.green)
                                                .font(.caption2)
                                        } else {
                                            Image(systemName: "info.circle")
                                                .foregroundColor(.secondary)
                                                .font(.caption2)
                                        }
                                        
                                        Text(part)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.cyan.opacity(0.05))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.cyan.opacity(0.2), lineWidth: 1)
                                    )
                            )
                        } else {
                            // 普通描述
                            Text(description)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                    }

                    // 可点击提示 - 只对非购买类型的事件显示
                    if event.sourceId != nil && event.sourceType != "Product" {
                        HStack {
                            Image(systemName: "arrow.right.circle")
                                .font(.caption)
                            Text("点击查看详情")
                                .font(.caption)
                        }
                        .foregroundColor(event.type.color)
                        .padding(.top, 4)
                    }
                }
                .padding(.bottom, 16)
            }
            // 移除过于突兀的背景和边框，保持时间线的整体美感
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}
