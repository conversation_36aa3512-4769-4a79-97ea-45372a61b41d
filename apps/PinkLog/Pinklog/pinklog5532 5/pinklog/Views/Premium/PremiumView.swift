import SwiftUI
import StoreKit

struct PremiumView: View {
    @StateObject private var purchaseManager = PurchaseManager.shared
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @State private var isAnimating = false
    @State private var selectedFeatureIndex = 0
    @State private var showingPurchaseSuccess = false
    
    private let features = [
        PremiumFeature(
            icon: "infinity",
            title: "无限添加物品",
            description: "记录您所有心爱的物品，没有数量限制",
            color: .blue
        ),
        PremiumFeature(
            icon: "chart.line.uptrend.xyaxis",
            title: "高级分析报告",
            description: "深度分析您的消费行为，优化购买决策",
            color: .green
        ),
        PremiumFeature(
            icon: "bell.badge",
            title: "智能提醒",
            description: "保修期提醒、低使用率提醒，不错过任何重要信息",
            color: .orange
        ),
        PremiumFeature(
            icon: "icloud",
            title: "云端同步",
            description: "数据安全备份，多设备无缝同步",
            color: .purple
        ),
        PremiumFeature(
            icon: "paintbrush",
            title: "专属主题",
            description: "独家主题色彩，打造专属个性化体验",
            color: .pink
        ),
        PremiumFeature(
            icon: "headphones",
            title: "专属客服",
            description: "优先技术支持，专业团队一对一服务",
            color: .indigo
        )
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 顶部关闭按钮
                    HStack {
                        Spacer()
                        Button {
                            dismiss()
                        } label: {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }
                    
                    // 主标题区域
                    VStack(spacing: 20) {
                        // 动画图标
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple, .pink],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 120)
                                .scaleEffect(isAnimating ? 1.1 : 1.0)
                                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: isAnimating)
                            
                            Image(systemName: "crown.fill")
                                .font(.system(size: 50))
                                .foregroundColor(.white)
                        }
                        
                        VStack(spacing: 12) {
                            Text("升级到 PinkLog Pro")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                            
                            Text("解锁全部功能，让记录更有价值")
                                .font(.title3)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.horizontal, 30)
                    .padding(.bottom, 40)
                    
                    // 当前限制提醒（仅对免费用户）
                    if !purchaseManager.isPremiumPurchased {
                        VStack(spacing: 12) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                Text("您已达到免费版本限制")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                            }
                            
                            Text("免费版本最多添加 10 个物品")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange.opacity(0.1))
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                        .padding(.horizontal, 24)
                        .padding(.bottom, 30)
                    }
                    
                    // 功能特性展示
                    VStack(spacing: 16) {
                        Text("Pro 功能特性")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 24)
                        
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 16) {
                            ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                                FeatureCard(
                                    feature: feature,
                                    isSelected: selectedFeatureIndex == index
                                )
                                .onTapGesture {
                                    withAnimation(.spring()) {
                                        selectedFeatureIndex = index
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 24)
                    }
                    .padding(.bottom, 40)
                    
                    // 社会认同和紧迫感
                    VStack(spacing: 16) {
                        HStack {
                            Image(systemName: "person.3.fill")
                                .foregroundColor(.green)
                            Text("已有 10,000+ 用户升级到 Pro 版本")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                            Text("App Store 4.8 星好评")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 30)
                    
                    // 价格和购买按钮
                    VStack(spacing: 20) {
                        if let product = purchaseManager.products.first {
                            PricingCard(product: product)
                        } else {
                            // 加载占位符
                            VStack(spacing: 12) {
                                Text("正在加载价格信息...")
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                                
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            }
                            .frame(height: 120)
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 40)
                    
                    // 满意保证和条款
                    VStack(spacing: 12) {
                        HStack {
                            Image(systemName: "checkmark.shield.fill")
                                .foregroundColor(.green)
                            Text("30天无理由退款保证")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(spacing: 8) {
                            Text("购买即表示同意")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack(spacing: 4) {
                                Link("服务条款", destination: URL(string: "https://example.com/terms")!)
                                Text("和")
                                Link("隐私政策", destination: URL(string: "https://example.com/privacy")!)
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 20)
                }
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        themeManager.currentTheme.primaryColor.opacity(0.03)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .onAppear {
            isAnimating = true
        }
        .onReceive(NotificationCenter.default.publisher(for: .purchaseCompleted)) { notification in
            print("🎉 收到购买成功通知")
            if let productId = notification.userInfo?["productId"] as? String {
                print("✅ 购买的产品: \(productId)")
            }

            let isRestore = notification.userInfo?["isRestore"] as? Bool ?? false
            print("🔄 是否为恢复购买: \(isRestore)")

            // 延迟一下确保状态同步完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                showingPurchaseSuccess = true
            }
        }
        .alert("购买失败", isPresented: .constant(purchaseManager.errorMessage != nil)) {
            Button("确定") {
                purchaseManager.errorMessage = nil
            }
        } message: {
            if let errorMessage = purchaseManager.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("购买成功", isPresented: $showingPurchaseSuccess) {
            Button("太棒了！") {
                dismiss()
            }
        } message: {
            Text("恭喜您成功升级到 Pro 版本！现在可以享受所有高级功能了。")
        }
    }
}

struct FeatureCard: View {
    let feature: PremiumFeature
    let isSelected: Bool
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(feature.color.opacity(0.15))
                    .frame(width: 50, height: 50)
                
                Image(systemName: feature.icon)
                    .font(.title2)
                    .foregroundColor(feature.color)
            }
            
            VStack(spacing: 6) {
                Text(feature.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                
                Text(feature.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(
                    color: isSelected ? feature.color.opacity(0.3) : Color.black.opacity(0.05),
                    radius: isSelected ? 8 : 4,
                    x: 0,
                    y: isSelected ? 4 : 2
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    isSelected ? feature.color.opacity(0.5) : Color.clear,
                    lineWidth: 2
                )
        )
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3), value: isSelected)
    }
}

struct PricingCard: View {
    let product: StoreKit.Product
    @StateObject private var purchaseManager = PurchaseManager.shared
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: 16) {
            // 价格展示
            VStack(spacing: 8) {
                HStack(alignment: .bottom, spacing: 4) {
                    Text(product.displayPrice)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                    
                    Text("一次性购买")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.bottom, 8)
                }
                
                Text("终身享受所有功能")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // 购买按钮
            Button {
                print("🛒 购买按钮被点击")
                print("📦 产品信息: \(product.id), \(product.displayName)")
                print("🔄 当前加载状态: \(purchaseManager.isLoading)")
                print("✅ 按钮是否可用: \(!purchaseManager.isLoading)")
                print("💎 是否已购买: \(purchaseManager.isPremiumPurchased)")

                Task {
                    print("🚀 开始购买流程")
                    await purchaseManager.purchase(product)
                    print("🏁 购买流程结束")
                    print("💎 购买后状态: \(purchaseManager.isPremiumPurchased)")
                }
            } label: {
                HStack {
                    if purchaseManager.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else if purchaseManager.isPremiumPurchased {
                        Image(systemName: "checkmark.circle.fill")
                        Text("已购买")
                            .fontWeight(.semibold)
                    } else {
                        Image(systemName: "crown.fill")
                        Text("立即升级到 Pro")
                            .fontWeight(.semibold)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 54)
                .background(
                    LinearGradient(
                        colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.primaryColor.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .foregroundColor(.white)
                .cornerRadius(27)
                .shadow(color: themeManager.currentTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(purchaseManager.isLoading || purchaseManager.isPremiumPurchased)
            
            // 恢复购买按钮
            Button {
                Task {
                    await purchaseManager.restorePurchases()
                }
            } label: {
                Text("恢复购买")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }
            .disabled(purchaseManager.isLoading)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [themeManager.currentTheme.primaryColor.opacity(0.3), themeManager.currentTheme.primaryColor.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .onAppear {
            print("🎯 PricingCard 出现")
            print("📦 产品信息: \(product.id)")
            print("💰 PurchaseManager 状态: isLoading=\(purchaseManager.isLoading), products=\(purchaseManager.products.count)")
            print("💎 Premium购买状态: \(purchaseManager.isPremiumPurchased)")
            print("🔐 已购买产品列表: \(purchaseManager.purchasedProducts)")
            if let error = purchaseManager.errorMessage {
                print("❌ PurchaseManager 错误: \(error)")
            }
        }
        .alert("购买错误", isPresented: .constant(purchaseManager.errorMessage != nil)) {
            Button("确定") {
                purchaseManager.errorMessage = nil
            }
        } message: {
            Text(purchaseManager.errorMessage ?? "")
        }
    }
}

struct PremiumFeature {
    let icon: String
    let title: String
    let description: String
    let color: Color
}

#Preview {
    PremiumView()
        .environmentObject(ThemeManager())
}