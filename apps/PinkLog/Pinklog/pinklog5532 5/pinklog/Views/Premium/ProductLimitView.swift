import SwiftUI

struct ProductLimitView: View {
    @Binding var isPresented: Bool
    @State private var remainingItems: Int
    @EnvironmentObject var themeManager: ThemeManager
    
    init(isPresented: Binding<Bool>, remainingItems: Int = 0) {
        self._isPresented = isPresented
        self._remainingItems = State(initialValue: remainingItems)
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 图标区域
            ZStack {
                Circle()
                    .fill(LinearGradient(
                        colors: [.orange, .red],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 80, height: 80)
                
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 35))
                    .foregroundColor(.white)
            }
            
            // 标题和描述
            VStack(spacing: 12) {
                Text("达到物品数量限制")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                if remainingItems > 0 {
                    Text("您还可以添加 \(remainingItems) 个物品")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                } else {
                    Text("免费版本最多添加 10 个物品")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Text("升级到 Pro 版本解锁无限物品添加")
                    .font(.callout)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .multilineTextAlignment(.center)
            }
            
            // 升级优势简介
            VStack(spacing: 12) {
                FeatureRow(icon: "infinity", text: "无限添加物品", color: .blue)
                FeatureRow(icon: "chart.line.uptrend.xyaxis", text: "高级分析报告", color: .green)
                FeatureRow(icon: "icloud", text: "云端同步", color: .purple)
            }
            .padding(.horizontal)
            
            // 按钮区域
            VStack(spacing: 12) {
                Button {
                    isPresented = false
                    // 这里应该打开完整的Premium页面
                } label: {
                    HStack {
                        Image(systemName: "crown.fill")
                        Text("立即升级")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.primaryColor.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .foregroundColor(.white)
                    .cornerRadius(25)
                }
                
                Button {
                    isPresented = false
                } label: {
                    Text("稍后提醒")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(32)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
        )
        .padding(.horizontal, 40)
    }
}

struct FeatureRow: View {
    let icon: String
    let text: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(.green)
        }
    }
}

#Preview {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        ProductLimitView(isPresented: .constant(true), remainingItems: 3)
    }
    .environmentObject(ThemeManager())
}