//
//  ArchiveSuggestionsView.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI

struct ArchiveSuggestionsView: View {
    @StateObject private var viewModel = ConsumableViewModel()
    @State private var suggestions: [ArchiveSuggestion] = []
    @State private var isLoading = false
    @State private var showingArchiveAlert = false
    @State private var selectedSuggestion: ArchiveSuggestion?
    
    var body: some View {
        NavigationView {
            Group {
                if suggestions.isEmpty {
                    emptyStateView
                } else {
                    suggestionsList
                }
            }
            .navigationTitle("归档建议")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("刷新") {
                        refreshSuggestions()
                    }
                    .disabled(isLoading)
                }
            }
            .onAppear {
                loadSuggestions()
            }
            .refreshable {
                await performArchiveCheck()
            }
        }
        .alert("确认归档", isPresented: $showingArchiveAlert) {
            But<PERSON>("取消", role: .cancel) { }
            But<PERSON>("归档", role: .destructive) {
                if let suggestion = selectedSuggestion {
                    archiveProduct(suggestion)
                }
            }
        } message: {
            if let suggestion = selectedSuggestion {
                Text("确定要归档 \"\(suggestion.product.name ?? "此产品")\" 吗？归档后可在归档列表中找到并恢复。")
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "archivebox")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("暂无归档建议")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("系统会自动分析您的消耗品使用情况，\n为长期未使用或已耗尽的产品提供归档建议。")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("立即检查") {
                refreshSuggestions()
            }
            .buttonStyle(.borderedProminent)
            .disabled(isLoading)
        }
        .padding()
    }
    
    private var suggestionsList: some View {
        List {
            ForEach(suggestions, id: \.id) { suggestion in
                SuggestionRow(suggestion: suggestion) {
                    archiveProduct(suggestion)
                } onPostpone: {
                    postponeSuggestion(suggestion)
                } onIgnore: {
                    ignoreSuggestion(suggestion)
                }
            }
        }
        .listStyle(.insetGrouped)
    }
    
    private func loadSuggestions() {
        suggestions = viewModel.getArchiveSuggestions()
    }
    
    private func refreshSuggestions() {
        isLoading = true
        Task {
            await performArchiveCheck()
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    private func performArchiveCheck() async {
        await viewModel.performArchiveCheck()
        await MainActor.run {
            loadSuggestions()
        }
    }
    
    private func archiveProduct(_ suggestion: ArchiveSuggestion) {
        selectedSuggestion = suggestion
        showingArchiveAlert = true
    }
    
    private func postponeSuggestion(_ suggestion: ArchiveSuggestion) {
        // 找到对应的产品并推迟建议
        if let product = viewModel.consumableProducts.first(where: { $0.id == suggestion.product.id }) {
            viewModel.postponeArchiveSuggestion(for: product, days: 7)
            loadSuggestions()
        }
    }
    
    private func ignoreSuggestion(_ suggestion: ArchiveSuggestion) {
        // 找到对应的产品并忽略建议
        if let product = viewModel.consumableProducts.first(where: { $0.id == suggestion.product.id }) {
            viewModel.ignoreArchiveSuggestion(for: product)
            loadSuggestions()
        }
    }
}

struct SuggestionRow: View {
    let suggestion: ArchiveSuggestion
    let onArchive: () -> Void
    let onPostpone: () -> Void
    let onIgnore: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 产品信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.product.name ?? "未知产品")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(reasonDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 置信度标签
                confidenceBadge
            }
            
            // 建议描述
            Text(suggestion.reason.description)
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
            
            // 操作按钮
            HStack(spacing: 12) {
                Button("归档") {
                    onArchive()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
                
                Button("推迟") {
                    onPostpone()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("忽略") {
                    onIgnore()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
    
    private var reasonDescription: String {
        switch suggestion.reason {
        case .longTermUnused(let days):
            return "长期未使用（\(days)天）"
        case .depletedLongTime(let days):
            return "已耗尽且长时间未补充（\(days)天）"
        case .expiredLongTime(let days):
            return "已过期且超过处理期限（\(days)天）"
        case .seasonalOffPeriod:
            return "季节性物品非使用期"
        case .replacedByAlternative:
            return "已被替代品取代"
        case .usageDecline(let rate):
            return "使用频率持续下降（\(String(format: "%.1f", rate * 100))%）"
        case .userRequested:
            return "用户手动归档"
        }
    }
    
    private var confidenceBadge: some View {
        let confidence = Int(suggestion.confidence * 100)
        let color: Color = confidence >= 90 ? .green : 
                          confidence >= 70 ? .orange : .red
        
        return Text("\(confidence)%")
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .clipShape(Capsule())
    }
}

#Preview {
    ArchiveSuggestionsView()
}