import SwiftUI

struct TagsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager

    @State private var showingAddTagSheet = false
    @State private var showingEditTagSheet = false
    @State private var showingDeleteAlert = false
    @State private var tagToEdit: Tag?
    @State private var tagToDelete: Tag?
    @State private var searchText = ""

    var selectionMode: Bool = false
    var onDone: (() -> Void)?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar

                // 标签列表
                List {
                    ForEach(tagsByType.keys.sorted(), id: \.self) { type in
                        Section(header: Text(type)) {
                            ForEach(tagsByType[type] ?? []) { tag in
                                tagRow(tag)
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("标签管理")
            .navigationBarItems(
                leading: selectionMode ? Button("取消") {
                    tagViewModel.clearSelection()
                    presentationMode.wrappedValue.dismiss()
                } : nil,
                trailing: HStack {
                    if selectionMode {
                        Button("完成") {
                            onDone?()
                            presentationMode.wrappedValue.dismiss()
                        }
                    } else {
                        Button(action: {
                            showingAddTagSheet = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
            )
            .sheet(isPresented: $showingAddTagSheet) {
                AddTagView()
            }
            .sheet(item: $tagToEdit) { tag in
                EditTagView(tag: tag)
            }
            .alert(isPresented: $showingDeleteAlert) {
                Alert(
                    title: Text("删除标签"),
                    message: Text("确定要删除\"\(tagToDelete?.name ?? "")\"标签吗？此操作不会删除已标记的产品。"),
                    primaryButton: .destructive(Text("删除")) {
                        if let tag = tagToDelete {
                            tagViewModel.deleteTag(tag)
                        }
                    },
                    secondaryButton: .cancel()
                )
            }
            .onAppear {
                tagViewModel.loadTags()
            }
        }
    }

    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索标签", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }

    // 标签行
    private func tagRow(_ tag: Tag) -> some View {
        HStack {
            if selectionMode {
                Image(systemName: tagViewModel.isSelected(tag) ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(tagViewModel.isSelected(tag) ? themeManager.currentTheme.primaryColor : .secondary)
            }

            Circle()
                .fill(tagViewModel.getTagColor(tag))
                .frame(width: 12, height: 12)

            Text(tag.name ?? "")
                .font(.body)

            Spacer()

            if !selectionMode {
                Menu {
                    Button(action: {
                        tagToEdit = tag
                        showingEditTagSheet = true
                    }) {
                        Label("编辑", systemImage: "pencil")
                    }

                    Button(role: .destructive, action: {
                        tagToDelete = tag
                        showingDeleteAlert = true
                    }) {
                        Label("删除", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.secondary)
                }
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if selectionMode {
                tagViewModel.toggleTag(tag)
            }
        }
    }

    // 按类型分组的标签
    private var tagsByType: [String: [Tag]] {
        let filteredTags = searchText.isEmpty ? tagViewModel.tags : tagViewModel.tags.filter {
            $0.name?.localizedCaseInsensitiveContains(searchText) ?? false
        }

        var result = [String: [Tag]]()

        for tag in filteredTags {
            let type = tag.type ?? "未分类"
            if result[type] == nil {
                result[type] = []
            }
            result[type]?.append(tag)
        }

        return result
    }
}

struct TagsView_Previews: PreviewProvider {
    static var previews: some View {
        TagsView()
            .environmentObject(TagViewModel(
                repository: TagRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}
