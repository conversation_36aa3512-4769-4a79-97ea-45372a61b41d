import SwiftUI

struct EditTagView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    let tag: Tag
    
    @State private var name: String = ""
    @State private var selectedColor: String = "red"
    @State private var type: String = "自定义标签"
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    private let colors = [
        ("红色", "red"),
        ("蓝色", "blue"),
        ("绿色", "green"),
        ("橙色", "orange"),
        ("紫色", "purple"),
        ("粉色", "pink"),
        ("黄色", "yellow"),
        ("青色", "teal"),
        ("靛蓝", "indigo")
    ]
    
    private let types = ["基础标签", "自定义标签", "情感标签", "使用场景", "购买动机"]
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section(header: Text("基本信息")) {
                    TextField("标签名称 *", text: $name)
                    
                    Picker("标签类型", selection: $type) {
                        ForEach(types, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // 颜色选择
                Section(header: Text("颜色")) {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 60))], spacing: 10) {
                        ForEach(colors, id: \.1) { colorName, colorValue in
                            colorButton(colorName: colorName, colorValue: colorValue)
                        }
                    }
                }
                
                // 预览
                Section(header: Text("预览")) {
                    HStack {
                        Spacer()
                        
                        Text(name.isEmpty ? "标签名称" : name)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(tagColor.opacity(0.2))
                            .foregroundColor(tagColor)
                            .cornerRadius(8)
                        
                        Spacer()
                    }
                }
                
                // 保存按钮
                Section {
                    Button(action: updateTag) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.primaryColor)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("编辑标签")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showingAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .onAppear {
                // 加载标签数据
                name = tag.name ?? ""
                selectedColor = tag.color ?? "red"
                type = tag.type ?? "自定义标签"
            }
        }
    }
    
    // 颜色按钮
    private func colorButton(colorName: String, colorValue: String) -> some View {
        VStack {
            Circle()
                .fill(colorFromString(colorValue))
                .frame(width: 40, height: 40)
                .overlay(
                    Circle()
                        .stroke(Color.primary, lineWidth: selectedColor == colorValue ? 2 : 0)
                        .padding(2)
                )
                .overlay(
                    Image(systemName: "checkmark")
                        .foregroundColor(.white)
                        .opacity(selectedColor == colorValue ? 1 : 0)
                )
                .onTapGesture {
                    selectedColor = colorValue
                }
            
            Text(colorName)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // 标签颜色
    private var tagColor: Color {
        colorFromString(selectedColor)
    }
    
    // 从字符串获取颜色
    private func colorFromString(_ colorString: String) -> Color {
        switch colorString {
        case "red": return .red
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "purple": return .purple
        case "pink": return .pink
        case "yellow": return .yellow
        case "teal": return .teal
        case "indigo": return .indigo
        default: return .gray
        }
    }
    
    // 更新标签
    private func updateTag() {
        // 验证输入
        if name.isEmpty {
            alertMessage = "请输入标签名称"
            showingAlert = true
            return
        }
        
        // 检查是否已存在同名标签（排除自身）
        let existingTag = tagViewModel.tags.first { $0.name == name && $0.id != tag.id }
        if existingTag != nil {
            alertMessage = "已存在同名标签"
            showingAlert = true
            return
        }
        
        // 更新标签
        let success = tagViewModel.updateTag(tag, name: name, color: selectedColor, type: type)
        
        if success {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "更新标签失败"
            showingAlert = true
        }
    }
}

struct EditTagView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let tag = Tag(context: context)
        tag.id = UUID()
        tag.name = "测试标签"
        tag.color = "blue"
        tag.type = "自定义标签"
        
        return EditTagView(tag: tag)
            .environmentObject(TagViewModel(
                repository: TagRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}
