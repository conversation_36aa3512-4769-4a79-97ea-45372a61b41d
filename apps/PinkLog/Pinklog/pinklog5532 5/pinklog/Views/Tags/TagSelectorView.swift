import SwiftUI

struct TagSelectorView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var searchText = ""
    @State private var showingAddTagSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 已选标签
                if !tagViewModel.selectedTags.isEmpty {
                    selectedTagsSection
                }
                
                // 标签列表
                List {
                    ForEach(tagsByType.keys.sorted(), id: \.self) { type in
                        Section(header: Text(type)) {
                            ForEach(tagsByType[type] ?? []) { tag in
                                tagRow(tag)
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("选择标签")
            .navigationBarItems(
                leading: But<PERSON>("取消") {
                    tagViewModel.clearSelection()
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: HStack {
                    Button(action: {
                        showingAddTagSheet = true
                    }) {
                        Image(systemName: "plus")
                    }
                    
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            )
            .sheet(isPresented: $showingAddTagSheet) {
                AddTagView()
            }
            .onAppear {
                tagViewModel.loadTags()
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索标签", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 已选标签部分
    private var selectedTagsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("已选标签")
                .font(.headline)
                .padding(.horizontal)
                .padding(.top, 8)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Array(tagViewModel.selectedTags), id: \.self) { tag in
                        HStack(spacing: 4) {
                            Text(tag.name ?? "")
                                .font(.subheadline)
                            
                            Button(action: {
                                tagViewModel.unselectTag(tag)
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.caption)
                            }
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(tagViewModel.getTagColor(tag).opacity(0.2))
                        .foregroundColor(tagViewModel.getTagColor(tag))
                        .cornerRadius(8)
                    }
                }
                .padding(.horizontal)
            }
            .padding(.bottom, 8)
            
            Divider()
        }
    }
    
    // 标签行
    private func tagRow(_ tag: Tag) -> some View {
        HStack {
            Circle()
                .fill(tagViewModel.getTagColor(tag))
                .frame(width: 12, height: 12)
            
            Text(tag.name ?? "")
                .font(.body)
            
            Spacer()
            
            Image(systemName: tagViewModel.isSelected(tag) ? "checkmark.circle.fill" : "circle")
                .foregroundColor(tagViewModel.isSelected(tag) ? themeManager.currentTheme.primaryColor : .secondary)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            tagViewModel.toggleTag(tag)
        }
    }
    
    // 按类型分组的标签
    private var tagsByType: [String: [Tag]] {
        let filteredTags = searchText.isEmpty ? tagViewModel.tags : tagViewModel.tags.filter {
            $0.name?.localizedCaseInsensitiveContains(searchText) ?? false
        }
        
        var result = [String: [Tag]]()
        
        for tag in filteredTags {
            let type = tag.type ?? "未分类"
            if result[type] == nil {
                result[type] = []
            }
            result[type]?.append(tag)
        }
        
        return result
    }
}

struct TagSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        TagSelectorView()
            .environmentObject(TagViewModel(
                repository: TagRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}
