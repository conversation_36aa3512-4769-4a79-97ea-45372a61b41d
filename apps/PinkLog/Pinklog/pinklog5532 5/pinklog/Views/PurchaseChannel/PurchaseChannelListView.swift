import SwiftUI

struct PurchaseChannelListView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @ObservedObject var viewModel: PurchaseChannelViewModel

    let category: PurchaseChannelCategory

    @State private var showingAddSheet = false
    @State private var editingChannel: PurchaseChannel?
    @State private var channelName = ""
    @State private var channelURL = ""
    @State private var channelLocation = ""
    @State private var showingDeleteAlert = false
    @State private var channelToDelete: PurchaseChannel?
    @State private var animateButton = false
    @State private var searchText = ""

    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("搜索渠道", text: $searchText)
                    .disableAutocorrection(true)

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                    }
                    .foregroundColor(.secondary)
                }
            }
            .padding(8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .padding(.horizontal)
            .padding(.top, 8)

            // 添加按钮区域
            HStack {
                Spacer()

                Button(action: {
                    editingChannel = nil
                    channelName = ""
                    channelURL = ""
                    channelLocation = ""
                    showingAddSheet = true
                    animateButton = true

                    // 动画效果
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        animateButton = false
                    }
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16))
                            .scaleEffect(animateButton ? 1.3 : 1.0)
                            .animation(.spring(response: 0.3), value: animateButton)
                        Text("添加渠道")
                            .font(.subheadline)
                    }
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                    .cornerRadius(20)
                }
                .padding(.trailing)
                .padding(.vertical, 8)
            }

            // 渠道列表
            List {
                Section {
                    if filteredChannels.isEmpty {
                        HStack {
                            Spacer()
                            VStack(spacing: 12) {
                                Image(systemName: "magnifyingglass")
                                    .font(.system(size: 40))
                                    .foregroundColor(.secondary)
                                    .padding()

                                Text(searchText.isEmpty ? "暂无渠道" : "未找到匹配的渠道")
                                    .font(.headline)
                                    .foregroundColor(.secondary)

                                if !searchText.isEmpty {
                                    Button(action: {
                                        searchText = ""
                                    }) {
                                        Text("清除搜索")
                                            .foregroundColor(themeManager.currentTheme.primaryColor)
                                    }
                                    .padding(.top, 8)
                                }
                            }
                            .padding()
                            Spacer()
                        }
                    } else {
                        ForEach(filteredChannels, id: \.self) { channel in
                            HStack {
                                VStack(alignment: .leading, spacing: 6) {
                                    HStack(spacing: 8) {
                                        Text(channel.name ?? "未命名")
                                            .font(.headline)

                                        // 使用频率标签
                                        if channel.usageCount > 0 {
                                            HStack(spacing: 2) {
                                                Image(systemName: "star.fill")
                                                    .font(.system(size: 10))
                                                Text("\(channel.usageCount)")
                                                    .font(.system(size: 12))
                                            }
                                            .foregroundColor(.white)
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(frequencyColor(for: channel.usageCount))
                                            .cornerRadius(10)
                                        }
                                    }

                                    if let url = channel.url, !url.isEmpty {
                                        HStack {
                                            Image(systemName: "link")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                            Text(url)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }

                                    if let location = channel.location, !location.isEmpty {
                                        HStack {
                                            Image(systemName: "mappin")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                            Text(location)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                }

                                Spacer()
                            }
                            .padding(.vertical, 6)
                            .contentShape(Rectangle())
                            .swipeActions {
                                Button(role: .destructive) {
                                    channelToDelete = channel
                                    showingDeleteAlert = true
                                } label: {
                                    Label("删除", systemImage: "trash")
                                }

                                Button {
                                    editingChannel = channel
                                    channelName = channel.name ?? ""
                                    channelURL = channel.url ?? ""
                                    channelLocation = channel.location ?? ""
                                    showingAddSheet = true
                                } label: {
                                    Label("编辑", systemImage: "pencil")
                                }
                                .tint(.blue)
                            }
                        }
                    }
                } footer: {
                    Text("常用的渠道会自动排在前面")
                }
            }
            .listStyle(InsetGroupedListStyle())
        }
        .navigationTitle(category.name ?? "渠道列表")
        .sheet(isPresented: $showingAddSheet) {
            channelFormView
        }
        .alert("确认删除", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                if let channel = channelToDelete {
                    viewModel.deleteChannel(channel)
                }
            }
        } message: {
            Text("确定要删除渠道\"" + (channelToDelete?.name ?? "") + "\"吗？")
        }
        .onAppear {
            viewModel.fetchChannels(for: category)
        }
    }

    // 过滤后的渠道列表
    private var filteredChannels: [PurchaseChannel] {
        if searchText.isEmpty {
            return viewModel.channelsInSelectedCategory
        } else {
            return viewModel.channelsInSelectedCategory.filter { channel in
                let name = channel.name ?? ""
                let url = channel.url ?? ""
                let location = channel.location ?? ""

                return name.localizedCaseInsensitiveContains(searchText) ||
                       url.localizedCaseInsensitiveContains(searchText) ||
                       location.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // 根据使用频率返回不同颜色
    private func frequencyColor(for count: Int32) -> Color {
        switch count {
        case 0...2:
            return Color.gray
        case 3...5:
            return Color.blue
        case 6...10:
            return Color.green
        case 11...20:
            return Color.orange
        default:
            return Color.red
        }
    }

    var channelFormView: some View {
        NavigationView {
            Form {
                Section {
                    TextField("渠道名称", text: $channelName)
                        .font(.body)
                        .padding(.vertical, 4)

                    TextField("网址 (可选)", text: $channelURL)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                        .font(.body)
                        .padding(.vertical, 4)

                    TextField("位置 (可选)", text: $channelLocation)
                        .font(.body)
                        .padding(.vertical, 4)
                } footer: {
                    Text("线上渠道可填写网址，线下渠道可填写位置")
                }
            }
            .navigationTitle(editingChannel == nil ? "添加渠道" : "编辑渠道")
            .navigationBarItems(
                leading: Button("取消") {
                    showingAddSheet = false
                },
                trailing: Button("保存") {
                    if !channelName.isEmpty {
                        if let channel = editingChannel {
                            viewModel.updateChannel(channel, name: channelName, url: channelURL, location: channelLocation)
                        } else {
                            _ = viewModel.addChannel(to: category, name: channelName, url: channelURL, location: channelLocation)
                        }
                    }
                    showingAddSheet = false
                }
                .disabled(channelName.isEmpty)
            )
        }
    }
}

// 预览
struct PurchaseChannelListView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let viewModel = PurchaseChannelViewModel(context: context)

        // 创建一个预览类别
        let category = PurchaseChannelCategory(context: context)
        category.id = UUID()
        category.name = "预览类别"
        category.icon = "tag"

        return PurchaseChannelListView(viewModel: viewModel, category: category)
            .environmentObject(ThemeManager())
    }
}