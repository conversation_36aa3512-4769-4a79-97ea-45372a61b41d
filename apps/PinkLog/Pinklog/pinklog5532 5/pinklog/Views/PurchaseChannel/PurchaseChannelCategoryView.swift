import SwiftUI

struct PurchaseChannelCategoryView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @ObservedObject var viewModel: PurchaseChannelViewModel

    @State private var showingAddSheet = false
    @State private var editingCategory: PurchaseChannelCategory?
    @State private var categoryName = ""
    @State private var selectedIcon = "tag"
    @State private var showingIconPicker = false
    @State private var showingDeleteAlert = false
    @State private var categoryToDelete: PurchaseChannelCategory?
    @State private var animateIcon = false

    private let availableIcons = [
        "network", "building.2", "cart", "bag", "storefront", "creditcard", "globe",
        "house", "tag", "mappin.and.ellipse", "shippingbox", "flame", "star", "flag",
        "ellipsis.circle", "cart.fill", "bag.fill", "star.fill", "heart.fill", "gift"
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部添加按钮区域
                HStack {
                    Text("渠道类别")
                        .font(.headline)
                        .padding(.leading)

                    Spacer()

                    Button(action: {
                        editingCategory = nil
                        categoryName = ""
                        selectedIcon = "tag"
                        showingAddSheet = true
                        animateIcon = true

                        // 动画效果
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            animateIcon = false
                        }
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 16))
                                .scaleEffect(animateIcon ? 1.3 : 1.0)
                                .animation(.spring(response: 0.3), value: animateIcon)
                            Text("添加类别")
                                .font(.subheadline)
                        }
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                        .cornerRadius(20)
                    }
                    .padding(.trailing)
                }
                .padding(.vertical, 12)
                .background(Color(.systemBackground))

                // 类别列表
                List {
                    ForEach(viewModel.categories, id: \.self) { category in
                        NavigationLink(destination: PurchaseChannelListView(viewModel: viewModel, category: category)) {
                            HStack(spacing: 12) {
                                // 图标容器
                                ZStack {
                                    Circle()
                                        .fill(themeManager.currentTheme.primaryColor.opacity(0.15))
                                        .frame(width: 40, height: 40)

                                    Image(systemName: category.icon ?? "tag")
                                        .font(.system(size: 18))
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                }

                                VStack(alignment: .leading, spacing: 4) {
                                    Text(category.name ?? "未命名")
                                        .font(.body)

                                    if let channels = category.channels?.allObjects as? [PurchaseChannel] {
                                        Text("\(channels.count) 个渠道")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }

                                Spacer()

                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 8)
                        }
                        .contextMenu {
                            Button(action: {
                                editingCategory = category
                                categoryName = category.name ?? ""
                                selectedIcon = category.icon ?? "tag"
                                showingAddSheet = true
                            }) {
                                Label("编辑", systemImage: "pencil")
                            }

                            Button(role: .destructive, action: {
                                categoryToDelete = category
                                showingDeleteAlert = true
                            }) {
                                Label("删除", systemImage: "trash")
                            }
                        }
                        .swipeActions {
                            Button(role: .destructive) {
                                categoryToDelete = category
                                showingDeleteAlert = true
                            } label: {
                                Label("删除", systemImage: "trash")
                            }

                            Button {
                                editingCategory = category
                                categoryName = category.name ?? ""
                                selectedIcon = category.icon ?? "tag"
                                showingAddSheet = true
                            } label: {
                                Label("编辑", systemImage: "pencil")
                            }
                            .tint(.blue)
                        }
                    }
                }
            }
            .navigationTitle("购买渠道管理")
            .navigationBarItems(
                leading: Button("返回") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .sheet(isPresented: $showingAddSheet) {
                categoryFormView
            }
            .alert("确认删除", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) {}
                Button("删除", role: .destructive) {
                    if let category = categoryToDelete {
                        viewModel.deleteCategory(category)
                    }
                }
            } message: {
                Text("确定要删除类别\"" + (categoryToDelete?.name ?? "") + "\"吗？此操作将同时删除该类别下的所有渠道。")
            }
        }
    }

    var categoryFormView: some View {
        NavigationView {
            Form {
                Section {
                    VStack(alignment: .center, spacing: 16) {
                        // 图标预览
                        ZStack {
                            Circle()
                                .fill(themeManager.currentTheme.primaryColor.opacity(0.15))
                                .frame(width: 80, height: 80)

                            Image(systemName: selectedIcon)
                                .font(.system(size: 36))
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                        }
                        .onTapGesture {
                            withAnimation {
                                showingIconPicker.toggle()
                            }
                        }

                        Text("点击图标更换")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }

                Section {
                    TextField("类别名称", text: $categoryName)
                        .font(.body)
                        .padding(.vertical, 8)
                }

                if showingIconPicker {
                    Section("选择图标") {
                        LazyVGrid(columns: [GridItem(.adaptive(minimum: 50))], spacing: 16) {
                            ForEach(availableIcons, id: \.self) { icon in
                                Button(action: {
                                    withAnimation(.spring(response: 0.3)) {
                                        selectedIcon = icon
                                    }
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(selectedIcon == icon ?
                                                  themeManager.currentTheme.primaryColor.opacity(0.2) :
                                                  Color.gray.opacity(0.1))
                                            .frame(width: 50, height: 50)

                                        Image(systemName: icon)
                                            .font(.system(size: 22))
                                            .foregroundColor(selectedIcon == icon ?
                                                            themeManager.currentTheme.primaryColor : .primary)
                                    }
                                    .scaleEffect(selectedIcon == icon ? 1.1 : 1.0)
                                    .animation(.spring(response: 0.2), value: selectedIcon)
                                }
                            }
                        }
                        .padding(.vertical, 8)
                    }
                }
            }
            .navigationTitle(editingCategory == nil ? "添加渠道类别" : "编辑渠道类别")
            .navigationBarItems(
                leading: Button("取消") {
                    showingAddSheet = false
                },
                trailing: Button("保存") {
                    if !categoryName.isEmpty {
                        if let category = editingCategory {
                            viewModel.updateCategory(category, name: categoryName, icon: selectedIcon)
                        } else {
                            _ = viewModel.createCategory(name: categoryName, icon: selectedIcon)
                        }
                    }
                    showingAddSheet = false
                }
                .disabled(categoryName.isEmpty)
            )
        }
    }
}

// 预览
struct PurchaseChannelCategoryView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let viewModel = PurchaseChannelViewModel(context: context)

        return PurchaseChannelCategoryView(viewModel: viewModel)
            .environmentObject(ThemeManager())
    }
}