import SwiftUI

struct PurchaseChannelPickerView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @ObservedObject var viewModel: PurchaseChannelViewModel

    @Binding var selectedChannel: PurchaseChannel?
    @State private var searchText = ""
    @State private var showingCategoryManager = false
    @State private var showingAddChannelSheet = false
    @State private var selectedCategoryForAdd: PurchaseChannelCategory?
    @State private var channelName = ""
    @State private var channelURL = ""
    @State private var channelLocation = ""
    @State private var animateButton = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索框
                searchBar

                // 快速添加按钮
                HStack {
                    Spacer()

                    Button(action: {
                        // 显示添加渠道的操作表
                        if let firstCategory = viewModel.categories.first {
                            selectedCategoryForAdd = firstCategory
                            channelName = ""
                            channelURL = ""
                            channelLocation = ""
                            showingAddChannelSheet = true
                        }

                        animateButton = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            animateButton = false
                        }
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 16))
                                .scaleEffect(animateButton ? 1.3 : 1.0)
                                .animation(.spring(response: 0.3), value: animateButton)
                            Text("快速添加")
                                .font(.subheadline)
                        }
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                        .cornerRadius(20)
                    }
                    .padding(.trailing)
                    .padding(.vertical, 8)
                }

                // 列表内容
                channelListContent
            }
            .navigationTitle("选择购买渠道")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("管理") {
                    showingCategoryManager = true
                }
            )
            .onAppear {
                viewModel.fetchCategories()
            }
            .sheet(isPresented: $showingCategoryManager) {
                PurchaseChannelCategoryView(viewModel: viewModel)
                    .environmentObject(themeManager)
            }
            .sheet(isPresented: $showingAddChannelSheet) {
                addChannelView
            }
        }
    }

    // 添加渠道视图
    private var addChannelView: some View {
        NavigationView {
            Form {
                // 选择类别
                Section(header: Text("选择类别")) {
                    Picker("类别", selection: $selectedCategoryForAdd) {
                        ForEach(viewModel.categories, id: \.self) { category in
                            HStack {
                                Image(systemName: category.icon ?? "tag")
                                Text(category.name ?? "未命名")
                            }
                            .tag(category as PurchaseChannelCategory?)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }

                // 渠道信息
                Section(header: Text("渠道信息")) {
                    TextField("渠道名称", text: $channelName)
                        .font(.body)
                        .padding(.vertical, 4)

                    TextField("网址 (可选)", text: $channelURL)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                        .font(.body)
                        .padding(.vertical, 4)

                    TextField("位置 (可选)", text: $channelLocation)
                        .font(.body)
                        .padding(.vertical, 4)
                }
            }
            .navigationTitle("添加渠道")
            .navigationBarItems(
                leading: Button("取消") {
                    showingAddChannelSheet = false
                },
                trailing: Button("保存") {
                    if !channelName.isEmpty, let category = selectedCategoryForAdd {
                        if let newChannel = viewModel.addChannel(to: category, name: channelName, url: channelURL, location: channelLocation) {
                            selectedChannel = newChannel
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                    showingAddChannelSheet = false
                }
                .disabled(channelName.isEmpty || selectedCategoryForAdd == nil)
            )
        }
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索渠道", text: $searchText)
                .disableAutocorrection(true)

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                }
                .foregroundColor(.secondary)
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
    }

    // 列表内容
    private var channelListContent: some View {
        List {
            // 最常用的渠道
            Section {
                ForEach(filteredMostUsedChannels, id: \.self) { channel in
                    channelRow(channel)
                }

                if filteredMostUsedChannels.isEmpty && !searchText.isEmpty {
                    Text("没有匹配的常用渠道")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 8)
                }
            } header: {
                HStack {
                    Image(systemName: "star.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.yellow)
                    Text("常用渠道")
                        .font(.headline)
                }
            }

            // 按类别分组的渠道
            ForEach(viewModel.categories, id: \.self) { category in
                categorySection(category)
            }

            // 如果搜索没有结果
            if allFilteredChannels.isEmpty && !searchText.isEmpty {
                Section {
                    VStack(spacing: 12) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundColor(.secondary)
                            .padding()

                        Text("未找到匹配的渠道")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Button(action: {
                            searchText = ""
                        }) {
                            Text("清除搜索")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                        }
                        .padding(.top, 8)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
    }

    // 过滤后的最常用渠道
    private var filteredMostUsedChannels: [PurchaseChannel] {
        let mostUsed = viewModel.getMostUsedChannels()
        if searchText.isEmpty {
            return mostUsed
        } else {
            return mostUsed.filter { channel in
                let name = channel.name ?? ""
                let url = channel.url ?? ""
                let location = channel.location ?? ""

                return name.localizedCaseInsensitiveContains(searchText) ||
                       url.localizedCaseInsensitiveContains(searchText) ||
                       location.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // 所有过滤后的渠道（用于检查是否有搜索结果）
    private var allFilteredChannels: [PurchaseChannel] {
        let mostUsed = filteredMostUsedChannels
        let categoryChannels = viewModel.categories.compactMap { category -> [PurchaseChannel]? in
            guard let channels = category.channels?.allObjects as? [PurchaseChannel] else {
                return nil
            }
            return filteredChannels(for: channels)
        }.flatMap { $0 }

        return mostUsed + categoryChannels
    }

    // 类别分组
    private func categorySection(_ category: PurchaseChannelCategory) -> some View {
        Group {
            if let channels = category.channels?.allObjects as? [PurchaseChannel] {
                let filtered = filteredChannels(for: channels)
                if !filtered.isEmpty {
                    Section(header: categoryHeader(category)) {
                        ForEach(filtered, id: \.self) { channel in
                            channelRow(channel)
                        }
                    }
                }
            }
        }
    }

    // 类别标题
    private func categoryHeader(_ category: PurchaseChannelCategory) -> some View {
        HStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(themeManager.currentTheme.primaryColor.opacity(0.15))
                    .frame(width: 24, height: 24)

                Image(systemName: category.icon ?? "tag")
                    .font(.system(size: 12))
                    .foregroundColor(themeManager.currentTheme.primaryColor)
            }

            Text(category.name ?? "未命名")
                .font(.headline)
        }
    }

    private func channelRow(_ channel: PurchaseChannel) -> some View {
        Button(action: {
            selectedChannel = channel
            viewModel.incrementChannelUsageCount(channel)
            presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text(channel.name ?? "未命名")
                            .font(.body)

                        // 使用频率标签
                        if channel.usageCount > 0 {
                            HStack(spacing: 2) {
                                Image(systemName: "star.fill")
                                    .font(.system(size: 8))
                                Text("\(channel.usageCount)")
                                    .font(.system(size: 10))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 5)
                            .padding(.vertical, 2)
                            .background(frequencyColor(for: channel.usageCount))
                            .cornerRadius(8)
                        }
                    }

                    HStack(spacing: 12) {
                        if let url = channel.url, !url.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "link")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text(url)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        if let location = channel.location, !location.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "mappin")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text(location)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }

                Spacer()

                if selectedChannel == channel {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .font(.system(size: 20))
                }
            }
            .contentShape(Rectangle())
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 根据使用频率返回不同颜色
    private func frequencyColor(for count: Int32) -> Color {
        switch count {
        case 0...2:
            return Color.gray
        case 3...5:
            return Color.blue
        case 6...10:
            return Color.green
        case 11...20:
            return Color.orange
        default:
            return Color.red
        }
    }

    private func filteredChannels(for channels: [PurchaseChannel]) -> [PurchaseChannel] {
        let sortedChannels = channels.sorted { $0.usageCount > $1.usageCount }

        if searchText.isEmpty {
            return sortedChannels
        } else {
            return sortedChannels.filter { channel in
                let name = channel.name ?? ""
                let url = channel.url ?? ""
                let location = channel.location ?? ""

                return name.localizedCaseInsensitiveContains(searchText) ||
                       url.localizedCaseInsensitiveContains(searchText) ||
                       location.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// 预览
struct PurchaseChannelPickerView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let viewModel = PurchaseChannelViewModel(context: context)

        return PurchaseChannelPickerView(viewModel: viewModel, selectedChannel: .constant(nil))
            .environmentObject(ThemeManager())
    }
}