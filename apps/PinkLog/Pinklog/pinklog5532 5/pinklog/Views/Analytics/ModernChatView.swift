import SwiftUI
import MarkdownUI

// MARK: - 现代化AI助手主界面
struct ModernChatView: View {
    
    // MARK: - 状态管理
    @StateObject private var analyticsAssistant = AnalyticsAssistant()
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel
    @EnvironmentObject private var userProfileManager: UserProfileManager
    
    // UI状态
    @State private var showingSidebar = false
    @State private var inputText = ""
    @State private var isLoading = false
    @FocusState private var isInputFocused: Bool
    @State private var inputHeight: CGFloat = 56 // 默认2行高度

    // 导航状态
    @State private var selectedProduct: Product?

    // 消息状态
    @State private var messages: [ConversationMessage] = []
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 侧边栏
                if showingSidebar {
                    ChatSidebarView(
                        analyticsAssistant: analyticsAssistant,
                        showingSidebar: $showingSidebar
                    )
                    .frame(width: min(320, geometry.size.width * 0.8))
                    .transition(.move(edge: .leading))
                }
                
                // 主聊天区域
                mainChatArea
                    .frame(maxWidth: .infinity)
            }
        }
        .background(AppColors.background)
        .sheet(item: $selectedProduct) { product in
            ProductDetailView(product: product)
                .environmentObject(productViewModel)
                .environmentObject(usageViewModel)
        }
        .onAppear {
            setupAnalyticsAssistant()
        }
        .onReceive(analyticsAssistant.$conversationHistory) { newMessages in
            messages = newMessages
        }
        .onReceive(analyticsAssistant.$isProcessing) { processing in
            isLoading = processing
        }
    }
    
    // MARK: - 主聊天区域
    private var mainChatArea: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            topNavigationBar
            
            // 聊天内容区域
            if messages.isEmpty {
                welcomeView
            } else {
                chatMessagesView
            }
            
            // 底部输入区域
            chatInputArea
        }
    }
    
    // MARK: - 顶部导航栏
    private var topNavigationBar: some View {
        HStack {
            // 侧边栏按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingSidebar.toggle()
                }
            }) {
                Image(systemName: "sidebar.left")
                    .font(.title2)
                    .foregroundColor(AppColors.primary)
            }
            
            Spacer()
            
            // 当前对话标题
            VStack(spacing: 2) {
                Text(currentConversationTitle)
                    .font(AppFonts.headline)
                    .foregroundColor(AppColors.text)
                    .lineLimit(1)
                
                if !messages.isEmpty {
                    Text("\(messages.count) 条消息")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
            }
            
            Spacer()
            
            // 新对话按钮
            Button(action: startNewConversation) {
                Image(systemName: "plus.message")
                    .font(.title2)
                    .foregroundColor(AppColors.primary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(AppColors.surface)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(AppColors.border),
            alignment: .bottom
        )
    }
    
    // MARK: - 欢迎界面
    private var welcomeView: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                
                // 欢迎图标和标题
                VStack(spacing: 16) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 60))
                        .foregroundColor(AppColors.primary)
                    
                    VStack(spacing: 8) {
                        Text("PinkLog AI助手")
                            .font(AppFonts.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(AppColors.text)
                        
                        Text("智能分析您的消费数据，提供个性化洞察")
                            .font(AppFonts.body)
                            .foregroundColor(AppColors.secondaryText)
                            .multilineTextAlignment(.center)
                    }
                }
                
                // 快速开始建议
                quickStartSuggestions
                
                Spacer()
            }
            .padding(.horizontal, 24)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 快速开始建议
    private var quickStartSuggestions: some View {
        VStack(spacing: 16) {
            Text("试试这些问题")
                .font(AppFonts.headline)
                .foregroundColor(AppColors.text)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(suggestionQuestions, id: \.self) { question in
                    SuggestionCard(
                        question: question,
                        onTap: { sendSuggestion(question) }
                    )
                }
            }
        }
    }
    
    // MARK: - 聊天消息视图
    private var chatMessagesView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(messages, id: \.id) { message in
                        ModernMessageBubble(
                            message: message,
                            selectedProduct: $selectedProduct
                        )
                        .environmentObject(productViewModel)
                        .environmentObject(usageViewModel)
                        .id(message.id)
                    }

                    // 流式消息显示
                    if analyticsAssistant.isStreaming && !analyticsAssistant.streamingContent.isEmpty {
                        StreamingMessageView(content: analyticsAssistant.streamingContent)
                            .id("streaming")
                    } else if isLoading {
                        TypingIndicator()
                            .id("loading")
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
            .onChange(of: messages.count) {
                scrollToBottom(proxy: proxy)
            }
            .onChange(of: isLoading) {
                scrollToBottom(proxy: proxy)
            }
            .onChange(of: analyticsAssistant.streamingContent) {
                if analyticsAssistant.isStreaming {
                    scrollToBottom(proxy: proxy)
                }
            }
        }
    }
    
    // MARK: - 聊天输入区域
    private var chatInputArea: some View {
        VStack(spacing: 0) {
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(AppColors.border)
            
            HStack(alignment: .bottom, spacing: 12) {
                // 多行文本输入框
                ZStack(alignment: .topLeading) {
                    RoundedRectangle(cornerRadius: 20)
                        .fill(AppColors.surface)
                        .stroke(AppColors.border, lineWidth: 1)

                    if inputText.isEmpty {
                        Text("输入您的问题...")
                            .foregroundColor(AppColors.secondaryText)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                    }

                    TextEditor(text: $inputText)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.clear)
                        .font(AppFonts.body)
                        .scrollContentBackground(.hidden)
                        .focused($isInputFocused)
                        .onChange(of: inputText) {
                            updateInputHeight()
                        }
                }
                .frame(height: inputHeight)
                .animation(.easeInOut(duration: 0.2), value: inputHeight)
                
                // 发送按钮
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(canSend ? AppColors.primary : AppColors.secondaryText)
                }
                .disabled(!canSend)
                .animation(.easeInOut(duration: 0.2), value: canSend)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(AppColors.background)
        }
    }
    
    // MARK: - 计算属性
    private var currentConversationTitle: String {
        if let conversationId = analyticsAssistant.currentConversationId {
            let conversations = analyticsAssistant.getAllConversations()
            if let conversation = conversations.first(where: { $0.id == conversationId }) {
                return conversation.title ?? "新对话"
            }
        }
        return "AI助手"
    }
    
    private var canSend: Bool {
        !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isLoading
    }
    
    private var suggestionQuestions: [String] {
        [
            "分析我的消费习惯",
            "哪些产品最值得购买？",
            "我的使用频率如何？",
            "推荐优化建议"
        ]
    }
    
    // MARK: - 方法
    private func setupAnalyticsAssistant() {
        // 同步当前消息
        messages = analyticsAssistant.conversationHistory

        // 加载最近的对话
        let conversations = analyticsAssistant.getAllConversations()
        if conversations.isEmpty {
            // 如果没有对话，保持欢迎界面
        } else if analyticsAssistant.currentConversationId == nil {
            // 加载最近的对话
            if let latestConversation = conversations.first {
                analyticsAssistant.loadConversation(conversationId: latestConversation.id ?? UUID())
            }
        }
    }
    
    private func startNewConversation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            if let newConversationId = analyticsAssistant.createNewConversation() {
                analyticsAssistant.loadConversation(conversationId: newConversationId)
                inputText = ""
                showingSidebar = false
            }
        }
    }
    
    private func sendMessage() {
        guard canSend else { return }

        let message = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        inputText = ""

        // 收回键盘并重置输入框高度
        isInputFocused = false
        inputHeight = 56 // 重置为默认2行高度

        withAnimation(.easeInOut(duration: 0.3)) {
            isLoading = true
        }

        // 使用流式处理
        analyticsAssistant.processUserQuestionStream(message)

        // 如果是新对话的第一条消息，自动生成标题
        generateTitleIfNeeded(for: message)
    }
    
    private func sendSuggestion(_ question: String) {
        inputText = question
        // 收回键盘
        isInputFocused = false
        sendMessage()
    }
    
    private func scrollToBottom(proxy: ScrollViewProxy) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.3)) {
                if analyticsAssistant.isStreaming {
                    proxy.scrollTo("streaming", anchor: .bottom)
                } else if isLoading {
                    proxy.scrollTo("loading", anchor: .bottom)
                } else if let lastMessage = messages.last {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }
            }
        }
    }

    private func updateInputHeight() {
        let lineHeight: CGFloat = 20 // 单行文本高度
        let padding: CGFloat = 16 // 上下内边距
        let minHeight: CGFloat = 56 // 最小高度（2行）
        let maxHeight: CGFloat = 96 // 最大高度（4行）

        // 计算文本行数
        let lines = inputText.components(separatedBy: .newlines).count
        let calculatedHeight = CGFloat(lines) * lineHeight + padding

        // 限制在最小和最大高度之间
        let newHeight = max(minHeight, min(maxHeight, calculatedHeight))

        if newHeight != inputHeight {
            inputHeight = newHeight
        }
    }

    private func generateTitleIfNeeded(for message: String) {
        guard let conversationId = analyticsAssistant.currentConversationId,
              messages.count <= 2 else { return }

        let conversations = analyticsAssistant.getAllConversations()
        guard let conversation = conversations.first(where: { $0.id == conversationId }),
              conversation.title == nil || conversation.title == "新对话" else { return }

        // 使用消息的前30个字符作为标题，或者可以调用AI生成更智能的标题
        let title = String(message.prefix(30)) + (message.count > 30 ? "..." : "")

        // 这里可以扩展为调用AI生成更智能的标题
        _ = analyticsAssistant.renameConversation(
            conversationId: conversationId,
            newTitle: title
        )
    }
}

// StreamingMessageView已在AssistantChatView.swift中定义，这里不需要重复定义
