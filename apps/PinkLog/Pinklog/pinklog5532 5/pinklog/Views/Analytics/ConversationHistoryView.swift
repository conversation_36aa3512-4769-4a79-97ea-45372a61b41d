import SwiftUI

// MARK: - 会话历史界面
struct ConversationHistoryView: View {
    @StateObject private var viewModel: ConversationHistoryViewModel
    @State private var showingNewConversationAlert = false
    @State private var newConversationTitle = ""
    @State private var showingRenameAlert = false
    @State private var renameConversationTitle = ""
    @State private var conversationToRename: Conversation?
    
    // 初始化
    init(conversationRepository: ConversationRepository, messageRepository: MessageRepository) {
        self._viewModel = StateObject(wrappedValue: ConversationHistoryViewModel(
            conversationRepository: conversationRepository,
            messageRepository: messageRepository
        ))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 筛选和排序栏
                filterSortBar
                
                // 会话列表
                conversationList
            }
            .navigationTitle("会话历史")
            .navigationBarTitleDisplayMode(.large)
            .searchable(text: $viewModel.searchText, prompt: "搜索会话")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Picker("排序方式", selection: $viewModel.sortOption) {
                            ForEach(ConversationHistoryViewModel.SortOption.allCases) { option in
                                Text(option.rawValue).tag(option)
                            }
                        }
                        
                        Divider()
                        
                        Picker("筛选条件", selection: $viewModel.filterOption) {
                            ForEach(ConversationHistoryViewModel.FilterOption.allCases) { option in
                                Text(option.rawValue).tag(option)
                            }
                        }
                        
                        Divider()
                        
                        Button("重置筛选") {
                            viewModel.resetFilters()
                        }
                    } label: {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .foregroundColor(AppColors.primary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingNewConversationAlert = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(AppColors.primary)
                    }
                }
            }
            .refreshable {
                viewModel.refresh()
            }
            .alert("新建会话", isPresented: $showingNewConversationAlert) {
                TextField("会话标题（可选）", text: $newConversationTitle)
                Button("创建") {
                    let title = newConversationTitle.isEmpty ? nil : newConversationTitle
                    viewModel.createNewConversation(title: title)
                    newConversationTitle = ""
                }
                Button("取消", role: .cancel) {
                    newConversationTitle = ""
                }
            } message: {
                Text("为新会话输入一个标题，或留空使用默认标题")
            }
            .alert("重命名会话", isPresented: $showingRenameAlert) {
                TextField("新标题", text: $renameConversationTitle)
                Button("确定") {
                    if let conversation = conversationToRename {
                        viewModel.renameConversation(conversation, newTitle: renameConversationTitle)
                    }
                    renameConversationTitle = ""
                    conversationToRename = nil
                }
                Button("取消", role: .cancel) {
                    renameConversationTitle = ""
                    conversationToRename = nil
                }
            } message: {
                Text("输入新的会话标题")
            }
            .alert("删除确认", isPresented: $viewModel.showingDeleteAlert) {
                Button("删除", role: .destructive) {
                    if let conversation = viewModel.conversationToDelete {
                        viewModel.deleteConversation(conversation)
                    }
                }
                Button("取消", role: .cancel) {}
            } message: {
                Text("确定要删除这个会话吗？此操作无法撤销。")
            }
        }
        .onAppear {
            viewModel.loadConversations()
        }
        .overlay {
            if let errorMessage = viewModel.errorMessage {
                VStack {
                    Spacer()
                    HStack {
                        Text(errorMessage)
                            .foregroundColor(.white)
                            .padding()
                            .background(AppColors.error)
                            .cornerRadius(AppSizes.smallCornerRadius)
                        
                        Button("关闭") {
                            viewModel.clearError()
                        }
                        .foregroundColor(.white)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(AppColors.error.opacity(0.8))
                        .cornerRadius(AppSizes.smallCornerRadius)
                    }
                    .padding()
                }
                .transition(.move(edge: .bottom))
            }
        }
    }
    
    // MARK: - 筛选排序栏
    private var filterSortBar: some View {
        HStack {
            // 当前筛选状态指示
            if viewModel.filterOption != .all || viewModel.sortOption != .lastMessageDesc {
                HStack(spacing: 4) {
                    Image(systemName: "line.3.horizontal.decrease")
                        .font(.caption)
                        .foregroundColor(AppColors.primary)
                    
                    Text("\(viewModel.filterOption.rawValue) • \(viewModel.sortOption.rawValue)")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(AppColors.primary.opacity(0.1))
                .cornerRadius(AppSizes.smallCornerRadius)
            }
            
            Spacer()
            
            // 会话数量
            Text("\(viewModel.conversations.count) 个会话")
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
        }
        .padding(.horizontal, AppSizes.padding)
        .padding(.vertical, AppSizes.smallPadding)
        .background(AppColors.background)
    }
    
    // MARK: - 会话列表
    private var conversationList: some View {
        Group {
            if viewModel.isLoading {
                VStack {
                    Spacer()
                    ProgressView("加载中...")
                        .foregroundColor(AppColors.secondaryText)
                    Spacer()
                }
            } else if viewModel.conversations.isEmpty {
                emptyStateView
            } else {
                List {
                    ForEach(viewModel.conversations, id: \.id) { conversation in
                        ConversationRowView(
                            conversation: conversation,
                            viewModel: viewModel,
                            onRename: { conv in
                                conversationToRename = conv
                                renameConversationTitle = conv.title ?? ""
                                showingRenameAlert = true
                            }
                        )
                        .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
                        .listRowSeparator(.hidden)
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: AppSizes.padding) {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 60))
                .foregroundColor(AppColors.secondaryText.opacity(0.5))
            
            Text("暂无会话")
                .font(AppFonts.title2)
                .foregroundColor(AppColors.text)
            
            Text("点击右上角的 + 按钮开始新的对话")
                .font(AppFonts.body)
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
            
            Button(action: {
                showingNewConversationAlert = true
            }) {
                HStack {
                    Image(systemName: "plus")
                    Text("新建会话")
                }
                .primaryButtonStyle()
            }
        }
        .padding(AppSizes.largePadding)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 会话行视图
struct ConversationRowView: View {
    let conversation: Conversation
    let viewModel: ConversationHistoryViewModel
    let onRename: (Conversation) -> Void

    // 环境对象
    @EnvironmentObject private var analyticsAssistant: AnalyticsAssistant
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel
    
    var body: some View {
        NavigationLink(destination: AssistantChatView(
            conversationId: conversation.id,
            assistant: analyticsAssistant
        )
        .environmentObject(productViewModel)
        .environmentObject(usageViewModel)
        ) {
            VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                // 标题和时间
                HStack {
                    Text(conversation.title ?? "未命名会话")
                        .font(AppFonts.headline)
                        .foregroundColor(AppColors.text)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text(viewModel.formatDate(conversation.lastMessageDate))
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
                
                // 最后一条消息预览
                Text(viewModel.getLastMessagePreview(for: conversation))
                    .font(AppFonts.callout)
                    .foregroundColor(AppColors.secondaryText)
                    .lineLimit(2)
                
                // 消息数量和状态
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "message")
                            .font(.caption)
                        Text("\(viewModel.getMessageCount(for: conversation)) 条消息")
                            .font(AppFonts.caption)
                    }
                    .foregroundColor(AppColors.secondaryText)
                    
                    Spacer()
                    
                    if viewModel.getMessageCount(for: conversation) == 0 {
                        Text("空会话")
                            .font(AppFonts.caption)
                            .foregroundColor(AppColors.warning)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(AppColors.warning.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
            }
            .padding(AppSizes.padding)
            .background(AppColors.cardBackground)
            .cornerRadius(AppSizes.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
        .swipeActions(edge: .trailing) {
            Button(role: .destructive) {
                viewModel.conversationToDelete = conversation
                viewModel.showingDeleteAlert = true
            } label: {
                Label("删除", systemImage: "trash")
            }
            
            Button {
                onRename(conversation)
            } label: {
                Label("重命名", systemImage: "pencil")
            }
            .tint(AppColors.primary)
        }
        .contextMenu {
            Button(action: {
                onRename(conversation)
            }) {
                Label("重命名", systemImage: "pencil")
            }
            
            Button(role: .destructive, action: {
                viewModel.conversationToDelete = conversation
                viewModel.showingDeleteAlert = true
            }) {
                Label("删除", systemImage: "trash")
            }
        }
    }
}

// MARK: - 预览
struct ConversationHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let conversationRepo = ConversationRepository(context: context)
        let messageRepo = MessageRepository(context: context)
        
        ConversationHistoryView(
            conversationRepository: conversationRepo,
            messageRepository: messageRepo
        )
    }
}
