import SwiftUI

// MARK: - 聊天侧边栏
struct ChatSidebarView: View {
    
    @ObservedObject var analyticsAssistant: AnalyticsAssistant
    @Binding var showingSidebar: Bool

    @State private var showingDeleteAlert = false
    @State private var conversationToDelete: Conversation?
    @State private var editingConversation: Conversation?
    @State private var editingTitle = ""
    @State private var conversations: [Conversation] = []
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部区域
            sidebarHeader
            
            // 对话列表
            conversationsList
            
            // 底部区域
            sidebarFooter
        }
        .background(AppColors.surface)
        .overlay(
            Rectangle()
                .frame(width: 0.5)
                .foregroundColor(AppColors.border),
            alignment: .trailing
        )
        .onAppear {
            loadConversations()
        }
        .alert("删除对话", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let conversation = conversationToDelete {
                    deleteConversation(conversation)
                }
            }
        } message: {
            Text("确定要删除这个对话吗？此操作无法撤销。")
        }
        .sheet(item: $editingConversation) { conversation in
            EditConversationSheet(
                conversation: conversation,
                title: $editingTitle,
                onSave: { newTitle in
                    analyticsAssistant.renameConversation(
                        conversationId: conversation.id ?? UUID(),
                        newTitle: newTitle
                    )
                }
            )
        }
    }
    
    // MARK: - 侧边栏头部
    private var sidebarHeader: some View {
        VStack(spacing: 16) {
            // 关闭按钮
            HStack {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingSidebar = false
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.title3)
                        .foregroundColor(AppColors.secondaryText)
                }
                
                Spacer()
            }
            
            // 新对话按钮
            Button(action: createNewConversation) {
                HStack {
                    Image(systemName: "plus.message.fill")
                        .font(.title3)
                    
                    Text("新对话")
                        .font(AppFonts.headline)
                        .fontWeight(.medium)
                    
                    Spacer()
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 14)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColors.primary)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 8)
    }
    
    // MARK: - 对话列表
    private var conversationsList: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                if conversations.isEmpty {
                    emptyStateView
                } else {
                    ForEach(sortedConversations, id: \.id) { conversation in
                        SidebarConversationRowView(
                            conversation: conversation,
                            isSelected: isSelected(conversation),
                            onTap: { selectConversation(conversation) },
                            onEdit: { editConversation(conversation) },
                            onDelete: { deleteConversation(conversation) }
                        )
                    }
                }
            }
            .padding(.horizontal, 12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "message.circle")
                .font(.system(size: 40))
                .foregroundColor(AppColors.secondaryText)
            
            VStack(spacing: 8) {
                Text("还没有对话")
                    .font(AppFonts.headline)
                    .foregroundColor(AppColors.text)
                
                Text("点击上方按钮开始新对话")
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.secondaryText)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.vertical, 40)
    }
    
    // MARK: - 侧边栏底部
    private var sidebarFooter: some View {
        VStack(spacing: 0) {
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(AppColors.border)
            
            HStack {
                // 设置按钮
                Button(action: {
                    // TODO: 打开设置
                }) {
                    HStack {
                        Image(systemName: "gearshape")
                        Text("设置")
                    }
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.secondaryText)
                }
                
                Spacer()
                
                // 统计信息
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(conversations.count) 个对话")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)

                    Text("总计 \(totalMessageCount) 条消息")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .background(AppColors.surface)
    }
    
    // MARK: - 计算属性
    private var sortedConversations: [Conversation] {
        conversations.sorted { (conversation1: Conversation, conversation2: Conversation) -> Bool in
            let date1 = conversation1.lastMessageDate ?? Date.distantPast
            let date2 = conversation2.lastMessageDate ?? Date.distantPast
            return date1 > date2
        }
    }

    private var totalMessageCount: Int {
        conversations.reduce(0) { total, conversation in
            total + (conversation.messages?.count ?? 0)
        }
    }
    
    // MARK: - 方法
    private func loadConversations() {
        conversations = analyticsAssistant.getAllConversations()
    }

    private func isSelected(_ conversation: Conversation) -> Bool {
        conversation.id == analyticsAssistant.currentConversationId
    }
    
    private func createNewConversation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            if let newConversationId = analyticsAssistant.createNewConversation() {
                analyticsAssistant.loadConversation(conversationId: newConversationId)
                showingSidebar = false
            }
        }
    }
    
    private func selectConversation(_ conversation: Conversation) {
        withAnimation(.easeInOut(duration: 0.3)) {
            analyticsAssistant.loadConversation(conversationId: conversation.id ?? UUID())
            showingSidebar = false
        }
    }
    
    private func editConversation(_ conversation: Conversation) {
        editingTitle = conversation.title ?? "新对话"
        editingConversation = conversation
    }
    
    private func deleteConversation(_ conversation: Conversation) {
        conversationToDelete = conversation
        showingDeleteAlert = true
    }
}

// MARK: - 侧边栏对话行视图
struct SidebarConversationRowView: View {
    let conversation: Conversation
    let isSelected: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @State private var showingContextMenu = false
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 对话图标
                Image(systemName: "message")
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : AppColors.primary)
                    .frame(width: 20)
                
                // 对话信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(conversation.title ?? "新对话")
                        .font(AppFonts.body)
                        .fontWeight(isSelected ? .medium : .regular)
                        .foregroundColor(isSelected ? .white : AppColors.text)
                        .lineLimit(1)
                    
                    HStack {
                        Text(lastMessagePreview)
                            .font(AppFonts.caption)
                            .foregroundColor(isSelected ? .white.opacity(0.8) : AppColors.secondaryText)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text(formattedDate)
                            .font(AppFonts.caption)
                            .foregroundColor(isSelected ? .white.opacity(0.8) : AppColors.secondaryText)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? AppColors.primary : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            Button(action: onEdit) {
                Label("重命名", systemImage: "pencil")
            }
            
            Button(action: onDelete) {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    private var lastMessagePreview: String {
        guard let messages = conversation.messages?.allObjects as? [Message],
              let lastMessage = messages.sorted(by: { $0.timestamp ?? Date() < $1.timestamp ?? Date() }).last else {
            return "暂无消息"
        }
        
        let content = lastMessage.text ?? ""
        return String(content.prefix(30)) + (content.count > 30 ? "..." : "")
    }
    
    private var formattedDate: String {
        let date = conversation.lastMessageDate ?? Date()
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - 编辑对话标题Sheet
struct EditConversationSheet: View {
    let conversation: Conversation
    @Binding var title: String
    let onSave: (String) -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                TextField("对话标题", text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(AppFonts.body)
                
                Spacer()
            }
            .padding()
            .navigationTitle("重命名对话")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        onSave(title)
                        dismiss()
                    }
                    .disabled(title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}
