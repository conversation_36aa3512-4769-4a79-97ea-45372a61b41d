import SwiftUI
import MarkdownUI

// MARK: - 现代化消息气泡
struct ModernMessageBubble: View {

    let message: ConversationMessage
    @Binding var selectedProduct: Product?
    
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel
    @EnvironmentObject private var userProfileManager: UserProfileManager
    
    @State private var showingCopyFeedback = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.role == .user {
                Spacer()
                userMessageView
                userAvatarView
            } else {
                assistantMessageView
                Spacer()
            }
        }
        .onAppear {
            // 消息加载完成
        }
    }
    
    // MARK: - 用户消息视图
    private var userMessageView: some View {
        VStack(alignment: .trailing, spacing: 8) {
            // 消息内容
            Text(message.content)
                .font(AppFonts.body)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 18)
                        .fill(AppColors.primary)
                )
                .contextMenu {
                    Button(action: { copyMessage() }) {
                        Label("复制", systemImage: "doc.on.doc")
                    }
                }
            
            // 时间戳
            Text(formattedTimestamp)
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .trailing)
    }

    // MARK: - 用户头像
    private var userAvatarView: some View {
        Image(uiImage: userProfileManager.displayAvatar)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 32, height: 32)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(AppColors.border, lineWidth: 1)
            )
    }
    
    // MARK: - AI助手消息视图
    private var assistantMessageView: some View {
        HStack(alignment: .top, spacing: 12) {
            // AI头像
            aiAvatarView
            
            // 消息内容区域
            VStack(alignment: .leading, spacing: 12) {
                // 消息内容
                messageContentView
                
                // 快捷操作按钮（暂时移除）
                // if !actionButtons.isEmpty {
                //     actionButtonsView
                // }
                
                // 消息操作栏
                messageActionsView
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .leading)
        }
    }
    
    // MARK: - AI头像
    private var aiAvatarView: some View {
        Image("pinkbot-logo")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 32, height: 32)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(AppColors.border, lineWidth: 1)
            )
    }
    
    // MARK: - 消息内容
    private var messageContentView: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 处理产品链接的内容
            let contentWithProductLinks = ProductLinkProcessor.processProductLinks(
                in: message.content,
                with: productViewModel.products
            )

            if containsMarkdown(contentWithProductLinks) || contentWithProductLinks != message.content {
                Markdown(contentWithProductLinks)
                    .markdownTheme(modernMarkdownTheme)
                    .environment(\.openURL, OpenURLAction { url in
                        return handleLinkClick(url)
                    })
            } else {
                Text(contentWithProductLinks)
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.text)
            }
            
            // 时间戳
            Text(formattedTimestamp)
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 18)
                .fill(AppColors.surface)
                .stroke(AppColors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - 快捷操作按钮（暂时移除）
    // private var actionButtonsView: some View {
    //     ScrollView(.horizontal, showsIndicators: false) {
    //         HStack(spacing: 8) {
    //             // 快捷操作按钮
    //         }
    //         .padding(.horizontal, 16)
    //     }
    // }
    
    // MARK: - 消息操作栏
    private var messageActionsView: some View {
        HStack(spacing: 16) {
            // 复制按钮
            Button(action: copyMessage) {
                HStack(spacing: 4) {
                    Image(systemName: showingCopyFeedback ? "checkmark" : "doc.on.doc")
                        .font(.caption)
                    Text(showingCopyFeedback ? "已复制" : "复制")
                        .font(AppFonts.caption)
                }
                .foregroundColor(AppColors.secondaryText)
            }
            .buttonStyle(PlainButtonStyle())
            
            // 重新生成按钮
            Button(action: regenerateResponse) {
                HStack(spacing: 4) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                    Text("重新生成")
                        .font(AppFonts.caption)
                }
                .foregroundColor(AppColors.secondaryText)
            }
            .buttonStyle(PlainButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - 自定义Markdown主题
    private var modernMarkdownTheme: Theme {
        Theme()
            .text {
                FontSize(16)
                ForegroundColor(AppColors.text)
            }
            .code {
                FontFamilyVariant(.monospaced)
                FontSize(.em(0.9))
                ForegroundColor(AppColors.primary)
                BackgroundColor(AppColors.surface)
            }
            .strong {
                FontWeight(.semibold)
            }
            .emphasis {
                FontStyle(.italic)
            }
            .link {
                ForegroundColor(AppColors.primary)
                UnderlineStyle(.single)
                FontWeight(.medium)
            }
            .heading1 { configuration in
                VStack(alignment: .leading, spacing: 0) {
                    configuration.label
                        .relativePadding(.bottom, length: .em(0.3))
                        .relativeLineSpacing(.em(0.125))
                        .markdownMargin(top: .zero, bottom: .em(0.8))
                        .markdownTextStyle {
                            FontWeight(.semibold)
                            FontSize(.em(1.5))
                        }
                    Rectangle()
                        .fill(AppColors.border)
                        .relativeFrame(height: .em(0.05))
                }
            }
            .heading2 { configuration in
                configuration.label
                    .fontWeight(.semibold)
                    .font(.system(size: 20))
                    .foregroundColor(AppColors.text)
            }
            .listItem { configuration in
                configuration.label
                    .markdownMargin(top: .em(0.25))
            }
            .codeBlock { configuration in
                configuration.label
                    .relativePadding(.all, length: .em(1))
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        FontSize(.em(0.85))
                    }
                    .background(AppColors.surface)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .markdownMargin(top: .em(0.8), bottom: .em(0.8))
            }
    }
    
    // MARK: - 计算属性
    private var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: message.timestamp)
    }
    
    // MARK: - 方法
    // private func parseActionButtons() {
    //     // 暂时移除快捷操作按钮功能
    // }
    
    private func containsMarkdown(_ text: String) -> Bool {
        let markdownPatterns = [
            "\\*\\*.*?\\*\\*",  // 粗体
            "\\*.*?\\*",        // 斜体
            "`.*?`",            // 行内代码
            "```[\\s\\S]*?```", // 代码块
            "\\[.*?\\]\\(.*?\\)", // 链接
            "^#{1,6}\\s",       // 标题
            "^\\s*[-*+]\\s",    // 列表
            "^\\s*\\d+\\.\\s"   // 有序列表
        ]
        
        for pattern in markdownPatterns {
            if text.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }
        return false
    }
    
    private func handleLinkClick(_ url: URL) -> OpenURLAction.Result {
        // 检查是否是产品链接
        if let productId = ProductLinkProcessor.parseProductURL(url) {
            if let product = ProductLinkProcessor.findProduct(by: productId, in: productViewModel.products) {
                DispatchQueue.main.async {
                    selectedProduct = product
                }
                return .handled
            } else {
                print("Product not found for ID: \(productId)")
                return .handled
            }
        }
        
        // 其他链接使用系统默认处理
        print("Opening external URL: \(url)")
        return .systemAction
    }
    
    private func copyMessage() {
        UIPasteboard.general.string = message.content
        
        withAnimation(.easeInOut(duration: 0.2)) {
            showingCopyFeedback = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showingCopyFeedback = false
            }
        }
    }
    
    private func regenerateResponse() {
        // TODO: 实现重新生成响应的功能
        print("重新生成响应")
    }
}

// MARK: - 输入指示器
struct TypingIndicator: View {
    @State private var animationPhase = 0
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // AI头像
            Image("pinkbot-logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(AppColors.border, lineWidth: 1)
                )
            
            // 输入动画
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(AppColors.secondaryText)
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationPhase
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 18)
                    .fill(AppColors.surface)
                    .stroke(AppColors.border, lineWidth: 0.5)
            )
            
            Spacer()
        }
        .onAppear {
            withAnimation {
                animationPhase = 1
            }
        }
    }
}

// MARK: - 建议卡片
struct SuggestionCard: View {
    let question: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(question)
                .font(AppFonts.body)
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.leading)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColors.surface)
                        .stroke(AppColors.border, lineWidth: 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
