import SwiftUI
import MarkdownUI

// MARK: - AI助手对话界面
struct AssistantChatView: View {
    @ObservedObject private var assistant: AnalyticsAssistant
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var messageText: String = ""
    @State private var showingSettings: Bool = false
    @State private var showingCostInfo: Bool = false
    @State private var showingConversationHistory: Bool = false
    @State private var showingTitleEditor: Bool = false
    @State private var editingTitle: String = ""
    @State private var currentConversationTitle: String = "AI智能助手"
    @FocusState private var isTextFieldFocused: Bool

    let analysisContext: AnalysisContext?
    let conversationId: UUID?

    init(analysisContext: AnalysisContext? = nil, conversationId: UUID? = nil, assistant: AnalyticsAssistant? = nil) {
        self.analysisContext = analysisContext
        self.conversationId = conversationId
        self.assistant = assistant ?? AnalyticsAssistant()
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 对话历史
                conversationScrollView
                
                // 输入区域
                messageInputArea
            }
            .navigationTitle(currentConversationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    HStack {
                        Button(action: {
                            showingConversationHistory = true
                        }) {
                            Image(systemName: "clock.arrow.circlepath")
                                .foregroundColor(AppColors.primary)
                        }

                        Button("清除") {
                            assistant.clearConversation()
                            updateConversationTitle()
                        }
                        .foregroundColor(AppColors.primary)
                    }
                }

                ToolbarItem(placement: .principal) {
                    Button(action: {
                        editingTitle = currentConversationTitle
                        showingTitleEditor = true
                    }) {
                        HStack {
                            Text(currentConversationTitle)
                                .font(.headline)
                                .foregroundColor(AppColors.text)
                                .lineLimit(1)

                            Image(systemName: "pencil")
                                .font(.caption)
                                .foregroundColor(AppColors.secondaryText)
                        }
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(action: {
                            showingCostInfo.toggle()
                        }) {
                            Image(systemName: "chart.bar.fill")
                                .foregroundColor(assistant.costTracker.isOverBudget ? .red : AppColors.primary)
                        }

                        Button(action: {
                            showingSettings.toggle()
                        }) {
                            Image(systemName: "gear")
                                .foregroundColor(AppColors.primary)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                AssistantSettingsView(assistant: assistant)
            }
            .sheet(isPresented: $showingCostInfo) {
                CostTrackingView(costTracker: assistant.costTracker)
            }
            .sheet(isPresented: $showingConversationHistory) {
                ConversationHistoryView(
                    conversationRepository: ConversationRepository(context: PersistenceController.shared.container.viewContext),
                    messageRepository: MessageRepository(context: PersistenceController.shared.container.viewContext)
                )
            }
            .alert("编辑会话标题", isPresented: $showingTitleEditor) {
                TextField("会话标题", text: $editingTitle)
                Button("确定") {
                    updateConversationTitle(newTitle: editingTitle)
                }
                Button("取消", role: .cancel) {
                    editingTitle = ""
                }
            } message: {
                Text("输入新的会话标题")
            }
            .onAppear {
                if let context = analysisContext {
                    assistant.setAnalysisContext(context)
                }

                // 如果指定了会话ID，加载该会话
                if let conversationId = conversationId {
                    assistant.loadConversation(conversationId: conversationId)
                    updateConversationTitle()
                } else {
                    // 如果没有指定会话ID，确保有当前会话
                    if assistant.currentConversationId == nil {
                        _ = assistant.createNewConversation()
                    }
                    updateConversationTitle()
                }
            }
        }
    }
    
    // MARK: - 对话滚动视图
    private var conversationScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: AppSizes.padding) {
                    if assistant.conversationHistory.isEmpty {
                        welcomeMessage
                    } else {
                        ForEach(assistant.conversationHistory) { message in
                            MessageBubbleView(message: message, markdownEnabled: assistant.markdownRenderingEnabled)
                                .id(message.id)
                        }
                    }
                    
                    if assistant.isStreaming && !assistant.streamingContent.isEmpty {
                        StreamingMessageView(content: assistant.streamingContent)
                            .id("streaming")
                    } else if assistant.isProcessing {
                        TypingIndicatorView()
                            .id("typing")
                    }
                }
                .padding()
            }
            .onChange(of: assistant.conversationHistory.count) { _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    if let lastMessage = assistant.conversationHistory.last {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
            .onChange(of: assistant.isProcessing) { isProcessing in
                if isProcessing {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        if assistant.isStreaming {
                            proxy.scrollTo("streaming", anchor: .bottom)
                        } else {
                            proxy.scrollTo("typing", anchor: .bottom)
                        }
                    }
                }
            }
            .onChange(of: assistant.streamingContent) { _ in
                if assistant.isStreaming {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        proxy.scrollTo("streaming", anchor: .bottom)
                    }
                }
            }
        }
    }
    
    // MARK: - 欢迎消息
    private var welcomeMessage: some View {
        VStack(spacing: AppSizes.padding) {
            Image("pinkbot-logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(AppColors.primary, lineWidth: 2)
                )

            Text("AI智能助手")
                .font(AppFonts.title2)
                .fontWeight(.bold)
                .foregroundColor(AppColors.text)

            Text("我可以帮您分析消费数据、解读三曲线分析结果、提供个性化建议。请随时向我提问！")
                .font(AppFonts.body)
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 快速问题建议
            VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                Text("您可以这样问我：")
                    .font(AppFonts.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppColors.text)
                
                ForEach(suggestedQuestions, id: \.self) { question in
                    Button(action: {
                        messageText = question
                        sendMessage()
                    }) {
                        HStack {
                            Text("• \(question)")
                                .font(AppFonts.caption)
                                .foregroundColor(AppColors.secondaryText)
                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                    .fill(AppColors.background.opacity(0.5))
            )
        }
        .padding()
    }
    
    // MARK: - 消息输入区域
    private var messageInputArea: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: AppSizes.smallPadding) {
                TextField("输入您的问题...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($isTextFieldFocused)
                    .onSubmit {
                        sendMessage()
                    }
                
                Button(action: sendMessage) {
                    Image(systemName: "paperplane.fill")
                        .font(.system(size: 20))
                        .foregroundColor(messageText.isEmpty ? AppColors.secondaryText : AppColors.primary)
                }
                .disabled(messageText.isEmpty || assistant.isProcessing)
            }
            .padding()
        }
        .background(AppColors.cardBackground)
    }
    
    // MARK: - 建议问题
    private var suggestedQuestions: [String] {
        if let context = analysisContext {
            switch context.analysisType {
            case .threeCurve:
                return [
                    "这个产品的三曲线分析结果如何？",
                    "我应该继续使用这个产品吗？",
                    "如何提高这个产品的价值？"
                ]
            case .valueRealization:
                return [
                    "这个产品的投资回报如何？",
                    "什么时候是最佳处置时机？",
                    "如何提高价值实现效率？"
                ]
            default:
                return defaultQuestions
            }
        } else {
            return defaultQuestions
        }
    }
    
    private var defaultQuestions: [String] {
        return [
            "我的消费习惯有什么特点？",
            "哪些产品表现最好？",
            "有什么优化建议？",
            "本月的消费分析如何？"
        ]
    }
    
    // MARK: - 发送消息
    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let question = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        messageText = ""
        isTextFieldFocused = false

        // 使用流式传输
        assistant.processUserQuestionStream(question, context: analysisContext)
    }
}

// MARK: - 消息气泡视图
struct MessageBubbleView: View {
    let message: ConversationMessage
    let markdownEnabled: Bool
    @StateObject private var actionExecutor = ActionExecutor()
    @State private var actionButtons: [ActionButton] = []
    @State private var processedContent: String = ""

    // 环境对象
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel

    // 导航状态
    @State private var selectedProduct: Product?

    init(message: ConversationMessage, markdownEnabled: Bool = true) {
        self.message = message
        self.markdownEnabled = markdownEnabled
    }

    var body: some View {
        HStack {
            if message.role == .user {
                Spacer()
            }

            VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 4) {
                VStack(alignment: .leading, spacing: 8) {
                    messageContentView
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(message.role == .user ? AppColors.primary : AppColors.cardBackground)
                        )

                    // 动作按钮（仅对AI消息显示）
                    if message.role != .user && !actionButtons.isEmpty {
                        actionButtonsView
                    }
                }

                Text(formatTimestamp(message.timestamp))
                    .font(AppFonts.caption2)
                    .foregroundColor(AppColors.secondaryText)
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: message.role == .user ? .trailing : .leading)

            if message.role != .user {
                Spacer()
            }
        }
        .onAppear {
            // 解析AI消息中的动作按钮
            if message.role != .user {
                actionButtons = ActionButtonParser.parseActions(from: message.content)
                // 设置ActionExecutor的依赖
                actionExecutor.setDependencies(
                    productViewModel: productViewModel,
                    usageViewModel: usageViewModel
                )
            }
        }
        .overlay(alignment: .topTrailing) {
            // 执行反馈
            if actionExecutor.showingExecutionFeedback {
                executionFeedbackView
            }
        }
        .sheet(item: $selectedProduct) { product in
            ProductDetailView(product: product)
                .environmentObject(productViewModel)
                .environmentObject(usageViewModel)
        }
    }

    @ViewBuilder
    private var messageContentView: some View {
        if message.role == .user {
            // 用户消息始终使用纯文本
            Text(message.content)
                .font(AppFonts.body)
                .foregroundColor(.white)
        } else {
            // AI消息支持Markdown渲染和产品链接
            let cleanedContent = ActionButtonParser.cleanContent(message.content)
            let contentWithProductLinks = ProductLinkProcessor.processProductLinks(
                in: cleanedContent,
                with: productViewModel.products
            )

            if markdownEnabled && (containsMarkdown(contentWithProductLinks) || contentWithProductLinks != cleanedContent) {
                Markdown(contentWithProductLinks)
                    .markdownTheme(customMarkdownTheme)
                    .environment(\.openURL, OpenURLAction { url in
                        return handleLinkClick(url)
                    })
            } else {
                Text(contentWithProductLinks)
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.text)
            }
        }
    }

    // 检测文本是否包含Markdown语法
    private func containsMarkdown(_ text: String) -> Bool {
        let markdownPatterns = [
            "\\*\\*.*?\\*\\*",      // 粗体 **text**
            "\\*.*?\\*",           // 斜体 *text*
            "`.*?`",               // 行内代码 `code`
            "```[\\s\\S]*?```",    // 代码块 ```code```
            "^#{1,6}\\s",          // 标题 # ## ###
            "^\\s*[-*+]\\s",       // 列表 - * +
            "^\\s*\\d+\\.\\s",     // 有序列表 1. 2.
            "\\[.*?\\]\\(.*?\\)",  // 链接 [text](url)
            "^>\\s"                // 引用 >
        ]

        for pattern in markdownPatterns {
            if text.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }
        return false
    }

    // 自定义Markdown主题，与应用主题保持一致
    private var customMarkdownTheme: Theme {
        Theme()
            .text {
                ForegroundColor(Color(AppColors.text))
                FontSize(16)
            }
            .code {
                FontFamilyVariant(.monospaced)
                FontSize(.em(0.9))
                ForegroundColor(Color(AppColors.primary))
                BackgroundColor(Color(AppColors.primary.opacity(0.1)))
            }
            .strong {
                FontWeight(.semibold)
                ForegroundColor(Color(AppColors.text))
            }
            .emphasis {
                FontStyle(.italic)
                ForegroundColor(Color(AppColors.text))
            }
            .link {
                ForegroundColor(Color(AppColors.primary))
                UnderlineStyle(.single)
                FontWeight(.medium)
            }
            .heading1 { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.5), bottom: .rem(0.5))
                    .markdownTextStyle {
                        FontWeight(.bold)
                        FontSize(.em(1.5))
                        ForegroundColor(Color(AppColors.text))
                    }
            }
            .heading2 { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.4), bottom: .rem(0.4))
                    .markdownTextStyle {
                        FontWeight(.semibold)
                        FontSize(.em(1.3))
                        ForegroundColor(Color(AppColors.text))
                    }
            }
            .heading3 { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.3), bottom: .rem(0.3))
                    .markdownTextStyle {
                        FontWeight(.semibold)
                        FontSize(.em(1.1))
                        ForegroundColor(Color(AppColors.text))
                    }
            }
            .paragraph { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.2), bottom: .rem(0.2))
            }
            .listItem { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.1))
            }
            .codeBlock { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.3), bottom: .rem(0.3))
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        FontSize(.em(0.85))
                        ForegroundColor(Color(AppColors.text))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(AppColors.cardBackground))
                    .cornerRadius(8)
            }
            .blockquote { configuration in
                configuration.label
                    .markdownMargin(top: .rem(0.3), bottom: .rem(0.3))
                    .padding(.leading, 12)
                    .overlay(alignment: .leading) {
                        Rectangle()
                            .fill(Color(AppColors.primary))
                            .frame(width: 3)
                    }
                    .markdownTextStyle {
                        ForegroundColor(Color(AppColors.secondaryText))
                        FontStyle(.italic)
                    }
            }
    }

    // MARK: - 动作按钮视图
    @ViewBuilder
    private var actionButtonsView: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8) {
            ForEach(actionButtons) { action in
                Button(action: {
                    actionExecutor.executeAction(action)
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: action.type.icon)
                            .font(.caption)
                            .foregroundColor(action.type.color)

                        Text(action.title)
                            .font(AppFonts.caption)
                            .foregroundColor(AppColors.text)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(action.type.color.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(action.type.color.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(actionExecutor.isExecuting)
                .opacity(actionExecutor.isExecuting ? 0.6 : 1.0)
                .scaleEffect(actionExecutor.isExecuting ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: actionExecutor.isExecuting)
            }
        }
        .padding(.horizontal, 4)
    }

    // MARK: - 执行反馈视图
    @ViewBuilder
    private var executionFeedbackView: some View {
        if let result = actionExecutor.lastExecutionResult {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.caption)

                Text(result)
                    .font(AppFonts.caption2)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.black.opacity(0.8))
            )
            .offset(x: -10, y: -10)
            .transition(.scale.combined(with: .opacity))
        }
    }

    // MARK: - 链接处理方法

    /// 处理链接点击
    private func handleLinkClick(_ url: URL) -> OpenURLAction.Result {
        // 检查是否是产品链接
        if let productId = ProductLinkProcessor.parseProductURL(url) {
            if let product = ProductLinkProcessor.findProduct(by: productId, in: productViewModel.products) {
                // 使用DispatchQueue.main.async确保在主线程上更新UI状态
                DispatchQueue.main.async {
                    selectedProduct = product
                }
                return .handled
            } else {
                print("Product not found for ID: \(productId)")
                return .handled
            }
        }

        // 其他链接使用系统默认处理
        print("Opening external URL: \(url)")
        return .systemAction
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - 输入指示器
struct TypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        HStack {
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(AppColors.secondaryText)
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppColors.cardBackground)
            )
            
            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

// MARK: - 助手设置视图
struct AssistantSettingsView: View {
    @ObservedObject var assistant: AnalyticsAssistant
    @Environment(\.dismiss) private var dismiss
    @State private var apiKey: String = ""
    @State private var budgetLimit: String = ""
    @State private var showingAPIKeyInput: Bool = false

    var body: some View {
        NavigationView {
            Form {
                Section("API配置") {
                    HStack {
                        Text("DeepSeek API")
                        Spacer()
                        if assistant.deepSeekService.isConfigured {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Button("配置") {
                                showingAPIKeyInput = true
                            }
                        }
                    }

                    if assistant.deepSeekService.isConfigured {
                        Button("重新配置API Key") {
                            showingAPIKeyInput = true
                        }
                        .foregroundColor(AppColors.primary)
                    }
                }

                Section("成本控制") {
                    HStack {
                        Text("月度预算")
                        Spacer()
                        Text("¥\(String(format: "%.2f", assistant.costTracker.monthlyBudget))")
                            .foregroundColor(AppColors.secondaryText)
                    }

                    HStack {
                        Text("已使用")
                        Spacer()
                        Text("¥\(String(format: "%.2f", assistant.costTracker.totalCost))")
                            .foregroundColor(assistant.costTracker.isOverBudget ? .red : AppColors.secondaryText)
                    }

                    Button("重置成本追踪") {
                        assistant.resetCostTracking()
                    }
                    .foregroundColor(.red)
                }

                Section("功能设置") {
                    Toggle("Markdown渲染", isOn: $assistant.markdownRenderingEnabled)

                    NavigationLink("主动提醒设置") {
                        ProactiveServiceSettingsView()
                    }

                    Toggle("离线模式", isOn: .constant(false))
                        .disabled(true)

                    Toggle("自动生成月报", isOn: .constant(true))
                        .disabled(true)
                }

                if assistant.markdownRenderingEnabled {
                    Section("Markdown说明") {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("AI助手支持以下Markdown格式：")
                                .font(AppFonts.caption)
                                .foregroundColor(AppColors.secondaryText)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("• **粗体文本** 和 *斜体文本*")
                                Text("• `行内代码` 和代码块")
                                Text("• # 标题 和 ## 子标题")
                                Text("• - 列表项 和 1. 有序列表")
                                Text("• [链接](URL) 和 > 引用")
                            }
                            .font(AppFonts.caption2)
                            .foregroundColor(AppColors.secondaryText)
                        }
                    }
                }


            }
            .navigationTitle("助手设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingAPIKeyInput) {
            APIKeyInputView(assistant: assistant)
        }
    }
}

// MARK: - API Key输入视图
struct APIKeyInputView: View {
    @ObservedObject var assistant: AnalyticsAssistant
    @Environment(\.dismiss) private var dismiss
    @State private var apiKey: String = ""
    @State private var isConfiguring: Bool = false

    var body: some View {
        NavigationView {
            VStack(spacing: AppSizes.padding) {
                VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                    Text("配置DeepSeek API Key")
                        .font(AppFonts.headline)
                        .fontWeight(.bold)

                    Text("请输入您的DeepSeek API Key以启用AI助手功能")
                        .font(AppFonts.body)
                        .foregroundColor(AppColors.secondaryText)
                }

                VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                    Text("API Key")
                        .font(AppFonts.caption)
                        .fontWeight(.medium)

                    SecureField("sk-...", text: $apiKey)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                    Text("获取API Key")
                        .font(AppFonts.caption)
                        .fontWeight(.medium)

                    Text("1. 访问 https://platform.deepseek.com")
                    Text("2. 注册并登录账户")
                    Text("3. 在API Keys页面创建新的API Key")
                    Text("4. 复制并粘贴到上方输入框")
                }
                .font(AppFonts.caption2)
                .foregroundColor(AppColors.secondaryText)

                Spacer()

                Button(action: configureAPI) {
                    HStack {
                        if isConfiguring {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text(isConfiguring ? "配置中..." : "保存配置")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(AppColors.primary)
                    .foregroundColor(.white)
                    .cornerRadius(AppSizes.cornerRadius)
                }
                .disabled(apiKey.isEmpty || isConfiguring)
            }
            .padding()
            .navigationTitle("API配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func configureAPI() {
        isConfiguring = true

        Task {
            await assistant.deepSeekService.configure(apiKey: apiKey)

            await MainActor.run {
                isConfiguring = false
                if assistant.deepSeekService.isConfigured {
                    dismiss()
                }
            }
        }
    }
}

// MARK: - 成本追踪视图
struct CostTrackingView: View {
    @ObservedObject var costTracker: CostTracker
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: AppSizes.padding) {
                // 预算概览
                budgetOverviewCard

                // 使用统计
                usageStatisticsCard

                // 使用历史
                usageHistoryList

                Spacer()
            }
            .padding()
            .navigationTitle("成本追踪")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var budgetOverviewCard: some View {
        VStack(spacing: AppSizes.smallPadding) {
            HStack {
                Text("月度预算")
                    .font(AppFonts.headline)
                    .fontWeight(.bold)
                Spacer()
                Text("¥\(String(format: "%.2f", costTracker.monthlyBudget))")
                    .font(AppFonts.headline)
                    .fontWeight(.bold)
                    .foregroundColor(AppColors.primary)
            }

            HStack {
                Text("已使用")
                Spacer()
                Text("¥\(String(format: "%.2f", costTracker.totalCost))")
                    .foregroundColor(costTracker.isOverBudget ? .red : AppColors.text)
            }

            HStack {
                Text("剩余预算")
                Spacer()
                Text("¥\(String(format: "%.2f", costTracker.remainingBudget))")
                    .foregroundColor(costTracker.remainingBudget > 0 ? .green : .red)
            }

            // 预算使用进度条
            ProgressView(value: min(costTracker.totalCost / costTracker.monthlyBudget, 1.0))
                .progressViewStyle(LinearProgressViewStyle(tint: costTracker.isOverBudget ? .red : AppColors.primary))
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }

    private var usageStatisticsCard: some View {
        let stats = costTracker.getStatistics()

        return VStack(spacing: AppSizes.smallPadding) {
            Text("使用统计")
                .font(AppFonts.headline)
                .fontWeight(.bold)

            HStack {
                VStack {
                    Text("\(stats.totalRequests)")
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("总请求数")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }

                Spacer()

                VStack {
                    Text("¥\(String(format: "%.4f", stats.averageCostPerRequest))")
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("平均成本")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }

                Spacer()

                VStack {
                    Text(stats.budgetUtilizationPercentage)
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("预算使用率")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }

    private var usageHistoryList: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("使用历史")
                .font(AppFonts.headline)
                .fontWeight(.bold)

            if costTracker.usageHistory.isEmpty {
                Text("暂无使用记录")
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.secondaryText)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: AppSizes.smallPadding) {
                        ForEach(costTracker.usageHistory.reversed()) { record in
                            UsageRecordRow(record: record)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 使用记录行
struct UsageRecordRow: View {
    let record: AIUsageRecord

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(record.model.displayName)
                    .font(AppFonts.caption)
                    .fontWeight(.medium)

                Text(formatTimestamp(record.timestamp))
                    .font(AppFonts.caption2)
                    .foregroundColor(AppColors.secondaryText)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("¥\(String(format: "%.4f", record.cost))")
                    .font(AppFonts.caption)
                    .fontWeight(.medium)

                Text("\(record.inputTokens + record.outputTokens) tokens")
                    .font(AppFonts.caption2)
                    .foregroundColor(AppColors.secondaryText)
            }
        }
        .padding(.vertical, 4)
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - 流式消息视图
struct StreamingMessageView: View {
    let content: String
    @State private var showCursor: Bool = true

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Pinkbot头像
            Image("pinkbot-logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(AppColors.border, lineWidth: 1)
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(content + (showCursor ? "|" : ""))
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.text)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppColors.cardBackground)
                    )
                    .animation(.none, value: content)

                Text("正在输入...")
                    .font(AppFonts.caption2)
                    .foregroundColor(AppColors.secondaryText)
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .leading)

            Spacer()
        }
        .onAppear {
            startCursorAnimation()
        }
    }

    private func startCursorAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showCursor.toggle()
            }
        }
    }
}

// MARK: - AssistantChatView 扩展方法
extension AssistantChatView {

    /// 更新会话标题显示
    private func updateConversationTitle() {
        if let conversationId = assistant.currentConversationId {
            // 从数据库获取会话信息
            let context = PersistenceController.shared.container.viewContext
            let conversationRepo = ConversationRepository(context: context)

            if let conversation = conversationRepo.fetchById(id: conversationId) {
                currentConversationTitle = conversation.title ?? "AI智能助手"
            } else {
                currentConversationTitle = "AI智能助手"
            }
        } else {
            currentConversationTitle = "AI智能助手"
        }
    }

    /// 更新会话标题
    private func updateConversationTitle(newTitle: String) {
        guard !newTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        if let conversationId = assistant.currentConversationId {
            let success = assistant.renameConversation(conversationId: conversationId, newTitle: newTitle)
            if success {
                currentConversationTitle = newTitle
            }
        }
        editingTitle = ""
    }

    /// 切换到指定会话
    func switchToConversation(conversationId: UUID) {
        // 保存当前会话状态
        assistant.saveConversationHistory()

        // 加载新会话
        assistant.loadConversation(conversationId: conversationId)

        // 更新标题
        updateConversationTitle()

        // 重置输入状态
        messageText = ""
        isTextFieldFocused = false
    }

    /// 创建新会话并切换
    func createAndSwitchToNewConversation(title: String? = nil) {
        // 保存当前会话状态
        assistant.saveConversationHistory()

        // 创建新会话
        if let newConversationId = assistant.createNewConversation(title: title) {
            // 更新标题
            updateConversationTitle()

            // 重置输入状态
            messageText = ""
            isTextFieldFocused = false
        }
    }
}

#Preview {
    AssistantChatView()
}
