import SwiftUI

struct BackupProgressView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var backupManager: BackupManager
    
    @State private var showingCancelConfirmation = false
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                // 主要进度区域
                mainProgressSection
                
                // 详细信息区域
                detailsSection
                
                // 操作按钮区域
                actionButtonsSection
                
                Spacer()
            }
            .padding()
            .navigationTitle(operationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    if !backupManager.isBackupInProgress && !backupManager.isRestoreInProgress {
                        Button("完成") {
                            dismiss()
                        }
                        .fontWeight(.semibold)
                    }
                }
            }
            .alert("取消确认", isPresented: $showingCancelConfirmation) {
                But<PERSON>("继续操作", role: .cancel) { }
                Button("取消操作", role: .destructive) {
                    backupManager.cancelCurrentOperation()
                    dismiss()
                }
            } message: {
                Text("确定要取消当前操作吗？已进行的操作将会丢失。")
            }
            .onAppear {
                startAnimation()
            }
        }
    }
    
    // MARK: - 主要进度区域
    
    private var mainProgressSection: some View {
        VStack(spacing: 20) {
            // 圆形进度指示器
            ZStack {
                // 背景圆环
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 8)
                    .frame(width: 120, height: 120)
                
                // 进度圆环
                Circle()
                    .trim(from: 0, to: backupManager.operationProgress)
                    .stroke(
                        themeManager.currentTheme.primaryColor,
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: backupManager.operationProgress)
                
                // 中心图标和百分比
                VStack(spacing: 4) {
                    Image(systemName: currentOperationIcon)
                        .font(.title2)
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .offset(x: animationOffset)
                    
                    Text("\(Int(backupManager.operationProgress * 100))%")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
            
            // 操作状态文本
            Text(backupManager.currentOperation.isEmpty ? "准备中..." : backupManager.currentOperation)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .animation(.easeInOut(duration: 0.3), value: backupManager.currentOperation)
        }
    }
    
    // MARK: - 详细信息区域
    
    private var detailsSection: some View {
        VStack(spacing: 16) {
            // 进度详情卡片
            progressDetailsCard
            
            // 错误信息（如果有）
            if let error = backupManager.lastError {
                errorCard(error: error)
            }
        }
    }
    
    private var progressDetailsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "info.circle")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                
                Text("操作详情")
                    .font(.headline)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                // 操作类型
                HStack {
                    Text("操作类型:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(operationTypeText)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                }
                
                Divider()
                
                // 进度百分比
                HStack {
                    Text("完成进度:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(backupManager.operationProgress * 100))%")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .fontWeight(.medium)
                }
                
                Divider()
                
                // 当前状态
                HStack {
                    Text("当前状态:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(statusText)
                        .font(.subheadline)
                        .foregroundColor(statusColor)
                        .fontWeight(.medium)
                }
                
                // 预估剩余时间（如果可用）
                if let estimatedTime = estimatedRemainingTime {
                    Divider()
                    
                    HStack {
                        Text("预估剩余:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text(estimatedTime)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func errorCard(error: BackupError) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle")
                    .font(.headline)
                    .foregroundColor(.red)
                
                Text("错误信息")
                    .font(.headline)
                    .foregroundColor(.red)
                
                Spacer()
            }
            
            Text(error.localizedDescription)
                .font(.subheadline)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .background(Color(.systemRed).opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemRed), lineWidth: 1)
        )
    }
    
    // MARK: - 操作按钮区域
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            if backupManager.isBackupInProgress || backupManager.isRestoreInProgress {
                // 取消按钮
                Button(action: {
                    showingCancelConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                            .font(.headline)
                        
                        Text("取消操作")
                            .font(.headline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemRed))
                    .cornerRadius(12)
                }
            } else {
                // 完成按钮
                Button(action: {
                    dismiss()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                            .font(.headline)
                        
                        Text("完成")
                            .font(.headline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(themeManager.currentTheme.primaryColor)
                    .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var operationTitle: String {
        if backupManager.isBackupInProgress {
            return "数据备份"
        } else if backupManager.isRestoreInProgress {
            return "数据恢复"
        } else {
            return "操作完成"
        }
    }
    
    private var currentOperationIcon: String {
        if backupManager.isBackupInProgress {
            return "icloud.and.arrow.up"
        } else if backupManager.isRestoreInProgress {
            return "icloud.and.arrow.down"
        } else {
            return "checkmark"
        }
    }
    
    private var operationTypeText: String {
        if backupManager.isBackupInProgress {
            return "数据备份"
        } else if backupManager.isRestoreInProgress {
            return "数据恢复"
        } else {
            return "已完成"
        }
    }
    
    private var statusText: String {
        if backupManager.isBackupInProgress || backupManager.isRestoreInProgress {
            return "进行中"
        } else if backupManager.lastError != nil {
            return "失败"
        } else {
            return "成功"
        }
    }
    
    private var statusColor: Color {
        if backupManager.isBackupInProgress || backupManager.isRestoreInProgress {
            return .orange
        } else if backupManager.lastError != nil {
            return .red
        } else {
            return .green
        }
    }
    
    private var estimatedRemainingTime: String? {
        // 简单的时间估算逻辑
        if backupManager.operationProgress > 0 && backupManager.operationProgress < 1.0 {
            let remainingProgress = 1.0 - backupManager.operationProgress
            let estimatedSeconds = Int(remainingProgress * 60) // 假设总共需要60秒
            
            if estimatedSeconds > 60 {
                let minutes = estimatedSeconds / 60
                return "\(minutes)分钟"
            } else {
                return "\(estimatedSeconds)秒"
            }
        }
        return nil
    }
    
    // MARK: - 动画方法
    
    private func startAnimation() {
        withAnimation(
            Animation.easeInOut(duration: 1.0)
                .repeatForever(autoreverses: true)
        ) {
            animationOffset = 2
        }
    }
}

#Preview {
    BackupProgressView()
        .environmentObject(ThemeManager())
        .environmentObject(BackupManager.shared)
}
