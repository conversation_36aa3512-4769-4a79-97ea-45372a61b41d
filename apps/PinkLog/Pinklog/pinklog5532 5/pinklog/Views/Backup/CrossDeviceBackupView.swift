import SwiftUI
import Combine

/// 跨设备备份和恢复视图
struct CrossDeviceBackupView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var backupManager = BackupManager.shared
    @StateObject private var iCloudService = iCloudDocumentsBackupService.shared
    
    @State private var showingBackupProgress = false
    @State private var showingRestoreConfirmation = false
    @State private var showingErrorAlert = false
    @State private var showingSuccessAlert = false
    @State private var errorMessage = ""
    @State private var successMessage = ""
    @State private var selectedBackup: CrossDeviceBackupMetadata?
    @State private var refreshTrigger = UUID()
    @State private var showingClearAllConfirmation = false
    @State private var isClearingAll = false
    @State private var showingCleanupConfirmation = false
    @State private var isCleaningUp = false
    @State private var showingDiagnosticAlert = false
    @State private var diagnosticMessage = ""
    @State private var isDiagnosing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // iCloud状态卡片
                    iCloudStatusCard
                    
                    // 跨设备备份操作卡片
                    crossDeviceBackupCard
                    
                    // 可用备份列表
                    availableBackupsSection
                }
                .padding()
            }
            .navigationTitle("跨设备备份")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("刷新") {
                        Task {
                            await refreshBackups()
                        }
                    }
                    .disabled(iCloudService.isScanning)
                }
            }
            .onAppear {
                Task {
                    await refreshBackups()
                }
            }
            .onDisappear {
                // 页面消失时清理备份文件缓存，释放内存
                Task {
                    await iCloudService.clearAllCache()
                }
            }
            .alert("错误", isPresented: $showingErrorAlert) {
                Button("确定") { }
            } message: {
                Text(errorMessage)
            }
            .alert("成功", isPresented: $showingSuccessAlert) {
                Button("确定") { }
            } message: {
                Text(successMessage)
            }
            .alert("清空所有备份", isPresented: $showingClearAllConfirmation) {
                Button("取消", role: .cancel) { }
                Button("确认清空", role: .destructive) {
                    Task {
                        await clearAllBackups()
                    }
                }
            } message: {
                Text("此操作将删除所有备份文件和CloudKit记录，无法恢复。确定要继续吗？")
            }
            .alert("智能清理备份", isPresented: $showingCleanupConfirmation) {
                Button("取消", role: .cancel) { }
                Button("确认清理", role: .destructive) {
                    Task {
                        await cleanupOldBackups()
                    }
                }
            } message: {
                Text("此操作将为每种备份类型（手动备份、自动备份、恢复前备份）仅保留最新的2个版本，删除其余旧版本。确定要继续吗？")
            }
            .confirmationDialog(
                "确认恢复备份",
                isPresented: $showingRestoreConfirmation,
                titleVisibility: .visible
            ) {
                Button("恢复", role: .destructive) {
                    if let backup = selectedBackup {
                        Task {
                            await performRestore(backup)
                        }
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                if let backup = selectedBackup {
                    Text("这将使用来自 \(backup.deviceName) 的备份覆盖当前数据。此操作不可撤销。")
                }
            }
            .alert("文件诊断报告", isPresented: $showingDiagnosticAlert) {
                Button("确定") { }
            } message: {
                Text(diagnosticMessage)
            }
        }
    }
    
    // MARK: - iCloud状态卡片
    
    private var iCloudStatusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "icloud")
                    .foregroundColor(iCloudService.isAvailable ? .green : .red)
                Text("iCloud Documents状态")
                    .font(.headline)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("状态:")
                        .foregroundColor(.secondary)
                    Text(iCloudService.isAvailable ? "可用" : "不可用")
                        .foregroundColor(iCloudService.isAvailable ? .green : .red)
                        .fontWeight(.medium)
                }
                
                if !iCloudService.isAvailable {
                    Text("请确保已登录iCloud并为此应用启用了iCloud Drive")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                
                if let error = iCloudService.lastError {
                    Text("错误: \(error.localizedDescription)")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(themeManager.currentTheme.cardBackgroundColor)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 跨设备备份操作卡片
    
    private var crossDeviceBackupCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "arrow.triangle.2.circlepath.icloud")
                    .foregroundColor(.blue)
                Text("跨设备备份")
                    .font(.headline)
                Spacer()
            }
            
            Text("创建可在所有设备上访问的备份文件")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button(action: {
                Task {
                    await performCrossDeviceBackup()
                }
            }) {
                HStack {
                    if backupManager.isBackupInProgress {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "plus.circle.fill")
                    }
                    Text(backupManager.isBackupInProgress ? "备份中..." : "创建跨设备备份")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(backupManager.isBackupInProgress || !iCloudService.isAvailable)
            
            if backupManager.isBackupInProgress {
                VStack(alignment: .leading, spacing: 8) {
                    Text(backupManager.currentOperation)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ProgressView(value: backupManager.operationProgress)
                        .progressViewStyle(LinearProgressViewStyle())
                }
            }
        }
        .padding()
        .background(themeManager.currentTheme.cardBackgroundColor)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 可用备份列表
    
    private var availableBackupsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "folder.badge.gearshape")
                    .foregroundColor(.purple)
                Text("可用备份")
                    .font(.headline)
                Spacer()

                // 清理和清空按钮
                if !iCloudService.availableBackups.isEmpty {
                    HStack(spacing: 8) {
                        // 智能清理按钮
                        Button(action: {
                            showingCleanupConfirmation = true
                        }) {
                            HStack(spacing: 4) {
                                if isCleaningUp {
                                    ProgressView()
                                        .scaleEffect(0.7)
                                } else {
                                    Image(systemName: "sparkles")
                                }
                                Text("清理")
                                    .font(.caption)
                            }
                            .foregroundColor(.orange)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(6)
                        }
                        .disabled(isCleaningUp || isClearingAll || iCloudService.isScanning)

                        // 清空所有备份按钮
                        Button(action: {
                            showingClearAllConfirmation = true
                        }) {
                            HStack(spacing: 4) {
                                if isClearingAll {
                                    ProgressView()
                                        .scaleEffect(0.7)
                                } else {
                                    Image(systemName: "trash.fill")
                                }
                                Text("清空")
                                    .font(.caption)
                            }
                            .foregroundColor(.red)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(6)
                        }
                        .disabled(isClearingAll || isCleaningUp || iCloudService.isScanning)
                    }
                }

                if iCloudService.isScanning {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            if iCloudService.availableBackups.isEmpty {
                emptyBackupsView
            } else {
                deviceGroupedBackupsView
            }
        }
        .padding()
        .background(themeManager.currentTheme.cardBackgroundColor)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var emptyBackupsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("暂无可用备份")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("创建第一个跨设备备份以开始使用")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 32)
    }
    
    private var deviceGroupedBackupsView: some View {
        VStack(spacing: 16) {
            ForEach(groupedBackups, id: \.device.deviceIdentifier) { deviceGroup in
                deviceBackupGroupView(deviceGroup)
            }
        }
    }
    
    private func deviceBackupGroupView(_ deviceGroup: DeviceBackupGroup) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // 设备标题
            HStack {
                Image(systemName: deviceIcon(for: deviceGroup.device.deviceName))
                    .foregroundColor(.blue)
                VStack(alignment: .leading, spacing: 2) {
                    Text(deviceGroup.device.deviceName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text("\(deviceGroup.backups.count) 个备份")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            .padding(.bottom, 4)
            
            // 备份列表
            ForEach(deviceGroup.backups, id: \.backupId) { backup in
                backupRowView(backup)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private func backupRowView(_ backup: CrossDeviceBackupMetadata) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(backup.backupType.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(backup.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(backup.formattedCreatedAt)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let description = backup.description, !description.isEmpty {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.blue)
                        .lineLimit(2)
                }
                
                Text("\(backup.totalEntities) 个项目")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(spacing: 8) {
                Button(action: {
                    selectedBackup = backup
                    showingRestoreConfirmation = true
                }) {
                    Image(systemName: "arrow.clockwise.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                }
                .disabled(backupManager.isRestoreInProgress)
                
                Menu {
                    Button(action: {
                        Task {
                            await diagnoseBackup(backup)
                        }
                    }) {
                        Label("诊断文件", systemImage: "stethoscope")
                    }
                    .disabled(isDiagnosing)
                    
                    Button(action: {
                        Task {
                            await exportBackup(backup)
                        }
                    }) {
                        Label("导出文件", systemImage: "square.and.arrow.up")
                    }
                    
                    Button(role: .destructive, action: {
                        Task {
                            await deleteBackup(backup)
                        }
                    }) {
                        Label("删除备份", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 8)
        .background(Color.clear)
    }
    
    // MARK: - 辅助计算属性
    
    private var groupedBackups: [DeviceBackupGroup] {
        let grouped = Dictionary(grouping: iCloudService.availableBackups) { $0.deviceIdentifier }
        return grouped.map { (identifier, backups) in
            let deviceName = backups.first?.deviceName ?? "未知设备"
            let sortedBackups = backups.sorted { $0.createdAt > $1.createdAt }
            return DeviceBackupGroup(
                device: DeviceInfo(deviceName: deviceName, deviceIdentifier: identifier),
                backups: sortedBackups
            )
        }.sorted { $0.device.deviceName < $1.device.deviceName }
    }
    
    private func deviceIcon(for deviceName: String) -> String {
        let lowercased = deviceName.lowercased()
        if lowercased.contains("iphone") {
            return "iphone"
        } else if lowercased.contains("ipad") {
            return "ipad"
        } else if lowercased.contains("mac") {
            return "laptopcomputer"
        } else {
            return "devicepixel"
        }
    }
    
    // MARK: - 操作方法
    
    private func refreshBackups() async {
        await iCloudService.refreshBackups()
        refreshTrigger = UUID()
    }
    
    private func performCrossDeviceBackup() async {
        showingBackupProgress = true
        
        let result = await backupManager.performCrossDeviceBackup(
            description: "手动跨设备备份"
        )
        
        showingBackupProgress = false
        
        if result.success {
            successMessage = "跨设备备份创建成功！"
            showingSuccessAlert = true
            
            // 刷新备份列表
            await refreshBackups()
        } else {
            errorMessage = result.error?.localizedDescription ?? "备份失败"
            showingErrorAlert = true
        }
    }
    
    private func performRestore(_ backup: CrossDeviceBackupMetadata) async {
        let result = await backupManager.restoreFromiCloudDocuments(backup.backupId)

        if result.success {
            // 发送数据恢复完成通知，触发UI刷新
            await MainActor.run {
                NotificationCenter.default.post(name: .dataRestoreCompleted, object: nil)
            }

            successMessage = "数据恢复成功！仪表盘和产品页面已自动刷新。"
            showingSuccessAlert = true
        } else {
            // 检查是否为跨设备兼容性问题
            if let error = result.error as? DataImportError, error.isCrossDeviceIssue {
                // 尝试跨设备兼容模式恢复
                await performCrossDeviceCompatibleRestore(backup)
            } else {
                errorMessage = result.error?.localizedDescription ?? "恢复失败"
                showingErrorAlert = true
            }
        }
    }
    
    private func performCrossDeviceCompatibleRestore(_ backup: CrossDeviceBackupMetadata) async {
        let result = await backupManager.restoreFromiCloudDocumentsWithCrossDeviceMode(backup.backupId)

        if result.success {
            // 发送数据恢复完成通知，触发UI刷新
            await MainActor.run {
                NotificationCenter.default.post(name: .dataRestoreCompleted, object: nil)
            }

            if !result.warnings.isEmpty {
                successMessage = "跨设备恢复成功！仪表盘和产品页面已自动刷新。\n注意：\(result.warnings.joined(separator: "；"))"
            } else {
                successMessage = "跨设备恢复成功！仪表盘和产品页面已自动刷新。某些数据可能由于兼容性原因被调整。"
            }
            showingSuccessAlert = true
        } else {
            if let error = result.error as? DataImportError {
                errorMessage = error.userFriendlyMessage
            } else {
                errorMessage = result.error?.localizedDescription ?? "跨设备恢复失败"
            }
            showingErrorAlert = true
        }
    }
    
    private func exportBackup(_ backup: CrossDeviceBackupMetadata) async {
        do {
            let fileURL = try await iCloudService.exportBackupToFiles(with: backup.backupId)
            // 这里可以添加分享文件的逻辑
            successMessage = "备份文件已导出到: \(fileURL.lastPathComponent)"
            showingSuccessAlert = true
        } catch {
            errorMessage = "导出失败: \(error.localizedDescription)"
            showingErrorAlert = true
        }
    }
    
    private func deleteBackup(_ backup: CrossDeviceBackupMetadata) async {
        do {
            try await iCloudService.deleteBackup(with: backup.backupId)
            successMessage = "备份删除成功"
            showingSuccessAlert = true

            // 刷新备份列表
            await refreshBackups()
        } catch {
            errorMessage = "删除失败: \(error.localizedDescription)"
            showingErrorAlert = true
        }
    }

    private func clearAllBackups() async {
        isClearingAll = true
        defer { isClearingAll = false }

        do {
            // 1. 清空iCloud Documents中的备份文件
            let deletedFilesCount = try await iCloudService.clearAllBackupFiles()

            // 2. 清空CloudKit中的备份记录
            let cloudKitService = CloudKitBackupService.shared
            let cloudKitResult = await cloudKitService.clearAllBackupRecords()

            var message = "成功清空 \(deletedFilesCount) 个备份文件"

            if cloudKitResult.success {
                let deletedRecordsCount = cloudKitResult.data ?? 0
                message += "和 \(deletedRecordsCount) 个CloudKit记录"
            } else {
                message += "，但CloudKit记录清理失败: \(cloudKitResult.error?.localizedDescription ?? "未知错误")"
            }

            successMessage = message
            showingSuccessAlert = true

            // 刷新备份列表
            await refreshBackups()

        } catch {
            errorMessage = "清空失败: \(error.localizedDescription)"
            showingErrorAlert = true
        }
    }

    private func cleanupOldBackups() async {
        isCleaningUp = true
        defer { isCleaningUp = false }

        do {
            let deletedCount = try await iCloudService.autoCleanupBackups()

            if deletedCount > 0 {
                successMessage = "智能清理完成，删除了 \(deletedCount) 个旧备份文件"
            } else {
                successMessage = "无需清理，所有备份类型都不超过2个版本"
            }
            showingSuccessAlert = true

            // 刷新备份列表
            await refreshBackups()

        } catch {
            errorMessage = "清理失败: \(error.localizedDescription)"
            showingErrorAlert = true
        }
    }
    
    private func diagnoseBackup(_ backup: CrossDeviceBackupMetadata) async {
        isDiagnosing = true
        defer { isDiagnosing = false }
        
        var report: [String] = []
        
        report.append("📋 备份文件诊断报告")
        report.append("")
        report.append("📄 基本信息:")
        report.append("  • 备份ID: \(backup.backupId)")
        report.append("  • 设备名称: \(backup.deviceName)")
        report.append("  • 设备型号: \(backup.deviceModel)")
        report.append("  • 创建时间: \(backup.formattedCreatedAt)")
        report.append("  • 文件大小: \(backup.formattedFileSize)")
        report.append("  • 实体数量: \(backup.totalEntities)")
        report.append("")
        
        // 尝试加载文件进行诊断
        do {
            // 检查文件是否存在于可用列表中
            let isInList = iCloudService.availableBackups.contains { $0.backupId == backup.backupId }
            report.append("📂 文件状态:")
            report.append("  • 在备份列表中: \(isInList ? "✅ 是" : "❌ 否")")
            
            // 尝试加载完整备份数据
            report.append("  • 正在尝试加载备份数据...")
            let backupFile = try await iCloudService.loadBackup(with: backup.backupId)
            
            report.append("  • 文件读取: ✅ 成功")
            report.append("  • JSON解码: ✅ 成功")
            report.append("  • 元数据验证: ✅ 成功")
            
            // 验证数据完整性
            let container = backupFile.backupData
            let validationResult = container.validateBackup(allowCrossDeviceValidation: true)
            
            report.append("")
            report.append("🔍 数据验证:")
            report.append("  • 验证结果: \(validationResult.isValid ? "✅ 通过" : "❌ 失败")")
            report.append("  • 总实体数: \(container.totalEntities)")
            report.append("  • 数据校验和: \(container.dataChecksum)")
            
            if !validationResult.warnings.isEmpty {
                report.append("")
                report.append("⚠️ 警告信息:")
                for warning in validationResult.warnings {
                    report.append("  • \(warning)")
                }
            }
            
            if !validationResult.errors.isEmpty {
                report.append("")
                report.append("❌ 错误信息:")
                for error in validationResult.errors {
                    report.append("  • \(error)")
                }
            }
            
            report.append("")
            report.append("✅ 诊断结论: 备份文件完整，可以用于恢复")
            
        } catch {
            report.append("  • 文件加载: ❌ 失败")
            report.append("  • 错误详情: \(error.localizedDescription)")
            
            // 尝试分析错误原因
            if error.localizedDescription.contains("invalidBackupFile") {
                report.append("")
                report.append("🔍 可能的问题:")
                report.append("  • 文件可能正在同步中，请稍后再试")
                report.append("  • 文件可能已损坏或格式不兼容")
                report.append("  • metadata与实际文件内容不匹配")
            }
            
            report.append("")
            report.append("❌ 诊断结论: 备份文件存在问题，建议重新创建备份")
        }
        
        diagnosticMessage = report.joined(separator: "\n")
        showingDiagnosticAlert = true
    }
}

// MARK: - 辅助数据结构

private struct DeviceInfo {
    let deviceName: String
    let deviceIdentifier: String
}

private struct DeviceBackupGroup {
    let device: DeviceInfo
    let backups: [CrossDeviceBackupMetadata]
}

// MARK: - 预览

struct CrossDeviceBackupView_Previews: PreviewProvider {
    static var previews: some View {
        CrossDeviceBackupView()
            .environmentObject(ThemeManager())
    }
}