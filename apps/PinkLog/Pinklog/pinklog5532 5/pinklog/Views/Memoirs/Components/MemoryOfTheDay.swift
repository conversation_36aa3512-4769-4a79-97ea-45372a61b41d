import SwiftUI

struct MemoryOfTheDay: View {
    @Binding var isPresented: Bool
    let memories: [UsageRecord]
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var currentIndex: Int = 0
    @State private var isAnimating: Bool = false
    @State private var showContent: Bool = false
    @State private var particles: [Particle] = []
    
    private let heavyImpact = UIImpactFeedbackGenerator(style: .heavy)
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        ZStack {
            // 背景
            backgroundView
            
            // 主内容
            if showContent {
                mainContent
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            }
            
            // 粒子效果
            particleSystem
            
            // 关闭按钮
            closeButton
        }
        .onAppear {
            initializeView()
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.height > 100 {
                        dismissView()
                    }
                }
        )
    }
    
    // MARK: - 背景视图
    private var backgroundView: some View {
        ZStack {
            // 基础背景
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissView()
                }
            
            // 渐变叠加
            RadialGradient(
                colors: [
                    themeManager.currentTheme.primaryColor.opacity(0.3),
                    themeManager.currentTheme.secondaryColor.opacity(0.2),
                    Color.clear
                ],
                center: .center,
                startRadius: 100,
                endRadius: 400
            )
            .ignoresSafeArea()
            .scaleEffect(isAnimating ? 1.2 : 1.0)
            .animation(
                .easeInOut(duration: 3.0)
                .repeatForever(autoreverses: true),
                value: isAnimating
            )
        }
    }
    
    // MARK: - 主要内容
    private var mainContent: some View {
        VStack(spacing: 0) {
            // 标题区域
            titleSection
            
            // 内容区域
            if !memories.isEmpty {
                memoryCardView
            } else {
                emptyMemoryView
            }
            
            // 操作按钮
            actionButtons
        }
        .padding(24)
        .background(cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 28))
        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
        .scaleEffect(showContent ? 1.0 : 0.8)
        .animation(.spring(response: 0.8, dampingFraction: 0.8), value: showContent)
    }
    
    // MARK: - 标题区域
    private var titleSection: some View {
        VStack(spacing: 12) {
            // 装饰图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                themeManager.currentTheme.primaryColor.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 20,
                            endRadius: 50
                        )
                    )
                    .frame(width: 80, height: 80)
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                
                Image(systemName: "calendar.badge.clock")
                    .font(.system(size: 32, weight: .light))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .rotationEffect(.degrees(isAnimating ? 5 : -5))
            }
            .animation(
                .easeInOut(duration: 2.0)
                .repeatForever(autoreverses: true),
                value: isAnimating
            )
            
            // 主标题
            Text("那年今日")
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
            
            // 副标题
            Text(subtitleText)
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - 回忆卡片视图
    private var memoryCardView: some View {
        VStack(spacing: 20) {
            // 当前回忆
            currentMemoryCard
            
            // 页面指示器
            if memories.count > 1 {
                pageIndicator
            }
        }
    }
    
    // MARK: - 当前回忆卡片
    private var currentMemoryCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 日期和年份
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(formattedDate(memories[currentIndex].date))
                        .font(.system(size: 18, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                    
                    Text(yearsAgoText(memories[currentIndex].date))
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
                
                Spacer()
                
                EmotionGem(emotionalValue: Int32(memories[currentIndex].emotionalValue))
            }
            
            // 标题
            if let title = memories[currentIndex].title, !title.isEmpty {
                Text(title)
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .lineLimit(2)
            }
            
            // 内容预览
            Text(contentPreview(memories[currentIndex]))
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .foregroundColor(.secondary)
                .lineLimit(6)
                .lineSpacing(4)
            
            // 产品信息
            if let product = memories[currentIndex].product {
                HStack {
                    Image(systemName: "cube.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                    
                    Text(product.name ?? "产品")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(themeManager.currentTheme.primaryColor.opacity(0.12))
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.6),
                                    Color.white.opacity(0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .gesture(
            DragGesture()
                .onEnded { value in
                    if abs(value.translation.width) > 50 {
                        if value.translation.width > 0 && currentIndex > 0 {
                            // 向右滑动，显示上一个
                            previousMemory()
                        } else if value.translation.width < 0 && currentIndex < memories.count - 1 {
                            // 向左滑动，显示下一个
                            nextMemory()
                        }
                    }
                }
        )
    }
    
    // MARK: - 页面指示器
    private var pageIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<memories.count, id: \.self) { index in
                Circle()
                    .fill(index == currentIndex ? themeManager.currentTheme.primaryColor : Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(index == currentIndex ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: currentIndex)
            }
        }
    }
    
    // MARK: - 空回忆视图
    private var emptyMemoryView: some View {
        VStack(spacing: 20) {
            Image(systemName: "calendar.badge.questionmark")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary)
            
            Text("今天没有发现\n历史上的回忆")
                .font(.system(size: 18, weight: .medium, design: .rounded))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text("不如创建一个新的美好回忆？")
                .font(.system(size: 14, weight: .regular, design: .rounded))
                .foregroundColor(Color(.tertiaryLabel))
        }
        .padding(.vertical, 40)
    }
    
    // MARK: - 操作按钮
    private var actionButtons: some View {
        HStack(spacing: 16) {
            if !memories.isEmpty {
                // 查看详情按钮
                Button(action: {
                    // TODO: 导航到详情页面
                    lightImpact.impactOccurred()
                }) {
                    Text("查看详情")
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)
                        .padding(.vertical, 12)
                        .padding(.horizontal, 24)
                        .background(
                            LinearGradient(
                                colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .clipShape(Capsule())
                }
                .buttonStyle(ScaleButtonStyle())
            }
            
            // 再看一个按钮
            Button(action: {
                refreshMemories()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 14, weight: .medium))
                    
                    Text(memories.isEmpty ? "刷新" : "再看一个")
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                }
                .foregroundColor(themeManager.currentTheme.primaryColor)
                .padding(.vertical, 12)
                .padding(.horizontal, 24)
                .background(
                    Capsule()
                        .fill(themeManager.currentTheme.primaryColor.opacity(0.12))
                )
            }
            .buttonStyle(ScaleButtonStyle())
        }
        .padding(.top, 24)
    }
    
    // MARK: - 关闭按钮
    private var closeButton: some View {
        VStack {
            HStack {
                Spacer()
                
                Button(action: {
                    dismissView()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 40, height: 40)
                        )
                }
                .buttonStyle(ScaleButtonStyle())
            }
            .padding(.horizontal, 24)
            .padding(.top, 50)
            
            Spacer()
        }
    }
    
    // MARK: - 卡片背景
    private var cardBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 28)
                .fill(.ultraThinMaterial)
            
            RoundedRectangle(cornerRadius: 28)
                .fill(
                    LinearGradient(
                        colors: [
                            themeManager.currentTheme.primaryColor.opacity(0.1),
                            themeManager.currentTheme.secondaryColor.opacity(0.05),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }
    
    // MARK: - 粒子系统
    private var particleSystem: some View {
        ForEach(particles, id: \.id) { particle in
            Circle()
                .fill(particle.color)
                .frame(width: particle.size, height: particle.size)
                .position(particle.position)
                .opacity(particle.opacity)
        }
    }
    
    // MARK: - 计算属性
    private var subtitleText: String {
        if memories.isEmpty {
            return "在时光的长河中寻找"
        } else {
            return "发现了 \(memories.count) 个美好回忆"
        }
    }
    
    // MARK: - 辅助函数
    private func initializeView() {
        heavyImpact.impactOccurred()
        
        withAnimation(.easeOut(duration: 0.3)) {
            isAnimating = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                showContent = true
            }
        }
        
        generateParticles()
    }
    
    private func dismissView() {
        lightImpact.impactOccurred()
        
        withAnimation(.easeInOut(duration: 0.3)) {
            showContent = false
            isPresented = false
        }
    }
    
    private func nextMemory() {
        guard currentIndex < memories.count - 1 else { return }
        
        lightImpact.impactOccurred()
        
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            currentIndex += 1
        }
    }
    
    private func previousMemory() {
        guard currentIndex > 0 else { return }
        
        lightImpact.impactOccurred()
        
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            currentIndex -= 1
        }
    }
    
    private func refreshMemories() {
        lightImpact.impactOccurred()
        
        // TODO: 重新获取那年今日的回忆
        if !memories.isEmpty {
            currentIndex = Int.random(in: 0..<memories.count)
        }
    }
    
    private func formattedDate(_ date: Date?) -> String {
        guard let date = date else { return "未知日期" }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
    }
    
    private func yearsAgoText(_ date: Date?) -> String {
        guard let date = date else { return "" }
        
        let calendar = Calendar.current
        let years = calendar.dateComponents([.year], from: date, to: Date()).year ?? 0
        
        if years == 0 {
            return "今年"
        } else if years == 1 {
            return "一年前"
        } else {
            return "\(years)年前"
        }
    }
    
    private func contentPreview(_ memory: UsageRecord) -> String {
        // 优先使用memories字段，然后是notes字段
        if let memories = memory.memories, !memories.isEmpty {
            return String(memories.prefix(200)) + (memories.count > 200 ? "..." : "")
        } else if let notes = memory.notes, !notes.isEmpty {
            return String(notes.prefix(200)) + (notes.count > 200 ? "..." : "")
        } else {
            return "这是一段珍贵的回忆，承载着当时的美好时光..."
        }
    }
    
    private func generateParticles() {
        particles = (0..<20).map { _ in
            Particle(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...400),
                    y: CGFloat.random(in: 0...800)
                ),
                size: CGFloat.random(in: 2...6),
                color: [
                    themeManager.currentTheme.primaryColor.opacity(0.3),
                    themeManager.currentTheme.secondaryColor.opacity(0.3),
                    Color.white.opacity(0.4)
                ].randomElement()!,
                opacity: Double.random(in: 0.2...0.8)
            )
        }
        
        animateParticles()
    }
    
    private func animateParticles() {
        withAnimation(.linear(duration: 10).repeatForever(autoreverses: false)) {
            for i in particles.indices {
                particles[i].position.y -= 1000
                particles[i].opacity = 0
            }
        }
    }
}

// MARK: - 粒子结构
struct Particle: Identifiable {
    let id: UUID
    var position: CGPoint
    let size: CGFloat
    let color: Color
    var opacity: Double
}

#Preview {
    MemoryOfTheDay(
        isPresented: .constant(true),
        memories: []
    )
    .environmentObject(ThemeManager())
}