import SwiftUI

struct FavoriteMemoryCell: View {
    let story: UsageRecord
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isVisible: Bool = false
    @State private var isPressed: Bool = false
    @State private var shimmerAnimation: Bool = false
    @State private var heartAnimation: Bool = false
    @State private var glowAnimation: Bool = false
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let cellSize: (width: CGFloat, height: CGFloat) = (160, 180)
    
    var body: some View {
        ZStack {
            // 背景层
            cellBackground
            
            // 内容层
            cellContent
            
            // 收藏装饰
            favoriteDecorations
            
            // 爱心粒子效果
            heartParticles
        }
        .frame(width: cellSize.width, height: cellSize.height)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(
            color: Color.pink.opacity(isPressed ? 0.2 : 0.3),
            radius: isPressed ? 8 : 12,
            x: 0,
            y: isPressed ? 4 : 8
        )
        .scaleEffect(isPressed ? 0.95 : (isVisible ? 1.0 : 0.9))
        .opacity(isVisible ? 1.0 : 0.0)
        .rotation3DEffect(
            .degrees(isPressed ? 3 : 0),
            axis: (x: 0.1, y: 0.1, z: 0)
        )
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                isVisible = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                startAnimations()
            }
        }
        .allowsHitTesting(true)
    }
    
    // MARK: - 背景层
    private var cellBackground: some View {
        ZStack {
            // 基础背景
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
            
            // 收藏特殊渐变
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.pink.opacity(0.3),
                            Color.red.opacity(0.2),
                            Color.orange.opacity(0.1),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .opacity(0.8)
            
            // 收藏光晕效果
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    RadialGradient(
                        colors: [
                            Color.pink.opacity(glowAnimation ? 0.4 : 0.2),
                            Color.red.opacity(glowAnimation ? 0.2 : 0.1),
                            Color.clear
                        ],
                        center: UnitPoint(x: 0.8, y: 0.2),
                        startRadius: 20,
                        endRadius: 100
                    )
                )
                .animation(
                    .easeInOut(duration: 2.5)
                    .repeatForever(autoreverses: true),
                    value: glowAnimation
                )
            
            // 收藏边框
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.7),
                            Color.pink.opacity(0.6),
                            Color.red.opacity(0.4),
                            Color.white.opacity(0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        }
    }
    
    // MARK: - 内容层
    private var cellContent: some View {
        VStack(spacing: 0) {
            // 顶部收藏标识区域
            topFavoriteSection
            
            // 中间内容区域
            mainContentSection
            
            // 底部信息
            bottomSection
        }
        .padding(16)
    }
    
    // MARK: - 顶部收藏标识区域
    private var topFavoriteSection: some View {
        HStack {
            // 日期标记
            VStack(alignment: .leading, spacing: 2) {
                Text(monthDay)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                
                Text(year)
                    .font(.system(size: 10, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 收藏爱心标识
            ZStack {
                // 背景光晕
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.pink.opacity(heartAnimation ? 0.3 : 0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 2,
                            endRadius: 15
                        )
                    )
                    .frame(width: 30, height: 30)
                    .scaleEffect(heartAnimation ? 1.2 : 1.0)
                
                // 爱心图标
                Image(systemName: "heart.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.pink, Color.red],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(heartAnimation ? 1.1 : 1.0)
                    .shadow(color: Color.pink.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            .animation(
                .easeInOut(duration: 1.5)
                .repeatForever(autoreverses: true),
                value: heartAnimation
            )
        }
        .padding(.bottom, 12)
    }
    
    // MARK: - 主内容区域
    private var mainContentSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            if let coverImage = storyCoverImage {
                // 有封面图的布局
                VStack(alignment: .leading, spacing: 10) {
                    // 封面图
                    Image(uiImage: coverImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 70)
                        .frame(maxWidth: .infinity)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.4),
                                            Color.pink.opacity(0.3)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )
                        .shadow(color: Color.pink.opacity(0.2), radius: 6, x: 0, y: 3)
                    
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 15, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                    
                    // 多媒体指示器
                    if hasImages || hasAudioRecordings {
                        HStack(spacing: 6) {
                            if hasImages {
                                HStack(spacing: 3) {
                                    Image(systemName: "photo.fill")
                                        .font(.system(size: 9, weight: .medium))
                                    Text("图片")
                                        .font(.system(size: 10, weight: .medium, design: .rounded))
                                }
                                .foregroundColor(.pink)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    Capsule()
                                        .fill(Color.pink.opacity(0.15))
                                )
                            }
                            
                            if hasAudioRecordings {
                                HStack(spacing: 3) {
                                    Image(systemName: "waveform")
                                        .font(.system(size: 9, weight: .medium))
                                    Text("语音")
                                        .font(.system(size: 10, weight: .medium, design: .rounded))
                                }
                                .foregroundColor(.pink)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    Capsule()
                                        .fill(Color.pink.opacity(0.15))
                                )
                            }
                        }
                    }
                }
            } else {
                // 无封面图的布局
                VStack(alignment: .leading, spacing: 8) {
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }

                    // 简短预览
                    Text(briefPreview)
                        .font(.system(size: 13, weight: .regular, design: .rounded))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                        .lineSpacing(1.5)
                    
                    // 多媒体指示器
                    if hasAudioRecordings {
                        HStack(spacing: 4) {
                            Image(systemName: "waveform.path")
                                .font(.system(size: 9, weight: .medium))
                            Text("语音记忆")
                                .font(.system(size: 10, weight: .medium, design: .rounded))
                        }
                        .foregroundColor(.pink)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.pink.opacity(0.15))
                        )
                    }
                }
            }
            
            Spacer(minLength: 0)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 底部区域
    private var bottomSection: some View {
        HStack {
            // 相对时间
            Text(relativeTime)
                .font(.system(size: 10, weight: .medium, design: .rounded))
                .foregroundColor(.secondary.opacity(0.8))
            
            Spacer()
            
            // 收藏标识
            Text("珍藏")
                .font(.system(size: 10, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.pink, Color.red],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color.pink.opacity(0.12))
                        .overlay(
                            Capsule()
                                .stroke(Color.pink.opacity(0.3), lineWidth: 0.5)
                        )
                )
        }
    }
    
    // MARK: - 收藏装饰
    private var favoriteDecorations: some View {
        VStack {
            HStack {
                Spacer()
                
                // 角落收藏装饰
                ZStack {
                    // 光芒效果
                    ForEach(0..<8, id: \.self) { index in
                        Rectangle()
                            .fill(Color.pink.opacity(0.4))
                            .frame(width: 1, height: 8)
                            .offset(y: -4)
                            .rotationEffect(.degrees(Double(index) * 45))
                            .scaleEffect(shimmerAnimation ? 1.0 : 0.6)
                            .opacity(shimmerAnimation ? 0.8 : 0.3)
                    }
                    
                    // 中心小星星
                    Image(systemName: "sparkle")
                        .font(.system(size: 8, weight: .medium))
                        .foregroundColor(.pink)
                        .rotationEffect(.degrees(shimmerAnimation ? 360 : 0))
                }
                .animation(
                    .linear(duration: 3.0)
                    .repeatForever(autoreverses: false),
                    value: shimmerAnimation
                )
                .padding(.top, 8)
                .padding(.trailing, 8)
            }
            
            Spacer()
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - 爱心粒子效果
    private var heartParticles: some View {
        ZStack {
            ForEach(0..<4, id: \.self) { index in
                Image(systemName: "heart.fill")
                    .font(.system(size: CGFloat.random(in: 4...8), weight: .light))
                    .foregroundColor(Color.pink.opacity(0.6))
                    .position(
                        x: CGFloat.random(in: 20...(cellSize.width - 20)),
                        y: CGFloat.random(in: 40...(cellSize.height - 40))
                    )
                    .opacity(heartAnimation ? 0.8 : 0.2)
                    .scaleEffect(heartAnimation ? 1.3 : 0.7)
                    .animation(
                        .easeInOut(duration: Double.random(in: 1.8...2.5))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.3),
                        value: heartAnimation
                    )
            }
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - 计算属性
    private var monthDay: String {
        guard let date = story.date else { return "??" }
        let formatter = DateFormatter()
        formatter.dateFormat = "M/d"
        return formatter.string(from: date)
    }
    
    private var year: String {
        guard let date = story.date else { return "????" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: date)
    }
    
    private var briefPreview: String {
        if let memories = story.memories, !memories.isEmpty {
            // 移除收藏标记，显示纯净内容
            let cleanedMemories = memories.replacingOccurrences(of: "[收藏] ", with: "")
            return String(cleanedMemories.prefix(80)) + (cleanedMemories.count > 80 ? "..." : "")
        } else if let notes = story.notes, !notes.isEmpty {
            let cleanedNotes = notes.replacingOccurrences(of: "[收藏] ", with: "")
            return String(cleanedNotes.prefix(80)) + (cleanedNotes.count > 80 ? "..." : "")
        } else {
            return "珍贵的回忆..."
        }
    }
    
    private var relativeTime: String {
        guard let date = story.date else { return "" }
        let calendar = Calendar.current
        let now = Date()
        
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                if days < 7 {
                    return "\(days)天前"
                } else if days < 30 {
                    return "\(days / 7)周前"
                } else {
                    return "\(days / 30)月前"
                }
            }
        }
        return "很久前"
    }
    
    // MARK: - 动画函数
    private func startAnimations() {
        shimmerAnimation = true
        heartAnimation = true
        glowAnimation = true
    }
    
    // MARK: - 辅助计算属性
    private var hasImages: Bool {
        guard let imageData = story.images else { return false }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                return !imageDatas.isEmpty
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return !images.isEmpty
            }
            return false
        } catch {
            return false
        }
    }
    
    private var hasAudioRecordings: Bool {
        return story.hasAudio
    }
    
    private var storyCoverImage: UIImage? {
        guard let imageData = story.images else { return nil }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                if let firstImageData = imageDatas.first,
                   let image = UIImage(data: firstImageData) {
                    return image
                }
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return images.first
            }
            return nil
        } catch {
            return nil
        }
    }
}

#Preview {
    ScrollView {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2),
            spacing: 8
        ) {
            FavoriteMemoryCell(story: UsageRecord())
            FavoriteMemoryCell(story: UsageRecord())
            FavoriteMemoryCell(story: UsageRecord())
            FavoriteMemoryCell(story: UsageRecord())
        }
        .padding()
    }
    .environmentObject(ThemeManager())
}