import SwiftUI

struct PoeticSearchBar: View {
    @Binding var searchText: String
    @Binding var isActive: Bool
    let onClear: () -> Void
    let onCancel: () -> Void
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isAnimating: Bool = false
    @State private var placeholder: String = "在记忆中寻找..."
    @State private var currentPlaceholderIndex: Int = 0
    @FocusState private var isTextFieldFocused: Bool
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    
    // 诗意的占位符文案
    private let poeticPlaceholders = [
        "在记忆中寻找...",
        "搜索时光的片段...",
        "寻找心中的故事...",
        "探索回忆的宝藏...",
        "发现被遗忘的美好...",
        "追寻岁月的足迹..."
    ]
    
    var body: some View {
        HStack(spacing: 16) {
            // 主搜索栏
            searchField
            
            // 取消按钮
            if isActive {
                cancelButton
                    .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isActive)
        .onAppear {
            startPlaceholderAnimation()
        }
        .onChange(of: isActive) { active in
            if active {
                isTextFieldFocused = true
            } else {
                isTextFieldFocused = false
            }
        }
    }
    
    // MARK: - 搜索输入框
    private var searchField: some View {
        HStack(spacing: 12) {
            // 搜索图标
            searchIcon
            
            // 输入框
            textField
            
            // 清除按钮
            if !searchText.isEmpty {
                clearButton
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(searchBarBackground)
        .clipShape(RoundedRectangle(cornerRadius: isActive ? 16 : 20))
        .overlay(
            RoundedRectangle(cornerRadius: isActive ? 16 : 20)
                .stroke(borderGradient, lineWidth: 1.5)
        )
        .scaleEffect(isActive ? 1.02 : 1.0)
        .shadow(
            color: shadowColor,
            radius: isActive ? 12 : 8,
            x: 0,
            y: isActive ? 6 : 4
        )
        .onTapGesture {
            if !isActive {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    isActive = true
                }
                lightImpact.impactOccurred()
            }
        }
    }
    
    // MARK: - 搜索图标
    private var searchIcon: some View {
        Image(systemName: isActive ? "magnifyingglass.circle.fill" : "magnifyingglass")
            .font(.system(size: 18, weight: .medium))
            .foregroundStyle(iconGradient)
            .scaleEffect(isAnimating ? 1.1 : 1.0)
            .rotationEffect(.degrees(isAnimating ? 180 : 0))
            .animation(
                .easeInOut(duration: 0.3),
                value: isAnimating
            )
    }
    
    // MARK: - 文本输入框
    private var textField: some View {
        TextField("", text: $searchText, prompt: promptText)
            .font(.system(size: 16, weight: .regular, design: .rounded))
            .foregroundColor(.primary)
            .textFieldStyle(PlainTextFieldStyle())
            .focused($isTextFieldFocused)
            .onSubmit {
                lightImpact.impactOccurred()
            }
            .onChange(of: searchText) { _ in
                if !searchText.isEmpty {
                    lightImpact.impactOccurred()
                }
            }
    }
    
    // MARK: - 占位符文本
    private var promptText: Text {
        Text(placeholder)
            .foregroundColor(.secondary)
            .font(.system(size: 16, weight: .regular, design: .rounded))
    }
    
    // MARK: - 清除按钮
    private var clearButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                searchText = ""
                onClear()
            }
            lightImpact.impactOccurred()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .background(
                    Circle()
                        .fill(Color(.systemBackground))
                        .frame(width: 20, height: 20)
                )
        }
        .buttonStyle(ScaleButtonStyle())
    }
    
    // MARK: - 取消按钮
    private var cancelButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                isActive = false
                onCancel()
            }
            lightImpact.impactOccurred()
        }) {
            Text("取消")
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
        }
        .buttonStyle(ScaleButtonStyle())
    }
    
    // MARK: - 样式计算属性
    private var searchBarBackground: some View {
        ZStack {
            // 基础磨砂背景
            RoundedRectangle(cornerRadius: isActive ? 16 : 20)
                .fill(.ultraThinMaterial)
            
            // 活跃状态的光晕
            if isActive {
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        RadialGradient(
                            colors: [
                                themeManager.currentTheme.primaryColor.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 30,
                            endRadius: 100
                        )
                    )
            }
        }
    }
    
    private var borderGradient: LinearGradient {
        LinearGradient(
            colors: isActive ? [
                themeManager.currentTheme.primaryColor.opacity(0.6),
                themeManager.currentTheme.secondaryColor.opacity(0.4),
                Color.white.opacity(0.3)
            ] : [
                Color.white.opacity(0.6),
                Color.white.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var iconGradient: LinearGradient {
        LinearGradient(
            colors: isActive ? [
                themeManager.currentTheme.primaryColor,
                themeManager.currentTheme.secondaryColor
            ] : [
                Color.secondary,
                Color.secondary.opacity(0.8)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var shadowColor: Color {
        isActive ? 
        themeManager.currentTheme.primaryColor.opacity(0.2) : 
        Color.black.opacity(0.08)
    }
    
    // MARK: - 动画函数
    private func startPlaceholderAnimation() {
        Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            guard !isActive && searchText.isEmpty else { return }
            
            withAnimation(.easeInOut(duration: 0.5)) {
                isAnimating = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                currentPlaceholderIndex = (currentPlaceholderIndex + 1) % poeticPlaceholders.count
                placeholder = poeticPlaceholders[currentPlaceholderIndex]
                
                withAnimation(.easeInOut(duration: 0.5)) {
                    isAnimating = false
                }
            }
        }
    }
}

// MARK: - 智能搜索建议
struct SearchSuggestions: View {
    let suggestions: [String]
    let onSelect: (String) -> Void
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var visibleSuggestions: [String] = []
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach(Array(visibleSuggestions.enumerated()), id: \.offset) { index, suggestion in
                suggestionRow(suggestion: suggestion, index: index)
                
                if index < visibleSuggestions.count - 1 {
                    Divider()
                        .opacity(0.3)
                }
            }
        }
        .padding(.vertical, 8)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
        .onAppear {
            animateIn()
        }
        .onChange(of: suggestions) { _ in
            animateIn()
        }
    }
    
    private func suggestionRow(suggestion: String, index: Int) -> some View {
        Button(action: {
            onSelect(suggestion)
        }) {
            HStack(spacing: 12) {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                
                Text(suggestion)
                    .font(.system(size: 15, weight: .regular, design: .rounded))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Image(systemName: "arrow.up.left")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color(.tertiaryLabel))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .opacity(visibleSuggestions.contains(suggestion) ? 1.0 : 0.0)
        .offset(x: visibleSuggestions.contains(suggestion) ? 0 : 20)
    }
    
    private func animateIn() {
        visibleSuggestions.removeAll()
        
        for (index, suggestion) in suggestions.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.1) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    visibleSuggestions.append(suggestion)
                }
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        PoeticSearchBar(
            searchText: .constant(""),
            isActive: .constant(false),
            onClear: {},
            onCancel: {}
        )
        
        PoeticSearchBar(
            searchText: .constant("搜索内容"),
            isActive: .constant(true),
            onClear: {},
            onCancel: {}
        )
        
        SearchSuggestions(
            suggestions: ["美好回忆", "重要时刻", "珍贵瞬间"],
            onSelect: { _ in }
        )
    }
    .padding()
    .environmentObject(ThemeManager())
}