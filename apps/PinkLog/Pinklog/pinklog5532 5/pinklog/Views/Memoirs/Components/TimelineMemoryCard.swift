import SwiftUI

struct TimelineMemoryCard: View {
    let story: UsageRecord
    let isLast: Bool
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isVisible: Bool = false
    @State private var timelineAnimation: Bool = false
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        HStack(alignment: .top, spacing: 20) {
            // 时间轴指示器
            timelineIndicator
            
            // 内容卡片
            contentCard
        }
        .opacity(isVisible ? 1.0 : 0.0)
        .offset(x: isVisible ? 0 : 30)
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                isVisible = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    timelineAnimation = true
                }
            }
        }
    }
    
    // MARK: - 时间轴指示器
    private var timelineIndicator: some View {
        VStack(spacing: 0) {
            // 时间点
            ZStack {
                // 外圈光晕
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                emotionalColor.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 8,
                            endRadius: 20
                        )
                    )
                    .frame(width: 32, height: 32)
                    .scaleEffect(timelineAnimation ? 1.2 : 1.0)
                    .opacity(timelineAnimation ? 0.6 : 1.0)
                    .animation(
                        .easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true),
                        value: timelineAnimation
                    )
                
                // 主圆点
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                emotionalColor,
                                emotionalColor.opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 16, height: 16)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                    )
                    .shadow(color: emotionalColor.opacity(0.4), radius: 4, x: 0, y: 2)
                
                // 内部闪烁点
                if story.emotionalValue >= 4 {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 4, height: 4)
                        .opacity(timelineAnimation ? 1.0 : 0.3)
                        .animation(
                            .easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: true),
                            value: timelineAnimation
                        )
                }
            }
            
            // 连接线
            if !isLast {
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                emotionalColor.opacity(0.6),
                                emotionalColor.opacity(0.3),
                                themeManager.currentTheme.primaryColor.opacity(0.2)
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 2)
                    .frame(minHeight: 60)
                    .clipShape(RoundedRectangle(cornerRadius: 1))
            }
        }
        .frame(width: 40)
    }
    
    // MARK: - 内容卡片
    private var contentCard: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 顶部时间信息
            timeHeader
            
            // 主要内容区域
            mainContent
            
            // 底部元信息
            metaInfo
        }
        .padding(20)
        .background(cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 18))
        .shadow(
            color: shadowColor,
            radius: 8,
            x: 0,
            y: 4
        )
        .overlay(
            // 边框装饰
            RoundedRectangle(cornerRadius: 18)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.5),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
    }
    
    // MARK: - 时间头部
    private var timeHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(preciseTimeString)
                    .font(.system(size: 14, weight: .semibold, design: .rounded))
                    .foregroundColor(emotionalColor)
                
                Text(relativeTimeString)
                    .font(.system(size: 12, weight: .regular, design: .rounded))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 情感强度指示器
            EmotionalIntensityIndicator(level: Int32(story.emotionalValue))
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - 主要内容
    private var mainContent: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            if let title = story.title, !title.isEmpty {
                Text(title)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }

            // 内容预览
            Text(contentPreview)
                .font(.system(size: 15, weight: .regular, design: .rounded))
                .foregroundColor(.secondary)
                .lineLimit(1)
                .lineSpacing(3)
            
            // 多媒体指示器
            if hasImages || hasAudioRecordings {
                HStack(spacing: 8) {
                    if hasImages {
                        HStack(spacing: 4) {
                            Image(systemName: "photo")
                                .font(.system(size: 12, weight: .medium))
                            Text("图片")
                                .font(.system(size: 12, weight: .medium, design: .rounded))
                        }
                        .foregroundColor(emotionalColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(emotionalColor.opacity(0.12))
                        )
                    }
                    
                    if hasAudioRecordings {
                        HStack(spacing: 4) {
                            Image(systemName: "waveform")
                                .font(.system(size: 12, weight: .medium))
                            Text("语音")
                                .font(.system(size: 12, weight: .medium, design: .rounded))
                        }
                        .foregroundColor(emotionalColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(emotionalColor.opacity(0.12))
                        )
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 元信息
    private var metaInfo: some View {
        HStack {
            // 产品标签
            if let product = story.product {
                Label(product.name ?? "产品", systemImage: "cube.transparent")
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(Color.secondary.opacity(0.7))
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 装饰性标记
            HStack(spacing: 6) {
                ForEach(0..<min(Int(story.emotionalValue), 5), id: \.self) { _ in
                    Circle()
                        .fill(emotionalColor.opacity(0.6))
                        .frame(width: 4, height: 4)
                }
            }
        }
        .padding(.top, 12)
    }
    
    // MARK: - 背景样式
    private var cardBackground: some View {
        ZStack {
            // 基础磨砂背景
            RoundedRectangle(cornerRadius: 18)
                .fill(.ultraThinMaterial)
            
            // 情感色彩叠加
            RoundedRectangle(cornerRadius: 18)
                .fill(
                    LinearGradient(
                        colors: [
                            emotionalColor.opacity(0.08),
                            emotionalColor.opacity(0.03),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 高情感值的特殊光效
            if story.emotionalValue >= 4 {
                RoundedRectangle(cornerRadius: 18)
                    .fill(
                        RadialGradient(
                            colors: [
                                emotionalColor.opacity(0.15),
                                Color.clear
                            ],
                            center: .topTrailing,
                            startRadius: 30,
                            endRadius: 120
                        )
                    )
            }
        }
    }
    
    // MARK: - 计算属性
    private var emotionalColor: Color {
        switch story.emotionalValue {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
    
    private var shadowColor: Color {
        emotionalColor.opacity(story.emotionalValue >= 4 ? 0.2 : 0.1)
    }
    
    private var preciseTimeString: String {
        guard let date = story.date else { return "未知时间" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
    }
    
    private var relativeTimeString: String {
        guard let date = story.date else { return "" }
        let calendar = Calendar.current
        let now = Date()
        
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                if days < 7 {
                    return "\(days)天前"
                } else if days < 30 {
                    return "\(days / 7)周前"
                } else if days < 365 {
                    return "\(days / 30)个月前"
                } else {
                    return "\(days / 365)年前"
                }
            }
        }
        return "很久以前"
    }
    
    private var contentPreview: String {
        // 优先使用memories字段，然后是notes字段
        if let memories = story.memories, !memories.isEmpty {
            return String(memories.prefix(150)) + (memories.count > 150 ? "..." : "")
        } else if let notes = story.notes, !notes.isEmpty {
            return String(notes.prefix(150)) + (notes.count > 150 ? "..." : "")
        } else {
            return "一段值得回味的美好时光，每一个细节都闪闪发光..."
        }
    }
    
    private var hasImages: Bool {
        guard let imageData = story.images else { return false }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                return !imageDatas.isEmpty
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return !images.isEmpty
            }
            return false
        } catch {
            return false
        }
    }
    
    private var hasAudioRecordings: Bool {
        return story.hasAudio
    }
}

// MARK: - 情感强度指示器
struct EmotionalIntensityIndicator: View {
    let level: Int32
    @State private var animateWaves: Bool = false
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<5, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(
                        index < level ? 
                        LinearGradient(
                            colors: [intensityColor, intensityColor.opacity(0.7)],
                            startPoint: .top,
                            endPoint: .bottom
                        ) :
                        LinearGradient(
                            colors: [Color.gray.opacity(0.3), Color.gray.opacity(0.2)],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 3, height: CGFloat(6 + index * 2))
                    .scaleEffect(y: animateWaves && index < level ? 1.2 : 1.0)
                    .animation(
                        .easeInOut(duration: 0.8)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.1),
                        value: animateWaves
                    )
            }
        }
        .onAppear {
            if level >= 4 {
                animateWaves = true
            }
        }
    }
    
    private var intensityColor: Color {
        switch level {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
}

#Preview {
    ScrollView {
        VStack(spacing: 24) {
            TimelineMemoryCard(story: UsageRecord(), isLast: false)
            TimelineMemoryCard(story: UsageRecord(), isLast: false)
            TimelineMemoryCard(story: UsageRecord(), isLast: true)
        }
        .padding()
    }
    .environmentObject(ThemeManager())
}