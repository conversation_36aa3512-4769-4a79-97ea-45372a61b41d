import SwiftUI

enum MosaicCellSize {
    case normal
    case large
    
    var dimensions: (width: CGFloat, height: CGFloat) {
        switch self {
        case .normal:
            return (160, 140)
        case .large:
            return (160, 200)
        }
    }
}

struct MosaicMemoryCell: View {
    let story: UsageRecord
    let size: MosaicCellSize
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isVisible: Bool = false
    @State private var isPressed: Bool = false
    @State private var shimmerAnimation: Bool = false
    @State private var particleAnimation: Bool = false
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        ZStack {
            // 背景层
            cellBackground
            
            // 内容层
            cellContent
            
            // 装饰粒子效果
            decorativeParticles
            
            // 前景装饰
            foregroundDecorations
        }
        .frame(width: size.dimensions.width, height: size.dimensions.height)
        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        .shadow(
            color: shadowColor,
            radius: isPressed ? 6 : 10,
            x: 0,
            y: isPressed ? 3 : 6
        )
        .scaleEffect(isPressed ? 0.95 : (isVisible ? 1.0 : 0.9))
        .opacity(isVisible ? 1.0 : 0.0)
        .rotation3DEffect(
            .degrees(isPressed ? 2 : 0),
            axis: (x: 0.1, y: 0.1, z: 0)
        )
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                isVisible = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                startAnimations()
            }
        }
        .allowsHitTesting(true)
    }
    
    // MARK: - 背景层
    private var cellBackground: some View {
        ZStack {
            // 基础背景
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(.ultraThinMaterial)
            
            // 情感色彩渐变
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(
                    LinearGradient(
                        colors: emotionalGradient,
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .opacity(0.6)
            
            // 微光效果
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(
                    RadialGradient(
                        colors: [
                            Color.white.opacity(shimmerAnimation ? 0.3 : 0.1),
                            Color.clear
                        ],
                        center: UnitPoint(x: 0.7, y: 0.3),
                        startRadius: 20,
                        endRadius: 80
                    )
                )
                .animation(
                    .easeInOut(duration: 2.0)
                    .repeatForever(autoreverses: true),
                    value: shimmerAnimation
                )
            
            // 边框光晕
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            emotionalColor.opacity(0.4),
                            Color.white.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        }
    }
    
    // MARK: - 内容层
    private var cellContent: some View {
        VStack(spacing: 0) {
            // 顶部区域
            topSection
            
            // 中间内容区域
            if size == .large {
                expandedContentSection
            } else {
                compactContentSection
            }
            
            // 底部装饰
            bottomSection
        }
        .padding(16)
    }
    
    // MARK: - 顶部区域
    private var topSection: some View {
        HStack {
            // 日期标记
            VStack(alignment: .leading, spacing: 2) {
                Text(monthDay)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                
                Text(year)
                    .font(.system(size: 10, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 情感宝石
            EmotionGem(emotionalValue: Int32(story.emotionalValue))
        }
        .padding(.bottom, size == .large ? 16 : 8)
    }
    
    // MARK: - 紧凑内容区域
    private var compactContentSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            if let coverImage = storyCoverImage {
                // 有封面图的布局
                VStack(alignment: .leading, spacing: 8) {
                    // 封面图
                    Image(uiImage: coverImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 60)
                        .frame(maxWidth: .infinity)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.white.opacity(0.2), lineWidth: 0.5)
                        )
                    
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 13, weight: .semibold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                    
                    // 多媒体指示器
                    if hasImages || hasAudioRecordings {
                        HStack(spacing: 4) {
                            if hasImages {
                                Image(systemName: "photo")
                                    .font(.system(size: 8, weight: .medium))
                            }
                            if hasAudioRecordings {
                                Image(systemName: "waveform")
                                    .font(.system(size: 8, weight: .medium))
                            }
                        }
                        .foregroundColor(emotionalColor.opacity(0.7))
                    }
                }
            } else {
                // 无封面图的布局
                VStack(alignment: .leading, spacing: 6) {
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 14, weight: .semibold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(2)
                    }
                    
                    // 简短预览
                    Text(briefPreview)
                        .font(.system(size: 12, weight: .regular, design: .rounded))
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    // 多媒体指示器
                    if hasAudioRecordings {
                        HStack(spacing: 4) {
                            Image(systemName: "waveform")
                                .font(.system(size: 8, weight: .medium))
                            Text("语音")
                                .font(.system(size: 10, weight: .medium, design: .rounded))
                        }
                        .foregroundColor(emotionalColor.opacity(0.7))
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(emotionalColor.opacity(0.1))
                        )
                    }
                }
            }
            
            Spacer(minLength: 0)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 扩展内容区域
    private var expandedContentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            if let coverImage = storyCoverImage {
                // 有封面图的布局
                VStack(alignment: .leading, spacing: 12) {
                    // 封面图
                    Image(uiImage: coverImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 80)
                        .frame(maxWidth: .infinity)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.3),
                                            emotionalColor.opacity(0.2)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .shadow(color: emotionalColor.opacity(0.15), radius: 4, x: 0, y: 2)
                    
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(2)
                    }
                    
                    // 详细预览
                    Text(detailedPreview)
                        .font(.system(size: 13, weight: .regular, design: .rounded))
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .lineSpacing(2)
                    
                    // 多媒体指示器
                    if hasImages || hasAudioRecordings {
                        HStack(spacing: 6) {
                            if hasImages {
                                HStack(spacing: 3) {
                                    Image(systemName: "photo")
                                        .font(.system(size: 9, weight: .medium))
                                    Text("图片")
                                        .font(.system(size: 10, weight: .medium, design: .rounded))
                                }
                                .foregroundColor(emotionalColor)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    Capsule()
                                        .fill(emotionalColor.opacity(0.1))
                                )
                            }
                            
                            if hasAudioRecordings {
                                HStack(spacing: 3) {
                                    Image(systemName: "waveform")
                                        .font(.system(size: 9, weight: .medium))
                                    Text("语音")
                                        .font(.system(size: 10, weight: .medium, design: .rounded))
                                }
                                .foregroundColor(emotionalColor)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    Capsule()
                                        .fill(emotionalColor.opacity(0.1))
                                )
                            }
                        }
                    }
                    
                    Spacer(minLength: 0)
                }
            } else {
                // 无封面图的布局
                VStack(alignment: .leading, spacing: 12) {
                    // 标题
                    if let title = story.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                            .lineLimit(2)
                    }
                    
                    // 详细预览
                    Text(detailedPreview)
                        .font(.system(size: 13, weight: .regular, design: .rounded))
                        .foregroundColor(.secondary)
                        .lineLimit(5)
                        .lineSpacing(2)
                    
                    // 多媒体指示器
                    if hasAudioRecordings {
                        HStack(spacing: 4) {
                            Image(systemName: "waveform")
                                .font(.system(size: 9, weight: .medium))
                            Text("语音")
                                .font(.system(size: 10, weight: .medium, design: .rounded))
                        }
                        .foregroundColor(emotionalColor.opacity(0.8))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(emotionalColor.opacity(0.12))
                        )
                    }
                    
                    Spacer(minLength: 0)
                }
            }
            
            // 产品信息
            if let product = story.product {
                HStack {
                    Image(systemName: "cube.fill")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(emotionalColor.opacity(0.8))
                    
                    Text(product.name ?? "产品")
                        .font(.system(size: 11, weight: .medium, design: .rounded))
                        .foregroundColor(emotionalColor)
                        .lineLimit(1)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(emotionalColor.opacity(0.15))
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 底部区域
    private var bottomSection: some View {
        HStack {
            // 相对时间
            Text(relativeTime)
                .font(.system(size: 10, weight: .medium, design: .rounded))
                .foregroundColor(Color.secondary.opacity(0.7))
            
            Spacer()
            
            // 情感强度点
            HStack(spacing: 3) {
                ForEach(0..<min(Int(story.emotionalValue), 5), id: \.self) { index in
                    Circle()
                        .fill(emotionalColor.opacity(0.7))
                        .frame(width: 4, height: 4)
                        .scaleEffect(particleAnimation ? 1.2 : 1.0)
                        .animation(
                            .easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.1),
                            value: particleAnimation
                        )
                }
            }
        }
    }
    
    // MARK: - 装饰粒子效果
    private var decorativeParticles: some View {
        ZStack {
            // 只对高情感值的回忆显示特殊粒子效果
            if story.emotionalValue >= 4 {
                ForEach(0..<6, id: \.self) { index in
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    emotionalColor.opacity(0.6),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 0,
                                endRadius: 4
                            )
                        )
                        .frame(width: 3, height: 3)
                        .position(
                            x: CGFloat.random(in: 20...(size.dimensions.width - 20)),
                            y: CGFloat.random(in: 20...(size.dimensions.height - 20))
                        )
                        .opacity(particleAnimation ? 0.8 : 0.2)
                        .scaleEffect(particleAnimation ? 1.5 : 0.5)
                        .animation(
                            .easeInOut(duration: Double.random(in: 1.5...3.0))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                            value: particleAnimation
                        )
                }
            }
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - 前景装饰
    private var foregroundDecorations: some View {
        VStack {
            HStack {
                Spacer()
                
                // 角落装饰
                if story.emotionalValue == 5 {
                    Image(systemName: "sparkles")
                        .font(.system(size: 12, weight: .light))
                        .foregroundColor(emotionalColor.opacity(0.6))
                        .rotationEffect(.degrees(particleAnimation ? 15 : -15))
                        .animation(
                            .easeInOut(duration: 2.0)
                            .repeatForever(autoreverses: true),
                            value: particleAnimation
                        )
                        .padding(.top, 8)
                        .padding(.trailing, 8)
                }
            }
            
            Spacer()
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - 计算属性
    private var cornerRadius: CGFloat {
        size == .large ? 20 : 16
    }
    
    private var emotionalColor: Color {
        switch story.emotionalValue {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
    
    private var emotionalGradient: [Color] {
        [
            emotionalColor.opacity(0.3),
            emotionalColor.opacity(0.1),
            Color.clear
        ]
    }
    
    private var shadowColor: Color {
        emotionalColor.opacity(story.emotionalValue >= 4 ? 0.25 : 0.15)
    }
    
    private var monthDay: String {
        guard let date = story.date else { return "??" }
        let formatter = DateFormatter()
        formatter.dateFormat = "M/d"
        return formatter.string(from: date)
    }
    
    private var year: String {
        guard let date = story.date else { return "????" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: date)
    }
    
    private var briefPreview: String {
        if let memories = story.memories, !memories.isEmpty {
            return String(memories.prefix(60)) + (memories.count > 60 ? "..." : "")
        } else if let notes = story.notes, !notes.isEmpty {
            return String(notes.prefix(60)) + (notes.count > 60 ? "..." : "")
        } else {
            return "美好回忆..."
        }
    }
    
    private var detailedPreview: String {
        if let memories = story.memories, !memories.isEmpty {
            return String(memories.prefix(100)) + (memories.count > 100 ? "..." : "")
        } else if let notes = story.notes, !notes.isEmpty {
            return String(notes.prefix(100)) + (notes.count > 100 ? "..." : "")
        } else {
            return "这是一段值得珍藏的美好时光，每一个细节都闪闪发光..."
        }
    }
    
    private var relativeTime: String {
        guard let date = story.date else { return "" }
        let calendar = Calendar.current
        let now = Date()
        
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                if days < 7 {
                    return "\(days)天前"
                } else if days < 30 {
                    return "\(days / 7)周前"
                } else {
                    return "\(days / 30)月前"
                }
            }
        }
        return "很久前"
    }
    
    // MARK: - 动画函数
    private func startAnimations() {
        shimmerAnimation = true
        
        if story.emotionalValue >= 3 {
            particleAnimation = true
        }
    }
    
    // MARK: - 辅助计算属性
    private var hasImages: Bool {
        guard let imageData = story.images else { return false }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                return !imageDatas.isEmpty
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return !images.isEmpty
            }
            return false
        } catch {
            return false
        }
    }
    
    private var hasAudioRecordings: Bool {
        return story.hasAudio
    }
    
    private var storyCoverImage: UIImage? {
        guard let imageData = story.images else { return nil }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                if let firstImageData = imageDatas.first,
                   let image = UIImage(data: firstImageData) {
                    return image
                }
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return images.first
            }
            return nil
        } catch {
            return nil
        }
    }
}

#Preview {
    ScrollView {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2),
            spacing: 8
        ) {
            MosaicMemoryCell(story: UsageRecord(), size: .large)
            MosaicMemoryCell(story: UsageRecord(), size: .normal)
            MosaicMemoryCell(story: UsageRecord(), size: .normal)
            MosaicMemoryCell(story: UsageRecord(), size: .large)
        }
        .padding()
    }
    .environmentObject(ThemeManager())
}