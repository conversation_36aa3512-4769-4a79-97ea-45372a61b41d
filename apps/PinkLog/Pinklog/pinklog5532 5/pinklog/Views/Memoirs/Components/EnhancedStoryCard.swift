import SwiftUI

struct EnhancedStoryCard: View {
    let story: UsageRecord
    let index: Int
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isVisible: Bool = false
    @State private var isPressed: Bool = false
    @State private var shimmerOffset: CGFloat = -200
    
    // 触感反馈
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let mediumImpact = UIImpactFeedbackGenerator(style: .medium)
    
    var body: some View {
        VStack(spacing: 0) {
            // 故事卡片主体
            ZStack {
                // 背景层
                cardBackground
                
                // 内容层
                cardContent
                
                // 装饰层
                decorativeElements
            }
            .frame(height: cardHeight)
            .clipShape(RoundedRectangle(cornerRadius: 24))
            .shadow(
                color: shadowColor,
                radius: isPressed ? 8 : 12,
                x: 0,
                y: isPressed ? 4 : 8
            )
            .scaleEffect(isPressed ? 0.96 : 1.0)
            .scaleEffect(isVisible ? 1.0 : 0.95)
            .opacity(isVisible ? 1.0 : 0.0)
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(Double(index) * 0.1)) {
                    isVisible = true
                }
                startShimmerAnimation()
            }
            .onLongPressGesture(
                minimumDuration: 0.5,
                maximumDistance: 10
            ) { isPressing in
                withAnimation(.easeOut(duration: 0.1)) {
                    isPressed = isPressing
                }
                if isPressing {
                    mediumImpact.impactOccurred()
                }
            } perform: {
                // 长按操作
            }
        }
        .padding(.horizontal, 4)
        .padding(.vertical, 8)
    }
    
    // MARK: - 卡片背景
    private var cardBackground: some View {
        ZStack {
            // 基础背景
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.6),
                                    Color.white.opacity(0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
            
            // 渐变叠加
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            emotionalGradientColors.0.opacity(0.15),
                            emotionalGradientColors.1.opacity(0.08),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 光晕效果
            if hasHighEmotionalValue {
                RoundedRectangle(cornerRadius: 24)
                    .fill(
                        RadialGradient(
                            colors: [
                                emotionalGradientColors.0.opacity(0.2),
                                Color.clear
                            ],
                            center: .topTrailing,
                            startRadius: 50,
                            endRadius: 150
                        )
                    )
            }
        }
    }
    
    // MARK: - 卡片内容
    private var cardContent: some View {
        VStack(spacing: 0) {
            // 顶部区域：日期和情感标签
            topSection
            
            // 主要内容区域
            mainContentSection
            
            // 底部区域：产品信息和装饰
            bottomSection
        }
        .padding(20)
    }
    
    // MARK: - 顶部区域
    private var topSection: some View {
        HStack {
            // 日期标签
            VStack(alignment: .leading, spacing: 2) {
                Text(formattedDate)
                    .font(.system(size: 13, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
                
                Text(relativeDate)
                    .font(.system(size: 11, weight: .regular, design: .rounded))
                    .foregroundColor(Color.secondary.opacity(0.7))
            }
            
            Spacer()
            
            // 情感标签
            EmotionGem(emotionalValue: Int32(story.emotionalValue))
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - 主要内容区域
    private var mainContentSection: some View {
        HStack(alignment: .top, spacing: 16) {
            // 左侧文本内容
            VStack(alignment: .leading, spacing: 12) {
                // 故事标题
                if let title = story.title, !title.isEmpty {
                    Text(title)
                        .font(.system(size: 18, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                        .multilineTextAlignment(.leading)
                }
                
                // 故事摘要
                Text(storyPreview)
                    .font(.system(size: 14, weight: .regular, design: .rounded))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                    .multilineTextAlignment(.leading)
                    .lineSpacing(2)
                
                // 多媒体指示器
                if storyCoverImage != nil || hasAudioRecordings {
                    HStack(spacing: 8) {
                        if storyCoverImage != nil {
                            HStack(spacing: 4) {
                                Image(systemName: "photo")
                                    .font(.system(size: 10, weight: .medium))
                                Text("图片")
                                    .font(.system(size: 11, weight: .medium, design: .rounded))
                            }
                            .foregroundColor(emotionalGradientColors.0)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(emotionalGradientColors.0.opacity(0.1))
                            )
                        }
                        
                        if hasAudioRecordings {
                            HStack(spacing: 4) {
                                Image(systemName: "waveform")
                                    .font(.system(size: 10, weight: .medium))
                                Text("语音")
                                    .font(.system(size: 11, weight: .medium, design: .rounded))
                            }
                            .foregroundColor(emotionalGradientColors.0)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(emotionalGradientColors.0.opacity(0.1))
                            )
                        }
                    }
                }
                
                Spacer(minLength: 0)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 右侧封面图
            if let coverImage = storyCoverImage {
                VStack {
                    Image(uiImage: coverImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.3),
                                            emotionalGradientColors.0.opacity(0.2)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .shadow(color: emotionalGradientColors.0.opacity(0.15), radius: 6, x: 0, y: 3)
                    
                    Spacer()
                }
            }
        }
    }
    
    // MARK: - 底部区域
    private var bottomSection: some View {
        HStack {
            // 产品信息
            if let product = story.product {
                HStack(spacing: 8) {
                    Image(systemName: "cube.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(themeManager.currentTheme.primaryColor.opacity(0.7))
                    
                    Text(product.name ?? "未知产品")
                        .font(.system(size: 13, weight: .medium, design: .rounded))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .lineLimit(1)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(themeManager.currentTheme.primaryColor.opacity(0.12))
                )
            }
            
            Spacer()
            
            // 装饰性元素
            HStack(spacing: 4) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: emotionalGradientColors.0.opacity(0.6 - Double(index) * 0.2) == Color.clear ? 
                                [themeManager.currentTheme.primaryColor.opacity(0.6 - Double(index) * 0.2)] : 
                                [emotionalGradientColors.0.opacity(0.6 - Double(index) * 0.2)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 6 - Double(index), height: 6 - Double(index))
                        .scaleEffect(isVisible ? 1.0 : 0.5)
                        .animation(
                            .spring(response: 0.6, dampingFraction: 0.8)
                            .delay(Double(index) * 0.1 + 0.5),
                            value: isVisible
                        )
                }
            }
        }
    }
    
    // MARK: - 装饰元素
    private var decorativeElements: some View {
        ZStack {
            // 微光效果
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.4),
                            Color.clear,
                            Color.clear,
                            Color.white.opacity(0.2)
                        ],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .mask(
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [Color.clear, Color.white, Color.clear],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .rotationEffect(.degrees(30))
                        .offset(x: shimmerOffset)
                )
            
            // 角落装饰
            VStack {
                HStack {
                    Spacer()
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.white.opacity(0.3),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 2,
                                endRadius: 20
                            )
                        )
                        .frame(width: 40, height: 40)
                        .offset(x: 15, y: -15)
                }
                Spacer()
            }
        }
    }
    
    // MARK: - 计算属性
    private var cardHeight: CGFloat {
        switch story.emotionalValue {
        case 4...5: return 180
        case 3: return 160
        default: return 150
        }
    }
    
    private var shadowColor: Color {
        if hasHighEmotionalValue {
            return emotionalGradientColors.0.opacity(0.25)
        } else {
            return Color.black.opacity(0.08)
        }
    }
    
    private var hasHighEmotionalValue: Bool {
        story.emotionalValue >= 4
    }
    
    private var emotionalGradientColors: (Color, Color) {
        switch story.emotionalValue {
        case 5: return (Color.pink, Color.purple)
        case 4: return (Color.orange, Color.red)
        case 3: return (Color.blue, Color.cyan)
        case 2: return (Color.green, Color.mint)
        default: return (Color.gray, Color.secondary)
        }
    }
    
    private var formattedDate: String {
        guard let date = story.date else { return "未知日期" }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        return formatter.string(from: date)
    }
    
    private var relativeDate: String {
        guard let date = story.date else { return "" }
        let calendar = Calendar.current
        let now = Date()
        
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            let components = calendar.dateComponents([.day], from: date, to: now)
            if let days = components.day, days > 0 {
                return "\(days)天前"
            } else {
                return "最近"
            }
        }
    }
    
    private var storyPreview: String {
        // 优先使用memories字段，然后是notes字段
        if let memories = story.memories, !memories.isEmpty {
            return String(memories.prefix(120)) + (memories.count > 120 ? "..." : "")
        } else if let notes = story.notes, !notes.isEmpty {
            return String(notes.prefix(120)) + (notes.count > 120 ? "..." : "")
        } else {
            return "这是一段美好的回忆，值得细细品味..."
        }
    }
    
    private var storyCoverImage: UIImage? {
        guard let imageData = story.images else { return nil }
        
        do {
            // 尝试读取为Data数组（新格式）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                if let firstImageData = imageDatas.first,
                   let image = UIImage(data: firstImageData) {
                    return image
                }
            }
            // 尝试读取为UIImage数组（旧格式兼容）
            else if let images = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [UIImage] {
                return images.first
            }
            return nil
        } catch {
            print("Error loading cover image: \(error)")
            return nil
        }
    }
    
    private var hasAudioRecordings: Bool {
        return story.hasAudio
    }
    
    // MARK: - 动画函数
    private func startShimmerAnimation() {
        withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: false)) {
            shimmerOffset = 400
        }
    }
}

// MARK: - 情感宝石组件
struct EmotionGem: View {
    let emotionalValue: Int32
    @State private var isGlowing: Bool = false
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            gemColor.opacity(0.3),
                            gemColor.opacity(0.1),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 8,
                        endRadius: 20
                    )
                )
                .frame(width: 32, height: 32)
                .scaleEffect(isGlowing ? 1.2 : 1.0)
            
            // 主体宝石
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            gemColor.opacity(0.8),
                            gemColor.opacity(0.6)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 20, height: 20)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .shadow(color: gemColor.opacity(0.4), radius: 4, x: 0, y: 2)
            
            // 高光效果
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            Color.clear
                        ],
                        center: UnitPoint(x: 0.3, y: 0.3),
                        startRadius: 2,
                        endRadius: 8
                    )
                )
                .frame(width: 20, height: 20)
        }
        .onAppear {
            if emotionalValue >= 4 {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    isGlowing = true
                }
            }
        }
    }
    
    private var gemColor: Color {
        switch emotionalValue {
        case 5: return .pink
        case 4: return .orange
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // 创建预览用的 UsageRecord
        EnhancedStoryCard(story: UsageRecord(), index: 0)
        EnhancedStoryCard(story: UsageRecord(), index: 1)
    }
    .padding()
    .environmentObject(ThemeManager())
}