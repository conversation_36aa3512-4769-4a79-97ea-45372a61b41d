import SwiftUI

struct SerendipityMoment: View {
    @Binding var isPresented: Bool
    let allMemories: [UsageRecord]
    
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var currentMemory: UsageRecord?
    @State private var isRevealing: Bool = false
    @State private var showContent: Bool = false
    @State private var magicParticles: [MagicParticle] = []
    @State private var pulseAnimation: Bool = false
    @State private var rotationDegrees: Double = 0
    
    private let heavyImpact = UIImpactFeedbackGenerator(style: .heavy)
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        ZStack {
            // 神秘背景
            mysticalBackground
            
            // 主要内容
            if showContent {
                mainContent
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            }
            
            // 魔法粒子系统
            magicalParticleSystem
            
            // 关闭按钮
            closeButton
        }
        .onAppear {
            initializeMagicalMoment()
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.height > 100 {
                        dismissView()
                    }
                }
        )
    }
    
    // MARK: - 神秘背景
    private var mysticalBackground: some View {
        ZStack {
            // 基础深色背景
            Color.black.opacity(0.8)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissView()
                }
            
            // 星空效果
            ForEach(0..<50, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.8))
                    .frame(width: CGFloat.random(in: 1...3))
                    .position(
                        x: CGFloat.random(in: 0...400),
                        y: CGFloat.random(in: 0...800)
                    )
                    .opacity(pulseAnimation ? 1.0 : 0.3)
                    .animation(
                        .easeInOut(duration: Double.random(in: 1...3))
                        .repeatForever(autoreverses: true)
                        .delay(Double.random(in: 0...2)),
                        value: pulseAnimation
                    )
            }
            
            // 神秘光晕
            RadialGradient(
                colors: [
                    Color.purple.opacity(0.4),
                    Color.pink.opacity(0.3),
                    Color.blue.opacity(0.2),
                    Color.clear
                ],
                center: .center,
                startRadius: 50,
                endRadius: 300
            )
            .ignoresSafeArea()
            .scaleEffect(pulseAnimation ? 1.3 : 1.0)
            .rotationEffect(.degrees(rotationDegrees))
            .animation(
                .linear(duration: 20)
                .repeatForever(autoreverses: false),
                value: rotationDegrees
            )
        }
    }
    
    // MARK: - 主要内容
    private var mainContent: some View {
        VStack(spacing: 0) {
            // 魔法标题区域
            magicalTitleSection
            
            // 内容揭示区域
            if isRevealing {
                revealedContent
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            } else {
                mysteryBox
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            }
            
            // 魔法操作按钮
            magicalActions
        }
        .padding(28)
        .background(magicalCardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 32))
        .shadow(
            color: Color.purple.opacity(0.3),
            radius: 25,
            x: 0,
            y: 15
        )
        .scaleEffect(showContent ? 1.0 : 0.7)
        .animation(.spring(response: 0.8, dampingFraction: 0.8), value: showContent)
    }
    
    // MARK: - 魔法标题区域
    private var magicalTitleSection: some View {
        VStack(spacing: 16) {
            // 魔法图标
            ZStack {
                // 外圈光环
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.purple.opacity(0.6),
                                Color.pink.opacity(0.4),
                                Color.blue.opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(rotationDegrees))
                
                // 内圈背景
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.purple.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 10,
                            endRadius: 40
                        )
                    )
                    .frame(width: 70, height: 70)
                    .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                
                // 中心图标
                Image(systemName: "sparkles")
                    .font(.system(size: 28, weight: .light))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.purple, Color.pink, Color.blue],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                    .rotationEffect(.degrees(-rotationDegrees * 0.5))
            }
            .animation(
                .easeInOut(duration: 2.0)
                .repeatForever(autoreverses: true),
                value: pulseAnimation
            )
            
            // 主标题
            Text("奇遇时刻")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.purple, Color.pink, Color.blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
            
            // 副标题
            Text(isRevealing ? "命运为你选中了这个回忆" : "让命运为你选择一个神奇的回忆")
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding(.bottom, 32)
    }
    
    // MARK: - 神秘盒子
    private var mysteryBox: some View {
        VStack(spacing: 24) {
            // 神秘盒子视觉
            ZStack {
                // 盒子背景
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.purple.opacity(0.2),
                                Color.pink.opacity(0.1),
                                Color.clear
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 120)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.purple.opacity(0.6),
                                        Color.pink.opacity(0.4)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
                
                // 神秘符号
                VStack(spacing: 12) {
                    HStack(spacing: 16) {
                        ForEach(["star.fill", "heart.fill", "diamond.fill"], id: \.self) { symbol in
                            Image(systemName: symbol)
                                .font(.system(size: 20, weight: .light))
                                .foregroundColor(.white.opacity(0.6))
                                .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                                .animation(
                                    .easeInOut(duration: 1.5)
                                    .repeatForever(autoreverses: true)
                                    .delay(Double.random(in: 0...1)),
                                    value: pulseAnimation
                                )
                        }
                    }
                    
                    Text("？？？")
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundColor(.white.opacity(0.8))
                        .opacity(pulseAnimation ? 1.0 : 0.5)
                }
            }
            
            // 魔法文案
            Text("宇宙中有无数个美好瞬间\n让我们为你找到其中一个...")
                .font(.system(size: 15, weight: .regular, design: .rounded))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
    }
    
    // MARK: - 揭示的内容
    private var revealedContent: some View {
        VStack(spacing: 20) {
            if let memory = currentMemory {
                // 回忆卡片
                magicalMemoryCard(memory)
            } else {
                // 未找到回忆
                noMemoryFound
            }
        }
    }
    
    // MARK: - 魔法回忆卡片
    private func magicalMemoryCard(_ memory: UsageRecord) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 日期和情感
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(formattedDate(memory.date))
                        .font(.system(size: 18, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text(relativeTime(memory.date))
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.purple.opacity(0.8))
                }
                
                Spacer()
                
                // 魔法情感宝石
                MagicalEmotionGem(emotionalValue: Int32(memory.emotionalValue))
            }
            
            // 标题
            if let title = memory.title, !title.isEmpty {
                Text(title)
                    .font(.system(size: 22, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .lineLimit(2)
            }
            
            // 内容预览
            Text(contentPreview(memory))
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(8)
                .lineSpacing(4)
            
            // 产品信息
            if let product = memory.product {
                HStack {
                    Image(systemName: "cube.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.purple)
                    
                    Text(product.name ?? "神秘物品")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.purple)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(Color.purple.opacity(0.2))
                        .overlay(
                            Capsule()
                                .stroke(Color.purple.opacity(0.4), lineWidth: 1)
                        )
                )
            }
        }
        .padding(24)
        .background(magicalContentBackground)
        .clipShape(RoundedRectangle(cornerRadius: 24))
    }
    
    // MARK: - 未找到回忆
    private var noMemoryFound: some View {
        VStack(spacing: 20) {
            Image(systemName: "moon.stars.fill")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white.opacity(0.6))
            
            Text("今夜星空寂静")
                .font(.system(size: 20, weight: .medium, design: .rounded))
                .foregroundColor(.white)
            
            Text("暂时没有发现合适的奇遇\n或许是时候创造新的回忆了")
                .font(.system(size: 15, weight: .regular, design: .rounded))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .padding(.vertical, 40)
    }
    
    // MARK: - 魔法操作按钮
    private var magicalActions: some View {
        HStack(spacing: 16) {
            if isRevealing && currentMemory != nil {
                // 查看详情按钮
                Button(action: {
                    // TODO: 导航到详情页面
                    lightImpact.impactOccurred()
                }) {
                    Text("探索详情")
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 28)
                        .background(
                            LinearGradient(
                                colors: [Color.purple, Color.pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .clipShape(Capsule())
                        .shadow(color: Color.purple.opacity(0.4), radius: 8, x: 0, y: 4)
                }
                .buttonStyle(ScaleButtonStyle())
            }
            
            // 主要魔法按钮
            Button(action: {
                performMagic()
            }) {
                HStack(spacing: 10) {
                    Image(systemName: isRevealing ? "arrow.clockwise" : "wand.and.stars")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text(isRevealing ? "再次施法" : "开启奇遇")
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                }
                .foregroundColor(.purple)
                .padding(.vertical, 14)
                .padding(.horizontal, 28)
                .background(
                    Capsule()
                        .fill(Color.white.opacity(0.9))
                        .shadow(color: Color.purple.opacity(0.2), radius: 6, x: 0, y: 3)
                )
            }
            .buttonStyle(ScaleButtonStyle())
        }
        .padding(.top, 32)
    }
    
    // MARK: - 关闭按钮
    private var closeButton: some View {
        VStack {
            HStack {
                Spacer()
                
                Button(action: {
                    dismissView()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 40, height: 40)
                        )
                }
                .buttonStyle(ScaleButtonStyle())
            }
            .padding(.horizontal, 28)
            .padding(.top, 50)
            
            Spacer()
        }
    }
    
    // MARK: - 背景样式
    private var magicalCardBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 32)
                .fill(.ultraThinMaterial)
            
            RoundedRectangle(cornerRadius: 32)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.purple.opacity(0.3),
                            Color.pink.opacity(0.2),
                            Color.blue.opacity(0.1),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }
    
    private var magicalContentBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.black.opacity(0.2))
            
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.purple.opacity(0.1),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.purple.opacity(0.4),
                                    Color.pink.opacity(0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        }
    }
    
    // MARK: - 魔法粒子系统
    private var magicalParticleSystem: some View {
        ForEach(magicParticles, id: \.id) { particle in
            particle.shape
                .fill(particle.color)
                .frame(width: particle.size, height: particle.size)
                .position(particle.position)
                .opacity(particle.opacity)
                .scaleEffect(particle.scale)
        }
    }
    
    // MARK: - 辅助函数
    private func initializeMagicalMoment() {
        heavyImpact.impactOccurred()
        
        withAnimation(.easeOut(duration: 0.3)) {
            pulseAnimation = true
            rotationDegrees = 360
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                showContent = true
            }
        }
        
        generateMagicParticles()
    }
    
    private func dismissView() {
        lightImpact.impactOccurred()
        
        withAnimation(.easeInOut(duration: 0.3)) {
            showContent = false
            isPresented = false
        }
    }
    
    private func performMagic() {
        heavyImpact.impactOccurred()
        
        if !isRevealing {
            // 首次施法
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                isRevealing = true
            }
            
            // 随机选择一个回忆
            if !allMemories.isEmpty {
                currentMemory = allMemories.randomElement()
            }
        } else {
            // 再次施法
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                isRevealing = false
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                if !allMemories.isEmpty {
                    currentMemory = allMemories.randomElement()
                }
                
                withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                    isRevealing = true
                }
            }
        }
        
        // 重新生成粒子效果
        generateMagicParticles()
    }
    
    private func generateMagicParticles() {
        magicParticles = (0..<30).map { _ in
            MagicParticle(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...400),
                    y: CGFloat.random(in: 0...800)
                ),
                size: CGFloat.random(in: 3...8),
                color: [
                    Color.purple.opacity(0.6),
                    Color.pink.opacity(0.6),
                    Color.blue.opacity(0.6),
                    Color.white.opacity(0.8)
                ].randomElement()!,
                opacity: Double.random(in: 0.3...0.9),
                scale: 1.0,
                shape: [
                    AnyShape(Circle()),
                    AnyShape(Diamond()),
                    AnyShape(Star())
                ].randomElement()!
            )
        }
        
        animateMagicParticles()
    }
    
    private func animateMagicParticles() {
        withAnimation(.linear(duration: 8).repeatForever(autoreverses: false)) {
            for i in magicParticles.indices {
                magicParticles[i].position.y -= 1000
                magicParticles[i].opacity = 0
                magicParticles[i].scale = 2.0
            }
        }
    }
    
    private func formattedDate(_ date: Date?) -> String {
        guard let date = date else { return "未知时间" }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
    }
    
    private func relativeTime(_ date: Date?) -> String {
        guard let date = date else { return "" }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: date, to: Date())
        
        if let days = components.day {
            if days == 0 {
                return "今天的奇遇"
            } else if days == 1 {
                return "昨日的奇遇"
            } else if days < 30 {
                return "\(days)天前的奇遇"
            } else if days < 365 {
                return "\(days / 30)个月前的奇遇"
            } else {
                return "\(days / 365)年前的奇遇"
            }
        }
        
        return "遥远的奇遇"
    }
    
    private func contentPreview(_ memory: UsageRecord) -> String {
        // 优先使用memories字段，然后是notes字段
        if let memories = memory.memories, !memories.isEmpty {
            return String(memories.prefix(300)) + (memories.count > 300 ? "..." : "")
        } else if let notes = memory.notes, !notes.isEmpty {
            return String(notes.prefix(300)) + (notes.count > 300 ? "..." : "")
        } else {
            return "这是一个神奇的时刻，充满了未知的美好和意外的惊喜..."
        }
    }
}

// MARK: - 魔法情感宝石
struct MagicalEmotionGem: View {
    let emotionalValue: Int32
    @State private var isGlowing: Bool = false
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        ZStack {
            // 外圈光环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [
                            gemColor.opacity(0.8),
                            gemColor.opacity(0.4),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
                .frame(width: 36, height: 36)
                .rotationEffect(.degrees(rotationAngle))
            
            // 主体宝石
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            gemColor.opacity(0.9),
                            gemColor.opacity(0.6),
                            gemColor.opacity(0.8)
                        ],
                        center: .center,
                        startRadius: 5,
                        endRadius: 15
                    )
                )
                .frame(width: 24, height: 24)
                .scaleEffect(isGlowing ? 1.1 : 1.0)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.4), lineWidth: 1)
                )
                .shadow(color: gemColor.opacity(0.6), radius: 6, x: 0, y: 3)
        }
        .onAppear {
            withAnimation(.linear(duration: 3).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
            
            if emotionalValue >= 4 {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    isGlowing = true
                }
            }
        }
    }
    
    private var gemColor: Color {
        switch emotionalValue {
        case 5: return .pink
        case 4: return .purple
        case 3: return .blue
        case 2: return .green
        default: return .gray
        }
    }
}

// MARK: - 魔法粒子结构
struct MagicParticle: Identifiable {
    let id: UUID
    var position: CGPoint
    let size: CGFloat
    let color: Color
    var opacity: Double
    var scale: CGFloat
    let shape: AnyShape
}

// MARK: - 自定义形状
struct Diamond: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.midY))
        path.addLine(to: CGPoint(x: rect.midX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.midY))
        path.closeSubpath()
        return path
    }
}

struct Star: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.width / 2, y: rect.height / 2)
        let outerRadius = min(rect.width, rect.height) / 2
        let innerRadius = outerRadius * 0.4
        
        for i in 0..<5 {
            let angle = Double(i) * .pi * 2 / 5 - .pi / 2
            let outerPoint = CGPoint(
                x: center.x + CGFloat(cos(angle)) * outerRadius,
                y: center.y + CGFloat(sin(angle)) * outerRadius
            )
            
            if i == 0 {
                path.move(to: outerPoint)
            } else {
                path.addLine(to: outerPoint)
            }
            
            let innerAngle = angle + .pi / 5
            let innerPoint = CGPoint(
                x: center.x + CGFloat(cos(innerAngle)) * innerRadius,
                y: center.y + CGFloat(sin(innerAngle)) * innerRadius
            )
            path.addLine(to: innerPoint)
        }
        
        path.closeSubpath()
        return path
    }
}

struct AnyShape: Shape {
    private let _path: (CGRect) -> Path
    
    init<S: Shape>(_ wrapped: S) {
        _path = wrapped.path(in:)
    }
    
    func path(in rect: CGRect) -> Path {
        return _path(rect)
    }
}

#Preview {
    SerendipityMoment(
        isPresented: .constant(true),
        allMemories: []
    )
    .environmentObject(ThemeManager())
}