import SwiftUI
import CoreData

struct MemoirsView: View {
    @StateObject private var viewModel: MemoirsViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // 视图状态
    @State private var selectedViewMode: ViewMode = .story
    @State private var scrollOffset: CGFloat = 0
    @State private var isSearchActive: Bool = false
    @State private var showingMemoryOfTheDay: Bool = false
    @State private var showingSerendipity: Bool = false
    @State private var headerOpacity: Double = 1.0
    @State private var currentQuoteIndex: Int = 0
    @State private var isQuoteAnimating: Bool = false
    
    // 动画状态
    @State private var headerAnimation: Bool = false
    @State private var contentAnimation: Bool = false
    @State private var parallaxOffset: CGFloat = 0
    
    // 触感反馈
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let mediumImpact = UIImpactFeedbackGenerator(style: .medium)
    private let heavyImpact = UIImpactFeedbackGenerator(style: .heavy)
    
    // 视图模式
    enum ViewMode: String, CaseIterable {
        case story = "故事模式"
        case timeline = "时光轴"
        case favorites = "收藏回忆"
        
        var icon: String {
            switch self {
            case .story: return "book.pages"
            case .timeline: return "clock.arrow.circlepath"
            case .favorites: return "heart.fill"
            }
        }
    }
    
    // 诗意引言
    private let poeticQuotes = [
        "每一件物品，都是时光的信使",
        "在这里，回忆永远鲜活如初",
        "物品会老去，但故事永远年轻",
        "这些珍藏，见证了我们的成长",
        "时间流逝，而美好永恒",
        "每个故事，都是心灵的港湾",
        "在回忆的海洋里，我们都是诗人"
    ]

    init(context: NSManagedObjectContext) {
        _viewModel = StateObject(wrappedValue: LazyLoadingManager.shared.getMemoirsViewModel(context: context))
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 深度背景系统
                dreamlikeBackground
                    .offset(y: parallaxOffset * 0.3)
                
                // 主内容区域
                mainContentView(geometry: geometry)
                
                // 浮动操作栏
                VStack {
                    Spacer()
                    floatingActionBar
                        .padding(.bottom, 34) // 适配安全区域
                }
                
                // 顶部渐变标题栏
                VStack {
                    sophisticatedHeader
                    Spacer()
                }
            }
            .ignoresSafeArea(.all, edges: .top)
        }
        .navigationBarHidden(true)
        .onAppear {
            initializeView()
        }
        .onChange(of: viewModel.filteredStoryRecords) { _ in
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                contentAnimation = true
            }
        }
        .sheet(isPresented: $showingMemoryOfTheDay) {
            MemoryOfTheDay(
                isPresented: $showingMemoryOfTheDay,
                memories: viewModel.todayInHistoryMemories
            )
            .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingSerendipity) {
            SerendipityMoment(
                isPresented: $showingSerendipity,
                allMemories: viewModel.storyRecords
            )
            .environmentObject(themeManager)
        }
    }
    
    // MARK: - 深度背景系统
    private var dreamlikeBackground: some View {
        ZStack {
            // 基础渐变背景
            LinearGradient(
                colors: [
                    Color(.systemBackground).opacity(0.95),
                    themeManager.currentTheme.primaryColor.opacity(0.08),
                    themeManager.currentTheme.secondaryColor.opacity(0.12),
                    Color(.systemBackground).opacity(0.98)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 光晕效果层
            ZStack {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    themeManager.currentTheme.primaryColor.opacity(0.15),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 50,
                                endRadius: 200
                            )
                        )
                        .frame(width: 300, height: 300)
                        .offset(
                            x: CGFloat([100, -150, 80][index]),
                            y: CGFloat([-200, 300, 100][index])
                        )
                        .scaleEffect(headerAnimation ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: 4.0 + Double(index))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.8),
                            value: headerAnimation
                        )
                }
            }
            
            // 细腻的光点装饰
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(themeManager.currentTheme.primaryColor.opacity(0.3))
                    .frame(width: CGFloat.random(in: 2...6))
                    .position(
                        x: CGFloat.random(in: 50...350),
                        y: CGFloat.random(in: 100...800)
                    )
                    .opacity(headerAnimation ? 0.8 : 0.2)
                    .animation(
                        .easeInOut(duration: Double.random(in: 2...4))
                        .repeatForever(autoreverses: true)
                        .delay(Double.random(in: 0...2)),
                        value: headerAnimation
                    )
            }
        }
    }
    
    // MARK: - 精致标题栏
    private var sophisticatedHeader: some View {
        VStack(spacing: 0) {
            // 顶部安全区域
            Rectangle()
                .fill(Color.clear)
                .frame(height: 50)
            
            // 标题区域
            HStack {
                // 返回按钮
                Button(action: {
                    lightImpact.impactOccurred()
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 3)
                        )
                }
                .scaleEffect(headerAnimation ? 1.0 : 0.7)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("回忆的海洋")
                        .font(.system(size: 28, weight: .black, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .scaleEffect(headerAnimation ? 1.0 : 0.8)
                    
                    // 诗意引言
                    Text(poeticQuotes[currentQuoteIndex])
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                        .opacity(isQuoteAnimating ? 0 : 1)
                        .animation(.easeInOut(duration: 0.5), value: isQuoteAnimating)
                }
                
                Spacer()
                
                // 快捷操作按钮
                HStack(spacing: 12) {
                    Button(action: {
                        showMemoryOfTheDay()
                    }) {
                        Image(systemName: "calendar.badge.clock")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 3)
                            )
                    }
                    
                    Button(action: {
                        showSerendipityMoment()
                    }) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 3)
                            )
                    }
                }
                .scaleEffect(headerAnimation ? 1.0 : 0.7)
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 20)
        }
        .background(
            Rectangle()
                .fill(.ultraThinMaterial)
                .opacity(1.0 - min((scrollOffset / 100), 0.95))
        )
        .opacity(headerOpacity)
    }
    
    // MARK: - 主内容视图
    private func mainContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 无形顶部空间（用于头部覆盖）
            Rectangle()
                .fill(Color.clear)
                .frame(height: 140)
            
            // 搜索栏区域
            if isSearchActive {
                PoeticSearchBar(
                    searchText: $viewModel.searchText,
                    isActive: $isSearchActive,
                    onClear: {
                        viewModel.clearSearch()
                    },
                    onCancel: {
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            isSearchActive = false
                        }
                        hideKeyboard()
                    }
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 16)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
            
            // 内容区域
            if viewModel.isLoading {
                enchantedLoadingView
            } else if viewModel.storyRecords.isEmpty {
                poeticEmptyStateView
            } else if viewModel.filteredStoryRecords.isEmpty {
                elegantNoResultsView
            } else {
                contentBasedOnViewMode
            }
        }
    }
    
    // MARK: - 内容视图选择
    private var contentBasedOnViewMode: some View {
        Group {
            switch selectedViewMode {
            case .story:
                immersiveStoryView
            case .timeline:
                poeticTimelineView
            case .favorites:
                favoritesMemoryView
            }
        }
        .opacity(contentAnimation ? 1.0 : 0.0)
        .scaleEffect(contentAnimation ? 1.0 : 0.95)
    }
    
    // MARK: - 沉浸式故事视图
    private var immersiveStoryView: some View {
        ScrollViewReader { proxy in
            ScrollView(.vertical, showsIndicators: false) {
                LazyVStack(spacing: 0) {
                    ForEach(Array(viewModel.filteredStoryRecords.enumerated()), id: \.element) { index, story in
                        NavigationLink(destination: UsageRecordDetailView(
                            usageRecord: story,
                            product: story.product ?? Product(context: viewModel.context),
                            sourceType: .memoirs
                        )) {
                            EnhancedStoryCard(story: story, index: index)
                                .id(story.id)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .simultaneousGesture(
                            TapGesture().onEnded { _ in
                                lightImpact.impactOccurred()
                            }
                        )
                        .onAppear {
                            // 添加逐个出现动画
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1)) {
                                // 动画逻辑将在EnhancedStoryCard内部实现
                            }
                        }
                    }
                    
                    // 底部缓冲
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 120)
                }
                .padding(.horizontal, 20)
            }
            .scrollTargetBehavior(.viewAligned)
            .scrollPosition(id: Binding<AnyHashable?>(
                get: { nil },
                set: { _ in }
            ))
            .background(
                GeometryReader { geo in
                    Color.clear
                        .preference(key: ScrollOffsetPreferenceKey.self, value: geo.frame(in: .named("scroll")).minY)
                }
            )
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                updateHeaderOpacity()
            }
            .coordinateSpace(name: "scroll")
            .refreshable {
                await refreshContent()
            }
        }
    }
    
    // MARK: - 诗意时间轴视图
    private var poeticTimelineView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 24) {
                ForEach(Array(viewModel.filteredStoryRecords.enumerated()), id: \.element) { index, story in
                    NavigationLink(destination: UsageRecordDetailView(
                        usageRecord: story,
                        product: story.product ?? Product(context: viewModel.context),
                        sourceType: .memoirs
                    )) {
                        TimelineMemoryCard(story: story, isLast: index == viewModel.filteredStoryRecords.count - 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .simultaneousGesture(
                        TapGesture().onEnded { _ in
                            lightImpact.impactOccurred()
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
        }
        .refreshable {
            await refreshContent()
        }
    }
    
    // MARK: - 收藏回忆视图
    private var favoritesMemoryView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            let favoritedStories = viewModel.filteredStoryRecords.filter { story in
                // 检查 notes 字段是否包含 [收藏] 标记
                if let notes = story.notes, notes.contains("[收藏]") {
                    return true
                }
                return false
            }
            
            if favoritedStories.isEmpty {
                // 无收藏内容的空状态
                VStack(spacing: 32) {
                    Spacer()
                    
                    // 诗意图标
                    ZStack {
                        // 背景光晕
                        ForEach(0..<3, id: \.self) { index in
                            Circle()
                                .fill(
                                    RadialGradient(
                                        colors: [
                                            Color.pink.opacity(0.15 - Double(index) * 0.05),
                                            Color.clear
                                        ],
                                        center: .center,
                                        startRadius: 30,
                                        endRadius: 120 + Double(index) * 20
                                    )
                                )
                                .frame(width: 200 + Double(index) * 40, height: 200 + Double(index) * 40)
                                .scaleEffect(headerAnimation ? 1.1 : 0.9)
                                .animation(
                                    .easeInOut(duration: 3.0 + Double(index))
                                    .repeatForever(autoreverses: true)
                                    .delay(Double(index) * 0.7),
                                    value: headerAnimation
                                )
                        }
                        
                        // 主图标
                        VStack(spacing: 12) {
                            Image(systemName: "heart")
                                .font(.system(size: 48, weight: .ultraLight))
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [Color.pink, Color.red],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .scaleEffect(headerAnimation ? 1.2 : 1.0)
                            
                            Image(systemName: "sparkles")
                                .font(.system(size: 32, weight: .light))
                                .foregroundColor(Color.pink.opacity(0.6))
                                .offset(y: headerAnimation ? -5 : 5)
                        }
                        .animation(
                            .easeInOut(duration: 2.5)
                            .repeatForever(autoreverses: true),
                            value: headerAnimation
                        )
                    }
                    
                    // 诗意文案
                    VStack(spacing: 16) {
                        Text("还没有珍藏的回忆")
                            .font(.system(size: 28, weight: .light, design: .rounded))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [Color.pink, Color.red],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                        
                        VStack(spacing: 8) {
                            Text("将最美好的时光标记为收藏")
                                .font(.system(size: 17, weight: .medium, design: .rounded))
                                .foregroundColor(.primary)
                            
                            Text("它们将在这里永远闪闪发光")
                                .font(.system(size: 15, weight: .regular, design: .rounded))
                                .foregroundColor(.secondary)
                        }
                    }
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                    
                    Spacer()
                }
                .padding(.horizontal, 30)
            } else {
                // 收藏内容网格布局
                LazyVGrid(
                    columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2),
                    spacing: 8
                ) {
                    ForEach(Array(favoritedStories.enumerated()), id: \.element) { index, story in
                        NavigationLink(destination: UsageRecordDetailView(
                            usageRecord: story,
                            product: story.product ?? Product(context: viewModel.context),
                            sourceType: .memoirs
                        )) {
                            FavoriteMemoryCell(story: story)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .simultaneousGesture(
                            TapGesture().onEnded { _ in
                                lightImpact.impactOccurred()
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 20)
            }
        }
        .refreshable {
            await refreshContent()
        }
    }
    
    // MARK: - 诗意空状态视图
    private var poeticEmptyStateView: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // 诗意图标
            ZStack {
                // 背景光晕
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    themeManager.currentTheme.primaryColor.opacity(0.15 - Double(index) * 0.05),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 30,
                                endRadius: 120 + Double(index) * 20
                            )
                        )
                        .frame(width: 200 + Double(index) * 40, height: 200 + Double(index) * 40)
                        .scaleEffect(headerAnimation ? 1.1 : 0.9)
                        .animation(
                            .easeInOut(duration: 3.0 + Double(index))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.7),
                            value: headerAnimation
                        )
                }
                
                // 主图标
                VStack(spacing: 12) {
                    Image(systemName: "sparkles")
                        .font(.system(size: 48, weight: .ultraLight))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(headerAnimation ? 1.2 : 1.0)
                    
                    Image(systemName: "book.pages")
                        .font(.system(size: 32, weight: .light))
                        .foregroundColor(themeManager.currentTheme.primaryColor.opacity(0.6))
                        .offset(y: headerAnimation ? -5 : 5)
                }
                .animation(
                    .easeInOut(duration: 2.5)
                    .repeatForever(autoreverses: true),
                    value: headerAnimation
                )
            }
            
            // 诗意文案
            VStack(spacing: 16) {
                Text("第一章还未开始")
                    .font(.system(size: 28, weight: .light, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                
                VStack(spacing: 8) {
                    Text("每一件物品都是一页故事")
                        .font(.system(size: 17, weight: .medium, design: .rounded))
                        .foregroundColor(.primary)
                    
                    Text("让我们一起书写属于你的回忆篇章")
                        .font(.system(size: 15, weight: .regular, design: .rounded))
                        .foregroundColor(.secondary)
                }
            }
            .multilineTextAlignment(.center)
            .padding(.horizontal, 40)
            
            // 优雅的引导按钮
            Button(action: {
                // TODO: 导航到添加故事页面
                lightImpact.impactOccurred()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle")
                        .font(.system(size: 18, weight: .medium))
                    
                    Text("开始记录")
                        .font(.system(size: 17, weight: .semibold, design: .rounded))
                }
                .foregroundColor(.white)
                .padding(.vertical, 16)
                .padding(.horizontal, 32)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(
                            LinearGradient(
                                colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: themeManager.currentTheme.primaryColor.opacity(0.4), radius: 12, x: 0, y: 6)
                )
            }
            .scaleEffect(headerAnimation ? 1.0 : 0.95)
            .buttonStyle(ScaleButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - 优雅无结果视图
    private var elegantNoResultsView: some View {
        VStack(spacing: 36) {
            Spacer()
            
            // 优雅搜索图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                themeManager.currentTheme.primaryColor.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 30,
                            endRadius: 80
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 36, weight: .ultraLight))
                    .foregroundColor(themeManager.currentTheme.primaryColor.opacity(0.8))
            }
            
            VStack(spacing: 12) {
                Text("在回忆的迷宫中未找到")
                    .font(.system(size: 24, weight: .light, design: .rounded))
                    .foregroundColor(.primary)
                
                Text("换个关键词，或许会发现意外的惊喜")
                    .font(.system(size: 16, weight: .regular, design: .rounded))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            HStack(spacing: 16) {
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        viewModel.clearSearch()
                        isSearchActive = false
                        hideKeyboard()
                    }
                    lightImpact.impactOccurred()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 14, weight: .medium))
                        Text("重新搜索")
                            .font(.system(size: 15, weight: .medium, design: .rounded))
                    }
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                    .padding(.vertical, 12)
                    .padding(.horizontal, 20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(themeManager.currentTheme.primaryColor.opacity(0.12))
                    )
                }
                .buttonStyle(ScaleButtonStyle())
                
                Button(action: {
                    showSerendipityMoment()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 14, weight: .medium))
                        Text("随机漫游")
                            .font(.system(size: 15, weight: .medium, design: .rounded))
                    }
                    .foregroundColor(.white)
                    .padding(.vertical, 12)
                    .padding(.horizontal, 20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    )
                }
                .buttonStyle(ScaleButtonStyle())
            }
            
            Spacer()
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - 魅力加载视图
    private var enchantedLoadingView: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // 神奇的加载动画
            ZStack {
                // 多层波纹效果
                ForEach(0..<4, id: \.self) { index in
                    Circle()
                        .stroke(
                            LinearGradient(
                                colors: [
                                    themeManager.currentTheme.primaryColor.opacity(0.6 - Double(index) * 0.15),
                                    themeManager.currentTheme.secondaryColor.opacity(0.4 - Double(index) * 0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                        .frame(width: 60 + Double(index) * 20, height: 60 + Double(index) * 20)
                        .scaleEffect(headerAnimation ? 1.2 + Double(index) * 0.1 : 0.8)
                        .opacity(headerAnimation ? 0.8 - Double(index) * 0.2 : 0.3)
                        .animation(
                            .easeInOut(duration: 2.0 + Double(index) * 0.5)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.3),
                            value: headerAnimation
                        )
                }
                
                // 中心旋转图标
                VStack(spacing: 8) {
                    Image(systemName: "sparkles")
                        .font(.system(size: 20, weight: .light))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [themeManager.currentTheme.primaryColor, themeManager.currentTheme.secondaryColor],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .rotationEffect(.degrees(headerAnimation ? 360 : 0))
                        .animation(
                            .linear(duration: 3)
                            .repeatForever(autoreverses: false),
                            value: headerAnimation
                        )
                    
                    Image(systemName: "book.pages")
                        .font(.system(size: 16, weight: .light))
                        .foregroundColor(themeManager.currentTheme.primaryColor.opacity(0.7))
                        .scaleEffect(headerAnimation ? 1.1 : 0.9)
                        .animation(
                            .easeInOut(duration: 1.5)
                            .repeatForever(autoreverses: true),
                            value: headerAnimation
                        )
                }
            }
            
            // 诗意加载文案
            VStack(spacing: 12) {
                Text("翻阅记忆的书页")
                    .font(.system(size: 20, weight: .light, design: .rounded))
                    .foregroundColor(.primary)
                
                Text("每一页都是珍贵的回忆...")
                    .font(.system(size: 15, weight: .regular, design: .rounded))
                    .foregroundColor(.secondary)
            }
            .opacity(headerAnimation ? 1.0 : 0.7)
            .animation(
                .easeInOut(duration: 2)
                .repeatForever(autoreverses: true),
                value: headerAnimation
            )
            
            Spacer()
        }
    }
    
    // MARK: - 浮动操作栏
    private var floatingActionBar: some View {
        HStack(spacing: 20) {
            // 视图模式切换器
            HStack(spacing: 4) {
                ForEach(ViewMode.allCases, id: \.self) { mode in
                    Button(action: {
                        switchViewMode(to: mode)
                    }) {
                        Image(systemName: mode.icon)
                            .font(.system(size: 16, weight: selectedViewMode == mode ? .semibold : .regular))
                            .foregroundColor(selectedViewMode == mode ? .white : themeManager.currentTheme.primaryColor)
                            .frame(width: 44, height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 18)
                                    .fill(selectedViewMode == mode ? themeManager.currentTheme.primaryColor : Color.clear)
                            )
                    }
                    .buttonStyle(ScaleButtonStyle())
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 22)
                    .fill(.ultraThinMaterial)
                    .shadow(color: Color.black.opacity(0.1), radius: 12, x: 0, y: 4)
            )
            
            Spacer()
            
            // 搜索和排序控件
            HStack(spacing: 12) {
                // 搜索按钮
                Button(action: {
                    toggleSearch()
                }) {
                    Image(systemName: isSearchActive ? "xmark" : "magnifyingglass")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isSearchActive ? .white : themeManager.currentTheme.primaryColor)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(isSearchActive ? AnyShapeStyle(themeManager.currentTheme.primaryColor) : AnyShapeStyle(.ultraThinMaterial))
                                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 3)
                        )
                }
                .buttonStyle(ScaleButtonStyle())
                
                // 排序按钮
                Button(action: {
                    toggleSort()
                }) {
                    Image(systemName: viewModel.isDescendingOrder ? "arrow.down.circle" : "arrow.up.circle")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 3)
                        )
                }
                .buttonStyle(ScaleButtonStyle())
            }
        }
        .padding(.horizontal, 24)
    }
    
    // MARK: - 辅助函数
    private func initializeView() {
        // 初始化动画
        withAnimation(.easeOut(duration: 1.2)) {
            headerAnimation = true
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3)) {
            contentAnimation = true
        }
        
        // 加载数据
        if !viewModel.isDataLoaded {
            Task {
                await refreshContent()
            }
        }
        
        // 加载特殊功能数据
        viewModel.loadTodayInHistoryMemories()
        
        // 启动引言轮播
        startQuoteRotation()
    }
    
    private func refreshContent() async {
        viewModel.loadAllStories()
    }
    
    private func updateHeaderOpacity() {
        let threshold: CGFloat = 100
        let newOpacity = max(0.0, min(1.0, 1.0 - (abs(scrollOffset) / threshold)))
        
        withAnimation(.easeOut(duration: 0.1)) {
            headerOpacity = newOpacity
        }
        
        parallaxOffset = scrollOffset * 0.5
    }
    
    private func switchViewMode(to mode: ViewMode) {
        guard selectedViewMode != mode else { return }
        
        mediumImpact.impactOccurred()
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            selectedViewMode = mode
            contentAnimation = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                contentAnimation = true
            }
        }
    }
    
    private func toggleSearch() {
        lightImpact.impactOccurred()
        
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            isSearchActive.toggle()
            if !isSearchActive {
                viewModel.clearSearch()
                hideKeyboard()
            }
        }
    }
    
    private func toggleSort() {
        lightImpact.impactOccurred()
        viewModel.toggleSortOrder()
    }
    
    private func showMemoryOfTheDay() {
        heavyImpact.impactOccurred()
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            showingMemoryOfTheDay = true
        }
    }
    
    private func showSerendipityMoment() {
        heavyImpact.impactOccurred()
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            showingSerendipity = true
        }
    }
    
    private func startQuoteRotation() {
        Timer.scheduledTimer(withTimeInterval: 4.0, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                isQuoteAnimating = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                currentQuoteIndex = (currentQuoteIndex + 1) % poeticQuotes.count
                
                withAnimation(.easeInOut(duration: 0.3)) {
                    isQuoteAnimating = false
                }
            }
        }
    }
    
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - 辅助结构
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.92 : 1.0)
            .animation(.easeOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 预览
#Preview {
    NavigationStack {
        MemoirsView(context: PersistenceController.preview.container.viewContext)
    }
    .environmentObject(ThemeManager())
}