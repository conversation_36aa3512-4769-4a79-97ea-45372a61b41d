import SwiftUI

// MARK: - 月度报告展示界面
struct MonthlyReportView: View {
    @StateObject private var assistant = AnalyticsAssistant()
    @State private var selectedMonth: Date = Date()
    @State private var isGenerating: Bool = false
    @State private var showingShareSheet: Bool = false
    @State private var reportPDFData: Data?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppSizes.padding) {
                    // 月份选择器
                    monthSelector
                    
                    if let report = assistant.monthlyReport {
                        // 报告内容
                        reportContent(report)
                    } else {
                        // 生成报告按钮
                        generateReportSection
                    }
                }
                .padding()
            }
            .navigationTitle("月度报告")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    if assistant.monthlyReport != nil {
                        Button(action: shareReport) {
                            Image(systemName: "square.and.arrow.up")
                        }
                    }
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if let pdfData = reportPDFData {
                    ShareSheet(items: [pdfData])
                }
            }
        }
    }
    
    // MARK: - 月份选择器
    private var monthSelector: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("选择月份")
                .font(AppFonts.headline)
                .fontWeight(.bold)
            
            DatePicker(
                "月份",
                selection: $selectedMonth,
                displayedComponents: [.date]
            )
            .datePickerStyle(CompactDatePickerStyle())
            .onChange(of: selectedMonth) { _ in
                assistant.monthlyReport = nil
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 生成报告区域
    private var generateReportSection: some View {
        VStack(spacing: AppSizes.padding) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(AppColors.primary)
            
            Text("生成月度分析报告")
                .font(AppFonts.title2)
                .fontWeight(.bold)
                .foregroundColor(AppColors.text)
            
            Text("AI将为您分析\(formatMonth(selectedMonth))的消费数据，生成专业的月度报告")
                .font(AppFonts.body)
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
            
            Button(action: generateReport) {
                HStack {
                    if isGenerating {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                    Text(isGenerating ? "生成中..." : "生成报告")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(AppColors.primary)
                .foregroundColor(.white)
                .cornerRadius(AppSizes.cornerRadius)
            }
            .disabled(isGenerating || assistant.costTracker.isOverBudget)
            
            if assistant.costTracker.isOverBudget {
                Text("预算已用完，请重置成本追踪或使用离线模式")
                    .font(AppFonts.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 报告内容
    private func reportContent(_ report: MonthlyReport) -> some View {
        VStack(spacing: AppSizes.padding) {
            // 报告标题
            reportHeader(report)
            
            // 摘要卡片
            summaryCard(report.summary)
            
            // 关键指标
            metricsGrid(report.metrics)
            
            // 洞察列表
            insightsSection(report.insights)
            
            // 建议列表
            recommendationsSection(report.recommendations)
            
            // 重新生成按钮
            Button("重新生成报告") {
                generateReport()
            }
            .foregroundColor(AppColors.primary)
            .padding()
        }
    }
    
    // MARK: - 报告标题
    private func reportHeader(_ report: MonthlyReport) -> some View {
        VStack(spacing: AppSizes.smallPadding) {
            Text(report.monthDisplayString)
                .font(AppFonts.title)
                .fontWeight(.bold)
                .foregroundColor(AppColors.text)
            
            Text("消费分析报告")
                .font(AppFonts.title3)
                .foregroundColor(AppColors.secondaryText)
            
            Text("生成时间：\(formatTimestamp(report.generatedAt))")
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.primary.opacity(0.1))
        )
    }
    
    // MARK: - 摘要卡片
    private func summaryCard(_ summary: ReportSummary) -> some View {
        VStack(spacing: AppSizes.smallPadding) {
            Text("月度摘要")
                .font(AppFonts.headline)
                .fontWeight(.bold)
            
            Text(summary.keyHighlight)
                .font(AppFonts.body)
                .foregroundColor(AppColors.text)
                .multilineTextAlignment(.center)
            
            HStack {
                VStack {
                    Text("\(summary.totalProducts)")
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("产品数量")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
                
                Spacer()
                
                VStack {
                    Text(summary.spendingDisplayString)
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("总消费")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
                
                Spacer()
                
                VStack {
                    Text(summary.worthinessDisplayString)
                        .font(AppFonts.title2)
                        .fontWeight(.bold)
                    Text("平均价值度")
                        .font(AppFonts.caption)
                        .foregroundColor(AppColors.secondaryText)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 指标网格
    private func metricsGrid(_ metrics: ReportMetrics) -> some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("关键指标")
                .font(AppFonts.headline)
                .fontWeight(.bold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppSizes.smallPadding) {
                MetricCard(title: "组合健康度", value: metrics.healthGrade, color: healthGradeColor(metrics.healthGrade))
                MetricCard(title: "满意度评分", value: String(format: "%.1f", metrics.satisfactionScore), color: .blue)
                MetricCard(title: "使用效率", value: String(format: "%.1f%%", metrics.usageEfficiency), color: .green)
                MetricCard(title: "月增长率", value: String(format: "%.1f%%", metrics.monthlyGrowthRate), color: .orange)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 洞察区域
    private func insightsSection(_ insights: [ReportInsight]) -> some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("深度洞察")
                .font(AppFonts.headline)
                .fontWeight(.bold)
            
            if insights.isEmpty {
                Text("暂无洞察数据")
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.secondaryText)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(insights) { insight in
                    InsightCard(insight: insight)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 建议区域
    private func recommendationsSection(_ recommendations: [ReportRecommendation]) -> some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("行动建议")
                .font(AppFonts.headline)
                .fontWeight(.bold)
            
            if recommendations.isEmpty {
                Text("暂无建议")
                    .font(AppFonts.body)
                    .foregroundColor(AppColors.secondaryText)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(recommendations) { recommendation in
                    RecommendationCard(recommendation: recommendation)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                .fill(AppColors.cardBackground)
                .shadow(color: AppColors.shadow, radius: AppSizes.shadowRadius, x: 0, y: 2)
        )
    }
    
    // MARK: - 辅助方法
    private func generateReport() {
        isGenerating = true
        
        Task {
            let report = await assistant.generateMonthlyReport(for: selectedMonth)
            
            await MainActor.run {
                isGenerating = false
                if report != nil {
                    // 报告生成成功
                }
            }
        }
    }
    
    private func shareReport() {
        // 生成PDF并分享
        // 实现省略
    }
    
    private func formatMonth(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: date)
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    private func healthGradeColor(_ grade: String) -> Color {
        switch grade {
        case "A+", "A": return .green
        case "B+", "B": return .blue
        case "C+", "C": return .orange
        default: return .red
        }
    }
}

// MARK: - 指标卡片
struct MetricCard: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(AppFonts.title2)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - 洞察卡片
struct InsightCard: View {
    let insight: ReportInsight

    var body: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            HStack {
                Circle()
                    .fill(insight.category.color)
                    .frame(width: 8, height: 8)

                Text(insight.title)
                    .font(AppFonts.caption)
                    .fontWeight(.medium)

                Spacer()

                Text(insight.importance.displayName)
                    .font(AppFonts.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(importanceColor(insight.importance).opacity(0.2))
                    )
                    .foregroundColor(importanceColor(insight.importance))
            }

            Text(insight.description)
                .font(AppFonts.caption2)
                .foregroundColor(AppColors.secondaryText)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(insight.category.color.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(insight.category.color.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private func importanceColor(_ importance: InsightImportance) -> Color {
        switch importance {
        case .critical: return .red
        case .high: return .orange
        case .medium: return .yellow
        case .low: return .green
        }
    }
}

// MARK: - 建议卡片
struct RecommendationCard: View {
    let recommendation: ReportRecommendation
    @State private var isExpanded: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            HStack {
                Image(systemName: recommendation.actionType.icon)
                    .foregroundColor(recommendation.actionType == .disposal ? .red : AppColors.primary)

                VStack(alignment: .leading, spacing: 2) {
                    Text(recommendation.title)
                        .font(AppFonts.caption)
                        .fontWeight(.medium)

                    Text(recommendation.timeframe.displayName)
                        .font(AppFonts.caption2)
                        .foregroundColor(AppColors.secondaryText)
                }

                Spacer()

                HStack(spacing: 4) {
                    Text(recommendation.priority.displayName)
                        .font(AppFonts.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(recommendation.priority.color.opacity(0.2))
                        )
                        .foregroundColor(recommendation.priority.color)

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 12))
                            .foregroundColor(AppColors.secondaryText)
                    }
                }
            }

            Text(recommendation.description)
                .font(AppFonts.caption2)
                .foregroundColor(AppColors.secondaryText)

            if isExpanded && !recommendation.steps.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("执行步骤：")
                        .font(AppFonts.caption2)
                        .fontWeight(.medium)

                    ForEach(Array(recommendation.steps.enumerated()), id: \.offset) { index, step in
                        HStack(alignment: .top, spacing: 6) {
                            Text("\(index + 1).")
                                .font(AppFonts.caption2)
                                .foregroundColor(AppColors.secondaryText)

                            Text(step)
                                .font(AppFonts.caption2)
                                .foregroundColor(AppColors.secondaryText)
                        }
                    }
                }
                .padding(.top, 4)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColors.background.opacity(0.5))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(recommendation.priority.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - 分享表单
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    MonthlyReportView()
}
