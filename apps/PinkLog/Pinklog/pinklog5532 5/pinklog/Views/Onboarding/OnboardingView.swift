import SwiftUI

/// 用于sheet显示的可识别图片包装器
struct IdentifiableImage: Identifiable {
    let id = UUID()
    let image: UIImage
}

/// 首次安装引导界面
struct OnboardingView: View {
    @EnvironmentObject var userProfileManager: UserProfileManager
    @State private var currentStep: OnboardingStep = .welcome
    @State private var nickname = ""
    @State private var showingCamera = false
    @State private var capturedImage: UIImage?
    @State private var originalCapturedImage: UIImage? // 保存原始拍照图片
    @State private var isProcessingAvatar = false
    @State private var showingStyleSelection = false
    @State private var showingError = false
    @State private var errorMessage = ""

    // 动画状态
    @State private var animationOffset: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var backgroundParticles: [ParticleData] = []
    @State private var showContent = false
    
    var body: some View {
        ZStack {
            // 动态背景
            DynamicBackgroundView(particles: $backgroundParticles)
                .ignoresSafeArea()

            // 主要内容
            VStack(spacing: 0) {
                // 顶部装饰区域
                TopDecorativeArea()
                    .opacity(showContent ? 1 : 0)
                    .offset(y: showContent ? 0 : -50)
                    .animation(.easeOut(duration: 0.8).delay(0.2), value: showContent)

                Spacer()

                // 进度指示器
                ModernProgressIndicator(currentStep: currentStep)
                    .padding(.horizontal, 40)
                    .opacity(showContent ? 1 : 0)
                    .scaleEffect(showContent ? 1 : 0.8)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: showContent)

                Spacer()

                // 主要内容区域
                ZStack {
                    switch currentStep {
                    case .welcome:
                        EnhancedWelcomeView(onNext: {
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                currentStep = .nickname
                            }
                        })

                    case .nickname:
                        EnhancedNicknameView(
                            nickname: $nickname,
                            onNext: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentStep = .avatar
                                }
                            },
                            onBack: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentStep = .welcome
                                }
                            }
                        )

                    case .avatar:
                        EnhancedAvatarView(
                            capturedImage: $capturedImage,
                            isProcessing: $isProcessingAvatar,
                            onTakePhoto: {
                                showingCamera = true
                            },
                            onNext: {
                                completeOnboarding()
                            },
                            onBack: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentStep = .nickname
                                }
                            }
                        )

                    case .complete:
                        EnhancedCompleteView()
                    }
                }
                .opacity(showContent ? 1 : 0)
                .scaleEffect(showContent ? 1 : 0.9)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: showContent)

                Spacer()

                // 底部装饰区域
                BottomDecorativeArea()
                    .opacity(showContent ? 1 : 0)
                    .offset(y: showContent ? 0 : 50)
                    .animation(.easeOut(duration: 0.8).delay(0.8), value: showContent)
            }
        }
        .onAppear {
            initializeAnimations()
        }
        .sheet(isPresented: $showingCamera) {
            SelfieAvatarCameraView(capturedImage: $originalCapturedImage)
                .onDisappear {
                    // 拍照完成后，originalCapturedImage会自动触发风格选择sheet
                    // 不需要额外的逻辑
                }
        }
        .sheet(item: Binding<IdentifiableImage?>(
            get: { originalCapturedImage.map(IdentifiableImage.init) },
            set: { _ in originalCapturedImage = nil }
        )) { identifiableImage in
            StyleSelectionView(
                originalImage: identifiableImage.image,
                onStyleSelected: { style in
                    originalCapturedImage = nil
                    processAvatarImage(identifiableImage.image, style: style)
                },
                onCancel: {
                    originalCapturedImage = nil
                }
            )
        }
        .alert("处理失败", isPresented: $showingError) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Private Methods
    
    private func processAvatarImage(_ image: UIImage, style: PixelArtStyle) {
        isProcessingAvatar = true
        print("开始处理头像，原始尺寸: \(image.size)，风格: \(style.rawValue)")

        // 先压缩图片到合理尺寸，避免内存问题
        let compressedImage = compressImageForAvatar(image)
        print("压缩后尺寸: \(compressedImage.size)")

        // 1. 使用SubjectLiftManager抠出主体
        SubjectLiftManager.shared.liftSubject(from: compressedImage) { result in
            switch result {
            case .success(let liftedImage):
                print("抠图成功，开始像素化处理")
                // 2. 应用指定的像素风格化
                PixelStyleProcessor.shared.createPixelAvatar(liftedImage, style: style) { pixelImage in
                    DispatchQueue.main.async {
                        isProcessingAvatar = false
                        if let finalImage = pixelImage {
                            print("像素化处理成功")
                            capturedImage = finalImage
                        } else {
                            print("像素化失败，使用抠图结果")
                            // 如果像素化失败，使用抠图结果
                            capturedImage = liftedImage
                        }
                    }
                }

            case .failure(let error):
                print("抠图失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    isProcessingAvatar = false
                    // 如果抠图失败，直接使用压缩后的原图进行像素化
                    PixelStyleProcessor.shared.createPixelAvatar(compressedImage, style: style) { pixelImage in
                        DispatchQueue.main.async {
                            if let finalImage = pixelImage {
                                print("直接像素化成功")
                                capturedImage = finalImage
                            } else {
                                print("所有处理都失败")
                                errorMessage = "头像处理失败，请重新拍照"
                                showingError = true
                            }
                        }
                    }
                }
            }
        }
    }

    /// 压缩图片到适合头像处理的尺寸
    private func compressImageForAvatar(_ image: UIImage) -> UIImage {
        let maxSize: CGFloat = 400 // 头像处理最大尺寸
        let size = image.size

        // 如果图片已经足够小，直接返回
        if max(size.width, size.height) <= maxSize {
            return image
        }

        // 计算压缩比例
        let scale = maxSize / max(size.width, size.height)
        let newSize = CGSize(width: size.width * scale, height: size.height * scale)

        // 使用高质量压缩
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    // MARK: - 动画初始化
    private func initializeAnimations() {
        // 初始化背景粒子
        backgroundParticles = (0..<20).map { _ in
            ParticleData(
                x: Double.random(in: 0...UIScreen.main.bounds.width),
                y: Double.random(in: 0...UIScreen.main.bounds.height),
                size: Double.random(in: 2...8),
                opacity: Double.random(in: 0.1...0.3),
                speed: Double.random(in: 0.5...2.0)
            )
        }

        // 启动内容显示动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            showContent = true
        }

        // 启动持续动画
        startContinuousAnimations()
    }

    private func startContinuousAnimations() {
        // 脉冲动画
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            pulseScale = 1.1
        }

        // 浮动动画
        withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
            animationOffset = 10
        }
    }

    private func completeOnboarding() {
        // 保存用户信息
        userProfileManager.updateNickname(nickname)
        if let avatar = capturedImage {
            userProfileManager.updateAvatar(avatar)
        }
        
        // 完成引导
        withAnimation(.easeInOut(duration: 0.5)) {
            currentStep = .complete
        }
        
        // 延迟后完成引导流程
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            userProfileManager.completeOnboarding()
        }
    }
}

// MARK: - Onboarding Steps
enum OnboardingStep: Int, CaseIterable {
    case welcome = 0
    case nickname = 1
    case avatar = 2
    case complete = 3
    
    var title: String {
        switch self {
        case .welcome: return "欢迎"
        case .nickname: return "昵称"
        case .avatar: return "头像"
        case .complete: return "完成"
        }
    }
}

// MARK: - Progress View
struct OnboardingProgressView: View {
    let currentStep: OnboardingStep
    
    var body: some View {
        HStack(spacing: 20) {
            ForEach(OnboardingStep.allCases, id: \.self) { step in
                VStack(spacing: 8) {
                    Circle()
                        .fill(step.rawValue <= currentStep.rawValue ? Color.pink : Color.gray.opacity(0.3))
                        .frame(width: 12, height: 12)
                    
                    Text(step.title)
                        .font(.caption)
                        .foregroundColor(step.rawValue <= currentStep.rawValue ? .pink : .gray)
                }
                
                if step != OnboardingStep.allCases.last {
                    Rectangle()
                        .fill(step.rawValue < currentStep.rawValue ? Color.pink : Color.gray.opacity(0.3))
                        .frame(height: 2)
                        .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.horizontal, 40)
    }
}

// MARK: - Welcome Step
struct WelcomeStepView: View {
    let onNext: () -> Void
    
    var body: some View {
        VStack(spacing: 30) {
            // Pinkbot头像
            Image(uiImage: PinkbotAvatarGenerator.shared.generatePinkbotAvatar(size: CGSize(width: 120, height: 120)))
                .resizable()
                .frame(width: 120, height: 120)
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: UUID())
            
            VStack(spacing: 16) {
                Text("你好！我是 Pinkbot")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.pink)
                
                Text("我是你的智能助手，专门帮助你管理和分析产品使用情况")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
                
                Text("让我们先设置一下你的个人信息吧！")
                    .font(.callout)
                    .foregroundColor(.pink)
                    .padding(.top, 10)
            }
            
            Button(action: onNext) {
                HStack {
                    Text("开始设置")
                        .fontWeight(.semibold)
                    Image(systemName: "arrow.right")
                }
                .foregroundColor(.white)
                .padding(.horizontal, 40)
                .padding(.vertical, 16)
                .background(Color.pink)
                .cornerRadius(25)
            }
            .padding(.top, 20)
        }
        .padding(.horizontal, 30)
    }
}

// MARK: - 支持组件

/// 粒子数据结构
struct ParticleData: Identifiable {
    let id = UUID()
    var x: Double
    var y: Double
    var size: Double
    var opacity: Double
    var speed: Double
}

/// 动态背景视图
struct DynamicBackgroundView: View {
    @Binding var particles: [ParticleData]
    @State private var animationTimer: Timer?

    var body: some View {
        ZStack {
            // 渐变背景
            RadialGradient(
                colors: [
                    Color.pink.opacity(0.15),
                    Color.purple.opacity(0.1),
                    Color.blue.opacity(0.05),
                    Color.clear
                ],
                center: .topLeading,
                startRadius: 100,
                endRadius: 800
            )

            // 动态粒子
            ForEach(particles) { particle in
                Circle()
                    .fill(Color.white.opacity(particle.opacity))
                    .frame(width: particle.size, height: particle.size)
                    .position(x: particle.x, y: particle.y)
                    .blur(radius: 1)
            }
        }
        .onAppear {
            startParticleAnimation()
        }
        .onDisappear {
            animationTimer?.invalidate()
        }
    }

    private func startParticleAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            withAnimation(.linear(duration: 0.1)) {
                for i in particles.indices {
                    particles[i].y -= particles[i].speed
                    if particles[i].y < -10 {
                        particles[i].y = UIScreen.main.bounds.height + 10
                        particles[i].x = Double.random(in: 0...UIScreen.main.bounds.width)
                    }
                }
            }
        }
    }
}

/// 顶部装饰区域
struct TopDecorativeArea: View {
    var body: some View {
        VStack(spacing: 0) {
            // 装饰性图标
            HStack {
                Spacer()
                Image(systemName: "sparkles")
                    .font(.title2)
                    .foregroundColor(.pink.opacity(0.6))
                Spacer()
                Image(systemName: "heart.fill")
                    .font(.title3)
                    .foregroundColor(.purple.opacity(0.5))
                Spacer()
                Image(systemName: "star.fill")
                    .font(.title2)
                    .foregroundColor(.blue.opacity(0.6))
                Spacer()
            }
            .padding(.top, 20)
        }
    }
}

/// 现代化进度指示器
struct ModernProgressIndicator: View {
    let currentStep: OnboardingStep

    private var progress: Double {
        switch currentStep {
        case .welcome: return 0.25
        case .nickname: return 0.5
        case .avatar: return 0.75
        case .complete: return 1.0
        }
    }

    var body: some View {
        VStack(spacing: 12) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 8)

                    // 进度
                    RoundedRectangle(cornerRadius: 4)
                        .fill(
                            LinearGradient(
                                colors: [Color.pink, Color.purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * progress, height: 8)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8), value: progress)
                }
            }
            .frame(height: 8)

            // 步骤指示器
            HStack {
                ForEach(OnboardingStep.allCases, id: \.self) { step in
                    Circle()
                        .fill(step.rawValue <= currentStep.rawValue ? Color.pink : Color.gray.opacity(0.3))
                        .frame(width: 12, height: 12)
                        .scaleEffect(step == currentStep ? 1.2 : 1.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentStep)

                    if step != OnboardingStep.allCases.last {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                    }
                }
            }
        }
    }
}

/// 增强的欢迎视图
struct EnhancedWelcomeView: View {
    let onNext: () -> Void
    @State private var titleScale: CGFloat = 0.8
    @State private var subtitleOpacity: Double = 0
    @State private var buttonScale: CGFloat = 0.8

    var body: some View {
        VStack(spacing: 40) {
            VStack(spacing: 20) {
                // 主标题
                Text("欢迎来到 PinkLog")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.pink, Color.purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .scaleEffect(titleScale)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: titleScale)

                // 副标题
                Text("记录生活的美好瞬间\n让每一天都充满意义")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .opacity(subtitleOpacity)
                    .animation(.easeOut(duration: 0.8).delay(0.6), value: subtitleOpacity)
            }

            // 装饰性图标
            HStack(spacing: 30) {
                ForEach(["heart.fill", "star.fill", "sparkles"], id: \.self) { iconName in
                    Image(systemName: iconName)
                        .font(.title)
                        .foregroundColor(.pink.opacity(0.7))
                        .scaleEffect(titleScale)
                        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.4), value: titleScale)
                }
            }

            // 开始按钮
            Button(action: onNext) {
                HStack(spacing: 12) {
                    Text("开始体验")
                        .font(.system(size: 18, weight: .semibold))
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.title2)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 40)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: [Color.pink, Color.purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
                .shadow(color: .pink.opacity(0.3), radius: 10, x: 0, y: 5)
            }
            .scaleEffect(buttonScale)
            .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.8), value: buttonScale)
        }
        .padding(.horizontal, 40)
        .onAppear {
            titleScale = 1.0
            subtitleOpacity = 1.0
            buttonScale = 1.0
        }
    }
}

/// 增强的昵称输入视图
struct EnhancedNicknameView: View {
    @Binding var nickname: String
    let onNext: () -> Void
    let onBack: () -> Void

    @State private var isTextFieldFocused = false
    @State private var showValidation = false
    @FocusState private var textFieldFocused: Bool

    var isValidNickname: Bool {
        !nickname.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        nickname.count >= 2 && nickname.count <= 20
    }

    var body: some View {
        VStack(spacing: 40) {
            VStack(spacing: 20) {
                // 标题
                Text("设置你的昵称")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Text("这将是其他人看到的名字")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // 输入框
            VStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    isTextFieldFocused ? Color.pink : Color.clear,
                                    lineWidth: 2
                                )
                        )
                        .frame(height: 56)

                    TextField("输入昵称", text: $nickname)
                        .font(.system(size: 18, weight: .medium))
                        .padding(.horizontal, 20)
                        .focused($textFieldFocused)
                        .onChange(of: textFieldFocused) { focused in
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isTextFieldFocused = focused
                            }
                        }
                        .onSubmit {
                            if isValidNickname {
                                onNext()
                            } else {
                                showValidation = true
                            }
                        }
                }

                // 验证提示
                if showValidation && !isValidNickname {
                    Text("昵称长度应在2-20个字符之间")
                        .font(.caption)
                        .foregroundColor(.red)
                        .transition(.opacity)
                }
            }

            // 按钮组
            VStack(spacing: 16) {
                // 下一步按钮
                Button(action: {
                    if isValidNickname {
                        onNext()
                    } else {
                        showValidation = true
                    }
                }) {
                    HStack(spacing: 12) {
                        Text("下一步")
                            .font(.system(size: 18, weight: .semibold))
                        Image(systemName: "arrow.right")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 40)
                    .padding(.vertical, 16)
                    .background(
                        isValidNickname ?
                        LinearGradient(colors: [Color.pink, Color.purple], startPoint: .leading, endPoint: .trailing) :
                        LinearGradient(colors: [Color.gray, Color.gray], startPoint: .leading, endPoint: .trailing)
                    )
                    .cornerRadius(25)
                    .shadow(color: isValidNickname ? .pink.opacity(0.3) : .clear, radius: 10, x: 0, y: 5)
                }
                .disabled(!isValidNickname)
                .animation(.easeInOut(duration: 0.2), value: isValidNickname)

                // 返回按钮
                Button(action: onBack) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.left")
                        Text("返回")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 40)
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                textFieldFocused = true
            }
        }
    }
}

/// 增强的头像设置视图
struct EnhancedAvatarView: View {
    @Binding var capturedImage: UIImage?
    @Binding var isProcessing: Bool
    let onTakePhoto: () -> Void
    let onNext: () -> Void
    let onBack: () -> Void

    @State private var pulseScale: CGFloat = 1.0
    @State private var rotationAngle: Double = 0

    var body: some View {
        VStack(spacing: 40) {
            VStack(spacing: 20) {
                // 标题
                Text("设置你的头像")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Text("拍一张照片，我们会为你创造独特的艺术头像")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // 头像区域
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.pink.opacity(0.1), Color.purple.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 160, height: 160)
                    .scaleEffect(pulseScale)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseScale)

                if let image = capturedImage {
                    // 显示拍摄的头像
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 140, height: 140)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.pink, lineWidth: 3)
                        )
                        .shadow(color: .pink.opacity(0.3), radius: 10, x: 0, y: 5)
                } else {
                    // 拍照按钮
                    Button(action: onTakePhoto) {
                        ZStack {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 140, height: 140)
                                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)

                            VStack(spacing: 12) {
                                Image(systemName: "camera.fill")
                                    .font(.system(size: 32))
                                    .foregroundColor(.pink)
                                    .rotationEffect(.degrees(rotationAngle))
                                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: rotationAngle)

                                Text("拍照")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.pink)
                            }
                        }
                    }
                    .disabled(isProcessing)
                }

                // 处理中指示器
                if isProcessing {
                    ZStack {
                        Circle()
                            .fill(Color.black.opacity(0.6))
                            .frame(width: 140, height: 140)

                        VStack(spacing: 12) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.2)

                            Text("处理中...")
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                    }
                }
            }
            .onAppear {
                pulseScale = 1.05
                rotationAngle = 5
            }

            // 按钮组
            VStack(spacing: 16) {
                if capturedImage != nil {
                    // 完成按钮
                    Button(action: onNext) {
                        HStack(spacing: 12) {
                            Text("完成设置")
                                .font(.system(size: 18, weight: .semibold))
                            Image(systemName: "checkmark.circle.fill")
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: [Color.green, Color.blue],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(25)
                        .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
                    }
                    .disabled(isProcessing)

                    // 重新拍照按钮
                    Button(action: onTakePhoto) {
                        HStack(spacing: 8) {
                            Image(systemName: "camera.rotate")
                            Text("重新拍照")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.pink)
                    }
                    .disabled(isProcessing)
                }

                // 返回按钮
                Button(action: onBack) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.left")
                        Text("返回")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                }
                .disabled(isProcessing)
            }
        }
        .padding(.horizontal, 40)
    }
}

/// 增强的完成视图
struct EnhancedCompleteView: View {
    @State private var celebrationScale: CGFloat = 0.5
    @State private var showConfetti = false

    var body: some View {
        VStack(spacing: 40) {
            // 庆祝动画
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.green.opacity(0.2), Color.blue.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 200, height: 200)
                    .scaleEffect(celebrationScale)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: celebrationScale)

                // 成功图标
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .scaleEffect(celebrationScale)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.4), value: celebrationScale)
            }

            VStack(spacing: 20) {
                Text("设置完成！")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.green, Color.blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .scaleEffect(celebrationScale)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.6), value: celebrationScale)

                Text("欢迎来到 PinkLog 大家庭\n开始记录你的美好生活吧！")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .opacity(celebrationScale > 0.8 ? 1 : 0)
                    .animation(.easeOut(duration: 0.8).delay(0.8), value: celebrationScale)
            }
        }
        .padding(.horizontal, 40)
        .onAppear {
            celebrationScale = 1.0

            // 延迟显示彩带效果
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                showConfetti = true
            }
        }
    }
}

/// 底部装饰区域
struct BottomDecorativeArea: View {
    var body: some View {
        VStack(spacing: 0) {
            // 装饰性波浪
            HStack(spacing: 20) {
                ForEach(0..<5, id: \.self) { _ in
                    Circle()
                        .fill(Color.pink.opacity(0.1))
                        .frame(width: 8, height: 8)
                }
            }
            .padding(.bottom, 30)
        }
    }
}

#Preview {
    OnboardingView()
        .environmentObject(UserProfileManager.shared)
}
