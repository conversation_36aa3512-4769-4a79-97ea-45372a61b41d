import SwiftUI
import AVFoundation
import UIKit

/// 自拍头像相机界面
struct SelfieAvatarCameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> SelfieAvatarCameraViewController {
        let controller = SelfieAvatarCameraViewController()
        controller.delegate = context.coordinator
        return controller
    }
    
    func updateUIViewController(_ uiViewController: SelfieAvatarCameraViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, SelfieAvatarCameraDelegate {
        let parent: SelfieAvatarCameraView
        
        init(_ parent: SelfieAvatarCameraView) {
            self.parent = parent
        }
        
        func didCaptureImage(_ image: UIImage) {
            parent.capturedImage = image
            parent.dismiss()
        }
        
        func didCancel() {
            parent.dismiss()
        }
    }
}

// MARK: - Camera Delegate Protocol
protocol SelfieAvatarCameraDelegate: AnyObject {
    func didCaptureImage(_ image: UIImage)
    func didCancel()
}

// MARK: - Camera View Controller
class SelfieAvatarCameraViewController: UIViewController {
    weak var delegate: SelfieAvatarCameraDelegate?
    
    private var captureSession: AVCaptureSession!
    private var previewLayer: AVCaptureVideoPreviewLayer!
    private var photoOutput: AVCapturePhotoOutput!
    private var frontCamera: AVCaptureDevice?
    
    private let captureButton = UIButton()
    private let cancelButton = UIButton()
    private let guideOverlay = UIView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
        setupUI()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startCamera()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        stopCamera()
    }
    
    // MARK: - Camera Setup
    private func setupCamera() {
        captureSession = AVCaptureSession()
        captureSession.sessionPreset = .photo
        
        // 设置前置摄像头
        guard let frontCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front) else {
            print("无法访问前置摄像头")
            return
        }
        
        self.frontCamera = frontCamera
        
        do {
            let input = try AVCaptureDeviceInput(device: frontCamera)
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
            }
        } catch {
            print("无法创建摄像头输入: \(error)")
            return
        }
        
        // 设置照片输出
        photoOutput = AVCapturePhotoOutput()
        if captureSession.canAddOutput(photoOutput) {
            captureSession.addOutput(photoOutput)
        }
        
        // 设置预览层
        previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.videoGravity = .resizeAspectFill
        view.layer.addSublayer(previewLayer)
    }
    
    private func setupUI() {
        view.backgroundColor = .black
        
        // 设置引导遮罩
        setupGuideOverlay()
        
        // 设置拍照按钮
        setupCaptureButton()
        
        // 设置取消按钮
        setupCancelButton()
    }
    
    private func setupGuideOverlay() {
        guideOverlay.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        view.addSubview(guideOverlay)
        
        // 创建圆形透明区域
        let circleSize: CGFloat = 250
        let circleLayer = CAShapeLayer()
        let circlePath = UIBezierPath(rect: CGRect(x: 0, y: 0, width: view.bounds.width, height: view.bounds.height))
        let circleHole = UIBezierPath(ovalIn: CGRect(
            x: (view.bounds.width - circleSize) / 2,
            y: (view.bounds.height - circleSize) / 2 - 50,
            width: circleSize,
            height: circleSize
        ))
        circlePath.append(circleHole.reversing())
        
        circleLayer.path = circlePath.cgPath
        circleLayer.fillRule = .evenOdd
        guideOverlay.layer.mask = circleLayer
        
        // 添加提示文字
        let instructionLabel = UILabel()
        instructionLabel.text = "请将脸部对准圆圈内"
        instructionLabel.textColor = .white
        instructionLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        instructionLabel.textAlignment = .center
        instructionLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(instructionLabel)
        
        NSLayoutConstraint.activate([
            instructionLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            instructionLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 50)
        ])
    }
    
    private func setupCaptureButton() {
        captureButton.backgroundColor = .systemPink
        captureButton.layer.cornerRadius = 35
        captureButton.setTitle("拍照", for: .normal)
        captureButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        captureButton.addTarget(self, action: #selector(capturePhoto), for: .touchUpInside)
        captureButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(captureButton)
        
        NSLayoutConstraint.activate([
            captureButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            captureButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            captureButton.widthAnchor.constraint(equalToConstant: 70),
            captureButton.heightAnchor.constraint(equalToConstant: 70)
        ])
    }
    
    private func setupCancelButton() {
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.setTitleColor(.white, for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.addTarget(self, action: #selector(cancelCapture), for: .touchUpInside)
        cancelButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(cancelButton)
        
        NSLayoutConstraint.activate([
            cancelButton.leadingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leadingAnchor, constant: 20),
            cancelButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20)
        ])
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        previewLayer?.frame = view.bounds
        
        // 更新遮罩层
        if let maskLayer = guideOverlay.layer.mask as? CAShapeLayer {
            let circleSize: CGFloat = 250
            let circlePath = UIBezierPath(rect: view.bounds)
            let circleHole = UIBezierPath(ovalIn: CGRect(
                x: (view.bounds.width - circleSize) / 2,
                y: (view.bounds.height - circleSize) / 2 - 50,
                width: circleSize,
                height: circleSize
            ))
            circlePath.append(circleHole.reversing())
            maskLayer.path = circlePath.cgPath
        }
    }
    
    // MARK: - Camera Control
    private func startCamera() {
        DispatchQueue.global(qos: .userInitiated).async {
            self.captureSession.startRunning()
        }
    }
    
    private func stopCamera() {
        DispatchQueue.global(qos: .userInitiated).async {
            self.captureSession.stopRunning()
        }
    }
    
    // MARK: - Actions
    @objc private func capturePhoto() {
        let settings = AVCapturePhotoSettings()
        settings.flashMode = .auto

        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    @objc private func cancelCapture() {
        delegate?.didCancel()
    }
}

// MARK: - Photo Capture Delegate
extension SelfieAvatarCameraViewController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            print("拍照失败: \(error)")
            return
        }
        
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            print("无法处理照片数据")
            return
        }
        
        // 由于是前置摄像头，需要水平翻转图片
        let flippedImage = image.withHorizontallyFlippedOrientation()
        
        delegate?.didCaptureImage(flippedImage)
    }
}

// MARK: - UIImage Extension
extension UIImage {
    func withHorizontallyFlippedOrientation() -> UIImage {
        guard let cgImage = self.cgImage else { return self }
        return UIImage(cgImage: cgImage, scale: scale, orientation: .leftMirrored)
    }
}
