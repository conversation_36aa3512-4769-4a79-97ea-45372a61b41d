import SwiftUI

// MARK: - Nickname Step View
struct NicknameStepView: View {
    @Binding var nickname: String
    let onNext: () -> Void
    let onBack: () -> Void
    
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(spacing: 30) {
            VStack(spacing: 16) {
                Text("设置你的昵称")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.pink)
                
                Text("这个昵称将在与Pinkbot对话时显示")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
            }
            
            // 昵称输入框
            VStack(spacing: 12) {
                TextField("请输入你的昵称", text: $nickname)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(.title2)
                    .multilineTextAlignment(.center)
                    .focused($isTextFieldFocused)
                    .submitLabel(.done)
                    .onSubmit {
                        if canProceed {
                            onNext()
                        }
                    }
                
                Text("2-10个字符，可以是中文、英文或数字")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 40)
            
            // 按钮组
            VStack(spacing: 16) {
                Button(action: onNext) {
                    HStack {
                        Text("下一步")
                            .fontWeight(.semibold)
                        Image(systemName: "arrow.right")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 40)
                    .padding(.vertical, 16)
                    .background(canProceed ? Color.pink : Color.gray)
                    .cornerRadius(25)
                }
                .disabled(!canProceed)
                
                Button(action: onBack) {
                    Text("返回")
                        .foregroundColor(.pink)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                }
            }
            .padding(.top, 20)
        }
        .padding(.horizontal, 30)
        .onAppear {
            // 延迟聚焦，避免动画冲突
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isTextFieldFocused = true
            }
        }
    }
    
    private var canProceed: Bool {
        let trimmed = nickname.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmed.count >= 2 && trimmed.count <= 10
    }
}

// MARK: - Avatar Step View
struct AvatarStepView: View {
    @Binding var capturedImage: UIImage?
    @Binding var isProcessing: Bool
    let onTakePhoto: () -> Void
    let onNext: () -> Void
    let onBack: () -> Void
    
    var body: some View {
        VStack(spacing: 30) {
            VStack(spacing: 16) {
                Text("设置你的头像")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.pink)
                
                Text("拍一张自拍照，我会为你制作专属的像素风格头像")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
            }
            
            // 头像预览区域
            VStack(spacing: 20) {
                ZStack {
                    Circle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 150, height: 150)
                        .overlay(
                            Circle()
                                .stroke(Color.pink.opacity(0.3), lineWidth: 2)
                        )
                    
                    if isProcessing {
                        VStack(spacing: 12) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.pink)
                            Text("处理中...")
                                .font(.caption)
                                .foregroundColor(.pink)
                        }
                    } else if let image = capturedImage {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 150, height: 150)
                            .clipShape(Circle())
                    } else {
                        VStack(spacing: 8) {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.pink.opacity(0.6))
                            Text("点击拍照")
                                .font(.caption)
                                .foregroundColor(.pink)
                        }
                    }
                }
                .onTapGesture {
                    if !isProcessing {
                        onTakePhoto()
                    }
                }
                
                if capturedImage != nil && !isProcessing {
                    Button("重新拍照") {
                        onTakePhoto()
                    }
                    .foregroundColor(.pink)
                    .font(.callout)
                }
            }
            
            // 按钮组
            VStack(spacing: 16) {
                Button(action: onNext) {
                    HStack {
                        Text("完成设置")
                            .fontWeight(.semibold)
                        Image(systemName: "checkmark")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 40)
                    .padding(.vertical, 16)
                    .background(canProceed ? Color.pink : Color.gray)
                    .cornerRadius(25)
                }
                .disabled(!canProceed)
                
                Button(action: onBack) {
                    Text("返回")
                        .foregroundColor(.pink)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                }
            }
            .padding(.top, 20)
        }
        .padding(.horizontal, 30)
    }
    
    private var canProceed: Bool {
        return capturedImage != nil && !isProcessing
    }
}

// MARK: - Complete Step View
struct CompleteStepView: View {
    var body: some View {
        VStack(spacing: 30) {
            // 成功动画
            ZStack {
                Circle()
                    .fill(Color.pink.opacity(0.1))
                    .frame(width: 120, height: 120)
                
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.pink)
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: UUID())
            }
            
            VStack(spacing: 16) {
                Text("设置完成！")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.pink)
                
                Text("欢迎使用PinkLog！")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("现在你可以开始记录和管理你的产品了")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
            }
            
            // Pinkbot欢迎消息
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    Image(uiImage: PinkbotAvatarGenerator.shared.generatePinkbotAvatar(size: CGSize(width: 40, height: 40)))
                        .resizable()
                        .frame(width: 40, height: 40)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Pinkbot")
                            .font(.caption)
                            .foregroundColor(.pink)
                        Text("很高兴认识你！我会帮助你更好地管理产品使用情况。")
                            .font(.callout)
                            .foregroundColor(.primary)
                    }
                    
                    Spacer()
                }
                .padding()
                .background(Color.pink.opacity(0.1))
                .cornerRadius(16)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
        .padding(.horizontal, 30)
    }
}

#Preview("Nickname Step") {
    NicknameStepView(
        nickname: .constant(""),
        onNext: {},
        onBack: {}
    )
}

#Preview("Avatar Step") {
    AvatarStepView(
        capturedImage: .constant(nil),
        isProcessing: .constant(false),
        onTakePhoto: {},
        onNext: {},
        onBack: {}
    )
}

#Preview("Complete Step") {
    CompleteStepView()
}
