import SwiftUI
import Network

struct BackupSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var backupManager = BackupManager.shared
    
    @State private var showingBackupProgress = false
    @State private var showingCrossDeviceBackup = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var showingSuccessAlert = false
    @State private var successMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 备份状态卡片
                    backupStatusCard
                    
                    // 备份配置卡片
                    backupConfigurationCard
                    
                    // 手动操作卡片
                    manualOperationsCard
                    
                    // CloudKit状态卡片
                    cloudKitStatusCard
                }
                .padding()
            }
            .navigationTitle("数据备份")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
            .sheet(isPresented: $showingBackupProgress) {
                BackupProgressView()
                    .environmentObject(backupManager)
                    .environmentObject(themeManager)
            }
            .sheet(isPresented: $showingCrossDeviceBackup) {
                CrossDeviceBackupView()
                    .environmentObject(themeManager)
            }
            .alert("备份错误", isPresented: $showingErrorAlert) {
                Button("确定") { }
            } message: {
                Text(errorMessage)
            }
            .alert("备份成功", isPresented: $showingSuccessAlert) {
                Button("确定") { }
            } message: {
                Text(successMessage)
            }
        }
        .onReceive(backupManager.$lastError) { error in
            if let error = error {
                errorMessage = error.localizedDescription
                showingErrorAlert = true
            }
        }
    }
    
    // MARK: - 备份状态卡片
    
    private var backupStatusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "icloud.and.arrow.up")
                    .font(.title2)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                
                Text("备份状态")
                    .font(.headline)
                
                Spacer()
                
                // 状态指示器
                Circle()
                    .fill(backupStatusColor)
                    .frame(width: 12, height: 12)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                let status = backupManager.getBackupStatus()
                
                Text(status.statusDescription)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                if let nextBackup = status.nextScheduledBackup {
                    Text("下次备份: \(nextBackup.formatted(date: .abbreviated, time: .shortened))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("可用版本: \(status.availableVersions)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("总大小: \(status.formattedTotalSize)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 进度条（如果正在备份）
            if backupManager.isBackupInProgress || backupManager.isRestoreInProgress {
                VStack(alignment: .leading, spacing: 4) {
                    Text(backupManager.currentOperation)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ProgressView(value: backupManager.operationProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: themeManager.currentTheme.primaryColor))
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 备份配置卡片
    
    private var backupConfigurationCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("备份设置")
                .font(.headline)
            
            VStack(spacing: 12) {
                // 自动备份开关
                HStack {
                    Text("自动备份")
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Toggle("", isOn: Binding(
                        get: { backupManager.configuration.autoBackupEnabled },
                        set: { newValue in
                            var config = backupManager.configuration
                            config = BackupConfiguration(
                                autoBackupEnabled: newValue,
                                backupFrequency: config.backupFrequency,
                                wifiOnlyBackup: config.wifiOnlyBackup,
                                lowPowerModeBackup: config.lowPowerModeBackup,
                                maxBackupVersions: config.maxBackupVersions,
                                compressionEnabled: config.compressionEnabled
                            )
                            backupManager.updateConfiguration(config)
                        }
                    ))
                    .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))
                }
                
                if backupManager.configuration.autoBackupEnabled {
                    Divider()
                    
                    // 备份频率
                    HStack {
                        Text("备份频率")
                            .font(.subheadline)
                        
                        Spacer()
                        
                        Picker("备份频率", selection: Binding(
                            get: { backupManager.configuration.backupFrequency },
                            set: { newValue in
                                var config = backupManager.configuration
                                config = BackupConfiguration(
                                    autoBackupEnabled: config.autoBackupEnabled,
                                    backupFrequency: newValue,
                                    wifiOnlyBackup: config.wifiOnlyBackup,
                                    lowPowerModeBackup: config.lowPowerModeBackup,
                                    maxBackupVersions: config.maxBackupVersions,
                                    compressionEnabled: config.compressionEnabled
                                )
                                backupManager.updateConfiguration(config)
                            }
                        )) {
                            ForEach(BackupConfiguration.BackupFrequency.allCases, id: \.self) { frequency in
                                Text(frequency.displayName).tag(frequency)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                    
                    Divider()
                    
                    // 仅WiFi备份
                    HStack {
                        Text("仅WiFi备份")
                            .font(.subheadline)
                        
                        Spacer()
                        
                        Toggle("", isOn: Binding(
                            get: { backupManager.configuration.wifiOnlyBackup },
                            set: { newValue in
                                var config = backupManager.configuration
                                config = BackupConfiguration(
                                    autoBackupEnabled: config.autoBackupEnabled,
                                    backupFrequency: config.backupFrequency,
                                    wifiOnlyBackup: newValue,
                                    lowPowerModeBackup: config.lowPowerModeBackup,
                                    maxBackupVersions: config.maxBackupVersions,
                                    compressionEnabled: config.compressionEnabled
                                )
                                backupManager.updateConfiguration(config)
                            }
                        ))
                        .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.primaryColor))
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 手动操作卡片
    
    private var manualOperationsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("手动操作")
                .font(.headline)
            
            VStack(spacing: 8) {
                // 跨设备备份按钮
                Button(action: {
                    showingCrossDeviceBackup = true
                }) {
                    HStack {
                        Image(systemName: "arrow.triangle.2.circlepath.icloud")
                            .font(.title3)
                            .foregroundColor(.blue)
                        
                        Text("跨设备备份")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                .disabled(backupManager.isBackupInProgress || backupManager.isRestoreInProgress)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - CloudKit状态卡片

    private var cloudKitStatusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "icloud")
                    .font(.title2)
                    .foregroundColor(themeManager.currentTheme.primaryColor)

                Text("iCloud状态")
                    .font(.headline)

                Spacer()

                Circle()
                    .fill(CloudKitBackupService.shared.isAvailable ? .green : .red)
                    .frame(width: 12, height: 12)
            }

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("账户状态:")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(cloudKitAccountStatusText)
                        .font(.caption)
                        .foregroundColor(.primary)
                }

                HStack {
                    Text("网络连接:")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(NetworkMonitor.shared.isConnected ? "已连接" : "未连接")
                        .font(.caption)
                        .foregroundColor(.primary)
                }

                if let connectionType = NetworkMonitor.shared.connectionType {
                    HStack {
                        Text("连接类型:")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(connectionTypeText(connectionType))
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 计算属性

    private var backupStatusColor: Color {
        if backupManager.isBackupInProgress || backupManager.isRestoreInProgress {
            return .orange
        } else if CloudKitBackupService.shared.isAvailable {
            return .green
        } else {
            return .red
        }
    }

    private var cloudKitAccountStatusText: String {
        switch CloudKitBackupService.shared.accountStatus {
        case .available:
            return "可用"
        case .noAccount:
            return "未登录"
        case .restricted:
            return "受限"
        case .couldNotDetermine:
            return "未知"
        case .temporarilyUnavailable:
            return "暂时不可用"
        @unknown default:
            return "未知"
        }
    }

    private func connectionTypeText(_ type: NWInterface.InterfaceType) -> String {
        switch type {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "蜂窝网络"
        case .wiredEthernet:
            return "有线网络"
        case .loopback:
            return "本地回环"
        case .other:
            return "其他"
        @unknown default:
            return "未知"
        }
    }
}

#Preview {
    BackupSettingsView()
        .environmentObject(ThemeManager())
}
