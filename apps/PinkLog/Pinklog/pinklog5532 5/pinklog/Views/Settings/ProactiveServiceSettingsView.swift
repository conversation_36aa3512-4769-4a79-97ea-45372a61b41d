import SwiftUI
import UserNotifications

// MARK: - 主动服务设置界面
struct ProactiveServiceSettingsView: View {
    @StateObject private var proactiveService = ProactiveService(
        aiDataContextBuilder: AIDataContextBuilder(
            productRepository: ProductRepository(context: PersistenceController.shared.container.viewContext),
            usageRepository: UsageRecordRepository(context: PersistenceController.shared.container.viewContext),
            expenseRepository: RelatedExpenseRepository(context: PersistenceController.shared.container.viewContext),
            overviewEngine: AdvancedOverviewEngine.shared
        ),
        analyticsAssistant: AnalyticsAssistant()
    )
    
    @StateObject private var reminderManager = ProactiveReminderManager.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var notificationPermissionStatus: UNAuthorizationStatus = .notDetermined
    @State private var showingPermissionAlert = false
    @State private var isAnalyzing = false
    
    var body: some View {
        NavigationView {
            Form {
                // 服务状态
                serviceStatusSection
                
                // 通知权限
                notificationPermissionSection
                
                // 基础设置
                basicSettingsSection
                
                // 消息类型设置
                messageTypesSection
                
                // 分析频率设置
                analysisFrequencySection
                
                // 高级设置
                advancedSettingsSection
                
                // 操作按钮
                actionsSection
            }
            .navigationTitle("主动提醒设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            checkNotificationPermission()
        }
    }
    
    // MARK: - 服务状态部分
    private var serviceStatusSection: some View {
        Section("服务状态") {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: proactiveService.config.isEnabled ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(proactiveService.config.isEnabled ? .green : .red)
                        
                        Text("主动分析服务")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Text(proactiveService.config.isEnabled ? "服务已启用" : "服务已禁用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: $proactiveService.config.isEnabled)
                    .labelsHidden()
            }
            
            if proactiveService.config.isEnabled {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("当前提醒数量")
                        Spacer()
                        Text("\(reminderManager.reminders.count)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("未读提醒")
                        Spacer()
                        Text("\(reminderManager.unreadCount)")
                            .foregroundColor(.red)
                            .fontWeight(.semibold)
                    }
                    
                    if let lastAnalysis = proactiveService.lastAnalysisTime {
                        HStack {
                            Text("上次分析")
                            Spacer()
                            Text(formatRelativeTime(lastAnalysis))
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .font(.caption)
            }
        }
    }
    
    // MARK: - 通知权限部分
    private var notificationPermissionSection: some View {
        Section("通知权限") {
            HStack {
                Image(systemName: notificationIcon)
                    .foregroundColor(notificationColor)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("推送通知")
                        .font(.subheadline)
                    
                    Text(notificationStatusText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if notificationPermissionStatus == .denied {
                    Button("设置") {
                        openSystemSettings()
                    }
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                } else if notificationPermissionStatus == .notDetermined {
                    Button("请求") {
                        requestNotificationPermission()
                    }
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                }
            }
        }
    }
    
    // MARK: - 基础设置部分
    private var basicSettingsSection: some View {
        Section("基础设置") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("置信度阈值")
                    Spacer()
                    Text("\(Int(proactiveService.config.minimumConfidence * 100))%")
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: $proactiveService.config.minimumConfidence,
                    in: 0.5...0.95,
                    step: 0.05
                )
                .accentColor(themeManager.currentTheme.primaryColor)
                
                Text("只有置信度高于此阈值的提醒才会被推送")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("每次最多推送")
                Spacer()
                
                Picker("每次最多推送", selection: $proactiveService.config.maxMessagesPerSession) {
                    ForEach(1...5, id: \.self) { count in
                        Text("\(count)条").tag(count)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
        }
    }
    
    // MARK: - 消息类型设置
    private var messageTypesSection: some View {
        Section("推送消息类型") {
            ForEach(ProactiveMessageType.allCases, id: \.self) { type in
                HStack {
                    Image(systemName: type.icon)
                        .foregroundColor(type.color)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(type.rawValue)
                            .font(.subheadline)
                        
                        Text(getTypeDescription(type))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: Binding(
                        get: { proactiveService.config.enabledMessageTypes.contains(type) },
                        set: { enabled in
                            if enabled {
                                if !proactiveService.config.enabledMessageTypes.contains(type) {
                                    proactiveService.config.enabledMessageTypes.append(type)
                                }
                            } else {
                                proactiveService.config.enabledMessageTypes.removeAll { $0 == type }
                            }
                        }
                    ))
                    .labelsHidden()
                }
            }
        }
    }
    
    // MARK: - 分析频率设置
    private var analysisFrequencySection: some View {
        Section("分析频率") {
            Picker("分析频率", selection: $proactiveService.config.frequency) {
                ForEach(ProactiveServiceConfig.ProactiveFrequency.allCases, id: \.self) { frequency in
                    Text(frequency.rawValue).tag(frequency)
                }
            }
            .pickerStyle(MenuPickerStyle())
            
            Text("更高的分析频率会消耗更多系统资源，但能更及时地发现问题")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 高级设置
    private var advancedSettingsSection: some View {
        Section("高级设置") {
            VStack(alignment: .leading, spacing: 12) {
                Text("免打扰时间")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Text("开始时间")
                    Spacer()
                    
                    Picker("开始时间", selection: $proactiveService.config.quietHoursStart) {
                        ForEach(0..<24, id: \.self) { hour in
                            Text(String(format: "%02d:00", hour)).tag(hour)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("结束时间")
                    Spacer()
                    
                    Picker("结束时间", selection: $proactiveService.config.quietHoursEnd) {
                        ForEach(0..<24, id: \.self) { hour in
                            Text(String(format: "%02d:00", hour)).tag(hour)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                Text("在免打扰时间内不会发送推送通知")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 操作按钮部分
    private var actionsSection: some View {
        Section("操作") {
            Button(action: {
                triggerManualAnalysis()
            }) {
                HStack {
                    if isAnalyzing {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "brain.head.profile")
                    }
                    
                    Text(isAnalyzing ? "分析中..." : "立即执行分析")
                }
                .foregroundColor(themeManager.currentTheme.primaryColor)
            }
            .disabled(isAnalyzing || !proactiveService.config.isEnabled)
            
            Button(action: {
                clearAllReminders()
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("清除所有提醒")
                }
                .foregroundColor(.red)
            }
            .disabled(reminderManager.reminders.isEmpty)
            
            NavigationLink("查看分析日志") {
                AnalysisLogView()
                    .environmentObject(themeManager)
            }
        }
    }
    
    // MARK: - 计算属性
    private var notificationIcon: String {
        switch notificationPermissionStatus {
        case .authorized:
            return "bell.fill"
        case .denied:
            return "bell.slash"
        case .notDetermined:
            return "bell"
        default:
            return "bell"
        }
    }
    
    private var notificationColor: Color {
        switch notificationPermissionStatus {
        case .authorized:
            return .green
        case .denied:
            return .red
        case .notDetermined:
            return .orange
        default:
            return .gray
        }
    }
    
    private var notificationStatusText: String {
        switch notificationPermissionStatus {
        case .authorized:
            return "已授权"
        case .denied:
            return "已拒绝"
        case .notDetermined:
            return "未设置"
        default:
            return "未知"
        }
    }
    
    // MARK: - 方法
    private func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                notificationPermissionStatus = settings.authorizationStatus
            }
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                checkNotificationPermission()
            }
        }
    }
    
    private func openSystemSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    private func triggerManualAnalysis() {
        isAnalyzing = true
        
        Task {
            await proactiveService.performProactiveAnalysis()
            
            await MainActor.run {
                isAnalyzing = false
            }
        }
    }
    
    private func clearAllReminders() {
        reminderManager.clearAllReminders()
    }
    
    private func getTypeDescription(_ type: ProactiveMessageType) -> String {
        switch type {
        case .reminder:
            return "保修期、清洁等提醒"
        case .suggestion:
            return "使用优化建议"
        case .warning:
            return "异常消费警告"
        case .insight:
            return "数据洞察分析"
        case .opportunity:
            return "购买机会推荐"
        case .maintenance:
            return "维护保养提醒"
        }
    }
    
    private func formatRelativeTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - 分析日志视图
struct AnalysisLogView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("分析日志")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("此功能正在开发中\n未来将显示详细的分析历史记录")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .navigationTitle("分析日志")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 预览
#Preview {
    ProactiveServiceSettingsView()
        .environmentObject(ThemeManager())
} 