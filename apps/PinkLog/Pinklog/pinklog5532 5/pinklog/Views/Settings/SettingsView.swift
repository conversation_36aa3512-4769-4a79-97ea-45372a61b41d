import SwiftUI

struct SettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager




    var body: some View {
        NavigationView {
            Form {
                // 外观设置
                Section(header: Text("外观")) {
                    Picker("主题", selection: $themeManager.currentTheme) {
                        ForEach(AppThemeType.allCases) { theme in
                            Text(theme.rawValue).tag(theme)
                        }
                    }
                }

                // 值度指数权重设置已移除，因为依赖于已删除的分析功能



                // 高级设置
                Section(header: Text("高级设置")) {
                    NavigationLink(destination: ImageOptimizationSettingsView()) {
                        HStack {
                            Image(systemName: "photo.fill")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                            Text("图片优化")
                        }
                    }
                }



                // 版本信息
                Section(footer: Text("版本 1.0.0 (1)")) {
                    EmptyView()
                }
            }
            .navigationTitle("设置")
            .navigationBarItems(trailing: But<PERSON>("完成") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(ThemeManager())
}
