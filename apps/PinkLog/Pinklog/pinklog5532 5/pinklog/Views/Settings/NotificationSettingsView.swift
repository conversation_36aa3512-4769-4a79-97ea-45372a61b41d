import SwiftUI

struct NotificationSettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @ObservedObject private var notificationManager = NotificationManager.shared
    
    // 用户偏好设置 (UserDefaults)
    @AppStorage("reminderDaysBeforeExpiry") private var reminderDaysBeforeExpiry: Int = 7 // 默认7天
    @AppStorage("reminderDaysBeforeWarrantyEnd") private var reminderDaysBeforeWarrantyEnd: Int = 30 // 默认30天
    @AppStorage("lowUsageReminderDays") private var lowUsageReminderDays: Int = 30 // 默认30天
    
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            Form {
                // 通知权限
                Section(header: Text("通知权限")) {
                    HStack {
                        Text("通知权限状态")
                        Spacer()
                        Text(notificationManager.isAuthorized ? "已授权" : "未授权")
                            .foregroundColor(notificationManager.isAuthorized ? .green : .red)
                    }
                    
                    if !notificationManager.isAuthorized {
                        Button("请求通知权限") {
                            notificationManager.requestAuthorization()
                        }
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                }
                
                // 通知提醒设置
                Section(header: Text("通知提醒设置"), footer: Text("设置各类提醒的触发时间")) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("过期提醒")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text("提前")
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(reminderDaysBeforeExpiry) 天")
                                .foregroundColor(.primary)
                                .frame(width: 60, alignment: .trailing)
                        }
                        
                        Slider(value: Binding(
                            get: { Double(reminderDaysBeforeExpiry) },
                            set: { reminderDaysBeforeExpiry = Int($0) }
                        ), in: 1...90, step: 1)
                        .accentColor(themeManager.currentTheme.primaryColor)
                    }
                    .padding(.vertical, 4)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("保修到期提醒")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text("提前")
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(reminderDaysBeforeWarrantyEnd) 天")
                                .foregroundColor(.primary)
                                .frame(width: 60, alignment: .trailing)
                        }
                        
                        Slider(value: Binding(
                            get: { Double(reminderDaysBeforeWarrantyEnd) },
                            set: { reminderDaysBeforeWarrantyEnd = Int($0) }
                        ), in: 1...90, step: 1)
                        .accentColor(themeManager.currentTheme.primaryColor)
                    }
                    .padding(.vertical, 4)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("低使用率提醒")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text("超过")
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(lowUsageReminderDays) 天未使用")
                                .foregroundColor(.primary)
                                .frame(width: 120, alignment: .trailing)
                        }
                        
                        Slider(value: Binding(
                            get: { Double(lowUsageReminderDays) },
                            set: { lowUsageReminderDays = Int($0) }
                        ), in: 7...180, step: 1)
                        .accentColor(themeManager.currentTheme.primaryColor)
                    }
                    .padding(.vertical, 4)
                }
                
                // 重置设置
                Section {
                    Button("恢复默认设置") {
                        reminderDaysBeforeExpiry = 7
                        reminderDaysBeforeWarrantyEnd = 30
                        lowUsageReminderDays = 30
                    }
                    .foregroundColor(themeManager.currentTheme.primaryColor)
                }
            }
            .navigationTitle("通知设置")
            .navigationBarItems(trailing: Button("完成") {
                presentationMode.wrappedValue.dismiss()
            })
            .onAppear {
                notificationManager.checkAuthorizationStatus()
            }
            .alert(isPresented: $showingPermissionAlert) {
                Alert(
                    title: Text("通知权限"),
                    message: Text("需要通知权限才能发送提醒。请在设置中允许通知。"),
                    primaryButton: .default(Text("去设置")) {
                        if let url = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(url)
                        }
                    },
                    secondaryButton: .cancel()
                )
            }
        }
    }
}

#Preview {
    NotificationSettingsView()
        .environmentObject(ThemeManager())
}
