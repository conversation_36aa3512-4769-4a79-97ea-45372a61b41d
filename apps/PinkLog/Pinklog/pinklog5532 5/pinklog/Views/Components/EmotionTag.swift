import SwiftUI

struct EmotionTag: View {
    let emotionalValue: Int16
    var compact: Bool = false
    
    // 情感等级对应的标签文本和颜色
    private var tagInfo: (text: String, color: Color) {
        switch emotionalValue {
        case 5:
            return ("珍贵回忆", Color.pink)
        case 4:
            return ("温馨时刻", Color.orange)
        case 3:
            return ("美好瞬间", Color.blue)
        case 2:
            return ("有趣经历", Color.green)
        default:
            return ("微小记忆", Color.gray)
        }
    }
    
    var body: some View {
        HStack(spacing: 4) {
            // 情感图标
            Image(systemName: "heart.fill")
                .font(compact ? .caption2 : .caption)
                .foregroundColor(tagInfo.color)
            
            // 情感文本
            if !compact {
                Text(tagInfo.text)
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(tagInfo.color)
            }
        }
        .padding(.horizontal, compact ? 6 : 8)
        .padding(.vertical, compact ? 2 : 4)
        .background(
            Capsule()
                .fill(tagInfo.color.opacity(0.15))
        )
        .overlay(
            Capsule()
                .stroke(tagInfo.color.opacity(0.3), lineWidth: 0.5)
        )
    }
}

// 预览
#Preview {
    VStack(spacing: 15) {
        EmotionTag(emotionalValue: 5)
        EmotionTag(emotionalValue: 4)
        EmotionTag(emotionalValue: 3)
        EmotionTag(emotionalValue: 2)
        EmotionTag(emotionalValue: 1)
        
        HStack {
            EmotionTag(emotionalValue: 5, compact: true)
            EmotionTag(emotionalValue: 4, compact: true)
            EmotionTag(emotionalValue: 3, compact: true)
        }
    }
    .padding()
    .background(Color.gray.opacity(0.1))
} 