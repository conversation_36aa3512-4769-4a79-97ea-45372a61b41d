import SwiftUI
import CoreData

// 图表数据适配器 - 负责将原有数据转换为新Chart格式
class ChartDataAdapter {
    // MARK: - 成本效益曲线数据转换
    static func getCostEffectivenessChartData(for product: Product) -> [ChartPoint] {
        let calendar = Calendar.current
        
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 按天显示成本数据
            guard let purchaseDate = product.purchaseDate else { return [] }
            
            // 计算从购买日期到今天的天数
            let daysFromPurchase = calendar.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
            guard daysFromPurchase > 0 else { return [] }
            
            // 计算总成本
            let totalCost = product.totalCostOfOwnership
            
            // 生成按天的数据点
            var dataPoints: [ChartPoint] = []
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "MM/dd"
            
            // 决定数据点间隔（如果天数太多，适当减少点数）
            let interval = daysFromPurchase > 30 ? (daysFromPurchase / 15) : 1
            
            for day in stride(from: 0, to: daysFromPurchase + 1, by: interval) {
                let pointDate = calendar.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()
                let dayCount = max(1, day)  // 避免除以零
                let dailyCost = totalCost / Double(dayCount)
                
                dataPoints.append(
                    ChartPoint(
                        date: pointDate,
                        label: dateFormatter.string(from: pointDate),
                        value: dailyCost
                    )
                )
            }
            
            return dataPoints
        } else {
            // 按次使用的产品 - 详细图表
            let costData = ChartDataProvider.getProductCostEffectivenessData(for: product)
            if costData.isEmpty { return [] }
            
            // 需要将原始数据转换为带日期的ChartPoint
            return costData.enumerated().map { index, dataPoint in
                // 从标签解析日期
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy/MM"
                let date = dateFormatter.date(from: dataPoint.label) ?? Date().addingTimeInterval(Double(index) * 30 * 24 * 3600)
                
                // 格式化为MM/dd格式
                let outputFormatter = DateFormatter()
                outputFormatter.dateFormat = "MM/dd"
                
                return ChartPoint(
                    date: date,
                    label: outputFormatter.string(from: date),
                    value: dataPoint.value
                )
            }
        }
    }
    
    // MARK: - 使用率曲线数据转换
    static func getUsageChartData(for product: Product) -> [ChartPoint] {
        let calendar = Calendar.current
        
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 按天显示服役累计数据
            guard let purchaseDate = product.purchaseDate else { return [] }
            
            // 计算从购买日期到今天的天数
            let daysFromPurchase = calendar.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
            guard daysFromPurchase > 0 else { return [] }
            
            // 生成按天的数据点
            var dataPoints: [ChartPoint] = []
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "MM/dd"
            
            // 决定数据点间隔（如果天数太多，适当减少点数）
            let interval = daysFromPurchase > 30 ? (daysFromPurchase / 15) : 1
            
            for day in stride(from: 0, to: daysFromPurchase + 1, by: interval) {
                let pointDate = calendar.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()
                
                dataPoints.append(
                    ChartPoint(
                        date: pointDate,
                        label: dateFormatter.string(from: pointDate),
                        value: Double(day + 1)  // 服役天数累计
                    )
                )
            }
            
            return dataPoints
        } else {
            // 按次使用的产品 - 详细图表
            let usageData = ChartDataProvider.getProductUsageData(for: product)
            if usageData.isEmpty { return [] }
            
            // 需要将原始数据转换为带日期的ChartPoint
            return usageData.enumerated().map { index, dataPoint in
                // 从标签解析日期
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy/MM"
                let date = dateFormatter.date(from: dataPoint.label) ?? Date().addingTimeInterval(Double(index) * 30 * 24 * 3600)
                
                // 格式化为MM/dd格式
                let outputFormatter = DateFormatter()
                outputFormatter.dateFormat = "MM/dd"
                
                return ChartPoint(
                    date: date,
                    label: outputFormatter.string(from: date),
                    value: dataPoint.value
                )
            }
        }
    }
    
    // MARK: - 满意度曲线数据转换
    static func getSatisfactionChartData(for product: Product) -> [ChartPoint] {
        let calendar = Calendar.current
        
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 尝试按天提取满意度变化
            guard let purchaseDate = product.purchaseDate else { return [] }
            
            // 获取满意度记录
            let records = product.usageRecords?.allObjects as? [UsageRecord] ?? []
            
            // 如果有满意度记录，使用实际记录
            if !records.isEmpty {
                // 先按日期对记录排序
                let sortedRecords = records.sorted { 
                    ($0.date ?? Date.distantPast) < ($1.date ?? Date.distantPast) 
                }
                
                // 创建数据点
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "MM/dd"
                
                return sortedRecords.map { record in
                    let recordDate = record.date ?? Date()
                    return ChartPoint(
                        date: recordDate,
                        label: dateFormatter.string(from: recordDate),
                        value: Double(record.satisfaction)
                    )
                }
            } else {
                // 如果没有满意度记录，生成均匀的模拟数据
                // 计算从购买日期到今天的天数
                let daysFromPurchase = calendar.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
                guard daysFromPurchase > 0 else { return [] }
                
                // 生成按天的数据点
                var dataPoints: [ChartPoint] = []
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "MM/dd"
                
                // 决定数据点间隔
                let interval = daysFromPurchase > 30 ? (daysFromPurchase / 15) : 1
                
                for day in stride(from: 0, to: daysFromPurchase + 1, by: interval) {
                    let pointDate = calendar.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()
                    
                    dataPoints.append(
                        ChartPoint(
                            date: pointDate,
                            label: dateFormatter.string(from: pointDate),
                            value: product.averageSatisfaction
                        )
                    )
                }
                
                return dataPoints
            }
        } else {
            // 按次使用的产品 - 详细图表
            let satisfactionData = ChartDataProvider.getProductSatisfactionData(for: product)
            if satisfactionData.isEmpty { return [] }
            
            // 需要将原始数据转换为带日期的ChartPoint
            return satisfactionData.enumerated().map { index, dataPoint in
                // 从标签解析日期
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy/MM"
                let date = dateFormatter.date(from: dataPoint.label) ?? Date().addingTimeInterval(Double(index) * 30 * 24 * 3600)
                
                // 格式化为MM/dd格式
                let outputFormatter = DateFormatter()
                outputFormatter.dateFormat = "MM/dd"
                
                return ChartPoint(
                    date: date,
                    label: outputFormatter.string(from: date),
                    value: dataPoint.value
                )
            }
        }
    }
    
    // MARK: - 格式化方法
    
    // 成本格式化
    static func costFormatter(_ value: Double) -> String {
        return "¥\(String(format: "%.1f", value))"
    }
    
    // 使用次数/天数格式化
    static func usageFormatter(_ value: Double) -> String {
        if value == 1 {
            return "\(Int(value))天"
        } else {
            return "\(Int(value))天"
        }
    }
    
    // 使用次数/天数格式化 - 增强版，根据产品计算方式返回不同单位
    static func getUsageFormatter(for product: Product) -> (Double) -> String {
        if product.valuationMethod == "daily" {
            // 按天计算的产品显示"天"
            return { value in
                return "\(Int(value))天"
            }
        } else {
            // 按次计算的产品显示"次"
            return { value in
                return "\(Int(value))次"
            }
        }
    }
    
    // 满意度格式化
    static func satisfactionFormatter(_ value: Double) -> String {
        return "\(String(format: "%.1f", value))/5"
    }
}