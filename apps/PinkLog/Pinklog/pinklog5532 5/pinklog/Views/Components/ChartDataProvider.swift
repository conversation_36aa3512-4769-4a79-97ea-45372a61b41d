import SwiftUI
import CoreData
// 引入ChartView中的类型定义
import Charts

// 图表数据提供者
class ChartDataProvider {
    // MARK: - 产品使用率图表数据
    static func getProductUsageData(for product: Product) -> [ChartDataPoint] {
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 显示服役天数
            guard let purchaseDate = product.purchaseDate else { return [] }
            let days = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
            return [
                ChartDataPoint(label: "总服役天数", value: Double(days))
            ]
        } else {
            // 按次计算的产品 - 原始逻辑
            guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                return []
            }
            
            // 按月分组使用记录
            let calendar = Calendar.current
            var usageByMonth: [Date: Int] = [:]
            
            for record in records {
                guard let date = record.date else { continue }
                
                // 获取年月
                let components = calendar.dateComponents([.year, .month], from: date)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    usageByMonth[monthDate, default: 0] += 1
                }
            }
            
            // 排序并转换为图表数据点
            let sortedData = usageByMonth.sorted { $0.key < $1.key }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy/MM"
            
            return sortedData.map { date, count in
                ChartDataPoint(label: dateFormatter.string(from: date), value: Double(count))
            }
        }
    }
    
    // MARK: - 产品成本效益图表数据
    static func getProductCostEffectivenessData(for product: Product) -> [ChartDataPoint] {
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 显示单日成本
            guard let purchaseDate = product.purchaseDate else { return [] }
            let days = max(1, Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 1)
            let dailyCost = product.totalCostOfOwnership / Double(days)
            return [
                ChartDataPoint(label: "单日成本", value: dailyCost)
            ]
        } else {
            // 按次计算的产品 - 原始逻辑
            guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                return []
            }
            
            // 按月分组使用记录和费用
            let calendar = Calendar.current
            var usageByMonth: [Date: Int] = [:]
            var expenseByMonth: [Date: Double] = [:]
            
            // 初始购买费用
            if let purchaseDate = product.purchaseDate {
                let components = calendar.dateComponents([.year, .month], from: purchaseDate)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    expenseByMonth[monthDate, default: 0] += product.price
                }
            }
            
            // 使用记录
            for record in records {
                guard let date = record.date else { continue }
                
                let components = calendar.dateComponents([.year, .month], from: date)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    usageByMonth[monthDate, default: 0] += 1
                }
            }
            
            // 费用记录
            if let expenses = product.relatedExpenses?.allObjects as? [RelatedExpense] {
                for expense in expenses {
                    guard let date = expense.date else { continue }
                    
                    let components = calendar.dateComponents([.year, .month], from: date)
                    if let year = components.year, let month = components.month,
                       let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                        expenseByMonth[monthDate, default: 0] += expense.amount
                    }
                }
            }
            
            // 计算累计使用次数和累计费用
            var totalUsage = 0
            var totalExpense = 0.0
            var costPerUseData: [Date: Double] = [:]
            
            // 获取所有月份并排序
            let allMonths = Set(usageByMonth.keys).union(expenseByMonth.keys).sorted()
            
            for month in allMonths {
                totalUsage += usageByMonth[month, default: 0]
                totalExpense += expenseByMonth[month, default: 0]
                
                if totalUsage > 0 {
                    costPerUseData[month] = totalExpense / Double(totalUsage)
                } else {
                    costPerUseData[month] = totalExpense
                }
            }
            
            // 排序并转换为图表数据点
            let sortedData = costPerUseData.sorted { $0.key < $1.key }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy/MM"
            
            return sortedData.map { date, cost in
                ChartDataPoint(label: dateFormatter.string(from: date), value: cost)
            }
        }
    }
    
    // MARK: - 产品满意度图表数据
    static func getProductSatisfactionData(for product: Product) -> [ChartDataPoint] {
        if product.valuationMethod == "daily" {
            // 按天计算的产品 - 使用初始满意度或显示"不适用"
            return [
                ChartDataPoint(label: "按天计算", value: product.averageSatisfaction)
            ]
        } else {
            // 按次计算的产品 - 原始逻辑
            guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                return []
            }
            
            // 按月分组使用记录
            let calendar = Calendar.current
            var satisfactionByMonth: [Date: (total: Double, count: Int)] = [:]
            
            for record in records {
                guard let date = record.date else { continue }
                
                // 获取年月
                let components = calendar.dateComponents([.year, .month], from: date)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    let current = satisfactionByMonth[monthDate, default: (0, 0)]
                    satisfactionByMonth[monthDate] = (current.total + Double(record.satisfaction), current.count + 1)
                }
            }
            
            // 计算每月平均满意度
            var averageSatisfactionByMonth: [Date: Double] = [:]
            for (month, data) in satisfactionByMonth {
                averageSatisfactionByMonth[month] = data.total / Double(data.count)
            }
            
            // 排序并转换为图表数据点
            let sortedData = averageSatisfactionByMonth.sorted { $0.key < $1.key }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy/MM"
            
            return sortedData.map { date, satisfaction in
                ChartDataPoint(label: dateFormatter.string(from: date), value: satisfaction)
            }
        }
    }
    
    // MARK: - 其他静态方法保持不变...
    static func getMonthlySpendingData(products: [Product]) -> [ChartDataPoint] {
        var monthlySpending: [Date: Double] = [:]
        let calendar = Calendar.current
        
        // 计算每月购买金额
        for product in products {
            guard let purchaseDate = product.purchaseDate else { continue }
            
            // 获取年月
            let components = calendar.dateComponents([.year, .month], from: purchaseDate)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                monthlySpending[monthDate, default: 0] += product.price
            }
        }
        
        // 排序并转换为图表数据点
        let sortedData = monthlySpending.sorted { $0.key < $1.key }
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy/MM"
        
        return sortedData.map { date, amount in
            ChartDataPoint(label: dateFormatter.string(from: date), value: amount)
        }
    }
    
    static func getPurchaseMotivationData(products: [Product]) -> [ChartDataPoint] {
        var motivationCounts: [String: Int] = [:]
        
        // 统计各购买动机的产品数量
        for product in products {
            let motivation = product.purchaseMotivation ?? "未知"
            motivationCounts[motivation, default: 0] += 1
        }
        
        // 转换为图表数据点
        return motivationCounts.map { motivation, count in
            ChartDataPoint(label: motivation, value: Double(count))
        }.sorted { $0.value > $1.value }
    }
}