//
//  PremiumCard.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/1/17.
//  世界级的卡片容器组件
//

import SwiftUI

struct PremiumCard<Content: View>: View {
    let content: Content
    let accentColor: Color
    let title: String
    let subtitle: String?
    @State private var isAnimating = false
    
    init(
        title: String,
        subtitle: String? = nil,
        accentColor: Color = .blue,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.accentColor = accentColor
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 卡片头部
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        if let subtitle = subtitle {
                            Text(subtitle)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                    
                    // 装饰性圆圈
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [accentColor, accentColor.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 8, height: 8)
                        .scaleEffect(isAnimating ? 1.5 : 1.0)
                        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: isAnimating)
                }
                
                // 装饰性分割线
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [accentColor.opacity(0.3), accentColor.opacity(0.1), Color.clear],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(height: 1)
            }
            .padding(.horizontal, 24)
            .padding(.top, 24)
            .padding(.bottom, 16)
            
            // 卡片内容
            content
                .padding(.horizontal, 24)
                .padding(.bottom, 24)
        }
        .background(
            ZStack {
                // 主背景
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                
                // 渐变边框
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [
                                accentColor.opacity(0.3),
                                accentColor.opacity(0.1),
                                Color.clear,
                                accentColor.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
                
                // 内部高光
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.5), Color.clear],
                            startPoint: .top,
                            endPoint: .bottom
                        ),
                        lineWidth: 0.5
                    )
                    .blur(radius: 0.5)
            }
        )
        .shadow(color: accentColor.opacity(0.1), radius: 20, x: 0, y: 10)
        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        .onAppear {
            isAnimating = true
        }
    }
}

// 3D翻转卡片效果
struct FlipCard<Front: View, Back: View>: View {
    let front: Front
    let back: Back
    @Binding var isFlipped: Bool
    
    init(
        isFlipped: Binding<Bool>,
        @ViewBuilder front: () -> Front,
        @ViewBuilder back: () -> Back
    ) {
        self._isFlipped = isFlipped
        self.front = front()
        self.back = back()
    }
    
    var body: some View {
        ZStack {
            front
                .opacity(isFlipped ? 0 : 1)
                .rotation3DEffect(
                    .degrees(isFlipped ? 180 : 0),
                    axis: (x: 0, y: 1, z: 0)
                )
            
            back
                .opacity(isFlipped ? 1 : 0)
                .rotation3DEffect(
                    .degrees(isFlipped ? 0 : -180),
                    axis: (x: 0, y: 1, z: 0)
                )
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isFlipped)
    }
}

// 流动的进度指示器
struct FluidStepIndicator: View {
    let currentStep: Int
    let totalSteps: Int
    let accentColor: Color
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(1...totalSteps, id: \.self) { step in
                Capsule()
                    .fill(step <= currentStep ? accentColor : Color.gray.opacity(0.3))
                    .frame(
                        width: step == currentStep ? 32 : 8,
                        height: 8
                    )
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentStep)
            }
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    VStack(spacing: 20) {
        PremiumCard(
            title: "测试卡片",
            subtitle: "这是一个预览",
            accentColor: .blue
        ) {
            Text("卡片内容")
                .padding()
        }
        
        FluidStepIndicator(currentStep: 2, totalSteps: 4, accentColor: .blue)
    }
    .padding()
    .background(Color.gray.opacity(0.1))
} 