//
//  MagicParticleEngine.swift
//  PinkLog
//
//  Created by World-Class Designer on 2025/1/17.
//  震撼的魔法粒子系统引擎
//

import SwiftUI
import QuartzCore
import UIKit

// MARK: - 魔法粒子引擎
class MagicParticleEngine: ObservableObject {
    static let shared = MagicParticleEngine()
    
    private init() {}
    
    // MARK: - 魔法阵粒子效果
    func createMagicCircleEmitter(for view: UIView, radius: CGFloat) -> CAEmitterLayer {
        let emitterLayer = CAEmitterLayer()
        emitterLayer.emitterPosition = CGPoint(x: view.bounds.midX, y: view.bounds.midY)
        emitterLayer.emitterShape = .circle
        emitterLayer.emitterSize = CGSize(width: radius * 2, height: radius * 2)
        emitterLayer.emitterMode = .outline
        
        // 魔法符文粒子
        let runeCell = CAEmitterCell()
        runeCell.birthRate = 20
        runeCell.lifetime = 3.0
        runeCell.velocity = 50
        runeCell.velocityRange = 20
        runeCell.emissionRange = .pi * 2
        runeCell.scale = 0.5
        runeCell.scaleRange = 0.3
        runeCell.alphaSpeed = -0.3
        runeCell.contents = createRuneTexture().cgImage
        
        // 魔法光粒子
        let lightCell = CAEmitterCell()
        lightCell.birthRate = 50
        lightCell.lifetime = 2.0
        lightCell.velocity = 30
        lightCell.velocityRange = 15
        lightCell.emissionRange = .pi * 2
        lightCell.scale = 0.2
        lightCell.scaleRange = 0.1
        lightCell.alphaSpeed = -0.5
        lightCell.redRange = 0.3
        lightCell.greenRange = 0.3
        lightCell.blueRange = 0.3
        lightCell.contents = createSparkTexture().cgImage
        
        emitterLayer.emitterCells = [runeCell, lightCell]
        return emitterLayer
    }
    
    // MARK: - 产品识别爆炸效果
    func createRecognitionExplosion(at point: CGPoint, in view: UIView) {
        let emitterLayer = CAEmitterLayer()
        emitterLayer.emitterPosition = point
        emitterLayer.emitterShape = .point
        
        // 爆炸粒子
        let explosionCell = CAEmitterCell()
        explosionCell.birthRate = 100
        explosionCell.lifetime = 1.5
        explosionCell.velocity = 200
        explosionCell.velocityRange = 100
        explosionCell.emissionRange = .pi * 2
        explosionCell.scale = 1.0
        explosionCell.scaleSpeed = -0.8
        explosionCell.alphaSpeed = -1.0
        explosionCell.contents = createStarTexture().cgImage
        
        // 光环粒子
        let ringCell = CAEmitterCell()
        ringCell.birthRate = 30
        ringCell.lifetime = 2.0
        ringCell.velocity = 150
        ringCell.velocityRange = 50
        ringCell.emissionRange = .pi * 2
        ringCell.scale = 0.8
        ringCell.scaleSpeed = 1.5
        ringCell.alphaSpeed = -0.5
        ringCell.contents = createRingTexture().cgImage
        
        emitterLayer.emitterCells = [explosionCell, ringCell]
        view.layer.addSublayer(emitterLayer)
        
        // 2秒后移除
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            emitterLayer.removeFromSuperlayer()
        }
    }
    
    // MARK: - 心跳粒子效果
    func createHeartbeatParticles(around view: UIView, intensity: Float) {
        let emitterLayer = CAEmitterLayer()
        emitterLayer.emitterPosition = CGPoint(x: view.bounds.midX, y: view.bounds.midY)
        emitterLayer.emitterShape = .circle
        emitterLayer.emitterSize = view.bounds.size
        
        let heartCell = CAEmitterCell()
        heartCell.birthRate = Float(intensity * 50)
        heartCell.lifetime = 2.0
        heartCell.velocity = 100
        heartCell.velocityRange = 50
        heartCell.emissionRange = .pi * 2
        heartCell.scale = 0.3
        heartCell.scaleRange = 0.2
        heartCell.alphaSpeed = -0.5
        
        // 根据强度改变颜色
        let hue = intensity * 0.8 // 从红色到彩虹色
        heartCell.color = UIColor(hue: CGFloat(hue), saturation: 1.0, brightness: 1.0, alpha: 1.0).cgColor
        heartCell.contents = createHeartTexture().cgImage
        
        emitterLayer.emitterCells = [heartCell]
        view.layer.addSublayer(emitterLayer)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            emitterLayer.removeFromSuperlayer()
        }
    }
    
    // MARK: - 收藏仪式粒子风暴
    func createCollectionRitualStorm(in view: UIView, completion: @escaping () -> Void) {
        let emitterLayer = CAEmitterLayer()
        emitterLayer.emitterPosition = CGPoint(x: view.bounds.midX, y: view.bounds.height + 100)
        emitterLayer.emitterShape = .point
        
        // 金币粒子
        let coinCell = CAEmitterCell()
        coinCell.birthRate = 200
        coinCell.lifetime = 4.0
        coinCell.velocity = -300
        coinCell.velocityRange = 100
        coinCell.emissionRange = .pi / 3
        coinCell.scale = 0.8
        coinCell.scaleRange = 0.4
        coinCell.spin = 5.0
        coinCell.spinRange = 3.0
        coinCell.alphaSpeed = -0.25
        coinCell.contents = createCoinTexture().cgImage
        
        // 星星粒子
        let starCell = CAEmitterCell()
        starCell.birthRate = 150
        starCell.lifetime = 5.0
        starCell.velocity = -250
        starCell.velocityRange = 80
        starCell.emissionRange = .pi / 4
        starCell.scale = 0.6
        starCell.scaleRange = 0.3
        starCell.alphaSpeed = -0.2
        starCell.contents = createStarTexture().cgImage
        
        emitterLayer.emitterCells = [coinCell, starCell]
        view.layer.addSublayer(emitterLayer)
        
        // 3秒后完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            emitterLayer.removeFromSuperlayer()
            completion()
        }
    }
    
    // MARK: - 纹理生成器
    private func createRuneTexture() -> UIImage {
        let size = CGSize(width: 30, height: 30)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.systemBlue.withAlphaComponent(0.8).cgColor)
        
        // 绘制简单的符文形状
        let path = UIBezierPath()
        path.move(to: CGPoint(x: 15, y: 5))
        path.addLine(to: CGPoint(x: 25, y: 15))
        path.addLine(to: CGPoint(x: 15, y: 25))
        path.addLine(to: CGPoint(x: 5, y: 15))
        path.close()
        
        context.addPath(path.cgPath)
        context.fillPath()
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
    
    private func createSparkTexture() -> UIImage {
        let size = CGSize(width: 10, height: 10)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.white.cgColor)
        context.fillEllipse(in: CGRect(origin: .zero, size: size))
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
    
    private func createStarTexture() -> UIImage {
        let size = CGSize(width: 20, height: 20)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.systemYellow.cgColor)
        
        // 绘制五角星
        let path = UIBezierPath()
        let center = CGPoint(x: 10, y: 10)
        let radius: CGFloat = 8
        
        for i in 0..<5 {
            let angle = Double(i) * 2 * Double.pi / 5 - Double.pi / 2
            let point = CGPoint(
                x: center.x + CGFloat(cos(angle)) * radius,
                y: center.y + CGFloat(sin(angle)) * radius
            )
            
            if i == 0 {
                path.move(to: point)
            } else {
                path.addLine(to: point)
            }
        }
        path.close()
        
        context.addPath(path.cgPath)
        context.fillPath()
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
    
    private func createRingTexture() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        context.setStrokeColor(UIColor.systemPurple.withAlphaComponent(0.6).cgColor)
        context.setLineWidth(3.0)
        
        let rect = CGRect(x: 3, y: 3, width: 34, height: 34)
        context.strokeEllipse(in: rect)
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
    
    private func createHeartTexture() -> UIImage {
        let size = CGSize(width: 16, height: 16)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.systemPink.cgColor)
        
        // 绘制心形
        let path = UIBezierPath()
        path.move(to: CGPoint(x: 8, y: 14))
        path.addCurve(to: CGPoint(x: 2, y: 6), 
                     controlPoint1: CGPoint(x: 8, y: 10), 
                     controlPoint2: CGPoint(x: 2, y: 10))
        path.addCurve(to: CGPoint(x: 8, y: 2), 
                     controlPoint1: CGPoint(x: 2, y: 2), 
                     controlPoint2: CGPoint(x: 8, y: 2))
        path.addCurve(to: CGPoint(x: 14, y: 6), 
                     controlPoint1: CGPoint(x: 8, y: 2), 
                     controlPoint2: CGPoint(x: 14, y: 2))
        path.addCurve(to: CGPoint(x: 8, y: 14), 
                     controlPoint1: CGPoint(x: 14, y: 10), 
                     controlPoint2: CGPoint(x: 8, y: 10))
        
        context.addPath(path.cgPath)
        context.fillPath()
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
    
    private func createCoinTexture() -> UIImage {
        let size = CGSize(width: 24, height: 24)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()!
        
        // 金色渐变
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colors = [UIColor.systemYellow.cgColor, UIColor.systemOrange.cgColor]
        let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: nil)!
        
        let rect = CGRect(origin: .zero, size: size)
        context.drawRadialGradient(gradient, 
                                  startCenter: CGPoint(x: 12, y: 12), 
                                  startRadius: 0, 
                                  endCenter: CGPoint(x: 12, y: 12), 
                                  endRadius: 12, 
                                  options: [])
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
} 