//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var showingForm = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var isProcessing = false
    @State private var processingProgress: Double = 0.0
    @State private var processingTimer: Timer?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 相机界面 - 始终存在，通过变形过渡到表单
                if !showingForm {
                    cameraView
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .scaleEffect(showingForm ? 0.8 : 1.0)
                        .opacity(showingForm ? 0 : 1)
                }

                // 表单界面 - 从相机界面变形而来
                if showingForm, let images = capturedImages {
                    formView(images: images)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .scaleEffect(showingForm ? 1.0 : 0.8)
                        .opacity(showingForm ? 1 : 0)
                }

                // 处理状态覆盖层 - 在相机界面上显示
                if isProcessing {
                    processingOverlay
                }
            }
        }
        .ignoresSafeArea()
        .animation(.spring(response: 0.8, dampingFraction: 0.85, blendDuration: 0.2), value: showingForm)
        .animation(.easeInOut(duration: 0.3), value: isProcessing)
        .onDisappear {
            // 🔥 视图销毁时，确保定时器也被销毁
            processingTimer?.invalidate()
            
            // 🔥 视图销毁时清理图片引用
            autoreleasepool {
                capturedImages = nil
            }
            ImageManager.shared.clearTemporaryCache()
            logMemoryUsage("CameraToAddProductView销毁后清理")
        }
    }

    // MARK: - 相机视图
    private var cameraView: some View {
        // 🔥 依赖注入：将SubjectLiftManager实例传递下去
        CameraViewControllerWrapper(subjectLiftManager: .shared) { originalImage, liftedImg, stickerImg in
            autoreleasepool {
                // 记录内存使用情况
                logMemoryUsage("拍照完成，处理前")

                // 🔥 关键修复：存储图片用于表单显示，但在动画完成后立即清理
                capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
                onProcessCompleted?(originalImage, liftedImg, stickerImg)

                // 开始处理状态
                isProcessing = true
                startProcessingAnimation()

                // 记录内存使用情况
                logMemoryUsage("拍照完成，处理后")
            }
        }
        .overlay(
            // 相机控制栏
            VStack {
                HStack {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .font(.system(size: 17, weight: .medium))
                    .padding()

                    Spacer()
                }

                Spacer()
            }
        )
    }

    // MARK: - 处理状态覆盖层
    private var processingOverlay: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.75)
                .ignoresSafeArea()

            VStack(spacing: 24) {
                // 🔥 移除预览图片显示，减少内存占用
                // 只显示处理进度

                // 处理进度
                VStack(spacing: 12) {
                    Text("正在创建贴纸风格...")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)

                    // 简洁的进度条
                    ProgressView(value: processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .white))
                        .frame(width: 180)
                        .scaleEffect(y: 2)

                    Text("\(Int(processingProgress * 100))%")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
    }

    // MARK: - 表单视图
    private func formView(images: (original: UIImage, lifted: UIImage, sticker: UIImage)) -> some View {
        NavigationView {
            AddProductView(
                presetImage: images.original,
                presetLiftedImage: images.lifted,
                presetStickerImage: images.sticker,
                showCancelButton: false,
                onSaveCompleted: {
                    print("🎉 保存完成，关闭整个拍照添加流程")
                    // 🔥 保存完成后清理图片引用
                    cleanupImageReferences()
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .navigationBarItems(leading: Button("取消") {
                // 🔥 取消时也清理图片引用
                cleanupImageReferences()
                presentationMode.wrappedValue.dismiss()
            })
        }
    }

    // MARK: - 处理动画
    private func startProcessingAnimation() {
        processingProgress = 0.0

        // 快速但平滑的进度动画
        processingTimer = Timer.scheduledTimer(withTimeInterval: 0.03, repeats: true) { timer in
            processingProgress += 0.04

            if processingProgress >= 1.0 {
                timer.invalidate()
                processingProgress = 1.0

                // 短暂延迟后直接变形到表单
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                    isProcessing = false

                    // 优雅的变形过渡到表单
                    withAnimation(.spring(response: 0.8, dampingFraction: 0.85, blendDuration: 0.2)) {
                        showingForm = true
                    }

                    // 🔥 不立即清理capturedImages，让表单正常显示
                    // 清理将在用户保存或关闭表单时进行

                    // 记录内存使用情况
                    logMemoryUsage("动画完成，清理预览图片后")
                }
            }
        }
    }

    // MARK: - Helper Methods

    /// 🔥 清理图片引用，释放内存
    private func cleanupImageReferences() {
        autoreleasepool {
            capturedImages = nil
            ImageManager.shared.clearTemporaryCache()
        }
        logMemoryUsage("清理图片引用后")
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("CameraToAddProductView: \(context)")
    }
}

// MARK: - 不自动关闭的相机控制器
class NonDismissingCameraController: CameraController {
    
    // 🔥 依赖注入: 重写init以确保依赖传递
    override init(subjectLiftManager: SubjectLiftManager = .shared) {
        super.init(subjectLiftManager: subjectLiftManager)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        autoreleasepool {
            // 记录内存使用情况
            logMemoryUsage("开始自动处理")

            // 显示处理指示器
            showProcessingIndicator()

            SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
                autoreleasepool {
                    DispatchQueue.main.async {
                        self.hideProcessingIndicator()

                        switch result {
                        case .success(let (liftedImage, stickerImage)):
                            callback(originalImage, liftedImage, stickerImage)
                            // 不自动dismiss，让父视图控制
                        case .failure(_):
                            // 处理失败，返回原图
                            callback(originalImage, originalImage, originalImage)
                            // 不自动dismiss，让父视图控制
                        }

                        // 记录内存使用情况
                        self.logMemoryUsage("自动处理完成")
                    }
                }
            }
        }
    }

    // MARK: - 重写相册选择图片处理，不自动关闭
    override func handleSelectedImage(_ image: UIImage) {
        autoreleasepool {
            logMemoryUsage("开始处理相册选择的图片")
            
            // 压缩图片以优化内存使用
            let compressedImage = ImageManager.shared.compressImage(image, maxSize: 512, quality: 0.5)
            print("📸 相册图片压缩: 原尺寸 \(image.size) -> 压缩后尺寸 \(compressedImage.size)")
            
            // 如果设置了自动处理回调，则进行自动抠图和贴纸化
            if let autoProcessCallback = onAutoProcessCompleted {
                startAutoProcessing(originalImage: compressedImage, callback: autoProcessCallback)
            } else {
                // 否则使用原有的简单回调
                onImageCaptured?(compressedImage)
                // 不自动dismiss，让父视图控制
            }
        }
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("NonDismissingCameraController: \(context)")
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    // 🔥 依赖注入: 持有SubjectLiftManager
    let subjectLiftManager: SubjectLiftManager

    // 🔥 自定义init，解决"Extra trailing closure"编译错误
    init(subjectLiftManager: SubjectLiftManager, onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)? = nil) {
        self.subjectLiftManager = subjectLiftManager
        self.onProcessCompleted = onProcessCompleted
    }

    func makeUIViewController(context: Context) -> NonDismissingCameraController {
        let controller = NonDismissingCameraController(subjectLiftManager: subjectLiftManager)
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: NonDismissingCameraController, context: Context) {}
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
