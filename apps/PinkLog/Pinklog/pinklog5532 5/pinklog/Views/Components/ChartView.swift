import SwiftUI
import Charts

// 图表数据点
struct ChartPoint: Identifiable, Equatable {
    var id = UUID()
    var date: Date
    var label: String
    var value: Double

    init(date: Date, label: String, value: Double) {
        self.date = date
        self.label = label
        self.value = value
    }

    // 便利初始化方法，支持从原有ChartDataPoint转换
    init(from dataPoint: ChartDataPoint, date: Date) {
        self.label = dataPoint.label
        self.value = dataPoint.value
        self.date = date
    }

    // 实现Equatable
    static func == (lhs: ChartPoint, rhs: ChartPoint) -> Bool {
        return lhs.id == rhs.id
    }
}

// 图表类型
enum ChartStyle {
    case line       // 折线图
    case bar        // 柱状图
    case area       // 面积图
    case point      // 散点图
}

// 交互图表视图
struct ChartView: View {
    var data: [ChartPoint]
    var title: String
    var subtitle: String
    var chartStyle: ChartStyle
    var color: Color
    var showYAxis: Bool = true
    var showXAxis: Bool = true
    var showLegend: Bool = true
    var animateOnAppear: Bool = true
    var valueFormatter: (Double) -> String = { value in
        return String(format: "%.1f", value)
    }

    @State private var selectedPoint: ChartPoint?
    @State private var plotWidth: CGFloat = 0
    @State private var animationProgress: CGFloat = 0
    @State private var chartInteractionScale: CGFloat = 1.0
    @State private var deactivationTask: DispatchWorkItem? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和副标题
            HStack(alignment: .bottom) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 显示选中的数据点
                if let selectedPoint = selectedPoint {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(selectedPoint.label)
                            .font(.caption)
                            .fontWeight(.semibold)

                        Text(valueFormatter(selectedPoint.value))
                            .font(.headline)
                            .foregroundColor(color)
                    }
                }
            }

            // 图表
            chartContent
                .frame(height: 180)
                .padding(.bottom, 32)
                .scaleEffect(chartInteractionScale)

            // 图例
            if showLegend && selectedPoint == nil {
                HStack {
                    Circle()
                        .fill(color)
                        .frame(width: 8, height: 8)

                    Text(title)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("点击查看详情")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .opacity(0.6)
                }
                .padding(.top, -15)
            }
        }
        .padding(.vertical)
        .padding(.horizontal, 12)
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 6, x: 0, y: 2)
        .onAppear {
            if animateOnAppear {
                withAnimation(.easeInOut(duration: 0.8)) {
                    animationProgress = 1
                }
            } else {
                animationProgress = 1
            }
        }
    }

    // 图表内容
    @ViewBuilder
    private var chartContent: some View {
        if data.isEmpty {
            Text("暂无足够数据")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
        } else {
            Chart {
                ForEach(data) { point in
                    switch chartStyle {
                    case .line:
                        LineMark(
                            x: .value("日期", point.label),
                            y: .value("数值", point.value * animationProgress)
                        )
                        .foregroundStyle(color.gradient)
                        .lineStyle(StrokeStyle(lineWidth: 2))
                        .symbol {
                            Circle()
                                .fill(selectedPoint?.id == point.id ? color : .clear)
                                .frame(width: 8, height: 8)
                                .shadow(color: color.opacity(0.3), radius: 2, x: 0, y: 1)
                        }

                    case .bar:
                        BarMark(
                            x: .value("日期", point.label),
                            y: .value("数值", point.value * animationProgress)
                        )
                        .foregroundStyle(color.gradient)
                        .cornerRadius(4)

                    case .area:
                        AreaMark(
                            x: .value("日期", point.label),
                            y: .value("数值", point.value * animationProgress)
                        )
                        .foregroundStyle(color.opacity(0.2).gradient)

                        LineMark(
                            x: .value("日期", point.label),
                            y: .value("数值", point.value * animationProgress)
                        )
                        .foregroundStyle(color.gradient)
                        .lineStyle(StrokeStyle(lineWidth: 2))

                    case .point:
                        PointMark(
                            x: .value("日期", point.label),
                            y: .value("数值", point.value * animationProgress)
                        )
                        .foregroundStyle(color.gradient)
                        .symbol(.circle)
                        .symbolSize(selectedPoint?.id == point.id ? 120 : 80)
                    }
                }

                if let selected = selectedPoint {
                    RuleMark(
                        x: .value("Selected", selected.label)
                    )
                    .foregroundStyle(Color.secondary.opacity(0.3))
                    .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                    .annotation(position: .top) {
                        Text(valueFormatter(selected.value))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(4)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color(UIColor.systemBackground))
                                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            )
                    }
                }
            }
            .chartYAxis {
                if showYAxis {
                    AxisMarks(position: .leading)
                }
            }
            .modifier(ConditionalXAxisModifier(showXAxis: showXAxis))
            .chartOverlay { proxy in
                GeometryReader { geo in
                    Rectangle()
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                        .simultaneousGesture(
                            // 使用高优先级的DragGesture来检测垂直滑动
                            DragGesture(minimumDistance: 5)
                                .onChanged { value in
                                    // 如果是明显的垂直滑动，则不做任何处理，让ScrollView处理滚动
                                    let verticalDelta = abs(value.translation.height)
                                    let horizontalDelta = abs(value.translation.width)

                                    // 如果垂直滑动明显大于水平滑动，则直接返回，不触发任何图表交互
                                    guard verticalDelta <= horizontalDelta * 1.5 else { return }

                                    // 只有当水平滑动明显大于垂直滑动时，才处理图表交互
                                    if horizontalDelta > 10 && horizontalDelta > verticalDelta * 1.5 {
                                        deactivationTask?.cancel()

                                        let xPosition = value.location.x
                                        guard let dateValue = proxy.value(atX: xPosition, as: String.self) else { return }
                                        if let matchPoint = data.first(where: { $0.label == dateValue }) {
                                            if selectedPoint == nil {
                                                withAnimation(.easeInOut(duration: 0.618)) {
                                                    chartInteractionScale = 0.95
                                                }
                                            }
                                            selectedPoint = matchPoint
                                        }
                                    }
                                }
                                .onEnded { value in
                                    // 只有当结束时是水平滑动，才处理取消选择
                                    let verticalDelta = abs(value.translation.height)
                                    let horizontalDelta = abs(value.translation.width)

                                    // 如果是垂直滑动结束，则不处理
                                    guard verticalDelta <= horizontalDelta * 1.5 else { return }

                                    deactivationTask?.cancel()

                                    let task = DispatchWorkItem {
                                        withAnimation(.easeInOut(duration: 0.618)) {
                                            selectedPoint = nil
                                            chartInteractionScale = 1.0
                                        }
                                    }
                                    deactivationTask = task
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 1, execute: task)
                                }
                        )
                }
            }
        }
    }
}

// 新增的 ViewModifier 用于条件化 X 轴
struct ConditionalXAxisModifier: ViewModifier {
    let showXAxis: Bool

    func body(content: Content) -> some View {
        if showXAxis {
            content
                .chartXAxis {
                    AxisMarks(values: .automatic) { value in
                        if let labelString = value.as(String.self) {
                            AxisValueLabel {
                                Text(labelString)
                                    .font(.caption2)
                                    .fixedSize(horizontal: true, vertical: false)
                                    .rotationEffect(.degrees(-45))
                                    .offset(x: -10, y: 5)
                            }
                        }
                        AxisTick()
                        AxisGridLine()
                    }
                }
                .padding(.trailing, 10)
        } else {
            content
                .chartXAxis(.hidden)
        }
    }
}

// 图表预览
struct ChartView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ChartView(
                data: [
                    ChartPoint(date: Date().addingTimeInterval(-5*24*3600), label: "5月1日", value: 10),
                    ChartPoint(date: Date().addingTimeInterval(-4*24*3600), label: "5月2日", value: 30),
                    ChartPoint(date: Date().addingTimeInterval(-3*24*3600), label: "5月3日", value: 20),
                    ChartPoint(date: Date().addingTimeInterval(-2*24*3600), label: "5月4日", value: 40),
                    ChartPoint(date: Date().addingTimeInterval(-1*24*3600), label: "5月5日", value: 25),
                    ChartPoint(date: Date(), label: "5月6日", value: 50)
                ],
                title: "成本效益趋势",
                subtitle: "单次使用成本随时间变化",
                chartStyle: .line,
                color: .green
            )

            ChartView(
                data: [
                    ChartPoint(date: Date().addingTimeInterval(-5*24*3600), label: "5月1日", value: 5),
                    ChartPoint(date: Date().addingTimeInterval(-4*24*3600), label: "5月2日", value: 10),
                    ChartPoint(date: Date().addingTimeInterval(-3*24*3600), label: "5月3日", value: 8),
                    ChartPoint(date: Date().addingTimeInterval(-2*24*3600), label: "5月4日", value: 12),
                    ChartPoint(date: Date().addingTimeInterval(-1*24*3600), label: "5月5日", value: 6),
                    ChartPoint(date: Date(), label: "5月6日", value: 15)
                ],
                title: "使用率趋势",
                subtitle: "产品使用次数随时间变化",
                chartStyle: .bar,
                color: .blue
            )

            ChartView(
                data: [
                    ChartPoint(date: Date().addingTimeInterval(-5*24*3600), label: "5月1日", value: 3.5),
                    ChartPoint(date: Date().addingTimeInterval(-4*24*3600), label: "5月2日", value: 4.2),
                    ChartPoint(date: Date().addingTimeInterval(-3*24*3600), label: "5月3日", value: 3.8),
                    ChartPoint(date: Date().addingTimeInterval(-2*24*3600), label: "5月4日", value: 4.5),
                    ChartPoint(date: Date().addingTimeInterval(-1*24*3600), label: "5月5日", value: 4.0),
                    ChartPoint(date: Date(), label: "5月6日", value: 4.8)
                ],
                title: "满意度趋势",
                subtitle: "产品满意度评分随时间变化",
                chartStyle: .area,
                color: .orange
            )
        }
        .padding()
        .background(Color(UIColor.systemGroupedBackground))
    }
}