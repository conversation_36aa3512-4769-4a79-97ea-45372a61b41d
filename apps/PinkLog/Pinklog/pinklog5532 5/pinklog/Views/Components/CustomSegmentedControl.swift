import SwiftUI

struct CustomSegmentedControl<T: Hashable, Content: View>: View {
    @Binding var selection: T
    let items: [T]
    let itemContent: (T) -> Content
    var accentColor: Color = .blue
    
    init(selection: Binding<T>, items: [T], @ViewBuilder itemContent: @escaping (T) -> Content) {
        self._selection = selection
        self.items = items
        self.itemContent = itemContent
    }
    
    func accentColor(_ color: Color) -> CustomSegmentedControl {
        var view = self
        view.accentColor = color
        return view
    }
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(items.indices, id: \.self) { index in
                let item = items[index]
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.15)) {
                        selection = item
                    }
                }) {
                    itemContent(item)
                        .foregroundColor(selection == item ? .white : .primary)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 10)
                        .frame(maxWidth: .infinity)
                        .background(selection == item ? accentColor : Color.secondary.opacity(0.1))
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 2)
            }
        }
        .padding(.vertical, 4)
    }
}

// 使用枚举值的便捷扩展
extension CustomSegmentedControl where T: CaseIterable & Identifiable, T.AllCases == [T] {
    init(selection: Binding<T>, @ViewBuilder itemContent: @escaping (T) -> Content) {
        self.init(selection: selection, items: T.allCases, itemContent: itemContent)
    }
}

// 为String标签类型的便捷初始化
extension CustomSegmentedControl where T == String, Content == Text {
    init(selection: Binding<String>, items: [String]) {
        self.init(selection: selection, items: items) { item in
            Text(item)
        }
    }
}

// 预览
struct CustomSegmentedControl_Previews: PreviewProvider {
    enum Tab: String, CaseIterable, Identifiable {
        case first = "第一个"
        case second = "第二个"
        case third = "第三个"
        
        var id: String { self.rawValue }
    }
    
    static var previews: some View {
        VStack(spacing: 20) {
            CustomSegmentedControlPreview()
            CustomSegmentedControlWithIconsPreview()
        }
        .padding()
    }
    
    struct CustomSegmentedControlPreview: View {
        @State private var selection: Tab = .first
        
        var body: some View {
            VStack {
                Text("基本分段控制器")
                CustomSegmentedControl(selection: $selection) { tab in
                    Text(tab.rawValue)
                }
                .accentColor(.purple)
            }
        }
    }
    
    struct CustomSegmentedControlWithIconsPreview: View {
        @State private var selection: Tab = .first
        
        var body: some View {
            VStack {
                Text("带图标的分段控制器")
                CustomSegmentedControl(selection: $selection) { tab in
                    HStack {
                        Image(systemName: iconForTab(tab))
                        Text(tab.rawValue)
                    }
                }
                .accentColor(.green)
            }
        }
        
        func iconForTab(_ tab: Tab) -> String {
            switch tab {
            case .first: return "1.circle"
            case .second: return "2.circle"
            case .third: return "3.circle"
            }
        }
    }
} 