//
//  ProductCardPreview.swift
//  消耗型物品卡片UI重设计预览
//
//  Created by AI Designer on 2024
//

import SwiftUI
import CoreData

/// 🎨 消耗型物品卡片重设计预览
/// 展示新的优雅、简洁的设计风格
struct ProductCardPreview: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var selectedDesign = 0
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 设计说明
                    designExplanationCard
                    
                    // 设计对比
                    designComparisonSection
                    
                    // 不同库存状态的展示
                    stockStatusShowcase
                }
                .padding()
            }
            .navigationTitle("消耗品卡片重设计")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // 设计说明卡片
    private var designExplanationCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "paintbrush.pointed.fill")
                    .foregroundColor(.blue)
                Text("设计理念")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            VStack(alignment: .leading, spacing: 12) {
                designPoint("简约优雅", "库存条从12pt降至3pt，视觉更轻盈")
                designPoint("信息层次", "智能显示，仅在需要时展示状态信息")
                designPoint("现代美学", "圆角胶囊设计，渐变色彩，微妙阴影")
                designPoint("交互友好", "状态圆点、精致标签，一目了然")
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func designPoint(_ title: String, _ description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 8, height: 8)
                .padding(.top, 6)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // 设计对比部分
    private var designComparisonSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("设计对比")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 16) {
                // 新设计示例
                VStack(alignment: .leading, spacing: 8) {
                    Text("✨ 新设计 - 优雅简洁")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                    
                    ProductCard(product: createSampleProduct(stockLevel: 0.65))
                }
                
                Divider()
                
                // 设计改进说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("🎯 主要改进")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 6) {
                        improvementPoint("库存条厚度", "12pt → 3pt", "减少75%视觉重量")
                        improvementPoint("信息密度", "4行信息 → 智能2-3行", "减少视觉噪音")
                        improvementPoint("状态指示", "文字标签 → 彩色圆点", "更直观的视觉语言")
                        improvementPoint("整体风格", "功能性 → 现代优雅", "符合iOS设计语言")
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func improvementPoint(_ aspect: String, _ change: String, _ benefit: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "arrow.right.circle.fill")
                .foregroundColor(.blue)
                .font(.caption)
                .padding(.top, 1)
            
            VStack(alignment: .leading, spacing: 1) {
                HStack {
                    Text(aspect)
                        .font(.caption)
                        .fontWeight(.medium)
                    Text(change)
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                Text(benefit)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // 不同库存状态展示
    private var stockStatusShowcase: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("库存状态展示")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                statusExample("充足库存", 0.85, "健康的绿色渐变，简洁的信息显示")
                statusExample("中等库存", 0.45, "温和的黄色过渡，保持视觉平衡")
                statusExample("库存不足", 0.15, "警示的红色，智能显示预估天数")
                statusExample("即将耗尽", 0.05, "紧急状态，完整的状态信息")
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func statusExample(_ title: String, _ stockLevel: Double, _ description: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text("(\(Int(stockLevel * 100))%)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            
            ProductCard(product: createSampleProduct(stockLevel: stockLevel))
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
    }
    
    // 创建示例产品
    private func createSampleProduct(stockLevel: Double) -> Product {
        let product = Product(context: viewContext)
        product.id = UUID()
        product.name = "维生素C片"
        product.brand = "善存"
        product.model = "1000mg"
        product.price = 89.0
        product.purchaseDate = Date()
        product.isConsumable = true
        product.currentQuantity = stockLevel * 100
        product.quantity = 100
        product.unitType = "片"
        product.minStockAlert = 20
        
        // 模拟不同的生命周期状态
        if stockLevel < 0.1 {
            // 库存极低时的状态
        } else if stockLevel < 0.3 {
            // 库存较低时的状态
        }
        
        return product
    }
}

#Preview {
    ProductCardPreview()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}