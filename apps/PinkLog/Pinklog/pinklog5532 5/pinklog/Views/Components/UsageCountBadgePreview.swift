//
//  UsageCountBadgePreview.swift
//  pinklog
//
//  使用次数角标设计预览
//  展示右上角使用次数角标的设计效果
//

import SwiftUI

struct UsageCountBadgePreview: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 设计理念说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎯 使用次数角标设计")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("将使用次数以角标形式显示在卡片右上角，简化主要信息区域，提升视觉层次")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 设计对比展示
                VStack(alignment: .leading, spacing: 16) {
                    Text("📊 设计演进对比")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    // 第一版：独立行显示
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第一版：独立行显示")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                        
                        Text("❌ 占用过多垂直空间\n❌ 信息层次不够清晰\n❌ 视觉重点分散")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 第二版：与值度指数同行
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第二版：与值度指数同行")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                        
                        Text("⚠️ 信息过于拥挤\n⚠️ 阅读体验不佳\n⚠️ 重要信息竞争注意力")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 第三版：物品名下方
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第三版：物品名下方")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                        
                        Text("✅ 符合阅读习惯\n⚠️ 仍占用主要信息区域\n⚠️ 次要信息过于突出")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 第四版：右上角角标
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第四版：右上角角标（当前）")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                        
                        Text("✅ 节省主要信息区域\n✅ 清晰的视觉层次\n✅ 符合角标设计规范\n✅ 保持信息可见性")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                
                // 角标设计原理
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎨 角标设计原理")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 8, height: 8)
                            Text("位置选择：右上角是传统角标位置，用户习惯在此查找次要信息")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 8, height: 8)
                            Text("视觉设计：深色背景确保在各种产品图片上都有良好对比度")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 8, height: 8)
                            Text("尺寸控制：24pt圆形提供足够空间显示数字，同时不过于突兀")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.purple)
                                .frame(width: 8, height: 8)
                            Text("字体选择：圆角设计字体与圆形背景协调，粗体确保可读性")
                                .font(.caption)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 设计优势总结
                VStack(alignment: .leading, spacing: 12) {
                    Text("🌟 设计优势")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(spacing: 8) {
                        DesignAdvantageRow(icon: "🎯", title: "聚焦主要信息", description: "价格和值度指数更加突出")
                        DesignAdvantageRow(icon: "📱", title: "移动端友好", description: "节省宝贵的垂直空间")
                        DesignAdvantageRow(icon: "👁️", title: "视觉层次清晰", description: "主要信息与次要信息分离")
                        DesignAdvantageRow(icon: "🔄", title: "符合用户习惯", description: "角标是通用的次要信息展示方式")
                        DesignAdvantageRow(icon: "🎨", title: "美观简洁", description: "减少视觉噪音，提升整体美感")
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            }
            .padding()
        }
        .navigationTitle("使用次数角标设计")
        .navigationBarTitleDisplayMode(.large)
    }
}

struct DesignAdvantageRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(icon)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    NavigationView {
        UsageCountBadgePreview()
    }
}