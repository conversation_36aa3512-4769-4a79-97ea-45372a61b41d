import SwiftUI
import CoreData

/// 多产品选择器 - 支持多选的产品选择界面
struct MultiProductPickerView: View {
    @Binding var selectedProducts: Set<Product>
    
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var searchText = ""
    @State private var selectedCategories: Set<Category> = []
    @State private var selectedTags: Set<Tag> = []
    @State private var virtualProductFilter: VirtualProductFilter = .all
    @State private var sortOption: SortOption = .name
    @State private var showingFilters = false
    
    enum VirtualProductFilter: String, CaseIterable {
        case all = "全部"
        case virtual = "仅虚拟商品"
        case physical = "仅实体商品"
        
        var icon: String {
            switch self {
            case .all: return "square.grid.2x2"
            case .virtual: return "cloud"
            case .physical: return "cube.box"
            }
        }
    }
    
    enum SortOption: String, CaseIterable {
        case name = "名称"
        case price = "价格"
        case date = "日期"
        case usage = "使用次数"
        
        var icon: String {
            switch self {
            case .name: return "textformat"
            case .price: return "dollarsign"
            case .date: return "calendar"
            case .usage: return "chart.bar"
            }
        }
    }
    
    private var filteredProducts: [Product] {
        var products = productViewModel.products
        
        // 搜索过滤
        if !searchText.isEmpty {
            products = products.filter { product in
                (product.name ?? "").localizedCaseInsensitiveContains(searchText) ||
                (product.brand ?? "").localizedCaseInsensitiveContains(searchText) ||
                (product.model ?? "").localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 分类过滤
        if !selectedCategories.isEmpty {
            products = products.filter { product in
                guard let category = product.category else { return false }
                return selectedCategories.contains(category)
            }
        }
        
        // 标签过滤
        if !selectedTags.isEmpty {
            products = products.filter { product in
                guard let productTags = product.tags?.allObjects as? [Tag] else { return false }
                return selectedTags.isSubset(of: Set(productTags))
            }
        }
        
        // 虚拟商品过滤
        switch virtualProductFilter {
        case .all:
            break // 不做筛选
        case .virtual:
            products = products.filter { $0.isVirtualProduct }
        case .physical:
            products = products.filter { !$0.isVirtualProduct }
        }
        
        // 排序
        switch sortOption {
        case .name:
            products = products.sorted { ($0.name ?? "") < ($1.name ?? "") }
        case .price:
            products = products.sorted { $0.price > $1.price }
        case .date:
            products = products.sorted { ($0.purchaseDate ?? Date()) > ($1.purchaseDate ?? Date()) }
        case .usage:
            products = products.sorted { ($0.usageRecords?.count ?? 0) > ($1.usageRecords?.count ?? 0) }
        }
        
        return products
    }
    
    private var categories: [Category] {
        // 获取所有分类
        let allCategories = productViewModel.products.compactMap { $0.category }
        return Array(Set(allCategories)).sorted { ($0.name ?? "") < ($1.name ?? "") }
    }
    
    private var availableTags: [Tag] {
        // 获取产品中使用的所有标签
        var allTags = Set<Tag>()
        for product in productViewModel.products {
            if let productTags = product.tags?.allObjects as? [Tag] {
                allTags.formUnion(productTags)
            }
        }
        return Array(allTags).sorted { ($0.name ?? "") < ($1.name ?? "") }
    }
    
    private var hasActiveFilters: Bool {
        return !selectedCategories.isEmpty || 
               !selectedTags.isEmpty || 
               virtualProductFilter != .all
    }
    
    private func clearAllFilters() {
        selectedCategories.removeAll()
        selectedTags.removeAll()
        virtualProductFilter = .all
    }
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 紧凑的顶部工具栏
                    compactTopToolbar
                    
                    // 筛选器面板（浮动覆盖）
                    ZStack(alignment: .top) {
                        // 主要产品列表区域
                        productListSection
                        
                        // 筛选面板（浮动）
                        if showingFilters {
                            Color.black.opacity(0.3)
                                .ignoresSafeArea()
                                .onTapGesture {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        showingFilters = false
                                    }
                                }
                            
                            VStack {
                                filterOptionsSection
                                    .background(themeManager.currentTheme.backgroundColor)
                                    .cornerRadius(16)
                                    .shadow(radius: 10)
                                    .padding(.horizontal, 16)
                                    .padding(.top, 8)
                                
                                Spacer()
                            }
                            .transition(.move(edge: .top).combined(with: .opacity))
                        }
                    }
                }
            }
            .navigationTitle("选择产品")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(selectedProducts.count < 2)
                }
            }
        }
        .onAppear {
            tagViewModel.loadTags()
        }
    }
    
    // MARK: - 紧凑顶部工具栏
    
    private var compactTopToolbar: some View {
        VStack(spacing: 8) {
            // 搜索和控制行
            HStack(spacing: 8) {
                // 搜索框（占大部分空间）
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                        .font(.system(size: 14))
                    
                    TextField("搜索产品...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.system(size: 15))
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                                .font(.system(size: 14))
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(themeManager.currentTheme.cardBackgroundColor)
                .cornerRadius(8)
                
                // 排序按钮
                Menu {
                    ForEach(SortOption.allCases, id: \.self) { option in
                        Button(action: { sortOption = option }) {
                            HStack {
                                Image(systemName: option.icon)
                                Text(option.rawValue)
                                if sortOption == option {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    Image(systemName: sortOption.icon)
                        .font(.system(size: 16))
                        .foregroundColor(themeManager.currentTheme.accentColor)
                        .frame(width: 32, height: 32)
                        .background(themeManager.currentTheme.accentColor.opacity(0.1))
                        .cornerRadius(8)
                }
                
                // 筛选按钮
                Button(action: { 
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingFilters.toggle()
                    }
                }) {
                    ZStack {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.system(size: 16))
                            .foregroundColor(
                                showingFilters ? .white : themeManager.currentTheme.accentColor
                            )
                        
                        if hasActiveFilters && !showingFilters {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 6, height: 6)
                                .offset(x: 8, y: -8)
                        }
                    }
                    .frame(width: 32, height: 32)
                    .background(
                        showingFilters ? 
                            themeManager.currentTheme.accentColor : 
                            themeManager.currentTheme.accentColor.opacity(0.1)
                    )
                    .cornerRadius(8)
                }
            }
            
            // 已选产品紧凑显示
            if !selectedProducts.isEmpty {
                compactSelectedProductsBar
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(themeManager.currentTheme.backgroundColor)
    }
    
    // MARK: - 筛选选项区域
    
    private var filterOptionsSection: some View {
        VStack(spacing: 12) {
            // 顶部控制栏
            HStack {
                Text("筛选条件")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.textColor)
                
                Spacer()
                
                Button("清空") {
                    clearAllFilters()
                }
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.accentColor)
                .disabled(!hasActiveFilters)
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingFilters = false
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                }
            }
            
            Divider()
            
            // 类别筛选（紧凑版）
            compactCategoryFilterSection
            
            // 标签筛选（紧凑版）
            compactTagFilterSection
            
            // 虚拟商品筛选（紧凑版）
            compactVirtualProductFilterSection
        }
        .padding(16)
    }
    
    private var compactCategoryFilterSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("分类")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(themeManager.currentTheme.textColor)
                
                Spacer()
                
                if !selectedCategories.isEmpty {
                    Text("\(selectedCategories.count)/\(categories.count)")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                }
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(categories.prefix(6), id: \.objectID) { category in
                    Button(action: {
                        if selectedCategories.contains(category) {
                            selectedCategories.remove(category)
                        } else {
                            selectedCategories.insert(category)
                        }
                    }) {
                        VStack(spacing: 4) {
                            Image(systemName: category.icon ?? "tag")
                                .font(.system(size: 16))
                            Text(category.name?.prefix(4) ?? "未知")
                                .font(.caption2)
                                .lineLimit(1)
                        }
                        .frame(height: 44)
                        .frame(maxWidth: .infinity)
                        .background(
                            selectedCategories.contains(category) ?
                                themeManager.currentTheme.accentColor :
                                themeManager.currentTheme.cardBackgroundColor
                        )
                        .foregroundColor(
                            selectedCategories.contains(category) ?
                                .white :
                                themeManager.currentTheme.textColor
                        )
                        .cornerRadius(8)
                    }
                }
            }
            
            if categories.count > 6 {
                Button("查看全部 \(categories.count) 个分类") {
                    // 可以展开显示所有分类
                }
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.accentColor)
            }
        }
    }
    
    private var compactTagFilterSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("标签")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(themeManager.currentTheme.textColor)
                
                Spacer()
                
                if !selectedTags.isEmpty {
                    Text("\(selectedTags.count)/\(availableTags.count)")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                }
            }
            
            if availableTags.isEmpty {
                Text("暂无可用标签")
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor)
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 6) {
                    ForEach(availableTags.prefix(8), id: \.objectID) { tag in
                        Button(action: {
                            if selectedTags.contains(tag) {
                                selectedTags.remove(tag)
                            } else {
                                selectedTags.insert(tag)
                            }
                        }) {
                            HStack(spacing: 4) {
                                Circle()
                                    .fill(tagViewModel.getTagColor(tag))
                                    .frame(width: 6, height: 6)
                                Text(tag.name?.prefix(6) ?? "未知")
                                    .font(.caption2)
                                    .lineLimit(1)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 6)
                            .background(
                                selectedTags.contains(tag) ?
                                    tagViewModel.getTagColor(tag).opacity(0.8) :
                                    themeManager.currentTheme.cardBackgroundColor
                            )
                            .foregroundColor(
                                selectedTags.contains(tag) ?
                                    .white :
                                    themeManager.currentTheme.textColor
                            )
                            .cornerRadius(6)
                        }
                    }
                }
                
                if availableTags.count > 8 {
                    Button("查看全部 \(availableTags.count) 个标签") {
                        // 可以展开显示所有标签
                    }
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.accentColor)
                }
            }
        }
    }
    
    private var compactVirtualProductFilterSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("商品类型")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(themeManager.currentTheme.textColor)
            
            HStack(spacing: 8) {
                ForEach(VirtualProductFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        virtualProductFilter = filter
                    }) {
                        VStack(spacing: 4) {
                            Image(systemName: filter.icon)
                                .font(.system(size: 14))
                            Text(filter.rawValue)
                                .font(.caption2)
                                .lineLimit(1)
                        }
                        .frame(height: 40)
                        .frame(maxWidth: .infinity)
                        .background(
                            virtualProductFilter == filter ?
                                themeManager.currentTheme.accentColor :
                                themeManager.currentTheme.cardBackgroundColor
                        )
                        .foregroundColor(
                            virtualProductFilter == filter ?
                                .white :
                                themeManager.currentTheme.textColor
                        )
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    
    // MARK: - 紧凑已选产品栏
    
    private var compactSelectedProductsBar: some View {
        HStack(spacing: 8) {
            Text("已选 \(selectedProducts.count)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(themeManager.currentTheme.accentColor)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 4) {
                    ForEach(Array(selectedProducts).prefix(5), id: \.objectID) { product in
                        Text(product.name?.prefix(8) ?? "未知")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(themeManager.currentTheme.accentColor.opacity(0.2))
                            .foregroundColor(themeManager.currentTheme.accentColor)
                            .cornerRadius(4)
                    }
                    
                    if selectedProducts.count > 5 {
                        Text("+\(selectedProducts.count - 5)")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(themeManager.currentTheme.secondaryTextColor.opacity(0.2))
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                            .cornerRadius(4)
                    }
                }
            }
            
            Spacer()
            
            Button("清空") {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedProducts.removeAll()
                }
            }
            .font(.caption)
            .foregroundColor(themeManager.currentTheme.secondaryTextColor)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(themeManager.currentTheme.cardBackgroundColor)
        .cornerRadius(8)
    }
    
    
    // MARK: - 产品列表区域
    
    private var productListSection: some View {
        ScrollView {
            LazyVStack(spacing: 6) {
                ForEach(filteredProducts, id: \.objectID) { product in
                    CompactProductSelectionRow(
                        product: product,
                        isSelected: selectedProducts.contains(product),
                        onToggle: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                if selectedProducts.contains(product) {
                                    selectedProducts.remove(product)
                                } else {
                                    selectedProducts.insert(product)
                                }
                            }
                        }
                    )
                }
                
                if filteredProducts.isEmpty {
                    EmptyProductListView(searchText: searchText)
                        .padding(.top, 40)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
    }
}


// MARK: - 紧凑产品选择行
struct CompactProductSelectionRow: View {
    let product: Product
    let isSelected: Bool
    let onToggle: () -> Void
    
    @EnvironmentObject var themeManager: ThemeManager
    
    private var satisfactionText: String {
        let satisfaction = product.averageSatisfaction
        if satisfaction.isNaN || satisfaction.isInfinite {
            return "0.0"
        }
        return String(format: "%.1f", satisfaction)
    }
    
    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: 12) {
                // 选择指示器（紧凑）
                ZStack {
                    Circle()
                        .stroke(
                            isSelected ? themeManager.currentTheme.accentColor : Color.gray.opacity(0.5),
                            lineWidth: 1.5
                        )
                        .frame(width: 20, height: 20)
                    
                    if isSelected {
                        Circle()
                            .fill(themeManager.currentTheme.accentColor)
                            .frame(width: 14, height: 14)
                        
                        Image(systemName: "checkmark")
                            .font(.system(size: 8, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                
                // 产品信息（紧凑布局）
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(product.name ?? "未知产品")
                            .font(.system(size: 15, weight: .medium))
                            .foregroundColor(themeManager.currentTheme.textColor)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text("¥\(Int(product.price))")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(themeManager.currentTheme.accentColor)
                    }
                    
                    HStack {
                        // 分类信息
                        HStack(spacing: 4) {
                            Image(systemName: product.category?.icon ?? "tag")
                                .font(.system(size: 10))
                                .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                            Text(product.category?.name ?? "未分类")
                                .font(.system(size: 12))
                                .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                        }
                        
                        Spacer()
                        
                        // 紧凑统计信息
                        HStack(spacing: 8) {
                            HStack(spacing: 2) {
                                Image(systemName: "chart.bar.fill")
                                    .font(.system(size: 9))
                                    .foregroundColor(.blue)
                                Text("\(product.usageRecords?.count ?? 0)")
                                    .font(.system(size: 10))
                                    .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                            }
                            
                            HStack(spacing: 2) {
                                Image(systemName: "heart.fill")
                                    .font(.system(size: 9))
                                    .foregroundColor(.pink)
                                Text(satisfactionText)
                                    .font(.system(size: 10))
                                    .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ? 
                    themeManager.currentTheme.accentColor.opacity(0.08) : 
                    themeManager.currentTheme.cardBackgroundColor
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        isSelected ? themeManager.currentTheme.accentColor.opacity(0.6) : Color.clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 紧凑空列表视图
struct EmptyProductListView: View {
    let searchText: String
    
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: searchText.isEmpty ? "cube.box" : "magnifyingglass")
                .font(.system(size: 32))
                .foregroundColor(themeManager.currentTheme.secondaryTextColor)
            
            VStack(spacing: 4) {
                Text(searchText.isEmpty ? "暂无产品" : "未找到匹配产品")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(themeManager.currentTheme.textColor)
                
                Text(searchText.isEmpty ? 
                     "请先添加产品" : 
                     "尝试调整筛选条件")
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 32)
    }
}


