import SwiftUI

/// 统一的分析卡片组件
/// 复用自旧系统的设计，提供一致的视觉风格
struct AnalyticsCard<Content: View>: View {
    let title: String
    let subtitle: String?
    let height: CGFloat?
    let content: () -> Content
    
    init(
        title: String,
        subtitle: String? = nil,
        height: CGFloat? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.height = height
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 头部区域
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(AnalyticsCardTheme.titleColor)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(AnalyticsCardTheme.subtitleColor)
                }
            }
            .padding(.horizontal)
            
            // 内容区域
            content()
                .if(height != nil) { view in
                    view.frame(height: height)
                }
        }
        .padding(.vertical)
        .background(AnalyticsCardTheme.cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        .shadow(color: AnalyticsCardTheme.cardShadow, radius: 10, x: 0, y: 5)
        .padding(.horizontal)
    }
}

/// 分析卡片主题配置
struct AnalyticsCardTheme {
    static let titleColor = Color.primary
    static let subtitleColor = Color.secondary
    static let cardBackground = Color(UIColor.secondarySystemBackground)
    static let cardShadow = Color.black.opacity(0.1)
    static let chartGradient = LinearGradient(
        gradient: Gradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.6)]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    static let primary = Color.primary
}

/// 图表占位符组件
struct ChartPlaceholder: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "chart.bar.fill")
                .font(.system(size: 40))
                .foregroundColor(.gray.opacity(0.5))
            
            Text("正在加载图表...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(height: 150)
        .frame(maxWidth: .infinity)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
        .padding()
    }
}

/// View扩展，用于条件修饰符
extension View {
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

#Preview {
    AnalyticsCard(
        title: "示例图表",
        subtitle: "这是一个示例副标题"
    ) {
        VStack {
            Text("图表内容区域")
                .font(.title2)
                .foregroundColor(.blue)
            
            Rectangle()
                .fill(Color.blue.opacity(0.3))
                .frame(height: 100)
                .cornerRadius(8)
        }
        .padding()
    }
    .padding()
}
