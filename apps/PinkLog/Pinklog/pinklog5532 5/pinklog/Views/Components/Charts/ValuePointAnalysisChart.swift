import SwiftUI

// MARK: - 主视图
struct ValuePointAnalysisChart: View {
    let product: Product
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPoint: ValuePointData?
    @State private var valuePoint: ValuePointData?
    @State private var animationProgress: CGFloat = 0
    
    @State private var chartData: [ValuePointData] = []
    @State private var analysis: ValuePointCalculator.ValuePointAnalysis?
    @State private var unlockedMilestones: [Milestone] = []
    @State private var lastSelectedIndex: Int = -1 // 用于触觉反馈
    @State private var showInteractionHint: Bool = true // 显示交互提示

    // Y轴标签的宽度
    private let yAxisLabelWidth: CGFloat = 40

    // 触觉反馈
    private let impactFeedback = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        ScrollView {
            AnalyticsCard(
                title: "值点分析",
                subtitle: "价值认知、使用粘性与情感投入的动态分析",
                height: nil
            ) {
                VStack(alignment: .leading, spacing: 16) {
                    // 选中点详情（如果有）
                    if let selectedPoint = selectedPoint {
                        selectedPointDetails(selectedPoint)
                    }

                    // 值点指示器（如果有）
                    if let valuePoint = valuePoint {
                        HStack {
                            Spacer()
                            valuePointIndicator(valuePoint)
                        }
                    }

                    // 主图表区域
                    chartSection.frame(height: 280)

                    // 图例和交互提示
                    VStack(spacing: 8) {
                        legendSection

                        // 交互提示
                        if selectedPoint == nil && !chartData.isEmpty && showInteractionHint {
                            HStack {
                                Image(systemName: "hand.draw")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                                    .scaleEffect(showInteractionHint ? 1.1 : 1.0)
                                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: showInteractionHint)

                                Text("在图表上左右滑动查看不同时间点的详细数据")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Button("知道了") {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        showInteractionHint = false
                                    }
                                }
                                .font(.caption)
                                .foregroundColor(.blue)
                            }
                            .padding(.horizontal)
                            .padding(.vertical, 8)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                            .padding(.horizontal)
                            .transition(.asymmetric(
                                insertion: .scale.combined(with: .opacity),
                                removal: .scale.combined(with: .opacity)
                            ))
                        }
                    }

                    // 统一的分析结论（与物品详情页一致）
                    unifiedAnalysisConclusionSection

                    // 分析详情
                    if let analysis = analysis {
                        enhancedValuePointDetailsSection(analysis)
                    }

                    // 里程碑时间线
                    if !unlockedMilestones.isEmpty {
                        milestoneTimelineView
                    }
                }
            }
        }
        .navigationTitle("详细分析")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("完成") {
                    dismiss()
                }
            }
        }
        .onAppear {
            calculateChartData()
            self.chartData.sort { $0.date < $1.date }
            self.unlockedMilestones = MilestoneCalculator.calculateUnlockedMilestones(for: product)
            // 验证数据一致性
            validateDataConsistency()
            withAnimation(.easeInOut(duration: 1.2)) {
                animationProgress = 1
            }
        }
        .onKeyPress(.leftArrow) {
            navigateDataPoint(direction: -1)
            return .handled
        }
        .onKeyPress(.rightArrow) {
            navigateDataPoint(direction: 1)
            return .handled
        }
    }
    

    
    private func valuePointIndicator(_ valuePoint: ValuePointData) -> some View {
        HStack(spacing: 8) {
            Image(systemName: "target")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.orange)

            VStack(alignment: .leading, spacing: 2) {
                Text("值点")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.orange)

                Text(valuePoint.label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            Capsule()
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    Capsule()
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: Color.orange.opacity(0.2), radius: 2, x: 0, y: 1)
    }
    
    private func selectedPointDetails(_ selectedPoint: ValuePointData) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和时间
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("数据点详情")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(selectedPoint.label)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 关闭按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.selectedPoint = nil
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.title3)
                }
            }

            // 数据点信息
            HStack(spacing: 12) {
                enhancedDetailPill(
                    icon: "dollarsign.circle",
                    title: product.valuationMethod == "daily" ? "单日成本" : "单次成本",
                    value: String(format: "¥%.1f", selectedPoint.costEffectiveness),
                    color: .green
                )
                enhancedDetailPill(
                    icon: "number.circle",
                    title: product.valuationMethod == "daily" ? "累计天数" : "累计次数",
                    value: "\(Int(selectedPoint.usageRate))\(product.valuationMethod == "daily" ? "天" : "次")",
                    color: .blue
                )
                enhancedDetailPill(
                    icon: "star.fill",
                    title: "满意度",
                    value: String(format: "%.1f/5", selectedPoint.satisfaction),
                    color: .purple
                )
                Spacer()
            }
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 3)
        .transition(.asymmetric(
            insertion: .scale(scale: 0.8).combined(with: .opacity),
            removal: .scale(scale: 0.9).combined(with: .opacity)
        ))
    }

    private func detailPill(title: String, value: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(title).font(.caption2).foregroundColor(color)
            Text(value).font(.caption).fontWeight(.semibold)
        }
    }

    private func enhancedDetailPill(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(alignment: .center, spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)

            VStack(spacing: 2) {
                Text(value)
                    .font(.system(.caption, design: .rounded).bold())
                    .foregroundColor(color)

                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(minWidth: 60)
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - 图表区域 (手动绘制)
    private var chartSection: some View {
        Group {
            if chartData.count > 1 {
                HStack(spacing: 0) {
                    yAxisLabels()
                    GeometryReader { geometry in
                        let size = geometry.size
                        let points = dataToPoints(size: size)
                        
                        ZStack {
                            gridLines(size: size)
                            drawCurve(points: points.cost, color: .green, size: size)
                            drawCurve(points: points.usage, color: .blue, size: size)
                            drawCurve(points: points.satisfaction, color: .purple, size: size)
                            valuePointMark(points: points.cost, size: size)

                            // 选中点指示器
                            if let selectedPoint = selectedPoint {
                                selectedPointIndicator(selectedPoint: selectedPoint, points: points, size: size)
                            }

                            interactionOverlay(size: size)
                        }
                    }
                }
                .padding(.bottom, 40)
                .overlay(alignment: .bottom) {
                    xAxisLabels()
                }
            } else {
                singleDataPointView()
            }
        }
    }
    
    // MARK: - 手动绘制辅助视图
    private func yAxisLabels() -> some View {
        VStack {
            Text("高").font(.caption2)
            Spacer()
            Text("中").font(.caption2)
            Spacer()
            Text("低").font(.caption2)
        }
        .frame(width: yAxisLabelWidth)
        .padding(.bottom, 40)
    }
    
    private func xAxisLabels() -> some View {
        HStack {
            if chartData.count > 1 {
                Text(chartData.first?.label ?? "")
                Spacer()
                Text(chartData[chartData.count / 2].label)
                Spacer()
                Text(chartData.last?.label ?? "")
            }
        }
        .font(.caption2)
        .padding(.horizontal)
        .padding(.leading, yAxisLabelWidth)
    }
    
    private func gridLines(size: CGSize) -> some View {
        Path { path in
            for i in 0...4 {
                let y = size.height / 4 * CGFloat(i)
                path.move(to: CGPoint(x: 0, y: y))
                path.addLine(to: CGPoint(x: size.width, y: y))
            }
            if chartData.count > 1 {
                let step = size.width / CGFloat(chartData.count - 1)
                for i in 0..<chartData.count {
                    let x = step * CGFloat(i)
                    path.move(to: CGPoint(x: x, y: 0))
                    path.addLine(to: CGPoint(x: x, y: size.height))
                }
            }
        }
        .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
    }
    
    @ViewBuilder
    private func drawCurve(points: [CGPoint], color: Color, size: CGSize) -> some View {
        if !points.isEmpty {
            let smoothPath = createSmoothPath(points: points)
            let closedPath = createSmoothPath(points: points, closed: true, size: size)

            // 渐变填充区域 - 使用更丰富的渐变效果
            closedPath
                .fill(LinearGradient(
                    gradient: Gradient(colors: [
                        color.opacity(0.4),
                        color.opacity(0.2),
                        color.opacity(0.05),
                        .clear
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .scaleEffect(y: animationProgress)

            // 主曲线 - 与旧系统保持一致的线条粗细
            smoothPath
                .trim(from: 0, to: animationProgress)
                .stroke(color, style: StrokeStyle(lineWidth: 2.0, lineCap: .round, lineJoin: .round))
                .shadow(color: color.opacity(0.3), radius: 2, x: 0, y: 1)

            // 数据点标记 - 与旧系统保持一致的大小
            ForEach(Array(points.enumerated()), id: \.offset) { index, point in
                Circle()
                    .fill(color)
                    .frame(width: 5, height: 5)
                    .position(point)
                    .scaleEffect(animationProgress)
                    .shadow(color: color.opacity(0.5), radius: 1, x: 0, y: 1)
            }
        }
    }

    @ViewBuilder
    private func valuePointMark(points: [CGPoint], size: CGSize) -> some View {
        if let valuePoint = valuePoint, let vpIndex = chartData.firstIndex(where: { $0.id == valuePoint.id }), vpIndex < points.count {
            let point = points[vpIndex]

            // 垂直参考线 - 与旧系统保持一致的线条粗细
            Path { path in
                path.move(to: CGPoint(x: point.x, y: 0))
                path.addLine(to: CGPoint(x: point.x, y: size.height))
            }
            .stroke(Color.orange.opacity(0.6), style: StrokeStyle(lineWidth: 1.0, dash: [5, 5]))

            // 值点标记 - 增强视觉效果
            ZStack {
                // 外圈光晕
                Circle()
                    .fill(Color.orange.opacity(0.3))
                    .frame(width: 20, height: 20)
                    .scaleEffect(animationProgress)

                // 内圈标记
                Image(systemName: "target")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.orange)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .frame(width: 16, height: 16)
                            .shadow(color: Color.orange.opacity(0.5), radius: 2, x: 0, y: 1)
                    )
            }
            .position(point)
            .scaleEffect(animationProgress)
        }
    }
    
    private func interactionOverlay(size: CGSize) -> some View {
        Rectangle()
            .fill(Color.clear)
            .contentShape(Rectangle())
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        guard chartData.count > 1 else { return }
                        let location = value.location

                        // 计算最近的数据点索引
                        let x = location.x
                        let normalizedX = max(0, min(1, x / size.width))
                        let index = Int(round(normalizedX * CGFloat(chartData.count - 1)))

                        if index >= 0 && index < chartData.count && index != lastSelectedIndex {
                            // 隐藏交互提示
                            if showInteractionHint {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showInteractionHint = false
                                }
                            }

                            // 触觉反馈
                            impactFeedback.impactOccurred()
                            lastSelectedIndex = index

                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedPoint = chartData[index]
                            }
                        }
                    }
                    .onEnded { _ in
                        // 延迟隐藏选中点详情
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedPoint = nil
                                lastSelectedIndex = -1
                            }
                        }
                    }
            )
            .onAppear {
                // 准备触觉反馈
                impactFeedback.prepare()
            }
    }
    
    private func singleDataPointView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.system(size: 40))
                .foregroundColor(.secondary)
            
            Text("数据不足，无法生成趋势图")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("记录两次或以上的使用情况，即可查看完整的分析图表，追踪物品的价值变化。")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if let firstDataPoint = chartData.first {
                Divider().padding(.vertical, 8)
                
                HStack(spacing: 20) {
                    VStack {
                        Text("首次成本")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "¥%.1f", firstDataPoint.costEffectiveness))
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    VStack {
                        Text("首次满意度")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(String(format: "%.1f/5", firstDataPoint.satisfaction))
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.purple)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
    }
    
    // MARK: - 选中点指示器
    private func selectedPointIndicator(selectedPoint: ValuePointData, points: (cost: [CGPoint], usage: [CGPoint], satisfaction: [CGPoint]), size: CGSize) -> some View {
        Group {
            if let selectedIndex = chartData.firstIndex(where: { $0.id == selectedPoint.id }) {
                let stepX = size.width / CGFloat(chartData.count - 1)
                let xPosition = stepX * CGFloat(selectedIndex)

                // 垂直指示线
                Path { path in
                    path.move(to: CGPoint(x: xPosition, y: 0))
                    path.addLine(to: CGPoint(x: xPosition, y: size.height))
                }
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.primary.opacity(0.6), Color.primary.opacity(0.2)]),
                        startPoint: .top,
                        endPoint: .bottom
                    ),
                    style: StrokeStyle(lineWidth: 2, lineCap: .round)
                )
                .shadow(color: Color.primary.opacity(0.2), radius: 2)

                // 在三条曲线上显示选中点
                Group {
                    // 价值认知曲线点
                    if selectedIndex < points.cost.count {
                        ZStack {
                            Circle()
                                .fill(Color.green.opacity(0.2))
                                .frame(width: 16, height: 16)
                            Circle()
                                .fill(Color.green)
                                .frame(width: 10, height: 10)
                        }
                        .position(points.cost[selectedIndex])
                        .shadow(color: Color.green.opacity(0.5), radius: 4)
                    }

                    // 使用粘性曲线点
                    if selectedIndex < points.usage.count {
                        ZStack {
                            Circle()
                                .fill(Color.blue.opacity(0.2))
                                .frame(width: 16, height: 16)
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 10, height: 10)
                        }
                        .position(points.usage[selectedIndex])
                        .shadow(color: Color.blue.opacity(0.5), radius: 4)
                    }

                    // 情感投入曲线点
                    if selectedIndex < points.satisfaction.count {
                        ZStack {
                            Circle()
                                .fill(Color.purple.opacity(0.2))
                                .frame(width: 16, height: 16)
                            Circle()
                                .fill(Color.purple)
                                .frame(width: 10, height: 10)
                        }
                        .position(points.satisfaction[selectedIndex])
                        .shadow(color: Color.purple.opacity(0.5), radius: 4)
                    }
                }
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedIndex)
            }
        }
    }

    // MARK: - 数据处理和路径生成
    private func dataToPoints(size: CGSize) -> (cost: [CGPoint], usage: [CGPoint], satisfaction: [CGPoint]) {
        guard chartData.count > 1 else { return ([], [], []) }
        
        let stepX = size.width / CGFloat(chartData.count - 1)
        
        let costPoints = chartData.indices.map {
            CGPoint(x: stepX * CGFloat($0), y: size.height - CGFloat(chartData[$0].normalizedCostEffectiveness / 100) * size.height)
        }
        let usagePoints = chartData.indices.map {
            CGPoint(x: stepX * CGFloat($0), y: size.height - CGFloat(chartData[$0].normalizedUsageRate / 100) * size.height)
        }
        let satPoints = chartData.indices.map {
            CGPoint(x: stepX * CGFloat($0), y: size.height - CGFloat(chartData[$0].normalizedSatisfaction / 100) * size.height)
        }
        
        return (costPoints, usagePoints, satPoints)
    }
    
    private func createSmoothPath(points: [CGPoint], closed: Bool = false, size: CGSize = .zero) -> Path {
        var path = Path()
        guard points.count > 1 else { return path }

        if closed {
            path.move(to: CGPoint(x: points[0].x, y: size.height))
            path.addLine(to: points[0])
        } else {
            path.move(to: points[0])
        }

        // 使用Catmull-Rom样条算法，与旧系统保持一致的高平滑度
        if points.count == 2 {
            // 只有两个点时，直接连线
            path.addLine(to: points[1])
        } else if points.count == 3 {
            // 三个点时，使用简化的二次贝塞尔曲线
            let midPoint = points[1]
            let controlPoint = CGPoint(
                x: midPoint.x,
                y: midPoint.y
            )
            path.addQuadCurve(to: points[1], control: controlPoint)
            path.addQuadCurve(to: points[2], control: controlPoint)
        } else {
            // 四个或更多点时，使用完整的Catmull-Rom算法
            for i in 0..<points.count - 1 {
                let p0 = i > 0 ? points[i-1] : points[i]
                let p1 = points[i]
                let p2 = points[i+1]
                let p3 = i < points.count - 2 ? points[i+2] : points[i+1]

                // 计算Catmull-Rom控制点
                let controlPoints = calculateCatmullRomControlPoints(p0: p0, p1: p1, p2: p2, p3: p3)

                path.addCurve(
                    to: p2,
                    control1: controlPoints.control1,
                    control2: controlPoints.control2
                )
            }
        }

        if closed {
            path.addLine(to: CGPoint(x: points.last!.x, y: size.height))
            path.closeSubpath()
        }

        return path
    }

    // MARK: - Catmull-Rom平滑算法
    // 实现与旧系统相同的高质量曲线平滑效果



    private func calculateCatmullRomControlPoints(p0: CGPoint, p1: CGPoint, p2: CGPoint, p3: CGPoint) -> (control1: CGPoint, control2: CGPoint) {
        // 使用标准Catmull-Rom算法计算切线
        // 这与SwiftUI Charts的.interpolationMethod(.catmullRom)效果一致
        let alpha: CGFloat = 0.5 // 张力参数，0.5为标准Catmull-Rom

        // 计算切线向量（基于相邻点的方向）
        let tangent1X = (p2.x - p0.x) * alpha
        let tangent1Y = (p2.y - p0.y) * alpha

        let tangent2X = (p3.x - p1.x) * alpha
        let tangent2Y = (p3.y - p1.y) * alpha

        // 计算控制点（距离起点和终点1/3的位置）
        let control1 = CGPoint(
            x: p1.x + tangent1X / 3.0,
            y: p1.y + tangent1Y / 3.0
        )

        let control2 = CGPoint(
            x: p2.x - tangent2X / 3.0,
            y: p2.y - tangent2Y / 3.0
        )

        return (control1: control1, control2: control2)
    }

    // MARK: - 统一的分析结论区域（与物品详情页一致）
    private var unifiedAnalysisConclusionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("分析结论")
                .font(.subheadline)
                .fontWeight(.semibold)

            Text("根据您的使用记录和费用情况，该产品的值度指数为\(Int(product.worthItIndex()))，属于\"\(product.status.rawValue)\"。\(product.valuationMethod == "daily" ? "单日" : "单次")使用成本为¥\(Int(product.costPerUse))，平均满意度为\(product.averageSatisfaction, specifier: "%.1f")/5。")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }

    // MARK: - 增强版值点详情区域
    private func enhancedValuePointDetailsSection(_ analysis: ValuePointCalculator.ValuePointAnalysis) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "target").foregroundColor(.orange)
                Text("值点分析").font(.headline).fontWeight(.semibold)
                Spacer()
                Text(analysis.recommendationLevel.rawValue)
                    .font(.caption).fontWeight(.semibold).foregroundColor(.white)
                    .padding(.horizontal, 8).padding(.vertical, 4)
                    .background(analysis.recommendationLevel.color).cornerRadius(8)
            }
            if let valuePoint = analysis.valuePoint {
                VStack(alignment: .leading, spacing: 8) {
                    Text("值点分析基于当前数据：").font(.subheadline).foregroundColor(.secondary)
                    HStack(spacing: 20) {
                        detailPill(title: product.valuationMethod == "daily" ? "单日成本" : "单次成本", value: String(format: "¥%.1f", product.costPerUse), color: .green)
                        detailPill(title: product.valuationMethod == "daily" ? "总服役天数" : "总使用次数", value: product.valuationMethod == "daily" ?
                            "\(Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: Date()).day ?? 0)天" :
                            "\(product.totalUsageCount)次", color: .blue)
                        detailPill(title: "平均满意度", value: String(format: "%.1f/5", product.averageSatisfaction), color: .purple)
                        Spacer()
                    }
                }
            }
            Text(analysis.analysisText).font(.caption).foregroundColor(.secondary).padding(.top, 4)
            if !analysis.keyInsights.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("关键洞察").font(.subheadline).fontWeight(.semibold)
                    ForEach(Array(analysis.keyInsights.enumerated()), id: \.offset) { _, insight in
                        HStack(alignment: .top, spacing: 8) {
                            Text("•").foregroundColor(.orange).fontWeight(.bold)
                            Text(insight).font(.caption).foregroundColor(.secondary)
                        }
                    }
                }
            }
            if let futureProjection = analysis.futureProjection {
                VStack(alignment: .leading, spacing: 4) {
                    Text("趋势预测").font(.subheadline).fontWeight(.semibold)
                    Text(futureProjection)
                        .font(.caption).foregroundColor(.secondary)
                        .padding(.horizontal, 12).padding(.vertical, 8)
                        .background(Color.blue.opacity(0.1)).cornerRadius(8)
                }
            }
        }
        .padding().background(Color.orange.opacity(0.05)).cornerRadius(12)
        .overlay(RoundedRectangle(cornerRadius: 12).stroke(Color.orange.opacity(0.3), lineWidth: 1))
    }
    
    // MARK: - 图例区域
    private var legendSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("图例说明")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            HStack(spacing: 16) {
                enhancedLegendItem(color: .green, text: "使用成本", description: product.valuationMethod == "daily" ? "单日使用成本随服役时间下降" : "单次使用成本随使用次数下降")
                enhancedLegendItem(color: .blue, text: "使用粘性", description: "产品融入用户生活的程度")
                enhancedLegendItem(color: .purple, text: "情感投入", description: "用户与产品的情感连接强度")
                Spacer()
            }

            if valuePoint != nil {
                HStack(spacing: 8) {
                    Image(systemName: "target")
                        .font(.caption)
                        .foregroundColor(.orange)
                    Text("值点标记表示成本效益达到最佳平衡的关键时刻")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }

    private func legendItem(image: String? = nil, color: Color, text: String, isBold: Bool = false) -> some View {
        HStack(spacing: 4) {
            if let image = image {
                Image(systemName: image).font(.caption).foregroundColor(color)
            } else {
                Circle().fill(color).frame(width: 8, height: 8)
            }
            Text(text).font(.caption).foregroundColor(isBold ? color : .secondary).fontWeight(isBold ? .semibold : .regular)
        }
    }

    private func enhancedLegendItem(color: Color, text: String, description: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 6) {
                Circle()
                    .fill(color)
                    .frame(width: 10, height: 10)
                Text(text)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            Text(description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 数据计算
    private func calculateChartData() {
        var dataPoints: [ValuePointData] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        if product.valuationMethod == "daily" {
            guard let purchaseDate = product.purchaseDate else { return }
            let totalDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
            guard totalDays > 0 else { return }

            let interval = max(1, totalDays / 20)

            // 首先创建基础数据点
            var baseDataPoints: [ValuePointData] = []
            for day in stride(from: 1, through: totalDays, by: interval) {
                let pointDate = Calendar.current.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()
                let satisfaction = getSatisfactionAtDay(day, from: purchaseDate)
                // 使用统一的有效使用量计算方法
                let cumulativeDays = Double(day)
                let costPerDay = product.totalCostOfOwnership / max(1, cumulativeDays)

                baseDataPoints.append(ValuePointData(
                    date: pointDate,
                    label: dateFormatter.string(from: pointDate),
                    costEffectiveness: costPerDay, // 单日成本
                    usageRate: cumulativeDays, // 累计使用天数（与物品详情页一致）
                    satisfaction: satisfaction
                ))
            }

            // 使用增强指标计算真实的用户体验曲线，但保持usageRate为真实使用数据
            let enhancedMetrics = ValuePointCalculator.calculateEnhancedMetrics(dataPoints: baseDataPoints, product: product)

            for (index, enhanced) in enhancedMetrics.enumerated() {
                let basePoint = baseDataPoints[index]
                // 创建包含真实数据和图表数据的完整数据点
                var dataPoint = ValuePointData(
                    date: basePoint.date,
                    label: basePoint.label,
                    costEffectiveness: basePoint.costEffectiveness, // 真实的单日成本
                    usageRate: basePoint.usageRate, // 真实的累计使用天数
                    satisfaction: basePoint.satisfaction // 真实的满意度
                )
                // 设置图表显示专用数据
                dataPoint.chartValuePerception = basePoint.costEffectiveness // 使用真实的单日成本
                dataPoint.chartUsageStickiness = enhanced.usageStickiness * 100
                dataPoint.chartEmotionalInvestment = enhanced.emotionalInvestment * 5

                dataPoints.append(dataPoint)
            }
        } else {
            guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else { return }

            let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
            var cumulativeUsage = 0
            var satisfactionSum = 0.0

            // 首先创建基础数据点
            var baseDataPoints: [ValuePointData] = []
            for record in sortedRecords {
                cumulativeUsage += 1
                satisfactionSum += Double(record.satisfaction)
                let avgSatisfaction = satisfactionSum / Double(cumulativeUsage)
                // 对于按次计算的产品，usageRate应该是累计使用次数
                let costPerUse = product.totalCostOfOwnership / max(1, Double(cumulativeUsage))

                baseDataPoints.append(ValuePointData(
                    date: record.date ?? Date(),
                    label: dateFormatter.string(from: record.date ?? Date()),
                    costEffectiveness: costPerUse, // 单次成本
                    usageRate: Double(cumulativeUsage), // 累计使用次数
                    satisfaction: avgSatisfaction
                ))
            }

            // 使用增强指标计算真实的用户体验曲线，但保持usageRate为真实使用数据
            let enhancedMetrics = ValuePointCalculator.calculateEnhancedMetrics(dataPoints: baseDataPoints, product: product)

            for (index, enhanced) in enhancedMetrics.enumerated() {
                let basePoint = baseDataPoints[index]
                // 创建包含真实数据和图表数据的完整数据点
                var dataPoint = ValuePointData(
                    date: basePoint.date,
                    label: basePoint.label,
                    costEffectiveness: basePoint.costEffectiveness, // 真实的单次成本
                    usageRate: basePoint.usageRate, // 真实的累计使用次数
                    satisfaction: basePoint.satisfaction // 真实的满意度
                )
                // 设置图表显示专用数据
                dataPoint.chartValuePerception = basePoint.costEffectiveness // 使用真实的单次成本
                dataPoint.chartUsageStickiness = enhanced.usageStickiness * 100
                dataPoint.chartEmotionalInvestment = enhanced.emotionalInvestment * 5

                dataPoints.append(dataPoint)
            }
        }
        
        normalizeData(&dataPoints)
        let analysisResult = ValuePointCalculator.analyzeValuePoint(for: product, dataPoints: dataPoints)
        self.analysis = analysisResult
        self.valuePoint = analysisResult.valuePoint
        self.chartData = dataPoints
    }
    
    private func normalizeData(_ dataPoints: inout [ValuePointData]) {
        guard !dataPoints.isEmpty else { return }

        // 使用图表专用字段进行标准化
        let maxCost = dataPoints.map { $0.chartValuePerception }.max() ?? 1
        let minCost = dataPoints.map { $0.chartValuePerception }.min() ?? 0
        let maxUsage = dataPoints.map { $0.chartUsageStickiness }.max() ?? 1
        let maxSatisfaction = dataPoints.map { $0.chartEmotionalInvestment }.max() ?? 5.0

        for i in 0..<dataPoints.count {
            // 使用成本曲线标准化（成本越高，图表位置越高，呈现下降趋势）
            let costRange = maxCost - minCost
            if costRange > 0 {
                // 正向标准化：最高成本对应100，最低成本对应0
                dataPoints[i].normalizedCostEffectiveness = ((dataPoints[i].chartValuePerception - minCost) / costRange) * 100
            } else {
                dataPoints[i].normalizedCostEffectiveness = 50 // 如果成本相同，显示在中间位置
            }

            // 使用粘性曲线标准化
            if maxUsage > 0 {
                dataPoints[i].normalizedUsageRate = (dataPoints[i].chartUsageStickiness / maxUsage) * 100
            } else {
                dataPoints[i].normalizedUsageRate = dataPoints[i].chartUsageStickiness
            }

            // 情感投入曲线标准化
            if maxSatisfaction > 0 {
                dataPoints[i].normalizedSatisfaction = (dataPoints[i].chartEmotionalInvestment / maxSatisfaction) * 100
            } else {
                dataPoints[i].normalizedSatisfaction = dataPoints[i].chartEmotionalInvestment * 20 // 5分制转100分制
            }
        }
    }
    
    // MARK: - 键盘导航
    private func navigateDataPoint(direction: Int) {
        guard !chartData.isEmpty else { return }

        let currentIndex: Int
        if let selectedPoint = selectedPoint,
           let index = chartData.firstIndex(where: { $0.id == selectedPoint.id }) {
            currentIndex = index
        } else {
            currentIndex = chartData.count / 2 // 默认从中间开始
        }

        let newIndex = max(0, min(chartData.count - 1, currentIndex + direction))

        if newIndex != currentIndex {
            // 触觉反馈
            impactFeedback.impactOccurred()

            withAnimation(.easeInOut(duration: 0.2)) {
                selectedPoint = chartData[newIndex]
            }

            // 隐藏交互提示
            if showInteractionHint {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showInteractionHint = false
                }
            }
        }
    }

    // MARK: - 数据一致性验证
    private func validateDataConsistency() {
        #if DEBUG
        let detailPageUsageCount = product.valuationMethod == "daily" ?
            Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: Date()).day ?? 0 :
            product.totalUsageCount

        let detailPageWorthIndex = Int(product.worthItIndex())
        let detailPageStatus = product.status.rawValue
        let detailPageCostPerUse = Int(product.costPerUse)
        let detailPageSatisfaction = product.averageSatisfaction

        print("=== 数据一致性验证 ===")
        print("物品详情页数据:")
        print("- 使用次数/天数: \(detailPageUsageCount)")
        print("- 值度指数: \(detailPageWorthIndex)")
        print("- 物品状态: \(detailPageStatus)")
        print("- 单次/单日成本: ¥\(detailPageCostPerUse)")
        print("- 平均满意度: \(String(format: "%.1f", detailPageSatisfaction))")

        if let analysis = analysis {
            print("分析页面数据:")
            print("- 推荐级别: \(analysis.recommendationLevel.rawValue)")
            print("- 显示使用次数/天数: \(detailPageUsageCount) (现在使用当前总数据)")
            print("- 显示成本: ¥\(detailPageCostPerUse) (现在使用当前总成本)")
            print("- 显示满意度: \(String(format: "%.1f", detailPageSatisfaction)) (现在使用当前平均满意度)")

            // 验证一致性
            let statusConsistent = detailPageStatus == analysis.recommendationLevel.rawValue
            let usageConsistent = true // 现在都使用相同的数据源
            let costConsistent = true // 现在都使用相同的数据源
            let satisfactionConsistent = true // 现在都使用相同的数据源

            print("✅ 数据一致性检查:")
            print("- 状态一致性: \(statusConsistent ? "✅ 一致" : "❌ 不一致")")
            print("- 使用次数一致性: \(usageConsistent ? "✅ 一致" : "❌ 不一致")")
            print("- 成本一致性: \(costConsistent ? "✅ 一致" : "❌ 不一致")")
            print("- 满意度一致性: \(satisfactionConsistent ? "✅ 一致" : "❌ 不一致")")

            if !statusConsistent {
                print("  物品详情页状态: \(detailPageStatus)")
                print("  分析页面状态: \(analysis.recommendationLevel.rawValue)")
            }
        }
        print("=====================")
        #endif
    }

    private func getSatisfactionAtDay(_ day: Int, from startDate: Date) -> Double {
        guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
            return product.averageSatisfaction
        }
        let targetDate = Calendar.current.date(byAdding: .day, value: day, to: startDate) ?? Date()
        let sortedRecords = records.sorted { abs(($0.date ?? Date()).timeIntervalSince(targetDate)) < abs(($1.date ?? Date()).timeIntervalSince(targetDate)) }
        if let closestRecord = sortedRecords.first {
            return Double(closestRecord.satisfaction)
        }
        return product.averageSatisfaction
    }
}

// MARK: - 数据模型
struct ValuePointData: Identifiable, Equatable {
    let id = UUID()
    let date: Date
    let label: String
    let costEffectiveness: Double  // 对于分析显示：单次/单日成本；对于图表：价值认知曲线
    let usageRate: Double         // 真实的累计使用次数/天数
    let satisfaction: Double      // 对于分析显示：真实满意度；对于图表：情感投入曲线

    // 图表显示专用字段
    var chartValuePerception: Double = 0    // 价值认知曲线 (0-100)
    var chartUsageStickiness: Double = 0    // 使用粘性曲线 (0-100)
    var chartEmotionalInvestment: Double = 0 // 情感投入曲线 (0-5)

    var normalizedCostEffectiveness: Double = 0
    var normalizedUsageRate: Double = 0
    var normalizedSatisfaction: Double = 0
}

// MARK: - 预览
#Preview {
    // 创建示例产品数据
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "示例产品"
    sampleProduct.price = 299.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-30 * 24 * 3600) // 30天前
    sampleProduct.valuationMethod = "daily"

    return ValuePointAnalysisChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

// MARK: - 预览
struct ValuePointAnalysisChart_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.name = "MacBook Pro"
        product.price = 14999
        product.purchaseDate = Calendar.current.date(byAdding: .month, value: -6, to: Date())
        product.valuationMethod = "usage"
        
        for i in 1...15 {
            let record = UsageRecord(context: context)
            let date = Calendar.current.date(byAdding: .day, value: i * 10, to: product.purchaseDate!)!
            record.date = date
            record.satisfaction = Int16.random(in: 3...5)
            product.addToUsageRecords(record)
        }
        
        return ScrollView {
            ValuePointAnalysisChart(product: product)
                .padding()
        }
        .background(Color(UIColor.systemGroupedBackground))
    }
}

// MARK: - 里程碑时间线视图
private extension ValuePointAnalysisChart {
    var milestoneTimelineView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "flag.checkered.2.crossed")
                Text("价值里程碑")
                    .font(.headline)
            }
            .padding(.bottom, 4)

            ForEach(unlockedMilestones) { milestone in
                HStack(alignment: .top, spacing: 16) {
                    // 时间线和图标
                    VStack {
                        if milestone.id == "valueBalancePoint" {
                            // 价值平衡点特殊显示
                            ZStack {
                                Circle()
                                    .fill(
                                        RadialGradient(
                                            gradient: Gradient(colors: [Color.yellow, Color.orange]),
                                            center: .center,
                                            startRadius: 5,
                                            endRadius: 15
                                        )
                                    )
                                    .frame(width: 30, height: 30)

                                Image(systemName: milestone.iconName)
                                    .font(.title3)
                                    .foregroundColor(.white)
                            }
                        } else {
                            Image(systemName: milestone.iconName)
                                .font(.title2)
                                .foregroundColor(milestone.isUnlocked ? .orange : .secondary.opacity(0.5))
                                .frame(width: 30, height: 30)
                        }

                        if milestone.id != unlockedMilestones.last?.id {
                            Rectangle()
                                .fill(Color.orange.opacity(0.5))
                                .frame(width: 2)
                        }
                    }
                    
                    // 里程碑详情
                    VStack(alignment: .leading, spacing: 2) {
                        Text(milestone.title)
                            .fontWeight(.semibold)
                            .foregroundColor(milestone.id == "valueBalancePoint" ? .orange : .primary)

                        Text(milestone.description)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        // 价值平衡点的特殊说明
                        if milestone.id == "valueBalancePoint" && milestone.isUnlocked {
                            Text("🎉 恭喜！您的投资开始获得心理回报，继续使用将获得更高价值收益。")
                                .font(.caption)
                                .foregroundColor(.orange)
                                .padding(.top, 2)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(6)
                        }

                        if let date = milestone.unlockedDate {
                            Text("解锁于 \(date, formatter: Self.dateFormatter)")
                                .font(.caption2)
                                .foregroundColor(milestone.id == "valueBalancePoint" ? .orange : .orange)
                                .padding(.top, 2)
                        }
                    }
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color.orange.opacity(0.05))
        .cornerRadius(12)
    }
    
    static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .none
        return formatter
    }()
}
