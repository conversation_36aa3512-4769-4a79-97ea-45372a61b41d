import Foundation

/// 订阅产品的价值计算器
/// 专门处理虚拟订阅产品的复杂价值模式
struct SubscriptionValueCalculator {
    
    // MARK: - 订阅价值组件
    struct SubscriptionValue {
        let availabilityValue: Double    // 可用性价值 (0-1)
        let usageValue: Double          // 使用价值 (0-1)
        let efficiencyValue: Double     // 效率价值 (0-1)
        let compositeValue: Double      // 综合价值 (0-1)
        
        let availabilityWeight: Double  // 可用性权重
        let usageWeight: Double        // 使用权重
        let efficiencyWeight: Double   // 效率权重
        
        let usageFrequency: Double     // 使用频率 (0-1)
        let subscriptionUtilization: Double // 订阅利用率 (0-1)
    }
    
    // MARK: - 订阅阶段
    enum SubscriptionPhase {
        case honeymoon      // 蜜月期 (前10%)
        case adaptation     // 适应期 (10%-30%)
        case stable         // 稳定期 (30%-80%)
        case expiration     // 临期 (80%-100%)
        case expired        // 已过期
    }
    
    // MARK: - 主要计算方法
    static func calculateSubscriptionValue(
        product: Product,
        usageRecords: [UsageRecord],
        currentDate: Date = Date()
    ) -> SubscriptionValue {
        
        guard let startDate = product.subscriptionStartDate ?? product.purchaseDate,
              let endDate = product.subscriptionEndDate else {
            // 如果没有订阅信息，降级为普通按天计算
            return calculateFallbackValue(product: product, usageRecords: usageRecords)
        }
        
        let totalDays = Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 1
        let elapsedDays = Calendar.current.dateComponents([.day], from: startDate, to: min(currentDate, endDate)).day ?? 0
        let usageDays = Set(usageRecords.compactMap { $0.date }).count
        
        // 计算基础指标
        let usageFrequency = Double(usageDays) / Double(max(elapsedDays, 1))
        let subscriptionUtilization = Double(usageDays) / Double(totalDays)
        let averageSatisfaction = usageRecords.isEmpty ? 3.0 : 
            usageRecords.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(usageRecords.count)
        
        // 计算三个价值维度
        let availabilityValue = calculateAvailabilityValue(
            totalDays: totalDays,
            elapsedDays: elapsedDays,
            subscriptionCost: product.price
        )
        
        let usageValue = calculateUsageValue(
            usageRecords: usageRecords,
            averageSatisfaction: averageSatisfaction,
            usageFrequency: usageFrequency
        )
        
        let efficiencyValue = calculateEfficiencyValue(
            usageFrequency: usageFrequency,
            subscriptionUtilization: subscriptionUtilization,
            costPerDay: product.price / Double(totalDays)
        )
        
        // 动态权重计算
        let weights = calculateDynamicWeights(usageFrequency: usageFrequency)
        
        // 综合价值计算
        let compositeValue = availabilityValue * weights.availability +
                           usageValue * weights.usage +
                           efficiencyValue * weights.efficiency
        
        return SubscriptionValue(
            availabilityValue: availabilityValue,
            usageValue: usageValue,
            efficiencyValue: efficiencyValue,
            compositeValue: compositeValue,
            availabilityWeight: weights.availability,
            usageWeight: weights.usage,
            efficiencyWeight: weights.efficiency,
            usageFrequency: usageFrequency,
            subscriptionUtilization: subscriptionUtilization
        )
    }
    
    // MARK: - 可用性价值计算
    private static func calculateAvailabilityValue(
        totalDays: Int,
        elapsedDays: Int,
        subscriptionCost: Double
    ) -> Double {
        // 基础可用性价值：每天都有"随时可用"的价值
        let dailyCost = subscriptionCost / Double(totalDays)
        let baseAvailability = min(1.0, (100.0 - dailyCost) / 100.0) // 成本越低，可用性价值越高
        
        // 时间衰减：随着时间推移，可用性价值可能下降
        let timeProgress = Double(elapsedDays) / Double(totalDays)
        let timeDecay = 1.0 - (timeProgress * 0.2) // 最多衰减20%
        
        return baseAvailability * timeDecay
    }
    
    // MARK: - 使用价值计算
    private static func calculateUsageValue(
        usageRecords: [UsageRecord],
        averageSatisfaction: Double,
        usageFrequency: Double
    ) -> Double {
        if usageRecords.isEmpty {
            return 0.1 // 没有使用记录，给予最低使用价值
        }
        
        // 满意度归一化 (1-5 -> 0-1)
        let normalizedSatisfaction = (averageSatisfaction - 1.0) / 4.0
        
        // 使用频率加成
        let frequencyBonus = min(1.0, usageFrequency * 2.0) // 高频使用有加成
        
        // 使用趋势分析
        let trendBonus = calculateUsageTrend(usageRecords)
        
        return (normalizedSatisfaction * 0.6 + frequencyBonus * 0.3 + trendBonus * 0.1)
    }
    
    // MARK: - 效率价值计算
    private static func calculateEfficiencyValue(
        usageFrequency: Double,
        subscriptionUtilization: Double,
        costPerDay: Double
    ) -> Double {
        // 使用效率：实际使用频率
        let usageEfficiency = min(1.0, usageFrequency * 1.5)
        
        // 成本效率：成本越低，效率价值越高
        let costEfficiency = max(0.0, min(1.0, (50.0 - costPerDay) / 50.0))
        
        // 订阅利用率
        let utilizationEfficiency = min(1.0, subscriptionUtilization * 2.0)
        
        return (usageEfficiency * 0.5 + costEfficiency * 0.3 + utilizationEfficiency * 0.2)
    }
    
    // MARK: - 动态权重计算
    private static func calculateDynamicWeights(usageFrequency: Double) -> (availability: Double, usage: Double, efficiency: Double) {
        if usageFrequency < 0.3 {
            // 低频用户：重视可用性价值
            return (availability: 0.6, usage: 0.25, efficiency: 0.15)
        } else if usageFrequency < 0.7 {
            // 中频用户：平衡各种价值
            return (availability: 0.4, usage: 0.4, efficiency: 0.2)
        } else {
            // 高频用户：重视使用价值
            return (availability: 0.25, usage: 0.6, efficiency: 0.15)
        }
    }
    
    // MARK: - 使用趋势分析
    private static func calculateUsageTrend(_ records: [UsageRecord]) -> Double {
        guard records.count >= 3 else { return 0.5 }
        
        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        let recentRecords = Array(sortedRecords.suffix(3))
        let earlierRecords = Array(sortedRecords.prefix(3))
        
        let recentAvg = recentRecords.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(recentRecords.count)
        let earlierAvg = earlierRecords.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(earlierRecords.count)
        
        let trend = (recentAvg - earlierAvg) / 4.0 // 归一化到 -1 到 1
        return max(0.0, min(1.0, 0.5 + trend)) // 转换到 0-1 范围
    }
    
    // MARK: - 降级计算
    private static func calculateFallbackValue(product: Product, usageRecords: [UsageRecord]) -> SubscriptionValue {
        // 当没有订阅信息时，使用简化的计算方式
        let usageFrequency = usageRecords.isEmpty ? 0.1 : 0.5
        let averageSatisfaction = usageRecords.isEmpty ? 3.0 : 
            usageRecords.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(usageRecords.count)
        
        let normalizedSatisfaction = (averageSatisfaction - 1.0) / 4.0
        
        return SubscriptionValue(
            availabilityValue: 0.5,
            usageValue: normalizedSatisfaction,
            efficiencyValue: usageFrequency,
            compositeValue: (0.5 + normalizedSatisfaction + usageFrequency) / 3.0,
            availabilityWeight: 0.33,
            usageWeight: 0.33,
            efficiencyWeight: 0.34,
            usageFrequency: usageFrequency,
            subscriptionUtilization: usageFrequency
        )
    }
    
    // MARK: - 订阅阶段判断
    static func getSubscriptionPhase(
        startDate: Date,
        endDate: Date,
        currentDate: Date = Date()
    ) -> SubscriptionPhase {
        let totalDays = Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 1
        let elapsedDays = Calendar.current.dateComponents([.day], from: startDate, to: currentDate).day ?? 0
        
        if currentDate > endDate {
            return .expired
        }
        
        let progress = Double(elapsedDays) / Double(totalDays)
        
        switch progress {
        case 0..<0.1: return .honeymoon
        case 0.1..<0.3: return .adaptation
        case 0.3..<0.8: return .stable
        default: return .expiration
        }
    }
}

// MARK: - Product 扩展
extension Product {
    var subscriptionStartDate: Date? {
        // 这里需要在Core Data模型中添加相应字段
        // 暂时返回购买日期作为订阅开始日期
        return purchaseDate
    }
    
    var subscriptionEndDate: Date? {
        // 这里需要在Core Data模型中添加相应字段
        // 暂时根据购买日期推算（假设年订阅）
        guard let startDate = purchaseDate else { return nil }
        return Calendar.current.date(byAdding: .year, value: 1, to: startDate)
    }
}
