import Foundation
import CoreData
import SwiftUI

// 值点计算器 - 专门用于计算和分析产品的值点
class ValuePointCalculator {
    
    // MARK: - 值点分析结果
    struct ValuePointAnalysis {
        let valuePoint: ValuePointData?
        let analysisText: String
        let recommendationLevel: RecommendationLevel
        let keyInsights: [String]
        let futureProjection: String?
    }
    
    enum RecommendationLevel: String, CaseIterable {
        case excellent = "核心产品"      // 对应ProductStatus.core
        case good = "值得拥有"          // 对应ProductStatus.worthwhile
        case average = "潜力股"         // 对应ProductStatus.potential
        case poor = "需要重新考虑"       // 对应ProductStatus.reconsider
        case reconsider = "建议出售"     // 对应ProductStatus.sellRecommended

        var color: Color {
            switch self {
            case .excellent: return .green
            case .good: return .blue
            case .average: return .orange
            case .poor: return .yellow
            case .reconsider: return .red
            }
        }
    }
    
    // 值点类型枚举
    enum ValuePointType: String, CaseIterable {
        case breakthrough = "突破式值点"
        case equilibrium = "平衡式值点"
        case emotional = "情感式值点"
        case practical = "实用式值点"
        case declining = "衰退式值点"
        
        var description: String {
            switch self {
            case .breakthrough: return "价值感知大幅提升的关键时刻"
            case .equilibrium: return "价值感知、使用粘性、情感投入达到平衡"
            case .emotional: return "情感连接达到峰值"
            case .practical: return "实用价值最大化"
            case .declining: return "价值开始下降的转折点"
            }
        }
        
        var color: Color {
            switch self {
            case .breakthrough: return .green
            case .equilibrium: return .blue
            case .emotional: return .purple
            case .practical: return .orange
            case .declining: return .red
            }
        }
    }
    
    // 增强的数据点结构
    struct EnhancedValuePointData {
        let original: ValuePointData
        let valuePerception: Double      // 价值感知 (0-1)
        let usageStickiness: Double      // 使用粘性 (0-1)
        let emotionalInvestment: Double  // 情感投入 (0-1)
    }
    
    // MARK: - 主要分析方法
    static func analyzeValuePoint(for product: Product, dataPoints: [ValuePointData]) -> ValuePointAnalysis {
        guard !dataPoints.isEmpty else {
            return ValuePointAnalysis(
                valuePoint: nil,
                analysisText: "数据不足，无法进行值点分析",
                recommendationLevel: .reconsider,
                keyInsights: ["需要更多使用记录来进行准确分析"],
                futureProjection: nil
            )
        }
        
        // 寻找值点
        let valuePoint = findOptimalValuePoint(in: dataPoints, for: product)
        
        // 生成分析文本
        let analysisText = generateDetailedAnalysis(valuePoint: valuePoint, product: product, dataPoints: dataPoints)
        
        // 确定推荐级别
        let recommendationLevel = determineRecommendationLevel(valuePoint: valuePoint, product: product)
        
        // 生成关键洞察
        let keyInsights = generateKeyInsights(valuePoint: valuePoint, product: product, dataPoints: dataPoints)
        
        // 生成未来预测
        let futureProjection = generateFutureProjection(dataPoints: dataPoints, product: product)
        
        return ValuePointAnalysis(
            valuePoint: valuePoint,
            analysisText: analysisText,
            recommendationLevel: recommendationLevel,
            keyInsights: keyInsights,
            futureProjection: futureProjection
        )
    }
    
    // MARK: - 高级值点寻找算法
    private static func findOptimalValuePoint(in dataPoints: [ValuePointData], for product: Product) -> ValuePointData? {
        guard dataPoints.count >= 3 else { return nil } // 至少需要3个点才能进行有意义的分析
        
        var candidates: [(point: ValuePointData, score: Double, type: ValuePointType)] = []
        
        // 计算每个点的价值感知、使用粘性、情感投入
        let enhancedPoints = calculateEnhancedMetrics(dataPoints: dataPoints, product: product)
        
        // 分析每个数据点作为值点的可能性
        for i in 2..<enhancedPoints.count {
            let currentPoint = enhancedPoints[i]
            let previousPoint = enhancedPoints[i-1]
            let prePreviousPoint = enhancedPoints[i-2]
            
            // 计算价值感知变化率
            let perceptionVelocity = currentPoint.valuePerception - previousPoint.valuePerception
            let perceptionAcceleration = perceptionVelocity - (previousPoint.valuePerception - prePreviousPoint.valuePerception)
            
            // 计算使用粘性趋势
            let stickinessStability = 1.0 - abs(currentPoint.usageStickiness - previousPoint.usageStickiness)
            
            // 计算情感投入强度
            let emotionalGrowth = currentPoint.emotionalInvestment - previousPoint.emotionalInvestment
            
            // 检查是否为值点
            let valuePointResult = evaluateValuePoint(
                current: currentPoint,
                previous: previousPoint,
                prePrevious: prePreviousPoint,
                index: i
            )
            
            if let (score, type) = valuePointResult {
                candidates.append((point: dataPoints[i], score: score, type: type))
            }
        }
        
        // 选择最有意义的值点
        return selectBestValuePoint(candidates: candidates)
    }
    
    // 计算增强指标 - 公开方法供外部调用
    static func calculateEnhancedMetrics(dataPoints: [ValuePointData], product: Product) -> [EnhancedValuePointData] {
        var enhanced: [EnhancedValuePointData] = []
        
        for i in 0..<dataPoints.count {
            let point = dataPoints[i]
            
            // 价值感知计算 - 考虑时间衰减和期望调整
            let daysSincePurchase = point.date.timeIntervalSince(product.purchaseDate ?? Date()) / (24 * 3600)
            let noveltyFactor = exp(-daysSincePurchase / 60.0) // 60天半衰期
            let baseValue = point.satisfaction / 5.0
            let expectedValue = calculateExpectedSatisfaction(for: product, at: daysSincePurchase)
            let expectationGap = max(0, baseValue - expectedValue)
            let valuePerception = baseValue + (noveltyFactor * 0.2) + (expectationGap * 0.3)
            
            // 使用粘性计算 - 考虑频率和主动性
            let usageStickiness = calculateUsageStickiness(at: i, dataPoints: dataPoints)
            
            // 情感投入计算 - 考虑满意度趋势和稳定性
            let emotionalInvestment = calculateEmotionalInvestment(at: i, dataPoints: dataPoints)
            
            enhanced.append(EnhancedValuePointData(
                original: point,
                valuePerception: valuePerception,
                usageStickiness: usageStickiness,
                emotionalInvestment: emotionalInvestment
            ))
        }
        
        return enhanced
    }
    
    // 评估是否为值点
    private static func evaluateValuePoint(
        current: EnhancedValuePointData,
        previous: EnhancedValuePointData,
        prePrevious: EnhancedValuePointData,
        index: Int
    ) -> (score: Double, type: ValuePointType)? {
        
        // 条件1：价值感知显著提升
        let perceptionImprovement = current.valuePerception - previous.valuePerception
        let significantImprovement = perceptionImprovement > 0.15
        
        // 条件2：三维度平衡
        let balance = calculateMultiDimensionalBalance(current)
        let isBalanced = balance > 0.7
        
        // 条件3：趋势稳定性
        let stability = calculateTrendStability(current: current, previous: previous, prePrevious: prePrevious)
        let isStable = stability > 0.6
        
        // 条件4：情感连接峰值
        let emotionalPeak = current.emotionalInvestment > 0.8 && 
                           current.emotionalInvestment > previous.emotionalInvestment
        
        // 值点类型判断
        var valuePointType: ValuePointType?
        var score: Double = 0
        
        if significantImprovement && isStable {
            valuePointType = .breakthrough
            score = perceptionImprovement * 2 + stability
        } else if isBalanced && isStable {
            valuePointType = .equilibrium
            score = balance + stability
        } else if emotionalPeak {
            valuePointType = .emotional
            score = current.emotionalInvestment * 1.5
        } else if current.valuePerception > 0.8 && current.usageStickiness > 0.7 {
            valuePointType = .practical
            score = (current.valuePerception + current.usageStickiness) / 2
        }
        
        // 只有达到最低分数要求才被认为是值点
        if let type = valuePointType, score > 0.6 {
            return (score, type)
        }
        
        return nil
    }
    
    // 选择最佳值点
    private static func selectBestValuePoint(candidates: [(point: ValuePointData, score: Double, type: ValuePointType)]) -> ValuePointData? {
        guard !candidates.isEmpty else { return nil }
        
        // 优先选择突破式值点
        if let breakthrough = candidates.filter({ $0.type == .breakthrough }).max(by: { $0.score < $1.score }) {
            return breakthrough.point
        }
        
        // 其次选择平衡式值点
        if let equilibrium = candidates.filter({ $0.type == .equilibrium }).max(by: { $0.score < $1.score }) {
            return equilibrium.point
        }
        
        // 最后选择得分最高的值点
        return candidates.max(by: { $0.score < $1.score })?.point
    }
    
    // 计算三条曲线的收敛程度
    private static func calculateConvergence(_ point: ValuePointData) -> Double {
        let values = [
            point.normalizedCostEffectiveness,
            point.normalizedUsageRate,
            point.normalizedSatisfaction
        ]
        
        let mean = values.reduce(0, +) / Double(values.count)
        let variance = values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count)
        
        // 方差越小，收敛程度越高
        return max(0, 100 - variance)
    }
    
    // MARK: - 分析文本生成
    private static func generateDetailedAnalysis(valuePoint: ValuePointData?, product: Product, dataPoints: [ValuePointData]) -> String {
        guard let valuePoint = valuePoint else {
            return "暂未找到明确的值点。建议继续使用产品以获得更多数据进行分析。"
        }
        
        let isDaily = product.valuationMethod == "daily"
        let unit = isDaily ? "天" : "次"
        let costType = isDaily ? "单日成本" : "单次成本"
        
        var analysis = ""
        
        // 值点基本信息 - 现在使用真实的使用次数/天数和成本数据
        analysis += "在\(valuePoint.label)达到值点，此时已使用\(Int(valuePoint.usageRate))\(unit)，"
        analysis += "\(costType)为¥\(String(format: "%.1f", valuePoint.costEffectiveness))，"
        analysis += "满意度为\(String(format: "%.1f", valuePoint.satisfaction))/5。"

        // 值点意义解释 - 基于真实成本数据进行判断
        analysis += "\n\n这个值点意味着："

        // 计算合理的成本阈值
        let totalCost = product.totalCostOfOwnership
        let reasonableCostThreshold = isDaily ? (totalCost / 365.0) : (totalCost / 50.0) // 按天：年均成本，按次：50次摊销

        if valuePoint.satisfaction >= 4.0 && valuePoint.costEffectiveness <= reasonableCostThreshold {
            analysis += "产品已充分证明其价值，投资回报优秀。"
        } else if valuePoint.satisfaction >= 3.5 {
            analysis += "产品基本达到预期，投资开始显现回报。"
        } else if valuePoint.costEffectiveness <= reasonableCostThreshold * 2 {
            analysis += "产品使用成本趋于合理，但满意度仍有提升空间。"
        } else {
            analysis += "建议增加使用频率以降低单次成本，或考虑优化使用方式提升满意度。"
        }
        
        return analysis
    }
    
    // MARK: - 推荐级别确定
    private static func determineRecommendationLevel(valuePoint: ValuePointData?, product: Product) -> RecommendationLevel {
        guard let valuePoint = valuePoint else { return .reconsider }

        // 使用与ProductStatus一致的值度指数计算逻辑
        let worthIndex = product.worthItIndex()

        // 基于值度指数确定推荐级别，与ProductStatus保持一致
        if worthIndex >= 80 {
            return .excellent  // 对应 "核心产品"
        } else if worthIndex >= 60 {
            return .good       // 对应 "值得拥有"
        } else if worthIndex >= 40 {
            return .average    // 对应 "潜力股"
        } else if worthIndex >= 20 {
            return .poor       // 对应 "需要重新考虑"
        } else {
            return .reconsider // 对应 "建议出售"
        }
    }
    
    // MARK: - 关键洞察生成
    private static func generateKeyInsights(valuePoint: ValuePointData?, product: Product, dataPoints: [ValuePointData]) -> [String] {
        var insights: [String] = []
        
        guard let valuePoint = valuePoint, dataPoints.count >= 2 else {
            insights.append("需要更多使用数据来生成准确洞察")
            return insights
        }
        
        let isDaily = product.valuationMethod == "daily"
        let unit = isDaily ? "天" : "次"
        
        // 使用效率洞察
        let usageEfficiency = valuePoint.usageRate / Double(Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: Date()).day ?? 1)
        if isDaily {
            if usageEfficiency > 0.8 {
                insights.append("使用频率很高，几乎每天都在使用")
            } else if usageEfficiency > 0.5 {
                insights.append("使用频率适中，大部分时间都有使用")
            } else {
                insights.append("使用频率偏低，可以考虑增加使用场景")
            }
        } else {
            let avgUsagePerMonth = valuePoint.usageRate / max(1, Double(Calendar.current.dateComponents([.month], from: product.purchaseDate ?? Date(), to: Date()).month ?? 1))
            if avgUsagePerMonth >= 10 {
                insights.append("使用频率很高，平均每月使用\(Int(avgUsagePerMonth))次")
            } else if avgUsagePerMonth >= 5 {
                insights.append("使用频率适中，平均每月使用\(Int(avgUsagePerMonth))次")
            } else {
                insights.append("使用频率偏低，平均每月仅使用\(Int(avgUsagePerMonth))次")
            }
        }
        
        // 成本效益洞察
        let totalCost = product.totalCostOfOwnership
        let costRatio = valuePoint.costEffectiveness / totalCost
        if costRatio <= 0.1 {
            insights.append("成本摊销效果优秀，已充分发挥产品价值")
        } else if costRatio <= 0.2 {
            insights.append("成本摊销效果良好，投资回报明显")
        } else {
            insights.append("仍有进一步降低使用成本的空间")
        }
        
        // 满意度趋势洞察
        if dataPoints.count >= 3 {
            let recentSatisfaction = dataPoints.suffix(3).map { $0.satisfaction }.reduce(0, +) / 3
            let earlySatisfaction = dataPoints.prefix(3).map { $0.satisfaction }.reduce(0, +) / 3
            
            if recentSatisfaction > earlySatisfaction + 0.5 {
                insights.append("满意度呈上升趋势，产品体验在改善")
            } else if recentSatisfaction < earlySatisfaction - 0.5 {
                insights.append("满意度有所下降，可能需要关注产品状态")
            } else {
                insights.append("满意度保持稳定，产品表现一致")
            }
        }
        
        return insights
    }
    
    // MARK: - 未来预测生成
    private static func generateFutureProjection(dataPoints: [ValuePointData], product: Product) -> String? {
        guard dataPoints.count >= 3 else { return nil }
        
        let isDaily = product.valuationMethod == "daily"
        let unit = isDaily ? "天" : "次"
        
        // 计算趋势
        let recentPoints = dataPoints.suffix(3)
        let costTrend = recentPoints.last!.costEffectiveness - recentPoints.first!.costEffectiveness
        let satisfactionTrend = recentPoints.last!.satisfaction - recentPoints.first!.satisfaction
        
        var projection = "基于当前趋势预测："
        
        if costTrend < 0 && satisfactionTrend >= 0 {
            projection += "继续使用将进一步提升性价比，建议保持当前使用模式。"
        } else if costTrend < 0 && satisfactionTrend < 0 {
            projection += "虽然成本在降低，但满意度有下降趋势，建议关注使用体验。"
        } else if satisfactionTrend > 0 {
            projection += "满意度在提升，产品价值将继续体现。"
        } else {
            projection += "建议优化使用方式以获得更好的投资回报。"
        }
        
        return projection
    }
    
    // MARK: - 新增辅助方法
    
    // 计算期望满意度
    private static func calculateExpectedSatisfaction(for product: Product, at days: Double) -> Double {
        // 基于产品类型和价格的期望满意度模型
        let baseExpectation = 0.7
        let priceAdjustment = min(0.2, product.totalCostOfOwnership / 10000.0 * 0.2)
        let timeDecay = exp(-days / 180.0) * 0.1 // 180天期望值衰减
        return baseExpectation + priceAdjustment + timeDecay
    }
    
    // 计算使用粘性
    private static func calculateUsageStickiness(at index: Int, dataPoints: [ValuePointData]) -> Double {
        guard index > 0 else { return 0.3 }
        
        let currentPoint = dataPoints[index]
        
        // 对于消耗型物品，使用粘性应该随着使用记录增加而提升
        // 计算累积使用频率（使用次数/时间跨度）
        let totalUsageCount = Double(index + 1) // 当前累积使用次数
        let firstPoint = dataPoints[0]
        let totalTimeSpan = max(1.0, currentPoint.date.timeIntervalSince(firstPoint.date) / (24 * 3600)) // 总时间跨度（天）
        
        // 使用密度评分：使用次数越多，时间跨度内的密度越高，粘性越强
        let usageDensity = totalUsageCount / totalTimeSpan
        let densityScore = min(1.0, usageDensity / 0.5) // 假设每2天使用1次为满分
        
        // 使用趋势评分：最近的使用频率
        let recentTrendScore = calculateRecentUsageTrend(at: index, dataPoints: dataPoints)
        
        // 满意度影响：满意度越高，说明用户越愿意继续使用
        let satisfactionImpact = currentPoint.satisfaction / 5.0
        
        // 使用连续性：评估使用的规律性
        let continuityScore = calculateUsageContinuity(at: index, dataPoints: dataPoints)
        
        // 综合计算使用粘性，确保随着正向使用增加而提升
        let baseStickiness = (densityScore * 0.4 + recentTrendScore * 0.3 + satisfactionImpact * 0.2 + continuityScore * 0.1)
        
        // 添加累积使用奖励：使用次数越多，基础粘性越高
        let usageBonus = min(0.3, totalUsageCount * 0.02) // 每次使用增加2%，最多30%奖励
        
        return min(1.0, baseStickiness + usageBonus)
    }
    
    // 计算最近使用趋势
    private static func calculateRecentUsageTrend(at index: Int, dataPoints: [ValuePointData]) -> Double {
        guard index >= 2 else { return 0.6 }
        
        // 分析最近3次使用的时间间隔
        let recentCount = min(3, index + 1)
        let recentPoints = Array(dataPoints[(index - recentCount + 1)...index])
        
        if recentPoints.count < 2 { return 0.6 }
        
        // 计算最近使用的平均间隔
        var totalInterval: TimeInterval = 0
        for i in 1..<recentPoints.count {
            totalInterval += recentPoints[i].date.timeIntervalSince(recentPoints[i-1].date)
        }
        let avgInterval = totalInterval / Double(recentPoints.count - 1) / (24 * 3600) // 转换为天数
        
        // 间隔越短，趋势评分越高
        // 1天间隔=1.0分，7天间隔=0.5分，30天间隔=0.1分
        let trendScore = max(0.1, min(1.0, 1.0 / (1.0 + avgInterval / 3.0)))
        
        return trendScore
    }
    
    // 计算使用连续性
    private static func calculateUsageContinuity(at index: Int, dataPoints: [ValuePointData]) -> Double {
        guard index >= 2 else { return 0.5 }
        
        let recentPoints = Array(dataPoints[max(0, index-4)...index]) // 最近5次使用
        if recentPoints.count < 3 { return 0.5 }
        
        // 计算使用间隔的变异系数
        var intervals: [Double] = []
        for i in 1..<recentPoints.count {
            let interval = recentPoints[i].date.timeIntervalSince(recentPoints[i-1].date) / (24 * 3600)
            intervals.append(interval)
        }
        
        let avgInterval = intervals.reduce(0, +) / Double(intervals.count)
        let variance = intervals.map { pow($0 - avgInterval, 2) }.reduce(0, +) / Double(intervals.count)
        let standardDeviation = sqrt(variance)
        
        // 变异系数越小，连续性越好
        let coefficientOfVariation = avgInterval > 0 ? standardDeviation / avgInterval : 1.0
        let continuityScore = max(0.1, 1.0 - min(1.0, coefficientOfVariation))
        
        return continuityScore
    }
    
    // 计算情感投入
    private static func calculateEmotionalInvestment(at index: Int, dataPoints: [ValuePointData]) -> Double {
        let currentPoint = dataPoints[index]
        
        // 基础满意度
        let baseSatisfaction = currentPoint.satisfaction / 5.0
        
        // 满意度趋势
        let trend = calculateSatisfactionTrend(at: index, dataPoints: dataPoints)
        
        // 情感稳定性
        let stability = calculateEmotionalStability(at: index, dataPoints: dataPoints)
        
        return (baseSatisfaction + trend + stability) / 3.0
    }
    
    // 计算满意度趋势
    private static func calculateSatisfactionTrend(at index: Int, dataPoints: [ValuePointData]) -> Double {
        guard index >= 2 else { return 0.5 }
        
        let recentPoints = Array(dataPoints[max(0, index-2)...index])
        let satisfactions = recentPoints.map { $0.satisfaction }
        
        // 简单线性回归斜率
        let n = Double(satisfactions.count)
        let sumX = (0..<satisfactions.count).reduce(0, +)
        let sumY = satisfactions.reduce(0, +)
        let sumXY = zip(0..<satisfactions.count, satisfactions).map { Double($0.0) * $0.1 }.reduce(0, +)
        let sumX2 = (0..<satisfactions.count).map { $0 * $0 }.reduce(0, +)
        
        let slope = (n * sumXY - Double(sumX) * sumY) / (n * Double(sumX2) - Double(sumX * sumX))
        
        // 标准化到0-1范围
        return max(0, min(1, 0.5 + slope / 2.0))
    }
    
    // 计算情感稳定性
    private static func calculateEmotionalStability(at index: Int, dataPoints: [ValuePointData]) -> Double {
        guard index >= 2 else { return 0.5 }
        
        let recentPoints = Array(dataPoints[max(0, index-2)...index])
        let satisfactions = recentPoints.map { $0.satisfaction }
        
        let avg = satisfactions.reduce(0, +) / Double(satisfactions.count)
        let variance = satisfactions.map { pow($0 - avg, 2) }.reduce(0, +) / Double(satisfactions.count)
        
        // 方差越小，稳定性越高
        return max(0.1, 1.0 - variance / 25.0) // 25是5^2，满意度最大方差
    }
    
    // 计算多维平衡
    private static func calculateMultiDimensionalBalance(_ point: EnhancedValuePointData) -> Double {
        let values = [point.valuePerception, point.usageStickiness, point.emotionalInvestment]
        let mean = values.reduce(0, +) / Double(values.count)
        let variance = values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count)
        
        // 方差越小，平衡性越好
        return max(0, 1.0 - variance * 4) // 调整系数
    }
    
    // 计算趋势稳定性
    private static func calculateTrendStability(
        current: EnhancedValuePointData,
        previous: EnhancedValuePointData,
        prePrevious: EnhancedValuePointData
    ) -> Double {
        // 计算各维度的变化率
        let perceptionChange1 = current.valuePerception - previous.valuePerception
        let perceptionChange2 = previous.valuePerception - prePrevious.valuePerception
        
        let stickinessChange1 = current.usageStickiness - previous.usageStickiness
        let stickinessChange2 = previous.usageStickiness - prePrevious.usageStickiness
        
        let emotionalChange1 = current.emotionalInvestment - previous.emotionalInvestment
        let emotionalChange2 = previous.emotionalInvestment - prePrevious.emotionalInvestment
        
        // 计算变化率的一致性
        let perceptionConsistency = 1.0 - abs(perceptionChange1 - perceptionChange2)
        let stickinessConsistency = 1.0 - abs(stickinessChange1 - stickinessChange2)
        let emotionalConsistency = 1.0 - abs(emotionalChange1 - emotionalChange2)
        
        return (perceptionConsistency + stickinessConsistency + emotionalConsistency) / 3.0
    }
}