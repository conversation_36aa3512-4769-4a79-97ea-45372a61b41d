import SwiftUI

/// 产品价值旅程图表 - 重新设计的直观界面
/// 将三个维度合成为一个易懂的价值故事
struct ValueJourneyChart: View {
    let product: Product
    
    @State private var chartData: [ValueJourneyPoint] = []
    @State private var selectedPoint: ValueJourneyPoint?
    @State private var animationProgress: Double = 0
    @State private var showDetailedView = false
    @State private var significantEvents: [ValueEvent] = []
    
    var body: some View {
        AnalyticsCard(
            title: "产品价值旅程",
            subtitle: "追踪您与产品的价值关系发展"
        ) {
            VStack(spacing: 20) {
                // 主价值旅程图
                mainJourneyChart
                
                // 当前状态指示器
                if let selectedPoint = selectedPoint {
                    currentStateIndicators(selectedPoint)
                }
                
                // 智能洞察卡片
                if !significantEvents.isEmpty {
                    insightCards
                }
                
                // 切换到详细视图的按钮
                detailedViewToggle
            }
        }
        .onAppear {
            calculateJourneyData()
            withAnimation(.easeInOut(duration: 1.5)) {
                animationProgress = 1.0
            }
        }
    }
    
    // MARK: - 主价值旅程图
    private var mainJourneyChart: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("价值发展轨迹")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            GeometryReader { geometry in
                let size = geometry.size
                let valuePoints = journeyDataToPoints(size: size)
                let costPoints = costDataToPoints(size: size)

                ZStack {
                    // 背景网格
                    gridBackground(size: size)

                    // 使用成本曲线（绿色）
                    drawCostCurve(points: costPoints, size: size)

                    // 价值旅程曲线（蓝色）
                    drawValueJourney(points: valuePoints, size: size)

                    // 价值平衡点标记
                    drawValueBalancePoint(valuePoints: valuePoints, costPoints: costPoints, size: size)

                    // 重要事件标记
                    drawEventMarkers(points: valuePoints, size: size)

                    // 交互覆盖层
                    interactionOverlay(points: valuePoints, size: size)
                }
            }
            .frame(height: 200)
            
            // 图例说明
            chartLegend

            // 时间轴标签
            timeAxisLabels
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 当前状态指示器
    private func currentStateIndicators(_ point: ValueJourneyPoint) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前状态分析")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            HStack(spacing: 16) {
                dimensionIndicator(
                    icon: "brain.head.profile",
                    title: "价值认知",
                    value: point.valuePerception,
                    color: .blue,
                    description: getPerceptionDescription(point.valuePerception)
                )

                dimensionIndicator(
                    icon: "repeat.circle",
                    title: "使用粘性",
                    value: point.usageStickiness,
                    color: .green,
                    description: getStickinessDescription(point.usageStickiness)
                )

                dimensionIndicator(
                    icon: "heart.fill",
                    title: "情感投入",
                    value: point.emotionalInvestment,
                    color: .red,
                    description: getEmotionalDescription(point.emotionalInvestment)
                )

                // 使用成本指示器
                costIndicator(
                    cost: point.usageCost,
                    color: .orange
                )

                Spacer()
            }
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }
    
    private func dimensionIndicator(icon: String, title: String, value: Double, color: Color, description: String) -> some View {
        VStack(spacing: 8) {
            // 圆形进度指示器
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 4)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: value / 100.0)
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: animationProgress)
                
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
            }
            
            VStack(spacing: 2) {
                Text("\(Int(value))%")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
    }

    private func costIndicator(cost: Double, color: Color) -> some View {
        VStack(spacing: 8) {
            // 成本图标
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 4)
                    .frame(width: 50, height: 50)

                Image(systemName: "dollarsign.circle.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
            }

            VStack(spacing: 2) {
                Text("¥\(String(format: "%.1f", cost))")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(color)

                Text(product.valuationMethod == "daily" ? "单日成本" : "单次成本")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Text(getCostDescription(cost))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: 80)
    }
    
    // MARK: - 智能洞察卡片
    private var insightCards: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("智能洞察")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            LazyVStack(spacing: 8) {
                ForEach(significantEvents.prefix(3)) { event in
                    insightCard(event)
                }
            }
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }
    
    private func insightCard(_ event: ValueEvent) -> some View {
        HStack(spacing: 12) {
            Image(systemName: event.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(event.color)
                .frame(width: 24, height: 24)
                .background(event.color.opacity(0.1))
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 2) {
                Text(event.title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(event.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(event.timeLabel)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(8)
    }
    
    // MARK: - 图例说明
    private var chartLegend: some View {
        HStack(spacing: 20) {
            // 价值旅程图例
            HStack(spacing: 6) {
                Rectangle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [.blue, .green]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ))
                    .frame(width: 20, height: 3)
                    .cornerRadius(1.5)

                Text("价值指数")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 使用成本图例
            HStack(spacing: 6) {
                Rectangle()
                    .fill(Color.green)
                    .frame(width: 20, height: 2.5)
                    .cornerRadius(1.25)
                    .overlay(
                        Rectangle()
                            .fill(Color.clear)
                            .frame(width: 20, height: 2.5)
                            .overlay(
                                Path { path in
                                    path.move(to: CGPoint(x: 0, y: 1.25))
                                    path.addLine(to: CGPoint(x: 5, y: 1.25))
                                    path.move(to: CGPoint(x: 8, y: 1.25))
                                    path.addLine(to: CGPoint(x: 13, y: 1.25))
                                    path.move(to: CGPoint(x: 16, y: 1.25))
                                    path.addLine(to: CGPoint(x: 20, y: 1.25))
                                }
                                .stroke(Color.green, lineWidth: 2.5)
                            )
                    )

                Text(product.valuationMethod == "daily" ? "单日成本" : "单次成本")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 价值平衡点图例
            HStack(spacing: 6) {
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [Color.yellow, Color.orange]),
                            center: .center,
                            startRadius: 1,
                            endRadius: 4
                        )
                    )
                    .frame(width: 8, height: 8)

                Text("价值平衡点")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.horizontal)
    }

    // MARK: - 详细视图切换
    private var detailedViewToggle: some View {
        Button(action: {
            showDetailedView.toggle()
        }) {
            HStack {
                Image(systemName: showDetailedView ? "chart.line.uptrend.xyaxis" : "chart.xyaxis.line")
                Text(showDetailedView ? "显示简化视图" : "显示详细分析")
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .sheet(isPresented: $showDetailedView) {
            NavigationView {
                ValuePointAnalysisChart(product: product)
            }
        }
    }
}

// MARK: - 数据模型
struct ValueJourneyPoint: Identifiable {
    let id = UUID()
    let date: Date
    let label: String
    let valueIndex: Double // 综合价值指数 (0-100)
    let valuePerception: Double
    let usageStickiness: Double
    let emotionalInvestment: Double
    let usageCost: Double // 单次/单日使用成本
}

struct ValueEvent: Identifiable {
    let id = UUID()
    let type: EventType
    let date: Date
    let timeLabel: String
    let title: String
    let description: String
    let icon: String
    let color: Color

    enum EventType {
        case breakthrough, peak, decline, milestone
    }
}

// MARK: - 图表绘制扩展
extension ValueJourneyChart {

    // MARK: - 数据计算
    private func calculateJourneyData() {
        var journeyPoints: [ValueJourneyPoint] = []
        var baseDataPoints: [ValuePointData] = []

        // 根据产品类型选择不同的计算策略
        if product.isVirtualProduct {
            // 虚拟产品（订阅）：使用订阅专用算法
            calculateSubscriptionJourneyData(&baseDataPoints)
        } else if product.valuationMethod == "daily" {
            // 实物产品按天数计算：使用实物产品专用算法
            calculatePhysicalProductJourneyData(&baseDataPoints)
        } else {
            // 按使用次数计算的物品
            calculateUsageJourneyData(&baseDataPoints)
        }

        guard !baseDataPoints.isEmpty else { return }

        let enhancedMetrics = ValuePointCalculator.calculateEnhancedMetrics(dataPoints: baseDataPoints, product: product)

        // 转换为价值旅程数据
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        for (index, enhanced) in enhancedMetrics.enumerated() {
            let basePoint = baseDataPoints[index]

            // 计算使用成本
            let usageCost = calculateUsageCostAtPoint(basePoint, index: index)

            // 使用改进的价值指数计算
            let valueIndex = calculateImprovedValueIndex(
                enhanced: enhanced,
                basePoint: basePoint,
                product: product
            )

            journeyPoints.append(ValueJourneyPoint(
                date: basePoint.date,
                label: dateFormatter.string(from: basePoint.date),
                valueIndex: valueIndex,
                valuePerception: enhanced.valuePerception * 100,
                usageStickiness: enhanced.usageStickiness * 100,
                emotionalInvestment: enhanced.emotionalInvestment * 100,
                usageCost: usageCost
            ))
        }

        self.chartData = journeyPoints
        self.selectedPoint = journeyPoints.last
        self.significantEvents = detectSignificantEvents(journeyPoints)
    }

    // MARK: - 改进的价值指数计算
    private func calculateImprovedValueIndex(
        enhanced: ValuePointCalculator.EnhancedValuePointData,
        basePoint: ValuePointData,
        product: Product
    ) -> Double {
        // 获取该时间点的生命周期阶段
        let daysSincePurchase = Calendar.current.dateComponents(
            [.day],
            from: product.purchaseDate ?? Date(),
            to: basePoint.date
        ).day ?? 0

        let stage = ProductLifecycleStage(daysSincePurchase: daysSincePurchase)
        let weights = stage.getWeights()

        // 应用动态权重计算价值指数
        let valueIndex = (enhanced.valuePerception * weights.satisfaction +
                         enhanced.usageStickiness * weights.usage +
                         enhanced.emotionalInvestment * weights.cost) * 100

        // 应用置信度调整
        let confidence = ConfidenceMetrics.calculate(for: product)
        let adjustedIndex = valueIndex * (0.7 + confidence.factor * 0.3) // 置信度影响30%

        return max(0, min(100, adjustedIndex))
    }

    // MARK: - 使用成本计算
    private func calculateUsageCostAtPoint(_ basePoint: ValuePointData, index: Int) -> Double {
        let totalCost = product.totalCostOfOwnership

        if product.valuationMethod == "daily" {
            // 按天计算：成本 = 总成本 / 累计天数
            guard let purchaseDate = product.purchaseDate else { return totalCost }
            let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: basePoint.date).day ?? 1
            return totalCost / max(1, Double(daysSincePurchase))
        } else {
            // 按次计算：成本 = 总成本 / 累计使用次数
            let cumulativeUsage = index + 1 // index从0开始，所以+1
            return totalCost / max(1, Double(cumulativeUsage))
        }
    }

    // MARK: - 事件检测
    private func detectSignificantEvents(_ points: [ValueJourneyPoint]) -> [ValueEvent] {
        guard points.count >= 3 else { return [] }

        var events: [ValueEvent] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        for i in 1..<points.count - 1 {
            let prev = points[i-1]
            let current = points[i]
            let next = points[i+1]

            // 检测价值突破点 (显著上升)
            if current.valueIndex > prev.valueIndex + 15 && current.valueIndex > next.valueIndex - 5 {
                events.append(ValueEvent(
                    type: .breakthrough,
                    date: current.date,
                    timeLabel: current.label,
                    title: "价值突破",
                    description: "您开始真正认识到这个产品的价值",
                    icon: "arrow.up.circle.fill",
                    color: .green
                ))
            }

            // 检测情感高峰
            if current.emotionalInvestment > 80 && current.emotionalInvestment > prev.emotionalInvestment + 10 {
                events.append(ValueEvent(
                    type: .peak,
                    date: current.date,
                    timeLabel: current.label,
                    title: "情感高峰",
                    description: "您与产品建立了深度的情感连接",
                    icon: "heart.fill",
                    color: .red
                ))
            }

            // 检测使用习惯形成
            if current.usageStickiness > 75 && prev.usageStickiness < 60 {
                events.append(ValueEvent(
                    type: .milestone,
                    date: current.date,
                    timeLabel: current.label,
                    title: "习惯养成",
                    description: "产品已经融入您的日常生活",
                    icon: "checkmark.circle.fill",
                    color: .blue
                ))
            }

            // 订阅产品特有事件检测
            if product.isVirtualProduct {
                // 检测订阅价值确认点
                if current.valueIndex > 70 && prev.valueIndex < 60 && current.usageStickiness > 60 {
                    events.append(ValueEvent(
                        type: .milestone,
                        date: current.date,
                        timeLabel: current.label,
                        title: "订阅价值确认",
                        description: "您已经充分体验到订阅服务的价值",
                        icon: "star.circle.fill",
                        color: .orange
                    ))
                }

                // 检测续费推荐点
                if current.valueIndex > 75 && current.emotionalInvestment > 70 {
                    events.append(ValueEvent(
                        type: .milestone,
                        date: current.date,
                        timeLabel: current.label,
                        title: "续费推荐",
                        description: "基于您的使用情况，建议续费此订阅",
                        icon: "arrow.clockwise.circle.fill",
                        color: .green
                    ))
                }
            }
        }

        return events.sorted { $0.date < $1.date }
    }

    // MARK: - 实物产品的数据处理（按天计算）
    private func calculatePhysicalProductJourneyData(_ baseDataPoints: inout [ValuePointData]) {
        guard let purchaseDate = product.purchaseDate else { return }

        let totalDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
        guard totalDays > 0 else { return }

        let interval = max(1, totalDays / 20) // 最多20个数据点
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        // 获取使用记录
        let records = product.usageRecords?.allObjects as? [UsageRecord] ?? []

        // 识别产品类型
        let productType = identifyProductType(product.name ?? "")

        for day in stride(from: 1, through: totalDays, by: interval) {
            let pointDate = Calendar.current.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()

            // 使用实物产品专用的满意度计算
            let satisfaction = calculatePhysicalProductSatisfaction(
                pointDate,
                records: records,
                productType: productType
            )

            baseDataPoints.append(ValuePointData(
                date: pointDate,
                label: dateFormatter.string(from: pointDate),
                costEffectiveness: 0, // 将被增强指标替换
                usageRate: 0, // 将被增强指标替换
                satisfaction: satisfaction
            ))
        }
    }

    // MARK: - 产品类型识别
    private func identifyProductType(_ productName: String) -> PhysicalProductType {
        let name = productName.lowercased()

        if name.contains("空调") || name.contains("冷气") || name.contains("暖气") {
            return .airConditioner
        } else if name.contains("洗衣机") || name.contains("烘干机") {
            return .washingMachine
        } else if name.contains("电视") || name.contains("显示器") || name.contains("投影") {
            return .television
        } else if name.contains("冰箱") || name.contains("冷柜") {
            return .refrigerator
        } else if name.contains("微波炉") || name.contains("烤箱") || name.contains("电饭煲") {
            return .kitchenAppliance
        } else if name.contains("手机") || name.contains("电脑") || name.contains("平板") {
            return .electronics
        } else {
            return .general
        }
    }

    // MARK: - 实物产品类型枚举
    enum PhysicalProductType {
        case airConditioner    // 空调 - 季节性强
        case washingMachine   // 洗衣机 - 日常使用
        case television       // 电视 - 娱乐设备
        case refrigerator     // 冰箱 - 持续运行
        case kitchenAppliance // 厨房电器
        case electronics      // 电子产品 - 技术更新快
        case general          // 一般产品

        var expectedLifespan: Double {
            switch self {
            case .airConditioner: return 10.0 // 10年
            case .washingMachine: return 12.0 // 12年
            case .television: return 8.0 // 8年
            case .refrigerator: return 15.0 // 15年
            case .kitchenAppliance: return 8.0 // 8年
            case .electronics: return 5.0 // 5年
            case .general: return 10.0 // 10年
            }
        }

        var hasSeasonality: Bool {
            switch self {
            case .airConditioner: return true
            default: return false
            }
        }
    }

    // MARK: - 订阅产品的数据处理
    private func calculateSubscriptionJourneyData(_ baseDataPoints: inout [ValuePointData]) {
        guard let purchaseDate = product.purchaseDate else { return }

        // 对于订阅产品，我们假设它是年订阅（可以后续扩展为用户输入）
        let subscriptionEndDate = Calendar.current.date(byAdding: .year, value: 1, to: purchaseDate) ?? Date()
        let totalDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: min(Date(), subscriptionEndDate)).day ?? 0

        guard totalDays > 0 else { return }

        let interval = max(1, totalDays / 20) // 最多20个数据点
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        // 获取使用记录
        let records = product.usageRecords?.allObjects as? [UsageRecord] ?? []

        for day in stride(from: 1, through: totalDays, by: interval) {
            let pointDate = Calendar.current.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()

            // 使用订阅专用的满意度计算
            let satisfaction = calculateSubscriptionSatisfaction(pointDate, records: records, totalDays: totalDays)

            baseDataPoints.append(ValuePointData(
                date: pointDate,
                label: dateFormatter.string(from: pointDate),
                costEffectiveness: 0, // 将被增强指标替换
                usageRate: 0, // 将被增强指标替换
                satisfaction: satisfaction
            ))
        }
    }

    // MARK: - 订阅产品满意度计算（混合价值模型）
    private func calculateSubscriptionSatisfaction(_ targetDate: Date, records: [UsageRecord], totalDays: Int) -> Double {
        guard let purchaseDate = product.purchaseDate else { return 3.0 }

        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: targetDate).day ?? 0
        let progress = Double(daysSincePurchase) / Double(totalDays)

        // 获取到目标日期为止的使用记录
        let relevantRecords = records.filter { record in
            guard let recordDate = record.date else { return false }
            return recordDate <= targetDate
        }

        if !relevantRecords.isEmpty {
            // 混合价值模型：可用性价值 + 使用体验价值 + 使用效率价值
            return calculateHybridSubscriptionValue(
                relevantRecords: relevantRecords,
                targetDate: targetDate,
                progress: progress,
                totalDays: totalDays
            )
        } else {
            // 没有使用记录时，基于订阅阶段估算
            return getSubscriptionPhaseSatisfaction(progress)
        }
    }

    // MARK: - 混合价值模型计算
    private func calculateHybridSubscriptionValue(
        relevantRecords: [UsageRecord],
        targetDate: Date,
        progress: Double,
        totalDays: Int
    ) -> Double {

        // 1. 基础可用性价值（订阅阶段基础满意度）
        let availabilityValue = getSubscriptionPhaseSatisfaction(progress)

        // 2. 实际使用体验价值（时间加权的满意度）
        let usageValue = calculateTimeWeightedSatisfaction(relevantRecords, targetDate: targetDate)

        // 3. 使用效率价值（使用密度和频率）
        let efficiencyValue = calculateUsageEfficiency(relevantRecords, totalDays: totalDays, targetDate: targetDate)

        // 4. 动态权重计算
        let weights = calculateDynamicWeights(
            usageCount: relevantRecords.count,
            totalDays: totalDays,
            targetDate: targetDate
        )

        // 5. 混合价值计算
        let hybridValue = availabilityValue * weights.availability +
                         usageValue * weights.usage +
                         efficiencyValue * weights.efficiency

        return max(1.0, min(5.0, hybridValue))
    }

    // MARK: - 时间加权满意度计算
    private func calculateTimeWeightedSatisfaction(_ records: [UsageRecord], targetDate: Date) -> Double {
        guard !records.isEmpty else { return 3.0 }

        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        var weightedSum = 0.0
        var totalWeight = 0.0

        for record in sortedRecords {
            guard let recordDate = record.date else { continue }

            // 计算时间权重：最近的记录权重更高
            let daysSinceRecord = Calendar.current.dateComponents([.day], from: recordDate, to: targetDate).day ?? 0
            let timeWeight = exp(-Double(daysSinceRecord) / 30.0) // 30天衰减期

            weightedSum += Double(record.satisfaction) * timeWeight
            totalWeight += timeWeight
        }

        return totalWeight > 0 ? weightedSum / totalWeight : 3.0
    }

    // MARK: - 使用效率计算
    private func calculateUsageEfficiency(_ records: [UsageRecord], totalDays: Int, targetDate: Date) -> Double {
        guard !records.isEmpty, totalDays > 0 else { return 2.0 }

        guard let purchaseDate = product.purchaseDate else { return 2.0 }
        let elapsedDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: targetDate).day ?? 1

        // 1. 使用密度：实际使用天数 / 已过天数
        let uniqueUsageDays = Set(records.compactMap { record -> Date? in
            guard let date = record.date else { return nil }
            return Calendar.current.startOfDay(for: date)
        }).count

        let usageDensity = Double(uniqueUsageDays) / Double(elapsedDays)

        // 2. 使用频率评估
        let usageFrequency = Double(records.count) / Double(elapsedDays) * 30.0 // 月使用次数
        let frequencyScore = min(1.0, usageFrequency / 10.0) // 假设月使用10次为满分

        // 3. 使用分布均匀性
        let distributionScore = calculateUsageDistribution(records, elapsedDays: elapsedDays)

        // 4. 综合效率评分
        let efficiencyScore = (usageDensity * 0.4 + frequencyScore * 0.4 + distributionScore * 0.2) * 5.0

        return max(1.0, min(5.0, efficiencyScore))
    }

    // MARK: - 使用分布均匀性计算
    private func calculateUsageDistribution(_ records: [UsageRecord], elapsedDays: Int) -> Double {
        guard records.count >= 3, elapsedDays > 7 else { return 0.5 }

        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        var intervals: [Double] = []

        for i in 1..<sortedRecords.count {
            let prevDate = sortedRecords[i-1].date ?? Date()
            let currDate = sortedRecords[i].date ?? Date()
            let interval = currDate.timeIntervalSince(prevDate) / (24 * 3600) // 转换为天数
            intervals.append(interval)
        }

        // 计算间隔的标准差，标准差越小，分布越均匀
        let meanInterval = intervals.reduce(0, +) / Double(intervals.count)
        let variance = intervals.reduce(0) { sum, interval in
            sum + pow(interval - meanInterval, 2)
        } / Double(intervals.count)
        let standardDeviation = sqrt(variance)

        // 标准差越小，分布越均匀，得分越高
        let maxExpectedStdDev = Double(elapsedDays) / 4.0 // 期望的最大标准差
        let distributionScore = max(0.0, 1.0 - standardDeviation / maxExpectedStdDev)

        return distributionScore
    }

    // MARK: - 动态权重计算
    private func calculateDynamicWeights(usageCount: Int, totalDays: Int, targetDate: Date) -> (availability: Double, usage: Double, efficiency: Double) {

        let usageFrequency = Double(usageCount) / Double(totalDays) * 30.0 // 月使用频率

        switch usageFrequency {
        case 0..<2:
            // 低频用户：重视可用性价值
            return (availability: 0.6, usage: 0.25, efficiency: 0.15)

        case 2..<8:
            // 中频用户：平衡各种价值
            return (availability: 0.35, usage: 0.45, efficiency: 0.2)

        case 8..<20:
            // 高频用户：重视使用体验
            return (availability: 0.2, usage: 0.6, efficiency: 0.2)

        default:
            // 超高频用户：重视使用体验和效率
            return (availability: 0.15, usage: 0.55, efficiency: 0.3)
        }
    }

    // MARK: - 订阅阶段满意度模型
    private func getSubscriptionPhaseSatisfaction(_ progress: Double) -> Double {
        // 订阅产品的典型满意度曲线
        switch progress {
        case 0..<0.1:
            // 蜜月期：高期待，高满意度
            return 4.2 + sin(progress * .pi * 10) * 0.3

        case 0.1..<0.3:
            // 适应期：可能有失望，满意度下降
            let adaptationProgress = (progress - 0.1) / 0.2
            return 3.8 - adaptationProgress * 0.8 + sin(adaptationProgress * .pi * 2) * 0.2

        case 0.3..<0.7:
            // 稳定期：习惯使用，满意度相对稳定
            let stableProgress = (progress - 0.3) / 0.4
            return 3.2 + sin(stableProgress * .pi * 3) * 0.3

        case 0.7..<0.9:
            // 续费考虑期：开始评估价值，可能有波动
            let considerationProgress = (progress - 0.7) / 0.2
            return 3.0 + considerationProgress * 0.5 + sin(considerationProgress * .pi * 4) * 0.4

        default:
            // 临期：如果还在使用，说明认可价值
            return 3.8 + sin(progress * .pi * 8) * 0.2
        }
    }

    // MARK: - 按天数计算的数据处理
    private func calculateDailyJourneyData(_ baseDataPoints: inout [ValuePointData]) {
        guard let purchaseDate = product.purchaseDate else { return }

        let totalDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
        guard totalDays > 0 else { return }

        let interval = max(1, totalDays / 20) // 最多20个数据点
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        // 获取使用记录以计算满意度
        let records = product.usageRecords?.allObjects as? [UsageRecord] ?? []
        let recordsByDate = Dictionary(grouping: records) { record in
            Calendar.current.startOfDay(for: record.date ?? Date())
        }

        for day in stride(from: 1, through: totalDays, by: interval) {
            let pointDate = Calendar.current.date(byAdding: .day, value: day, to: purchaseDate) ?? Date()
            let dayStart = Calendar.current.startOfDay(for: pointDate)

            // 计算到该日期为止的平均满意度
            let satisfaction = calculateSatisfactionUpToDate(pointDate, records: records)

            baseDataPoints.append(ValuePointData(
                date: pointDate,
                label: dateFormatter.string(from: pointDate),
                costEffectiveness: 0, // 将被增强指标替换
                usageRate: 0, // 将被增强指标替换
                satisfaction: satisfaction
            ))
        }
    }

    // MARK: - 按使用次数计算的数据处理
    private func calculateUsageJourneyData(_ baseDataPoints: inout [ValuePointData]) {
        guard let records = product.usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else { return }

        let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
        var cumulativeUsage = 0
        var satisfactionSum = 0.0
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"

        for record in sortedRecords {
            cumulativeUsage += 1
            satisfactionSum += Double(record.satisfaction)
            let avgSatisfaction = satisfactionSum / Double(cumulativeUsage)

            baseDataPoints.append(ValuePointData(
                date: record.date ?? Date(),
                label: dateFormatter.string(from: record.date ?? Date()),
                costEffectiveness: 0, // 将被增强指标替换
                usageRate: 0, // 将被增强指标替换
                satisfaction: avgSatisfaction
            ))
        }
    }

    // MARK: - 计算到指定日期的满意度
    private func calculateSatisfactionUpToDate(_ targetDate: Date, records: [UsageRecord]) -> Double {
        let relevantRecords = records.filter { record in
            guard let recordDate = record.date else { return false }
            return recordDate <= targetDate
        }

        if relevantRecords.isEmpty {
            // 如果没有使用记录，返回一个基于时间的估算满意度
            return estimateSatisfactionForDailyProduct(targetDate)
        }

        let totalSatisfaction = relevantRecords.reduce(0.0) { sum, record in
            sum + Double(record.satisfaction)
        }

        return totalSatisfaction / Double(relevantRecords.count)
    }

    // MARK: - 为按天计算的产品估算满意度
    private func estimateSatisfactionForDailyProduct(_ date: Date) -> Double {
        guard let purchaseDate = product.purchaseDate else { return 3.0 }

        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: date).day ?? 0
        let totalDays = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 1

        // 智能满意度估算模型 - 基于产品生命周期
        let progress = Double(daysSincePurchase) / Double(totalDays)

        // 不同阶段的满意度模式
        let satisfaction: Double

        if progress < 0.1 {
            // 蜜月期 (前10%) - 高满意度
            satisfaction = 4.5 - progress * 0.5
        } else if progress < 0.3 {
            // 适应期 (10%-30%) - 满意度可能下降
            let adaptationProgress = (progress - 0.1) / 0.2
            satisfaction = 4.0 - adaptationProgress * 1.0 + sin(adaptationProgress * .pi) * 0.3
        } else if progress < 0.7 {
            // 稳定期 (30%-70%) - 相对稳定
            let stableProgress = (progress - 0.3) / 0.4
            satisfaction = 3.2 + sin(stableProgress * .pi * 2) * 0.4
        } else {
            // 成熟期 (70%+) - 可能疲劳或深度依赖
            let matureProgress = (progress - 0.7) / 0.3
            let baseValue = 3.0 + (1.0 - matureProgress) * 0.5
            satisfaction = baseValue + sin(matureProgress * .pi * 3) * 0.2
        }

        // 添加基于产品价格的满意度调整
        let priceAdjustment = min(0.5, product.price / 1000.0 * 0.3) // 价格越高，期望越高
        let adjustedSatisfaction = satisfaction + priceAdjustment

        return max(1.0, min(5.0, adjustedSatisfaction))
    }

    // MARK: - 实物产品满意度计算
    private func calculatePhysicalProductSatisfaction(
        _ targetDate: Date,
        records: [UsageRecord],
        productType: PhysicalProductType
    ) -> Double {
        guard let purchaseDate = product.purchaseDate else { return 3.0 }

        let daysSincePurchase = Calendar.current.dateComponents([.day], from: purchaseDate, to: targetDate).day ?? 0
        let yearsOfUse = Double(daysSincePurchase) / 365.0

        // 获取到目标日期为止的使用记录
        let relevantRecords = records.filter { record in
            guard let recordDate = record.date else { return false }
            return recordDate <= targetDate
        }

        // 1. 基础生命周期满意度
        let lifecycleSatisfaction = getPhysicalProductLifecycleSatisfaction(yearsOfUse, productType: productType)

        // 2. 折旧影响
        let depreciationFactor = calculateDepreciationFactor(yearsOfUse, productType: productType)

        // 3. 季节性调整
        let seasonalAdjustment = getSeasonalAdjustment(targetDate, productType: productType)

        // 4. 实际使用记录的影响
        let usageInfluence = calculateUsageInfluence(relevantRecords, yearsOfUse: yearsOfUse)

        // 5. 综合计算
        let baseSatisfaction = lifecycleSatisfaction * depreciationFactor * seasonalAdjustment
        let finalSatisfaction = baseSatisfaction * 0.6 + usageInfluence * 0.4

        return max(1.0, min(5.0, finalSatisfaction))
    }

    // MARK: - 实物产品生命周期满意度
    private func getPhysicalProductLifecycleSatisfaction(_ yearsOfUse: Double, productType: PhysicalProductType) -> Double {
        switch yearsOfUse {
        case 0..<0.25:
            // 新品期 (0-3个月)：学习使用，满意度逐步上升
            return 3.8 + yearsOfUse * 4 * 0.6

        case 0.25..<1.0:
            // 蜜月期 (3个月-1年)：最高满意度期
            let progress = (yearsOfUse - 0.25) / 0.75
            return 4.4 + sin(progress * .pi) * 0.4

        case 1.0..<3.0:
            // 稳定期 (1-3年)：日常使用，满意度稳定
            let progress = (yearsOfUse - 1.0) / 2.0
            return 4.2 - progress * 0.3 + sin(progress * .pi * 2) * 0.2

        case 3.0..<7.0:
            // 成熟期 (3-7年)：功能稳定，可能有小问题
            let progress = (yearsOfUse - 3.0) / 4.0
            return 3.8 - progress * 0.4 + sin(progress * .pi * 3) * 0.15

        default:
            // 老化期 (7年+)：考虑更换，满意度可能下降
            let progress = min(1.0, (yearsOfUse - 7.0) / 3.0)
            return 3.2 - progress * 0.7 + sin(progress * .pi * 2) * 0.1
        }
    }

    // MARK: - 折旧系数计算
    private func calculateDepreciationFactor(_ yearsOfUse: Double, productType: PhysicalProductType) -> Double {
        let expectedLifespan = productType.expectedLifespan
        let depreciationRate = yearsOfUse / expectedLifespan

        // 不同产品类型的折旧曲线
        switch productType {
        case .electronics:
            // 电子产品：技术更新快，折旧较快
            return max(0.6, 1.0 - depreciationRate * 0.8)

        case .airConditioner, .refrigerator:
            // 大家电：折旧相对较慢
            return max(0.7, 1.0 - depreciationRate * 0.5)

        default:
            // 一般产品：中等折旧速度
            return max(0.65, 1.0 - depreciationRate * 0.6)
        }
    }

    // MARK: - 季节性调整
    private func getSeasonalAdjustment(_ date: Date, productType: PhysicalProductType) -> Double {
        guard productType.hasSeasonality else { return 1.0 }

        let calendar = Calendar.current
        let month = calendar.component(.month, from: date)

        switch productType {
        case .airConditioner:
            // 空调的季节性调整
            switch month {
            case 6, 7, 8: return 1.3 // 夏季高峰
            case 12, 1, 2: return 1.1 // 冬季使用
            case 5, 9: return 1.0 // 过渡期
            default: return 0.7 // 春秋季较少使用
            }

        default:
            return 1.0
        }
    }

    // MARK: - 使用记录影响计算
    private func calculateUsageInfluence(_ records: [UsageRecord], yearsOfUse: Double) -> Double {
        guard !records.isEmpty else {
            // 没有使用记录时，基于产品年限估算
            return max(2.5, 4.0 - yearsOfUse * 0.2)
        }

        // 计算时间加权的满意度
        let recentWeight = 0.7
        let historicalWeight = 0.3

        let sortedRecords = records.sorted { ($0.date ?? Date()) > ($1.date ?? Date()) }
        let recentRecords = Array(sortedRecords.prefix(3)) // 最近3条记录
        let allRecordsAvg = records.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(records.count)

        if !recentRecords.isEmpty {
            let recentAvg = recentRecords.reduce(0.0) { $0 + Double($1.satisfaction) } / Double(recentRecords.count)
            return recentAvg * recentWeight + allRecordsAvg * historicalWeight
        } else {
            return allRecordsAvg
        }
    }

    // MARK: - 图表绘制
    private func journeyDataToPoints(size: CGSize) -> [CGPoint] {
        guard !chartData.isEmpty else { return [] }

        let maxValue = chartData.map { $0.valueIndex }.max() ?? 100
        let minValue = chartData.map { $0.valueIndex }.min() ?? 0
        let valueRange = max(maxValue - minValue, 20) // 最小范围20

        return chartData.enumerated().map { index, point in
            let x = CGFloat(index) / CGFloat(max(chartData.count - 1, 1)) * size.width
            let normalizedValue = (point.valueIndex - minValue) / valueRange
            let y = size.height - (CGFloat(normalizedValue) * size.height * 0.8 + size.height * 0.1)
            return CGPoint(x: x, y: y)
        }
    }

    private func costDataToPoints(size: CGSize) -> [CGPoint] {
        guard !chartData.isEmpty else { return [] }

        let maxCost = chartData.map { $0.usageCost }.max() ?? 1
        let minCost = chartData.map { $0.usageCost }.min() ?? 0
        let costRange = max(maxCost - minCost, 1) // 最小范围1

        return chartData.enumerated().map { index, point in
            let x = CGFloat(index) / CGFloat(max(chartData.count - 1, 1)) * size.width
            // 成本曲线：成本越高，图表位置越高（呈现下降趋势）
            let normalizedCost = (point.usageCost - minCost) / costRange
            let y = size.height - (CGFloat(normalizedCost) * size.height * 0.8 + size.height * 0.1)
            return CGPoint(x: x, y: y)
        }
    }

    private func gridBackground(size: CGSize) -> some View {
        Path { path in
            // 水平网格线
            for i in 0...4 {
                let y = CGFloat(i) * size.height / 4
                path.move(to: CGPoint(x: 0, y: y))
                path.addLine(to: CGPoint(x: size.width, y: y))
            }

            // 垂直网格线
            for i in 0...4 {
                let x = CGFloat(i) * size.width / 4
                path.move(to: CGPoint(x: x, y: 0))
                path.addLine(to: CGPoint(x: x, y: size.height))
            }
        }
        .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
    }

    private func drawCostCurve(points: [CGPoint], size: CGSize) -> some View {
        Group {
            if !points.isEmpty {
                // 成本曲线（绿色，无填充）
                Path { path in
                    if let firstPoint = points.first {
                        path.move(to: firstPoint)

                        for i in 1..<points.count {
                            let currentPoint = points[i-1]
                            let nextPoint = points[i]

                            let controlPoint1 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: currentPoint.y
                            )
                            let controlPoint2 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: nextPoint.y
                            )

                            path.addCurve(to: nextPoint, control1: controlPoint1, control2: controlPoint2)
                        }
                    }
                }
                .trim(from: 0, to: animationProgress)
                .stroke(
                    Color.green,
                    style: StrokeStyle(lineWidth: 2.5, lineCap: .round, lineJoin: .round, dash: [5, 3])
                )
                .shadow(color: Color.green.opacity(0.3), radius: 2, x: 0, y: 1)
            }
        }
    }

    // 绘制价值平衡点
    private func drawValueBalancePoint(valuePoints: [CGPoint], costPoints: [CGPoint], size: CGSize) -> some View {
        Group {
            if let balancePoint = findValueBalancePoint(valuePoints: valuePoints, costPoints: costPoints) {
                ZStack {
                    // 平衡点标记
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [Color.yellow, Color.orange]),
                                center: .center,
                                startRadius: 2,
                                endRadius: 8
                            )
                        )
                        .frame(width: 16, height: 16)
                        .position(balancePoint.point)
                        .scaleEffect(animationProgress)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animationProgress)

                    // 平衡点光环效果
                    Circle()
                        .stroke(Color.yellow.opacity(0.6), lineWidth: 2)
                        .frame(width: 24, height: 24)
                        .position(balancePoint.point)
                        .scaleEffect(animationProgress * 1.2)
                        .opacity(0.8)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animationProgress)

                    // 平衡点标签
                    VStack(spacing: 2) {
                        Text("🎯")
                            .font(.caption)
                        Text("价值平衡点")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(4)
                    }
                    .position(x: balancePoint.point.x, y: max(20, balancePoint.point.y - 30))
                    .opacity(animationProgress)
                }
            }
        }
    }

    // 查找价值平衡点 - V4 算法: 基于渲染曲线的精确查找
    private func findValueBalancePoint(valuePoints: [CGPoint], costPoints: [CGPoint]) -> (point: CGPoint, index: Int)? {
        guard valuePoints.count == costPoints.count, valuePoints.count >= 2 else { return nil }

        // 1. 生成高保真度的曲线点，模拟实际渲染的Bézier曲线
        let curveValuePoints = interpolateOnCurve(points: valuePoints)
        let curveCostPoints = interpolateOnCurve(points: costPoints)

        let pointCount = min(curveValuePoints.count, curveCostPoints.count)

        // 2. 遍历曲线上的点，寻找第一个价值高于成本的穿越点
        for i in 1..<pointCount {
            let prevValueY = curveValuePoints[i-1].y
            let prevCostY = curveCostPoints[i-1].y
            let currValueY = curveValuePoints[i].y
            let currCostY = curveCostPoints[i].y

            // 3. 检查是否为“向上穿越”
            if prevValueY >= prevCostY && currValueY <= currCostY {
                // 4. 这是一个穿越点。通过线性插值找到更精确的交点位置
                let valueDiff1 = prevValueY - prevCostY // 差值 >= 0
                let valueDiff2 = currValueY - currCostY // 差值 <= 0

                // 避免除以零
                if (valueDiff1 - valueDiff2) == 0 {
                    let originalIndex = findOriginalIndex(for: i, interpolatedCount: pointCount, originalCount: valuePoints.count)
                    return (curveValuePoints[i], originalIndex)
                }

                // 计算交点在线段上的比例
                let t = valueDiff1 / (valueDiff1 - valueDiff2)

                // 根据比例t，计算精确的交点坐标
                let intersectionX = curveValuePoints[i-1].x + t * (curveValuePoints[i].x - curveValuePoints[i-1].x)
                let intersectionY = curveValuePoints[i-1].y + t * (curveValuePoints[i].y - curveValuePoints[i-1].y)
                let intersectionPoint = CGPoint(x: intersectionX, y: intersectionY)

                // 5. 映射回原始数据索引并返回，确保只取第一个交点
                let originalIndex = findOriginalIndex(for: i, interpolatedCount: pointCount, originalCount: valuePoints.count)
                return (intersectionPoint, originalIndex)
            }
        }

        return nil // 未找到穿越点
    }

    // 辅助函数：将插值点索引映射回原始数据点索引
    private func findOriginalIndex(for interpolatedIndex: Int, interpolatedCount: Int, originalCount: Int) -> Int {
        let ratio = Double(interpolatedIndex) / Double(interpolatedCount)
        let originalIdx = Int(round(ratio * Double(originalCount - 1)))
        return min(originalIdx, originalCount - 1)
    }

    // 辅助函数：在Bézier曲线上进行插值，生成高保真点集
    private func interpolateOnCurve(points: [CGPoint], factor: Int = 20) -> [CGPoint] {
        guard points.count >= 2 else { return points }
        
        var curvePoints: [CGPoint] = []
        
        for i in 0..<points.count - 1 {
            let p0 = points[i]
            let p3 = points[i+1]
            
            // 控制点必须与 `drawValueJourney` 和 `drawCostCurve` 中的定义完全一致
            let p1 = CGPoint(x: p0.x + (p3.x - p0.x) * 0.5, y: p0.y)
            let p2 = CGPoint(x: p0.x + (p3.x - p0.x) * 0.5, y: p3.y)
            
            if i == 0 {
                curvePoints.append(p0)
            }

            // 沿当前Bézier曲线段生成点
            for j in 1...factor {
                let t = CGFloat(j) / CGFloat(factor)
                let oneMinusT = 1.0 - t
                
                let x = oneMinusT * oneMinusT * oneMinusT * p0.x +
                        3 * oneMinusT * oneMinusT * t * p1.x +
                        3 * oneMinusT * t * t * p2.x +
                        t * t * t * p3.x
                
                let y = oneMinusT * oneMinusT * oneMinusT * p0.y +
                        3 * oneMinusT * oneMinusT * t * p1.y +
                        3 * oneMinusT * t * t * p2.y +
                        t * t * t * p3.y
                
                curvePoints.append(CGPoint(x: x, y: y))
            }
        }
        
        return curvePoints
    }

    

    private func drawValueJourney(points: [CGPoint], size: CGSize) -> some View {
        Group {
            if !points.isEmpty {
                // 渐变填充区域
                Path { path in
                    if let firstPoint = points.first {
                        path.move(to: CGPoint(x: firstPoint.x, y: size.height))
                        path.addLine(to: firstPoint)

                        for i in 1..<points.count {
                            let currentPoint = points[i-1]
                            let nextPoint = points[i]

                            // 使用平滑曲线连接
                            let controlPoint1 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: currentPoint.y
                            )
                            let controlPoint2 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: nextPoint.y
                            )

                            path.addCurve(to: nextPoint, control1: controlPoint1, control2: controlPoint2)
                        }

                        if let lastPoint = points.last {
                            path.addLine(to: CGPoint(x: lastPoint.x, y: size.height))
                        }
                        path.closeSubpath()
                    }
                }
                .fill(LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.3),
                        Color.green.opacity(0.2),
                        Color.green.opacity(0.1),
                        .clear
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .scaleEffect(y: animationProgress)

                // 主曲线
                Path { path in
                    if let firstPoint = points.first {
                        path.move(to: firstPoint)

                        for i in 1..<points.count {
                            let currentPoint = points[i-1]
                            let nextPoint = points[i]

                            let controlPoint1 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: currentPoint.y
                            )
                            let controlPoint2 = CGPoint(
                                x: currentPoint.x + (nextPoint.x - currentPoint.x) * 0.5,
                                y: nextPoint.y
                            )

                            path.addCurve(to: nextPoint, control1: controlPoint1, control2: controlPoint2)
                        }
                    }
                }
                .trim(from: 0, to: animationProgress)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [.blue, .green]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ),
                    style: StrokeStyle(lineWidth: 3.0, lineCap: .round, lineJoin: .round)
                )
                .shadow(color: Color.blue.opacity(0.3), radius: 2, x: 0, y: 1)
            }
        }
    }

    private func drawEventMarkers(points: [CGPoint], size: CGSize) -> some View {
        Group {
            ForEach(significantEvents) { event in
                if let eventIndex = chartData.firstIndex(where: { Calendar.current.isDate($0.date, inSameDayAs: event.date) }),
                   eventIndex < points.count {
                    let point = points[eventIndex]

                    ZStack {
                        // 外圈光晕
                        Circle()
                            .fill(event.color.opacity(0.3))
                            .frame(width: 20, height: 20)
                            .scaleEffect(animationProgress)

                        // 内圈标记
                        Image(systemName: event.icon)
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .background(
                                Circle()
                                    .fill(event.color)
                                    .frame(width: 16, height: 16)
                                    .shadow(color: event.color.opacity(0.5), radius: 2, x: 0, y: 1)
                            )
                    }
                    .position(point)
                    .scaleEffect(animationProgress)
                }
            }
        }
    }

    private func interactionOverlay(points: [CGPoint], size: CGSize) -> some View {
        Rectangle()
            .fill(Color.clear)
            .contentShape(Rectangle())
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        let location = value.location
                        if let nearestIndex = findNearestPointIndex(to: location, in: points) {
                            selectedPoint = chartData[nearestIndex]
                        }
                    }
            )
    }

    private func findNearestPointIndex(to location: CGPoint, in points: [CGPoint]) -> Int? {
        guard !points.isEmpty else { return nil }

        var nearestIndex = 0
        var minDistance = CGFloat.greatestFiniteMagnitude

        for (index, point) in points.enumerated() {
            let distance = sqrt(pow(point.x - location.x, 2) + pow(point.y - location.y, 2))
            if distance < minDistance {
                minDistance = distance
                nearestIndex = index
            }
        }

        return nearestIndex
    }

    private var timeAxisLabels: some View {
        HStack {
            if let first = chartData.first {
                Text(first.label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if chartData.count > 2 {
                Text(chartData[chartData.count / 2].label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if let last = chartData.last {
                Text(last.label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal)
    }

    // MARK: - 描述文本生成
    private func getPerceptionDescription(_ value: Double) -> String {
        switch value {
        case 80...: return "非常认可"
        case 60..<80: return "比较满意"
        case 40..<60: return "一般般"
        case 20..<40: return "不太满意"
        default: return "很失望"
        }
    }

    private func getStickinessDescription(_ value: Double) -> String {
        switch value {
        case 80...: return "深度依赖"
        case 60..<80: return "经常使用"
        case 40..<60: return "偶尔使用"
        case 20..<40: return "很少使用"
        default: return "几乎不用"
        }
    }

    private func getEmotionalDescription(_ value: Double) -> String {
        switch value {
        case 80...: return "深度喜爱"
        case 60..<80: return "比较喜欢"
        case 40..<60: return "感觉还行"
        case 20..<40: return "不太喜欢"
        default: return "很不喜欢"
        }
    }

    private func getCostDescription(_ cost: Double) -> String {
        let totalCost = product.totalCostOfOwnership
        let costRatio = cost / totalCost

        switch costRatio {
        case 0..<0.01: return "极低成本"
        case 0.01..<0.05: return "很低成本"
        case 0.05..<0.1: return "较低成本"
        case 0.1..<0.2: return "中等成本"
        case 0.2..<0.5: return "较高成本"
        default: return "高成本"
        }
    }
}

// MARK: - 预览
#Preview("按天数计算") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "健身会员卡"
    sampleProduct.price = 1200.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-45 * 24 * 3600) // 45天前
    sampleProduct.valuationMethod = "daily"

    // 添加一些使用记录
    for i in [5, 12, 20, 28, 35] {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(45-i) * 24 * 3600)
        record.satisfaction = Int16([4, 5, 4, 3, 4][i/7 % 5])
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

#Preview("按次数计算") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "蓝牙耳机"
    sampleProduct.price = 299.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-30 * 24 * 3600)
    sampleProduct.valuationMethod = "usage"

    // 添加使用记录
    for i in 0..<8 {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(30-i*3) * 24 * 3600)
        record.satisfaction = Int16([3, 4, 4, 5, 5, 4, 4, 3][i])
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

#Preview("订阅产品 - 混合计算") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "Netflix订阅"
    sampleProduct.price = 98.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-30 * 24 * 3600) // 30天前
    sampleProduct.valuationMethod = "daily"
    sampleProduct.isVirtualProduct = true // 标记为虚拟产品

    // 模拟真实的使用模式：不是每天都用，但有规律的使用记录
    let usageDays = [2, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28] // 11次使用
    for (index, day) in usageDays.enumerated() {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(30-day) * 24 * 3600)
        // 满意度有变化：开始一般，中期很好，后期稳定
        let satisfactionPattern = [3, 4, 4, 5, 5, 5, 4, 4, 4, 3, 4]
        record.satisfaction = Int16(satisfactionPattern[index])
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

#Preview("订阅产品 - 低频使用") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "Adobe Creative Suite"
    sampleProduct.price = 888.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-30 * 24 * 3600) // 30天前
    sampleProduct.valuationMethod = "daily"
    sampleProduct.isVirtualProduct = true

    // 低频但高质量的使用：专业软件的典型使用模式
    let usageDays = [3, 10, 17, 24] // 只有4次使用
    for (index, day) in usageDays.enumerated() {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(30-day) * 24 * 3600)
        // 每次使用都很满意，因为是专业需求
        record.satisfaction = Int16([5, 5, 4, 5][index])
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

#Preview("实物产品 - 空调") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "格力空调"
    sampleProduct.price = 3500.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-18 * 30 * 24 * 3600) // 18个月前
    sampleProduct.valuationMethod = "daily"
    sampleProduct.isVirtualProduct = false // 实物产品

    // 添加一些季节性使用记录
    let usageMonths = [6, 7, 8, 12, 1, 6, 7, 8] // 夏季和冬季使用
    for (index, monthsAgo) in usageMonths.enumerated() {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(monthsAgo * 30) * 24 * 3600)
        // 夏季满意度更高
        let satisfaction = monthsAgo <= 2 ? [5, 5, 4, 4, 4, 5, 5, 4] : [4, 4, 3, 3, 3, 4, 4, 3]
        record.satisfaction = Int16(satisfaction[index % satisfaction.count])
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}

#Preview("实物产品 - 洗衣机") {
    let context = PersistenceController.preview.container.viewContext
    let sampleProduct = Product(context: context)
    sampleProduct.name = "海尔洗衣机"
    sampleProduct.price = 2800.0
    sampleProduct.purchaseDate = Date().addingTimeInterval(-3 * 365 * 24 * 3600) // 3年前
    sampleProduct.valuationMethod = "daily"
    sampleProduct.isVirtualProduct = false // 实物产品

    // 添加一些使用记录，模拟长期使用的满意度变化
    let usagePattern = [
        (months: 1, satisfaction: 4),   // 初期学习
        (months: 6, satisfaction: 5),   // 蜜月期
        (months: 12, satisfaction: 5),  // 稳定期开始
        (months: 18, satisfaction: 4),  // 稳定期
        (months: 24, satisfaction: 4),  // 继续稳定
        (months: 30, satisfaction: 3),  // 开始有小问题
        (months: 36, satisfaction: 3)   // 当前状态
    ]

    for pattern in usagePattern {
        let record = UsageRecord(context: context)
        record.date = Date().addingTimeInterval(-Double(pattern.months * 30) * 24 * 3600)
        record.satisfaction = Int16(pattern.satisfaction)
        record.product = sampleProduct
    }

    return ValueJourneyChart(product: sampleProduct)
        .padding()
        .background(Color(UIColor.systemBackground))
}
