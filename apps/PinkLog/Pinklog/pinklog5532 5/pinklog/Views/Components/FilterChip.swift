import SwiftUI

// MARK: - 筛选芯片组件
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let count: Int
    var color: Color = .blue
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .regular)
                
                if count > 0 {
                    Text("\(count)")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(isSelected ? .white : color.opacity(0.2))
                        )
                        .foregroundColor(isSelected ? color : color)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(isSelected ? color : Color(.systemGray6))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    VStack {
        FilterChip(
            title: "示例",
            isSelected: false,
            count: 5,
            color: .blue,
            action: {}
        )
        
        FilterChip(
            title: "选中",
            isSelected: true,
            count: 3,
            color: .orange,
            action: {}
        )
    }
    .padding()
} 