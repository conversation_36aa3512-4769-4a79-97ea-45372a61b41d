import SwiftUI

struct GlassBackground: View {
    var tint: Color = .white
    var intensity: CGFloat = 0.2
    var blurRadius: CGFloat = 10
    var cornerRadius: CGFloat = AppSizes.cornerRadius
    
    var body: some View {
        ZStack {
            // 磨砂背景
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(tint.opacity(intensity))
                .background(
                    BlurEffect(style: .systemThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                )
            
            // 轻微光泽边框
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            tint.opacity(0.6),
                            tint.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 0.8
                )
        }
    }
}

struct BlurEffect: UIViewRepresentable {
    var style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        return UIVisualEffectView(effect: UIBlurEffect(style: style))
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {
        uiView.effect = UIBlurEffect(style: style)
    }
}

// 预览
#Preview {
    ZStack {
        LinearGradient(
            gradient: Gradient(colors: [Color.pink.opacity(0.3), Color.purple.opacity(0.3)]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        VStack(spacing: 20) {
            GlassBackground(tint: .pink, intensity: 0.15)
                .frame(width: 300, height: 180)
            
            GlassBackground(tint: .blue, intensity: 0.1, cornerRadius: 20)
                .frame(width: 300, height: 180)
            
            GlassBackground(tint: .purple, intensity: 0.2, cornerRadius: 15)
                .frame(width: 300, height: 180)
        }
    }
} 