//
//  Product3DView.swift
//  PinkLog
//
//  Created by World-Class Designer on 2025/1/17.
//  震撼的3D产品展示
//

import SwiftUI
import UIKit
import SceneKit

struct Product3DView: UIViewRepresentable {
    let productImage: UIImage
    @Binding var rotation: Float
    @State private var sceneView: SCNView!
    
    func makeUIView(context: Context) -> SCNView {
        let sceneView = SCNView()
        sceneView.scene = createMagical3DScene()
        sceneView.backgroundColor = UIColor.clear
        sceneView.allowsCameraControl = true
        sceneView.showsStatistics = false
        sceneView.autoenablesDefaultLighting = true
        
        // 添加手势识别
        let panGesture = UIPanGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handlePan(_:)))
        sceneView.addGestureRecognizer(panGesture)
        
        self.sceneView = sceneView
        return sceneView
    }
    
    func updateUIView(_ uiView: SCNView, context: Context) {
        if let productNode = uiView.scene?.rootNode.childNode(withName: "product", recursively: true) {
            productNode.rotation = SCNVector4(0, 1, 0, rotation)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        var parent: Product3DView
        
        init(_ parent: Product3DView) {
            self.parent = parent
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            let translation = gesture.translation(in: gesture.view)
            parent.rotation += Float(translation.x) * 0.01
            gesture.setTranslation(.zero, in: gesture.view)
            
            // 触觉反馈
            if gesture.state == .began {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        }
    }
    
    private func createMagical3DScene() -> SCNScene {
        let scene = SCNScene()
        
        // 创建产品节点
        let productNode = createProductNode()
        productNode.name = "product"
        scene.rootNode.addChildNode(productNode)
        
        // 添加魔法粒子系统
        addMagicalParticles(to: scene)
        
        // 添加环境光
        let ambientLight = SCNLight()
        ambientLight.type = .ambient
        ambientLight.color = UIColor.white
        ambientLight.intensity = 300
        
        let ambientNode = SCNNode()
        ambientNode.light = ambientLight
        scene.rootNode.addChildNode(ambientNode)
        
        // 添加点光源
        let spotLight = SCNLight()
        spotLight.type = .spot
        spotLight.color = UIColor.systemBlue
        spotLight.intensity = 1000
        spotLight.spotInnerAngle = 45
        spotLight.spotOuterAngle = 90
        
        let spotNode = SCNNode()
        spotNode.light = spotLight
        spotNode.position = SCNVector3(2, 2, 2)
        spotNode.look(at: SCNVector3(0, 0, 0))
        scene.rootNode.addChildNode(spotNode)
        
        return scene
    }
    
    private func createProductNode() -> SCNNode {
        // 创建产品几何体
        let plane = SCNPlane(width: 2, height: 2)
        
        // 应用产品图片材质
        let material = SCNMaterial()
        material.diffuse.contents = productImage
        material.emission.contents = UIColor.white.withAlphaComponent(0.1) // 微弱发光
        plane.materials = [material]
        
        let productNode = SCNNode(geometry: plane)
        
        // 添加浮动动画
        let floatAction = SCNAction.sequence([
            SCNAction.moveBy(x: 0, y: 0.2, z: 0, duration: 2.0),
            SCNAction.moveBy(x: 0, y: -0.2, z: 0, duration: 2.0)
        ])
        let repeatFloat = SCNAction.repeatForever(floatAction)
        productNode.runAction(repeatFloat)
        
        // 添加旋转动画
        let rotateAction = SCNAction.rotateBy(x: 0, y: .pi * 2, z: 0, duration: 8.0)
        let repeatRotate = SCNAction.repeatForever(rotateAction)
        productNode.runAction(repeatRotate)
        
        return productNode
    }
    
    private func addMagicalParticles(to scene: SCNScene) {
        // 创建粒子系统
        let particleSystem = SCNParticleSystem()
        
        particleSystem.birthRate = 50
        particleSystem.particleLifeSpan = 3.0
        particleSystem.particleVelocity = 1.0
        particleSystem.particleVelocityVariation = 0.5
        particleSystem.emissionDuration = 0
        
        // 粒子外观
        particleSystem.particleSize = 0.05
        particleSystem.particleSizeVariation = 0.02
        particleSystem.particleColor = UIColor.systemBlue
        particleSystem.particleColorVariation = SCNVector4(0.3, 0.3, 0.3, 0)
        
        // 发射器形状
        particleSystem.emitterShape = SCNSphere(radius: 3.0)
        
        // 添加到场景
        let particleNode = SCNNode()
        particleNode.addParticleSystem(particleSystem)
        scene.rootNode.addChildNode(particleNode)
    }
}

// MARK: - 心跳满意度视图
struct HeartbeatSatisfactionView: View {
    @Binding var satisfaction: Float
    @State private var heartScale: CGFloat = 1.0
    @State private var heartOpacity: Double = 1.0
    @State private var particles: [HeartParticle] = []
    @State private var timer: Timer?
    
    var body: some View {
        ZStack {
            // 背景渐变
            RadialGradient(
                colors: [
                    Color.pink.opacity(0.3),
                    Color.purple.opacity(0.1),
                    Color.clear
                ],
                center: .center,
                startRadius: 0,
                endRadius: 200
            )
            
            VStack(spacing: 30) {
                // 心跳可视化
                ZStack {
                    // 心形背景
                    HeartShape()
                        .fill(
                            LinearGradient(
                                colors: satisfactionColors,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .scaleEffect(heartScale)
                        .opacity(heartOpacity)
                        .overlay(
                            HeartShape()
                                .stroke(Color.white, lineWidth: 3)
                                .frame(width: 120, height: 120)
                                .scaleEffect(heartScale)
                        )
                    
                    // 心跳数值
                    Text("\(Int(satisfaction * 100))")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2)
                }
                .onTapGesture { location in
                    triggerHeartbeat()
                    addParticles(at: location)
                }
                
                // 满意度描述
                Text(satisfactionDescription)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // 魔法滑条
                MagicSatisfactionSlider(value: $satisfaction)
                    .onChange(of: satisfaction) { _ in
                        triggerHeartbeat()
                    }
            }
            
            // 粒子效果
            ForEach(particles.indices, id: \.self) { index in
                if index < particles.count {
                    ParticleView(particle: particles[index])
                }
            }
        }
        .onAppear {
            startHeartbeat()
        }
        .onDisappear {
            stopHeartbeat()
        }
    }
    
    private var satisfactionColors: [Color] {
        switch satisfaction {
        case 0.0..<0.2: return [.gray, .black]
        case 0.2..<0.4: return [.red, .orange]
        case 0.4..<0.6: return [.orange, .yellow]
        case 0.6..<0.8: return [.yellow, .green]
        default: return [.green, .cyan, .blue, .purple, .pink]
        }
    }
    
    private var satisfactionDescription: String {
        switch satisfaction {
        case 0.0..<0.2: return "😞 需要改进"
        case 0.2..<0.4: return "😐 还可以"
        case 0.4..<0.6: return "🙂 不错"
        case 0.6..<0.8: return "😊 很满意"
        default: return "🤩 超级满意！"
        }
    }
    
    private func startHeartbeat() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.8, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.2)) {
                heartScale = 1.2
                heartOpacity = 0.8
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    heartScale = 1.0
                    heartOpacity = 1.0
                }
            }
        }
    }
    
    private func stopHeartbeat() {
        timer?.invalidate()
        timer = nil
    }
    
    private func triggerHeartbeat() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
            heartScale = 1.3
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
                heartScale = 1.0
            }
        }
    }
    
    private func addParticles(at location: CGPoint) {
        for _ in 0..<5 {
            let particle = HeartParticle(
                x: location.x + CGFloat.random(in: -20...20),
                y: location.y + CGFloat.random(in: -20...20),
                scale: CGFloat.random(in: 0.5...1.5),
                opacity: Double.random(in: 0.7...1.0)
            )
            particles.append(particle)
        }
        
        // 2秒后移除粒子
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            particles.removeAll()
        }
    }
}

// MARK: - 心形形状
struct HeartShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let width = rect.width
        let height = rect.height
        
        path.move(to: CGPoint(x: width * 0.5, y: height * 0.9))
        
        path.addCurve(
            to: CGPoint(x: width * 0.1, y: height * 0.3),
            control1: CGPoint(x: width * 0.5, y: height * 0.7),
            control2: CGPoint(x: width * 0.1, y: height * 0.6)
        )
        
        path.addCurve(
            to: CGPoint(x: width * 0.5, y: height * 0.1),
            control1: CGPoint(x: width * 0.1, y: height * 0.1),
            control2: CGPoint(x: width * 0.3, y: height * 0.1)
        )
        
        path.addCurve(
            to: CGPoint(x: width * 0.9, y: height * 0.3),
            control1: CGPoint(x: width * 0.7, y: height * 0.1),
            control2: CGPoint(x: width * 0.9, y: height * 0.1)
        )
        
        path.addCurve(
            to: CGPoint(x: width * 0.5, y: height * 0.9),
            control1: CGPoint(x: width * 0.9, y: height * 0.6),
            control2: CGPoint(x: width * 0.5, y: height * 0.7)
        )
        
        path.closeSubpath()
        return path
    }
}

// MARK: - 魔法满意度滑条
struct MagicSatisfactionSlider: View {
    @Binding var value: Float
    @State private var sliderWidth: CGFloat = 0
    
    var body: some View {
        VStack(spacing: 10) {
            // 滑条轨道
            ZStack(alignment: .leading) {
                // 背景轨道
                RoundedRectangle(cornerRadius: 15)
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 30)
                
                // 进度轨道
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        LinearGradient(
                            colors: [.pink, .purple, .blue, .cyan],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: sliderWidth * CGFloat(value), height: 30)
                    .animation(.spring(response: 0.3), value: value)
                
                // 拖拽按钮
                Circle()
                    .fill(Color.white)
                    .frame(width: 40, height: 40)
                    .shadow(color: .black.opacity(0.2), radius: 5, x: 0, y: 2)
                    .overlay(
                        Circle()
                            .stroke(Color.blue, lineWidth: 3)
                            .frame(width: 35, height: 35)
                    )
                    .offset(x: sliderWidth * CGFloat(value) - 20)
                    .gesture(
                        DragGesture()
                            .onChanged { gesture in
                                let newValue = Float(gesture.location.x / sliderWidth)
                                value = max(0, min(1, newValue))
                                
                                // 触觉反馈
                                if Int(value * 10) != Int(Float(gesture.location.x / sliderWidth) * 10) {
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                    impactFeedback.impactOccurred()
                                }
                            }
                    )
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            sliderWidth = geometry.size.width
                        }
                }
            )
            
            // 刻度标签
            HStack {
                ForEach(0..<6) { index in
                    VStack {
                        Text("\(index * 2)")
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        Circle()
                            .fill(Color.gray.opacity(0.5))
                            .frame(width: 4, height: 4)
                    }
                    
                    if index < 5 {
                        Spacer()
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.horizontal)
    }
}

// MARK: - 粒子数据模型
struct HeartParticle: Identifiable {
    let id = UUID()
    let x: CGFloat
    let y: CGFloat
    let scale: CGFloat
    let opacity: Double
}

// MARK: - 粒子视图
struct ParticleView: View {
    let particle: HeartParticle
    @State private var offset: CGSize = .zero
    @State private var opacity: Double = 1.0
    
    var body: some View {
        Image(systemName: "heart.fill")
            .foregroundColor(.pink)
            .scaleEffect(particle.scale)
            .opacity(opacity)
            .position(x: particle.x, y: particle.y)
            .offset(offset)
            .onAppear {
                withAnimation(.easeOut(duration: 2.0)) {
                    offset = CGSize(
                        width: CGFloat.random(in: -50...50),
                        height: CGFloat.random(in: -100...(-20))
                    )
                    opacity = 0
                }
            }
    }
} 