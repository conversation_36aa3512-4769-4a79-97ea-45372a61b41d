import SwiftUI

// MARK: - 全局悬浮AI助手按钮
struct FloatingAIAssistantButton: View {
    @EnvironmentObject var themeManager: ThemeManager
    @State private var isPressed = false
    @State private var isAnimating = false
    @State private var pulseAnimation = false
    @State private var rotationAngle: Double = 0
    
    let action: () -> Void
    
    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            // 执行动作
            action()
        }) {
            ZStack {
                // 背景圆形
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.pink.opacity(0.9),
                                Color.pink.opacity(0.7),
                                themeManager.currentTheme.primaryColor.opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 56, height: 56)
                    .scaleEffect(isPressed ? 0.9 : 1.0)
                    .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                    .shadow(
                        color: Color.pink.opacity(0.4),
                        radius: pulseAnimation ? 15 : 8,
                        x: 0,
                        y: 4
                    )
                
                // Pinkbot图标
                Image("pinkbot-logo")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                    .rotationEffect(.degrees(rotationAngle))
                    .scaleEffect(isPressed ? 0.8 : 1.0)
                
                // 装饰性光环
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.6),
                                Color.white.opacity(0.2),
                                Color.clear
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
                    .frame(width: 60, height: 60)
                    .opacity(isAnimating ? 1.0 : 0.3)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                
                // 粒子效果指示器
                if isAnimating {
                    ForEach(0..<6, id: \.self) { index in
                        Circle()
                            .fill(Color.white.opacity(0.8))
                            .frame(width: 4, height: 4)
                            .offset(
                                x: cos(Double(index) * .pi / 3) * 35,
                                y: sin(Double(index) * .pi / 3) * 35
                            )
                            .scaleEffect(pulseAnimation ? 1.5 : 0.5)
                            .opacity(pulseAnimation ? 0.0 : 1.0)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {
            // 长按执行的动作（可选）
        }
        .onAppear {
            startAnimations()
        }
    }
    
    // MARK: - 动画控制
    private func startAnimations() {
        // 脉冲动画
        withAnimation(
            .easeInOut(duration: 2.0)
            .repeatForever(autoreverses: true)
        ) {
            pulseAnimation = true
        }
        
        // 光环动画
        withAnimation(
            .easeInOut(duration: 3.0)
            .repeatForever(autoreverses: true)
        ) {
            isAnimating = true
        }
        
        // 轻微旋转动画
        withAnimation(
            .linear(duration: 20.0)
            .repeatForever(autoreverses: false)
        ) {
            rotationAngle = 360
        }
    }
}

// MARK: - 悬浮按钮容器
struct FloatingAIAssistantContainer: View {
    @State private var showingAIAssistant = false
    @State private var dragOffset = CGSize.zero
    @State private var position = CGPoint(x: UIScreen.main.bounds.width - 80, y: UIScreen.main.bounds.height - 200)
    @GestureState private var isDragging = false

    // 环境对象
    @EnvironmentObject private var analyticsAssistant: AnalyticsAssistant
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel
    
    var body: some View {
        ZStack {
            // 悬浮按钮
            FloatingAIAssistantButton {
                showingAIAssistant = true
            }
            .position(
                x: position.x + dragOffset.width,
                y: position.y + dragOffset.height
            )
            .gesture(
                DragGesture()
                    .updating($isDragging) { _, state, _ in
                        state = true
                    }
                    .onChanged { value in
                        dragOffset = value.translation
                    }
                    .onEnded { value in
                        // 计算最终位置
                        let newX = position.x + value.translation.width
                        let newY = position.y + value.translation.height
                        
                        // 边界检测和磁吸效果
                        let screenWidth = UIScreen.main.bounds.width
                        let screenHeight = UIScreen.main.bounds.height
                        let buttonSize: CGFloat = 56
                        let margin: CGFloat = 20
                        
                        // 水平磁吸到边缘
                        let finalX: CGFloat
                        if newX < screenWidth / 2 {
                            finalX = margin + buttonSize / 2
                        } else {
                            finalX = screenWidth - margin - buttonSize / 2
                        }
                        
                        // 垂直位置限制
                        let finalY = max(
                            margin + buttonSize / 2 + 100, // 顶部安全区域
                            min(newY, screenHeight - margin - buttonSize / 2 - 100) // 底部安全区域
                        )
                        
                        // 动画到最终位置
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            position = CGPoint(x: finalX, y: finalY)
                            dragOffset = .zero
                        }
                    }
            )
            .scaleEffect(isDragging ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isDragging)
        }
        .sheet(isPresented: $showingAIAssistant) {
            ModernChatView()
                .interactiveDismissDisabled(false)
                .environmentObject(productViewModel)
                .environmentObject(usageViewModel)
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        FloatingAIAssistantContainer()
    }
    .environmentObject(ThemeManager())
}
