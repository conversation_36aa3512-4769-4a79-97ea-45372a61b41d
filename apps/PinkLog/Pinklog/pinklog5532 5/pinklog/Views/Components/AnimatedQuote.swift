import SwiftUI

struct AnimatedQuote: View {
    @State private var opacity: Double = 0
    @State private var scale: CGFloat = 0.9
    @State private var quoteIndex: Int = 0
    @State private var isChanging: Bool = false
    
    private let quotes = [
        "回忆是记忆中最美的花朵",
        "每一件物品都承载着独特的故事",
        "生活由珍贵的小确幸组成",
        "温暖的回忆是时光的礼物",
        "记录下的瞬间，成为永恒",
        "那些美好，都值得被珍藏",
        "物品是故事的容器，记忆的载体",
        "点滴记忆，汇聚成海",
        "将情感与物品联结，创造专属回忆",
        "每个物品，都有自己的生命故事"
    ]
    
    private let timer = Timer.publish(every: 6, on: .main, in: .common).autoconnect()
    
    var body: some View {
        Text(quotes[quoteIndex])
            .font(.system(size: 16, weight: .medium, design: .serif))
            .italic()
            .foregroundColor(Color.black.opacity(0.6))
            .multilineTextAlignment(.center)
            .padding(.horizontal)
            .opacity(opacity)
            .scaleEffect(scale)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.5)) {
                    opacity = 1
                    scale = 1
                }
            }
            .onReceive(timer) { _ in
                changeQuote()
            }
    }
    
    private func changeQuote() {
        withAnimation(.easeOut(duration: 0.7)) {
            opacity = 0
            scale = 0.9
            isChanging = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            quoteIndex = (quoteIndex + 1) % quotes.count
            
            withAnimation(.easeIn(duration: 0.7)) {
                opacity = 1
                scale = 1
                isChanging = false
            }
        }
    }
}

// 预览
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        AnimatedQuote()
            .frame(width: 300)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.7))
                    .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
            )
    }
} 