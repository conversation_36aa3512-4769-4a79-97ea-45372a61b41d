import SwiftUI

struct WorthIndexView: View {
    let value: Double
    
    var body: some View {
        VStack(spacing: 4) {
            ZStack {
                // 背景圆环
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                // 进度圆环
                Circle()
                    .trim(from: 0, to: CGFloat(value / 100.0))
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [colorForValue(value), colorForValue(value).opacity(0.7)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: value)
                
                // 中心数值
                VStack(spacing: 2) {
                    Text("\(Int(value.rounded()))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("值度")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(statusText(for: value))
                .font(.caption)
                .foregroundColor(colorForValue(value))
                .fontWeight(.medium)
        }
    }
    
    private func colorForValue(_ value: Double) -> Color {
        switch value {
        case 80...100:
            return .green
        case 60..<80:
            return .blue
        case 40..<60:
            return .orange
        case 20..<40:
            return .red
        default:
            return .gray
        }
    }
    
    private func statusText(for value: Double) -> String {
        switch value {
        case 80...100:
            return "超值"
        case 60..<80:
            return "值得"
        case 40..<60:
            return "一般"
        case 20..<40:
            return "不值"
        default:
            return "很不值"
        }
    }
}

#Preview {
    VStack(spacing: 30) {
        HStack(spacing: 30) {
            WorthIndexView(value: 95)
            WorthIndexView(value: 75)
            WorthIndexView(value: 45)
        }
        
        HStack(spacing: 30) {
            WorthIndexView(value: 25)
            WorthIndexView(value: 10)
        }
    }
    .padding()
}
