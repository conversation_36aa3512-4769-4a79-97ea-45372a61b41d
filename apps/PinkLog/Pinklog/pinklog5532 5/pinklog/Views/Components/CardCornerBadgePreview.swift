//
//  CardCornerBadgePreview.swift
//  pinklog
//
//  整个卡片右上角使用次数角标设计预览
//  展示角标在卡片右上角的最终设计效果
//

import SwiftUI

struct CardCornerBadgePreview: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 设计理念说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎯 卡片右上角角标设计")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("将使用次数角标放置在整个产品卡片的右上角，实现最佳的视觉层次和空间利用")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 位置对比展示
                VStack(alignment: .leading, spacing: 16) {
                    Text("📍 角标位置演进")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    // 第一版：产品图片右上角
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第一版：产品图片右上角")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                        
                        Text("⚠️ 角标过小，可读性不佳\n⚠️ 与消耗品标记拥挤\n⚠️ 受产品图片背景影响")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 第二版：整个卡片右上角
                    VStack(alignment: .leading, spacing: 8) {
                        Text("第二版：整个卡片右上角（当前）")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                        
                        Text("✅ 角标更大更清晰\n✅ 独立的视觉空间\n✅ 不受产品图片干扰\n✅ 符合卡片设计规范")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                
                // 设计细节说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎨 设计细节优化")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 8, height: 8)
                            Text("尺寸升级：从24pt升级到28pt，提升可读性和点击体验")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 8, height: 8)
                            Text("背景优化：深色透明度从0.7提升到0.8，增强对比度")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 8, height: 8)
                            Text("阴影增强：更深的阴影效果，提升角标的立体感")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.purple)
                                .frame(width: 8, height: 8)
                            Text("字体调整：字号从10pt升级到11pt，保持最佳可读性")
                                .font(.caption)
                        }
                        
                        HStack {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 8, height: 8)
                            Text("位置精确：8pt边距确保角标不会被意外裁切")
                                .font(.caption)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 技术实现说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("⚙️ 技术实现")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("布局结构：")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("```\nZStack {\n  // 主要卡片内容\n  HStack { ... }\n  \n  // 右上角角标\n  VStack {\n    HStack {\n      Spacer()\n      Badge()\n    }\n    Spacer()\n  }\n}\n```")
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.secondary)
                            .padding()
                            .background(Color(.systemGray5))
                            .cornerRadius(8)
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                
                // 用户体验优势
                VStack(alignment: .leading, spacing: 12) {
                    Text("🌟 用户体验优势")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(spacing: 8) {
                        UXAdvantageRow(icon: "👁️", title: "视觉清晰", description: "角标在独立空间中，不受其他元素干扰")
                        UXAdvantageRow(icon: "📱", title: "触控友好", description: "28pt尺寸符合iOS触控设计规范")
                        UXAdvantageRow(icon: "🎯", title: "信息层次", description: "次要信息明确分离，主要内容更突出")
                        UXAdvantageRow(icon: "🔄", title: "一致性", description: "符合移动应用角标设计惯例")
                        UXAdvantageRow(icon: "⚡", title: "性能优化", description: "ZStack布局高效，渲染性能优秀")
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                
                // 设计原则总结
                VStack(alignment: .leading, spacing: 12) {
                    Text("📐 设计原则")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("1. **空间分离** - 主要信息与次要信息各占独立空间")
                            .font(.caption)
                        Text("2. **视觉层次** - 通过位置和样式建立清晰的信息优先级")
                            .font(.caption)
                        Text("3. **功能性** - 角标既美观又实用，提供快速信息获取")
                            .font(.caption)
                        Text("4. **一致性** - 遵循iOS设计规范和用户习惯")
                            .font(.caption)
                        Text("5. **可扩展性** - 布局结构支持未来功能扩展")
                            .font(.caption)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .padding()
        }
        .navigationTitle("卡片角标设计")
        .navigationBarTitleDisplayMode(.large)
    }
}

struct UXAdvantageRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(icon)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    NavigationView {
        CardCornerBadgePreview()
    }
}