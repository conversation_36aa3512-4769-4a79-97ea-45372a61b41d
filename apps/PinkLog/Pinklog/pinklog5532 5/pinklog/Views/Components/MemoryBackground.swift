import SwiftUI

struct MemoryBackground: View {
    var theme: Theme = .pink
    
    enum Theme {
        case pink
        case purple
        case blue
        
        var colors: [Color] {
            switch self {
            case .pink:
                return [
                    Color(red: 0.98, green: 0.89, blue: 0.9),
                    Color(red: 0.94, green: 0.81, blue: 0.91),
                    Color(red: 0.9, green: 0.85, blue: 0.95)
                ]
            case .purple:
                return [
                    Color(red: 0.9, green: 0.85, blue: 0.95),
                    Color(red: 0.82, green: 0.8, blue: 0.94),
                    Color(red: 0.78, green: 0.85, blue: 0.95)
                ]
            case .blue:
                return [
                    Color(red: 0.85, green: 0.9, blue: 0.95),
                    Color(red: 0.78, green: 0.85, blue: 0.95),
                    Color(red: 0.82, green: 0.9, blue: 0.98)
                ]
            }
        }
    }
    
    var body: some View {
        ZStack {
            // 基础渐变
            LinearGradient(
                colors: theme.colors,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 柔和光晕效果
            RadialGradient(
                colors: [
                    Color.white.opacity(0.3),
                    Color.white.opacity(0.0)
                ],
                center: .topLeading,
                startRadius: 0,
                endRadius: 500
            )
            
            // 底部装饰图形
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            theme.colors[1].opacity(0.3),
                            theme.colors[2].opacity(0.1)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .frame(width: 250, height: 250)
                .offset(x: -100, y: 300)
                .blur(radius: 50)
            
            // 顶部装饰图形
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            theme.colors[0].opacity(0.2),
                            theme.colors[1].opacity(0.1)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .frame(width: 200, height: 200)
                .offset(x: 150, y: -150)
                .blur(radius: 30)
        }
        .ignoresSafeArea()
    }
}

// 预览
#Preview {
    TabView {
        MemoryBackground(theme: .pink)
            .tabItem {
                Label("粉色", systemImage: "heart.fill")
            }
        
        MemoryBackground(theme: .purple)
            .tabItem {
                Label("紫色", systemImage: "heart.fill")
            }
        
        MemoryBackground(theme: .blue)
            .tabItem {
                Label("蓝色", systemImage: "heart.fill")
            }
    }
} 