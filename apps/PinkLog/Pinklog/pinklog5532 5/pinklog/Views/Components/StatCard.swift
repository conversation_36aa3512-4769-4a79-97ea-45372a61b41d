import SwiftUI

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    HStack(spacing: 16) {
        StatCard(
            title: "总产品数",
            value: "42",
            icon: "cube.box.fill",
            color: .blue
        )
        
        StatCard(
            title: "总投资",
            value: "¥12,345",
            icon: "yensign.circle.fill",
            color: .green
        )
    }
    .padding()
}
