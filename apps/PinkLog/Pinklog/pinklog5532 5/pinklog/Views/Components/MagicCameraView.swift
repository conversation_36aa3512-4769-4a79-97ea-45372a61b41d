//
//  MagicCameraView.swift
//  PinkLog
//
//  Created by World-Class Designer on 2025/1/17.
//  震撼的魔法相机界面
//

import SwiftUI
import UIKit
import AVFoundation

struct MagicCameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> MagicCameraController {
        let controller = MagicCameraController()
        controller.delegate = context.coordinator
        return controller
    }
    
    func updateUIViewController(_ uiViewController: MagicCameraController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, MagicCameraDelegate {
        let parent: MagicCameraView
        
        init(_ parent: MagicCameraView) {
            self.parent = parent
        }
        
        func didCaptureImage(_ image: UIImage) {
            parent.capturedImage = image
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - 魔法相机控制器
protocol MagicCameraDelegate: AnyObject {
    func didCaptureImage(_ image: UIImage)
}

class MagicCameraController: UIViewController {
    weak var delegate: MagicCameraDelegate?
    
    private var captureSession: AVCaptureSession?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?
    
    // 魔法效果层
    private var magicCircleLayer: CAEmitterLayer?
    private var overlayView: MagicOverlayView!
    private var isCapturing = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
        setupMagicOverlay()
        setupControls()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startMagicEffects()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopMagicEffects()
    }
    
    private func setupCamera() {
        captureSession = AVCaptureSession()
        captureSession?.sessionPreset = .photo
        
        guard let backCamera = AVCaptureDevice.default(for: .video),
              let input = try? AVCaptureDeviceInput(device: backCamera) else { return }
        
        photoOutput = AVCapturePhotoOutput()
        
        if captureSession?.canAddInput(input) == true {
            captureSession?.addInput(input)
        }
        
        if captureSession?.canAddOutput(photoOutput!) == true {
            captureSession?.addOutput(photoOutput!)
        }
        
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession!)
        videoPreviewLayer?.videoGravity = .resizeAspectFill
        videoPreviewLayer?.frame = view.layer.bounds
        
        view.layer.addSublayer(videoPreviewLayer!)
        
        DispatchQueue.global(qos: .background).async {
            self.captureSession?.startRunning()
        }
    }
    
    private func setupMagicOverlay() {
        overlayView = MagicOverlayView(frame: view.bounds)
        overlayView.backgroundColor = .clear
        view.addSubview(overlayView)
    }
    
    private func setupControls() {
        // 魔法拍照按钮
        let captureButton = MagicCaptureButton()
        captureButton.translatesAutoresizingMaskIntoConstraints = false
        captureButton.addTarget(self, action: #selector(capturePhoto), for: .touchUpInside)
        
        view.addSubview(captureButton)
        
        NSLayoutConstraint.activate([
            captureButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            captureButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            captureButton.widthAnchor.constraint(equalToConstant: 80),
            captureButton.heightAnchor.constraint(equalToConstant: 80)
        ])
        
        // 返回按钮
        let backButton = UIButton(type: .system)
        backButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        backButton.tintColor = .white
        backButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        backButton.layer.cornerRadius = 20
        backButton.translatesAutoresizingMaskIntoConstraints = false
        backButton.addTarget(self, action: #selector(dismissCamera), for: .touchUpInside)
        
        view.addSubview(backButton)
        
        NSLayoutConstraint.activate([
            backButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            backButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            backButton.widthAnchor.constraint(equalToConstant: 40),
            backButton.heightAnchor.constraint(equalToConstant: 40)
        ])
    }
    
    private func startMagicEffects() {
        // 创建魔法阵粒子效果
        let radius = min(view.bounds.width, view.bounds.height) * 0.35
        magicCircleLayer = MagicParticleEngine.shared.createMagicCircleEmitter(for: view, radius: radius)
        
        if let magicLayer = magicCircleLayer {
            view.layer.insertSublayer(magicLayer, above: videoPreviewLayer)
        }
        
        // 启动魔法阵动画
        overlayView.startMagicCircleAnimation()
    }
    
    private func stopMagicEffects() {
        magicCircleLayer?.removeFromSuperlayer()
        overlayView.stopMagicCircleAnimation()
    }
    
    @objc private func capturePhoto() {
        guard !isCapturing else { return }
        isCapturing = true
        
        // 震撼的拍照魔法效果
        performMagicCapture()
        
        let settings = AVCapturePhotoSettings()
        photoOutput?.capturePhoto(with: settings, delegate: self)
    }
    
    private func performMagicCapture() {
        // 屏幕震动
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        // 魔法爆炸效果
        let centerPoint = CGPoint(x: view.bounds.midX, y: view.bounds.midY)
        MagicParticleEngine.shared.createRecognitionExplosion(at: centerPoint, in: view)
        
        // 屏幕闪光效果
        let flashView = UIView(frame: view.bounds)
        flashView.backgroundColor = .white
        flashView.alpha = 0
        view.addSubview(flashView)
        
        UIView.animate(withDuration: 0.1, animations: {
            flashView.alpha = 1
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                flashView.alpha = 0
            } completion: { _ in
                flashView.removeFromSuperview()
            }
        }
        
        // 魔法阵强化效果
        overlayView.triggerCaptureEffect()
    }
    
    @objc private func dismissCamera() {
        dismiss(animated: true)
    }
}

// MARK: - 照片捕获代理
extension MagicCameraController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            isCapturing = false
            return
        }
        
        // 延迟一秒让魔法效果完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.delegate?.didCaptureImage(image)
            self.isCapturing = false
        }
    }
}

// MARK: - 魔法叠加层
class MagicOverlayView: UIView {
    private var magicCircleShapeLayer: CAShapeLayer!
    private var runeSymbols: [CAShapeLayer] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupMagicCircle()
        setupRuneSymbols()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupMagicCircle() {
        // 主魔法阵
        magicCircleShapeLayer = CAShapeLayer()
        
        let center = CGPoint(x: bounds.midX, y: bounds.midY)
        let radius = min(bounds.width, bounds.height) * 0.35
        
        let circlePath = UIBezierPath(arcCenter: center, radius: radius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        
        // 内圈
        let innerRadius = radius * 0.8
        let innerPath = UIBezierPath(arcCenter: center, radius: innerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        circlePath.append(innerPath)
        
        // 外圈
        let outerRadius = radius * 1.2
        let outerPath = UIBezierPath(arcCenter: center, radius: outerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        circlePath.append(outerPath)
        
        magicCircleShapeLayer.path = circlePath.cgPath
        magicCircleShapeLayer.fillColor = UIColor.clear.cgColor
        magicCircleShapeLayer.strokeColor = UIColor.cyan.withAlphaComponent(0.7).cgColor
        magicCircleShapeLayer.lineWidth = 2.0
        magicCircleShapeLayer.lineDashPattern = [10, 5]
        
        layer.addSublayer(magicCircleShapeLayer)
    }
    
    private func setupRuneSymbols() {
        let center = CGPoint(x: bounds.midX, y: bounds.midY)
        let radius = min(bounds.width, bounds.height) * 0.42
        
        // 在圆周上放置8个符文
        for i in 0..<8 {
            let angle = Double(i) * .pi / 4
            let x = center.x + cos(angle) * radius
            let y = center.y + sin(angle) * radius
            
            let runeLayer = createRuneSymbol(at: CGPoint(x: x, y: y), type: i)
            runeSymbols.append(runeLayer)
            layer.addSublayer(runeLayer)
        }
    }
    
    private func createRuneSymbol(at position: CGPoint, type: Int) -> CAShapeLayer {
        let layer = CAShapeLayer()
        let path = UIBezierPath()
        
        // 根据类型创建不同的符文
        let size: CGFloat = 20
        let halfSize = size / 2
        
        switch type % 4 {
        case 0: // 十字
            path.move(to: CGPoint(x: position.x - halfSize, y: position.y))
            path.addLine(to: CGPoint(x: position.x + halfSize, y: position.y))
            path.move(to: CGPoint(x: position.x, y: position.y - halfSize))
            path.addLine(to: CGPoint(x: position.x, y: position.y + halfSize))
            
        case 1: // 菱形
            path.move(to: CGPoint(x: position.x, y: position.y - halfSize))
            path.addLine(to: CGPoint(x: position.x + halfSize, y: position.y))
            path.addLine(to: CGPoint(x: position.x, y: position.y + halfSize))
            path.addLine(to: CGPoint(x: position.x - halfSize, y: position.y))
            path.close()
            
        case 2: // 三角
            path.move(to: CGPoint(x: position.x, y: position.y - halfSize))
            path.addLine(to: CGPoint(x: position.x + halfSize, y: position.y + halfSize))
            path.addLine(to: CGPoint(x: position.x - halfSize, y: position.y + halfSize))
            path.close()
            
        default: // 圆
            let circleRect = CGRect(x: position.x - halfSize/2, y: position.y - halfSize/2, width: halfSize, height: halfSize)
            path.append(UIBezierPath(ovalIn: circleRect))
        }
        
        layer.path = path.cgPath
        layer.strokeColor = UIColor.systemBlue.withAlphaComponent(0.8).cgColor
        layer.fillColor = UIColor.clear.cgColor
        layer.lineWidth = 2.0
        
        return layer
    }
    
    func startMagicCircleAnimation() {
        // 魔法阵旋转动画
        let rotation = CABasicAnimation(keyPath: "transform.rotation")
        rotation.fromValue = 0
        rotation.toValue = Double.pi * 2
        rotation.duration = 10.0
        rotation.repeatCount = .infinity
        magicCircleShapeLayer.add(rotation, forKey: "rotation")
        
        // 符文闪烁动画
        for (index, rune) in runeSymbols.enumerated() {
            let opacity = CABasicAnimation(keyPath: "opacity")
            opacity.fromValue = 0.3
            opacity.toValue = 1.0
            opacity.duration = 1.0
            opacity.autoreverses = true
            opacity.repeatCount = .infinity
            opacity.beginTime = CACurrentMediaTime() + Double(index) * 0.2
            rune.add(opacity, forKey: "opacity")
        }
    }
    
    func stopMagicCircleAnimation() {
        magicCircleShapeLayer.removeAllAnimations()
        runeSymbols.forEach { $0.removeAllAnimations() }
    }
    
    func triggerCaptureEffect() {
        // 拍照时的强化效果
        let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
        pulseAnimation.fromValue = 1.0
        pulseAnimation.toValue = 1.3
        pulseAnimation.duration = 0.3
        pulseAnimation.autoreverses = true
        
        magicCircleShapeLayer.add(pulseAnimation, forKey: "captureEffect")
        
        // 符文同时发光
        for rune in runeSymbols {
            let glow = CABasicAnimation(keyPath: "shadowOpacity")
            glow.fromValue = 0
            glow.toValue = 1.0
            glow.duration = 0.5
            glow.autoreverses = true
            
            rune.shadowColor = UIColor.cyan.cgColor
            rune.shadowRadius = 10
            rune.add(glow, forKey: "glowEffect")
        }
    }
}

// MARK: - 魔法拍照按钮
class MagicCaptureButton: UIButton {
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupAppearance()
        setupAnimation()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupAppearance()
        setupAnimation()
    }
    
    private func setupAppearance() {
        backgroundColor = .clear
        layer.cornerRadius = 40
        layer.borderWidth = 4
        layer.borderColor = UIColor.white.cgColor
        
        // 内圈
        let innerCircle = CAShapeLayer()
        let innerPath = UIBezierPath(ovalIn: CGRect(x: 10, y: 10, width: 60, height: 60))
        innerCircle.path = innerPath.cgPath
        innerCircle.fillColor = UIColor.white.withAlphaComponent(0.8).cgColor
        layer.addSublayer(innerCircle)
        
        // 魔法符号
        let symbolLayer = CAShapeLayer()
        let symbolPath = UIBezierPath()
        symbolPath.move(to: CGPoint(x: 40, y: 25))
        symbolPath.addLine(to: CGPoint(x: 40, y: 55))
        symbolPath.move(to: CGPoint(x: 25, y: 40))
        symbolPath.addLine(to: CGPoint(x: 55, y: 40))
        
        symbolLayer.path = symbolPath.cgPath
        symbolLayer.strokeColor = UIColor.systemBlue.cgColor
        symbolLayer.lineWidth = 3
        layer.addSublayer(symbolLayer)
    }
    
    private func setupAnimation() {
        // 呼吸动画
        let breathAnimation = CABasicAnimation(keyPath: "transform.scale")
        breathAnimation.fromValue = 1.0
        breathAnimation.toValue = 1.1
        breathAnimation.duration = 2.0
        breathAnimation.autoreverses = true
        breathAnimation.repeatCount = .infinity
        layer.add(breathAnimation, forKey: "breathe")
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        
        // 按下效果
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        
        // 恢复效果
        UIView.animate(withDuration: 0.1) {
            self.transform = .identity
        }
    }
} 