import SwiftUI

struct StyleSelectionView: View {
    let originalImage: UIImage
    let onStyleSelected: (PixelArtStyle) -> Void
    let onCancel: () -> Void
    
    @State private var selectedStyle: PixelArtStyle = .popArt
    @State private var previewImages: [PixelArtStyle: UIImage] = [:]
    @State private var isGeneratingPreviews = true
    
    private let processor = PixelStyleProcessor.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("选择头像风格")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 原图预览
                VStack(spacing: 8) {
                    Text("原图")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 100, height: 100)
                        .clipShape(Circle())
                        .overlay(Circle().stroke(Color.gray.opacity(0.3), lineWidth: 1))
                }
                
                // 风格选择网格
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 20) {
                    ForEach(PixelArtStyle.allCases, id: \.self) { style in
                        StylePreviewCard(
                            style: style,
                            previewImage: previewImages[style],
                            isSelected: selectedStyle == style,
                            isGenerating: isGeneratingPreviews
                        ) {
                            selectedStyle = style
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // 底部按钮
                HStack(spacing: 16) {
                    Button("取消") {
                        onCancel()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(12)
                    
                    Button("确认选择") {
                        onStyleSelected(selectedStyle)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .disabled(isGeneratingPreviews)
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            generatePreviews()
        }
    }
    
    private func generatePreviews() {
        isGeneratingPreviews = true
        
        let group = DispatchGroup()
        
        for style in PixelArtStyle.allCases {
            group.enter()
            processor.createPixelAvatar(originalImage, style: style) { previewImage in
                DispatchQueue.main.async {
                    if let image = previewImage {
                        previewImages[style] = image
                    }
                    group.leave()
                }
            }
        }
        
        group.notify(queue: .main) {
            isGeneratingPreviews = false
        }
    }
}

struct StylePreviewCard: View {
    let style: PixelArtStyle
    let previewImage: UIImage?
    let isSelected: Bool
    let isGenerating: Bool
    let onTap: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 预览图
            ZStack {
                if let image = previewImage {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                } else if isGenerating {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                } else {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }
            }
            .overlay(
                Circle()
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 3)
            )
            
            // 风格名称
            Text(style.rawValue)
                .font(.caption)
                .fontWeight(isSelected ? .bold : .regular)
                .foregroundColor(isSelected ? .blue : .primary)
                .multilineTextAlignment(.center)
            
            // 风格描述
            Text(style.description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
        )
        .onTapGesture {
            onTap()
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

#Preview {
    StyleSelectionView(
        originalImage: UIImage(systemName: "person.circle.fill") ?? UIImage(),
        onStyleSelected: { style in
            print("Selected style: \(style)")
        },
        onCancel: {
            print("Cancelled")
        }
    )
}
