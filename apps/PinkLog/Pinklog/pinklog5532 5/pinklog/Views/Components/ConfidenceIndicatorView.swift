import SwiftUI

struct ConfidenceIndicatorView: View {
    let confidence: ConfidenceMetrics
    let showDetails: Bool
    
    init(confidence: ConfidenceMetrics, showDetails: Bool = false) {
        self.confidence = confidence
        self.showDetails = showDetails
    }
    
    var body: some View {
        if showDetails {
            detailedView
        } else {
            compactView
        }
    }
    
    // 紧凑视图（用于卡片）
    private var compactView: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(confidenceColor)
                .frame(width: 8, height: 8)
            
            Text(confidence.level.rawValue)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    // 详细视图（用于详情页）
    private var detailedView: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和总体置信度
            HStack {
                Text("分析置信度")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                HStack(spacing: 6) {
                    Circle()
                        .fill(confidenceColor)
                        .frame(width: 12, height: 12)
                    
                    Text(confidence.level.rawValue)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(confidenceColor)
                    
                    Text("(\(Int(confidence.factor * 100))%)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 置信度说明
            Text(confidence.level.recommendation)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(confidenceColor.opacity(0.1))
                .cornerRadius(8)
            
            // 详细指标
            VStack(spacing: 8) {
                confidenceMetricRow(
                    title: "数据量",
                    value: "\(confidence.dataPoints)个记录",
                    score: min(1.0, Double(confidence.dataPoints) / 10.0),
                    description: confidence.dataPoints >= 10 ? "数据充足" : 
                                confidence.dataPoints >= 5 ? "数据适中" : "数据较少"
                )
                
                confidenceMetricRow(
                    title: "时间跨度",
                    value: timeSpanDescription,
                    score: min(1.0, confidence.timeSpan / (90 * 24 * 3600)),
                    description: confidence.timeSpan >= (90 * 24 * 3600) ? "时间充足" :
                                confidence.timeSpan >= (30 * 24 * 3600) ? "时间适中" : "时间较短"
                )
                
                confidenceMetricRow(
                    title: "数据一致性",
                    value: "\(Int(confidence.consistency * 100))%",
                    score: confidence.consistency,
                    description: confidence.consistency >= 0.8 ? "非常一致" :
                                confidence.consistency >= 0.6 ? "比较一致" :
                                confidence.consistency >= 0.4 ? "一般一致" : "不够一致"
                )
            }
            .padding(.top, 4)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // 置信度指标行
    private func confidenceMetricRow(
        title: String,
        value: String,
        score: Double,
        description: String
    ) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                // 进度条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 4)
                            .cornerRadius(2)
                        
                        Rectangle()
                            .fill(scoreColor(score))
                            .frame(width: geometry.size.width * score, height: 4)
                            .cornerRadius(2)
                    }
                }
                .frame(width: 60, height: 4)
                
                Text(description)
                    .font(.caption2)
                    .foregroundColor(scoreColor(score))
            }
        }
    }
    
    // 计算置信度颜色
    private var confidenceColor: Color {
        switch confidence.level {
        case .veryHigh: return .green
        case .high: return .blue
        case .medium: return .orange
        case .low: return .yellow
        case .veryLow: return .red
        }
    }
    
    // 计算评分颜色
    private func scoreColor(_ score: Double) -> Color {
        switch score {
        case 0.8...: return .green
        case 0.6..<0.8: return .blue
        case 0.4..<0.6: return .orange
        case 0.2..<0.4: return .yellow
        default: return .red
        }
    }
    
    // 时间跨度描述
    private var timeSpanDescription: String {
        let days = Int(confidence.timeSpan / (24 * 3600))
        if days >= 365 {
            return "\(days / 365)年"
        } else if days >= 30 {
            return "\(days / 30)个月"
        } else {
            return "\(days)天"
        }
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        // 紧凑视图预览
        HStack {
            Text("紧凑视图:")
            ConfidenceIndicatorView(
                confidence: ConfidenceMetrics(
                    dataPoints: 8,
                    timeSpan: 60 * 24 * 3600, // 60天
                    consistency: 0.75,
                    factor: 0.7,
                    level: .high
                ),
                showDetails: false
            )
            Spacer()
        }
        
        // 详细视图预览
        ConfidenceIndicatorView(
            confidence: ConfidenceMetrics(
                dataPoints: 12,
                timeSpan: 120 * 24 * 3600, // 120天
                consistency: 0.85,
                factor: 0.85,
                level: .veryHigh
            ),
            showDetails: true
        )
        
        Spacer()
    }
    .padding()
}
