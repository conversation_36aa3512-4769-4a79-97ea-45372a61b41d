//
//  SubscriptionStatCard.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅统计卡片组件
//

import SwiftUI

struct SubscriptionStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            // 图标
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
            }
            
            // 数值
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            // 标题
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .fixedSize(horizontal: false, vertical: true)
        }
        .frame(width: 80)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    HStack(spacing: 12) {
        SubscriptionStatCard(
            title: "活跃订阅",
            value: "5",
            icon: "checkmark.circle.fill",
            color: .green
        )
        
        SubscriptionStatCard(
            title: "即将续费",
            value: "2",
            icon: "clock.fill",
            color: .red
        )
        
        SubscriptionStatCard(
            title: "年度支出",
            value: "¥1,200",
            icon: "calendar.circle.fill",
            color: .purple
        )
        
        SubscriptionStatCard(
            title: "平均价值",
            value: "8.5",
            icon: "star.fill",
            color: .yellow
        )
    }
    .padding()
    .background(Color(UIColor.secondarySystemBackground))
}