//
//  SmartProductNameInput.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/1/17.
//  智能产品名称输入组件
//

import SwiftUI

struct SmartProductNameInput: View {
    @Binding var name: String
    @Binding var brand: String
    let productImage: UIImage?
    @State private var isAnalyzing = false
    @State private var suggestedNames: [String] = []
    @State private var showSuggestions = false
    @State private var typingTimer: Timer?
    
    // AI建议的产品名称（基于图片分析）
    private let aiSuggestions = [
        "iPhone 15 Pro",
        "MacBook Pro",
        "iPad Air",
        "Apple Watch",
        "AirPods Pro",
        "Nike Air Force 1",
        "Levi's 501牛仔裤",
        "Coach手提包"
    ]
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题区域
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "sparkles")
                        .foregroundColor(.blue)
                        .font(.title2)
                    
                    Text("产品信息")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                Text("我们将智能识别您的产品")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // AI分析状态
            if isAnalyzing {
                HStack(spacing: 12) {
                    ProgressView()
                        .scaleEffect(0.8)
                    
                    Text("AI正在分析您的产品...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
            
            // 产品名称输入
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("产品名称")
                        .font(.headline)
                    
                    Text("*")
                        .foregroundColor(.red)
                    
                    Spacer()
                    
                    if !name.isEmpty {
                        Button(action: clearName) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                        .frame(height: 56)
                    
                    // 输入框
                    TextField("点击输入或选择下方建议", text: $name)
                        .font(.body)
                        .padding(.horizontal, 16)
                        .frame(height: 56)
                        .onChange(of: name) { _ in
                            handleNameChange()
                        }
                    
                    // 占位符动画
                    if name.isEmpty {
                        HStack {
                            Text("点击输入或选择下方建议")
                                .foregroundColor(.secondary)
                                .font(.body)
                                .padding(.horizontal, 16)
                            
                            Spacer()
                        }
                        .allowsHitTesting(false)
                    }
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(name.isEmpty ? Color.red.opacity(0.3) : Color.blue.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 品牌输入（可选）
            VStack(alignment: .leading, spacing: 12) {
                Text("品牌（可选）")
                    .font(.headline)
                
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                        .frame(height: 56)
                    
                    TextField("例如：Apple, Nike, 小米...", text: $brand)
                        .font(.body)
                        .padding(.horizontal, 16)
                        .frame(height: 56)
                }
            }
            
            // AI建议
            if showSuggestions && !suggestedNames.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                        
                        Text("AI建议")
                            .font(.headline)
                    }
                    
                    LazyVStack(spacing: 8) {
                        ForEach(suggestedNames.prefix(3), id: \.self) { suggestion in
                            Button(action: {
                                selectSuggestion(suggestion)
                            }) {
                                HStack {
                                    Text(suggestion)
                                        .foregroundColor(.primary)
                                        .font(.body)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "arrow.up.left")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                }
                                .padding()
                                .background(Color.blue.opacity(0.05))
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .transition(.move(edge: .top).combined(with: .opacity))
            }
            
            // 提示信息
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .font(.caption)
                
                Text("准确的产品名称有助于更好的价值分析")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 8)
        }
        .onAppear {
            startAIAnalysis()
        }
    }
    
    private func handleNameChange() {
        // 重置建议状态
        typingTimer?.invalidate()
        typingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { _ in
            if !name.isEmpty {
                generateSuggestions()
            }
        }
    }
    
    private func clearName() {
        withAnimation(.spring(response: 0.4)) {
            name = ""
            brand = ""
            showSuggestions = false
        }
        
        // 触觉反馈
        let impact = UIImpactFeedbackGenerator(style: .light)
        impact.impactOccurred()
    }
    
    private func selectSuggestion(_ suggestion: String) {
        withAnimation(.spring(response: 0.4)) {
            name = suggestion
            showSuggestions = false
        }
        
        // 触觉反馈
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
        
        // 自动填充品牌（如果建议中包含品牌信息）
        extractBrandFromSuggestion(suggestion)
    }
    
    private func extractBrandFromSuggestion(_ suggestion: String) {
        let brandKeywords = ["Apple", "iPhone", "iPad", "MacBook", "Nike", "Adidas", "Coach", "Levi's", "小米", "华为"]
        
        for brandKeyword in brandKeywords {
            if suggestion.contains(brandKeyword) {
                brand = brandKeyword
                break
            }
        }
    }
    
    private func startAIAnalysis() {
        guard productImage != nil else { return }
        
        isAnalyzing = true
        
        // 模拟AI分析延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.spring(response: 0.6)) {
                isAnalyzing = false
                generateSuggestions()
                showSuggestions = true
            }
        }
    }
    
    private func generateSuggestions() {
        // 基于产品图片和已输入内容生成建议
        let filteredSuggestions = aiSuggestions.filter { suggestion in
            if name.isEmpty {
                return true
            } else {
                return suggestion.localizedCaseInsensitiveContains(name)
            }
        }.shuffled()
        
        withAnimation(.spring(response: 0.5)) {
            suggestedNames = Array(filteredSuggestions.prefix(3))
            showSuggestions = !suggestedNames.isEmpty
        }
    }
}

#Preview {
    @State var name = ""
    @State var brand = ""
    
    return SmartProductNameInput(
        name: $name,
        brand: $brand,
        productImage: nil
    )
    .padding()
} 