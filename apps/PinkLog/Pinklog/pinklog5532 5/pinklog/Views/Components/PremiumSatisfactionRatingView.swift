//
//  PremiumSatisfactionRatingView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/1/17.
//  世界级的满意度评分组件
//

import SwiftUI

struct PremiumSatisfactionRatingView: View {
    @Binding var satisfaction: Int
    @State private var hoverIndex: Int = 0
    @State private var isAnimating = false
    
    private let emojis = ["😞", "😕", "😐", "😊", "🤩"]
    private let colors = [
        Color.red.opacity(0.8),
        Color.orange.opacity(0.8),
        Color.yellow.opacity(0.8),
        Color.green.opacity(0.8),
        Color.blue.opacity(0.8)
    ]
    
    private let descriptions = [
        "让我失望",
        "不太满意",
        "还可以",
        "很喜欢",
        "超级棒！"
    ]
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题
            VStack(spacing: 8) {
                Text("这件物品让你感觉如何？")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text("你的感受对我们的分析很重要")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // 主要评分区域
            ZStack {
                // 背景渐变
                RoundedRectangle(cornerRadius: 24)
                    .fill(
                        LinearGradient(
                            colors: [
                                colors[satisfaction - 1].opacity(0.15),
                                colors[satisfaction - 1].opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .animation(.easeInOut(duration: 0.3), value: satisfaction)
                
                VStack(spacing: 20) {
                    // 大表情符号
                    Text(emojis[satisfaction - 1])
                        .font(.system(size: 60))
                        .scaleEffect(isAnimating ? 1.2 : 1.0)
                        .animation(.spring(response: 0.5, dampingFraction: 0.6), value: isAnimating)
                    
                    // 圆形滑动条
                    VStack(spacing: 12) {
                        // 自定义滑动条
                        ZStack {
                            // 背景轨道
                            Capsule()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 8)
                            
                            // 活动轨道
                            HStack {
                                Capsule()
                                    .fill(
                                        LinearGradient(
                                            colors: [colors[satisfaction - 1], colors[satisfaction - 1].opacity(0.7)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(width: CGFloat(satisfaction) / 5.0 * 280, height: 8)
                                
                                Spacer()
                            }
                        }
                        .frame(width: 280)
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    let newValue = max(1, min(5, Int((value.location.x / 280) * 5) + 1))
                                    if newValue != satisfaction {
                                        satisfaction = newValue
                                        triggerHaptic()
                                        withAnimation(.spring(response: 0.3)) {
                                            isAnimating = true
                                        }
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            isAnimating = false
                                        }
                                    }
                                }
                        )
                        
                        // 星星指示器
                        HStack(spacing: 4) {
                            ForEach(1...5, id: \.self) { index in
                                Button(action: {
                                    if satisfaction != index {
                                        satisfaction = index
                                        triggerHaptic()
                                        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                            isAnimating = true
                                        }
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            isAnimating = false
                                        }
                                    }
                                }) {
                                    Image(systemName: index <= satisfaction ? "star.fill" : "star")
                                        .font(.title2)
                                        .foregroundColor(index <= satisfaction ? colors[satisfaction - 1] : .gray.opacity(0.4))
                                        .scaleEffect(index == satisfaction && isAnimating ? 1.3 : 1.0)
                                        .animation(.spring(response: 0.3), value: satisfaction)
                                }
                            }
                        }
                    }
                    
                    // 描述文字
                    Text(descriptions[satisfaction - 1])
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(colors[satisfaction - 1])
                        .transition(.opacity.combined(with: .scale))
                        .animation(.easeInOut(duration: 0.3), value: satisfaction)
                }
                .padding(32)
            }
            .frame(maxWidth: .infinity)
            .shadow(color: colors[satisfaction - 1].opacity(0.2), radius: 10, x: 0, y: 5)
        }
        .onAppear {
            // 初始动画
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
                    isAnimating = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    isAnimating = false
                }
            }
        }
    }
    
    private func triggerHaptic() {
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
    }
}

#Preview {
    @State var satisfaction = 3
    return PremiumSatisfactionRatingView(satisfaction: $satisfaction)
        .padding()
} 