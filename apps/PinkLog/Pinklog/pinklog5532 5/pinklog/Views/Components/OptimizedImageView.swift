import SwiftUI

/// u4f18u5316u7684u56feu7247u89c6u56feuff0cu4e13u95e8u7528u4e8eu5168u5c4fu56feu7247u663eu793auff0cu63d0u9ad8u8fd4u56deu6d41u7545u5ea6
struct OptimizedImageView: View {
    // u56feu7247u6570u636e
    let imageData: Data?
    
    // u7f13u5b58u952e
    let cacheKey: String
    
    // u5185u5bb9u6a21u5f0f
    var contentMode: ContentMode = .fit
    
    // u6e32u67d3u72b6u6001
    @State private var uiImage: UIImage? = nil
    @State private var isLoading = true
    
    // u7528u4e8eu6e05u7406u5185u5b58u7684u6807u5fd7
    @State private var shouldUnloadImage = false
    
    var body: some View {
        ZStack {
            // u52a0u8f7du4e2du72b6u6001
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .scaleEffect(1.2)
            }
            
            // u56feu7247u663eu793a
            if let image = uiImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: contentMode)
                    .transition(.opacity)
            }
        }
        .onAppear {
            loadImage()
        }
        .onDisappear {
            // 🔥 立即释放图片内存 - 解决30MB持续占用
            shouldUnloadImage = true
            
            // 🔥 立即清理图片引用，不延迟
            autoreleasepool {
                uiImage = nil
            }
            
            // 🔥 触发ImageManager清理
            ImageManager.shared.cleanupAfterImageProcessing()
            
            // 🔥 强制内存回收
            DispatchQueue.main.async {
                autoreleasepool {
                    // 确保内存释放
                }
            }
        }
    }
    
    private func loadImage() {
        // u91cdu7f6eu5378u8f7du6807u5fd7
        shouldUnloadImage = false
        
        // u5148u68c0u67e5u7f13u5b58
        if let cachedImage = ImageManager.shared.getImageFromCache(key: cacheKey) {
            withAnimation {
                self.uiImage = cachedImage
                self.isLoading = false
            }
            return
        }
        
        // u5982u679cu6ca1u6709u56feu7247u6570u636euff0cu7ed3u675fu52a0u8f7d
        guard let imageData = imageData else {
            self.isLoading = false
            return
        }
        
        // u5f02u6b65u52a0u8f7du56feu7247uff0cu4f7fu7528u4f4eu4f18u5148u7ea7u961fu5217u4ee5u4fbfu4e8eu5febu901fu8fd4u56de
        DispatchQueue.global(qos: .userInitiated).async {
            // u4f7fu7528u81eau52a8u91cau653eu6c60u9632u6b62u5185u5b58u6cc4u6f0f
            autoreleasepool {
                // u68c0u67e5u662fu5426u5df2u8bbeu7f6eu4e3au5378u8f7d
                if shouldUnloadImage {
                    return
                }
                
                // 🔥 创建UIImage后立即压缩，不保留原图引用
                let compressedImage: UIImage? = autoreleasepool {
                    guard let image = UIImage(data: imageData) else {
                        return nil
                    }
                    
                    // 立即压缩并返回，让原图自动释放
                    return ImageManager.shared.compressImage(image)
                }
                
                guard let finalImage = compressedImage else {
                    DispatchQueue.main.async {
                        self.isLoading = false
                    }
                    return
                }
                
                // u4e3bu7ebfu7a0bu8fd4u56deu7ed3u679c
                DispatchQueue.main.async {
                    // u518du6b21u68c0u67e5u662fu5426u5df2u8bbeu7f6eu4e3au5378u8f7d
                    if shouldUnloadImage {
                        return
                    }
                    
                    // u7f13u5b58u56feu7247
                    ImageManager.shared.cacheImage(finalImage, key: cacheKey)
                    
                    // u663eu793au56feu7247
                    withAnimation(.easeIn(duration: 0.2)) {
                        self.uiImage = finalImage
                        self.isLoading = false
                    }
                    
                    // 🔥 图片加载完成后立即清理
                    ImageManager.shared.cleanupAfterImageProcessing()
                }
            }
        }
    }
}