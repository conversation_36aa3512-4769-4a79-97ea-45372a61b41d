import SwiftUI

// MARK: - 动画过渡组件
struct AnimatedTransitions {
    
    // MARK: - 页面切换动画
    struct PageTransition: ViewModifier {
        let isActive: Bool
        
        func body(content: Content) -> some View {
            content
                .opacity(isActive ? 1 : 0)
                .scaleEffect(isActive ? 1 : 0.95)
                .animation(.easeInOut(duration: 0.3), value: isActive)
        }
    }
    
    // MARK: - 卡片入场动画
    struct CardAppearAnimation: ViewModifier {
        let delay: Double
        @State private var isVisible = false
        
        func body(content: Content) -> some View {
            content
                .opacity(isVisible ? 1 : 0)
                .offset(y: isVisible ? 0 : 20)
                .animation(.easeOut(duration: 0.5).delay(delay), value: isVisible)
                .onAppear {
                    isVisible = true
                }
        }
    }
    
    // MARK: - 数值变化动画
    struct NumberCountAnimation: View {
        let value: Double
        let format: String
        let duration: Double
        
        @State private var animatedValue: Double = 0
        
        var body: some View {
            Text(String(format: format, animatedValue))
                .fontWeight(.bold)
                .onAppear {
                    withAnimation(.easeInOut(duration: duration)) {
                        animatedValue = value
                    }
                }
                .onChange(of: value) { newValue in
                    withAnimation(.easeInOut(duration: duration)) {
                        animatedValue = newValue
                    }
                }
        }
    }
    
    // MARK: - 进度条动画
    struct AnimatedProgressBar: View {
        let progress: Double
        let color: Color
        let height: CGFloat
        
        @State private var animatedProgress: Double = 0
        
        var body: some View {
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: height)
                        .cornerRadius(height / 2)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * animatedProgress, height: height)
                        .cornerRadius(height / 2)
                        .animation(.easeInOut(duration: 1.0), value: animatedProgress)
                }
            }
            .frame(height: height)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animatedProgress = progress
                }
            }
            .onChange(of: progress) { newProgress in
                withAnimation(.easeInOut(duration: 0.5)) {
                    animatedProgress = newProgress
                }
            }
        }
    }
    
    // MARK: - 脉冲动画
    struct PulseAnimation: ViewModifier {
        @State private var isPulsing = false
        
        func body(content: Content) -> some View {
            content
                .scaleEffect(isPulsing ? 1.1 : 1.0)
                .animation(
                    .easeInOut(duration: 1.0)
                    .repeatForever(autoreverses: true),
                    value: isPulsing
                )
                .onAppear {
                    isPulsing = true
                }
        }
    }
    
    // MARK: - 摇摆动画
    struct ShakeAnimation: ViewModifier {
        @State private var shakeOffset: CGFloat = 0
        
        func body(content: Content) -> some View {
            content
                .offset(x: shakeOffset)
                .animation(.easeInOut(duration: 0.1), value: shakeOffset)
        }
        
        func shake() {
            withAnimation(.easeInOut(duration: 0.1)) {
                shakeOffset = -5
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    shakeOffset = 5
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    shakeOffset = 0
                }
            }
        }
    }
    
    // MARK: - 浮动动画
    struct FloatingAnimation: ViewModifier {
        @State private var isFloating = false
        
        func body(content: Content) -> some View {
            content
                .offset(y: isFloating ? -5 : 0)
                .animation(
                    .easeInOut(duration: 2.0)
                    .repeatForever(autoreverses: true),
                    value: isFloating
                )
                .onAppear {
                    isFloating = true
                }
        }
    }
    
    // MARK: - 旋转加载动画
    struct RotatingLoadingAnimation: View {
        @State private var isRotating = false
        
        var body: some View {
            Image(systemName: "arrow.clockwise")
                .rotationEffect(.degrees(isRotating ? 360 : 0))
                .animation(
                    .linear(duration: 1.0)
                    .repeatForever(autoreverses: false),
                    value: isRotating
                )
                .onAppear {
                    isRotating = true
                }
        }
    }
    
    // MARK: - 渐变背景动画
    struct AnimatedGradientBackground: View {
        @State private var animateGradient = false
        
        let colors: [Color]
        
        var body: some View {
            LinearGradient(
                colors: colors,
                startPoint: animateGradient ? .topLeading : .bottomLeading,
                endPoint: animateGradient ? .bottomTrailing : .topTrailing
            )
            .animation(
                .easeInOut(duration: 3.0)
                .repeatForever(autoreverses: true),
                value: animateGradient
            )
            .onAppear {
                animateGradient = true
            }
        }
    }
    
    // MARK: - 波纹动画
    struct RippleAnimation: View {
        @State private var animate = false
        let color: Color
        
        var body: some View {
            ZStack {
                Circle()
                    .fill(color.opacity(0.3))
                    .scaleEffect(animate ? 1.5 : 1.0)
                    .opacity(animate ? 0 : 1)
                
                Circle()
                    .fill(color.opacity(0.2))
                    .scaleEffect(animate ? 2.0 : 1.0)
                    .opacity(animate ? 0 : 0.5)
                    .animation(.easeOut(duration: 1.0).delay(0.3), value: animate)
                
                Circle()
                    .fill(color.opacity(0.1))
                    .scaleEffect(animate ? 2.5 : 1.0)
                    .opacity(animate ? 0 : 0.3)
                    .animation(.easeOut(duration: 1.0).delay(0.6), value: animate)
            }
            .animation(.easeOut(duration: 1.0), value: animate)
            .onAppear {
                animate = true
            }
        }
    }
}

// MARK: - 图表动画组件
struct ChartAnimations {
    
    // MARK: - 柱状图增长动画
    struct AnimatedBarChart: View {
        let data: [Double]
        let colors: [Color]
        let maxHeight: CGFloat
        
        @State private var animatedData: [Double]
        
        init(data: [Double], colors: [Color], maxHeight: CGFloat = 200) {
            self.data = data
            self.colors = colors
            self.maxHeight = maxHeight
            self._animatedData = State(initialValue: Array(repeating: 0, count: data.count))
        }
        
        var body: some View {
            HStack(alignment: .bottom, spacing: 8) {
                ForEach(0..<animatedData.count, id: \.self) { index in
                    let normalizedHeight = maxHeight * (animatedData[index] / (data.max() ?? 1))
                    
                    Rectangle()
                        .fill(colors[index % colors.count])
                        .frame(width: 30, height: normalizedHeight)
                        .cornerRadius(4)
                        .animation(
                            .easeInOut(duration: 0.8)
                            .delay(Double(index) * 0.1),
                            value: animatedData[index]
                        )
                }
            }
            .onAppear {
                for (index, value) in data.enumerated() {
                    DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.1) {
                        withAnimation(.easeInOut(duration: 0.8)) {
                            animatedData[index] = value
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 环形进度动画
    struct AnimatedCircularProgress: View {
        let progress: Double
        let lineWidth: CGFloat
        let color: Color
        
        @State private var animatedProgress: Double = 0
        
        var body: some View {
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: lineWidth)
                
                Circle()
                    .trim(from: 0, to: animatedProgress)
                    .stroke(
                        color,
                        style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.5), value: animatedProgress)
                
                Text("\(Int(animatedProgress * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .onAppear {
                withAnimation(.easeInOut(duration: 1.5)) {
                    animatedProgress = progress
                }
            }
            .onChange(of: progress) { newProgress in
                withAnimation(.easeInOut(duration: 1.0)) {
                    animatedProgress = newProgress
                }
            }
        }
    }
    
    // MARK: - 波浪动画
    struct WaveAnimation: View {
        let height: CGFloat
        let color: Color
        
        @State private var waveOffset = 0.0
        
        var body: some View {
            GeometryReader { geometry in
                Path { path in
                    let width = geometry.size.width
                    let wavelength = width / 2
                    
                    path.move(to: CGPoint(x: 0, y: height))
                    
                    for x in stride(from: 0, through: width, by: 1) {
                        let relativeX = x / wavelength
                        let y = sin(relativeX * Double.pi * 2 + waveOffset) * 10 + height
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                    
                    path.addLine(to: CGPoint(x: geometry.size.width, y: geometry.size.height))
                    path.addLine(to: CGPoint(x: 0, y: geometry.size.height))
                    path.closeSubpath()
                }
                .fill(color)
            }
            .onAppear {
                withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: false)) {
                    waveOffset = Double.pi * 2
                }
            }
        }
    }
}

// MARK: - 交互动画组件
struct InteractiveAnimations {
    
    // MARK: - 按压反馈动画
    struct PressableButton<Content: View>: View {
        let action: () -> Void
        let content: Content
        
        @State private var isPressed = false
        
        init(action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
            self.action = action
            self.content = content()
        }
        
        var body: some View {
            Button(action: action) {
                content
                    .scaleEffect(isPressed ? 0.95 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: isPressed)
            }
            .buttonStyle(PlainButtonStyle())
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
        }
    }
    
    // MARK: - 悬停效果动画
    struct HoverEffect: ViewModifier {
        @State private var isHovered = false
        
        func body(content: Content) -> some View {
            content
                .scaleEffect(isHovered ? 1.05 : 1.0)
                .shadow(
                    color: .black.opacity(isHovered ? 0.1 : 0.05),
                    radius: isHovered ? 8 : 4,
                    x: 0,
                    y: isHovered ? 4 : 2
                )
                .animation(.easeInOut(duration: 0.2), value: isHovered)
                .onHover { hovering in
                    isHovered = hovering
                }
        }
    }
    
    // MARK: - 拖拽反馈动画
    struct DragFeedback: ViewModifier {
        @State private var dragOffset = CGSize.zero
        
        func body(content: Content) -> some View {
            content
                .offset(dragOffset)
                .scaleEffect(dragOffset == .zero ? 1.0 : 1.1)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: dragOffset)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            dragOffset = value.translation
                        }
                        .onEnded { _ in
                            dragOffset = .zero
                        }
                )
        }
    }
}

// MARK: - View扩展
extension View {
    func pageTransition(isActive: Bool) -> some View {
        modifier(AnimatedTransitions.PageTransition(isActive: isActive))
    }
    
    func cardAppearAnimation(delay: Double = 0) -> some View {
        modifier(AnimatedTransitions.CardAppearAnimation(delay: delay))
    }
    
    func pulseAnimation() -> some View {
        modifier(AnimatedTransitions.PulseAnimation())
    }
    
    func floatingAnimation() -> some View {
        modifier(AnimatedTransitions.FloatingAnimation())
    }
    
    func hoverEffect() -> some View {
        modifier(InteractiveAnimations.HoverEffect())
    }
    
    func dragFeedback() -> some View {
        modifier(InteractiveAnimations.DragFeedback())
    }
    
    func pressableButton(action: @escaping () -> Void) -> some View {
        InteractiveAnimations.PressableButton(action: action) {
            self
        }
    }
}

// MARK: - 主题动画集成
struct ThemeAnimatedContainer<Content: View>: View {
    let content: Content
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showContent = false
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        ZStack {
            // 动画背景
            AnimatedTransitions.AnimatedGradientBackground(
                colors: [
                    themeManager.currentTheme.backgroundColor,
                    themeManager.currentTheme.backgroundColor.opacity(0.8),
                    themeManager.currentTheme.accentColor.opacity(0.1)
                ]
            )
            .ignoresSafeArea()
            
            // 主要内容
            content
                .pageTransition(isActive: showContent)
        }
        .onAppear {
            showContent = true
        }
        .onChange(of: themeManager.currentTheme.id) { _ in
            showContent = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                showContent = true
            }
        }
    }
}

// MARK: - 加载状态动画
struct LoadingStateAnimation: View {
    let isLoading: Bool
    let message: String
    
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        Group {
            if isLoading {
                VStack(spacing: 16) {
                    AnimatedTransitions.RotatingLoadingAnimation()
                        .font(.title)
                        .foregroundColor(themeManager.currentTheme.accentColor)
                    
                    Text(message)
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor)
                        .pulseAnimation()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(themeManager.currentTheme.backgroundColor.opacity(0.9))
                .transition(.opacity.combined(with: .scale))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isLoading)
    }
}

// MARK: - 成功状态动画
struct SuccessStateAnimation: View {
    @State private var showCheckmark = false
    @State private var showMessage = false
    
    let message: String
    
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.2))
                    .frame(width: 60, height: 60)
                    .scaleEffect(showCheckmark ? 1.0 : 0.0)
                
                Image(systemName: "checkmark")
                    .font(.title)
                    .foregroundColor(.green)
                    .scaleEffect(showCheckmark ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.6).delay(0.2), value: showCheckmark)
            }
            .animation(.spring(response: 0.5, dampingFraction: 0.6), value: showCheckmark)
            
            if showMessage {
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.textColor)
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
        }
        .onAppear {
            showCheckmark = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showMessage = true
                }
            }
        }
    }
}