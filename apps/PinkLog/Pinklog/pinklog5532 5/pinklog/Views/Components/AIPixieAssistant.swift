//
//  AIPixieAssistant.swift
//  PinkLog
//
//  Created by World-Class Designer on 2025/1/17.
//  震撼的AI小精灵助手
//

import SwiftUI

struct AIPixieAssistant: View {
    @Binding var suggestion: String
    @State private var isVisible = false
    @State private var pixiePosition = CGPoint(x: 50, y: 100)
    @State private var floatOffset: CGFloat = 0
    @State private var sparkles: [SparkleParticle] = []
    @State private var isThinking = false
    @State private var showSuggestion = false
    
    var body: some View {
        ZStack {
            // 小精灵本体
            if isVisible {
                VStack(spacing: 8) {
                    // 小精灵头部
                    ZStack {
                        // 主体
                        Circle()
                            .fill(
                                RadialGradient(
                                    colors: [
                                        Color.pink.opacity(0.8),
                                        Color.purple.opacity(0.6),
                                        Color.blue.opacity(0.4)
                                    ],
                                    center: .topLeading,
                                    startRadius: 5,
                                    endRadius: 25
                                )
                            )
                            .frame(width: 50, height: 50)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.8), lineWidth: 2)
                            )
                            .shadow(color: .purple.opacity(0.3), radius: 10)
                        
                        // 眼睛
                        HStack(spacing: 8) {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 8, height: 8)
                                .overlay(
                                    Circle()
                                        .fill(Color.black)
                                        .frame(width: 4, height: 4)
                                )
                            
                            Circle()
                                .fill(Color.white)
                                .frame(width: 8, height: 8)
                                .overlay(
                                    Circle()
                                        .fill(Color.black)
                                        .frame(width: 4, height: 4)
                                )
                        }
                        .offset(y: -5)
                        
                        // 嘴巴
                        if isThinking {
                            Text("...")
                                .font(.caption)
                                .foregroundColor(.white)
                                .offset(y: 5)
                        } else {
                            Ellipse()
                                .fill(Color.white.opacity(0.8))
                                .frame(width: 6, height: 3)
                                .offset(y: 5)
                        }
                    }
                    .scaleEffect(isThinking ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isThinking)
                    
                    // 小精灵翅膀
                    HStack(spacing: 20) {
                        WingView(isLeft: true)
                        WingView(isLeft: false)
                    }
                    .offset(y: -25)
                }
                .offset(x: pixiePosition.x, y: pixiePosition.y + floatOffset)
                .onAppear {
                    startFloatingAnimation()
                    generateSparkles()
                }
                
                // 建议气泡
                if showSuggestion && !suggestion.isEmpty {
                    SuggestionBubble(text: suggestion)
                        .offset(x: pixiePosition.x + 80, y: pixiePosition.y - 20)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            
            // 魔法闪光粒子
            ForEach(sparkles.indices, id: \.self) { index in
                if index < sparkles.count {
                    SparkleView(sparkle: sparkles[index])
                }
            }
        }
        .onAppear {
            appearWithMagic()
        }
        .onChange(of: suggestion) { newSuggestion in
            if !newSuggestion.isEmpty {
                showSuggestionWithAnimation()
            }
        }
    }
    
    private func appearWithMagic() {
        // 魔法出现动画
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
            isVisible = true
        }
        
        // 生成出现时的粒子效果
        for _ in 0..<20 {
            let sparkle = SparkleParticle(
                x: pixiePosition.x + CGFloat.random(in: -30...30),
                y: pixiePosition.y + CGFloat.random(in: -30...30),
                scale: CGFloat.random(in: 0.5...1.5),
                opacity: Double.random(in: 0.7...1.0),
                color: [.pink, .purple, .blue, .cyan].randomElement()!
            )
            sparkles.append(sparkle)
        }
        
        // 清理粒子
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            sparkles.removeAll()
        }
    }
    
    private func startFloatingAnimation() {
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            floatOffset = 10
        }
    }
    
    private func generateSparkles() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { timer in
            guard isVisible else {
                timer.invalidate()
                return
            }
            
            // 定期生成闪光
            let sparkle = SparkleParticle(
                x: pixiePosition.x + CGFloat.random(in: -20...20),
                y: pixiePosition.y + CGFloat.random(in: -20...20),
                scale: CGFloat.random(in: 0.3...0.8),
                opacity: Double.random(in: 0.5...1.0),
                color: [.pink, .purple, .yellow].randomElement()!
            )
            sparkles.append(sparkle)
            
            // 限制粒子数量
            if sparkles.count > 10 {
                sparkles.removeFirst()
            }
        }
    }
    
    private func showSuggestionWithAnimation() {
        isThinking = true
        
        // 思考动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isThinking = false
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showSuggestion = true
            }
            
            // 3秒后隐藏建议
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showSuggestion = false
                }
            }
        }
    }
}

// MARK: - 小精灵翅膀
struct WingView: View {
    let isLeft: Bool
    @State private var wingRotation: Double = 0
    
    var body: some View {
        Ellipse()
            .fill(
                LinearGradient(
                    colors: [
                        Color.white.opacity(0.8),
                        Color.cyan.opacity(0.6),
                        Color.blue.opacity(0.4)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: 15, height: 25)
            .rotationEffect(.degrees(wingRotation))
            .onAppear {
                withAnimation(.easeInOut(duration: 0.3).repeatForever(autoreverses: true)) {
                    wingRotation = isLeft ? -20 : 20
                }
            }
    }
}

// MARK: - 建议气泡
struct SuggestionBubble: View {
    let text: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "sparkles")
                    .foregroundColor(.yellow)
                    .scaleEffect(0.8)
                
                Text("小精灵建议")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
            }
            
            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
                .lineLimit(3)
                .multilineTextAlignment(.leading)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white)
                .shadow(color: .purple.opacity(0.3), radius: 10, x: 0, y: 5)
                .overlay(
                    RoundedRectangle(cornerRadius: 15)
                        .stroke(
                            LinearGradient(
                                colors: [.pink, .purple, .blue],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
        .frame(maxWidth: 150)
        .overlay(
            // 气泡箭头
            Path { path in
                path.move(to: CGPoint(x: -8, y: 20))
                path.addLine(to: CGPoint(x: 0, y: 15))
                path.addLine(to: CGPoint(x: 0, y: 25))
                path.closeSubpath()
            }
            .fill(Color.white)
            .offset(x: -8)
        )
    }
}

// MARK: - 闪光粒子
struct SparkleParticle: Identifiable {
    let id = UUID()
    let x: CGFloat
    let y: CGFloat
    let scale: CGFloat
    let opacity: Double
    let color: Color
}

struct SparkleView: View {
    let sparkle: SparkleParticle
    @State private var offset: CGSize = .zero
    @State private var opacity: Double = 1.0
    @State private var rotation: Double = 0
    
    var body: some View {
        Image(systemName: "sparkle")
            .foregroundColor(sparkle.color)
            .scaleEffect(sparkle.scale)
            .opacity(opacity)
            .rotationEffect(.degrees(rotation))
            .position(x: sparkle.x, y: sparkle.y)
            .offset(offset)
            .onAppear {
                withAnimation(.easeOut(duration: 2.0)) {
                    offset = CGSize(
                        width: CGFloat.random(in: -30...30),
                        height: CGFloat.random(in: -50...(-10))
                    )
                    opacity = 0
                    rotation = Double.random(in: 0...360)
                }
            }
    }
}

// MARK: - AI建议管理器
class AIPixieManager: ObservableObject {
    @Published var currentSuggestion = ""
    
    private let productSuggestions = [
        "这个产品看起来很棒！建议价格在 ¥100-200 之间",
        "根据图片分析，这可能是电子产品类别",
        "建议添加使用频率标签，这样可以更好地追踪",
        "这个颜色很漂亮！建议设置满意度为 8/10",
        "小精灵觉得这个产品值得收藏！",
        "建议为这个产品设置提醒，定期回顾使用感受"
    ]
    
    func generateSuggestion(for image: UIImage?) {
        // 模拟AI分析过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.currentSuggestion = self.productSuggestions.randomElement() ?? ""
        }
    }
    
    func generatePriceSuggestion(for productName: String) {
        let suggestions = [
            "根据产品名称，建议价格范围 ¥50-150",
            "这类产品通常在 ¥80-250 之间",
            "小精灵建议设置预算提醒哦！",
            "这个价位很合理，快收藏吧！"
        ]
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.currentSuggestion = suggestions.randomElement() ?? ""
        }
    }
    
    func clearSuggestion() {
        currentSuggestion = ""
    }
}

// MARK: - 使用示例
struct AIPixieAssistant_Preview: View {
    @StateObject private var pixieManager = AIPixieManager()
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.1)
                .ignoresSafeArea()
            
            AIPixieAssistant(suggestion: $pixieManager.currentSuggestion)
            
            VStack {
                Spacer()
                
                Button("生成建议") {
                    pixieManager.generateSuggestion(for: nil)
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding()
            }
        }
    }
}

#Preview {
    AIPixieAssistant_Preview()
} 