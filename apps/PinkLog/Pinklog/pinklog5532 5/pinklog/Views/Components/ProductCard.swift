import SwiftUI

struct ProductCard: View {
    let product: Product
    var showDetails: Bool = true

    var body: some View {
        ZStack {
            // 主要卡片内容
            HStack(spacing: 12) {
                // 产品图片 - 使用优化后的AsyncImageView
                ZStack {
                    if let imageData = product.images, let productId = product.id?.uuidString {
                        AsyncImageView(
                            imageData: imageData,
                            cacheKey: "product_card_\(productId)",
                            loadThumbnail: true,
                            contentMode: .fill
                        )
                        .frame(width: 80, height: 80)
                        .cornerRadius(8)
                    } else {
                        Image(systemName: "photo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .padding(20)
                            .frame(width: 80, height: 80)
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }

                    // 产品图片上的状态标记（仅消耗品和状态标记）
                    VStack {
                        HStack {
                            Spacer()
                            
                            // 🎨 优雅的消耗品标记
                            if product.isConsumable {
                                ZStack {
                                    // 背景圆形
                                    Circle()
                                        .fill(Color.white.opacity(0.9))
                                        .frame(width: 20, height: 20)
                                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                                    
                                    // 水滴图标
                                    Image(systemName: "drop.fill")
                                        .font(.system(size: 10, weight: .semibold))
                                        .foregroundColor(stockColor(for: product.stockPercentage))
                                }
                            } else {
                               // 产品状态标记 - 仅对非消耗品显示
                               Circle()
                                   .fill(product.status.color)
                                   .frame(width: 12, height: 12)
                           }
                        }

                        Spacer()
                    }
                    .padding(4)
                }
                .frame(width: 80, height: 80)

                // 产品信息
                VStack(alignment: .leading, spacing: 6) {
                    // 产品名称 - 主标题
                    Text(product.name ?? "未命名产品")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    // 品牌型号 - 副标题
                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand + (product.model != nil ? " · \(product.model!)" : ""))
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    if showDetails {
                        // 价格和值度信息行
                        HStack(alignment: .center, spacing: 12) {
                            // 价格信息
                            Text("¥\(Int(product.price))")
                                .font(.system(size: 15, weight: .medium))
                                .foregroundColor(.primary)

                            Spacer()
                            
                            // 使用次数
                            Text("使用\(product.totalUsageCount)次")
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(.secondary)
                            
                            // 值度指数
                            HStack(spacing: 3) {
                                Text("值度")
                                    .font(.system(size: 12, weight: .regular))
                                    .foregroundColor(.secondary)

                                Text("\(Int(product.improvedWorthItIndex()))")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(product.status.color)
                                
                                // 置信度指示器
                                let confidence = product.confidenceMetrics
                                Circle()
                                    .fill(confidenceColor(confidence.level))
                                    .frame(width: 6, height: 6)
                                    .help(confidence.level.recommendation)
                            }
                        }
                        

                        
                        // 借阅状态标记（如果需要显示）
                        if product.loanStatus != .available {
                            HStack {
                                Spacer()
                                loanStatusBadge
                            }
                            .padding(.top, 4)
                        }

                        // 消耗品库存显示
                        if product.isConsumable {
                            VStack(spacing: 4) {
                                // 库存信息行
                                HStack(spacing: 6) {
                                    // 库存状态圆点
                                    Circle()
                                        .fill(stockColor(for: product.stockPercentage))
                                        .frame(width: 6, height: 6)
                                    
                                    // 库存数值
                                    Text("\(String(format: "%.1f", product.actualCurrentQuantity))\(product.unitType ?? "")")
                                        .font(.system(size: 12, weight: .regular))
                                        .foregroundColor(.primary)
                                    
                                    Spacer()
                                }
                                
                                // 库存进度条和百分比
                                HStack(spacing: 8) {
                                    // 库存进度条
                                    ZStack(alignment: .leading) {
                                        // 背景轨道
                                        Capsule()
                                            .fill(Color.gray.opacity(0.2))
                                            .frame(height: 4)
                                        
                                        // 库存进度
                                        Capsule()
                                            .fill(stockColor(for: product.stockPercentage))
                                            .frame(width: max(4, UIScreen.main.bounds.width * 0.45 * product.stockPercentage), height: 4)
                                    }
                                    
                                    // 百分比 - 放在库存条后面
                                    Text("\(Int(product.stockPercentage * 100))%")
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(stockColor(for: product.stockPercentage))
                                        .frame(width: 35, alignment: .trailing)
                                }
                                
                                // 智能状态提示（仅在需要时显示）
                                if product.stockPercentage < 0.2 || product.lifecycleStatus != .active {
                                    HStack(spacing: 4) {
                                        Image(systemName: product.lifecycleStatus.icon)
                                            .font(.system(size: 10, weight: .medium))
                                            .foregroundColor(product.lifecycleStatus.color)
                                        
                                        Text(product.lifecycleStatus.description)
                                            .font(.system(size: 11, weight: .medium))
                                            .foregroundColor(product.lifecycleStatus.color)
                                        
                                        Spacer()
                                        
                                        // 预估天数（仅在库存较低时显示）
                                        if product.stockPercentage < 0.3, let days = product.estimatedDaysUntilEmpty {
                                            Text("\(days)天")
                                                .font(.system(size: 10, weight: .medium, design: .rounded))
                                                .foregroundColor(.secondary)
                                                .padding(.horizontal, 4)
                                                .padding(.vertical, 1)
                                                .background(
                                                    Capsule()
                                                        .fill(Color.gray.opacity(0.1))
                                                )
                                        }
                                    }
                                }
                            }
                            .padding(.top, 6)
                        }
                        
                        // 只在有类别时显示类别行（非消耗品）
                        if !product.isConsumable, let category = product.category?.name {
                            HStack {
                                Spacer()
                                Text(category)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.2))
                                    .cornerRadius(4)
                            }
                        }
                    }
                }

                Spacer()
            }
            .padding(12)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
            

        }
    }

    // 借阅状态标记
    private var loanStatusBadge: some View {
        Text(product.loanStatus.rawValue)
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [product.loanStatus.color, product.loanStatus.color.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: product.loanStatus.color.opacity(0.3), radius: 1, x: 0, y: 1)
            )
    }

    // 置信度颜色映射
    private func confidenceColor(_ level: ConfidenceMetrics.ConfidenceLevel) -> Color {
        switch level {
        case .veryHigh: return .green
        case .high: return .blue
        case .medium: return .orange
        case .low: return .yellow
        case .veryLow: return .red
        }
    }
    
    // 🎨 优雅的库存颜色映射 - 柔和且具有良好可读性
    private func stockColor(for percentage: Double) -> Color {
        let clampedPercentage = max(0, min(1, percentage))
        
        switch clampedPercentage {
        case 0.8...1.0:
            // 充足库存：深海绿 - 优雅且专业
            return Color(red: 0.2, green: 0.7, blue: 0.5)
            
        case 0.6..<0.8:
            // 良好库存：森林绿 - 自然且舒适
            return Color(red: 0.3, green: 0.75, blue: 0.4)
            
        case 0.4..<0.6:
            // 中等库存：琥珀色 - 温暖且平衡
            return Color(red: 0.9, green: 0.7, blue: 0.2)
            
        case 0.2..<0.4:
            // 偏低库存：暖橙色 - 提醒但不刺眼
            return Color(red: 0.95, green: 0.6, blue: 0.3)
            
        case 0.05..<0.2:
            // 低库存：珊瑚红 - 警示但优雅
            return Color(red: 0.9, green: 0.45, blue: 0.4)
            
        default:
            // 极低库存：深红色 - 强烈但不刺眼
            return Color(red: 0.8, green: 0.3, blue: 0.3)
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let product = Product(context: context)
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.model = "M1 Pro"
    product.price = 14999
    product.purchaseDate = Date()

    return ProductCard(product: product)
        .padding()
        .previewLayout(.sizeThatFits)
}
