//
//  AddEditFamilyMemberView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  家庭成员管理 - 添加/编辑成员视图
//

import SwiftUI

struct AddEditFamilyMemberView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var familySharingService: FamilySharingService
    
    let member: FamilyMember?
    let isEditing: Bool
    
    @State private var name: String = ""
    @State private var nickname: String = ""
    @State private var selectedRole: FamilyRole = .member
    @State private var notes: String = ""
    @State private var isActive: Bool = true
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(familySharingService: FamilySharingService, member: FamilyMember? = nil) {
        self.familySharingService = familySharingService
        self.member = member
        self.isEditing = member != nil
        
        if let member = member {
            _name = State(initialValue: member.name)
            _nickname = State(initialValue: member.nickname ?? "")
            _selectedRole = State(initialValue: member.role)
            _notes = State(initialValue: member.notes ?? "")
            _isActive = State(initialValue: member.isActive)
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section("基本信息") {
                    TextField("姓名", text: $name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    TextField("昵称（可选）", text: $nickname)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // 角色设置
                Section("角色设置") {
                    Picker("角色", selection: $selectedRole) {
                        ForEach(FamilyRole.allCases.filter { $0 != .owner || isEditing }) { role in
                            HStack {
                                Image(systemName: role.icon)
                                    .foregroundColor(role.color)
                                Text(role.displayName)
                            }
                            .tag(role)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    if selectedRole != .owner {
                        Toggle("活跃状态", isOn: $isActive)
                    }
                }
                
                // 备注
                Section("备注") {
                    TextField("备注信息（可选）", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // 权限说明
                Section("角色权限") {
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(selectedRole.permissions) { permission in
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.caption)
                                Text(permission.displayName)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
            .navigationTitle(isEditing ? "编辑成员" : "添加成员")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "保存" : "添加") {
                        saveOrUpdateMember()
                    }
                    .disabled(name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private func saveOrUpdateMember() {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else {
            errorMessage = "请输入成员姓名"
            showingError = true
            return
        }
        
        Task {
            do {
                if isEditing, let existingMember = member {
                    // 更新现有成员
                    var updatedMember = existingMember
                    updatedMember.name = trimmedName
                    updatedMember.nickname = nickname.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : nickname.trimmingCharacters(in: .whitespacesAndNewlines)
                    updatedMember.role = selectedRole
                    updatedMember.notes = notes.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
                    updatedMember.isActive = selectedRole == .owner ? true : isActive
                    
                    try await familySharingService.updateFamilyMember(updatedMember)
                } else {
                    // 添加新成员
                    let newMember = FamilyMember(
                        name: trimmedName,
                        nickname: nickname.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : nickname.trimmingCharacters(in: .whitespacesAndNewlines),
                        role: selectedRole,
                        isActive: selectedRole == .owner ? true : isActive,
                        notes: notes.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
                    )
                    
                    try await familySharingService.addFamilyMember(newMember)
                }
                
                dismiss()
            } catch {
                errorMessage = "操作失败: \(error.localizedDescription)"
                showingError = true
            }
        }
    }
}

// MARK: - 预览
struct AddEditFamilyMemberView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let subscriptionService = SubscriptionService(
            context: context,
            coreDataRepository: CoreDataRepository<Product>(context: context)
        )
        let familySharingService = FamilySharingService(
            context: context,
            subscriptionService: subscriptionService
        )
        
        Group {
            // 添加成员
            AddEditFamilyMemberView(familySharingService: familySharingService)
                .previewDisplayName("添加成员")
            
            // 编辑成员
            AddEditFamilyMemberView(
                familySharingService: familySharingService,
                member: FamilyMember(name: "张三", nickname: "小张", role: .member)
            )
            .previewDisplayName("编辑成员")
        }
    }
}