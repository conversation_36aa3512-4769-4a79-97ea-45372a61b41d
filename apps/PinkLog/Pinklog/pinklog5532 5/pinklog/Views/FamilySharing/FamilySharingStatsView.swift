//
//  FamilySharingStatsView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  家庭共享使用统计界面
//

import SwiftUI
import Charts

// MARK: - 趋势数据模型
struct TrendData {
    let date: Date
    let usage: Double
}

struct FamilySharingStatsView: View {
    @ObservedObject var familySharingService: FamilySharingService
    @State private var selectedTimeRange: TimeRange = .month
    @State private var selectedSubscription: SharedSubscription?
    @State private var familyStats: FamilyUsageStatistics?
    @State private var isLoading = false
    
    enum TimeRange: String, CaseIterable {
        case week = "本周"
        case month = "本月"
        case quarter = "本季度"
        case year = "本年"
        
        var days: Int {
            switch self {
            case .week: return 7
            case .month: return 30
            case .quarter: return 90
            case .year: return 365
            }
        }
        
        var period: StatisticsPeriod {
            switch self {
            case .week: return .weekly
            case .month: return .monthly
            case .quarter: return .quarterly
            case .year: return .yearly
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 时间范围选择器
                    timeRangeSelector
                    
                    // 订阅选择器
                    subscriptionSelector
                    
                    if let stats = familyStats {
                        // 总览卡片
                        overviewCards(stats: stats)
                        
                        // 成员使用分布图表
                        memberUsageChart(stats: stats)
                        
                        // 成本分摊详情
                        costSharingDetails(stats: stats)
                        
                        // 使用趋势图表
                        usageTrendChart(stats: stats)
                        
                        // 成员详细统计
                        memberDetailStats(stats: stats)
                    } else if isLoading {
                        ProgressView("加载中...")
                            .frame(maxWidth: .infinity, minHeight: 200)
                    } else {
                        emptyStateView
                    }
                }
                .padding()
            }
            .navigationTitle("家庭使用统计")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            loadFamilyStats()
        }
        .onChange(of: selectedTimeRange) { _ in
            loadFamilyStats()
        }
        .onChange(of: selectedSubscription) { _ in
            loadFamilyStats()
        }
    }
    
    // MARK: - 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("时间范围")
                .font(.headline)
                .fontWeight(.semibold)
            
            Picker("时间范围", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.rawValue).tag(range)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - 订阅选择器
    private var subscriptionSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("订阅服务")
                .font(.headline)
                .fontWeight(.semibold)
            
            Menu {
                Button("所有订阅") {
                    selectedSubscription = nil
                }
                
                ForEach(familySharingService.sharedSubscriptions, id: \.id) { subscription in
                    Button("订阅 \(subscription.subscriptionId.uuidString.prefix(8))") {
                        selectedSubscription = subscription
                    }
                }
            } label: {
                HStack {
                    Text(selectedSubscription != nil ? "订阅 \(selectedSubscription!.subscriptionId.uuidString.prefix(8))" : "所有订阅")
                        .foregroundColor(.primary)
                    Spacer()
                    Image(systemName: "chevron.down")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }
    
    // MARK: - 总览卡片
    private func overviewCards(stats: FamilyUsageStatistics) -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            // 总使用时长
            StatCard(
                title: "总使用时长",
                value: "\(Int(stats.totalUsageTime / 3600))小时",
                icon: "clock.fill",
                color: .blue
            )
            
            // 总成本
            StatCard(
                title: "总成本",
                value: String(format: "¥%.2f", calculateTotalCost(stats)),
                icon: "yensign.circle.fill",
                color: .green
            )
            
            // 活跃成员
            StatCard(
                title: "活跃成员",
                value: "\(stats.memberUsageDistribution.count)人",
                icon: "person.2.fill",
                color: .orange
            )
            
            // 平均成本
            StatCard(
                title: "人均成本",
                value: String(format: "¥%.2f", calculateAverageCost(stats)),
                icon: "person.crop.circle.badge.plus",
                color: .purple
            )
        }
    }
    
    // MARK: - 成员使用分布图表
    private func memberUsageChart(stats: FamilyUsageStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("成员使用分布")
                .font(.headline)
                .fontWeight(.semibold)
            
            if #available(iOS 16.0, *) {
                Chart(Array(stats.memberUsageDistribution.values), id: \.memberId) { memberStat in
                    SectorMark(
                        angle: .value("使用时长", memberStat.usageTime),
                        innerRadius: .ratio(0.5),
                        angularInset: 2
                    )
                    .foregroundStyle(by: .value("成员", getMemberName(memberStat.memberId)))
                    .opacity(0.8)
                }
                .frame(height: 200)
                .chartLegend(position: .bottom, alignment: .center)
            } else {
                // iOS 15 兼容性处理
                VStack(spacing: 8) {
                    ForEach(Array(stats.memberUsageDistribution.values), id: \.memberId) { memberStat in
                        HStack {
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 12, height: 12)
                            
                            Text(getMemberName(memberStat.memberId))
                                .font(.subheadline)
                            
                            Spacer()
                            
                            Text("\(Int(memberStat.usageTime / 3600))h")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 成本分摊详情
    private func costSharingDetails(stats: FamilyUsageStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("成本分摊详情")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(Array(stats.memberUsageDistribution.values), id: \.memberId) { memberStat in
                HStack {
                    // 成员头像和姓名
                    HStack(spacing: 12) {
                        Circle()
                            .fill(Color.blue.opacity(0.2))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: "person.fill")
                                    .foregroundColor(.blue)
                            )
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(getMemberName(memberStat.memberId))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text("\(memberStat.usageSessions)次使用")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                    
                    // 成本信息
                    VStack(alignment: .trailing, spacing: 2) {
                        Text(String(format: "¥%.2f", memberStat.costShare))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        Text(String(format: "%.1f%%", (memberStat.costShare / calculateTotalCost(stats)) * 100))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 8)
                
                if memberStat.memberId != Array(stats.memberUsageDistribution.values).last?.memberId {
                    Divider()
                }
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 使用趋势图表
    private func usageTrendChart(stats: FamilyUsageStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("使用趋势")
                .font(.headline)
                .fontWeight(.semibold)
            
            if #available(iOS 16.0, *) {
                // 简化的趋势图表，使用模拟数据
                Chart(generateMockTrendData(), id: \.date) { dailyUsage in
                    LineMark(
                        x: .value("日期", dailyUsage.date),
                        y: .value("使用时长", dailyUsage.usage)
                    )
                    .foregroundStyle(.blue)
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 150)
            } else {
                Text("暂无趋势数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 成员详细统计
    private func memberDetailStats(stats: FamilyUsageStatistics) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("成员详细统计")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(Array(stats.memberUsageDistribution.values), id: \.memberId) { memberStat in
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text(getMemberName(memberStat.memberId))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text(String(format: "¥%.2f", memberStat.costShare))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        VStack {
                            Text("\(Int(memberStat.usageTime / 3600))")
                                .font(.title3)
                                .fontWeight(.semibold)
                            Text("小时")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack {
                            Text("\(memberStat.usageSessions)")
                                .font(.title3)
                                .fontWeight(.semibold)
                            Text("会话")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack {
                            Text("\(Int(memberStat.averageSessionDuration / 60))")
                                .font(.title3)
                                .fontWeight(.semibold)
                            Text("分钟")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.bar.doc.horizontal")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("暂无统计数据")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("开始使用共享订阅后，这里将显示详细的使用统计")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 300)
    }
    
    // MARK: - 辅助方法
    private func calculateTotalCost(_ stats: FamilyUsageStatistics) -> Double {
        return stats.costDistribution.values.reduce(0, +)
    }
    
    private func calculateAverageCost(_ stats: FamilyUsageStatistics) -> Double {
        let totalCost = calculateTotalCost(stats)
        let memberCount = stats.memberUsageDistribution.count
        return memberCount > 0 ? totalCost / Double(memberCount) : 0
    }
    
    private func getMemberName(_ memberId: UUID) -> String {
        if let member = familySharingService.familyMembers.first(where: { $0.id == memberId }) {
            return member.displayName
        }
        return "未知成员"
    }
    
    private func generateMockTrendData() -> [TrendData] {
        let calendar = Calendar.current
        let endDate = Date()
        var trendData: [TrendData] = []
        
        for i in 0..<selectedTimeRange.days {
            if let date = calendar.date(byAdding: .day, value: -i, to: endDate) {
                let usage = Double.random(in: 0.5...4.0) // 随机使用时长（小时）
                trendData.append(TrendData(date: date, usage: usage))
            }
        }
        
        return trendData.reversed()
    }
    
    // MARK: - 方法
    private func loadFamilyStats() {
        isLoading = true
        
        Task {
            do {
                let endDate = Date()
                let startDate = Calendar.current.date(byAdding: .day, value: -selectedTimeRange.days, to: endDate) ?? endDate
                
                let stats: FamilyUsageStatistics
                if let subscriptionId = selectedSubscription?.subscriptionId {
                    stats = familySharingService.getFamilyUsageStatistics(for: subscriptionId, period: selectedTimeRange.period)
                } else {
                    // 如果没有选择订阅，创建一个空的统计数据
                    stats = FamilyUsageStatistics(
                        subscriptionId: UUID(),
                        totalUsageSessions: 0,
                        totalUsageTime: 0,
                        memberUsageDistribution: [:],
                        costDistribution: [:],
                        period: selectedTimeRange.period
                    )
                }
                
                await MainActor.run {
                    self.familyStats = stats
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.familyStats = nil
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - 统计卡片组件
// StatCard已在Components/StatCard.swift中定义

// MARK: - 预览
struct FamilySharingStatsView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let subscriptionService = SubscriptionService(
            context: context,
            coreDataRepository: CoreDataRepository<Product>(context: context)
        )
        let familySharingService = FamilySharingService(
            context: context,
            subscriptionService: subscriptionService
        )
        
        FamilySharingStatsView(familySharingService: familySharingService)
    }
}