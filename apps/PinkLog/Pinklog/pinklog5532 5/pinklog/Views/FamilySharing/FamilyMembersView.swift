//
//  FamilyMembersView.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  家庭成员管理界面
//

import SwiftUI

struct FamilyMembersView: View {
    @StateObject private var familySharingService: FamilySharingService
    @State private var showingAddMember = false
    @State private var selectedMember: FamilyMember?
    @State private var showingMemberDetail = false
    @State private var showingDeleteAlert = false
    @State private var memberToDelete: FamilyMember?
    
    init(familySharingService: FamilySharingService) {
        self._familySharingService = StateObject(wrappedValue: familySharingService)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 家庭概览卡片
                familyOverviewCard
                
                // 成员列表
                membersList
            }
            .navigationTitle("家庭成员")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(trailing: 
                Button("添加成员") {
                    showingAddMember = true
                }
            )
        }
        .sheet(isPresented: $showingAddMember) {
            AddEditFamilyMemberView(familySharingService: familySharingService)
        }
        .sheet(item: $selectedMember) { member in
            AddEditFamilyMemberView(familySharingService: familySharingService, member: member)
        }
        .alert("删除成员", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let member = memberToDelete {
                    deleteMember(member)
                }
            }
        } message: {
            Text("确定要删除成员 \"\(memberToDelete?.displayName ?? "")\" 吗？此操作无法撤销。")
        }
    }
    
    // MARK: - 家庭概览卡片
    private var familyOverviewCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("家庭成员")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text("\(familySharingService.familyMembers.count) 位成员")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "house.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            HStack(spacing: 20) {
                StatCard(
                    title: "活跃成员",
                    value: "\(activeMembers.count)",
                    icon: "person.fill",
                    color: .green
                )
                
                StatCard(
                    title: "共享订阅",
                    value: "\(familySharingService.sharedSubscriptions.count)",
                    icon: "square.stack.3d.up.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "月度总支出",
                    value: "¥\(String(format: "%.0f", totalMonthlyCost))",
                    icon: "yensign.circle.fill",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        .padding(.horizontal)
        .padding(.top)
    }
    
    // MARK: - 成员列表
    private var membersList: some View {
        List {
            ForEach(familySharingService.familyMembers) { member in
                MemberRowView(member: member) {
                    selectedMember = member
                } onDelete: {
                    if member.role != .owner {
                        memberToDelete = member
                        showingDeleteAlert = true
                    }
                }
                .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                .listRowSeparator(.hidden)
            }
        }
        .listStyle(PlainListStyle())
        .padding(.top)
    }
    
    // MARK: - 计算属性
    private var activeMembers: [FamilyMember] {
        return familySharingService.familyMembers.filter { $0.isRecentlyActive }
    }
    
    private var totalMonthlyCost: Double {
        return familySharingService.sharedSubscriptions.reduce(0) { total, shared in
            return total + familySharingService.calculateMemberCostShare(for: shared.familyMembers.first ?? UUID(), subscriptionId: shared.subscriptionId)
        }
    }
    
    // MARK: - 方法
    private func deleteMember(_ member: FamilyMember) {
        Task {
            do {
                try await familySharingService.removeFamilyMember(member.id)
            } catch {
                print("删除成员失败: \(error)")
            }
        }
    }
}

// MARK: - 统计卡片组件
// StatCard已在Components/StatCard.swift中定义

// MARK: - 成员行视图
struct MemberRowView: View {
    let member: FamilyMember
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 头像
            memberAvatar
            
            // 成员信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(member.displayName)
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    if member.role == .owner {
                        Image(systemName: "crown.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }
                }
                
                Text(member.role.displayName)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if member.isRecentlyActive {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)
                        Text("最近活跃")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                } else if let lastActive = member.lastActiveDate {
                    Text("上次活跃: \(formatDate(lastActive))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 操作按钮
            HStack(spacing: 8) {
                Button(action: onEdit) {
                    Image(systemName: "pencil")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .frame(width: 32, height: 32)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
                
                if member.role != .owner {
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                            .frame(width: 32, height: 32)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var memberAvatar: some View {
        ZStack {
            Circle()
                .fill(member.role.color.opacity(0.2))
                .frame(width: 50, height: 50)
            
            if let avatar = member.avatar, !avatar.isEmpty {
                // 如果有自定义头像，这里可以加载图片
                Image(systemName: "person.fill")
                    .font(.title2)
                    .foregroundColor(member.role.color)
            } else {
                // 使用角色图标
                Image(systemName: member.role.icon)
                    .font(.title2)
                    .foregroundColor(member.role.color)
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - 预览
struct FamilyMembersView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let subscriptionService = SubscriptionService(
            context: context,
            coreDataRepository: CoreDataRepository<Product>(context: context)
        )
        let familySharingService = FamilySharingService(
            context: context,
            subscriptionService: subscriptionService
        )
        
        FamilyMembersView(familySharingService: familySharingService)
    }
}