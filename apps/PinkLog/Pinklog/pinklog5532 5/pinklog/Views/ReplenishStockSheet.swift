//
//  ReplenishStockSheet.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI

struct ReplenishStockSheet: View {
    let product: Product
    let viewModel: ConsumableViewModel
    
    @Environment(\.dismiss) private var dismiss
    @State private var addedQuantity: String = ""
    @State private var notes: String = ""
    @State private var isSubmitting = false
    @State private var errorMessage: String?
    @State private var selectedReplenishType: ReplenishType = .purchase
    
    // 补充类型
    enum ReplenishType: String, CaseIterable, Identifiable {
        case purchase = "购买补充"
        case gift = "获得赠送"
        case transfer = "转移库存"
        case found = "找到遗失"
        case other = "其他"
        
        var id: String { self.rawValue }
        
        var icon: String {
            switch self {
            case .purchase: return "cart.fill"
            case .gift: return "gift.fill"
            case .transfer: return "arrow.triangle.2.circlepath"
            case .found: return "magnifyingglass"
            case .other: return "plus.circle"
            }
        }
        
        var color: Color {
            switch self {
            case .purchase: return .blue
            case .gift: return .purple
            case .transfer: return .orange
            case .found: return .green
            case .other: return .gray
            }
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 产品信息
                productInfoSection
                
                // 补充类型
                replenishTypeSection
                
                // 补充量输入
                quantityInputSection
                
                // 备注
                notesSection
                
                // 预览信息
                previewSection
            }
            .navigationTitle("补充库存")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确认补充") {
                        submitReplenishment()
                    }
                    .disabled(isSubmitting || addedQuantity.isEmpty)
                }
            }
            .alert("错误", isPresented: .constant(errorMessage != nil)) {
                Button("确定") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
        .onAppear {
            setupInitialValues()
        }
    }
    
    // MARK: - 产品信息区域
    
    private var productInfoSection: some View {
        Section {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未知产品")
                        .font(.headline)
                    
                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack {
                        Image(systemName: product.stockStatus.icon)
                            .foregroundColor(product.stockStatus.color)
                        Text(product.stockStatus.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(product.stockStatus.color)
                    }
                    
                    Text("\(formatQuantity(product.actualCurrentQuantity))\(product.unitType ?? "")")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
            .padding(.vertical, 4)
            
            // 库存状态进度条
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("当前库存")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(Int(product.stockPercentage * 100))%")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                ProgressView(value: product.stockPercentage)
                    .progressViewStyle(LinearProgressViewStyle(tint: product.stockStatus.color))
            }
        } header: {
            Text("产品信息")
        }
    }
    
    // MARK: - 补充类型区域
    
    private var replenishTypeSection: some View {
        Section {
            Picker("补充类型", selection: $selectedReplenishType) {
                ForEach(ReplenishType.allCases) { type in
                    HStack {
                        Image(systemName: type.icon)
                            .foregroundColor(type.color)
                        Text(type.rawValue)
                    }
                    .tag(type)
                }
            }
            .pickerStyle(.menu)
        } header: {
            Text("补充方式")
        }
    }
    
    // MARK: - 补充量输入区域
    
    private var quantityInputSection: some View {
        Section {
            // 补充量输入
            HStack {
                Text("补充量")
                Spacer()
                TextField("输入补充量", text: $addedQuantity)
                    .keyboardType(.decimalPad)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 100)
                
                Text(product.unitType ?? "")
                    .foregroundColor(.secondary)
            }
            
            // 快速选择按钮
            quickSelectionButtons
            
            // 智能建议
            smartSuggestions
            
        } header: {
            Text("补充数量")
        } footer: {
            if let quantity = Double(addedQuantity), quantity > 0 {
                let newTotal = product.actualCurrentQuantity + quantity
                Text("补充后总量: \(formatQuantity(newTotal))\(product.unitType ?? "")")
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var quickSelectionButtons: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("快速选择")
                .font(.caption)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                ForEach([1, 5, 10, 20], id: \.self) { amount in
                    Button("\(amount)") {
                        addedQuantity = "\(amount)"
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
        }
    }
    
    private var smartSuggestions: some View {
        VStack(alignment: .leading, spacing: 8) {
            if product.quantity > 0 {
                let currentQuantity = product.actualCurrentQuantity
                let suggestedRefill = Double(product.quantity) - currentQuantity
                
                if suggestedRefill > 0 {
                    HStack {
                        Text("建议补充至满量")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Button("\(formatQuantity(suggestedRefill))\(product.unitType ?? "")") {
                            addedQuantity = formatQuantity(suggestedRefill)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                }
            }
            
            // 基于最低库存预警的建议
            if product.minStockAlert > 0 {
                let currentQuantity = product.actualCurrentQuantity
                if currentQuantity < product.minStockAlert {
                    let suggestedRefill = product.minStockAlert - currentQuantity + (product.minStockAlert * 0.5) // 补充到预警线以上50%
                    HStack {
                        Text("建议补充至安全库存")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Button("\(formatQuantity(suggestedRefill))\(product.unitType ?? "")") {
                            addedQuantity = formatQuantity(suggestedRefill)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                }
            }
        }
    }
    
    // MARK: - 备注区域
    
    private var notesSection: some View {
        Section {
            TextField("备注（可选）", text: $notes, axis: .vertical)
                .textFieldStyle(.roundedBorder)
                .lineLimit(2...4)
        } header: {
            Text("备注信息")
        }
    }
    
    // MARK: - 预览区域
    
    private var previewSection: some View {
        Section {
            if let quantity = Double(addedQuantity), quantity > 0 {
                VStack(spacing: 8) {
                    HStack {
                        Text("补充量")
                        Spacer()
                        Text("\(formatQuantity(quantity))\(product.unitType ?? "")")
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                    
                    HStack {
                        Text("补充前")
                        Spacer()
                        Text("\(formatQuantity(product.actualCurrentQuantity))\(product.unitType ?? "")")
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Text("补充后")
                        Spacer()
                        let newTotal = product.actualCurrentQuantity + quantity
                        Text("\(formatQuantity(newTotal))\(product.unitType ?? "")")
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                    
                    // 新的库存状态
                    let newTotal = product.actualCurrentQuantity + quantity
                    let newPercentage = calculateNewStockPercentage(newTotal: newTotal)
                    let newStatus = calculateNewStockStatus(percentage: newPercentage)
                    
                    HStack {
                        Text("库存状态")
                        Spacer()
                        HStack {
                            Image(systemName: newStatus.icon)
                                .foregroundColor(newStatus.color)
                            Text(newStatus.rawValue)
                                .fontWeight(.medium)
                                .foregroundColor(newStatus.color)
                        }
                    }
                    
                    // 预计使用天数
                    if let estimatedDays = calculateEstimatedDays(withTotal: newTotal) {
                        HStack {
                            Text("预计可用天数")
                            Spacer()
                            Text("\(estimatedDays) 天")
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        } header: {
            Text("补充预览")
        }
    }
    
    // MARK: - 辅助方法
    
    private func setupInitialValues() {
        // 根据当前库存状态设置默认备注
        switch selectedReplenishType {
        case .purchase:
            notes = "购买补充库存"
        case .gift:
            notes = "收到赠送"
        case .transfer:
            notes = "库存转移"
        case .found:
            notes = "找到遗失物品"
        case .other:
            notes = "其他方式补充"
        }
    }
    
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
    
    private func calculateNewStockPercentage(newTotal: Double) -> Double {
        guard product.quantity > 0 else {
            return 1.0
        }
        return min(newTotal / Double(product.quantity), 1.0)
    }
    
    private func calculateNewStockStatus(percentage: Double) -> Product.StockStatus {
        switch percentage {
        case 0.8...1.0:
            return .sufficient
        case 0.5..<0.8:
            return .moderate
        case 0.2..<0.5:
            return .low
        case 0.05..<0.2:
            return .critical
        default:
            return .empty
        }
    }
    
    private func calculateEstimatedDays(withTotal total: Double) -> Int? {
        guard total > 0 else { return nil }
        
        // 基于历史平均消耗量计算
        if let averageConsumption = viewModel.getRecommendedConsumption(for: product),
           averageConsumption > 0 {
            return Int(total / averageConsumption)
        }
        
        return nil
    }
    
    private func submitReplenishment() {
        guard let quantity = Double(addedQuantity), quantity > 0 else {
            errorMessage = "请输入有效的补充量"
            return
        }
        
        isSubmitting = true
        
        let finalNotes = notes.isEmpty ? selectedReplenishType.rawValue : notes
        
        Task {
            do {
                try await viewModel.replenishStock(
                    for: product,
                    addedQuantity: quantity,
                    notes: finalNotes
                )
                
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isSubmitting = false
                }
            }
        }
    }
}

#Preview {
    ReplenishStockSheet(
        product: Product(),
        viewModel: ConsumableViewModel()
    )
}