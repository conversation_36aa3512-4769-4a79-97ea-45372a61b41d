//
//  AddConsumableView.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import SwiftUI
import CoreData
import UIKit

struct AddConsumableView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = ConsumableViewModel()
    @ObservedObject private var productRepository = ProductRepository.shared
    
    @State private var selectedTab: AddConsumableTab = .existing
    @State private var selectedProduct: Product?
    @State private var searchText = ""
    @State private var isSubmitting = false
    @State private var errorMessage: String?
    
    // 新产品创建相关状态
    @State private var newProductName = ""
    @State private var newProductBrand = ""
    @State private var newProductCategory = ""
    
    // 消耗品设置相关状态
    @State private var currentQuantity = ""
    @State private var selectedUnit: UsageRecord.ConsumptionUnit = .piece
    @State private var minStockAlert = ""
    @State private var consumptionRate = ""
    
    enum AddConsumableTab: String, CaseIterable {
        case existing = "现有产品"
        case new = "新建产品"
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签页选择器
                tabSelector
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    existingProductView
                        .tag(AddConsumableTab.existing)
                    
                    newProductView
                        .tag(AddConsumableTab.new)
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
            }
            .navigationTitle("添加消耗品")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        submitConsumable()
                    }
                    .disabled(isSubmitting || !canSubmit)
                }
            }
            .alert("错误", isPresented: .constant(errorMessage != nil)) {
                Button("确定") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
        .task {
            await loadProducts()
        }
    }
    
    // MARK: - 标签页选择器
    
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(AddConsumableTab.allCases, id: \.self) { tab in
                Button {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedTab = tab
                    }
                } label: {
                    Text(tab.rawValue)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(selectedTab == tab ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedTab == tab ? Color.accentColor : Color.clear)
                        )
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - 现有产品视图
    
    private var existingProductView: some View {
        VStack(spacing: 16) {
            // 搜索栏
            SearchBar(text: $searchText, placeholder: "搜索产品...")
                .padding(.horizontal)
            
            // 产品列表
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(filteredProducts, id: \.id) { product in
                        ExistingProductCard(
                            product: product,
                            isSelected: selectedProduct?.id == product.id
                        ) {
                            selectedProduct = product
                        }
                    }
                }
                .padding(.horizontal)
            }
            
            // 消耗品设置区域
            if selectedProduct != nil {
                consumableSettingsView
            }
        }
    }
    
    // MARK: - 新建产品视图
    
    private var newProductView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 产品基本信息
                productBasicInfoSection
                
                // 消耗品设置
                consumableSettingsView
            }
            .padding()
        }
    }
    
    // MARK: - 产品基本信息区域
    
    private var productBasicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("产品信息")
                .font(.headline)
            
            VStack(spacing: 12) {
                TextField("产品名称", text: $newProductName)
                    .textFieldStyle(.roundedBorder)
                
                TextField("品牌（可选）", text: $newProductBrand)
                    .textFieldStyle(.roundedBorder)
                
                TextField("分类（可选）", text: $newProductCategory)
                    .textFieldStyle(.roundedBorder)
            }
        }
    }
    
    // MARK: - 消耗品设置区域
    
    private var consumableSettingsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("消耗品设置")
                .font(.headline)
            
            VStack(spacing: 12) {
                // 当前库存量
                HStack {
                    Text("当前库存")
                    Spacer()
                    TextField("数量", text: $currentQuantity)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 80)
                    
                    Picker("单位", selection: $selectedUnit) {
                        ForEach(UsageRecord.ConsumptionUnit.allCases) { unit in
                            Text(unit.symbol).tag(unit as UsageRecord.ConsumptionUnit)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(width: 80)
                }
                
                // 最低库存预警
                HStack {
                    Text("库存预警")
                    Spacer()
                    TextField("预警值", text: $minStockAlert)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 80)
                    Text(selectedUnit.symbol)
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)
                }
                
                // 预期消耗率（可选）
                HStack {
                    Text("日均消耗")
                    Spacer()
                    TextField("可选", text: $consumptionRate)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 80)
                    Text("\(selectedUnit.symbol)/天")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)
                }
            }
            
            // 智能建议
            smartSuggestions
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    private var smartSuggestions: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("智能建议")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            if let currentQty = Double(currentQuantity), currentQty > 0 {
                // 预警值建议
                if minStockAlert.isEmpty {
                    HStack {
                        Text("建议预警值")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Button("\(formatQuantity(currentQty * 0.2))\(selectedUnit.symbol) (20%)") {
                            minStockAlert = formatQuantity(currentQty * 0.2)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                }
                
                // 消耗率建议
                if consumptionRate.isEmpty {
                    VStack(spacing: 4) {
                        HStack {
                            Text("常见消耗率")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        HStack(spacing: 8) {
                            ForEach([0.5, 1.0, 2.0, 5.0], id: \.self) { rate in
                                Button("\(formatQuantity(rate))/天") {
                                    consumptionRate = formatQuantity(rate)
                                }
                                .buttonStyle(.bordered)
                                .controlSize(.small)
                            }
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var filteredProducts: [Product] {
        let nonConsumableProducts = productRepository.fetchAll().filter { $0.isConsumable != true }
        
        if searchText.isEmpty {
            return nonConsumableProducts
        } else {
            return nonConsumableProducts.filter { product in
                (product.name?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (product.brand?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }
    
    private var canSubmit: Bool {
        let hasValidQuantity = !currentQuantity.isEmpty && Double(currentQuantity) != nil && Double(currentQuantity)! > 0
        
        if selectedTab == .existing {
            return selectedProduct != nil && hasValidQuantity
        } else {
            return !newProductName.isEmpty && hasValidQuantity
        }
    }
    
    // MARK: - 辅助方法
    
    private func loadProducts() async {
        // ProductRepository doesn't have async loadProducts method
        // The data is loaded automatically when accessing fetchAll()
    }
    
    private func formatQuantity(_ quantity: Double) -> String {
        if quantity == floor(quantity) {
            return "\(Int(quantity))"
        } else {
            return String(format: "%.1f", quantity)
        }
    }
    
    private func createNewProduct(name: String, brand: String?, category: String?) async throws -> Product {
        return try await withCheckedThrowingContinuation { continuation in
            let _ = productRepository.getContext()
            
            let success = productRepository.save { context in
                let product = Product(context: context)
                product.id = UUID()
                product.name = name
                product.brand = brand
                product.category = nil // TODO: Handle category creation if needed
                product.purchaseDate = Date()
                // Product entity doesn't have createdAt attribute
                // Product entity doesn't have updatedAt attribute
                product.isConsumable = false // Will be set to true later
                
                continuation.resume(returning: product)
            }
            
            if !success {
                continuation.resume(throwing: NSError(domain: "ProductCreationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to create product"]))
            }
        }
    }
    
    private func submitConsumable() {
        guard let quantity = Double(currentQuantity), quantity > 0 else {
            errorMessage = "请输入有效的库存数量"
            return
        }
        
        isSubmitting = true
        
        Task {
            do {
                let product: Product
                
                if selectedTab == .existing {
                    guard let selectedProduct = selectedProduct else {
                        throw NSError(domain: "ConsumableError", code: 1, userInfo: [NSLocalizedDescriptionKey: "请选择一个产品"])
                    }
                    product = selectedProduct
                } else {
                    // 创建新产品
                    product = try await createNewProduct(
                        name: newProductName,
                        brand: newProductBrand.isEmpty ? nil : newProductBrand,
                        category: newProductCategory.isEmpty ? nil : newProductCategory
                    )
                }
                
                // 设置为消耗品
                let minAlert = Double(minStockAlert) ?? (quantity * 0.2)
                let consumption = Double(consumptionRate)
                
                try await viewModel.setAsConsumable(
                    product: product,
                    currentQuantity: quantity,
                    unitType: selectedUnit.rawValue,
                    minStockAlert: minAlert,
                    consumptionRate: consumption
                )
                
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isSubmitting = false
                }
            }
        }
    }
}

// MARK: - 现有产品卡片

struct ExistingProductCard: View {
    let product: Product
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button {
            onSelect()
        } label: {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未知产品")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.accentColor)
                        .font(.title3)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.accentColor.opacity(0.1) : Color(.systemBackground))
                    .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 搜索栏

struct SearchBar: View {
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField(placeholder, text: $text)
            
            if !text.isEmpty {
                Button {
                    text = ""
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

#Preview {
    AddConsumableView()
}