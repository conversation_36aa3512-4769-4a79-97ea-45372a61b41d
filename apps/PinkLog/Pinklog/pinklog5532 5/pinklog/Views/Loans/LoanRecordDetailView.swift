import SwiftUI

struct LoanRecordDetailView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var loanRecordViewModel: LoanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let loanRecord: LoanRecord

    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var showingReturnAlert = false
    @State private var showingLostAlert = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 状态标记
                statusBadge

                // 借出信息
                loanInfoSection

                // 日期信息
                dateInfoSection

                // 备注信息
                if let notes = loanRecord.notes, !notes.isEmpty {
                    notesSection
                }

                // 操作按钮
                actionButtons
            }
            .padding()
        }
        .navigationTitle("借出详情")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingEditSheet) {
            EditLoanRecordView(loanRecord: loanRecord)
        }
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除记录"),
                message: Text("确定要删除此借出记录吗？此操作无法撤销。"),
                primaryButton: .destructive(Text("删除")) {
                    deleteLoanRecord()
                },
                secondaryButton: .cancel()
            )
        }
        .alert("标记为已归还", isPresented: $showingReturnAlert) {
            Button("取消", role: .cancel) { }
            Button("确认归还", role: .none) {
                markAsReturned()
            }
        } message: {
            Text("确认该物品已归还？")
        }
        .alert("标记为已丢失", isPresented: $showingLostAlert) {
            Button("取消", role: .cancel) { }
            Button("确认丢失", role: .destructive) {
                markAsLost()
            }
        } message: {
            Text("确认该物品已丢失？")
        }
    }

    // 状态标记
    private var statusBadge: some View {
        HStack {
            Spacer()

            Text(loanRecord.loanStatus.rawValue)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(loanRecord.loanStatus.color)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(loanRecord.loanStatus.color.opacity(0.2))
                .cornerRadius(16)
        }
    }

    // 借出信息
    private var loanInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("借出信息")
                .font(.headline)
                .foregroundColor(.primary)

            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("借出对象")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(loanRecord.borrowerName ?? "")
                        .font(.body)
                }

                Spacer()

                if let contactInfo = loanRecord.contactInfo, !contactInfo.isEmpty {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("联系方式")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(contactInfo)
                            .font(.body)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 日期信息
    private var dateInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("日期信息")
                .font(.headline)
                .foregroundColor(.primary)

            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("借出日期")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(loanRecord.createdAt?.formatted(date: .abbreviated, time: .omitted) ?? "未知")
                        .font(.body)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("预计归还日期")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(loanRecord.dueDate?.formatted(date: .abbreviated, time: .omitted) ?? "未知")
                        .font(.body)
                        .foregroundColor(loanRecord.isOverdue ? .red : .primary)
                }
            }

            if let returnDate = loanRecord.returnDate {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("实际归还日期")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(returnDate.formatted(date: .abbreviated, time: .omitted))
                            .font(.body)
                    }

                    Spacer()

                    if let duration = loanRecord.loanDuration {
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("借出时长")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("\(duration)天")
                                .font(.body)
                        }
                    }
                }
            } else if let daysRemaining = loanRecord.daysRemaining {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(daysRemaining >= 0 ? "剩余天数" : "已逾期")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(daysRemaining >= 0 ? "\(daysRemaining)天" : "\(abs(daysRemaining))天")
                            .font(.body)
                            .foregroundColor(daysRemaining >= 0 ? .primary : .red)
                    }

                    Spacer()

                    if let duration = loanRecord.loanDuration {
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("已借出时长")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("\(duration)天")
                                .font(.body)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 备注信息
    private var notesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("备注")
                .font(.headline)
                .foregroundColor(.primary)

            Text(loanRecord.notes ?? "")
                .font(.body)
                .foregroundColor(.primary)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 操作按钮
    private var actionButtons: some View {
        VStack(spacing: 12) {
            if loanRecord.loanStatus == .active || loanRecord.loanStatus == .overdue {
                Button(action: {
                    showingReturnAlert = true
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                        Text("标记为已归还")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(themeManager.currentTheme.primaryColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }

                Button(action: {
                    showingLostAlert = true
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                        Text("标记为已丢失")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }

            Button(action: {
                showingEditSheet = true
            }) {
                HStack {
                    Image(systemName: "pencil")
                    Text("编辑记录")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }

            Button(action: {
                showingDeleteAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("删除记录")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
    }

    // 删除借阅记录
    private func deleteLoanRecord() {
        if loanRecordViewModel.deleteLoanRecord(loanRecord) {
            presentationMode.wrappedValue.dismiss()
        }
    }

    // 标记为已归还
    private func markAsReturned() {
        if loanRecordViewModel.markAsReturned(loanRecord) {
            // 刷新视图
        }
    }

    // 标记为已丢失
    private func markAsLost() {
        if loanRecordViewModel.markAsLost(loanRecord) {
            // 刷新视图
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext

    // 创建示例产品
    let product = Product(context: context)
    product.id = UUID()
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.price = 14999

    // 创建示例借阅记录
    let loanRecord = LoanRecord(context: context)
    loanRecord.id = UUID()
    loanRecord.borrowerName = "张三"
    loanRecord.contactInfo = "13800138000"
    loanRecord.createdAt = Date().addingTimeInterval(-60*60*24*10) // 10天前
    loanRecord.dueDate = Date().addingTimeInterval(60*60*24*5) // 5天后
    loanRecord.notes = "工作需要借用，约定两周后归还"
    loanRecord.product = product

    return NavigationView {
        LoanRecordDetailView(loanRecord: loanRecord)
            .environmentObject(LoanRecordViewModel(
                repository: LoanRecordRepository(context: context),
                productRepository: ProductRepository(context: context)
            ))
            .environmentObject(ThemeManager())
    }
}
