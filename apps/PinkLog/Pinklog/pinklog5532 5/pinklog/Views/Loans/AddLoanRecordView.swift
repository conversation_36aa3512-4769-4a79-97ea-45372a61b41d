import SwiftUI

struct AddLoanRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var loanRecordViewModel: LoanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    let product: Product
    
    @State private var borrowerName: String = ""
    @State private var contactInfo: String = ""
    @State private var dueDate: Date = Date().addingTimeInterval(60*60*24*7) // 默认一周后
    @State private var notes: String = ""
    
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("借出信息")) {
                    TextField("借出对象姓名", text: $borrowerName)
                    TextField("联系方式", text: $contactInfo)
                        .keyboardType(.phonePad)
                }
                
                Section(header: Text("归还日期")) {
                    DatePicker("预计归还日期", selection: $dueDate, displayedComponents: .date)
                }
                
                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }
                
                Section {
                    Button(action: saveLoanRecord) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                    }
                    .disabled(!isFormValid)
                }
            }
            .navigationTitle("添加借出记录")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("提示"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }
    
    private var isFormValid: Bool {
        return !borrowerName.isEmpty
    }
    
    private func saveLoanRecord() {
        if let _ = loanRecordViewModel.addLoanRecord(
            borrowerName: borrowerName,
            contactInfo: contactInfo.isEmpty ? nil : contactInfo,
            dueDate: dueDate,
            notes: notes.isEmpty ? nil : notes,
            product: product
        ) {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "保存借出记录失败，请重试"
            showingAlert = true
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let product = Product(context: context)
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.model = "M1 Pro"
    product.price = 14999
    product.purchaseDate = Date()
    
    return AddLoanRecordView(product: product)
        .environment(\.managedObjectContext, context)
        .environmentObject(LoanRecordViewModel(
            repository: LoanRecordRepository(context: context),
            productRepository: ProductRepository(context: context)
        ))
        .environmentObject(ThemeManager())
}
