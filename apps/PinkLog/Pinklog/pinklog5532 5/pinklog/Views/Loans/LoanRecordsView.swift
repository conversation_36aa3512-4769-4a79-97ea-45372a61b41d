import SwiftUI

struct LoanRecordsView: View {
    @EnvironmentObject var loanRecordViewModel: LoanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager
    
    @State private var searchText = ""
    @State private var selectedFilter: LoanRecordViewModel.FilterOption = .all
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏
            searchBar
            
            // 筛选栏
            filterBar
            
            // 借阅记录列表
            if loanRecordViewModel.isLoading {
                ProgressView()
                    .padding()
            } else if loanRecordViewModel.loanRecords.isEmpty {
                emptyStateView
            } else {
                loanRecordsList
            }
        }
        .navigationTitle("借阅记录")
        .onAppear {
            loanRecordViewModel.setCurrentProduct(nil) // 加载所有借阅记录
            loanRecordViewModel.loadLoanRecords()
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("搜索借阅记录", text: $searchText)
                .onChange(of: searchText) { newValue in
                    loanRecordViewModel.searchText = newValue
                    loanRecordViewModel.loadLoanRecords()
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    loanRecordViewModel.searchText = ""
                    loanRecordViewModel.loadLoanRecords()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(8)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 筛选栏
    private var filterBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(LoanRecordViewModel.FilterOption.allCases) { option in
                    Button(action: {
                        selectedFilter = option
                        loanRecordViewModel.filterOption = option
                        loanRecordViewModel.loadLoanRecords()
                    }) {
                        Text(option.rawValue)
                            .font(.subheadline)
                            .padding(.vertical, 6)
                            .padding(.horizontal, 12)
                            .background(selectedFilter == option ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground))
                            .foregroundColor(selectedFilter == option ? .white : .primary)
                            .cornerRadius(8)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
        .background(Color(UIColor.systemBackground))
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "tray")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("暂无借阅记录")
                .font(.headline)
                .foregroundColor(.gray)
            
            Text("您可以在物品详情页添加借阅记录")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
    
    // 借阅记录列表
    private var loanRecordsList: some View {
        List {
            ForEach(loanRecordViewModel.loanRecords) { loanRecord in
                NavigationLink(destination: LoanRecordDetailView(loanRecord: loanRecord)) {
                    loanRecordRow(loanRecord)
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
    }
    
    // 借阅记录行
    private func loanRecordRow(_ loanRecord: LoanRecord) -> some View {
        HStack(spacing: 12) {
            // 产品图片
            if let product = loanRecord.product, let imageData = product.images, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 50, height: 50)
                    .cornerRadius(8)
            } else {
                Image(systemName: "cube.box")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .padding(10)
                    .frame(width: 50, height: 50)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
            }
            
            // 借阅信息
            VStack(alignment: .leading, spacing: 4) {
                Text(loanRecord.product?.name ?? "未知产品")
                    .font(.headline)
                
                Text("借给: \(loanRecord.borrowerName)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack {
                    Text(formatDate(loanRecord.createdAt))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("→")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(formatDate(loanRecord.returnDate ?? loanRecord.dueDate))
                        .font(.caption)
                        .foregroundColor(loanRecord.isOverdue ? .red : .secondary)
                }
            }
            
            Spacer()
            
            // 状态标记
            statusBadge(for: loanRecord)
        }
    }
    
    // 状态标记
    private func statusBadge(for loanRecord: LoanRecord) -> some View {
        Text(loanRecord.loanStatus.rawValue)
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(loanRecord.loanStatus.color)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(loanRecord.loanStatus.color.opacity(0.2))
            .cornerRadius(8)
    }
    
    // 格式化日期
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未知" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/MM/dd"
        return formatter.string(from: date)
    }
}

#Preview {
    NavigationView {
        LoanRecordsView()
            .environmentObject(LoanRecordViewModel(
                repository: LoanRecordRepository(context: PersistenceController.preview.container.viewContext),
                productRepository: ProductRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}
