import SwiftUI

struct EditLoanRecordView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var loanRecordViewModel: LoanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let loanRecord: LoanRecord

    @State private var borrowerName: String = ""
    @State private var contactInfo: String = ""
    @State private var dueDate: Date = Date()
    @State private var notes: String = ""

    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("借出信息")) {
                    TextField("借出对象姓名", text: $borrowerName)
                    TextField("联系方式", text: $contactInfo)
                        .keyboardType(.phonePad)
                }

                Section(header: Text("归还日期")) {
                    DatePicker("预计归还日期", selection: $dueDate, displayedComponents: .date)
                }

                Section(header: Text("备注")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }

                Section {
                    Button(action: updateLoanRecord) {
                        Text("保存")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                    }
                    .disabled(!isFormValid)
                }
            }
            .navigationTitle("编辑借出记录")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("提示"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
            .onAppear {
                loadLoanRecordData()
            }
        }
    }

    private var isFormValid: Bool {
        return !borrowerName.isEmpty
    }

    private func loadLoanRecordData() {
        borrowerName = loanRecord.borrowerName ?? ""
        contactInfo = loanRecord.contactInfo ?? ""
        dueDate = loanRecord.dueDate ?? Date()
        notes = loanRecord.notes ?? ""
    }

    private func updateLoanRecord() {
        if loanRecordViewModel.updateLoanRecord(
            loanRecord,
            borrowerName: borrowerName,
            contactInfo: contactInfo.isEmpty ? nil : contactInfo,
            dueDate: dueDate,
            notes: notes.isEmpty ? nil : notes
        ) {
            presentationMode.wrappedValue.dismiss()
        } else {
            alertMessage = "更新借出记录失败，请重试"
            showingAlert = true
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext

    // 创建示例产品
    let product = Product(context: context)
    product.id = UUID()
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.price = 14999

    // 创建示例借阅记录
    let loanRecord = LoanRecord(context: context)
    loanRecord.id = UUID()
    loanRecord.borrowerName = "张三"
    loanRecord.contactInfo = "13800138000"
    loanRecord.createdAt = Date().addingTimeInterval(-60*60*24*10) // 10天前
    loanRecord.dueDate = Date().addingTimeInterval(60*60*24*5) // 5天后
    loanRecord.notes = "工作需要借用，约定两周后归还"
    loanRecord.product = product

    return EditLoanRecordView(loanRecord: loanRecord)
        .environmentObject(LoanRecordViewModel(
            repository: LoanRecordRepository(context: context),
            productRepository: ProductRepository(context: context)
        ))
        .environmentObject(ThemeManager())
}
