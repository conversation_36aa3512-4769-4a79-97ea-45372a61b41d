import SwiftUI

struct ProductDetailLoanRecordsView: View {
    @EnvironmentObject var loanRecordViewModel: LoanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager

    let product: Product

    @State private var showingAddLoanSheet = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("借阅记录")
                    .font(.headline)

                Spacer()

                Button(action: {
                    showingAddLoanSheet = true
                }) {
                    Label("添加", systemImage: "plus")
                        .font(.caption)
                }
            }

            if loanRecordViewModel.loanRecords.isEmpty {
                Text("暂无借阅记录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(12)
            } else {
                ForEach(loanRecordViewModel.loanRecords) { loanRecord in
                    NavigationLink(destination: LoanRecordDetailView(loanRecord: loanRecord)) {
                        loanRecordRow(loanRecord)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .sheet(isPresented: $showingAddLoanSheet) {
            AddLoanRecordView(product: product)
        }
        .onAppear {
            loanRecordViewModel.setCurrentProduct(product)
        }
    }

    // 借阅记录行
    private func loanRecordRow(_ loanRecord: LoanRecord) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(loanRecord.borrowerName ?? "")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    statusBadge(for: loanRecord)
                }

                HStack {
                    Text("借出日期: \(formatDate(loanRecord.createdAt))")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    if loanRecord.returnDate != nil {
                        Text("归还日期: \(formatDate(loanRecord.returnDate))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text("预计归还: \(formatDate(loanRecord.dueDate))")
                            .font(.caption)
                            .foregroundColor(loanRecord.isOverdue ? .red : .secondary)
                    }
                }

                if let notes = loanRecord.notes, !notes.isEmpty {
                    Text(notes)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
    }

    // 状态标记
    private func statusBadge(for loanRecord: LoanRecord) -> some View {
        Text(loanRecord.loanStatus.rawValue)
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(loanRecord.loanStatus.color)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(loanRecord.loanStatus.color.opacity(0.2))
            .cornerRadius(8)
    }

    // 格式化日期
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未知" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/MM/dd"
        return formatter.string(from: date)
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext

    // 创建示例产品
    let product = Product(context: context)
    product.id = UUID()
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.price = 14999

    // 创建示例借阅记录
    let loanRecord = LoanRecord(context: context)
    loanRecord.id = UUID()
    loanRecord.borrowerName = "张三"
    loanRecord.contactInfo = "13800138000"
    loanRecord.createdAt = Date().addingTimeInterval(-60*60*24*10) // 10天前
    loanRecord.dueDate = Date().addingTimeInterval(60*60*24*5) // 5天后
    loanRecord.notes = "工作需要借用，约定两周后归还"
    loanRecord.product = product

    return ProductDetailLoanRecordsView(product: product)
        .environmentObject(LoanRecordViewModel(
            repository: LoanRecordRepository(context: context),
            productRepository: ProductRepository(context: context)
        ))
        .environmentObject(ThemeManager())
        .padding()
        .previewLayout(.sizeThatFits)
}
