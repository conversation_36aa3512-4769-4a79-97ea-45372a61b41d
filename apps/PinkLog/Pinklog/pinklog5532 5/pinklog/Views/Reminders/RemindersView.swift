import SwiftUI

struct RemindersView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    @StateObject private var reminderManager = ProactiveReminderManager.shared
    
    // 筛选和搜索状态
    @State private var selectedStatus: ProactiveReminderStatus? = nil
    @State private var selectedType: ProactiveMessageType? = nil
    @State private var searchText: String = ""
    @State private var selectedReminder: ProactiveReminder? = nil
    @State private var showingReminderChat: Bool = false
    @State private var showingSettings: Bool = false
    
    // 布局状态
    @State private var isLoading: Bool = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 筛选栏
                filterBar
                
                // 提醒列表
                if reminderManager.isLoading || isLoading {
                    loadingView
                } else if filteredReminders.isEmpty {
                    emptyStateView
                } else {
                    remindersList
                }
            }
            .navigationTitle("提醒中心")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(action: {
                            showingSettings = true
                        }) {
                            Image(systemName: "gear")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                        }
                        
                        Menu {
                            Button("清除已读提醒") {
                                clearReadReminders()
                            }
                            
                            Button("清除所有提醒", role: .destructive) {
                                clearAllReminders()
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle")
                                .foregroundColor(themeManager.currentTheme.primaryColor)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingReminderChat, onDismiss: {
                selectedReminder = nil
            }) {
                if let reminder = selectedReminder {
                    ProactiveReminderChatView(reminder: reminder)
                        .environmentObject(themeManager)
                        .environmentObject(productViewModel)
                        .environmentObject(usageViewModel)
                }
            }
            .sheet(isPresented: $showingSettings) {
                ProactiveServiceSettingsView()
                    .environmentObject(themeManager)
            }
        }
        .onAppear {
            refreshReminders()
        }
        .refreshable {
            await refreshRemindersAsync()
        }
    }
    
    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索提醒...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button("清除") {
                    searchText = ""
                }
                .foregroundColor(themeManager.currentTheme.primaryColor)
                .font(.caption)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal)
        .padding(.top)
    }
    
    // MARK: - 筛选栏
    private var filterBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 状态筛选
                FilterChip(
                    title: "全部",
                    isSelected: selectedStatus == nil,
                    count: reminderManager.reminders.filter { !$0.isExpired }.count,
                    action: {
                        selectedStatus = nil
                    }
                )
                
                ForEach(ProactiveReminderStatus.allCases, id: \.self) { status in
                    let count = reminderManager.getReminders(status: status).count
                    if count > 0 {
                        FilterChip(
                            title: status.rawValue,
                            isSelected: selectedStatus == status,
                            count: count,
                            color: status.displayColor,
                            action: {
                                selectedStatus = status
                            }
                        )
                    }
                }
                
                Divider()
                    .frame(height: 20)
                
                // 类型筛选
                ForEach(ProactiveMessageType.allCases, id: \.self) { type in
                    let count = reminderManager.getReminders(type: type).count
                    if count > 0 {
                        FilterChip(
                            title: type.rawValue,
                            isSelected: selectedType == type,
                            count: count,
                            color: type.color,
                            action: {
                                selectedType = selectedType == type ? nil : type
                            }
                        )
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - 提醒列表
    private var remindersList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredReminders) { reminder in
                    ReminderBannerCard(
                        reminder: reminder,
                        onTap: {
                            handleReminderTap(reminder)
                        },
                        onMarkAsRead: {
                            reminderManager.markAsRead(reminder.id)
                        },
                        onDismiss: {
                            reminderManager.dismissReminder(reminder.id)
                        },
                        onDelete: {
                            reminderManager.deleteReminder(reminder.id)
                        }
                    )
                    .environmentObject(themeManager)
                }
            }
            .padding(.horizontal)
            .padding(.top, 8)
        }
    }
    
    // MARK: - 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("正在加载提醒...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: searchText.isEmpty ? "bell.slash" : "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text(searchText.isEmpty ? "暂无提醒" : "无匹配结果")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text(searchText.isEmpty ? 
                 "当前没有主动助手提醒\nAI助手会智能分析您的数据并主动推送有价值的洞察" :
                 "尝试调整搜索条件或筛选器")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if searchText.isEmpty {
                Button("手动触发分析") {
                    triggerManualAnalysis()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(themeManager.currentTheme.primaryColor)
                .foregroundColor(.white)
                .clipShape(Capsule())
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 计算属性
    private var filteredReminders: [ProactiveReminder] {
        var reminders = reminderManager.reminders.filter { !$0.isExpired }
        
        // 状态筛选
        if let status = selectedStatus {
            reminders = reminders.filter { $0.status == status }
        }
        
        // 类型筛选
        if let type = selectedType {
            reminders = reminders.filter { $0.type == type }
        }
        
        // 搜索筛选
        if !searchText.isEmpty {
            let lowercasedSearch = searchText.lowercased()
            reminders = reminders.filter { reminder in
                reminder.title.lowercased().contains(lowercasedSearch) ||
                reminder.content.lowercased().contains(lowercasedSearch)
            }
        }
        
        return reminders
    }
    
    // MARK: - 方法
    private func handleReminderTap(_ reminder: ProactiveReminder) {
        // 标记为已读
        if reminder.status == .unread {
            reminderManager.markAsRead(reminder.id)
        }
        
        // 设置选中的提醒并显示聊天界面
        selectedReminder = reminder
        showingReminderChat = true
    }
    
    private func refreshReminders() {
        isLoading = true
        
        // 模拟刷新延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isLoading = false
        }
    }
    
    private func refreshRemindersAsync() async {
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
    }
    
    private func clearReadReminders() {
        let readReminders = reminderManager.reminders.filter { $0.status == .read }
        for reminder in readReminders {
            reminderManager.deleteReminder(reminder.id)
        }
    }
    
    private func clearAllReminders() {
        reminderManager.clearAllReminders()
    }
    
    private func triggerManualAnalysis() {
        // 这里可以触发手动分析
        // 可以调用 ProactiveService 的分析方法
        isLoading = true
        
        Task {
            // 模拟触发分析
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
            
            await MainActor.run {
                isLoading = false
            }
        }
    }
}

// FilterChip组件已移动到共享文件 Components/FilterChip.swift

// MARK: - 提醒条幅卡片
struct ReminderBannerCard: View {
    let reminder: ProactiveReminder
    let onTap: () -> Void
    let onMarkAsRead: () -> Void
    let onDismiss: () -> Void
    let onDelete: () -> Void
    
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingActionSheet = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // 头部信息
                HStack {
                    // 类型图标
                    Image(systemName: reminder.type.icon)
                        .font(.title3)
                        .foregroundColor(reminder.type.color)
                        .frame(width: 30)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        HStack {
                            Text(reminder.title)
                                .font(.headline)
                                .foregroundColor(.primary)
                                .lineLimit(1)
                            
                            Spacer()
                            
                            // 状态指示器
                            statusIndicator
                        }
                        
                        Text(reminder.timeAgo)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 更多操作按钮
                    Button(action: {
                        showingActionSheet = true
                    }) {
                        Image(systemName: "ellipsis")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // 内容
                Text(reminder.content)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
                
                // 优先级指示器
                if reminder.isHighPriority {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        
                        Text("高优先级")
                            .font(.caption)
                            .foregroundColor(.orange)
                        
                        Spacer()
                        
                        // 置信度
                        Text("置信度: \(Int(reminder.confidence * 100))%")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(reminder.status == .unread ? 
                          Color(.systemBackground) : 
                          Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                reminder.status == .unread ? 
                                reminder.type.color.opacity(0.3) : 
                                Color(.systemGray4),
                                lineWidth: reminder.status == .unread ? 2 : 1
                            )
                    )
            )
            .shadow(
                color: reminder.status == .unread ? 
                reminder.type.color.opacity(0.2) : 
                Color.black.opacity(0.05),
                radius: reminder.status == .unread ? 8 : 2,
                x: 0,
                y: reminder.status == .unread ? 4 : 1
            )
        }
        .buttonStyle(PlainButtonStyle())
        .confirmationDialog("提醒操作", isPresented: $showingActionSheet) {
            if reminder.status == .unread {
                Button("标记为已读") {
                    onMarkAsRead()
                }
            }
            
            Button("忽略此提醒") {
                onDismiss()
            }
            
            Button("删除提醒", role: .destructive) {
                onDelete()
            }
            
            Button("取消", role: .cancel) {}
        }
    }
    
    private var statusIndicator: some View {
        Circle()
            .fill(reminder.status.displayColor)
            .frame(width: 8, height: 8)
    }
}

#Preview {
    NavigationView {
        RemindersView()
            .environmentObject(ThemeManager())
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
                expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
            ))
    }
}
