import SwiftUI
import MarkdownUI

// MARK: - 主动提醒专用AI聊天界面
struct ProactiveReminderChatView: View {
    let reminder: ProactiveReminder
    
    @ObservedObject private var analyticsAssistant: AnalyticsAssistant
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var productViewModel: ProductViewModel
    @EnvironmentObject private var usageViewModel: UsageViewModel
    @Environment(\.dismiss) private var dismiss
    
    // 聊天状态
    @State private var messageText: String = ""
    @State private var isLoading: Bool = false
    @FocusState private var isTextFieldFocused: Bool
    
    // 界面状态
    @State private var showingReminderDetails = true
    @State private var hasStartedChat = false
    
    // MARK: - 初始化
    init(reminder: ProactiveReminder) {
        self.reminder = reminder
        // 为每个提醒创建独立的 AnalyticsAssistant 实例
        self.analyticsAssistant = AnalyticsAssistant()
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 提醒详情卡片（可折叠）
                if showingReminderDetails {
                    reminderDetailsCard
                        .transition(.move(edge: .top))
                }
                
                // 聊天区域
                chatArea
                
                // 输入区域
                messageInputArea
            }
            .navigationTitle("AI助手咨询")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingReminderDetails.toggle()
                        }
                    }) {
                        Image(systemName: showingReminderDetails ? "chevron.up" : "chevron.down")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                }
            }
        }
        .onAppear {
            setupInitialContext()
        }
    }
    
    // MARK: - 提醒详情卡片
    private var reminderDetailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题栏
            HStack {
                Image(systemName: reminder.type.icon)
                    .font(.title2)
                    .foregroundColor(reminder.type.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(reminder.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Text(reminder.type.rawValue)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(reminder.type.color.opacity(0.2))
                            .clipShape(Capsule())
                            .foregroundColor(reminder.type.color)
                        
                        Text(reminder.priority.description)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(priorityColor.opacity(0.2))
                            .clipShape(Capsule())
                            .foregroundColor(priorityColor)
                        
                        Spacer()
                        
                        Text(reminder.timeAgo)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            
            Divider()
            
            // 内容详情
            VStack(alignment: .leading, spacing: 12) {
                Text("详细说明")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(reminder.content)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
                
                // 置信度和时间信息
                HStack {
                    Label("置信度: \(Int(reminder.confidence * 100))%", systemImage: "brain.head.profile")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Label(formatDate(reminder.timestamp), systemImage: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 建议操作按钮
            if !reminder.actionButtons.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("建议操作")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 8) {
                        ForEach(reminder.actionButtons.indices, id: \.self) { index in
                            let actionText = reminder.actionButtons[index]
                            Button(action: {
                                handleSuggestedAction(actionText)
                            }) {
                                Text(cleanActionButtonText(actionText))
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(themeManager.currentTheme.primaryColor.opacity(0.1))
                                    .foregroundColor(themeManager.currentTheme.primaryColor)
                                    .clipShape(RoundedRectangle(cornerRadius: 6))
                                    .lineLimit(1)
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(reminder.type.color.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal)
        .padding(.top)
    }
    
    // MARK: - 聊天区域
    private var chatArea: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    // 对话历史（包含初始欢迎消息）
                    ForEach(analyticsAssistant.conversationHistory) { message in
                        ModernMessageBubble(message: message, selectedProduct: .constant(nil))
                            .environmentObject(productViewModel)
                            .environmentObject(usageViewModel)
                            .id(message.id)
                    }
                    
                    // 流式内容
                    if analyticsAssistant.isStreaming {
                        StreamingMessageView(content: analyticsAssistant.streamingContent)
                            .id("streaming")
                    }
                    
                    // 加载指示器
                    if isLoading && !analyticsAssistant.isStreaming {
                        LoadingIndicatorView()
                            .id("loading")
                    }
                    
                    // 快速问题建议（仅在没有开始聊天时显示）
                    if !hasStartedChat {
                        quickQuestionSuggestions
                            .padding(.top)
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }
            .onChange(of: analyticsAssistant.conversationHistory) {
                scrollToBottom(proxy: proxy)
            }
            .onChange(of: analyticsAssistant.streamingContent) {
                if analyticsAssistant.isStreaming {
                    scrollToBottom(proxy: proxy)
                }
            }
        }
    }
    

    
    // MARK: - 快速问题建议
    private var quickQuestionSuggestions: some View {
        VStack(spacing: 12) {
            Text("您可能想了解")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(generateQuickQuestions(), id: \.self) { question in
                    Button(action: {
                        sendSuggestedQuestion(question)
                    }) {
                        Text(question)
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .foregroundColor(.primary)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .lineLimit(2)
                            .multilineTextAlignment(.center)
                    }
                }
            }
        }
    }
    
    // MARK: - 消息输入区域
    private var messageInputArea: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                TextField("继续咨询这个提醒相关的问题...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($isTextFieldFocused)
                    .onSubmit {
                        sendMessage()
                    }
                
                Button(action: sendMessage) {
                    Image(systemName: "paperplane.fill")
                        .font(.system(size: 20))
                        .foregroundColor(messageText.isEmpty ? .secondary : themeManager.currentTheme.primaryColor)
                }
                .disabled(messageText.isEmpty || isLoading)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 计算属性
    private var priorityColor: Color {
        switch reminder.priority {
        case .low:
            return .green
        case .medium:
            return .orange
        case .high:
            return .red
        case .urgent:
            return .purple
        }
    }
    
    // MARK: - 方法
    private func setupInitialContext() {
        // 清空任何预加载的聊天记录，确保这是一个全新的会话
        analyticsAssistant.conversationHistory.removeAll()
        
        // 创建独立的聊天会话，确保每个提醒都有独立的聊天记录
        let conversationTitle = "提醒咨询：\(reminder.title)"
        let _ = analyticsAssistant.createNewConversation(title: conversationTitle)
        
        // 设置AI助手的上下文，包含提醒信息
        let context = buildReminderContext()
        analyticsAssistant.setAnalysisContext(context)
        
        // 添加初始的欢迎消息，介绍这个提醒的背景
        let welcomeMessage = ConversationMessage(
            role: .assistant,
            content: buildWelcomeMessage(),
            analysisContext: context
        )
        analyticsAssistant.conversationHistory.append(welcomeMessage)
        
        // 标记提醒为已读（如果尚未读取）
        if reminder.status == .unread {
            ProactiveReminderManager.shared.markAsRead(reminder.id)
        }
    }
    
    private func buildReminderContext() -> AnalysisContext {
        return AnalysisContext(
            productId: reminder.relatedProductIds.first,
            analysisType: .general,
            timeRange: DateRange(
                startDate: Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date(),
                endDate: Date()
            ),
            filters: [:],
            metadata: [
                "reminderType": reminder.type.rawValue,
                "reminderTitle": reminder.title,
                "reminderContent": reminder.content,
                "reminderPriority": reminder.priority.description,
                "reminderConfidence": String(reminder.confidence)
            ]
        )
    }
    
    private func generateQuickQuestions() -> [String] {
        switch reminder.type {
        case .reminder:
            return [
                "如何处理这个提醒？",
                "这个提醒的重要性如何？",
                "有什么行动建议？",
                "如何避免类似问题？"
            ]
        case .suggestion:
            return [
                "这个建议的可行性如何？",
                "实施这个建议的步骤？",
                "有什么替代方案？",
                "预期效果是什么？"
            ]
        case .warning:
            return [
                "这个警告有多严重？",
                "需要立即处理吗？",
                "如何解决这个问题？",
                "会有什么后果？"
            ]
        case .insight:
            return [
                "这个洞察意味着什么？",
                "如何利用这个发现？",
                "有什么行动指导？",
                "类似模式还有哪些？"
            ]
        case .opportunity:
            return [
                "如何抓住这个机会？",
                "风险评估如何？",
                "最佳时机是什么？",
                "需要什么准备？"
            ]
        case .maintenance:
            return [
                "维护的紧迫性如何？",
                "维护成本大概多少？",
                "不维护的后果？",
                "最佳维护时间？"
            ]
        }
    }
    
    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let question = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        messageText = ""
        isTextFieldFocused = false
        hasStartedChat = true
        
        withAnimation(.easeInOut(duration: 0.3)) {
            isLoading = true
        }
        
        // 添加提醒上下文到问题中
        let contextualQuestion = buildContextualQuestion(question)
        
        // 使用流式处理
        analyticsAssistant.processUserQuestionStream(contextualQuestion)
        
        // 监听处理完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isLoading = false
        }
    }
    
    private func sendSuggestedQuestion(_ question: String) {
        messageText = question
        sendMessage()
    }
    
    private func handleSuggestedAction(_ actionText: String) {
        let cleanedText = cleanActionButtonText(actionText)
        messageText = "关于建议操作「\(cleanedText)」，能详细解释一下吗？"
        sendMessage()
    }
    
    private func buildContextualQuestion(_ question: String) -> String {
        return """
        用户正在咨询关于以下主动提醒的问题：
        
        提醒类型：\(reminder.type.rawValue)
        提醒标题：\(reminder.title)
        提醒内容：\(reminder.content)
        优先级：\(reminder.priority.description)
        置信度：\(Int(reminder.confidence * 100))%
        
        用户问题：\(question)
        
        请基于这个提醒的上下文来回答用户的问题，提供具体可行的建议。
        """
    }
    
    private func buildWelcomeMessage() -> String {
        let confidencePercent = Int(reminder.confidence * 100)
        
        let typeGreeting = switch reminder.type {
        case .reminder:
            "我注意到有一个重要提醒需要您关注"
        case .suggestion:
            "我为您准备了一个优化建议"
        case .warning:
            "我发现了一个需要您注意的警告"
        case .insight:
            "我从您的数据中发现了一个有趣的洞察"
        case .opportunity:
            "我发现了一个潜在的机会"
        case .maintenance:
            "我检测到有物品可能需要维护"
        }
        
        return """
        👋 您好！\(typeGreeting)：
        
        **\(reminder.title)**
        
        \(reminder.content)
        
        这个\(reminder.type.rawValue)的置信度为 \(confidencePercent)%，优先级为\(reminder.priority.description)。
        
        我可以帮您详细解释这个提醒，并提供具体的处理建议。请随时向我提问！
        """
    }
    
    private func cleanActionButtonText(_ actionText: String) -> String {
        // 清理动作按钮文本，移除标记符号
        return actionText
            .replacingOccurrences(of: "[", with: "")
            .replacingOccurrences(of: "]", with: "")
            .components(separatedBy: ":").first ?? actionText
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    private func scrollToBottom(proxy: ScrollViewProxy) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.3)) {
                if analyticsAssistant.isStreaming {
                    proxy.scrollTo("streaming", anchor: .bottom)
                } else if isLoading {
                    proxy.scrollTo("loading", anchor: .bottom)
                } else if let lastMessage = analyticsAssistant.conversationHistory.last {
                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                }
            }
        }
    }
}

// MARK: - 加载指示器
struct LoadingIndicatorView: View {
    @State private var isAnimating = false
    
    var body: some View {
        HStack(spacing: 8) {
            Image("pinkbot-logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color(.systemGray))
                            .frame(width: 8, height: 8)
                            .scaleEffect(isAnimating ? 1.0 : 0.5)
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                                value: isAnimating
                            )
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                )
                
                Text("正在思考...")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - 预览
#Preview {
    let sampleReminder = ProactiveReminder(
        type: .suggestion,
        priority: .high,
        title: "发现优化机会",
        content: "您的咖啡机已购买3个月，但使用频率较低。建议评估是否需要调整使用习惯或考虑其他替代方案。",
        actionButtons: ["[查看分析:type=使用分析]", "[设置提醒:type=使用提醒]"],
        relatedProductIds: [],
        confidence: 0.85,
        timestamp: Date(),
        expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
    )
    
    ProactiveReminderChatView(reminder: sampleReminder)
        .environmentObject(ThemeManager())
        .environmentObject(ProductViewModel(
            repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
            categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(UsageViewModel(
            usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
            expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
        ))
} 