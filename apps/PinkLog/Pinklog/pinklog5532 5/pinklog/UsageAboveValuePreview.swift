//
//  UsageAboveValuePreview.swift
//  使用次数在值度指数正上方的设计预览
//
//  Created by Assistant on 2025-01-26.
//

import SwiftUI

/**
 * 🎯 使用次数在值度指数正上方的设计预览
 * 
 * 设计理念：
 * 将使用次数从突兀的角标位置移动到值度指数的正上方，
 * 形成自然的垂直信息层次，提升视觉和谐度。
 *
 * 📐 布局演进历程：
 * 
 * 第一版：独立行显示
 * ┌─────────────────────────────────────┐
 * │ [图片] 产品名称              价格 ¥99 │
 * │        品牌名称        值度 85 ●     │
 * │        使用 12 次                   │
 * └─────────────────────────────────────┘
 * 
 * 第二版：价格优先布局
 * ┌─────────────────────────────────────┐
 * │ [图片] 产品名称                     │
 * │        价格 ¥99          值度 85 ●  │
 * │        使用 12 次                   │
 * └─────────────────────────────────────┘
 * 
 * 第三版：右上角角标（图片区域）
 * ┌─────────────────────────────────────┐
 * │ [图片 12] 产品名称                  │
 * │           价格 ¥99       值度 85 ●  │
 * └─────────────────────────────────────┘
 * 
 * 第四版：右上角角标（整个卡片）
 * ┌─────────────────────────────────── 12┐
 * │ [图片] 产品名称                     │
 * │        价格 ¥99          值度 85 ●  │
 * └─────────────────────────────────────┘
 * 
 * 第五版：值度指数正上方（当前版本）✨
 * ┌─────────────────────────────────────┐
 * │ [图片] 产品名称                     │
 * │        价格 ¥99         使用 12 次  │
 * │                        值度 85 ●   │
 * └─────────────────────────────────────┘
 *
 * 🎨 设计优势：
 * 
 * 1. 自然的信息层次
 *    - 使用次数作为值度指数的上下文信息
 *    - 形成"使用次数 → 值度指数"的逻辑关系
 *    - 垂直对齐，视觉连贯性强
 * 
 * 2. 消除突兀感
 *    - 不再是独立的角标元素
 *    - 与值度指数形成信息组合
 *    - 整体布局更加和谐
 * 
 * 3. 语义关联性
 *    - 使用次数直接影响值度计算
 *    - 位置关系体现了数据关联
 *    - 用户更容易理解两者关系
 * 
 * 4. 视觉平衡
 *    - 左侧：价格信息（购买决策）
 *    - 右侧：使用数据（使用价值）
 *    - 形成完整的价值评估体系
 * 
 * 🔧 技术实现：
 * 
 * ```swift
 * VStack(alignment: .trailing, spacing: 2) {
 *     // 使用次数 - 在值度指数正上方
 *     Text("使用\(product.totalUsageCount)次")
 *         .font(.system(size: 11, weight: .medium))
 *         .foregroundColor(.secondary)
 *     
 *     // 值度指数和置信度
 *     HStack(spacing: 4) {
 *         // 值度指数内容...
 *     }
 * }
 * ```
 * 
 * 📱 用户体验提升：
 * 
 * • 信息关联性：使用次数和值度指数的位置关系体现了数据逻辑
 * • 视觉和谐：消除了角标的突兀感，整体更加协调
 * • 阅读流畅：垂直信息流更符合用户的阅读习惯
 * • 空间利用：充分利用右侧空间，布局更加紧凑
 * • 语义清晰：位置关系暗示了使用次数对值度的影响
 */

struct UsageAboveValuePreview: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("使用次数在值度指数正上方的设计")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("自然的信息层次，消除突兀感")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
    }
}

#Preview {
    UsageAboveValuePreview()
}