import Foundation

// MARK: - 应用通知名称定义
extension Notification.Name {
    /// 数据恢复完成通知
    /// 当跨设备备份恢复完成时发送，用于触发UI刷新
    static let dataRestoreCompleted = Notification.Name("dataRestoreCompleted")
    
    /// 产品数据更新通知
    /// 当产品数据发生变化时发送
    static let productDataUpdated = Notification.Name("productDataUpdated")
    
    /// 备份完成通知
    /// 当备份操作完成时发送
    static let backupCompleted = Notification.Name("backupCompleted")

    /// 购买完成通知
    /// 当内购完成时发送
    static let purchaseCompleted = Notification.Name("purchaseCompleted")

    /// PurchaseManager初始化完成通知
    /// 当PurchaseManager完成初始化时发送
    static let purchaseManagerInitialized = Notification.Name("purchaseManagerInitialized")

    /// 主题更改通知
    /// 当应用主题发生变化时发送
    static let themeChanged = Notification.Name("themeChanged")
}
