//
//  Persistence.swift
//  pinklog
//
//  Created by thr33 on 2025/5/10.
//

import CoreData
import SwiftUI

struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例数据
        createSampleData(in: viewContext)

        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "pinklog")
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                // Replace this implementation with code to handle the error appropriately.
                // fatalError() causes the application to generate a crash log and terminate. You should not use this function in a shipping application, although it may be useful during development.

                /*
                 Typical reasons for an error here include:
                 * The parent directory does not exist, cannot be created, or disallows writing.
                 * The persistent store is not accessible, due to permissions or data protection when the device is locked.
                 * The device is out of space.
                 * The store could not be migrated to the current model version.
                 Check the error message to determine what the actual problem was.
                 */
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        container.viewContext.automaticallyMergesChangesFromParent = true

        // 首次启动时初始化基础数据
        initializeBasicData()
    }

    // 初始化基础数据
    private func initializeBasicData() {
        let context = container.viewContext

        // 检查是否已经初始化过
        let fetchRequest: NSFetchRequest<ExpenseType> = ExpenseType.fetchRequest()

        do {
            let count = try context.count(for: fetchRequest)
            if count == 0 {
                // 创建基础费用类型
                createExpenseTypes(in: context)

                // 创建基础类别
                createCategories(in: context)

                // 保存上下文
                try context.save()
            }
        } catch {
            print("初始化基础数据失败: \(error)")
        }
    }

    // 创建基础费用类型
    private func createExpenseTypes(in context: NSManagedObjectContext) {
        let expenseTypes = [
            "维修": "wrench.and.screwdriver",
            "保养": "sparkles",
            "配件": "puzzlepiece",
            "耗材": "cart",
            "电池更换": "battery.100",
            "软件订阅": "app.badge",
            "干洗": "bubbles.and.sparkles",
            "其他": "ellipsis.circle"
        ]

        for (name, icon) in expenseTypes {
            let expenseType = ExpenseType(context: context)
            expenseType.id = UUID()
            expenseType.name = name
            expenseType.icon = icon
        }
    }

    // 创建基础类别
    private func createCategories(in context: NSManagedObjectContext) {
        let categories = [
            "电子产品": "laptopcomputer",
            "服装": "tshirt",
            "家居": "house",
            "厨房": "fork.knife",
            "美妆": "paintpalette",
            "健康": "heart",
            "户外": "leaf",
            "书籍": "book",
            "办公": "briefcase",
            "其他": "ellipsis.circle"
        ]

        for (name, icon) in categories {
            let category = Category(context: context)
            category.id = UUID()
            category.name = name
            category.icon = icon
        }
    }

    // 创建示例数据
    @MainActor
    static func createSampleData(in context: NSManagedObjectContext) {
        // 创建基础费用类型
        let expenseTypes = [
            "维修": "wrench.and.screwdriver",
            "保养": "sparkles",
            "配件": "puzzlepiece",
            "耗材": "cart",
            "电池更换": "battery.100",
            "软件订阅": "app.badge",
            "干洗": "bubbles.and.sparkles",
            "其他": "ellipsis.circle"
        ]

        var expenseTypeEntities = [String: ExpenseType]()

        for (name, icon) in expenseTypes {
            let expenseType = ExpenseType(context: context)
            expenseType.id = UUID()
            expenseType.name = name
            expenseType.icon = icon
            expenseTypeEntities[name] = expenseType
        }

        // 创建基础类别
        let categories = [
            "电子产品": "laptopcomputer",
            "服装": "tshirt",
            "家居": "house",
            "厨房": "fork.knife",
            "美妆": "paintpalette",
            "健康": "heart",
            "户外": "leaf",
            "书籍": "book",
            "办公": "briefcase",
            "其他": "ellipsis.circle"
        ]

        var categoryEntities = [String: Category]()

        for (name, icon) in categories {
            let category = Category(context: context)
            category.id = UUID()
            category.name = name
            category.icon = icon
            categoryEntities[name] = category
        }

        // 创建标签
        let tags = ["喜爱", "必需", "奢侈", "实用", "收藏", "礼物"]
        var tagEntities = [String: Tag]()

        for tagName in tags {
            let tag = Tag(context: context)
            tag.id = UUID()
            tag.name = tagName
            tag.color = ["red", "blue", "green", "orange", "purple", "pink"].randomElement()
            tagEntities[tagName] = tag
        }

        // 创建示例产品
        let calendar = Calendar.current
        let now = Date()

        // 示例产品1：笔记本电脑
        let laptop = Product(context: context)
        laptop.id = UUID()
        laptop.name = "MacBook Pro"
        laptop.brand = "Apple"
        laptop.model = "M1 Pro 14英寸"
        laptop.price = 14999
        laptop.purchaseDate = calendar.date(byAdding: .month, value: -6, to: now)!
        laptop.category = categoryEntities["电子产品"]
        laptop.purchaseMotivation = "提升效率"
        laptop.warrantyEndDate = calendar.date(byAdding: .year, value: 1, to: laptop.purchaseDate!)
        laptop.initialSatisfaction = 5
        laptop.addToTags(tagEntities["实用"]!)

        // 添加使用记录
        for i in 0..<30 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*3, to: now)!
            usageRecord.satisfaction = Int16(4 + (i % 2))
            usageRecord.product = laptop
            usageRecord.scenario = ["工作", "学习", "娱乐"].randomElement()
            usageRecord.duration = Double([2, 3, 4, 5].randomElement()!)
        }

        // 添加费用记录
        let laptopExpense = RelatedExpense(context: context)
        laptopExpense.id = UUID()
        laptopExpense.amount = 299
        laptopExpense.date = calendar.date(byAdding: .month, value: -2, to: now)!
        laptopExpense.product = laptop
        laptopExpense.type = expenseTypeEntities["配件"]
        laptopExpense.notes = "购买保护壳"

        // 示例产品2：运动鞋
        let shoes = Product(context: context)
        shoes.id = UUID()
        shoes.name = "跑步鞋"
        shoes.brand = "Nike"
        shoes.model = "Air Zoom"
        shoes.price = 899
        shoes.purchaseDate = calendar.date(byAdding: .month, value: -3, to: now)!
        shoes.category = categoryEntities["服装"]
        shoes.purchaseMotivation = "兴趣爱好"
        shoes.initialSatisfaction = 4
        shoes.addToTags(tagEntities["喜爱"]!)

        // 添加使用记录
        for i in 0..<15 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*5, to: now)!
            usageRecord.satisfaction = Int16(3 + (i % 3))
            usageRecord.product = shoes
            usageRecord.scenario = "跑步"
        }

        // 示例产品3：护肤品
        let skincare = Product(context: context)
        skincare.id = UUID()
        skincare.name = "面霜"
        skincare.brand = "La Mer"
        skincare.price = 1500
        skincare.purchaseDate = calendar.date(byAdding: .month, value: -2, to: now)!
        skincare.category = categoryEntities["美妆"]
        skincare.purchaseMotivation = "冲动消费"
        skincare.expiryDate = calendar.date(byAdding: .month, value: 10, to: skincare.purchaseDate!)
        skincare.initialSatisfaction = 3
        skincare.addToTags(tagEntities["奢侈"]!)

        // 添加使用记录
        for i in 0..<20 {
            let usageRecord = UsageRecord(context: context)
            usageRecord.id = UUID()
            usageRecord.date = calendar.date(byAdding: .day, value: -i*3, to: now)!
            usageRecord.satisfaction = Int16(2 + (i % 4))
            usageRecord.product = skincare
        }

        // 保存上下文
        do {
            try context.save()
        } catch {
            let nsError = error as NSError
            fatalError("创建示例数据失败: \(nsError), \(nsError.userInfo)")
        }
    }
}
