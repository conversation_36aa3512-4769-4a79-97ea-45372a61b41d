import Foundation
import CoreData
import SwiftUI
import Combine

class TimelineViewModel: ObservableObject {
    // MARK: - 属性
    @Published var events: [TimelineEvent] = []
    @Published var filteredEvents: [TimelineEvent] = []
    @Published var selectedProduct: Product?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // 过滤条件
    @Published var selectedEventTypes: Set<TimelineEventType> = Set(TimelineEventType.allCases)
    @Published var dateRange: (start: Date?, end: Date?) = (nil, nil)
    @Published var searchText: String = ""

    // 依赖的仓库
    private let usageRepository: UsageRecordRepository
    private let expenseRepository: RelatedExpenseRepository

    // MARK: - 初始化
    init(usageRepository: UsageRecordRepository, expenseRepository: RelatedExpenseRepository) {
        self.usageRepository = usageRepository
        self.expenseRepository = expenseRepository
    }

    // MARK: - 功能方法
    func loadTimelineForProduct(_ product: Product) {
        isLoading = true
        selectedProduct = product

        // 从各来源收集事件
        var allEvents: [TimelineEvent] = []

        // 1. 添加购买事件
        if let purchaseDate = product.purchaseDate {
            allEvents.append(TimelineEvent(
                date: purchaseDate,
                type: .purchase,
                title: "购买了 \(product.name ?? "产品")",
                description: "以 ¥\(Int(product.price)) 的价格购买",
                sourceId: product.id,
                sourceType: "Product"
            ))
        }

        // 2. 添加使用记录事件
        if let records = product.usageRecords?.allObjects as? [UsageRecord] {
            for record in records {
                if let date = record.date {
                    if record.usageType == "personal" {
                        // 个人使用记录
                        if record.isStory {
                            // 物品故事记录
                            let titleText = record.title != nil && !record.title!.isEmpty ? record.title! : "记录了物品故事"

                            // 构建更精致的描述
                            var descriptionParts: [String] = []

                            // 添加情感价值（如果有）
                            if record.emotionalValue > 0 {
                                descriptionParts.append("情感价值: \(record.emotionalValue)/5")
                            }

                            // 添加满意度
                            descriptionParts.append("满意度: \(record.satisfaction)/5")

                            // 添加备注（如果有，且仅显示一部分）
                            if let notes = record.notes, !notes.isEmpty {
                                let shortenedNotes = notes.count > 30 ? notes.prefix(30) + "..." : notes
                                descriptionParts.append(String(shortenedNotes))
                            }

                            // 如果有图片或音频，添加提示
                            if record.images != nil || record.audioRecordings != nil {
                                var mediaTypes: [String] = []
                                if record.images != nil { mediaTypes.append("图片") }
                                if record.audioRecordings != nil { mediaTypes.append("音频") }
                                descriptionParts.append("包含\(mediaTypes.joined(separator: "、"))")
                            }

                            let description = descriptionParts.joined(separator: " · ")

                            allEvents.append(TimelineEvent(
                                date: date,
                                type: .usage,
                                title: "【物品故事】\(titleText)",
                                description: description,
                                sourceId: record.id,
                                sourceType: "UsageRecord"
                            ))
                        } else {
                            // 检查是否为消耗记录
                            if record.isConsumptionRecord {
                                // 消耗记录
                                var descriptionParts: [String] = []
                                
                                // 添加消耗量信息
                                descriptionParts.append("消耗: \(record.consumedQuantityText)")
                                
                                // 添加剩余量信息
                                if record.remainingQuantity > 0 {
                                    descriptionParts.append(record.remainingQuantityText)
                                } else {
                                    descriptionParts.append("已用完")
                                }
                                
                                // 添加消耗效率（如果有）
                                if let rateText = record.consumptionRateText {
                                    descriptionParts.append("效率: \(rateText)")
                                }
                                
                                // 添加满意度
                                descriptionParts.append("满意度: \(record.satisfaction)/5")
                                
                                // 添加使用场景（如果有）
                                if let scenario = record.scenario, !scenario.isEmpty {
                                    descriptionParts.append("场景: \(scenario)")
                                }
                                
                                let description = descriptionParts.joined(separator: " · ")
                                
                                allEvents.append(TimelineEvent(
                                    date: date,
                                    type: .consumption,
                                    title: "消耗了 \(product.name ?? "产品")",
                                    description: description,
                                    sourceId: record.id,
                                    sourceType: "UsageRecord"
                                ))
                            } else {
                                // 普通使用记录
                                allEvents.append(TimelineEvent(
                                    date: date,
                                    type: .usage,
                                    title: "使用了 \(product.name ?? "产品")",
                                    description: "满意度: \(record.satisfaction)/5" + (record.notes != nil ? " - \(record.notes!)" : ""),
                                    sourceId: record.id,
                                    sourceType: "UsageRecord"
                                ))
                            }
                        }
                    } else if record.usageType == "loaned" {
                        // 借出记录
                        allEvents.append(TimelineEvent(
                            date: date,
                            type: .loanOut,
                            title: "借给了 \(record.borrowerName ?? "某人")",
                            description: "预计归还: \(record.dueDate?.formatted(date: .abbreviated, time: .omitted) ?? "未知")" + (record.notes != nil ? " - \(record.notes!)" : ""),
                            sourceId: record.id,
                            sourceType: "UsageRecord"
                        ))

                        // 如果已归还，添加归还事件
                        if let returnDate = record.returnDate {
                            allEvents.append(TimelineEvent(
                                date: returnDate,
                                type: .loanReturn,
                                title: "\(record.borrowerName ?? "某人") 归还了产品",
                                description: "借出时长: \(record.loanDuration ?? 0)天",
                                sourceId: record.id,
                                sourceType: "UsageRecord"
                            ))
                        }
                    } else if record.usageType == "transfer" {
                        // 转让记录 - 注意：这部分代码在CoreData模型更新前可能会报错
                        let transferTypeText = record.transferType?.rawValue ?? "转让"
                        let priceText = record.transferPrice > 0 ? "¥\(Int(record.transferPrice))" : "无偿"
                        let recipientText = record.recipient != nil ? "给 \(record.recipient!)" : ""

                        allEvents.append(TimelineEvent(
                            date: date,
                            type: .transfer,
                            title: "\(transferTypeText)了产品 \(recipientText)",
                            description: "\(priceText)" + (record.notes != nil ? " - \(record.notes!)" : ""),
                            sourceId: record.id,
                            sourceType: "UsageRecord"
                        ))
                    }
                }
            }
        }

        // 3. 添加费用记录事件
        if let expenses = product.relatedExpenses?.allObjects as? [RelatedExpense] {
            for expense in expenses {
                if let date = expense.date {
                    allEvents.append(TimelineEvent(
                        date: date,
                        type: .expense,
                        title: expense.type?.name ?? "花费",
                        description: "¥\(Int(expense.amount))" + (expense.notes != nil ? " - \(expense.notes!)" : ""),
                        sourceId: expense.id,
                        sourceType: "RelatedExpense"
                    ))
                }
            }
        }

        // 4. 添加旧版借阅记录（向后兼容）
        if let loanRecords = product.loanRecords?.allObjects as? [LoanRecord] {
            for loan in loanRecords {
                if let createdAt = loan.createdAt {
                    // 借出事件
                    allEvents.append(TimelineEvent(
                        date: createdAt,
                        type: .loanOut,
                        title: "借给了 \(loan.borrowerName)",
                        description: "预计归还: \(loan.dueDate?.formatted(date: .abbreviated, time: .omitted) ?? "未知")" + (loan.notes != nil ? " - \(loan.notes!)" : ""),
                        sourceId: loan.id,
                        sourceType: "LoanRecord"
                    ))

                    // 如果已归还，添加归还事件
                    if let returnDate = loan.returnDate {
                        allEvents.append(TimelineEvent(
                            date: returnDate,
                            type: .loanReturn,
                            title: "\(loan.borrowerName) 归还了产品",
                            description: "借出时长: \(loan.loanDuration ?? 0)天",
                            sourceId: loan.id,
                            sourceType: "LoanRecord"
                        ))
                    }
                }
            }
        }

        // 按日期排序
        events = allEvents.sorted(by: { $0.date > $1.date })

        // 应用过滤器
        applyFilters()

        isLoading = false
    }

    // 应用过滤器
    func applyFilters() {
        var filtered = events

        // 按事件类型过滤
        if selectedEventTypes.count < TimelineEventType.allCases.count {
            filtered = filtered.filter { selectedEventTypes.contains($0.type) }
        }

        // 按日期范围过滤
        if let startDate = dateRange.start {
            filtered = filtered.filter { $0.date >= startDate }
        }

        if let endDate = dateRange.end {
            filtered = filtered.filter { $0.date <= endDate }
        }

        // 按搜索文本过滤
        if !searchText.isEmpty {
            filtered = filtered.filter {
                $0.title.localizedCaseInsensitiveContains(searchText) ||
                ($0.description?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        filteredEvents = filtered
    }

    // 重置过滤器
    func resetFilters() {
        selectedEventTypes = Set(TimelineEventType.allCases)
        dateRange = (nil, nil)
        searchText = ""
        applyFilters()
    }
}
