import Foundation
import CoreData
import SwiftUI
import Combine

@MainActor
class ConversationHistoryViewModel: ObservableObject {
    
    // MARK: - 属性
    private let conversationRepository: ConversationRepository
    private let messageRepository: MessageRepository
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 发布属性
    @Published var conversations: [Conversation] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var searchText: String = "" {
        didSet {
            performSearch()
        }
    }
    @Published var selectedConversation: Conversation?
    @Published var showingDeleteAlert: Bool = false
    @Published var conversationToDelete: Conversation?
    @Published var isCreatingNewConversation: Bool = false
    
    // 筛选和排序选项
    @Published var sortOption: SortOption = .lastMessageDesc
    @Published var filterOption: FilterOption = .all
    @Published var dateFilterEnabled: Bool = false
    @Published var startDate: Date = Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date()
    @Published var endDate: Date = Date()
    
    // MARK: - 排序选项
    enum SortOption: String, CaseIterable, Identifiable {
        case lastMessageDesc = "最近活跃"
        case lastMessageAsc = "最早活跃"
        case startDateDesc = "最近创建"
        case startDateAsc = "最早创建"
        case titleAsc = "标题A-Z"
        case titleDesc = "标题Z-A"
        case messageCountDesc = "消息数量从多到少"
        case messageCountAsc = "消息数量从少到多"
        
        var id: String { self.rawValue }
    }
    
    // MARK: - 筛选选项
    enum FilterOption: String, CaseIterable, Identifiable {
        case all = "全部会话"
        case recent = "最近7天"
        case thisMonth = "本月"
        case hasMessages = "有消息"
        case empty = "空会话"
        
        var id: String { self.rawValue }
    }
    
    // MARK: - 初始化
    init(conversationRepository: ConversationRepository, messageRepository: MessageRepository) {
        self.conversationRepository = conversationRepository
        self.messageRepository = messageRepository
        
        loadConversations()
    }
    
    // MARK: - 数据加载
    func loadConversations() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                await MainActor.run {
                    var filteredConversations = conversationRepository.fetchAllConversations()
                    
                    // 应用筛选条件
                    filteredConversations = applyFilters(to: filteredConversations)
                    
                    // 应用排序
                    filteredConversations = applySorting(to: filteredConversations)
                    
                    self.conversations = filteredConversations
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载会话失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - 搜索功能
    private func performSearch() {
        if searchText.isEmpty {
            loadConversations()
        } else {
            isLoading = true
            
            Task {
                await MainActor.run {
                    let searchResults = conversationRepository.searchConversations(query: searchText)
                    let filteredResults = applyFilters(to: searchResults)
                    let sortedResults = applySorting(to: filteredResults)
                    
                    self.conversations = sortedResults
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - 筛选和排序
    private func applyFilters(to conversations: [Conversation]) -> [Conversation] {
        var filtered = conversations
        
        // 应用基础筛选
        switch filterOption {
        case .all:
            break
        case .recent:
            let sevenDaysAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
            filtered = filtered.filter { conversation in
                guard let lastMessageDate = conversation.lastMessageDate else { return false }
                return lastMessageDate >= sevenDaysAgo
            }
        case .thisMonth:
            let startOfMonth = Calendar.current.dateInterval(of: .month, for: Date())?.start ?? Date()
            filtered = filtered.filter { conversation in
                guard let lastMessageDate = conversation.lastMessageDate else { return false }
                return lastMessageDate >= startOfMonth
            }
        case .hasMessages:
            filtered = filtered.filter { conversation in
                !conversationRepository.isConversationEmpty(conversationId: conversation.id!)
            }
        case .empty:
            filtered = filtered.filter { conversation in
                conversationRepository.isConversationEmpty(conversationId: conversation.id!)
            }
        }
        
        // 应用日期范围筛选
        if dateFilterEnabled {
            filtered = filtered.filter { conversation in
                guard let startDate = conversation.startDate else { return false }
                return startDate >= self.startDate && startDate <= self.endDate
            }
        }
        
        return filtered
    }
    
    private func applySorting(to conversations: [Conversation]) -> [Conversation] {
        switch sortOption {
        case .lastMessageDesc:
            return conversations.sorted { (lhs, rhs) in
                let lhsDate = lhs.lastMessageDate ?? Date.distantPast
                let rhsDate = rhs.lastMessageDate ?? Date.distantPast
                return lhsDate > rhsDate
            }
        case .lastMessageAsc:
            return conversations.sorted { (lhs, rhs) in
                let lhsDate = lhs.lastMessageDate ?? Date.distantPast
                let rhsDate = rhs.lastMessageDate ?? Date.distantPast
                return lhsDate < rhsDate
            }
        case .startDateDesc:
            return conversations.sorted { (lhs, rhs) in
                let lhsDate = lhs.startDate ?? Date.distantPast
                let rhsDate = rhs.startDate ?? Date.distantPast
                return lhsDate > rhsDate
            }
        case .startDateAsc:
            return conversations.sorted { (lhs, rhs) in
                let lhsDate = lhs.startDate ?? Date.distantPast
                let rhsDate = rhs.startDate ?? Date.distantPast
                return lhsDate < rhsDate
            }
        case .titleAsc:
            return conversations.sorted { (lhs, rhs) in
                let lhsTitle = lhs.title ?? ""
                let rhsTitle = rhs.title ?? ""
                return lhsTitle < rhsTitle
            }
        case .titleDesc:
            return conversations.sorted { (lhs, rhs) in
                let lhsTitle = lhs.title ?? ""
                let rhsTitle = rhs.title ?? ""
                return lhsTitle > rhsTitle
            }
        case .messageCountDesc:
            return conversations.sorted { (lhs, rhs) in
                let lhsCount = conversationRepository.getMessageCount(conversationId: lhs.id!)
                let rhsCount = conversationRepository.getMessageCount(conversationId: rhs.id!)
                return lhsCount > rhsCount
            }
        case .messageCountAsc:
            return conversations.sorted { (lhs, rhs) in
                let lhsCount = conversationRepository.getMessageCount(conversationId: lhs.id!)
                let rhsCount = conversationRepository.getMessageCount(conversationId: rhs.id!)
                return lhsCount < rhsCount
            }
        }
    }
    
    // MARK: - 会话操作
    func createNewConversation(title: String? = nil) {
        isCreatingNewConversation = true
        errorMessage = nil
        
        Task {
            do {
                await MainActor.run {
                    let conversationTitle = title ?? generateDefaultTitle()
                    
                    if let newConversation = conversationRepository.createConversation(title: conversationTitle) {
                        self.selectedConversation = newConversation
                        self.loadConversations()
                    } else {
                        self.errorMessage = "创建新会话失败"
                    }
                    
                    self.isCreatingNewConversation = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "创建新会话失败: \(error.localizedDescription)"
                    self.isCreatingNewConversation = false
                }
            }
        }
    }
    
    func deleteConversation(_ conversation: Conversation) {
        guard let conversationId = conversation.id else {
            errorMessage = "无效的会话ID"
            return
        }
        
        Task {
            await MainActor.run {
                let success = conversationRepository.deleteConversation(conversationId: conversationId)
                
                if success {
                    // 如果删除的是当前选中的会话，清空选择
                    if selectedConversation?.id == conversationId {
                        selectedConversation = nil
                    }
                    loadConversations()
                } else {
                    errorMessage = "删除会话失败"
                }
            }
        }
    }
    
    func renameConversation(_ conversation: Conversation, newTitle: String) {
        guard let conversationId = conversation.id else {
            errorMessage = "无效的会话ID"
            return
        }
        
        Task {
            await MainActor.run {
                let success = conversationRepository.updateConversationTitle(
                    conversationId: conversationId,
                    newTitle: newTitle
                )
                
                if success {
                    loadConversations()
                } else {
                    errorMessage = "重命名会话失败"
                }
            }
        }
    }
    
    // MARK: - 便利方法
    func getMessageCount(for conversation: Conversation) -> Int {
        guard let conversationId = conversation.id else { return 0 }
        return conversationRepository.getMessageCount(conversationId: conversationId)
    }
    
    func getLastMessagePreview(for conversation: Conversation) -> String {
        guard let conversationId = conversation.id else { return "暂无消息" }
        
        if let lastMessage = messageRepository.fetchLatestMessage(conversationId: conversationId) {
            let content = lastMessage.text ?? ""
            return content.count > 50 ? String(content.prefix(50)) + "..." : content
        }
        
        return "暂无消息"
    }
    
    func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未知时间" }
        
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
            return "今天 \(formatter.string(from: date))"
        } else if calendar.isDateInYesterday(date) {
            formatter.dateFormat = "HH:mm"
            return "昨天 \(formatter.string(from: date))"
        } else if calendar.dateInterval(of: .weekOfYear, for: Date())?.contains(date) == true {
            formatter.dateFormat = "EEEE HH:mm"
            return formatter.string(from: date)
        } else {
            formatter.dateFormat = "MM/dd HH:mm"
            return formatter.string(from: date)
        }
    }
    
    private func generateDefaultTitle() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        return "对话 - \(formatter.string(from: Date()))"
    }
    
    // MARK: - 刷新和重置
    func refresh() {
        loadConversations()
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    func resetFilters() {
        searchText = ""
        sortOption = .lastMessageDesc
        filterOption = .all
        dateFilterEnabled = false
        startDate = Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date()
        endDate = Date()
        loadConversations()
    }
}
