import Foundation
import CoreData
import SwiftUI
import Combine

class MemoirsViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var storyRecords: [UsageRecord] = []
    @Published var isDataLoaded: Bool = false
    @Published var filteredStoryRecords: [UsageRecord] = []
    @Published var searchText: String = ""
    @Published var isDescendingOrder: Bool = true // 默认降序（最新的故事在前面）
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - 特殊功能属性
    @Published var todayInHistoryMemories: [UsageRecord] = []
    
    // MARK: - 私有属性
    let context: NSManagedObjectContext  // 改为 let 以便外部访问
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(context: NSManagedObjectContext) {
        self.context = context
        
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterStories()
            }
            .store(in: &cancellables)
        
        // 监听排序顺序变化
        $isDescendingOrder
            .sink { [weak self] _ in
                self?.sortStories()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 加载所有物品故事
    func loadAllStories() {
        isLoading = true
        
        let request = NSFetchRequest<UsageRecord>(entityName: "UsageRecord")
        request.predicate = NSPredicate(format: "isStory == %@", NSNumber(value: true))
        request.sortDescriptors = [NSSortDescriptor(key: "date", ascending: !isDescendingOrder)]
        
        do {
            storyRecords = try context.fetch(request)
            filterStories()
            isLoading = false
            isDataLoaded = true
        } catch {
            errorMessage = "加载故事失败: \(error.localizedDescription)"
            storyRecords = []
            filteredStoryRecords = []
            isLoading = false
        }
    }
    
    /// 切换排序顺序
    func toggleSortOrder() {
        isDescendingOrder.toggle()
    }
    
    /// 清除搜索
    func clearSearch() {
        searchText = ""
    }
    
    // MARK: - 私有方法
    
    /// 过滤故事
    private func filterStories() {
        if searchText.isEmpty {
            filteredStoryRecords = storyRecords
        } else {
            // 按标题、产品名称、记忆内容或笔记搜索
            filteredStoryRecords = storyRecords.filter { record in
                let titleMatch = record.title?.localizedCaseInsensitiveContains(searchText) ?? false
                let productNameMatch = record.product?.name?.localizedCaseInsensitiveContains(searchText) ?? false
                let memoriesMatch = record.memories?.localizedCaseInsensitiveContains(searchText) ?? false
                let notesMatch = record.notes?.localizedCaseInsensitiveContains(searchText) ?? false
                return titleMatch || productNameMatch || memoriesMatch || notesMatch
            }
        }
        
        // 应用排序
        sortStories()
    }
    
    /// 对过滤后的故事进行排序
    private func sortStories() {
        filteredStoryRecords.sort { first, second in
            guard let firstDate = first.date, let secondDate = second.date else {
                return false
            }
            
            return isDescendingOrder ? firstDate > secondDate : firstDate < secondDate
        }
    }
    
    // MARK: - 特殊功能方法
    
    /// 获取那年今日的回忆
    func loadTodayInHistoryMemories() {
        let calendar = Calendar.current
        let today = Date()
        let currentMonth = calendar.component(.month, from: today)
        let currentDay = calendar.component(.day, from: today)
        
        let request = NSFetchRequest<UsageRecord>(entityName: "UsageRecord")
        
        // 构建谓词，查找同一天但不同年的记录  
        let currentYear = calendar.component(.year, from: today)
        let predicate = NSPredicate(format: "isStory == %@", NSNumber(value: true))
        
        request.predicate = predicate
        request.sortDescriptors = [NSSortDescriptor(key: "date", ascending: false)]
        
        do {
            let allStories = try context.fetch(request)
            // 手动过滤同一天但不同年的记录
            todayInHistoryMemories = allStories.filter { record in
                guard let recordDate = record.date else { return false }
                let recordMonth = calendar.component(.month, from: recordDate)
                let recordDay = calendar.component(.day, from: recordDate)
                let recordYear = calendar.component(.year, from: recordDate)
                
                return recordMonth == currentMonth && 
                       recordDay == currentDay && 
                       recordYear != currentYear
            }
        } catch {
            print("Error loading today in history memories: \(error)")
            todayInHistoryMemories = []
        }
    }
    
    /// 刷新搜索结果
    func updateSearchText(_ text: String) {
        searchText = text
    }
} 