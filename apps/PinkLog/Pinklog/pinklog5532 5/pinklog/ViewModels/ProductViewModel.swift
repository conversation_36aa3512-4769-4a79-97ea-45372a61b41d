import Foundation
import CoreData
import SwiftUI
import Combine
import UniformTypeIdentifiers // For UTType

/// 真正的Premium同步状态管理器（单例）
private class PremiumSyncManager {
    private var isInProgress = false
    private var hasCompleted = false
    private let queue = DispatchQueue(label: "premium.sync.queue", qos: .userInitiated)

    func canSync() -> Bool {
        return queue.sync {
            return !isInProgress && !hasCompleted
        }
    }

    func startSync() {
        queue.sync {
            isInProgress = true
        }
    }

    func endSync() {
        queue.sync {
            isInProgress = false
            hasCompleted = true
        }
    }

    func reset() {
        queue.sync {
            isInProgress = false
            hasCompleted = false
        }
    }
}

class ProductViewModel: ObservableObject {
    // MARK: - 属性
    private let repository: ProductRepository
    private let categoryRepository: CategoryRepository
    // TODO: Consider creating a dedicated FileManagerService for file operations
    private let fileManager = FileManager.default
    let keychainManager = KeychainManager.shared
    private let purchaseManager = PurchaseManager.shared

    @Published var products: [Product] = []
    @Published var isDataLoaded: Bool = false
    @Published var categories: [Category] = []
    @Published var selectedCategory: Category?
    @Published var selectedProduct: Product?
    @Published var searchText: String = ""
    @Published var sortOption: SortOption = .dateDesc
    @Published var filterOption: FilterOption = .all
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false
    @Published var showPremiumSheet: Bool = false

    // 高级筛选属性
    @Published var advancedFilterEnabled: Bool = false
    @Published var advancedFilterCategories: Set<Category> = []
    @Published var advancedFilterStatuses: Set<Product.ProductStatus> = []
    @Published var advancedFilterTags: Set<UUID> = []
    @Published var advancedFilterChannels: Set<UUID> = []

    // 日期范围筛选
    @Published var dateFilterEnabled: Bool = false
    @Published var startDate: Date = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
    @Published var endDate: Date = Date()

    // 价格范围筛选
    @Published var priceFilterEnabled: Bool = false
    @Published var minPrice: Double = 0
    @Published var maxPrice: Double = 10000

    // 满意度范围筛选
    @Published var satisfactionFilterEnabled: Bool = false
    @Published var minSatisfaction: Double = 1
    @Published var maxSatisfaction: Double = 5

    // 值度范围筛选
    @Published var worthFilterEnabled: Bool = false
    @Published var minWorth: Double = 0
    @Published var maxWorth: Double = 100

    // 保修状态筛选
    @Published var warrantyFilterEnabled: Bool = false
    @Published var warrantyStatus: FilterView.WarrantyStatus = .all

    // 使用频率筛选
    @Published var usageFilterEnabled: Bool = false
    @Published var usageFrequency: FilterView.UsageFrequency = .all

    // Warranty related properties for Add/Edit Product
    @Published var warrantyDetailsInput: String = ""
    @Published var selectedWarrantyFileURL: URL? // Temporary URL from file importer
    @Published var currentWarrantyImagePath: String? // Stored path in CoreData, loaded for editing
    @Published var showWarrantyFileImporter: Bool = false
    @Published var selectedWarrantyFileName: String? // To display in UI
    @Published var warrantyEndDateInput: Date? = nil // For UI binding
    @Published var hasWarranty: Bool = false {
        didSet {
            if !hasWarranty {
                clearWarrantyFields()
            }
        }
    }

    // Allowed content types for warranty file importer
    let allowedWarrantyContentTypes: [UTType] = [.jpeg, .png, .pdf, .heic]


    // 排序选项
    enum SortOption: String, CaseIterable, Identifiable {
        case dateDesc = "最近购买"
        case dateAsc = "最早购买"
        case priceDesc = "价格从高到低"
        case priceAsc = "价格从低到高"
        case nameAsc = "名称A-Z"
        case nameDesc = "名称Z-A"
        case worthDesc = "值度从高到低"
        case worthAsc = "值度从低到高"
        case usageCountDesc = "使用次数从多到少"
        case usageCountAsc = "使用次数从少到多"
        case costPerUseDesc = "单次成本从高到低"
        case costPerUseAsc = "单次成本从低到高"

        var id: String { self.rawValue }
    }

    // 筛选选项
    enum FilterOption: String, CaseIterable, Identifiable {
        case all = "全部"
        case nearExpiry = "即将过期"
        case nearWarranty = "即将过保"
        case lowUsage = "低使用率"
        case highWorth = "高值度"
        case lowWorth = "低值度"
        case loaned = "已借出"
        case overdue = "借出逾期"

        var id: String { self.rawValue }
    }

    // MARK: - 初始化
    init(repository: ProductRepository, categoryRepository: CategoryRepository) {
        self.repository = repository
        self.categoryRepository = categoryRepository

        // 第一步：加载基础数据
        loadProducts()
        loadCategories()

        // 第二步：检查Premium状态（优化版本）
        print("🎯 ProductViewModel初始化：优化启动流程")

        // 智能Premium状态检查：只在必要时同步
        Task { @MainActor in
            performInitialPremiumCheck()
        }

        // 根据Premium状态决定是否需要限制检查
        if isPremiumUser {
            print("✅ 检测到Premium用户，跳过所有限制检查")
            print("🏁 ProductViewModel 初始化完成（Premium用户）")
            print("💎 当前Premium状态: \(isPremiumUser)")
            print("✅ 可以添加产品: \(canAddProduct())")
            print("📊 剩余免费物品: \(getRemainingFreeItemsDisplayText())")
        } else {
            print("🔒 检测到免费用户，执行限制检查")
            // 只有非Premium用户才需要进行产品计数和限制检查
            syncProductCount()
            print("🏁 ProductViewModel 初始化完成（免费用户）")
            print("💎 当前Premium状态: \(isPremiumUser)")
            print("✅ 可以添加产品: \(canAddProduct())")
            print("📊 剩余免费物品: \(getRemainingFreeItemsDisplayText())")
        }

        // 监听购买完成通知
        NotificationCenter.default.addObserver(
            forName: .purchaseCompleted,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            print("📢 ProductViewModel收到购买完成通知")
            print("📦 通知内容: \(notification.userInfo ?? [:])")

            if let shouldSync = notification.userInfo?["shouldSyncPremiumStatus"] as? Bool, shouldSync {
                print("🔄 开始同步Premium状态")
                let isRestore = notification.userInfo?["isRestore"] as? Bool ?? false
                print("🔄 是否为恢复购买: \(isRestore)")

                self?.syncPremiumStatus()
                print("💎 Premium状态同步完成: \(self?.isPremiumUser ?? false)")
                print("✅ 现在可以添加产品: \(self?.canAddProduct() ?? false)")
            } else {
                print("⚠️ 通知中没有shouldSyncPremiumStatus标志")
            }
        }

        // 监听PurchaseManager初始化完成通知
        NotificationCenter.default.addObserver(
            forName: .purchaseManagerInitialized,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            print("📢 ProductViewModel收到PurchaseManager初始化完成通知")
            print("📦 通知内容: \(notification.userInfo ?? [:])")

            // PurchaseManager初始化完成后，同步Premium状态（防重复逻辑在syncPremiumStatus内部）
            print("🔄 执行PurchaseManager初始化后的Premium状态同步")
            self?.syncPremiumStatus()
        }
    }

    /// 简化的Premium状态检查（只在PurchaseManager就绪时同步）
    @MainActor
    private func performInitialPremiumCheck() {
        // 如果PurchaseManager已经完成初始化，直接同步
        if !purchaseManager.isLoading {
            print("✅ PurchaseManager已就绪，执行Premium状态同步")
            syncPremiumStatus()
        } else {
            print("⏳ PurchaseManager正在加载，等待初始化完成通知")
            // 使用缓存的状态，不进行同步
            let cachedStatus = keychainManager.isPremiumUser
            print("💾 使用缓存的Premium状态: \(cachedStatus)")
        }
    }

    // MARK: - 内购相关方法
    func canAddProduct() -> Bool {
        // 如果是Premium用户，直接返回true，无需检查产品计数
        if isPremiumUser {
            print("✅ Premium用户，无需检查产品计数限制")
            return true
        }

        // 只有非Premium用户才需要检查产品计数限制
        let canAdd = keychainManager.canAddProduct()
        print("🔒 非Premium用户，检查产品计数限制: \(canAdd)")
        return canAdd
    }
    
    func getRemainingFreeItems() -> Int {
        // 如果是Premium用户，返回无限制（用一个很大的数字表示）
        if isPremiumUser {
            print("✅ Premium用户，剩余物品数量: 无限制")
            return Int.max
        }

        // 只有非Premium用户才需要计算剩余免费物品数量
        let remaining = keychainManager.getRemainingFreeItems()
        print("🔒 非Premium用户，剩余免费物品: \(remaining)")
        return remaining
    }
    
    /// 获取用户友好的剩余物品数量显示文本
    func getRemainingFreeItemsDisplayText() -> String {
        if isPremiumUser {
            return "无限制"
        }
        
        let remaining = keychainManager.getRemainingFreeItems()
        return "\(remaining)"
    }
    
    var isPremiumUser: Bool {
        return keychainManager.isPremiumUser
    }
    


    /// 全局Premium同步状态管理（真正的单例）
    private static let syncManager = PremiumSyncManager()

    func syncPremiumStatus() {
        // 使用真正的单例防重复机制
        guard Self.syncManager.canSync() else {
            print("🔒 Premium状态同步正在进行中或已完成，跳过重复调用")
            return
        }

        Self.syncManager.startSync()
        defer { Self.syncManager.endSync() }

        Task { @MainActor in
            let oldStatus = keychainManager.isPremiumUser
            let newStatus = purchaseManager.isPremiumPurchased

            // 安全检查：如果PurchaseManager还在加载中，不要覆盖现有的Premium状态
            if purchaseManager.isLoading {
                print("⚠️ PurchaseManager正在加载中，跳过Premium状态同步")
                return
            }

            // 严格的安全检查：如果当前是Premium用户，但PurchaseManager显示为false
            // 这很可能是初始化时序问题，绝对不要覆盖现有的Premium状态
            if oldStatus && !newStatus {
                print("🚨 严重警告：当前是Premium用户但PurchaseManager显示false")
                print("🔒 为安全起见，保持现有Premium状态，不进行覆盖")
                print("📊 PurchaseManager产品列表: \(purchaseManager.products.count) 个产品")
                print("📊 PurchaseManager已购买列表: \(purchaseManager.purchasedProducts)")

                // 如果产品列表为空，说明PurchaseManager可能还没完全初始化
                if purchaseManager.products.isEmpty {
                    print("⚠️ PurchaseManager产品列表为空，可能未完全初始化")
                    return
                }

                // 如果产品列表不为空但已购买列表为空，这是异常情况
                if !purchaseManager.products.isEmpty && purchaseManager.purchasedProducts.isEmpty {
                    print("⚠️ 产品列表不为空但已购买列表为空，可能是交易验证问题")
                    return
                }

                // 只有在确认PurchaseManager完全初始化且确实没有购买记录时才更新
                print("⚠️ PurchaseManager已完全初始化但确实没有购买记录，保持谨慎")
                return
            }

            // 如果从false变为true，这是安全的更新
            if !oldStatus && newStatus {
                keychainManager.isPremiumUser = newStatus
                print("✅ Premium状态安全更新: \(oldStatus) -> \(newStatus)")

                // 强制刷新UI
                objectWillChange.send()
            } else if oldStatus == newStatus {
                print("ℹ️ Premium状态无需更新: \(oldStatus)")
            } else {
                print("🔒 Premium状态保护：拒绝从true更新为false")
            }

            print("💎 PurchaseManager状态: \(purchaseManager.isPremiumPurchased)")
            print("🔑 KeychainManager状态: \(keychainManager.isPremiumUser)")
            print("✅ 现在可以添加产品: \(canAddProduct())")
        }
    }
    
    func syncProductCount() {
        // 如果是Premium用户，完全跳过产品计数和限制检查
        if isPremiumUser {
            print("✅ Premium用户，跳过产品计数和限制检查")
            return
        }

        print("🔒 非Premium用户，执行产品计数和限制检查")

        let actualCount = repository.fetchAll().count
        let currentKeychainCount = keychainManager.productCount

        // 同步设备使用历史以确保即使重装APP也能维持限制
        keychainManager.syncDeviceUsageHistory(with: actualCount)

        // 如果Keychain计数器为0且数据库有数据，说明是第一次启动或重装后
        // 此时应该使用数据库的实际数量，但要确保不低于实际数量（防止绕过限制）
        if currentKeychainCount == 0 && actualCount > 0 {
            keychainManager.productCount = actualCount
            print("🔄 首次同步产品计数: Keychain=\(keychainManager.productCount), 实际=\(actualCount)")
        } else if currentKeychainCount < actualCount {
            // 如果Keychain计数器小于实际数量，说明可能有数据不一致
            // 使用实际数量确保限制不被绕过
            keychainManager.productCount = actualCount
            print("⚠️ 修正产品计数不一致: Keychain=\(currentKeychainCount) -> \(actualCount), 实际=\(actualCount)")
        } else {
            // Keychain计数器大于或等于实际数量，保持Keychain的值
            print("✅ 保持产品计数: Keychain=\(currentKeychainCount), 实际=\(actualCount)")
        }

        print("🔒 设备使用历史检查完成，当前限制状态: 可添加=\(keychainManager.canAddProduct())")
    }
    
    func checkLimitAndShowSheet() -> Bool {
        // 如果是Premium用户，直接允许添加
        if isPremiumUser {
            print("✅ Premium用户，直接允许添加产品")
            return true
        }

        // 非Premium用户需要检查限制
        if !canAddProduct() {
            print("🔒 非Premium用户达到限制，显示Premium购买页面")
            showPremiumSheet = true
            return false
        }

        print("✅ 非Premium用户未达到限制，允许添加产品")
        return true
    }

    // MARK: - 数据加载
    func loadProducts() {
        isLoading = true
        
        // u5bfcu51fau5f53u524du4ea7u54c1u5217u8868u4ee5u4fbfu8fdb u884cu6bd4u8f83
        let oldProducts = products
        // u6e05u7a7au4ea7u54c1u5217u8868
        products = []

        // 获取所有产品
        var filteredProducts = repository.fetchAll()

        // 如果高级筛选启用，应用高级筛选条件
        if advancedFilterEnabled {
            // 应用类别筛选
            if !advancedFilterCategories.isEmpty {
                filteredProducts = filteredProducts.filter { product in
                    guard let category = product.category else { return false }
                    return advancedFilterCategories.contains(category)
                }
            }

            // 应用产品状态筛选
            if !advancedFilterStatuses.isEmpty {
                filteredProducts = filteredProducts.filter { product in
                    return advancedFilterStatuses.contains(product.status)
                }
            }

            // 应用标签筛选
            if !advancedFilterTags.isEmpty {
                filteredProducts = filteredProducts.filter { product in
                    if let tags = product.tags?.allObjects as? [Tag] {
                        for tag in tags {
                            if let tagId = tag.id, advancedFilterTags.contains(tagId) {
                                return true
                            }
                        }
                    }
                    return false
                }
            }

            // 应用购买渠道筛选
            if !advancedFilterChannels.isEmpty {
                filteredProducts = filteredProducts.filter { product in
                    guard let channel = product.purchaseChannelRelation, let channelId = channel.id else { return false }
                    return advancedFilterChannels.contains(channelId)
                }
            }

            // 应用日期范围筛选
            if dateFilterEnabled {
                filteredProducts = filteredProducts.filter { product in
                    guard let purchaseDate = product.purchaseDate else { return false }
                    return purchaseDate >= startDate && purchaseDate <= endDate
                }
            }

            // 应用价格范围筛选
            if priceFilterEnabled {
                filteredProducts = filteredProducts.filter { product in
                    return product.price >= minPrice && product.price <= maxPrice
                }
            }

            // 应用满意度范围筛选
            if satisfactionFilterEnabled {
                filteredProducts = filteredProducts.filter { product in
                    let satisfaction = product.averageSatisfaction
                    return satisfaction >= minSatisfaction && satisfaction <= maxSatisfaction
                }
            }

            // 应用值度范围筛选
            if worthFilterEnabled {
                filteredProducts = filteredProducts.filter { product in
                    let worth = product.worthItIndex()
                    return worth >= minWorth && worth <= maxWorth
                }
            }

            // 应用保修状态筛选
            if warrantyFilterEnabled {
                switch warrantyStatus {
                case .all:
                    break // 不做筛选
                case .inWarranty:
                    filteredProducts = filteredProducts.filter { product in
                        guard let warrantyEndDate = product.warrantyEndDate else { return false }
                        return warrantyEndDate > Date() && !product.isNearWarrantyEnd
                    }
                case .expiringSoon:
                    filteredProducts = filteredProducts.filter { product in
                        return product.isNearWarrantyEnd
                    }
                case .expired:
                    filteredProducts = filteredProducts.filter { product in
                        guard let warrantyEndDate = product.warrantyEndDate else { return true }
                        return warrantyEndDate < Date()
                    }
                }
            }

            // 应用使用频率筛选
            if usageFilterEnabled {
                switch usageFrequency {
                case .all:
                    break // 不做筛选
                case .high:
                    filteredProducts = filteredProducts.filter { product in
                        return product.monthlyUsageFrequency > 10
                    }
                case .medium:
                    filteredProducts = filteredProducts.filter { product in
                        return product.monthlyUsageFrequency >= 5 && product.monthlyUsageFrequency <= 10
                    }
                case .low:
                    filteredProducts = filteredProducts.filter { product in
                        return product.monthlyUsageFrequency > 0 && product.monthlyUsageFrequency < 5
                    }
                case .unused:
                    filteredProducts = filteredProducts.filter { product in
                        return product.totalUsageCount == 0
                    }
                }
            }
        } else {
            // 如果高级筛选未启用，则使用常规筛选
        switch filterOption {
        case .all:
                // 已经获取了所有产品
                break
        case .nearExpiry:
            filteredProducts = repository.fetchNearExpiry()
        case .nearWarranty:
            filteredProducts = repository.fetchNearWarrantyEnd()
        case .lowUsage:
            filteredProducts = repository.fetchLowUsageProducts()
        case .highWorth:
                filteredProducts = filteredProducts.filter { $0.worthItIndex() >= 60 }
        case .lowWorth:
                filteredProducts = filteredProducts.filter { $0.worthItIndex() < 40 }
        case .loaned:
                // 筛选出当前已借出的产品
                filteredProducts = filteredProducts.filter { product in
                    return product.loanStatus == .loanedOut
                }
        case .overdue:
                // 筛选出借出已逾期的产品
                filteredProducts = filteredProducts.filter { product in
                    return product.loanStatus == .overdue
                }
        }

        // 根据类别筛选
        if let selectedCategory = selectedCategory {
            filteredProducts = filteredProducts.filter { $0.category?.id == selectedCategory.id }
            }
        }

        // 根据搜索文本筛选
        if !searchText.isEmpty {
            filteredProducts = filteredProducts.filter {
                ($0.name?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                ($0.brand?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                ($0.model?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // 排序
        switch sortOption {
        case .dateDesc:
            filteredProducts.sort { ($0.purchaseDate ?? Date()) > ($1.purchaseDate ?? Date()) }
        case .dateAsc:
            filteredProducts.sort { ($0.purchaseDate ?? Date()) < ($1.purchaseDate ?? Date()) }
        case .priceDesc:
            filteredProducts.sort { $0.price > $1.price }
        case .priceAsc:
            filteredProducts.sort { $0.price < $1.price }
        case .nameAsc:
            filteredProducts.sort { ($0.name ?? "") < ($1.name ?? "") }
        case .nameDesc:
            filteredProducts.sort { ($0.name ?? "") > ($1.name ?? "") }
        case .worthDesc:
            filteredProducts.sort { $0.worthItIndex() > $1.worthItIndex() }
        case .worthAsc:
            filteredProducts.sort { $0.worthItIndex() < $1.worthItIndex() }
        case .usageCountDesc:
            filteredProducts.sort { $0.totalUsageCount > $1.totalUsageCount }
        case .usageCountAsc:
            filteredProducts.sort { $0.totalUsageCount < $1.totalUsageCount }
        case .costPerUseDesc:
            filteredProducts.sort { $0.costPerUse > $1.costPerUse }
        case .costPerUseAsc:
            filteredProducts.sort { $0.costPerUse < $1.costPerUse }
        }

        DispatchQueue.main.async {
            // u5e73u6ed1u8fc7u6e21u66f4u65b0uff0cu65b0u589eu7684u9879u76eeu4f1au4f7fu7528u52a8u753b
            withAnimation(.easeInOut(duration: 0.3)) {
                self.products = filteredProducts
                self.isLoading = false
                self.isDataLoaded = true
            }
        }
    }

    func loadCategories() {
        categories = categoryRepository.fetchAll()
    }

    // MARK: - CRUD操作
    func addProduct(name: String, brand: String?, model: String?, price: Double, purchaseDate: Date, category: Category?, images: Data?, expiryDate: Date?, valuationMethod: String = "usage") -> Product? {
        var createdProduct: Product? = nil
        var finalWarrantyImagePath: String? = nil

        if self.hasWarranty {
            if let tempURL = self.selectedWarrantyFileURL {
                finalWarrantyImagePath = saveWarrantyFileToAppStorage(temporaryURL: tempURL, productName: name)
            }
        }

        let success = repository.save { context in
            let newProduct = Product(context: context)
            newProduct.id = UUID()
            newProduct.name = name
            newProduct.brand = brand
            newProduct.model = model
            newProduct.price = price
            newProduct.purchaseDate = purchaseDate
            newProduct.category = category
            newProduct.images = images
            newProduct.expiryDate = expiryDate
            newProduct.valuationMethod = valuationMethod

            if self.hasWarranty {
                newProduct.warrantyEndDate = self.warrantyEndDateInput
                newProduct.warrantyDetails = self.warrantyDetailsInput.isEmpty ? nil : self.warrantyDetailsInput
                newProduct.warrantyImage = finalWarrantyImagePath
            } else {
                newProduct.warrantyEndDate = nil
                newProduct.warrantyDetails = nil
                newProduct.warrantyImage = nil
            }
            createdProduct = newProduct
        }

        if success {
            keychainManager.incrementProductCount()
            loadProducts()
            resetWarrantyInputs() // This will also set hasWarranty to false and clear fields

            if let product = createdProduct {
                if product.expiryDate != nil {
                    NotificationManager.shared.scheduleExpiryReminder(for: product)
                }
                if self.hasWarranty && product.warrantyEndDate != nil { // Only schedule if warranty was added
                    NotificationManager.shared.scheduleWarrantyReminder(for: product)
                }
                NotificationManager.shared.scheduleLowUsageReminder(for: product)
            }
        } else {
            errorMessage = "添加产品失败"
            if let pathToDelete = finalWarrantyImagePath { // If file was copied but product save failed
                deleteWarrantyFileFromAppStorage(relativePath: pathToDelete)
            }
        }
        return createdProduct
    }

    func updateProduct(_ product: Product, name: String, brand: String?, model: String?, price: Double, purchaseDate: Date, category: Category?, images: Data?, expiryDate: Date?, valuationMethod: String? = nil, clearExistingWarrantyImageExplicitly: Bool) -> Bool {
        var finalNewWarrantyImagePath: String? = product.warrantyImage // Start with existing

        if !self.hasWarranty {
            // If warranty is turned off, clear all warranty info
            if let oldPath = product.warrantyImage {
                deleteWarrantyFileFromAppStorage(relativePath: oldPath)
            }
            finalNewWarrantyImagePath = nil
        } else {
            // Warranty is on
            if clearExistingWarrantyImageExplicitly { // User explicitly cleared the image via UI
                if let oldPath = product.warrantyImage {
                    deleteWarrantyFileFromAppStorage(relativePath: oldPath)
                }
                finalNewWarrantyImagePath = nil
                self.currentWarrantyImagePath = nil // Reflect in VM
                self.selectedWarrantyFileName = nil
            }

            if let tempURL = self.selectedWarrantyFileURL { // New file selected
                // Delete old file if it exists and is different from the one we might have just cleared
                if let oldPath = product.warrantyImage, oldPath != finalNewWarrantyImagePath {
                     deleteWarrantyFileFromAppStorage(relativePath: oldPath)
                }
                finalNewWarrantyImagePath = saveWarrantyFileToAppStorage(temporaryURL: tempURL, productName: name)
            }
            // If no new file and not explicitly cleared, finalNewWarrantyImagePath remains product.warrantyImage (or nil if it was cleared)
        }

        let success = repository.save { context in
            product.name = name
            product.brand = brand
            product.model = model
            product.price = price
            product.purchaseDate = purchaseDate
            product.category = category

            if let images = images {
                product.images = images
            }
            product.expiryDate = expiryDate

            if let valuationMethod = valuationMethod {
                product.valuationMethod = valuationMethod
            }

            if self.hasWarranty {
                product.warrantyEndDate = self.warrantyEndDateInput
                product.warrantyDetails = self.warrantyDetailsInput.isEmpty ? nil : self.warrantyDetailsInput
                product.warrantyImage = finalNewWarrantyImagePath
            } else {
                product.warrantyEndDate = nil
                product.warrantyDetails = nil
                product.warrantyImage = nil // Ensure it's nil if hasWarranty is false
                 // The file itself should have been deleted above if hasWarranty became false
            }
        }

        if success {
            loadProducts()
            resetWarrantyInputs() // Clears VM state including hasWarranty

            if let id = product.id { // Reschedule all reminders for the product
                NotificationManager.shared.cancelReminders(for: id)
            }
            if product.expiryDate != nil {
                NotificationManager.shared.scheduleExpiryReminder(for: product)
            }
            // Check actual product state for warranty reminder, not self.hasWarranty as VM is reset
            if product.warrantyEndDate != nil {
                NotificationManager.shared.scheduleWarrantyReminder(for: product)
            }
            NotificationManager.shared.scheduleLowUsageReminder(for: product)
        } else {
            errorMessage = "更新产品失败"
            // Consider if a newly saved file (finalNewWarrantyImagePath) needs deletion if product save fails
        }
        return success
    }

    func deleteProduct(_ product: Product) -> Bool {
        // Delete associated warranty file before deleting the product from CoreData
        if let warrantyImagePath = product.warrantyImage {
            deleteWarrantyFileFromAppStorage(relativePath: warrantyImagePath)
        }

        // 取消提醒
        if let id = product.id {
            NotificationManager.shared.cancelReminders(for: id)
        }

        let success = repository.delete(entity: product)

        if success {
            // 同步更新Keychain计数器
            if keychainManager.productCount > 0 {
                keychainManager.productCount -= 1
                print("🔄 删除产品后更新Keychain计数: \(keychainManager.productCount)")
            }
            loadProducts()
        } else {
            errorMessage = "删除产品失败"
        }

        return success
    }

    // MARK: - Warranty File Handling
    func handleWarrantyFileSelection(result: Result<URL, Error>) {
        switch result {
        case .success(let url):
            // IMPORTANT: Request access to security-scoped resource
            let secured = url.startAccessingSecurityScopedResource()
            defer {
                if secured {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            // Store the temporary URL, actual file copy will happen on save
            self.selectedWarrantyFileURL = url
            self.selectedWarrantyFileName = url.lastPathComponent
            self.currentWarrantyImagePath = nil // Clear any existing path if a new file is selected
            self.errorMessage = nil
        case .failure(let error):
            self.errorMessage = "无法选择文件: \(error.localizedDescription)"
            self.selectedWarrantyFileURL = nil
            self.selectedWarrantyFileName = nil
        }
    }

    func clearSelectedWarrantyFile() {
        // This method is called when the user wants to clear the selected file from the UI
        // before saving, or to indicate they want to remove an existing persisted image.
        self.selectedWarrantyFileURL = nil
        self.selectedWarrantyFileName = nil
        // If `currentWarrantyImagePath` is not nil, the `updateProduct` method will need
        // `clearExistingWarrantyImageExplicitly: true` to be passed if the user intends to remove it
        // and `hasWarranty` is still true. If `hasWarranty` becomes false, it's removed anyway.
    }

    // loadWarrantyInfoForEditing is now effectively replaced by setupForExistingProduct
    // func loadWarrantyInfoForEditing(product: Product?) { ... } // Removed

    private func clearWarrantyFields() {
        self.warrantyEndDateInput = nil
        self.warrantyDetailsInput = ""
        self.selectedWarrantyFileURL = nil
        // self.currentWarrantyImagePath = nil // Don't clear this here, it's for existing image path. Cleared on save if needed.
        self.selectedWarrantyFileName = nil
        // self.showWarrantyFileImporter = false // This should be controlled by the view logic
    }

    func resetWarrantyInputs() {
        self.hasWarranty = false // This will trigger clearWarrantyFields via didSet
        self.currentWarrantyImagePath = nil // Also clear any loaded existing image path
        self.showWarrantyFileImporter = false // Reset importer flag
    }

    func setupForExistingProduct(_ product: Product) {
        // Logic to populate ViewModel from an existing product
        // This will set hasWarranty and other fields
        let productHasActualWarranty = product.warrantyEndDate != nil || !(product.warrantyDetails?.isEmpty ?? true) || !(product.warrantyImage?.isEmpty ?? true)
        self.hasWarranty = productHasActualWarranty

        if self.hasWarranty {
            self.warrantyEndDateInput = product.warrantyEndDate
            self.warrantyDetailsInput = product.warrantyDetails ?? ""
            self.currentWarrantyImagePath = product.warrantyImage
            if let path = product.warrantyImage, !path.isEmpty {
                 self.selectedWarrantyFileName = URL(fileURLWithPath: path).lastPathComponent
            } else {
                self.selectedWarrantyFileName = nil
            }
            self.selectedWarrantyFileURL = nil // Ensure this is nil when loading existing
        } else {
            // Ensure fields are clear if product has no warranty, though hasWarranty didSet should handle this.
            clearWarrantyFields()
            self.currentWarrantyImagePath = nil
        }
    }

    // Placeholder for actual file storage logic.
    // Ideally, this would be in a separate FileManagerService.
    // This basic version saves to a "WarrantyDocuments" subdirectory in Application Support.
    // For iCloud, more setup is needed (entitlements, container ID).
    private func getWarrantyDocumentsDirectory() -> URL? {
        guard let appSupportDir = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            errorMessage = "无法访问应用支持目录"
            return nil
        }
        let warrantyDir = appSupportDir.appendingPathComponent("WarrantyDocuments")
        if !fileManager.fileExists(atPath: warrantyDir.path) {
            do {
                try fileManager.createDirectory(at: warrantyDir, withIntermediateDirectories: true, attributes: nil)
            } catch {
                errorMessage = "无法创建保修文件目录: \(error.localizedDescription)"
                return nil
            }
        }
        return warrantyDir
    }

    private func saveWarrantyFileToAppStorage(temporaryURL: URL, productName: String) -> String? {
        guard let warrantyDir = getWarrantyDocumentsDirectory() else { return nil }

        // It's better to use a unique ID for the filename to avoid collisions
        // For simplicity now, using product name + original extension, but this is not robust.
        // A better approach: UUID().uuidString + "." + temporaryURL.pathExtension
        let uniqueFileName = (productName.isEmpty ? "warranty" : productName.replacingOccurrences(of: " ", with: "_")) + "_\(UUID().uuidString.prefix(8))." + temporaryURL.pathExtension
        let destinationURL = warrantyDir.appendingPathComponent(uniqueFileName)

        // Ensure we have security access to the temporary URL
        let secured = temporaryURL.startAccessingSecurityScopedResource()
        defer {
            if secured {
                temporaryURL.stopAccessingSecurityScopedResource()
            }
        }

        do {
            // If a file with the same name exists, remove it first (though unique names should prevent this)
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }
            try fileManager.copyItem(at: temporaryURL, to: destinationURL)
            // Return the path relative to the warrantyDir for storage in CoreData, or just the filename if warrantyDir is standard.
            // For simplicity, let's store just the filename and assume they are all in `WarrantyDocuments`.
            return uniqueFileName // This is the relative path to be stored.
        } catch {
            errorMessage = "无法保存保修文件: \(error.localizedDescription)"
            return nil
        }
    }

    private func deleteWarrantyFileFromAppStorage(relativePath: String) {
        guard let warrantyDir = getWarrantyDocumentsDirectory(), !relativePath.isEmpty else { return }
        let fileURL = warrantyDir.appendingPathComponent(relativePath)
        do {
            if fileManager.fileExists(atPath: fileURL.path) {
                try fileManager.removeItem(at: fileURL)
            }
        } catch {
            // Log error, but don't necessarily block UI for this background cleanup
            print("Error deleting warranty file \(relativePath): \(error.localizedDescription)")
            // errorMessage = "无法删除旧的保修文件: \(error.localizedDescription)" // Avoid too many error popups
        }
    }

    // 保存保修文件
    func saveWarrantyFile(from sourceURL: URL, for product: Product) {
        let productName = product.name ?? "Product"
        if let savedPath = saveWarrantyFileToAppStorage(temporaryURL: sourceURL, productName: productName) {
            product.warrantyImage = savedPath
            repository.save { _ in }
        }
    }

    // 删除保修文件
    func removeWarrantyFile(for product: Product) {
        if let path = product.warrantyImage {
            deleteWarrantyFileFromAppStorage(relativePath: path)
            product.warrantyImage = nil
            repository.save { _ in }
        }
    }

    // MARK: - 刷新与重新加载方法
    
    /// 强制刷新产品数据，用于产品增删改后立即刷新列表
    func forceRefreshProducts() {
        // 重新加载产品数据，但不清空数组
        DispatchQueue.main.async {
            self.loadProducts()
        }
    }
    
    /// 平滑添加新产品到列表
    /// - Parameter product: 新添加的产品
    func smoothlyAddProduct(_ product: Product) {
        // 检查产品是否已存在
        if !products.contains(where: { $0.id == product.id }) {
            // 根据当前排序和筛选条件确定是否应该显示该产品
            var shouldDisplay = true
            
            // 检查产品是否符合当前筛选条件
            if !matchesCurrentFilter(product) {
                shouldDisplay = false
            }
            
            if shouldDisplay {
                // 将新产品添加到列表首位（默认排序方式为时间降序）
                DispatchQueue.main.async {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.products.insert(product, at: 0)
                    }
                }
            }
        }
    }
    
    /// 检查产品是否匹配当前筛选条件
    /// - Parameter product: 要检查的产品
    /// - Returns: 是否匹配
    private func matchesCurrentFilter(_ product: Product) -> Bool {
        switch filterOption {
        case .all:
            return true
        case .nearExpiry:
            guard let expiryDate = product.expiryDate else { return false }
            let calendar = Calendar.current
            let components = calendar.dateComponents([.day], from: Date(), to: expiryDate)
            return components.day ?? 0 <= 30 && components.day ?? 0 >= 0
        case .nearWarranty:
            guard let warrantyEndDate = product.warrantyEndDate else { return false }
            let calendar = Calendar.current
            let components = calendar.dateComponents([.day], from: Date(), to: warrantyEndDate)
            return components.day ?? 0 <= 30 && components.day ?? 0 >= 0
        case .lowUsage:
            // 根据产品使用次数判断
            return product.totalUsageCount < 5
        case .highWorth:
            return product.worthItIndex() >= 4
        case .lowWorth:
            return product.worthItIndex() <= 2
        case .loaned, .overdue:
            // 已借出及逾期产品
            return false
        }
    }
    
    // MARK: - 辅助方法
    func getProductsByStatus() -> [Product.ProductStatus: [Product]] {
        var result = [Product.ProductStatus: [Product]]()

        for status in Product.ProductStatus.allCases {
            result[status] = products.filter { $0.status == status }
        }

        return result
    }

    func getProductsByCategory() -> [Category: [Product]] {
        var result = [Category: [Product]]()

        for product in products {
            if let category = product.category {
                if result[category] == nil {
                    result[category] = []
                }
                result[category]?.append(product)
            }
        }

        return result
    }

    func getTotalProductsValue() -> Double {
        return products.reduce(0) { $0 + $1.price }
    }

    func getTotalProductsCount() -> Int {
        return products.count
    }

    // 获取最早的产品购买日期
    func getEarliestProductDate() -> Date? {
        let allProducts = repository.fetchAll()

        // 筛选出有购买日期的产品
        let productsWithDate = allProducts.compactMap { product -> Date? in
            return product.purchaseDate
        }

        // 返回最早的日期
        return productsWithDate.min()
    }

    // 重置高级筛选条件
    func resetAdvancedFilters() {
        advancedFilterEnabled = false
        advancedFilterCategories.removeAll()
        advancedFilterStatuses.removeAll()
        advancedFilterTags.removeAll()
        advancedFilterChannels.removeAll()

        dateFilterEnabled = false
        startDate = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
        endDate = Date()

        priceFilterEnabled = false
        minPrice = 0
        maxPrice = 10000

        satisfactionFilterEnabled = false
        minSatisfaction = 1
        maxSatisfaction = 5

        worthFilterEnabled = false
        minWorth = 0
        maxWorth = 100

        warrantyFilterEnabled = false
        warrantyStatus = .all

        usageFilterEnabled = false
        usageFrequency = .all

        // 重新加载产品
        loadProducts()
    }
}
