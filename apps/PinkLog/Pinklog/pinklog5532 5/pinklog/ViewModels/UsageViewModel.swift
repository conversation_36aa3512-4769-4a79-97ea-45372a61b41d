import Foundation
import CoreData
import SwiftUI
import Combine

class UsageViewModel: ObservableObject {
    // MARK: - 属性
    private let usageRepository: UsageRecordRepository
    private let expenseRepository: RelatedExpenseRepository

    @Published var currentProduct: Product?
    @Published var usageRecords: [UsageRecord] = []
    @Published var expenses: [RelatedExpense] = []
    @Published var allLoanRecords: [UsageRecord] = [] // 所有借出记录
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false

    // MARK: - 初始化
    init(usageRepository: UsageRecordRepository, expenseRepository: RelatedExpenseRepository) {
        self.usageRepository = usageRepository
        self.expenseRepository = expenseRepository

        // 初始化时加载所有借出记录
        loadAllLoanRecords()
    }

    // MARK: - 数据加载
    func setCurrentProduct(_ product: Product) {
        currentProduct = product
        loadProductData()
    }

    func loadProductData() {
        isLoading = true

        guard let product = currentProduct, let productId = product.id else {
            isLoading = false
            return
        }

        // 加载使用记录
        usageRecords = usageRepository.fetchByProduct(productId: productId)

        // 加载费用记录
        expenses = expenseRepository.fetchByProduct(productId: productId)

        isLoading = false
    }

    // 加载所有借出记录
    func loadAllLoanRecords() {
        // 创建一个获取所有借出记录的请求
        let request = NSFetchRequest<UsageRecord>(entityName: "UsageRecord")
        request.predicate = NSPredicate(format: "usageType == %@", "loaned")
        request.sortDescriptors = [NSSortDescriptor(key: "date", ascending: false)]

        do {
            let context = usageRepository.getContext()
            allLoanRecords = try context.fetch(request)
        } catch {
            print("加载所有借出记录失败: \(error)")
            allLoanRecords = []
        }
    }

    // MARK: - 使用记录操作
    func addUsageRecord(date: Date, satisfaction: Int16, notes: String?, duration: Double? = nil, scenario: String? = nil, usageType: String = "personal") -> Bool {
        guard let product = currentProduct else {
            errorMessage = "未选择产品"
            return false
        }

        let success = usageRepository.save { context in
            let newRecord = UsageRecord(context: context)
            newRecord.id = UUID()
            newRecord.date = date
            newRecord.satisfaction = satisfaction
            newRecord.notes = notes
            newRecord.duration = duration ?? 0
            newRecord.scenario = scenario
            newRecord.product = product
            newRecord.usageType = usageType
        }

        if success {
            loadProductData()
        }

        return success
    }

    // 保存物品故事记录（包含图片、音频等多媒体内容）
    func addStoryRecord(
        date: Date,
        title: String?,
        satisfaction: Int16,
        emotionalValue: Int16,
        memories: String?,
        notes: String?,
        scenario: String?,
        images: [UIImage]?,
        audioRecordings: [URL]?
    ) -> Bool {
        guard let product = currentProduct else {
            errorMessage = "未选择产品"
            return false
        }

        let success = usageRepository.save { context in
            let newRecord = UsageRecord(context: context)
            newRecord.id = UUID()
            newRecord.date = date
            newRecord.title = title
            newRecord.satisfaction = satisfaction
            newRecord.emotionalValue = emotionalValue
            newRecord.memories = memories
            newRecord.notes = notes
            newRecord.scenario = scenario
            newRecord.product = product
            newRecord.usageType = "personal"
            newRecord.isStory = true

            // 处理图片 - 压缩并保存图片
            if let images = images, !images.isEmpty {
                var compressedImageDatas: [Data] = []

                // 处理每张图片
                for image in images {
                    // 使用ImageManager压缩图片
                    let compressedImage = ImageManager.shared.compressImage(image, maxSize: 1024, quality: 0.7)
                    if let imageData = compressedImage.jpegData(compressionQuality: 0.7) {
                        compressedImageDatas.append(imageData)
                    }
                }

                // 将图片数据数组存储到记录中
                if !compressedImageDatas.isEmpty {
                    do {
                        let imageDataArray = try NSKeyedArchiver.archivedData(withRootObject: compressedImageDatas, requiringSecureCoding: false)
                        newRecord.images = imageDataArray
                    } catch {
                        print("保存图片数据失败: \(error)")
                    }
                }
            }

            // 处理音频 - 只存储文件名，不存储完整路径
            if let audioRecordings = audioRecordings, !audioRecordings.isEmpty {
                do {
                    // 提取所有文件名
                    let fileNames: [String] = audioRecordings.map { url in
                        let fileName = url.lastPathComponent
                        print("📦 [UsageViewModel] 存储音频文件名: \(fileName)")
                        return fileName
                    }

                    // 存储文件名数组（而不是URL数组）
                    let audioData = try NSKeyedArchiver.archivedData(withRootObject: fileNames, requiringSecureCoding: false)
                    newRecord.audioRecordings = audioData
                    
                    print("📦 [UsageViewModel] ✅ 已存储 \(fileNames.count) 个音频文件名")
                } catch {
                    print("📦 [UsageViewModel] ❌ 保存音频记录失败: \(error)")
                }
            }
        }

        if success {
            loadProductData()
        }

        return success
    }

    // 添加借出记录
    func addLoanRecord(date: Date, borrowerName: String, contactInfo: String?, dueDate: Date, notes: String?) -> Bool {
        guard let product = currentProduct else {
            errorMessage = "未选择产品"
            return false
        }

        // 创建一个新的 UUID，以便在保存后找到这条记录
        let newRecordId = UUID()

        let success = usageRepository.save { context in
            let newRecord = UsageRecord(context: context)
            newRecord.id = newRecordId
            newRecord.date = date
            newRecord.borrowerName = borrowerName
            newRecord.contactInfo = contactInfo
            newRecord.dueDate = dueDate
            newRecord.notes = notes
            newRecord.product = product
            newRecord.usageType = "loaned"
            newRecord.isLoanedOut = true
            newRecord.satisfaction = 3 // 默认中等满意度
        }

        if success {
            loadProductData()
            loadAllLoanRecords() // 刷新所有借出记录

            // 设置提醒
            if let record = usageRecords.first(where: { $0.id == newRecordId }) {
                scheduleLoanDueReminder(for: record)
            }
        }

        return success
    }

    func deleteUsageRecord(_ record: UsageRecord) -> Bool {
        let success = usageRepository.delete(entity: record)
        if success {
            loadProductData()
        }
        return success
    }

    func updateUsageRecord(record: UsageRecord, date: Date, satisfaction: Int16, notes: String?, duration: Double? = nil, scenario: String? = nil, wearCondition: String? = nil, usageType: String? = nil) -> Bool {
        guard let product = currentProduct else {
            errorMessage = "未选择产品"
            return false
        }

        let success = usageRepository.save { context in
            // 更新现有记录而不是创建新记录
            record.date = date
            record.satisfaction = satisfaction
            record.notes = notes
            record.duration = duration ?? 0
            record.scenario = scenario
            record.wearCondition = wearCondition

            if let usageType = usageType {
                record.usageType = usageType
            }
        }

        if success {
            loadProductData()
        }

        return success
    }

    // 更新借出记录
    func updateLoanRecord(record: UsageRecord, date: Date, borrowerName: String, contactInfo: String?, dueDate: Date, notes: String?) -> Bool {
        guard record.usageType == "loaned" else {
            errorMessage = "不是借出记录"
            return false
        }

        let success = usageRepository.save { context in
            record.date = date
            record.borrowerName = borrowerName
            record.contactInfo = contactInfo
            record.dueDate = dueDate
            record.notes = notes
        }

        if success {
            loadProductData()
            loadAllLoanRecords() // 刷新所有借出记录

            // 更新提醒
            if let id = record.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
                scheduleLoanDueReminder(for: record)
            }
        }

        return success
    }

    // 标记借出记录为已归还
    func markAsReturned(record: UsageRecord, returnDate: Date = Date()) -> Bool {
        guard record.usageType == "loaned" else {
            errorMessage = "不是借出记录"
            return false
        }

        let success = usageRepository.save { context in
            record.returnDate = returnDate
            record.isLoanedOut = false
        }

        if success {
            loadProductData()
            loadAllLoanRecords() // 刷新所有借出记录

            // 取消提醒
            if let id = record.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
            }
        }

        return success
    }

    // 添加转让记录
    func addTransferRecord(date: Date, transferType: String, recipient: String?, price: Double, notes: String?) -> Bool {
        guard currentProduct != nil else {
            errorMessage = "未选择产品"
            return false
        }

        let newRecordId = UUID()

        let success = usageRepository.save { [self] context in
            let newRecord = UsageRecord(context: context)
            newRecord.id = newRecordId
            newRecord.date = date
            newRecord.usageType = "transfer"
            newRecord.transferTypeString = transferType // "sold"（出售）或 "gifted"（赠送）
            newRecord.recipient = recipient
            newRecord.transferPrice = price
            newRecord.notes = notes
            newRecord.product = self.currentProduct
            newRecord.satisfaction = 3 // 默认中等满意度
        }

        if success {
            loadProductData()
        }

        return success
    }

    // 更新转让记录
    func updateTransferRecord(record: UsageRecord, date: Date, transferType: String, recipient: String?, price: Double, notes: String?) -> Bool {
        guard record.usageType == "transfer" else {
            errorMessage = "不是转让记录"
            return false
        }

        let success = usageRepository.save { context in
            record.date = date
            record.transferTypeString = transferType
            record.recipient = recipient
            record.transferPrice = price
            record.notes = notes
        }

        if success {
            loadProductData()
        }

        return success
    }

    // MARK: - 费用记录操作
    func addExpense(amount: Double, date: Date, expenseType: ExpenseType, notes: String? = nil) -> Bool {
        guard let product = currentProduct else {
            errorMessage = "未选择产品"
            return false
        }

        let success = expenseRepository.save { [product] context in
            let newExpense = RelatedExpense(context: context)
            newExpense.id = UUID()
            newExpense.amount = amount
            newExpense.date = date
            newExpense.notes = notes
            newExpense.product = product
            newExpense.type = expenseType
        }

        if success {
            loadProductData()
        }

        return success
    }

    func deleteExpense(_ expense: RelatedExpense) -> Bool {
        let success = expenseRepository.delete(entity: expense)
        if success {
            loadProductData()
        }
        return success
    }

    func updateExpense(expense: RelatedExpense, amount: Double, date: Date, type: ExpenseType?, notes: String?) -> Bool {
        let success = expenseRepository.save { context in
            expense.amount = amount
            expense.date = date
            expense.type = type
            expense.notes = notes
        }

        if success {
            loadProductData()
        }

        return success
    }

    // MARK: - 借阅提醒
    private func scheduleLoanDueReminder(for record: UsageRecord) {
        guard let product = record.product, let recordId = record.id, let dueDate = record.dueDate else { return }

        // 只为未归还的借阅设置提醒
        if record.usageType == "loaned" && record.returnDate == nil {
            NotificationManager.shared.scheduleLoanDueReminder(for: product, recordId: recordId, dueDate: dueDate, borrowerName: record.borrowerName ?? "某人")
        }
    }

    // 获取活跃的借出记录（未归还）
    func getActiveLoans() -> [UsageRecord] {
        return usageRecords.filter {
            $0.usageType == "loaned" && $0.returnDate == nil
        }
    }

    // 获取逾期的借出记录
    func getOverdueLoans() -> [UsageRecord] {
        return usageRecords.filter {
            $0.usageType == "loaned" && $0.returnDate == nil && $0.isOverdue
        }
    }

    // 获取即将到期的借出记录（7天内）
    func getNearDueLoans() -> [UsageRecord] {
        return usageRecords.filter {
            $0.usageType == "loaned" && $0.returnDate == nil && $0.isNearDueDate
        }
    }

    // 获取已归还的借出记录
    func getReturnedLoans() -> [UsageRecord] {
        return usageRecords.filter {
            $0.usageType == "loaned" && $0.returnDate != nil
        }
    }

    // MARK: - 数据分析
    func getUsageByMonth() -> [Date: Int] {
        var result = [Date: Int]()
        let calendar = Calendar.current

        for record in usageRecords {
            guard let date = record.date else { continue }

            // 只统计个人使用记录
            if record.usageType == "personal" {
                // 获取年月
                let components = calendar.dateComponents([.year, .month], from: date)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    result[monthDate, default: 0] += 1
                }
            }
        }

        return result
    }

    // 获取借出记录按月统计
    func getLoansByMonth() -> [Date: Int] {
        var result = [Date: Int]()
        let calendar = Calendar.current

        for record in usageRecords {
            guard let date = record.date else { continue }

            // 只统计借出记录
            if record.usageType == "loaned" {
                // 获取年月
                let components = calendar.dateComponents([.year, .month], from: date)
                if let year = components.year, let month = components.month,
                   let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                    result[monthDate, default: 0] += 1
                }
            }
        }

        return result
    }

    func getExpensesByType() -> [String: Double] {
        var result = [String: Double]()

        for expense in expenses {
            let typeName = expense.type?.name ?? "其他"
            result[typeName, default: 0] += expense.amount
        }

        return result
    }

    func getExpensesByMonth() -> [Date: Double] {
        var result = [Date: Double]()
        let calendar = Calendar.current

        for expense in expenses {
            guard let date = expense.date else { continue }

            // 获取年月
            let components = calendar.dateComponents([.year, .month], from: date)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                result[monthDate, default: 0] += expense.amount
            }
        }

        return result
    }

    func getSatisfactionTrend() -> [Date: Double] {
        var result = [Date: (total: Double, count: Int)]()
        let calendar = Calendar.current

        for record in usageRecords {
            guard let date = record.date else { continue }

            // 获取年月
            let components = calendar.dateComponents([.year, .month], from: date)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                let current = result[monthDate, default: (0, 0)]
                result[monthDate] = (current.total + Double(record.satisfaction), current.count + 1)
            }
        }

        // 计算每月平均满意度
        return result.mapValues { $0.total / Double($0.count) }
    }

    func getCostPerUseOverTime() -> [Date: Double] {
        guard let product = currentProduct else { return [:] }

        var result = [Date: Double]()
        let calendar = Calendar.current

        // 按月统计使用次数和费用
        var usageByMonth = [Date: Int]()
        var expenseByMonth = [Date: Double]()

        // 初始购买费用
        if let purchaseDate = product.purchaseDate {
            let components = calendar.dateComponents([.year, .month], from: purchaseDate)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                expenseByMonth[monthDate, default: 0] += product.price
            }
        }

        // 使用记录
        for record in usageRecords {
            guard let date = record.date else { continue }

            let components = calendar.dateComponents([.year, .month], from: date)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                usageByMonth[monthDate, default: 0] += 1
            }
        }

        // 费用记录
        for expense in expenses {
            guard let date = expense.date else { continue }

            let components = calendar.dateComponents([.year, .month], from: date)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                expenseByMonth[monthDate, default: 0] += expense.amount
            }
        }

        // 计算累计使用次数和累计费用
        var totalUsage = 0
        var totalExpense = 0.0

        // 获取所有月份并排序
        let allMonths = Set(usageByMonth.keys).union(expenseByMonth.keys).sorted()

        for month in allMonths {
            totalUsage += usageByMonth[month, default: 0]
            totalExpense += expenseByMonth[month, default: 0]

            if totalUsage > 0 {
                result[month] = totalExpense / Double(totalUsage)
            } else {
                result[month] = totalExpense
            }
        }

        return result
    }

    // MARK: - 统计方法

    // 获取所有使用记录的总数
    func getTotalUsageRecordsCount() -> Int {
        // 创建一个获取所有使用记录的请求
        let request = NSFetchRequest<UsageRecord>(entityName: "UsageRecord")

        do {
            let context = usageRepository.getContext()
            let count = try context.count(for: request)
            return count
        } catch {
            print("获取使用记录总数失败: \(error)")
            return 0
        }
    }

    // MARK: - 多媒体内容处理

    // 从UsageRecord中获取图片数据
    func getImageDatasFromRecord(_ record: UsageRecord) -> [Data] {
        guard let imageData = record.images else { return [] }

        do {
            // 尝试解码为新格式（Data数组）
            if let imageDatas = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imageData) as? [Data] {
                return imageDatas
            }

            // 尝试解码为旧格式（UIImage数组）
            let unarchiver = try NSKeyedUnarchiver(forReadingFrom: imageData)
            unarchiver.requiresSecureCoding = false
            if let images = unarchiver.decodeObject(forKey: "images") as? [UIImage] {
                // 将旧格式转换为新格式
                var imageDatas: [Data] = []
                for image in images {
                    // 压缩图片并转换为数据
                    let compressedImage = ImageManager.shared.compressImage(image)
                    if let data = compressedImage.jpegData(compressionQuality: 0.7) {
                        imageDatas.append(data)
                    }
                }
                return imageDatas
            }
        } catch {
            print("解码图片数据失败: \(error)")
        }

        return []
    }

    // 从UsageRecord中获取图片（兼容旧方法）
    func getImagesFromRecord(_ record: UsageRecord) -> [UIImage] {
        let imageDatas = getImageDatasFromRecord(record)
        var images: [UIImage] = []

        for data in imageDatas {
            if let image = UIImage(data: data) {
                images.append(image)
            }
        }

        return images
    }

    // 从UsageRecord中获取音频URL
    func getAudioRecordingsFromRecord(_ record: UsageRecord) -> [URL] {
        guard let audioData = record.audioRecordings else { return [] }

        do {
            if let originalUrls = try NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [URL] {
                // 创建一个新的URL数组，用于存储可能更新的URL
                var updatedUrls: [URL] = []

                for originalUrl in originalUrls {
                    // 检查原始URL是否存在
                    if FileManager.default.fileExists(atPath: originalUrl.path) {
                        // 如果文件存在，直接使用原始URL
                        updatedUrls.append(originalUrl)
                        print("找到原始音频文件: \(originalUrl.path)")
                    } else {
                        // 如果文件不存在，尝试在当前应用的Documents目录中查找同名文件
                        let fileName = originalUrl.lastPathComponent
                        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
                        let newUrl = documentsDirectory.appendingPathComponent(fileName)

                        if FileManager.default.fileExists(atPath: newUrl.path) {
                            // 如果找到同名文件，使用新的URL
                            updatedUrls.append(newUrl)
                            print("找到同名音频文件: \(newUrl.path)")
                        } else {
                            // 如果仍然找不到，记录错误并继续
                            print("无法找到音频文件: \(originalUrl.path) 或 \(newUrl.path)")

                            // 尝试在Documents目录中查找所有.m4a文件
                            do {
                                let fileURLs = try FileManager.default.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: nil)
                                let audioFiles = fileURLs.filter { $0.pathExtension == "m4a" }
                                print("当前Documents目录中的音频文件: \(audioFiles.map { $0.lastPathComponent })")
                            } catch {
                                print("无法列出Documents目录中的文件: \(error)")
                            }
                        }
                    }
                }

                return updatedUrls
            }
        } catch {
            print("解码音频数据失败: \(error)")
        }

        return []
    }
}
