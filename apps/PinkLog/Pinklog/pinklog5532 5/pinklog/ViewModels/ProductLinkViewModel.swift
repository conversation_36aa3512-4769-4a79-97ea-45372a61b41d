import Foundation
import CoreData
import SwiftUI
import Combine

/// 管理产品关联关系的视图模型
class ProductLinkViewModel: ObservableObject {
    // MARK: - 属性
    
    private let context: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    
    @Published var links: [ProductLink] = []
    @Published var outgoingLinks: [ProductLink] = []
    @Published var incomingLinks: [ProductLink] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    @Published var currentProduct: Product?
    
    // MARK: - 初始化
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - 公共方法
    
    /// 设置当前产品并加载其关联关系
    func setCurrentProduct(_ product: Product) {
        // 检查是否需要加载不同的产品
        if currentProduct?.id != product.id {
            currentProduct = product
            // 清除之前的链接数据
            links = []
            outgoingLinks = []
            incomingLinks = []
            // 立即开始加载
            isLoading = true
            // 在后台线程中加载链接
            DispatchQueue.global(qos: .userInitiated).async {
                // 在主线程中更新UI
                DispatchQueue.main.async {
                    self.loadLinks(for: product)
                }
            }
        } else if links.isEmpty {
            // 如果是同一个产品但链接为空，也进行加载
            loadLinks(for: product)
        }
    }
    
    /// 加载指定产品的所有关联关系
    func loadLinks(for product: Product) {
        isLoading = true
        errorMessage = nil
        
        // 准备获取请求
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        request.predicate = NSPredicate(format: "sourceProduct == %@ OR targetProduct == %@", product, product)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ProductLink.createdAt, ascending: false)]
        
        // 确保加载产品关系
        request.relationshipKeyPathsForPrefetching = ["sourceProduct", "targetProduct"]
        
        // 执行请求
        do {
            let allLinks = try context.fetch(request)
            
            // 确保所有链接的源产品和目标产品都已加载
            for link in allLinks {
                // 触发关系的懒加载
                _ = link.sourceProduct?.id
                _ = link.targetProduct?.id
            }
            
            // 分类链接
            outgoingLinks = allLinks.filter { $0.sourceProduct == product }
            // 包含所有指向当前产品的链接，无论是否双向
            incomingLinks = allLinks.filter { $0.targetProduct == product }
            
            // 使用Set来合并出站和入站链接，它会自动处理重复的链接实例
            let uniqueLinksSet = Set(allLinks)
            
            // 设置最终的链接列表，并按创建日期排序
            // 注意：原始的 fetch request 已经排序，但Set操作不保证顺序，所以需要重新排序
            links = Array(uniqueLinksSet).sorted(by: { $0.createdAt ?? Date() > $1.createdAt ?? Date() })
            
            isLoading = false
        } catch {
            errorMessage = "加载关联关系失败: \(error.localizedDescription)"
            isLoading = false
        }
        
        // 如果有旧的关联关系，迁移它们
        migrateOldRelationships(for: product)
    }
    
    /// 检查具有相同名称和产品组合的关联组是否已存在
    func checkIfGroupExists(product: Product, groupName: String, products: [Product]) -> Bool {
        // 如果组名为空，则不进行检查（因为这不构成一个"组"）
        if groupName.isEmpty {
            return false
        }
        
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        
        // 筛选出具有相同组名和源产品的链接
        request.predicate = NSPredicate(format: "groupName == %@ AND sourceProduct == %@", groupName, product)
        
        do {
            let links = try context.fetch(request)
            
            // 如果链接数量与所选产品数量不匹配，则它们不是同一个组
            guard links.count == products.count else {
                return false
            }
            
            // 获取所有目标产品的ID
            let existingProductIDs = Set(links.compactMap { $0.targetProduct?.id })
            let selectedProductIDs = Set(products.map { $0.id })
            
            // 如果ID集合完全相同，则认为该组已存在
            return existingProductIDs == selectedProductIDs
            
        } catch {
            print("检查关联组是否存在时出错: \(error)")
            return false // 出错时，为安全起见，假定不存在
        }
    }
    
    /// 创建新的产品关联关系
    func createLink(sourceProduct: Product, targetProduct: Product, 
                   relationshipType: ProductRelationshipType, 
                   notes: String? = nil, 
                   strength: Int16 = 3, 
                   isBidirectional: Bool = false,
                   groupName: String? = nil,
                   groupID: String? = nil) -> Bool {
        
        // 检查产品是否相同
        guard sourceProduct.id != targetProduct.id else {
            errorMessage = "产品不能与自身关联"
            return false
        }
        
        // 注意：移除了之前的 existsLink 检查，以允许在不同组中存在相同的链接。
        // 重复组的检查将在视图模型或视图层进行。
        
        // 刷新上下文，确保我们使用的是最新的产品对象
        context.refresh(sourceProduct, mergeChanges: true)
        context.refresh(targetProduct, mergeChanges: true)
        
        // 默认使用关系类型的建议双向设置
        let shouldBeBidirectional = isBidirectional || relationshipType.isBidirectionalByDefault
        
        // 创建新的产品关联
        let link = ProductLink.create(
            sourceProduct: sourceProduct,
            targetProduct: targetProduct,
            relationshipType: relationshipType,
            notes: notes,
            strength: strength,
            isBidirectional: shouldBeBidirectional,
            groupName: groupName,
            groupID: groupID,
            context: context
        )
        
        // 如果是双向关系，则创建反向链接
        if shouldBeBidirectional {
            // 确定反向关系类型
            let reverseType = relationshipType.reverseRelationshipType ?? relationshipType
            
            // 检查反向链接是否已存在
            let reverseRequest: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
            reverseRequest.predicate = NSPredicate(format: "sourceProduct == %@ AND targetProduct == %@", 
                                                 targetProduct, sourceProduct)
            reverseRequest.fetchLimit = 1
            
            let reverseExists = ((try? context.count(for: reverseRequest)) ?? 0) > 0
            
            // 只有在反向链接不存在时才创建
            if !reverseExists {
                // 创建反向链接，并标记为双向以保证两端都能识别
                let reverseLink = ProductLink.create(
                    sourceProduct: targetProduct,
                    targetProduct: sourceProduct,
                    relationshipType: reverseType,
                    notes: notes,
                    strength: strength,
                    isBidirectional: true,  // 标记为双向，确保两端都能识别
                    groupName: groupName,
                    groupID: groupID,
                    context: context
                )
            }
        }
        
        // 保存上下文
        do {
            try context.save()
            
            // 如果当前产品是源产品或目标产品，重新加载链接
            if let current = currentProduct, current.id == sourceProduct.id || current.id == targetProduct.id {
                // 延迟时间增加，确保数据库操作完全完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    // 强制刷新产品
                    if let context = current.managedObjectContext {
                        context.refresh(current, mergeChanges: true)
                    }
                    // 重新加载链接
                    self.loadLinks(for: current)
                }
            }
            
            return true
        } catch {
            errorMessage = "创建关联关系失败: \(error.localizedDescription)"
            return false
        }
    }
    
    /// 更新现有的产品关联关系
    func updateLink(_ link: ProductLink, 
                   relationshipType: ProductRelationshipType, 
                   notes: String? = nil, 
                   strength: Int16? = nil, 
                   isBidirectional: Bool? = nil) -> Bool {
        
        // 更新关系类型
        link.relationshipTypeEnum = relationshipType
        
        // 更新笔记（如果提供）
        if let notes = notes {
            link.notes = notes
        }
        
        // 更新强度（如果提供）
        if let strength = strength {
            link.strength = strength
        }
        
        // 更新双向状态（如果提供）
        let oldBidirectional = link.isBidirectional
        if let newBidirectional = isBidirectional {
            link.isBidirectional = newBidirectional
            
            // 如果双向状态从true变为false，需要删除反向链接
            if oldBidirectional && !newBidirectional {
                if let reverseLink = link.getReverseLink() {
                    reverseLink.delete()
                }
            }
            
            // 如果双向状态从false变为true，需要创建反向链接
            if !oldBidirectional && newBidirectional, 
               let sourceProduct = link.sourceProduct,
               let targetProduct = link.targetProduct,
               let reverseType = relationshipType.reverseRelationshipType {
                
                // 创建反向链接
                let reverseLink = ProductLink.create(
                    sourceProduct: targetProduct,
                    targetProduct: sourceProduct,
                    relationshipType: reverseType,
                    notes: notes,
                    strength: strength ?? link.strength,
                    isBidirectional: false,
                    context: context
                )
            }
        }
        
        // 保存上下文
        do {
            try context.save()
            
            // 如果当前产品是该链接的源产品或目标产品，重新加载链接
            if let current = currentProduct, 
               let source = link.sourceProduct, 
               let target = link.targetProduct,
               current.id == source.id || current.id == target.id {
                loadLinks(for: current)
            }
            
            return true
        } catch {
            errorMessage = "更新关联关系失败: \(error.localizedDescription)"
            return false
        }
    }
    
    /// 记录两个产品一起使用
    func recordJointUsage(link: ProductLink) {
        // 确保我们使用的是上下文中的对象而不是分离的对象
        let managedLink: ProductLink
        
        if let linkID = link.id {
            let fetchRequest: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %@", linkID as CVarArg)
            fetchRequest.fetchLimit = 1
            
            do {
                if let fetchedLink = try context.fetch(fetchRequest).first {
                    managedLink = fetchedLink
                } else {
                    managedLink = link
                }
            } catch {
                managedLink = link
                print("获取链接失败: \(error.localizedDescription)")
            }
        } else {
            managedLink = link
        }
        
        managedLink.incrementUsageCount()
        
        // 如果是双向关系，也更新反向链接
        if managedLink.isBidirectional, let reverseLink = managedLink.getReverseLink() {
            reverseLink.incrementUsageCount()
        }
        
        // 保存上下文
        do {
            try context.save()
            
            // 如果当前产品是该链接的源产品或目标产品，重新加载链接
            if let current = currentProduct, 
               let source = managedLink.sourceProduct, 
               let target = managedLink.targetProduct,
               current.id == source.id || current.id == target.id {
                loadLinks(for: current)
            }
        } catch {
            errorMessage = "记录共同使用失败: \(error.localizedDescription)"
        }
    }
    
    /// 删除产品关联关系
    func deleteLink(_ link: ProductLink) -> Bool {
        // 检查链接是否有效
        guard link.id != nil, link.managedObjectContext != nil else {
            errorMessage = "无法删除：无效的关联关系"
            return false
        }
        
        // 安全获取源产品和目标产品ID（用于后续刷新）
        let sourceID = link.sourceProduct?.id
        let targetID = link.targetProduct?.id
        
        // 如果是双向关系，也删除反向链接
        if link.isBidirectional {
            if let reverseLink = link.getReverseLink() {
                do {
                    reverseLink.delete()
                } catch {
                    print("删除反向链接时出错: \(error)")
                    // 继续删除主链接
                }
            }
        }
        
        // 删除当前链接
        do {
            link.delete()
        } catch {
            print("删除链接时出错: \(error)")
            errorMessage = "删除关联关系失败: \(error.localizedDescription)"
            return false
        }
        
        // 保存上下文
        do {
            try context.save()
            
            // 如果当前产品是该链接的源产品或目标产品，重新加载链接
            if let current = currentProduct, let currentID = current.id,
               (sourceID != nil && currentID == sourceID) || 
               (targetID != nil && currentID == targetID) {
                loadLinks(for: current)
            }
            
            return true
        } catch {
            errorMessage = "删除关联关系失败: \(error.localizedDescription)"
            return false
        }
    }
    
    /// 获取特定类型的链接
    func getLinks(ofType type: ProductRelationshipType) -> [ProductLink] {
        return links.filter { $0.relationshipTypeEnum == type }
    }
    
    // 获取两个产品间的关联关系
    func getLink(between product1: Product, and product2: Product) -> ProductLink? {
        let link = ProductLink.findLink(between: product1, and: product2, context: context)
        
        // 确保链接的产品关系已加载
        if let link = link {
            _ = link.sourceProduct?.id
            _ = link.targetProduct?.id
        }
        
        return link
    }
    
    // 刷新所有与特定产品相关的链接
    func refreshLinks(for product: Product) {
        // 确保上下文中有最新的产品状态
        context.refresh(product, mergeChanges: true)
        // 重新加载链接
        loadLinks(for: product)
    }
    
    // MARK: - 数据迁移
    
    /// 将旧的 relatedProducts 关系迁移到新的 ProductLink 模型
    private func migrateOldRelationships(for product: Product) {
        guard let relatedProducts = product.relatedProducts?.allObjects as? [Product], !relatedProducts.isEmpty else {
            return
        }
        
        var didMigrateAny = false
        
        // 检查这些产品是否已经有对应的 ProductLink
        for relatedProduct in relatedProducts {
            if !ProductLink.existsLink(sourceProduct: product, targetProduct: relatedProduct, context: context) {
                // 获取旧的关系类型（从UserDefaults）
                let key = "relationship_\(product.id?.uuidString ?? "")_\(relatedProduct.id?.uuidString ?? "")"
                let typeString = UserDefaults.standard.string(forKey: key) ?? "other"
                let relationshipType = ProductRelationshipType(rawValue: typeString) ?? .other
                
                // 创建新的链接
                if createLink(sourceProduct: product, 
                              targetProduct: relatedProduct, 
                              relationshipType: relationshipType) {
                    didMigrateAny = true
                }
                
                // 清除旧的UserDefaults数据
                UserDefaults.standard.removeObject(forKey: key)
            }
        }
        
        // 只有在实际进行了迁移后才刷新链接列表
        if didMigrateAny {
            loadLinks(for: product)
        }
    }
}