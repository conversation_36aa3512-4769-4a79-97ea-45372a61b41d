//
//  ConsumableViewModel.swift
//  pinklog
//
//  Created by Assistant on 2024/12/19.
//

import Foundation
import CoreData
import SwiftUI
import Combine

@MainActor
class ConsumableViewModel: ObservableObject {
    @Published var consumableProducts: [Product] = []
    @Published var lowStockProducts: [Product] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 按生命周期状态分类的产品
    @Published var activeProducts: [Product] = []
    @Published var lowStockProductsLifecycle: [Product] = []
    @Published var criticalProducts: [Product] = []
    @Published var depletedProducts: [Product] = []
    @Published var expiredProducts: [Product] = []
    @Published var discontinuedProducts: [Product] = []
    @Published var archivedProducts: [Product] = []
    
    private let repository: ProductRepository
    private let usageRepository: UsageRecordRepository
    private let autoArchiveService: ConsumableAutoArchiveService
    private var cancellables = Set<AnyCancellable>()
    
    init(repository: ProductRepository = ProductRepository.shared,
         usageRepository: UsageRecordRepository = UsageRecordRepository.shared) {
        self.repository = repository
        self.usageRepository = usageRepository
        self.autoArchiveService = ConsumableAutoArchiveService()
        
        setupSubscriptions()
        loadConsumableProducts()
        
        // 自动归档服务已在初始化时启动定期检查
    }
    
    private func setupSubscriptions() {
        // 监听产品变化
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                Task {
                    await self?.loadConsumableProducts()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 数据加载
    
    func loadConsumableProducts() {
        isLoading = true
        errorMessage = nil
        
        let allProducts = repository.fetchAllProducts()
        
        // 筛选消耗品
        let consumables = allProducts.filter { $0.isConsumable == true }
        
        // 筛选低库存产品（保持向后兼容）
        let lowStock = consumables.filter { $0.needsStockAlert }
        
        // 按生命周期状态分类
        categorizeProductsByLifecycleStatus(consumables)
        
        self.consumableProducts = consumables.sorted { product1, product2 in
            // 按生命周期状态优先级排序
            if product1.lifecycleStatus.priority != product2.lifecycleStatus.priority {
                return product1.lifecycleStatus.priority < product2.lifecycleStatus.priority
            }
            // 然后按剩余量百分比排序
            return product1.stockPercentage < product2.stockPercentage
        }
        
        self.lowStockProducts = lowStock.sorted { $0.stockPercentage < $1.stockPercentage }
        self.isLoading = false
    }
    
    // MARK: - 生命周期状态分类
    
    private func categorizeProductsByLifecycleStatus(_ products: [Product]) {
        activeProducts = products.filter { $0.lifecycleStatus == .active }
        lowStockProductsLifecycle = products.filter { $0.lifecycleStatus == .lowStock }
        criticalProducts = products.filter { $0.lifecycleStatus == .critical }
        depletedProducts = products.filter { $0.lifecycleStatus == .depleted }
        expiredProducts = products.filter { $0.lifecycleStatus == .expired }
        discontinuedProducts = products.filter { $0.lifecycleStatus == .discontinued }
        archivedProducts = products.filter { $0.lifecycleStatus == .archived }
    }
    
    // 获取指定状态的产品数量
    func getProductCount(for status: Product.LifecycleStatus) -> Int {
        switch status {
        case .active: return activeProducts.count
        case .lowStock: return lowStockProductsLifecycle.count
        case .critical: return criticalProducts.count
        case .depleted: return depletedProducts.count
        case .expired: return expiredProducts.count
        case .discontinued: return discontinuedProducts.count
        case .archived: return archivedProducts.count
        }
    }
    
    // 获取指定状态的产品列表
    func getProducts(for status: Product.LifecycleStatus) -> [Product] {
        switch status {
        case .active: return activeProducts
        case .lowStock: return lowStockProductsLifecycle
        case .critical: return criticalProducts
        case .depleted: return depletedProducts
        case .expired: return expiredProducts
        case .discontinued: return discontinuedProducts
        case .archived: return archivedProducts
        }
    }
    
    // MARK: - 消耗品操作
    
    /// 记录消耗
    func recordConsumption(
        for product: Product,
        consumedQuantity: Double,
        unit: UsageRecord.ConsumptionUnit,
        satisfaction: Int16 = 5,
        notes: String = "",
        scenario: String = ""
    ) async throws {
        guard product.isConsumable == true else {
            throw ConsumableError.notConsumable
        }

        // 验证库存是否充足（考虑双单位系统）
        let availableQuantity: Double = {
            if product.usesDualUnitSystem && unit.rawValue == product.consumptionUnit {
                // 如果使用消耗单位，使用转换后的库存量
                return product.currentQuantityInConsumptionUnit
            } else {
                // 使用购买单位的库存量
                return product.actualCurrentQuantity
            }
        }()

        guard availableQuantity >= consumedQuantity else {
            throw ConsumableError.insufficientStock
        }

        // 计算消耗后的剩余量（用于记录）
        let newRemainingQuantity = availableQuantity - consumedQuantity

        // 创建使用记录
        let usageRecord = try await usageRepository.createUsageRecord(
            for: product,
            date: Date(),
            satisfaction: satisfaction,
            notes: notes,
            scenario: scenario
        )

        // 更新消耗信息
        usageRecord.updateConsumption(
            consumed: consumedQuantity,
            remaining: newRemainingQuantity,
            unit: unit
        )

        // 注意：不再手动更新product.currentQuantity
        // 库存将通过actualCurrentQuantity计算属性自动计算
        // 该属性基于初始数量减去所有消耗记录的总和

        // 保存更改
        let success = repository.save { _ in }
        if !success {
            throw ConsumableError.saveFailed
        }

        // 检查生命周期状态更新
        checkAndUpdateLifecycleStatus(for: product)

        // 检查是否需要发送低库存通知
        if product.needsStockAlert {
            scheduleStockAlert(for: product)
        }

        // 重新加载数据
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    // MARK: - 生命周期状态管理
    
    func discontinueProduct(_ product: Product) {
        product.discontinue()
        repository.save { _ in }
        loadConsumableProducts()
    }
    
    func reactivateProduct(_ product: Product) {
        product.reactivate()
        repository.save { _ in }
        loadConsumableProducts()
    }
    
    func archiveProduct(_ product: Product) {
        product.archive()
        repository.save { _ in }
        loadConsumableProducts()
    }
    
    func unarchiveProduct(_ product: Product) {
        product.unarchive()
        repository.save { _ in }
        loadConsumableProducts()
    }
    
    func checkAndUpdateLifecycleStatus(for product: Product) {
        if product.checkLifecycleStatusUpdate() {
            repository.save { _ in }
        }
    }
    
    func getLifecycleStatusSuggestions(for product: Product) -> [LifecycleStatusSuggestion] {
        return product.getLifecycleStatusSuggestions()
    }
    
    // 批量检查所有产品的生命周期状态
    func checkAllProductsLifecycleStatus() {
        let allProducts = repository.fetchAllProducts()
        let consumables = allProducts.filter { $0.isConsumable == true }
        
        var hasUpdates = false
        for product in consumables {
            if product.checkLifecycleStatusUpdate() {
                repository.save { _ in }
                hasUpdates = true
            }
        }
        
        if hasUpdates {
            loadConsumableProducts()
        }
    }
    
    /// 补充库存
    func replenishStock(
        for product: Product,
        addedQuantity: Double,
        notes: String = "库存补充"
    ) async throws {
        guard product.isConsumable == true else {
            throw ConsumableError.notConsumable
        }

        guard addedQuantity > 0 else {
            throw ConsumableError.invalidQuantity
        }

        // 获取当前库存（用于记录）
        let currentQuantity = product.actualCurrentQuantity
        let newQuantity = currentQuantity + addedQuantity

        // 创建补充记录
        let usageRecord = try await usageRepository.createUsageRecord(
            for: product,
            date: Date(),
            satisfaction: 5,
            notes: notes,
            scenario: "库存补充"
        )

        // 记录补充信息（使用负数表示补充）
        usageRecord.consumedQuantity = -addedQuantity // 负数表示补充
        usageRecord.remainingQuantity = newQuantity
        usageRecord.consumptionUnit = product.unitType

        // 注意：不再手动更新product.currentQuantity
        // 库存将通过actualCurrentQuantity计算属性自动计算
        // 该属性基于初始数量减去所有消耗记录的总和（包括负数的补充记录）

        // 保存更改
        let success = repository.save { _ in }
        if !success {
            throw ConsumableError.saveFailed
        }

        // 重新加载数据
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    /// 设置产品为消耗品
    func setAsConsumable(
        product: Product,
        currentQuantity: Double,
        unitType: String,
        minStockAlert: Double? = nil,
        consumptionRate: Double? = nil
    ) async throws {
        product.isConsumable = true

        // 设置初始库存量到quantity字段（这是actualCurrentQuantity计算的基础）
        product.quantity = Int16(currentQuantity)

        // 为了保持数据一致性，也设置currentQuantity字段
        // 但实际显示将使用actualCurrentQuantity计算属性
        product.currentQuantity = currentQuantity

        product.unitType = unitType
        product.minStockAlert = minStockAlert ?? (currentQuantity * 0.2) // 默认20%预警
        product.consumptionRate = consumptionRate ?? 0.0

        let success = repository.save { _ in }
        if !success {
            throw ConsumableError.saveFailed
        }
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    /// 取消消耗品设置
    func removeConsumableSettings(for product: Product) async throws {
        product.isConsumable = false
        product.currentQuantity = 0.0
        product.unitType = nil
        product.minStockAlert = 0.0
        product.consumptionRate = 0.0
        
        let success = repository.save { _ in }
        if !success {
            throw ConsumableError.saveFailed
        }
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    // MARK: - 智能推荐
    
    /// 获取推荐消耗量
    func getRecommendedConsumption(for product: Product) -> Double? {
        guard product.isConsumable == true else { return nil }
        
        // 基于历史消耗记录计算推荐量
        let consumptionRecords: [Double] = product.usageRecords?.compactMap { record in
            guard let usageRecord = record as? UsageRecord,
                  usageRecord.consumedQuantity > 0 else { return nil }
            return usageRecord.consumedQuantity
        } ?? []
        
        if consumptionRecords.isEmpty {
            // 如果没有历史记录，返回默认推荐量
            return product.consumptionRate > 0 ? product.consumptionRate : 1.0
        }
        
        // 计算最近10次使用的平均消耗量
        let recentRecords = Array(consumptionRecords.suffix(10))
        let totalConsumption = recentRecords.reduce(0.0) { $0 + $1 }
        let averageConsumption = totalConsumption / Double(recentRecords.count)
        
        return averageConsumption
    }
    
    /// 获取预计用完时间
    func getEstimatedEmptyDate(for product: Product) -> Date? {
        return product.estimatedEmptyDate
    }
    
    // MARK: - 通知管理
    
    private func scheduleStockAlert(for product: Product) {
        // 这里可以集成通知系统
        // 暂时使用简单的日志记录
        print("⚠️ 低库存警告: \(product.name ?? "未知产品") 库存不足")
    }
    
    // MARK: - 数据分析
    
    /// 获取消耗趋势数据
    func getConsumptionTrend(for product: Product, days: Int = 30) -> [ConsumptionDataPoint] {
        guard product.isConsumable == true else { return [] }
        
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) ?? endDate
        
        let consumptionRecords = product.usageRecords?.compactMap { record -> ConsumptionDataPoint? in
            guard let usageRecord = record as? UsageRecord,
                  let date = usageRecord.date,
                  usageRecord.consumedQuantity > 0,
                  date >= startDate && date <= endDate else { return nil }
            
            return ConsumptionDataPoint(
                date: date,
                consumedQuantity: usageRecord.consumedQuantity,
                remainingQuantity: usageRecord.remainingQuantity
            )
        } ?? []
        
        return consumptionRecords.sorted { $0.date < $1.date }
    }
    
    // MARK: - 自动归档功能
    
    /// 获取自动归档配置
    func getAutoArchiveConfiguration() -> AutoArchiveConfiguration {
        return autoArchiveService.configuration
    }
    
    /// 更新自动归档配置
    func updateAutoArchiveConfiguration(_ configuration: AutoArchiveConfiguration) {
        autoArchiveService.updateConfiguration(configuration)
    }
    
    /// 获取待归档产品列表
    func getPendingArchiveProducts() -> [Product] {
        return autoArchiveService.pendingArchiveProducts
    }
    
    /// 获取归档建议列表
    func getArchiveSuggestions() -> [ArchiveSuggestion] {
        return autoArchiveService.archiveSuggestions
    }
    
    /// 手动触发归档检查
    func performArchiveCheck() async {
        await autoArchiveService.performArchiveCheck()
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    /// 执行自动归档
    func executeAutoArchive(for product: Product) async throws {
        try await autoArchiveService.executeArchive(for: product)
        
        await MainActor.run {
            loadConsumableProducts()
        }
    }
    
    /// 忽略归档建议
    func ignoreArchiveSuggestion(for product: Product) {
        autoArchiveService.ignoreArchiveSuggestion(for: product)
    }
    
    /// 推迟归档建议
    func postponeArchiveSuggestion(for product: Product, days: Int = 7) {
        autoArchiveService.postponeArchiveSuggestion(for: product, days: days)
    }
    
    /// 批量执行自动归档
    func executeBatchAutoArchive() async throws {
        let pendingProducts = getPendingArchiveProducts()
        
        for product in pendingProducts {
            try await executeAutoArchive(for: product)
        }
    }
}

// MARK: - 数据模型

struct ConsumptionDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let consumedQuantity: Double
    let remainingQuantity: Double
}

// MARK: - 错误类型

enum ConsumableError: LocalizedError {
    case notConsumable
    case insufficientStock
    case invalidQuantity
    case dataInconsistency
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .notConsumable:
            return "该产品不是消耗品"
        case .insufficientStock:
            return "库存不足"
        case .invalidQuantity:
            return "数量无效"
        case .dataInconsistency:
            return "数据不一致"
        case .saveFailed:
            return "保存失败"
        }
    }
}