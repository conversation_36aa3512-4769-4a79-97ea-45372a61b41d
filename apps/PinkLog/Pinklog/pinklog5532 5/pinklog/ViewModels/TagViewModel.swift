import Foundation
import CoreData
import SwiftUI
import Combine

class TagViewModel: ObservableObject {
    // MARK: - 属性
    private let repository: TagRepository
    
    @Published var tags: [Tag] = []
    @Published var isDataLoaded: Bool = false
    @Published var selectedTags: Set<Tag> = []
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false
    
    // MARK: - 初始化
    init(repository: TagRepository) {
        self.repository = repository
    }
    
    // MARK: - 数据加载
    func loadTags() {
        isLoading = true
        
        tags = repository.fetchAll(sortDescriptors: [NSSortDescriptor(key: "name", ascending: true)])
        
        isLoading = false
        isDataLoaded = true
    }
    
    // MARK: - CRUD操作
    func addTag(name: String, color: String? = nil, type: String? = nil) -> Tag? {
        var createdTag: Tag? = nil
        
        let success = repository.save { context in
            let newTag = Tag(context: context)
            newTag.id = UUID()
            newTag.name = name
            newTag.color = color ?? ["red", "blue", "green", "orange", "purple", "pink"].randomElement()
            newTag.type = type
            createdTag = newTag
        }
        
        if success {
            loadTags()
        } else {
            errorMessage = "添加标签失败"
        }
        
        return createdTag
    }
    
    func updateTag(_ tag: Tag, name: String, color: String? = nil, type: String? = nil) -> Bool {
        let success = repository.save { context in
            tag.name = name
            
            if let color = color {
                tag.color = color
            }
            
            if let type = type {
                tag.type = type
            }
        }
        
        if success {
            loadTags()
        } else {
            errorMessage = "更新标签失败"
        }
        
        return success
    }
    
    func deleteTag(_ tag: Tag) -> Bool {
        let success = repository.delete(entity: tag)
        
        if success {
            loadTags()
            selectedTags.remove(tag)
        } else {
            errorMessage = "删除标签失败"
        }
        
        return success
    }
    
    // MARK: - 标签选择
    func toggleTag(_ tag: Tag) {
        if selectedTags.contains(tag) {
            selectedTags.remove(tag)
        } else {
            selectedTags.insert(tag)
        }
    }
    
    func selectTag(_ tag: Tag) {
        selectedTags.insert(tag)
    }
    
    func unselectTag(_ tag: Tag) {
        selectedTags.remove(tag)
    }
    
    func clearSelection() {
        selectedTags.removeAll()
    }
    
    func isSelected(_ tag: Tag) -> Bool {
        return selectedTags.contains(tag)
    }
    
    // MARK: - 辅助方法
    func getTagsByType() -> [String: [Tag]] {
        var result = [String: [Tag]]()
        
        for tag in tags {
            let type = tag.type ?? "未分类"
            if result[type] == nil {
                result[type] = []
            }
            result[type]?.append(tag)
        }
        
        return result
    }
    
    func getTagColor(_ tag: Tag) -> Color {
        guard let colorName = tag.color else {
            return .gray
        }
        
        switch colorName {
        case "red": return .red
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "purple": return .purple
        case "pink": return .pink
        case "yellow": return .yellow
        case "teal": return .teal
        case "indigo": return .indigo
        default: return .gray
        }
    }
}
