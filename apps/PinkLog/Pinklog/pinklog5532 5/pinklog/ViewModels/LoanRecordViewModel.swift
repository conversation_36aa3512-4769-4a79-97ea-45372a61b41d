import Foundation
import CoreData
import SwiftUI
import Combine

class LoanRecordViewModel: ObservableObject {
    // MARK: - 属性
    private let repository: LoanRecordRepository
    private let productRepository: ProductRepository
    
    @Published var loanRecords: [LoanRecord] = []
    @Published var activeLoans: [LoanRecord] = []
    @Published var overdueLoans: [LoanRecord] = []
    @Published var returnedLoans: [LoanRecord] = []
    @Published var currentProduct: Product?
    @Published var searchText: String = ""
    @Published var filterOption: FilterOption = .all
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false
    
    // 筛选选项
    enum FilterOption: String, CaseIterable, Identifiable {
        case all = "全部"
        case active = "借出中"
        case overdue = "已逾期"
        case nearDue = "即将到期"
        case returned = "已归还"
        case lost = "已丢失"
        
        var id: String { self.rawValue }
    }
    
    // MARK: - 初始化
    init(repository: LoanRecordRepository, productRepository: ProductRepository) {
        self.repository = repository
        self.productRepository = productRepository
    }
    
    // MARK: - 数据加载
    func loadLoanRecords() {
        isLoading = true
        
        if let product = currentProduct, let productId = product.id {
            // 加载特定产品的借阅记录
            loanRecords = repository.fetchByProduct(productId: productId)
        } else {
            // 加载所有借阅记录
            loanRecords = repository.fetchAll(sortDescriptors: [NSSortDescriptor(key: "createdAt", ascending: false)])
        }
        
        // 应用搜索筛选
        if !searchText.isEmpty {
            loanRecords = repository.search(query: searchText)
        }
        
        // 应用状态筛选
        switch filterOption {
        case .all:
            break // 不做额外筛选
        case .active:
            loanRecords = loanRecords.filter { $0.returnDate == nil && $0.status != "lost" }
        case .overdue:
            loanRecords = loanRecords.filter { $0.isOverdue }
        case .nearDue:
            loanRecords = loanRecords.filter { $0.isNearDueDate }
        case .returned:
            loanRecords = loanRecords.filter { $0.returnDate != nil || $0.status == "returned" }
        case .lost:
            loanRecords = loanRecords.filter { $0.status == "lost" }
        }
        
        // 加载活跃和逾期借阅记录（用于通知和统计）
        activeLoans = repository.fetchActiveLoans()
        overdueLoans = repository.fetchOverdueLoans()
        returnedLoans = repository.fetchReturnedLoans()
        
        isLoading = false
    }
    
    // 设置当前产品
    func setCurrentProduct(_ product: Product?) {
        currentProduct = product
        loadLoanRecords()
    }
    
    // MARK: - CRUD操作
    func addLoanRecord(borrowerName: String, contactInfo: String?, dueDate: Date, notes: String?, product: Product) -> LoanRecord? {
        var createdLoanRecord: LoanRecord? = nil
        
        let success = repository.save { context in
            let newLoanRecord = LoanRecord(context: context)
            newLoanRecord.id = UUID()
            newLoanRecord.borrowerName = borrowerName
            newLoanRecord.contactInfo = contactInfo
            newLoanRecord.createdAt = Date()
            newLoanRecord.dueDate = dueDate
            newLoanRecord.notes = notes
            newLoanRecord.isLoanedOut = true
            newLoanRecord.status = "active"
            newLoanRecord.product = product
            
            createdLoanRecord = newLoanRecord
        }
        
        if success {
            loadLoanRecords()
            
            // 设置提醒
            if let loanRecord = createdLoanRecord, let id = loanRecord.id {
                scheduleLoanDueReminder(for: loanRecord)
            }
        } else {
            errorMessage = "添加借阅记录失败"
        }
        
        return createdLoanRecord
    }
    
    func updateLoanRecord(_ loanRecord: LoanRecord, borrowerName: String, contactInfo: String?, dueDate: Date, notes: String?) -> Bool {
        let success = repository.save { context in
            loanRecord.borrowerName = borrowerName
            loanRecord.contactInfo = contactInfo
            loanRecord.dueDate = dueDate
            loanRecord.notes = notes
        }
        
        if success {
            loadLoanRecords()
            
            // 更新提醒
            if let id = loanRecord.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
                scheduleLoanDueReminder(for: loanRecord)
            }
        } else {
            errorMessage = "更新借阅记录失败"
        }
        
        return success
    }
    
    func markAsReturned(_ loanRecord: LoanRecord) -> Bool {
        let success = repository.save { context in
            loanRecord.returnDate = Date()
            loanRecord.status = "returned"
        }
        
        if success {
            loadLoanRecords()
            
            // 取消提醒
            if let id = loanRecord.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
            }
        } else {
            errorMessage = "标记归还失败"
        }
        
        return success
    }
    
    func markAsLost(_ loanRecord: LoanRecord) -> Bool {
        let success = repository.save { context in
            loanRecord.status = "lost"
        }
        
        if success {
            loadLoanRecords()
            
            // 取消提醒
            if let id = loanRecord.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
            }
        } else {
            errorMessage = "标记丢失失败"
        }
        
        return success
    }
    
    func deleteLoanRecord(_ loanRecord: LoanRecord) -> Bool {
        let success = repository.delete(entity: loanRecord)
        
        if success {
            loadLoanRecords()
            
            // 取消提醒
            if let id = loanRecord.id {
                NotificationManager.shared.cancelLoanReminder(for: id)
            }
        } else {
            errorMessage = "删除借阅记录失败"
        }
        
        return success
    }
    
    // MARK: - 提醒功能
    private func scheduleLoanDueReminder(for loanRecord: LoanRecord) {
        guard let product = loanRecord.product, let loanId = loanRecord.id else { return }
        
        // 只为未归还的借阅设置提醒
        if loanRecord.returnDate == nil && loanRecord.status != "lost" {
            NotificationManager.shared.scheduleLoanDueReminder(for: product, loanRecord: loanRecord)
        }
    }
    
    // MARK: - 统计分析
    func getLoanStatistics() -> [String: Any] {
        let allLoans = repository.fetchAll()
        let activeLoans = repository.fetchActiveLoans()
        let overdueLoans = repository.fetchOverdueLoans()
        let returnedLoans = repository.fetchReturnedLoans()
        let lostLoans = repository.fetchLostLoans()
        
        // 计算平均借阅时长
        var totalDuration = 0
        var loanCount = 0
        
        for loan in returnedLoans {
            if let duration = loan.loanDuration {
                totalDuration += duration
                loanCount += 1
            }
        }
        
        let averageDuration = loanCount > 0 ? Double(totalDuration) / Double(loanCount) : 0
        
        return [
            "totalLoans": allLoans.count,
            "activeLoans": activeLoans.count,
            "overdueLoans": overdueLoans.count,
            "returnedLoans": returnedLoans.count,
            "lostLoans": lostLoans.count,
            "averageDuration": averageDuration
        ]
    }
}
