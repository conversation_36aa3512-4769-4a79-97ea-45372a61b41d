//
//  ContentView.swift
//  pinklog
//
//  Created by thr33 on 2025/5/10.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var themeManager: ThemeManager // 接收 ThemeManager

    // 创建所需的ViewModel
    @StateObject private var productViewModel = ProductViewModel(
        repository: ProductRepository(context: PersistenceController.shared.container.viewContext),
        categoryRepository: CategoryRepository(context: PersistenceController.shared.container.viewContext)
    )

    @StateObject private var tagViewModel = TagViewModel(
        repository: TagRepository(context: PersistenceController.shared.container.viewContext)
    )

    @StateObject private var usageViewModel = UsageViewModel(
        usageRepository: UsageRecordRepository(context: PersistenceController.shared.container.viewContext),
        expenseRepository: RelatedExpenseRepository(context: PersistenceController.shared.container.viewContext)
    )

    // 主动服务相关
    @StateObject private var analyticsAssistant = AnalyticsAssistant()

    // 会话历史管理
    @StateObject private var conversationHistoryViewModel = ConversationHistoryViewModel(
        conversationRepository: ConversationRepository(context: PersistenceController.shared.container.viewContext),
        messageRepository: MessageRepository(context: PersistenceController.shared.container.viewContext)
    )

    // 延迟初始化ProactiveService
    @State private var proactiveService: ProactiveService?

    // 用户设置管理器
    @StateObject private var userProfileManager = UserProfileManager.shared

    // 移除了 loanRecordViewModel

    // @StateObject private var themeManager = ThemeManager() // 已移除

    var body: some View {
        Group {
            if userProfileManager.isOnboardingCompleted {
                MainView()
                    .environmentObject(productViewModel)
                    .environmentObject(tagViewModel)
                    .environmentObject(usageViewModel)
                    .environmentObject(analyticsAssistant)
                    .environmentObject(conversationHistoryViewModel)
                    .environmentObject(userProfileManager)
                    .task {
                        await initializeProactiveService()
                    }
            } else {
                OnboardingView()
                    .environmentObject(userProfileManager)
            }
        }
    }

    // MARK: - 主动服务初始化
    private func initializeProactiveService() async {
        // 创建AIDataContextBuilder
        let aiDataContextBuilder = AIDataContextBuilder(
            productRepository: ProductRepository(context: PersistenceController.shared.container.viewContext),
            usageRepository: UsageRecordRepository(context: PersistenceController.shared.container.viewContext),
            expenseRepository: RelatedExpenseRepository(context: PersistenceController.shared.container.viewContext),
            overviewEngine: AdvancedOverviewEngine.shared
        )

        // 创建ProactiveService
        await MainActor.run {
            proactiveService = ProactiveService(
                aiDataContextBuilder: aiDataContextBuilder,
                analyticsAssistant: analyticsAssistant
            )
        }

        // 延迟执行首次主动分析（避免阻塞启动）
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒后
        await proactiveService?.performProactiveAnalysis()
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
