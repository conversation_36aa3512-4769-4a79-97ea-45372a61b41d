//
//  pinklogApp.swift
//  pinklog
//
//  Created by thr33 on 2025/5/10.
//

import SwiftUI
import BackgroundTasks
import UserNotifications

@main
struct pinklogApp: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var themeManager = ThemeManager() // 添加 ThemeManager
    @StateObject private var purchaseChannelViewModel: PurchaseChannelViewModel
    @StateObject private var timelineViewModel: TimelineViewModel

    init() {
        // 启动网络监听
        _ = NetworkMonitor.shared

        // 初始化视图上下文
        let viewContext = PersistenceController.shared.container.viewContext

        // 初始化PurchaseChannelViewModel
        _purchaseChannelViewModel = StateObject(wrappedValue: PurchaseChannelViewModel(context: viewContext))

        // 初始化TimelineViewModel
        let usageRepository = UsageRecordRepository(context: viewContext)
        let expenseRepository = RelatedExpenseRepository(context: viewContext)
        _timelineViewModel = StateObject(wrappedValue: TimelineViewModel(
            usageRepository: usageRepository,
            expenseRepository: expenseRepository
        ))

        // 修改NavigationLink箭头的全局样式
        UINavigationBar.appearance().tintColor = UIColor(Color.accentColor) // 注意：这里的 AccentColor 可能也需要根据主题动态变化

        // 立即初始化备份系统以注册BGTaskScheduler（必须在应用启动完成前）
        // 但延迟启动实际的备份调度，等待PurchaseManager完成初始化
        print("🚀 应用启动：优先初始化PurchaseManager")

        // 立即初始化AutoBackupScheduler以注册BGTaskScheduler
        let autoBackupScheduler = AutoBackupScheduler.shared
        let backupManager = BackupManager.shared
        print("📋 BGTaskScheduler已注册，但备份调度将延迟启动")

        // 立即触发PurchaseManager初始化（最高优先级）
        _ = PurchaseManager.shared
        
        // 启动消耗品提醒服务
        _ = ConsumableReminderService.shared
        print("📦 消耗品提醒服务已启动")
        
        // 配置通知处理器
        let notificationHandler = NotificationHandler.shared
        notificationHandler.configure(context: viewContext)
        print("🔔 通知处理器已配置")

        // 监听PurchaseManager初始化完成通知
        NotificationCenter.default.addObserver(
            forName: .purchaseManagerInitialized,
            object: nil,
            queue: .main
        ) { _ in
            print("🎯 PurchaseManager初始化完成，现在启动备份系统")

            // 先启动iCloud备份服务，等待其完成后再启动备份调度
            Task {
                // 启动iCloud备份服务并等待完成
                await iCloudDocumentsBackupService.shared.startServiceAndWait()

                // 启动备份调度（确保iCloud服务已准备就绪）
                if backupManager.configuration.autoBackupEnabled {
                    await autoBackupScheduler.startScheduling()
                }
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(themeManager) // 注入 ThemeManager
                .environmentObject(purchaseChannelViewModel) // 注入 PurchaseChannelViewModel
                .environmentObject(timelineViewModel) // 注入 TimelineViewModel
                .preferredColorScheme(themeManager.currentTheme.colorScheme) // 应用 preferredColorScheme
                .onAppear {
                    // 在App启动时执行初始化任务
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        // 迁移旧的字符串渠道到新的关系模型
                        purchaseChannelViewModel.migrateAllProducts()

                        // 清理旧的渐进式JPEG设置
                        UserDefaults.standard.removeObject(forKey: "useProgressiveJPEG")
                        
                        // 处理待处理的通知导航
                        NotificationHandler.shared.handlePendingNavigation()
                    }
                }
        }
    }
}
