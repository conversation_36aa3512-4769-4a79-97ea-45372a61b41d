import Foundation
import CoreData

/// 完整的产品数据模型 - 包含所有字段供AI使用
struct CompleteProductData: Codable, Identifiable {
    let id: UUID
    
    // 基本信息
    let name: String
    let brand: String?
    let model: String?
    let price: Double
    let quantity: Int
    
    // 购买信息
    let purchaseDate: Date
    let purchaseChannel: String?
    let purchaseMotivation: String?
    let purchaseNotes: String?
    
    // 分类和标签
    let categoryName: String?
    let categoryIcon: String?
    let tags: [CompleteTagData]
    
    // 产品特性
    let isVirtualProduct: Bool
    let expectedLifespan: Int
    let expectedUsageFrequency: String?
    let valuationMethod: String
    
    // 保修和有效期
    let warrantyEndDate: Date?
    let warrantyDetails: String?
    let warrantyImage: String?
    let expiryDate: Date?
    
    // 满意度和评价
    let initialSatisfaction: Int
    let currentAverageSatisfaction: Double
    
    // 使用统计
    let totalUsageCount: Int
    let effectiveUsage: Double
    let costPerUse: Double
    let averageSatisfaction: Double
    let worthItIndex: Double
    
    // 财务信息
    let totalCostOfOwnership: Double
    let totalRelatedExpenses: Double
    
    // 状态信息
    let daysSincePurchase: Int
    let isNearExpiry: Bool
    let isNearWarrantyEnd: Bool
    let productStatus: String
    
    // 借阅信息
    let loanCount: Int
    let isCurrentlyLoaned: Bool
    let activeLoanInfo: String?
    
    // 使用记录
    let usageRecords: [CompleteUsageRecordData]
    let relatedExpenses: [CompleteExpenseData]
    let reminders: [CompleteReminderData]
    
    // 关联产品
    let relatedProductNames: [String]
    let parentProductNames: [String]
    
    // 图片和媒体
    let hasImages: Bool
    let imageCount: Int
    
    // 计算属性
    var formattedPrice: String {
        return String(format: "¥%.2f", price)
    }
    
    var formattedTotalCost: String {
        return String(format: "¥%.2f", totalCostOfOwnership)
    }
    
    var purchaseDateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: purchaseDate)
    }
    
    var usageFrequencyDescription: String {
        if totalUsageCount == 0 { return "未使用" }
        let daysOwned = max(daysSincePurchase, 1)
        let frequency = Double(totalUsageCount) / Double(daysOwned) * 30 // 月使用频率
        
        switch frequency {
        case 0..<1: return "很少使用"
        case 1..<5: return "偶尔使用"
        case 5..<15: return "经常使用"
        case 15..<30: return "频繁使用"
        default: return "每天使用"
        }
    }
    
    var satisfactionTrend: String {
        guard usageRecords.count >= 2 else { return "数据不足" }
        
        let recentRecords = usageRecords.sorted { $0.date > $1.date }.prefix(5)
        let oldRecords = usageRecords.sorted { $0.date > $1.date }.dropFirst(5).prefix(5)
        
        guard !recentRecords.isEmpty && !oldRecords.isEmpty else { return "数据不足" }
        
        let recentAvg = recentRecords.map { Double($0.satisfaction) }.reduce(0, +) / Double(recentRecords.count)
        let oldAvg = oldRecords.map { Double($0.satisfaction) }.reduce(0, +) / Double(oldRecords.count)
        
        let diff = recentAvg - oldAvg
        
        if diff > 0.5 { return "满意度上升" }
        else if diff < -0.5 { return "满意度下降" }
        else { return "满意度稳定" }
    }
}

/// 完整的使用记录数据模型
struct CompleteUsageRecordData: Codable, Identifiable {
    let id: UUID
    let date: Date
    let satisfaction: Int
    let emotionalValue: Int
    let duration: Double
    
    // 使用类型和场景
    let usageType: String
    let scenario: String?
    let title: String?
    let notes: String?
    let memories: String?
    
    // 借阅相关
    let isLoanedOut: Bool
    let borrowerName: String?
    let contactInfo: String?
    let dueDate: Date?
    let returnDate: Date?
    
    // 转让相关
    let recipient: String?
    let transferPrice: Double
    let transferType: String?
    let wearCondition: String?
    
    // 故事和回忆
    let isStory: Bool
    let hasImages: Bool
    let hasAudioRecordings: Bool
    
    var dateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
    
    var satisfactionDescription: String {
        switch satisfaction {
        case 5: return "非常满意"
        case 4: return "满意"
        case 3: return "一般"
        case 2: return "不满意"
        case 1: return "非常不满意"
        default: return "未评价"
        }
    }
    
    var emotionalValueDescription: String {
        switch emotionalValue {
        case 5: return "情感价值很高"
        case 4: return "有情感价值"
        case 3: return "一般情感价值"
        case 2: return "情感价值较低"
        case 1: return "无情感价值"
        default: return "未评价"
        }
    }
    
    var usageTypeDescription: String {
        switch usageType {
        case "personal": return "个人使用"
        case "loaned": return "借出"
        case "gifted": return "赠送"
        case "sold": return "出售"
        case "lost": return "丢失"
        case "damaged": return "损坏"
        default: return usageType
        }
    }
}

/// 完整的费用数据模型
struct CompleteExpenseData: Codable, Identifiable {
    let id: UUID
    let amount: Double
    let date: Date
    let notes: String?
    let typeName: String
    let typeIcon: String?
    
    var formattedAmount: String {
        return String(format: "¥%.2f", amount)
    }
    
    var dateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

/// 完整的标签数据模型
struct CompleteTagData: Codable, Identifiable {
    let id: UUID
    let name: String
    let color: String?
    let type: String?
    
    var displayColor: String {
        return color ?? "gray"
    }
}

/// 完整的提醒数据模型
struct CompleteReminderData: Codable, Identifiable {
    let id: UUID
    let date: Date
    let message: String
    let reminderType: String
    let isActive: Bool
    
    var dateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    var typeDescription: String {
        switch reminderType {
        case "warranty": return "保修提醒"
        case "expiry": return "过期提醒"
        case "maintenance": return "维护提醒"
        case "usage": return "使用提醒"
        default: return reminderType
        }
    }
}

/// 完整的类别数据模型
struct CompleteCategoryData: Codable, Identifiable {
    let id: UUID
    let name: String
    let icon: String?
    let productCount: Int
    let totalValue: Double
    let averageWorthIndex: Double
    
    var formattedTotalValue: String {
        return String(format: "¥%.2f", totalValue)
    }
}
