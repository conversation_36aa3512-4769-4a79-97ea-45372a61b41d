import Foundation
import SwiftUI

// MARK: - 对话相关模型

/// 对话消息
struct ConversationMessage: Identifiable, Codable, Equatable {
    let id: UUID
    let role: MessageRole
    let content: String
    let timestamp: Date
    let analysisContext: AnalysisContext?
    let attachments: [MessageAttachment]
    
    init(
        role: MessageRole,
        content: String,
        analysisContext: AnalysisContext? = nil,
        attachments: [MessageAttachment] = []
    ) {
        self.id = UUID()
        self.role = role
        self.content = content
        self.timestamp = Date()
        self.analysisContext = analysisContext
        self.attachments = attachments
    }
}

/// 消息角色
enum MessageRole: String, Codable, CaseIterable {
    case user = "user"
    case assistant = "assistant"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .user: return "用户"
        case .assistant: return "AI助手"
        case .system: return "系统"
        }
    }
    
    var color: Color {
        switch self {
        case .user: return .blue
        case .assistant: return .green
        case .system: return .gray
        }
    }
}

/// 消息附件
struct MessageAttachment: Identifiable, Codable, Equatable {
    let id: UUID
    let type: AttachmentType
    let title: String
    let data: Data
    let metadata: [String: String]
    
    init(type: AttachmentType, title: String, data: Data, metadata: [String: String] = [:]) {
        self.id = UUID()
        self.type = type
        self.title = title
        self.data = data
        self.metadata = metadata
    }
}

/// 附件类型
enum AttachmentType: String, Codable, CaseIterable {
    case chart = "chart"
    case report = "report"
    case analysis = "analysis"
    case image = "image"
    
    var displayName: String {
        switch self {
        case .chart: return "图表"
        case .report: return "报告"
        case .analysis: return "分析"
        case .image: return "图片"
        }
    }
    
    var icon: String {
        switch self {
        case .chart: return "chart.bar.fill"
        case .report: return "doc.text.fill"
        case .analysis: return "brain.head.profile"
        case .image: return "photo.fill"
        }
    }
}

/// 分析上下文
struct AnalysisContext: Codable, Equatable {
    let productId: UUID?
    let analysisType: AnalysisType
    let timeRange: DateRange?
    let filters: [String: String]
    let metadata: [String: String]
    
    init(
        productId: UUID? = nil,
        analysisType: AnalysisType,
        timeRange: DateRange? = nil,
        filters: [String: String] = [:],
        metadata: [String: String] = [:]
    ) {
        self.productId = productId
        self.analysisType = analysisType
        self.timeRange = timeRange
        self.filters = filters
        self.metadata = metadata
    }
}

/// 分析类型
enum AnalysisType: String, Codable, CaseIterable {
    case threeCurve = "three_curve"
    case valueRealization = "value_realization"
    case usageVitality = "usage_vitality"
    case emotionalBond = "emotional_bond"
    case comparative = "comparative"
    case portfolio = "portfolio"
    case general = "general"
    
    var displayName: String {
        switch self {
        case .threeCurve: return "三曲线分析"
        case .valueRealization: return "价值实现分析"
        case .usageVitality: return "使用活力分析"
        case .emotionalBond: return "情感依恋分析"
        case .comparative: return "对比分析"
        case .portfolio: return "组合分析"
        case .general: return "综合分析"
        }
    }
}

/// 日期范围
struct DateRange: Codable, Equatable {
    let startDate: Date
    let endDate: Date
    
    var duration: TimeInterval {
        return endDate.timeIntervalSince(startDate)
    }
    
    var displayString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
    }
}

/// AI助手响应
struct AssistantResponse: Codable {
    let content: String
    let confidence: Double
    let suggestions: [ConversationActionSuggestion]
    let relatedInsights: [String]
    let followUpQuestions: [String]
    let visualizations: [VisualizationSuggestion]
    
    init(
        content: String,
        confidence: Double = 0.8,
        suggestions: [ConversationActionSuggestion] = [],
        relatedInsights: [String] = [],
        followUpQuestions: [String] = [],
        visualizations: [VisualizationSuggestion] = []
    ) {
        self.content = content
        self.confidence = confidence
        self.suggestions = suggestions
        self.relatedInsights = relatedInsights
        self.followUpQuestions = followUpQuestions
        self.visualizations = visualizations
    }
}

/// 行动建议
struct ConversationActionSuggestion: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let priority: ConversationActionPriority
    let category: ConversationActionCategory
    let estimatedImpact: Double
    let timeframe: ConversationActionTimeframe
    
    init(
        title: String,
        description: String,
        priority: ConversationActionPriority,
        category: ConversationActionCategory,
        estimatedImpact: Double,
        timeframe: ConversationActionTimeframe
    ) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.priority = priority
        self.category = category
        self.estimatedImpact = estimatedImpact
        self.timeframe = timeframe
    }
}

/// 行动优先级
enum ConversationActionPriority: String, Codable, CaseIterable {
    case urgent = "urgent"
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .urgent: return "紧急"
        case .high: return "高"
        case .medium: return "中"
        case .low: return "低"
        }
    }
    
    var color: Color {
        switch self {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .yellow
        case .low: return .green
        }
    }
}

/// 行动类别
enum ConversationActionCategory: String, Codable, CaseIterable {
    case optimization = "optimization"
    case maintenance = "maintenance"
    case disposal = "disposal"
    case acquisition = "acquisition"
    case monitoring = "monitoring"
    
    var displayName: String {
        switch self {
        case .optimization: return "优化"
        case .maintenance: return "维护"
        case .disposal: return "处置"
        case .acquisition: return "获取"
        case .monitoring: return "监控"
        }
    }
    
    var icon: String {
        switch self {
        case .optimization: return "arrow.up.circle.fill"
        case .maintenance: return "wrench.and.screwdriver.fill"
        case .disposal: return "trash.fill"
        case .acquisition: return "plus.circle.fill"
        case .monitoring: return "eye.fill"
        }
    }
}

/// 行动时间框架
enum ConversationActionTimeframe: String, Codable, CaseIterable {
    case immediate = "immediate"
    case shortTerm = "short_term"
    case mediumTerm = "medium_term"
    case longTerm = "long_term"
    
    var displayName: String {
        switch self {
        case .immediate: return "立即"
        case .shortTerm: return "短期(1-4周)"
        case .mediumTerm: return "中期(1-3个月)"
        case .longTerm: return "长期(3个月以上)"
        }
    }
}

/// 可视化建议
struct VisualizationSuggestion: Identifiable, Codable {
    let id: UUID
    let type: VisualizationType
    let title: String
    let description: String
    let dataRequirements: [String]
    
    init(type: VisualizationType, title: String, description: String, dataRequirements: [String]) {
        self.id = UUID()
        self.type = type
        self.title = title
        self.description = description
        self.dataRequirements = dataRequirements
    }
}

/// 可视化类型
enum VisualizationType: String, Codable, CaseIterable {
    case threeCurve = "three_curve"
    case trendChart = "trend_chart"
    case heatmap = "heatmap"
    case comparison = "comparison"
    case distribution = "distribution"
    case correlation = "correlation"
    
    var displayName: String {
        switch self {
        case .threeCurve: return "三曲线图"
        case .trendChart: return "趋势图"
        case .heatmap: return "热力图"
        case .comparison: return "对比图"
        case .distribution: return "分布图"
        case .correlation: return "相关性图"
        }
    }
}

// MARK: - 月度报告模型

/// 月度报告
struct MonthlyReport: Identifiable, Codable {
    let id: UUID
    let month: Date
    let generatedAt: Date
    let summary: ReportSummary
    let sections: [ReportSection]
    let insights: [ReportInsight]
    let recommendations: [ReportRecommendation]
    let metrics: ReportMetrics
    let attachments: [ReportAttachment]

    init(
        month: Date,
        summary: ReportSummary,
        sections: [ReportSection],
        insights: [ReportInsight],
        recommendations: [ReportRecommendation],
        metrics: ReportMetrics,
        attachments: [ReportAttachment] = []
    ) {
        self.id = UUID()
        self.month = month
        self.generatedAt = Date()
        self.summary = summary
        self.sections = sections
        self.insights = insights
        self.recommendations = recommendations
        self.metrics = metrics
        self.attachments = attachments
    }

    var monthDisplayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: month)
    }
}

/// 报告摘要
struct ReportSummary: Codable {
    let totalProducts: Int
    let totalSpending: Double
    let averageWorthiness: Double
    let topPerformingCategory: String
    let keyHighlight: String
    let overallTrend: ConversationTrendDirection
    let monthlyGrowth: Double

    var spendingDisplayString: String {
        return String(format: "¥%.2f", totalSpending)
    }

    var worthinessDisplayString: String {
        return String(format: "%.1f分", averageWorthiness)
    }
}

/// 趋势方向
enum ConversationTrendDirection: String, Codable, CaseIterable {
    case increasing = "increasing"
    case decreasing = "decreasing"
    case stable = "stable"
    case volatile = "volatile"

    var displayName: String {
        switch self {
        case .increasing: return "上升"
        case .decreasing: return "下降"
        case .stable: return "稳定"
        case .volatile: return "波动"
        }
    }

    var color: Color {
        switch self {
        case .increasing: return .green
        case .decreasing: return .red
        case .stable: return .blue
        case .volatile: return .orange
        }
    }

    var icon: String {
        switch self {
        case .increasing: return "arrow.up.right"
        case .decreasing: return "arrow.down.right"
        case .stable: return "arrow.right"
        case .volatile: return "waveform"
        }
    }
}

/// 报告章节
struct ReportSection: Identifiable, Codable {
    let id: UUID
    let title: String
    let content: String
    let type: SectionType
    let visualizations: [String]
    let keyPoints: [String]
    let order: Int

    init(
        title: String,
        content: String,
        type: SectionType,
        visualizations: [String] = [],
        keyPoints: [String] = [],
        order: Int
    ) {
        self.id = UUID()
        self.title = title
        self.content = content
        self.type = type
        self.visualizations = visualizations
        self.keyPoints = keyPoints
        self.order = order
    }
}

/// 章节类型
enum SectionType: String, Codable, CaseIterable {
    case overview = "overview"
    case spending = "spending"
    case performance = "performance"
    case insights = "insights"
    case recommendations = "recommendations"
    case forecast = "forecast"

    var displayName: String {
        switch self {
        case .overview: return "总览"
        case .spending: return "消费分析"
        case .performance: return "表现分析"
        case .insights: return "深度洞察"
        case .recommendations: return "行动建议"
        case .forecast: return "趋势预测"
        }
    }

    var icon: String {
        switch self {
        case .overview: return "chart.pie.fill"
        case .spending: return "creditcard.fill"
        case .performance: return "chart.bar.fill"
        case .insights: return "lightbulb.fill"
        case .recommendations: return "target"
        case .forecast: return "crystal.ball.fill"
        }
    }
}

/// 报告洞察
struct ReportInsight: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let category: InsightCategory
    let importance: InsightImportance
    let supportingData: [String]
    let relatedProducts: [UUID]

    init(
        title: String,
        description: String,
        category: InsightCategory,
        importance: InsightImportance,
        supportingData: [String] = [],
        relatedProducts: [UUID] = []
    ) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.category = category
        self.importance = importance
        self.supportingData = supportingData
        self.relatedProducts = relatedProducts
    }
}

/// 洞察类别
enum InsightCategory: String, Codable, CaseIterable {
    case spending = "spending"
    case usage = "usage"
    case satisfaction = "satisfaction"
    case efficiency = "efficiency"
    case opportunity = "opportunity"
    case risk = "risk"
    // 个性化引擎专用类别
    case cost = "cost"
    case planning = "planning"
    case reflection = "reflection"
    case optimization = "optimization"
    case experience = "experience"
    case overview = "overview"

    var displayName: String {
        switch self {
        case .spending: return "消费模式"
        case .usage: return "使用习惯"
        case .satisfaction: return "满意度"
        case .efficiency: return "效率"
        case .opportunity: return "机会"
        case .risk: return "风险"
        case .cost: return "成本"
        case .planning: return "规划"
        case .reflection: return "反思"
        case .optimization: return "优化"
        case .experience: return "体验"
        case .overview: return "概览"
        }
    }

    var color: Color {
        switch self {
        case .spending: return .blue
        case .usage: return .green
        case .satisfaction: return .purple
        case .efficiency: return .orange
        case .opportunity: return .mint
        case .risk: return .red
        case .cost: return .blue
        case .planning: return .cyan
        case .reflection: return .indigo
        case .optimization: return .purple
        case .experience: return .pink
        case .overview: return .gray
        }
    }
}

/// 洞察重要性
enum InsightImportance: String, Codable, CaseIterable {
    case critical = "critical"
    case high = "high"
    case medium = "medium"
    case low = "low"

    var displayName: String {
        switch self {
        case .critical: return "关键"
        case .high: return "重要"
        case .medium: return "一般"
        case .low: return "次要"
        }
    }

    var priority: Int {
        switch self {
        case .critical: return 4
        case .high: return 3
        case .medium: return 2
        case .low: return 1
        }
    }
}

/// 报告建议
struct ReportRecommendation: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let actionType: ConversationActionCategory
    let priority: ConversationActionPriority
    let estimatedImpact: Double
    let timeframe: ConversationActionTimeframe
    let steps: [String]
    let expectedOutcome: String

    init(
        title: String,
        description: String,
        actionType: ConversationActionCategory,
        priority: ConversationActionPriority,
        estimatedImpact: Double,
        timeframe: ConversationActionTimeframe,
        steps: [String],
        expectedOutcome: String
    ) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.actionType = actionType
        self.priority = priority
        self.estimatedImpact = estimatedImpact
        self.timeframe = timeframe
        self.steps = steps
        self.expectedOutcome = expectedOutcome
    }
}

/// 报告指标
struct ReportMetrics: Codable {
    let totalProducts: Int
    let newProducts: Int
    let disposedProducts: Int
    let totalSpending: Double
    let averageProductValue: Double
    let topCategory: String
    let averageWorthiness: Double
    let satisfactionScore: Double
    let usageEfficiency: Double
    let monthlyGrowthRate: Double
    let portfolioHealth: Double

    var healthGrade: String {
        switch portfolioHealth {
        case 90...100: return "A+"
        case 80..<90: return "A"
        case 70..<80: return "B+"
        case 60..<70: return "B"
        case 50..<60: return "C+"
        case 40..<50: return "C"
        default: return "D"
        }
    }
}

/// 报告附件
struct ReportAttachment: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: AttachmentType
    let data: Data
    let description: String

    init(name: String, type: AttachmentType, data: Data, description: String) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.data = data
        self.description = description
    }
}
