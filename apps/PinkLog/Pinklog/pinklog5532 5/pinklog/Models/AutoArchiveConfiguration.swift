//
//  AutoArchiveConfiguration.swift
//  pinklog
//
//  Created by Assistant on 2025-01-21.
//

import Foundation

/// 自动归档配置
struct AutoArchiveConfiguration: Codable {
    /// 是否启用自动归档
    var isEnabled: Bool
    
    /// 未使用天数阈值（超过此天数未使用的产品可能被归档）
    var unusedDaysThreshold: Int
    
    /// 耗尽后天数阈值（产品耗尽后超过此天数未补充可能被归档）
    var depletedDaysThreshold: Int
    
    /// 过期后天数阈值（产品过期后超过此天数未处理可能被归档）
    var expiredDaysThreshold: Int
    
    /// 是否启用智能建议（基于使用模式分析）
    var enableSmartSuggestions: Bool
    
    /// 智能建议的置信度阈值（0.0-1.0）
    var confidenceThreshold: Double
    
    /// 检查频率（天数）
    var checkFrequencyDays: Int
    
    /// 默认配置
    static let `default` = AutoArchiveConfiguration(
        isEnabled: true,
        unusedDaysThreshold: 90,
        depletedDaysThreshold: 30,
        expiredDaysThreshold: 7,
        enableSmartSuggestions: true,
        confidenceThreshold: 0.7,
        checkFrequencyDays: 7
    )
    
    /// 初始化
    init(isEnabled: Bool = true,
         unusedDaysThreshold: Int = 90,
         depletedDaysThreshold: Int = 30,
         expiredDaysThreshold: Int = 7,
         enableSmartSuggestions: Bool = true,
         confidenceThreshold: Double = 0.7,
         checkFrequencyDays: Int = 7) {
        self.isEnabled = isEnabled
        self.unusedDaysThreshold = unusedDaysThreshold
        self.depletedDaysThreshold = depletedDaysThreshold
        self.expiredDaysThreshold = expiredDaysThreshold
        self.enableSmartSuggestions = enableSmartSuggestions
        self.confidenceThreshold = confidenceThreshold
        self.checkFrequencyDays = checkFrequencyDays
    }
    
    /// 验证配置的有效性
    var isValid: Bool {
        return unusedDaysThreshold > 0 &&
               depletedDaysThreshold > 0 &&
               expiredDaysThreshold > 0 &&
               confidenceThreshold >= 0.0 && confidenceThreshold <= 1.0 &&
               checkFrequencyDays > 0
    }
    
    /// 获取配置描述
    var description: String {
        var components: [String] = []
        
        if isEnabled {
            components.append("已启用")
        } else {
            components.append("已禁用")
        }
        
        components.append("未使用阈值: \(unusedDaysThreshold)天")
        components.append("耗尽阈值: \(depletedDaysThreshold)天")
        components.append("过期阈值: \(expiredDaysThreshold)天")
        
        if enableSmartSuggestions {
            components.append("智能建议: 启用 (置信度≥\(String(format: "%.0f", confidenceThreshold * 100))%)")
        } else {
            components.append("智能建议: 禁用")
        }
        
        components.append("检查频率: \(checkFrequencyDays)天")
        
        return components.joined(separator: ", ")
    }
}

// MARK: - 配置管理器

/// 自动归档配置管理器
class AutoArchiveConfigurationManager: ObservableObject {
    @Published var configuration: AutoArchiveConfiguration
    
    private let userDefaults = UserDefaults.standard
    private let configKey = "AutoArchiveConfiguration"
    
    init() {
        // 从 UserDefaults 加载配置
        if let data = userDefaults.data(forKey: configKey),
           let config = try? JSONDecoder().decode(AutoArchiveConfiguration.self, from: data) {
            self.configuration = config
        } else {
            self.configuration = .default
        }
    }
    
    /// 保存配置
    func saveConfiguration() {
        guard configuration.isValid else {
            print("⚠️ 自动归档配置无效，无法保存")
            return
        }
        
        do {
            let data = try JSONEncoder().encode(configuration)
            userDefaults.set(data, forKey: configKey)
            print("✅ 自动归档配置已保存")
        } catch {
            print("❌ 保存自动归档配置失败: \(error)")
        }
    }
    
    /// 重置为默认配置
    func resetToDefault() {
        configuration = .default
        saveConfiguration()
    }
    
    /// 更新配置
    func updateConfiguration(_ newConfig: AutoArchiveConfiguration) {
        configuration = newConfig
        saveConfiguration()
    }
}