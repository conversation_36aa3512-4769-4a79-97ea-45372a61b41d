import Foundation

// MARK: - 里程碑数据模型

/// 代表一个价值里程碑的结构体
struct Milestone: Identifiable, Hashable {
    let id: String
    let title: String
    let description: String
    let iconName: String
    var unlockedDate: Date?
    var isUnlocked: Bool { unlockedDate != nil }
}

// MARK: - 里程碑类型定义

enum MilestoneType: String, CaseIterable {
    // 成本维度
    case cost50Percent = "cost50Percent"
    case cost10Percent = "cost10Percent"
    case costIsOne = "costIsOne"
    
    // 使用维度
    case firstUse = "firstUse"
    case hundredUses = "hundredUses" // 按次
    case highFrequency = "highFrequency" // 按次
    case oneYearAnniversary = "oneYearAnniversary"
    case firstWeek = "firstWeek" // 按天
    case firstMonth = "firstMonth" // 按天
    case halfYear = "halfYear" // 按天
    
    // 满意度维度
    case loveAtFirstSight = "loveAtFirstSight"
    case topRated = "topRated"
    case consistentGlow = "consistentGlow"
    
    // 综合维度
    case trueValuePoint = "trueValuePoint"
    case veteran = "veteran"
    case valueBalancePoint = "valueBalancePoint" // 价值平衡点
    
    /// 获取该类型里程碑的详细信息
    var details: Milestone {
        switch self {
        // 成本维度
        case .cost50Percent:
            return Milestone(id: rawValue, title: "回本之路 · 50%", description: "单次/单日使用成本已低于购买价格的一半。", iconName: "chart.line.downtrend.xyaxis")
        case .cost10Percent:
            return Milestone(id: rawValue, title: "回本之路 · 10%", description: "单次/单日使用成本已低于购买价格的10%。", iconName: "chart.line.downtrend.xyaxis.circle")
        case .costIsOne:
            return Milestone(id: rawValue, title: "超值典范", description: "单次/单日使用成本已低于1元。", iconName: "chart.line.downtrend.xyaxis.circle.fill")
        
        // 使用维度
        case .firstUse:
            return Milestone(id: rawValue, title: "初次邂逅", description: "完成了第一次使用记录。", iconName: "sparkles")
        case .hundredUses:
            return Milestone(id: rawValue, title: "百战精英", description: "累计使用次数达到100次。", iconName: "100.square")
        case .highFrequency:
            return Milestone(id: rawValue, title: "高频战友", description: "在一个月内使用超过10次。", iconName: "flame")
        case .oneYearAnniversary:
            return Milestone(id: rawValue, title: "忠实伴侣", description: "在拥有物品满一年后仍在使用。", iconName: "calendar")
        case .firstWeek:
            return Milestone(id: rawValue, title: "陪伴一周", description: "物品的服役时间已满7天。", iconName: "7.square")
        case .firstMonth:
            return Milestone(id: rawValue, title: "月度印记", description: "物品的服役时间已满30天。", iconName: "30.square")
        case .halfYear:
            return Milestone(id: rawValue, title: "半年之交", description: "物品的服役时间已满180天。", iconName: "calendar.badge.plus")

        // 满意度维度
        case .loveAtFirstSight:
            return Milestone(id: rawValue, title: "一见钟情", description: "首次使用即获满分满意度。", iconName: "heart.fill")
        case .topRated:
            return Milestone(id: rawValue, title: "口碑之选", description: "累计平均满意度持续高于4.5星。", iconName: "star.leadinghalf.filled")
        case .consistentGlow:
            return Milestone(id: rawValue, title: "持续高光", description: "连续5次获得满分满意度。", iconName: "wand.and.stars")
        
        // 综合维度
        case .trueValuePoint:
            return Milestone(id: rawValue, title: "真·值点", description: "单次/单日成本已低于平均满意度分值。", iconName: "target")
        case .veteran:
            return Milestone(id: rawValue, title: "功勋元老", description: "同时满足使用满一年、超百次/长期服役、高满意度。", iconName: "rosette")
        case .valueBalancePoint:
            return Milestone(id: rawValue, title: "🎯 价值平衡点", description: "价值感知首次超过使用成本，投资开始获得回报的关键时刻。", iconName: "scale.3d")
        }
    }
}
