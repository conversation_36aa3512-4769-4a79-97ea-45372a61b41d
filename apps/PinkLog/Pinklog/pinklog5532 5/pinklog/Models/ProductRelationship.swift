import Foundation
import CoreData
import SwiftUI

// 产品关系模型
// 注意：这不是直接的CoreData实体，它是一个包装类，使用现有的relatedProducts关系并添加类型信息
class ProductRelationship: Identifiable {
    var id = UUID()
    var sourceProduct: Product
    var targetProduct: Product
    var relationshipType: ProductRelationshipType
    var notes: String?
    var createdAt: Date
    
    init(sourceProduct: Product, targetProduct: Product, relationshipType: ProductRelationshipType, notes: String? = nil) {
        self.sourceProduct = sourceProduct
        self.targetProduct = targetProduct
        self.relationshipType = relationshipType
        self.notes = notes
        self.createdAt = Date()
    }
}
