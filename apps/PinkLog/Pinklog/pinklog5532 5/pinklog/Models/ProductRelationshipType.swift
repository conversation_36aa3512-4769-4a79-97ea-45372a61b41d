import Foundation
import SwiftUI

// 产品关系类型
enum ProductRelationshipType: String, CaseIterable, Identifiable {
    // 基础关系类型
    case accessory = "配件"
    case consumable = "耗材"
    case replacement = "替代品"
    case complement = "互补品"
    
    // 套装相关类型
    case set = "套装"
    case setMember = "套装成员"
    case setParent = "套装容器"
    
    // 产品演进关系
    case predecessor = "前代产品"
    case successor = "后续产品"
    case upgrade = "升级产品"
    
    // 使用关系
    case prerequisite = "使用前提"
    case extensionProduct = "扩展产品"
    case alternative = "替代选择"
    
    // 其他
    case frequently_used_with = "常一起使用"
    case recommended_with = "推荐一起使用"
    case other = "其他"
    
    var id: String { self.rawValue }
    
    var icon: String {
        switch self {
        case .accessory: return "puzzlepiece"
        case .consumable: return "cart"
        case .replacement: return "arrow.triangle.swap"
        case .complement: return "circle.grid.2x1"
        case .set: return "square.grid.2x2"
        case .setMember: return "square.grid.3x2"
        case .setParent: return "square.grid.3x3"
        case .predecessor: return "arrow.left"
        case .successor: return "arrow.right"
        case .upgrade: return "arrow.up.right"
        case .prerequisite: return "exclamationmark.lock"
        case .extensionProduct: return "plus.rectangle"
        case .alternative: return "arrow.triangle.branch"
        case .frequently_used_with: return "person.2"
        case .recommended_with: return "hand.thumbsup"
        case .other: return "ellipsis.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .accessory: return .blue
        case .consumable: return .green
        case .replacement: return .orange
        case .complement: return .purple
        case .set, .setMember, .setParent: return .pink
        case .predecessor, .successor, .upgrade: return .indigo
        case .prerequisite: return .red
        case .extensionProduct: return .mint
        case .alternative: return .teal
        case .frequently_used_with: return .cyan
        case .recommended_with: return .brown
        case .other: return .gray
        }
    }
    
    var description: String {
        switch self {
        case .accessory: return "与主产品配合使用的附加产品"
        case .consumable: return "主产品使用过程中需要消耗的物品"
        case .replacement: return "可以替代主产品的同类产品"
        case .complement: return "与主产品互补使用的产品"
        case .set: return "与主产品属于同一套装的产品"
        case .setMember: return "作为套装的一部分"
        case .setParent: return "包含多个产品的套装"
        case .predecessor: return "当前产品的前代版本"
        case .successor: return "当前产品的后续版本"
        case .upgrade: return "当前产品的升级产品"
        case .prerequisite: return "使用主产品所需的必备产品"
        case .extensionProduct: return "扩展主产品功能的产品"
        case .alternative: return "可以替代使用的同类产品"
        case .frequently_used_with: return "经常与主产品一起使用的产品"
        case .recommended_with: return "推荐与主产品一起使用的产品"
        case .other: return "其他类型的关联产品"
        }
    }
    
    // 关系是否通常应该是双向的
    var isBidirectionalByDefault: Bool {
        switch self {
        case .set, .frequently_used_with, .recommended_with, .alternative:
            return true
        default:
            return false
        }
    }
    
    // 反向关系类型（如果存在）
    var reverseRelationshipType: ProductRelationshipType? {
        switch self {
        case .accessory: return .set
        case .predecessor: return .successor
        case .successor: return .predecessor
        case .setMember: return .setParent
        case .setParent: return .setMember
        case .prerequisite: return .extensionProduct
        case .extensionProduct: return .prerequisite
        default: return nil
        }
    }
    
    // 关系强度的默认建议值 (1-5)
    var defaultStrength: Int {
        switch self {
        case .prerequisite: return 5
        case .consumable, .accessory, .setMember, .setParent: return 4
        case .frequently_used_with, .complement: return 3
        case .recommended_with, .extensionProduct: return 2
        default: return 3
        }
    }
}
