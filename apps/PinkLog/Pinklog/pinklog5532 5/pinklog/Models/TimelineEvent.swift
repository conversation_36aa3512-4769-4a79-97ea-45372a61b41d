import Foundation
import SwiftUI

/// 时间线事件类型枚举
enum TimelineEventType: String, CaseIterable, Identifiable {
    case purchase = "购买"            // 购买
    case usage = "使用"               // 使用
    case consumption = "消耗"         // 消耗品使用
    case expense = "花费"             // 费用支出（如维护、配件）
    case loanOut = "借出"             // 借出
    case loanReturn = "归还"          // 归还
    case transfer = "转让"            // 转卖/转让
    
    var id: String { self.rawValue }
    
    var iconName: String {
        switch self {
        case .purchase: return "cart"
        case .usage: return "hand.tap"
        case .consumption: return "minus.circle.fill"
        case .expense: return "creditcard"
        case .loanOut: return "arrow.up.forward.circle"
        case .loanReturn: return "arrow.down.forward.circle"
        case .transfer: return "arrow.triangle.swap"
        }
    }
    
    var color: Color {
        switch self {
        case .purchase: return .blue
        case .usage: return .green
        case .consumption: return .cyan
        case .expense: return .red
        case .loanOut: return .orange
        case .loanReturn: return .mint
        case .transfer: return .purple
        }
    }
}

/// 时间线事件模型
struct TimelineEvent: Identifiable, Hashable {
    let id = UUID()
    let date: Date
    let type: TimelineEventType
    let title: String
    let description: String?
    let sourceId: UUID?      // 源数据ID
    let sourceType: String?  // 源数据类型（如 "Product", "UsageRecord" 等）
    
    // 用于Hashable协议
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: TimelineEvent, rhs: TimelineEvent) -> Bool {
        lhs.id == rhs.id
    }
}
