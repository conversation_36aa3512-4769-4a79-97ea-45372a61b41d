import Foundation
import CoreData

// MARK: - 生命周期阶段枚举
enum ProductLifecycleStage {
    case honeymoon      // 蜜月期 (0-30天)
    case adaptation     // 适应期 (30-90天)
    case stable         // 稳定期 (90-365天)
    case mature         // 成熟期 (365天+)
    
    init(daysSincePurchase: Int) {
        switch daysSincePurchase {
        case 0..<30: self = .honeymoon
        case 30..<90: self = .adaptation
        case 90..<365: self = .stable
        default: self = .mature
        }
    }
    
    // 获取各维度权重
    func getWeights() -> (usage: Double, cost: Double, satisfaction: Double) {
        switch self {
        case .honeymoon: return (0.2, 0.3, 0.5)    // 重视满意度和初体验
        case .adaptation: return (0.4, 0.3, 0.3)   // 重视使用习惯建立
        case .stable: return (0.35, 0.35, 0.3)     // 平衡各因素
        case .mature: return (0.3, 0.5, 0.2)       // 重视成本效益
        }
    }
    
    var description: String {
        switch self {
        case .honeymoon: return "蜜月期"
        case .adaptation: return "适应期"
        case .stable: return "稳定期"
        case .mature: return "成熟期"
        }
    }
}

// MARK: - 置信度评估系统
struct ConfidenceMetrics {
    let dataPoints: Int
    let timeSpan: TimeInterval
    let consistency: Double
    let factor: Double
    let level: ConfidenceLevel
    
    enum ConfidenceLevel: String, CaseIterable {
        case veryHigh = "很高"
        case high = "高"
        case medium = "中等"
        case low = "低"
        case veryLow = "很低"
        
        var threshold: Double {
            switch self {
            case .veryHigh: return 0.8
            case .high: return 0.6
            case .medium: return 0.4
            case .low: return 0.2
            case .veryLow: return 0.0
            }
        }
        
        var recommendation: String {
            switch self {
            case .veryHigh: return "强烈推荐基于此分析做决策"
            case .high: return "推荐基于此分析做决策"
            case .medium: return "建议收集更多数据后决策"
            case .low: return "分析仅供参考，建议谨慎决策"
            case .veryLow: return "数据不足，建议暂缓决策"
            }
        }
        
        var color: String {
            switch self {
            case .veryHigh: return "green"
            case .high: return "blue"
            case .medium: return "orange"
            case .low: return "yellow"
            case .veryLow: return "red"
            }
        }
    }
    
    static func calculate(for product: Product) -> ConfidenceMetrics {
        let records = product.usageRecords?.allObjects as? [UsageRecord] ?? []
        let dataPoints = records.count
        
        let timeSpan = product.daysSincePurchase > 0 ? 
            TimeInterval(product.daysSincePurchase * 24 * 3600) : 1
        
        let consistency = calculateConsistency(records)
        let factor = calculateConfidenceFactor(
            dataPoints: dataPoints, 
            timeSpan: timeSpan, 
            consistency: consistency
        )
        
        let level = determineConfidenceLevel(factor)
        
        return ConfidenceMetrics(
            dataPoints: dataPoints,
            timeSpan: timeSpan,
            consistency: consistency,
            factor: factor,
            level: level
        )
    }
    
    private static func calculateConsistency(_ records: [UsageRecord]) -> Double {
        guard records.count >= 3 else { return 0.5 }
        
        let satisfactions = records.map { Double($0.satisfaction) }
        let mean = satisfactions.reduce(0, +) / Double(satisfactions.count)
        let variance = satisfactions.map { pow($0 - mean, 2) }.reduce(0, +) / Double(satisfactions.count)
        let standardDeviation = sqrt(variance)
        
        // 标准差越小，一致性越高 (满意度范围1-5，标准差2.5为最大)
        return max(0, 1.0 - standardDeviation / 2.5)
    }
    
    private static func calculateConfidenceFactor(
        dataPoints: Int, 
        timeSpan: TimeInterval, 
        consistency: Double
    ) -> Double {
        // 数据点因子：10个数据点为满分
        let dataFactor = min(1.0, Double(dataPoints) / 10.0)
        
        // 时间因子：90天为满分
        let timeFactor = min(1.0, timeSpan / (90 * 24 * 3600))
        
        // 一致性因子
        let consistencyFactor = consistency
        
        // 加权平均
        return (dataFactor * 0.4 + timeFactor * 0.3 + consistencyFactor * 0.3)
    }
    
    private static func determineConfidenceLevel(_ factor: Double) -> ConfidenceLevel {
        for level in ConfidenceLevel.allCases {
            if factor >= level.threshold {
                return level
            }
        }
        return .veryLow
    }
}

// MARK: - 类别基准系统
struct CategoryBenchmark {
    let medianPrice: Double
    let averageUsageFrequency: Double
    let averageSatisfaction: Double
    let priceStandardDeviation: Double
    let usageStandardDeviation: Double
    let productCount: Int
    
    static let defaultBenchmark = CategoryBenchmark(
        medianPrice: 100.0,
        averageUsageFrequency: 5.0,
        averageSatisfaction: 3.5,
        priceStandardDeviation: 50.0,
        usageStandardDeviation: 2.0,
        productCount: 0
    )
    
    static func calculate(for category: Category?, products: [Product]) -> CategoryBenchmark {
        let categoryProducts: [Product]
        
        if let category = category {
            categoryProducts = products.filter { $0.category == category }
        } else {
            // 如果没有类别，使用所有产品作为基准
            categoryProducts = products
        }
        
        guard !categoryProducts.isEmpty else {
            return defaultBenchmark
        }
        
        let prices = categoryProducts.map { $0.price }
        let usageFreqs = categoryProducts.map { $0.monthlyUsageFrequency }
        let satisfactions = categoryProducts.map { $0.averageSatisfaction }
        
        return CategoryBenchmark(
            medianPrice: prices.median(),
            averageUsageFrequency: usageFreqs.average(),
            averageSatisfaction: satisfactions.average(),
            priceStandardDeviation: prices.standardDeviation(),
            usageStandardDeviation: usageFreqs.standardDeviation(),
            productCount: categoryProducts.count
        )
    }
}

// MARK: - 数组扩展：统计计算
extension Array where Element == Double {
    func average() -> Double {
        guard !isEmpty else { return 0 }
        return reduce(0, +) / Double(count)
    }
    
    func median() -> Double {
        guard !isEmpty else { return 0 }
        let sorted = self.sorted()
        let count = sorted.count
        
        if count % 2 == 0 {
            return (sorted[count/2 - 1] + sorted[count/2]) / 2.0
        } else {
            return sorted[count/2]
        }
    }
    
    func standardDeviation() -> Double {
        guard count > 1 else { return 0 }
        let mean = average()
        let variance = map { pow($0 - mean, 2) }.reduce(0, +) / Double(count - 1)
        return sqrt(variance)
    }
}

// MARK: - 数学工具函数
func sigmoid(_ x: Double) -> Double {
    return 1.0 / (1.0 + exp(-x))
}

func calculateTrendSlope(_ values: [Double]) -> Double {
    guard values.count >= 2 else { return 0 }
    
    let n = Double(values.count)
    let x = Array(0..<values.count).map(Double.init)
    let y = values
    
    let sumX = x.reduce(0, +)
    let sumY = y.reduce(0, +)
    let sumXY = zip(x, y).map(*).reduce(0, +)
    let sumX2 = x.map { $0 * $0 }.reduce(0, +)
    
    let denominator = n * sumX2 - sumX * sumX
    guard denominator != 0 else { return 0 }
    
    let slope = (n * sumXY - sumX * sumY) / denominator
    return slope
}
