import Foundation
import CoreData

// 产品关系辅助方法
extension ProductRelationship {
    // 从CoreData中加载产品关系
    static func loadRelationships(for product: Product, context: NSManagedObjectContext) -> [ProductRelationship] {
        var relationships = [ProductRelationship]()
        
        // 获取产品的关联产品
        if let relatedProducts = product.relatedProducts?.allObjects as? [Product] {
            for relatedProduct in relatedProducts {
                // 这里我们使用一个默认的关系类型，因为CoreData模型中没有存储关系类型
                // 在实际应用中，可以通过UserDefaults或其他方式存储关系类型信息
                let relationship = ProductRelationship(
                    sourceProduct: product,
                    targetProduct: relatedProduct,
                    relationshipType: .accessory
                )
                relationships.append(relationship)
            }
        }
        
        return relationships
    }
    
    // 保存产品关系到CoreData
    static func saveRelationship(_ relationship: ProductRelationship, context: NSManagedObjectContext) -> Bool {
        // 将源产品和目标产品关联起来
        relationship.sourceProduct.addToRelatedProducts(relationship.targetProduct)
        
        // 保存关系类型信息到UserDefaults
        let key = "relationship_\(relationship.sourceProduct.id?.uuidString ?? "")_\(relationship.targetProduct.id?.uuidString ?? "")"
        UserDefaults.standard.set(relationship.relationshipType.rawValue, forKey: key)
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("保存产品关系失败: \(error)")
            return false
        }
    }
    
    // 删除产品关系
    static func deleteRelationship(_ relationship: ProductRelationship, context: NSManagedObjectContext) -> Bool {
        // 移除源产品和目标产品的关联
        relationship.sourceProduct.removeFromRelatedProducts(relationship.targetProduct)
        
        // 删除UserDefaults中的关系类型信息
        let key = "relationship_\(relationship.sourceProduct.id?.uuidString ?? "")_\(relationship.targetProduct.id?.uuidString ?? "")"
        UserDefaults.standard.removeObject(forKey: key)
        
        // 保存上下文
        do {
            try context.save()
            return true
        } catch {
            print("删除产品关系失败: \(error)")
            return false
        }
    }
    
    // 获取产品关系类型
    static func getRelationshipType(sourceProductId: UUID, targetProductId: UUID) -> ProductRelationshipType {
        let key = "relationship_\(sourceProductId.uuidString)_\(targetProductId.uuidString)"
        if let typeString = UserDefaults.standard.string(forKey: key),
           let type = ProductRelationshipType(rawValue: typeString) {
            return type
        }
        return .other
    }
}
