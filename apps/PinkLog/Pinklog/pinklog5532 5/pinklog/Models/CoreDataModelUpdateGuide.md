# CoreData 模型更新指南 - 增强产品关联功能

本指南介绍如何更新 CoreData 模型以支持增强版的产品关联功能。

## 新增 ProductLink 实体

在 CoreData 模型编辑器中（pinklog.xcdatamodeld）创建一个新的实体：

1. 打开 pinklog.xcdatamodeld 文件
2. 点击 "Add Entity" 按钮
3. 将新实体命名为 `ProductLink`

## 实体属性

为 ProductLink 实体添加以下属性：

| 属性名 | 类型 | 描述 | 默认值 |
|-------|------|------|-------|
| id | UUID | 链接的唯一标识符 | (无) |
| relationshipType | String | 关系类型的字符串表示 | "other" |
| notes | String | 关于关系的笔记或说明 | (无) |
| createdAt | Date | 关系创建时间 | (当前日期) |
| strength | Integer 16 | 关系强度 (1-5) | 3 |
| isBidirectional | Boolean | 是否为双向关系 | false |
| usageCount | Integer 32 | 这两个产品一起使用的次数 | 0 |
| lastUsedTogether | Date | 最后一次一起使用的日期 | (无) |
| usageScenario | String | 常见的共同使用场景 | (无) |

## 实体关系

为 ProductLink 实体添加以下关系：

| 关系名 | 目标实体 | 关系类型 | 描述 |
|-------|---------|----------|------|
| sourceProduct | Product | To-One | 关系的源产品 |
| targetProduct | Product | To-One | 关系的目标产品 |

## Product 实体更新

修改现有的 Product 实体：

1. 添加一个新的 To-Many 关系 `outgoingLinks`，指向 ProductLink 实体，反向关系为 sourceProduct
2. 添加一个新的 To-Many 关系 `incomingLinks`，指向 ProductLink 实体，反向关系为 targetProduct

## 数据迁移考虑

由于我们正在引入一个新的实体和关系结构，需要从旧的关系模型（使用 relatedProducts）迁移：

1. 在应用程序更新后，添加一个迁移助手类来转换现有的关系
2. 迁移助手应该遍历所有产品，检查其 relatedProducts 关系，并为每个关系创建一个新的 ProductLink 实体
3. 可以使用 UserDefaults 中的关系类型信息（如果存在）来设置新 ProductLink 的 relationshipType

## 实施后续步骤

完成 CoreData 模型更新后：

1. 在 Xcode 中生成 NSManagedObject 子类
2. 更新所有使用 relatedProducts 关系的视图和视图模型
3. 实现 ProductLinkViewModel 来管理这些关系
4. 创建新的 UI 组件来显示和管理增强的关系

## 数据模型图示

```
Product <──┐       ┌─> Product
            │       │
            └─> ProductLink ─┘
               ├─ id (UUID)
               ├─ relationshipType (String)
               ├─ notes (String)
               ├─ strength (Int16)
               ├─ isBidirectional (Bool)
               ├─ usageCount (Int32)
               └─ lastUsedTogether (Date)
```

## 注意事项

- 所有新增的属性都应该是可选的，以便向后兼容
- 删除规则应设为 Cascade，这样当产品被删除时，相关的链接也会被删除
- 所有持久化的日期属性都需要考虑不同时区的问题