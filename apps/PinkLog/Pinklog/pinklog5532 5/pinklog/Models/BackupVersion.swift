import Foundation
import CloudKit
import UIKit

// MARK: - 备份版本状态

/// 备份版本状态枚举
enum BackupVersionStatus: String, Codable, CaseIterable {
    case creating = "creating"       // 创建中
    case completed = "completed"     // 已完成
    case failed = "failed"          // 失败
    case corrupted = "corrupted"    // 损坏
    case deleted = "deleted"        // 已删除
    
    var displayName: String {
        switch self {
        case .creating:
            return "创建中"
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        case .corrupted:
            return "损坏"
        case .deleted:
            return "已删除"
        }
    }
    
    var isAvailable: Bool {
        return self == .completed
    }
}

// MARK: - 备份版本类型

/// 备份版本类型枚举
enum BackupVersionType: String, Codable, CaseIterable {
    case full = "full"              // 完整备份
    case incremental = "incremental" // 增量备份
    case manual = "manual"          // 手动备份
    case automatic = "automatic"    // 自动备份
    
    var displayName: String {
        switch self {
        case .full:
            return "完整备份"
        case .incremental:
            return "增量备份"
        case .manual:
            return "手动备份"
        case .automatic:
            return "自动备份"
        }
    }
    
    var icon: String {
        switch self {
        case .full:
            return "externaldrive.fill"
        case .incremental:
            return "externaldrive.badge.plus"
        case .manual:
            return "hand.tap"
        case .automatic:
            return "clock.arrow.circlepath"
        }
    }
}

// MARK: - 备份版本数据模型

/// 备份版本数据模型
struct BackupVersion: Codable, Identifiable, Equatable {
    let id: UUID
    let createdAt: Date
    let completedAt: Date?
    let type: BackupVersionType
    let status: BackupVersionStatus
    
    // 备份内容信息
    let totalEntities: Int
    let totalBinaryAssets: Int
    let estimatedSize: Int64
    let dataChecksum: String
    
    // 设备信息
    let deviceModel: String
    let systemVersion: String
    let appVersion: String
    let deviceId: String
    
    // CloudKit信息
    let cloudKitRecordId: String?
    let cloudKitZoneId: String?
    
    // 备份统计
    let backupDuration: TimeInterval?
    let compressionRatio: Double?
    let errorCount: Int
    let warningCount: Int
    
    // 版本描述
    let description: String?
    let tags: [String]
    
    // 依赖关系
    let parentVersionId: UUID?  // 增量备份的父版本
    let childVersionIds: [UUID] // 子版本（增量备份）
    
    init(
        id: UUID = UUID(),
        type: BackupVersionType,
        status: BackupVersionStatus = .creating,
        totalEntities: Int = 0,
        totalBinaryAssets: Int = 0,
        estimatedSize: Int64 = 0,
        dataChecksum: String = "",
        deviceModel: String = UIDevice.current.model,
        systemVersion: String = UIDevice.current.systemVersion,
        appVersion: String = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
        deviceId: String = UIDevice.current.identifierForVendor?.uuidString ?? "Unknown",
        cloudKitRecordId: String? = nil,
        cloudKitZoneId: String? = nil,
        backupDuration: TimeInterval? = nil,
        compressionRatio: Double? = nil,
        errorCount: Int = 0,
        warningCount: Int = 0,
        description: String? = nil,
        tags: [String] = [],
        parentVersionId: UUID? = nil,
        childVersionIds: [UUID] = []
    ) {
        self.id = id
        self.createdAt = Date()
        self.completedAt = nil
        self.type = type
        self.status = status
        self.totalEntities = totalEntities
        self.totalBinaryAssets = totalBinaryAssets
        self.estimatedSize = estimatedSize
        self.dataChecksum = dataChecksum
        self.deviceModel = deviceModel
        self.systemVersion = systemVersion
        self.appVersion = appVersion
        self.deviceId = deviceId
        self.cloudKitRecordId = cloudKitRecordId
        self.cloudKitZoneId = cloudKitZoneId
        self.backupDuration = backupDuration
        self.compressionRatio = compressionRatio
        self.errorCount = errorCount
        self.warningCount = warningCount
        self.description = description
        self.tags = tags
        self.parentVersionId = parentVersionId
        self.childVersionIds = childVersionIds
    }
    
    // MARK: - 计算属性
    
    /// 格式化的文件大小
    var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: estimatedSize, countStyle: .file)
    }
    
    /// 备份完成时间
    var completionTime: TimeInterval? {
        guard let completedAt = completedAt else { return nil }
        return completedAt.timeIntervalSince(createdAt)
    }
    
    /// 格式化的备份时长
    var formattedDuration: String? {
        guard let duration = backupDuration else { return nil }
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration)
    }
    
    /// 是否可以恢复
    var canRestore: Bool {
        return status.isAvailable && cloudKitRecordId != nil
    }
    
    /// 是否可以删除
    var canDelete: Bool {
        return status != .creating && status != .deleted
    }
    
    /// 版本摘要信息
    var summary: String {
        var components: [String] = []
        components.append(type.displayName)
        components.append("\(totalEntities)个实体")
        if totalBinaryAssets > 0 {
            components.append("\(totalBinaryAssets)个资源")
        }
        components.append(formattedSize)
        return components.joined(separator: " • ")
    }
    
    // MARK: - 状态更新方法
    
    /// 标记为完成
    func markAsCompleted(
        completedAt: Date = Date(),
        backupDuration: TimeInterval,
        finalChecksum: String,
        cloudKitRecordId: String? = nil
    ) -> BackupVersion {
        return BackupVersion(
            id: self.id,
            type: self.type,
            status: .completed,
            totalEntities: self.totalEntities,
            totalBinaryAssets: self.totalBinaryAssets,
            estimatedSize: self.estimatedSize,
            dataChecksum: finalChecksum,
            deviceModel: self.deviceModel,
            systemVersion: self.systemVersion,
            appVersion: self.appVersion,
            deviceId: self.deviceId,
            cloudKitRecordId: cloudKitRecordId ?? self.cloudKitRecordId,
            cloudKitZoneId: self.cloudKitZoneId,
            backupDuration: backupDuration,
            compressionRatio: self.compressionRatio,
            errorCount: self.errorCount,
            warningCount: self.warningCount,
            description: self.description,
            tags: self.tags,
            parentVersionId: self.parentVersionId,
            childVersionIds: self.childVersionIds
        )
    }
    
    /// 标记为失败
    func markAsFailed(errorCount: Int = 1) -> BackupVersion {
        return BackupVersion(
            id: self.id,
            type: self.type,
            status: .failed,
            totalEntities: self.totalEntities,
            totalBinaryAssets: self.totalBinaryAssets,
            estimatedSize: self.estimatedSize,
            dataChecksum: self.dataChecksum,
            deviceModel: self.deviceModel,
            systemVersion: self.systemVersion,
            appVersion: self.appVersion,
            deviceId: self.deviceId,
            cloudKitRecordId: self.cloudKitRecordId,
            cloudKitZoneId: self.cloudKitZoneId,
            backupDuration: self.backupDuration,
            compressionRatio: self.compressionRatio,
            errorCount: errorCount,
            warningCount: self.warningCount,
            description: self.description,
            tags: self.tags,
            parentVersionId: self.parentVersionId,
            childVersionIds: self.childVersionIds
        )
    }
    
    // MARK: - Equatable
    
    static func == (lhs: BackupVersion, rhs: BackupVersion) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 备份版本查询条件

/// 备份版本查询条件
struct BackupVersionQuery {
    let status: BackupVersionStatus?
    let type: BackupVersionType?
    let deviceId: String?
    let dateRange: ClosedRange<Date>?
    let limit: Int?
    let sortBy: SortOption
    
    enum SortOption {
        case createdAtDescending
        case createdAtAscending
        case sizeDescending
        case sizeAscending
        case typeAndDate
    }
    
    static let `default` = BackupVersionQuery(
        status: nil,
        type: nil,
        deviceId: nil,
        dateRange: nil,
        limit: 50,
        sortBy: .createdAtDescending
    )
}

// MARK: - 备份版本统计

/// 备份版本统计信息
struct BackupVersionStatistics {
    let totalVersions: Int
    let completedVersions: Int
    let failedVersions: Int
    let totalSize: Int64
    let averageSize: Int64
    let oldestVersion: Date?
    let newestVersion: Date?
    let totalBackupTime: TimeInterval
    let averageBackupTime: TimeInterval
    
    var successRate: Double {
        guard totalVersions > 0 else { return 0.0 }
        return Double(completedVersions) / Double(totalVersions)
    }
    
    var formattedTotalSize: String {
        return ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
    
    var formattedAverageSize: String {
        return ByteCountFormatter.string(fromByteCount: averageSize, countStyle: .file)
    }
}
