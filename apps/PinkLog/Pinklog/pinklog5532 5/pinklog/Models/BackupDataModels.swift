import Foundation
import CoreData
import UIKit
import CryptoKit

// MARK: - 备份实体协议

/// 备份实体协议，定义备份数据的基本接口
protocol BackupEntity: Codable {
    var id: UUID { get }
    var lastModified: Date { get }
    var entityType: String { get }
    
    /// 验证数据完整性
    func validateData() -> Bool
}

// MARK: - Binary数据处理

/// Binary数据备份策略
enum BinaryDataStrategy: String, Codable {
    case base64 = "base64"           // 转换为Base64字符串
    case fileReference = "file_ref"  // 文件引用，用于CKAsset
    case excluded = "excluded"       // 排除不备份
}

/// Binary数据容器
struct BinaryDataContainer: Codable {
    let strategy: BinaryDataStrategy
    let data: String?              // Base64数据或文件引用
    let fileName: String?          // 原始文件名
    let mimeType: String?          // MIME类型
    let size: Int64?               // 文件大小
    let checksum: String?          // 数据校验和
    
    init(strategy: BinaryDataStrategy, data: String? = nil, fileName: String? = nil, mimeType: String? = nil, size: Int64? = nil, checksum: String? = nil) {
        self.strategy = strategy
        self.data = data
        self.fileName = fileName
        self.mimeType = mimeType
        self.size = size
        self.checksum = checksum
    }
}

// MARK: - 备份产品数据模型

/// 备份产品数据模型 - 包含Product实体的所有字段
struct BackupProduct: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Product"

    // 基本信息
    let name: String
    let brand: String?
    let model: String?
    let price: Double
    let quantity: Int

    // 购买信息
    let purchaseDate: Date
    let purchaseChannel: String?
    let purchaseMotivation: String?
    let purchaseNotes: String?

    // 产品特性
    let isVirtualProduct: Bool
    let expectedLifespan: Int
    let expectedUsageFrequency: String?
    let valuationMethod: String

    // 保修和有效期
    let warrantyEndDate: Date?
    let warrantyDetails: String?
    let warrantyImage: String?
    let expiryDate: Date?

    // 满意度
    let initialSatisfaction: Int

    // 消耗型物品相关字段
    let isConsumable: Bool
    let currentQuantity: Double?
    let unitType: String?
    let minStockAlert: Double?
    let consumptionRate: Double?
    let consumptionUnitType: String?
    let unitConversionRatio: Double?

    // 关联关系ID（用于恢复时重建关系）
    let categoryId: UUID?
    let tagIds: [UUID]
    let purchaseChannelId: UUID?
    let parentProductIds: [UUID]
    let relatedProductIds: [UUID]

    // Binary数据
    let images: BinaryDataContainer?

    func validateData() -> Bool {
        let basicValidation = !name.isEmpty && price >= 0 && quantity >= 0

        // 消耗型物品字段验证
        if isConsumable {
            let consumableValidation = (currentQuantity ?? 0) >= 0 &&
                                     (minStockAlert ?? 0) >= 0 &&
                                     (consumptionRate ?? 0) >= 0 &&
                                     (unitConversionRatio ?? 1.0) > 0
            return basicValidation && consumableValidation
        }

        return basicValidation
    }
}

// MARK: - 备份使用记录数据模型

/// 备份使用记录数据模型
struct BackupUsageRecord: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "UsageRecord"

    // 基本信息
    let date: Date
    let satisfaction: Int
    let emotionalValue: Int
    let duration: Double

    // 使用类型和场景
    let usageType: String
    let scenario: String?
    let title: String?
    let notes: String?
    let memories: String?

    // 借阅相关
    let isLoanedOut: Bool
    let borrowerName: String?
    let contactInfo: String?
    let dueDate: Date?
    let returnDate: Date?

    // 转让相关
    let recipient: String?
    let transferPrice: Double
    let transferType: String?
    let wearCondition: String?

    // 故事和回忆
    let isStory: Bool

    // 消耗量相关字段
    let consumedQuantity: Double?
    let remainingQuantity: Double?
    let consumptionUnit: String?

    // 关联关系ID
    let productId: UUID

    // Binary数据
    let images: BinaryDataContainer?
    let audioRecordings: BinaryDataContainer?

    func validateData() -> Bool {
        let basicValidation = satisfaction >= 0 && satisfaction <= 5 && emotionalValue >= 0 && emotionalValue <= 5

        // 消耗量字段验证
        if let consumed = consumedQuantity {
            let consumptionValidation = consumed >= 0 && (remainingQuantity ?? 0) >= 0
            return basicValidation && consumptionValidation
        }

        return basicValidation
    }
}

// MARK: - 备份相关费用数据模型

/// 备份相关费用数据模型
struct BackupRelatedExpense: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "RelatedExpense"
    
    let amount: Double
    let date: Date
    let notes: String?
    
    // 关联关系ID
    let productId: UUID
    let expenseTypeId: UUID
    
    func validateData() -> Bool {
        return amount >= 0
    }
}

// MARK: - 备份类别数据模型

/// 备份类别数据模型
struct BackupCategory: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Category"
    
    let name: String
    let icon: String?
    
    // 关联关系ID
    let parentCategoryId: UUID?
    
    func validateData() -> Bool {
        return !name.isEmpty
    }
}

// MARK: - 备份标签数据模型

/// 备份标签数据模型
struct BackupTag: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Tag"
    
    let name: String
    let color: String?
    let type: String?
    
    func validateData() -> Bool {
        return !name.isEmpty
    }
}

// MARK: - 备份借阅记录数据模型

/// 备份借阅记录数据模型
struct BackupLoanRecord: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "LoanRecord"
    
    let borrowerName: String
    let contactInfo: String?
    let createdAt: Date
    let dueDate: Date
    let returnDate: Date?
    let status: String
    let notes: String?
    let isLoanedOut: Bool
    
    // 关联关系ID
    let productId: UUID
    
    func validateData() -> Bool {
        return !borrowerName.isEmpty && dueDate > createdAt
    }
}

// MARK: - 备份费用类型数据模型

/// 备份费用类型数据模型
struct BackupExpenseType: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "ExpenseType"
    
    let name: String
    let icon: String?
    
    func validateData() -> Bool {
        return !name.isEmpty
    }
}

// MARK: - 备份购买渠道数据模型

/// 备份购买渠道数据模型
struct BackupPurchaseChannel: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "PurchaseChannel"
    
    let name: String
    let categoryId: UUID?
    
    func validateData() -> Bool {
        return !name.isEmpty
    }
}

// MARK: - 备份购买渠道分类数据模型

/// 备份购买渠道分类数据模型
struct BackupPurchaseChannelCategory: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "PurchaseChannelCategory"
    
    let name: String
    let icon: String?
    
    func validateData() -> Bool {
        return !name.isEmpty
    }
}

// MARK: - 备份提醒数据模型

/// 备份提醒数据模型
struct BackupReminder: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Reminder"
    
    let date: Date
    let message: String
    let reminderType: String
    let isActive: Bool
    
    // 关联关系ID（提醒可以关联多个产品）
    let productIds: [UUID]
    
    func validateData() -> Bool {
        return !message.isEmpty && !productIds.isEmpty
    }
}

// MARK: - 备份产品关联数据模型

/// 备份产品关联数据模型
struct BackupProductLink: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "ProductLink"
    
    let relationshipType: String
    let notes: String?
    let createdAt: Date
    let strength: Int
    let isBidirectional: Bool
    let usageCount: Int
    let lastUsedTogether: Date?
    let usageScenario: String?
    let groupID: String?
    
    // 关联关系ID
    let sourceProductId: UUID
    let targetProductId: UUID
    
    func validateData() -> Bool {
        return strength >= 1 && strength <= 5 && usageCount >= 0
    }
}

// MARK: - 备份对话数据模型

/// 备份对话数据模型
struct BackupConversation: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Conversation"
    
    let title: String
    let startDate: Date
    let lastMessageDate: Date
    
    func validateData() -> Bool {
        return !title.isEmpty && lastMessageDate >= startDate
    }
}

// MARK: - 备份消息数据模型

/// 备份消息数据模型
struct BackupMessage: BackupEntity {
    let id: UUID
    let lastModified: Date
    let entityType: String = "Message"
    
    let text: String
    let timestamp: Date
    let isFromUser: Bool
    let role: String
    
    // 关联关系ID
    let conversationId: UUID
    
    // Binary数据
    let analysisContextData: BinaryDataContainer?
    let attachmentsData: BinaryDataContainer?
    
    func validateData() -> Bool {
        return !text.isEmpty && !role.isEmpty
    }
}

// MARK: - 备份数据容器

/// 备份数据容器 - 顶层容器，包含所有备份数据
struct BackupDataContainer: Codable {
    let version: String
    let createdAt: Date
    let deviceInfo: BackupDeviceInfo
    let backupType: BackupType
    let dataChecksum: String

    // 所有实体数据
    let products: [BackupProduct]
    let usageRecords: [BackupUsageRecord]
    let relatedExpenses: [BackupRelatedExpense]
    let categories: [BackupCategory]
    let tags: [BackupTag]
    let loanRecords: [BackupLoanRecord]
    let expenseTypes: [BackupExpenseType]
    let purchaseChannels: [BackupPurchaseChannel]
    let purchaseChannelCategories: [BackupPurchaseChannelCategory]
    let reminders: [BackupReminder]
    let productLinks: [BackupProductLink]
    let conversations: [BackupConversation]
    let messages: [BackupMessage]

    // 统计信息
    let totalEntities: Int
    let totalBinaryAssets: Int
    let estimatedSize: Int64

    // 计算属性
    var formattedTotalSize: String {
        return ByteCountFormatter.string(fromByteCount: estimatedSize, countStyle: .file)
    }

    init(
        version: String = "1.0",
        backupType: BackupType,
        products: [BackupProduct] = [],
        usageRecords: [BackupUsageRecord] = [],
        relatedExpenses: [BackupRelatedExpense] = [],
        categories: [BackupCategory] = [],
        tags: [BackupTag] = [],
        loanRecords: [BackupLoanRecord] = [],
        expenseTypes: [BackupExpenseType] = [],
        purchaseChannels: [BackupPurchaseChannel] = [],
        purchaseChannelCategories: [BackupPurchaseChannelCategory] = [],
        reminders: [BackupReminder] = [],
        productLinks: [BackupProductLink] = [],
        conversations: [BackupConversation] = [],
        messages: [BackupMessage] = []
    ) {
        self.version = version
        self.createdAt = Date()
        self.deviceInfo = BackupDeviceInfo()
        self.backupType = backupType

        self.products = products
        self.usageRecords = usageRecords
        self.relatedExpenses = relatedExpenses
        self.categories = categories
        self.tags = tags
        self.loanRecords = loanRecords
        self.expenseTypes = expenseTypes
        self.purchaseChannels = purchaseChannels
        self.purchaseChannelCategories = purchaseChannelCategories
        self.reminders = reminders
        self.productLinks = productLinks
        self.conversations = conversations
        self.messages = messages

        // 计算统计信息
        self.totalEntities = products.count + usageRecords.count + relatedExpenses.count +
                           categories.count + tags.count + loanRecords.count +
                           expenseTypes.count + purchaseChannels.count + purchaseChannelCategories.count +
                           reminders.count + productLinks.count + conversations.count + messages.count

        // 计算Binary资源数量
        var binaryCount = 0
        binaryCount += products.compactMap { $0.images }.count
        binaryCount += usageRecords.compactMap { $0.images }.count
        binaryCount += usageRecords.compactMap { $0.audioRecordings }.count
        binaryCount += messages.compactMap { $0.analysisContextData }.count
        binaryCount += messages.compactMap { $0.attachmentsData }.count
        self.totalBinaryAssets = binaryCount

        // 计算实际序列化大小
        self.estimatedSize = Self.calculateActualSize(
            version: version,
            createdAt: createdAt,
            deviceInfo: deviceInfo,
            backupType: backupType,
            products: products,
            usageRecords: usageRecords,
            relatedExpenses: relatedExpenses,
            categories: categories,
            tags: tags,
            loanRecords: loanRecords,
            expenseTypes: expenseTypes,
            purchaseChannels: purchaseChannels,
            purchaseChannelCategories: purchaseChannelCategories,
            reminders: reminders,
            productLinks: productLinks,
            conversations: conversations,
            messages: messages,
            totalEntities: totalEntities,
            totalBinaryAssets: binaryCount
        )

        // 计算校验和（不包含校验和字段本身）
        self.dataChecksum = Self.generateChecksumForData(
            version: version,
            createdAt: createdAt,
            deviceInfo: deviceInfo,
            backupType: backupType,
            products: products,
            usageRecords: usageRecords,
            relatedExpenses: relatedExpenses,
            categories: categories,
            tags: tags,
            loanRecords: loanRecords,
            expenseTypes: expenseTypes,
            purchaseChannels: purchaseChannels,
            purchaseChannelCategories: purchaseChannelCategories,
            reminders: reminders,
            productLinks: productLinks,
            conversations: conversations,
            messages: messages,
            totalEntities: totalEntities,
            totalBinaryAssets: binaryCount,
            estimatedSize: estimatedSize
        )
    }

    /// 验证备份数据完整性
    func validateBackup() -> BackupValidationResult {
        return validateBackup(allowCrossDeviceValidation: false)
    }
    
    /// 验证备份数据完整性（增强跨设备兼容性）
    func validateBackup(allowCrossDeviceValidation: Bool) -> BackupValidationResult {
        var errors: [String] = []
        var warnings: [String] = []

        // 验证版本兼容性
        if !isVersionCompatible(version) {
            if allowCrossDeviceValidation {
                warnings.append("备份版本差异: \(version)，尝试兼容性导入")
            } else {
                errors.append("备份版本不兼容: \(version)")
            }
        }

        // 验证设备兼容性
        if allowCrossDeviceValidation {
            let deviceCompatibility = validateDeviceCompatibility()
            warnings.append(contentsOf: deviceCompatibility)
        }

        // 验证实体数据
        var allEntities: [BackupEntity] = []
        allEntities.append(contentsOf: products)
        allEntities.append(contentsOf: usageRecords)
        allEntities.append(contentsOf: relatedExpenses)
        allEntities.append(contentsOf: categories)
        allEntities.append(contentsOf: tags)
        allEntities.append(contentsOf: loanRecords)
        allEntities.append(contentsOf: expenseTypes)
        allEntities.append(contentsOf: purchaseChannels)
        allEntities.append(contentsOf: purchaseChannelCategories)
        allEntities.append(contentsOf: reminders)
        allEntities.append(contentsOf: productLinks)
        allEntities.append(contentsOf: conversations)
        allEntities.append(contentsOf: messages)

        // 批量验证实体数据，并记录具体错误
        let entityValidationResult = validateEntitiesInBatches(allEntities, allowPartialFailure: allowCrossDeviceValidation)
        errors.append(contentsOf: entityValidationResult.errors)
        warnings.append(contentsOf: entityValidationResult.warnings)

        // 验证关系完整性（跨设备模式下更宽松）
        let relationshipErrors = validateRelationships(allowPartialRelationships: allowCrossDeviceValidation)
        if allowCrossDeviceValidation {
            warnings.append(contentsOf: relationshipErrors)
        } else {
            errors.append(contentsOf: relationshipErrors)
        }

        // 验证数据校验和（跨设备模式下不强制）
        let currentChecksum = Self.generateChecksum(for: self)
        if currentChecksum != dataChecksum {
            if allowCrossDeviceValidation {
                warnings.append("数据校验和不匹配，跨设备导入时属于正常情况")
            } else {
                warnings.append("数据校验和不匹配，可能存在数据损坏")
            }
        }

        return BackupValidationResult(
            isValid: errors.isEmpty,
            errors: errors,
            warnings: warnings,
            totalEntities: totalEntities,
            validatedAt: Date()
        )
    }
    
    /// 验证设备兼容性
    private func validateDeviceCompatibility() -> [String] {
        var warnings: [String] = []
        
        let currentDeviceInfo = BackupDeviceInfo()
        
        // 检查iOS版本差异
        if deviceInfo.systemVersion != currentDeviceInfo.systemVersion {
            warnings.append("iOS版本差异：备份来源 \(deviceInfo.systemVersion)，当前设备 \(currentDeviceInfo.systemVersion)")
        }
        
        // 检查应用版本差异
        if deviceInfo.appVersion != currentDeviceInfo.appVersion {
            warnings.append("应用版本差异：备份来源 \(deviceInfo.appVersion)，当前设备 \(currentDeviceInfo.appVersion)")
        }
        
        // 检查设备类型
        if deviceInfo.deviceModel != currentDeviceInfo.deviceModel {
            warnings.append("设备型号差异：备份来源 \(deviceInfo.deviceModel)，当前设备 \(currentDeviceInfo.deviceModel)")
        }
        
        return warnings
    }
    
    /// 批量验证实体数据
    private func validateEntitiesInBatches(_ entities: [BackupEntity], allowPartialFailure: Bool) -> (errors: [String], warnings: [String]) {
        var errors: [String] = []
        var warnings: [String] = []
        
        let batchSize = 100
        let totalBatches = (entities.count + batchSize - 1) / batchSize
        
        for batchIndex in 0..<totalBatches {
            let startIndex = batchIndex * batchSize
            let endIndex = min(startIndex + batchSize, entities.count)
            let batch = Array(entities[startIndex..<endIndex])
            
            for entity in batch {
                if !entity.validateData() {
                    let errorMsg = "实体数据验证失败: \(entity.entityType) - \(entity.id)"
                    if allowPartialFailure {
                        warnings.append(errorMsg + "（将跳过此实体）")
                    } else {
                        errors.append(errorMsg)
                    }
                }
            }
        }
        
        return (errors, warnings)
    }

    /// 验证实体间关系完整性
    private func validateRelationships() -> [String] {
        return validateRelationships(allowPartialRelationships: false)
    }
    
    /// 验证实体间关系完整性（支持部分关系缺失）
    private func validateRelationships(allowPartialRelationships: Bool) -> [String] {
        var errors: [String] = []

        // 验证产品-类别关系
        let categoryIds = Set(categories.map { $0.id })
        for product in products {
            if let categoryId = product.categoryId, !categoryIds.contains(categoryId) {
                let errorMsg = "产品 \(product.id) 引用了不存在的类别 \(categoryId)"
                if allowPartialRelationships {
                    errors.append(errorMsg + "（将使用默认类别）")
                } else {
                    errors.append(errorMsg)
                }
            }
        }

        // 验证使用记录-产品关系
        let productIds = Set(products.map { $0.id })
        for record in usageRecords {
            if !productIds.contains(record.productId) {
                let errorMsg = "使用记录 \(record.id) 引用了不存在的产品 \(record.productId)"
                if allowPartialRelationships {
                    errors.append(errorMsg + "（将跳过此记录）")
                } else {
                    errors.append(errorMsg)
                }
            }
        }

        // 验证费用-产品关系
        for expense in relatedExpenses {
            if !productIds.contains(expense.productId) {
                let errorMsg = "费用记录 \(expense.id) 引用了不存在的产品 \(expense.productId)"
                if allowPartialRelationships {
                    errors.append(errorMsg + "（将跳过此记录）")
                } else {
                    errors.append(errorMsg)
                }
            }
        }

        // 验证借阅记录-产品关系
        for loan in loanRecords {
            if !productIds.contains(loan.productId) {
                let errorMsg = "借阅记录 \(loan.id) 引用了不存在的产品 \(loan.productId)"
                if allowPartialRelationships {
                    errors.append(errorMsg + "（将跳过此记录）")
                } else {
                    errors.append(errorMsg)
                }
            }
        }

        return errors
    }

    /// 生成数据校验和（用于验证）
    private static func generateChecksum(for container: BackupDataContainer) -> String {
        return generateChecksumForData(
            version: container.version,
            createdAt: container.createdAt,
            deviceInfo: container.deviceInfo,
            backupType: container.backupType,
            products: container.products,
            usageRecords: container.usageRecords,
            relatedExpenses: container.relatedExpenses,
            categories: container.categories,
            tags: container.tags,
            loanRecords: container.loanRecords,
            expenseTypes: container.expenseTypes,
            purchaseChannels: container.purchaseChannels,
            purchaseChannelCategories: container.purchaseChannelCategories,
            reminders: container.reminders,
            productLinks: container.productLinks,
            conversations: container.conversations,
            messages: container.messages,
            totalEntities: container.totalEntities,
            totalBinaryAssets: container.totalBinaryAssets,
            estimatedSize: container.estimatedSize
        )
    }

    /// 生成数据校验和（用于初始化）
    private static func generateChecksumForData(
        version: String,
        createdAt: Date,
        deviceInfo: BackupDeviceInfo,
        backupType: BackupType,
        products: [BackupProduct],
        usageRecords: [BackupUsageRecord],
        relatedExpenses: [BackupRelatedExpense],
        categories: [BackupCategory],
        tags: [BackupTag],
        loanRecords: [BackupLoanRecord],
        expenseTypes: [BackupExpenseType],
        purchaseChannels: [BackupPurchaseChannel],
        purchaseChannelCategories: [BackupPurchaseChannelCategory],
        reminders: [BackupReminder],
        productLinks: [BackupProductLink],
        conversations: [BackupConversation],
        messages: [BackupMessage],
        totalEntities: Int,
        totalBinaryAssets: Int,
        estimatedSize: Int64
    ) -> String {
        // 简化的校验和生成，实际应用中可以使用更复杂的算法
        let data = "\(version)-\(totalEntities)-\(totalBinaryAssets)-\(createdAt.timeIntervalSince1970)"
        return data.sha256
    }

    /// 检查版本兼容性
    private func isVersionCompatible(_ version: String) -> Bool {
        // 简单的版本检查，实际应用中需要更复杂的版本兼容性逻辑
        let supportedVersions = ["1.0"]
        return supportedVersions.contains(version)
    }
    
    /// 计算实际序列化大小
    private static func calculateActualSize(
        version: String,
        createdAt: Date,
        deviceInfo: BackupDeviceInfo,
        backupType: BackupType,
        products: [BackupProduct],
        usageRecords: [BackupUsageRecord],
        relatedExpenses: [BackupRelatedExpense],
        categories: [BackupCategory],
        tags: [BackupTag],
        loanRecords: [BackupLoanRecord],
        expenseTypes: [BackupExpenseType],
        purchaseChannels: [BackupPurchaseChannel],
        purchaseChannelCategories: [BackupPurchaseChannelCategory],
        reminders: [BackupReminder],
        productLinks: [BackupProductLink],
        conversations: [BackupConversation],
        messages: [BackupMessage],
        totalEntities: Int,
        totalBinaryAssets: Int
    ) -> Int64 {
        do {
            // 创建临时结构体用于序列化计算大小
            struct TempContainer: Codable {
                let version: String
                let createdAt: Date
                let deviceInfo: BackupDeviceInfo
                let backupType: BackupType
                let products: [BackupProduct]
                let usageRecords: [BackupUsageRecord]
                let relatedExpenses: [BackupRelatedExpense]
                let categories: [BackupCategory]
                let tags: [BackupTag]
                let loanRecords: [BackupLoanRecord]
                let expenseTypes: [BackupExpenseType]
                let purchaseChannels: [BackupPurchaseChannel]
                let purchaseChannelCategories: [BackupPurchaseChannelCategory]
                let reminders: [BackupReminder]
                let productLinks: [BackupProductLink]
                let conversations: [BackupConversation]
                let messages: [BackupMessage]
                let totalEntities: Int
                let totalBinaryAssets: Int
            }
            
            let tempContainer = TempContainer(
                version: version,
                createdAt: createdAt,
                deviceInfo: deviceInfo,
                backupType: backupType,
                products: products,
                usageRecords: usageRecords,
                relatedExpenses: relatedExpenses,
                categories: categories,
                tags: tags,
                loanRecords: loanRecords,
                expenseTypes: expenseTypes,
                purchaseChannels: purchaseChannels,
                purchaseChannelCategories: purchaseChannelCategories,
                reminders: reminders,
                productLinks: productLinks,
                conversations: conversations,
                messages: messages,
                totalEntities: totalEntities,
                totalBinaryAssets: totalBinaryAssets
            )
            
            // 序列化并计算实际大小
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let jsonData = try encoder.encode(tempContainer)
            return Int64(jsonData.count)
        } catch {
            // 如果序列化失败，使用简单估算
            return Int64(totalEntities * 1024)
        }
    }
}

// MARK: - 备份类型

/// 备份类型枚举
enum BackupType: String, Codable {
    case full = "full"              // 完整备份
    case incremental = "incremental" // 增量备份
    case manual = "manual"          // 手动备份
    case automatic = "automatic"    // 自动备份
}

// MARK: - 设备信息

/// 备份设备信息
struct BackupDeviceInfo: Codable {
    let deviceModel: String
    let systemVersion: String
    let appVersion: String
    let deviceId: String

    init() {
        self.deviceModel = UIDevice.current.model
        self.systemVersion = UIDevice.current.systemVersion
        self.appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        self.deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "Unknown"
    }
}

// MARK: - 验证结果

/// 备份验证结果
struct BackupValidationResult: Codable {
    let isValid: Bool
    let errors: [String]
    let warnings: [String]
    let totalEntities: Int
    let validatedAt: Date

    var hasWarnings: Bool {
        return !warnings.isEmpty
    }

    var summary: String {
        if isValid {
            return hasWarnings ? "验证通过，但有 \(warnings.count) 个警告" : "验证通过"
        } else {
            return "验证失败，\(errors.count) 个错误"
        }
    }
}

// MARK: - 扩展工具

extension String {
    /// 计算字符串的SHA256哈希值
    var sha256: String {
        let data = Data(self.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
}

// MARK: - JSON序列化扩展

extension BackupDataContainer {
    /// 将备份数据容器序列化为JSON数据
    func toJSONData() throws -> Data {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        return try encoder.encode(self)
    }

    /// 从JSON数据反序列化备份数据容器
    static func fromJSONData(_ data: Data) throws -> BackupDataContainer {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(BackupDataContainer.self, from: data)
    }

    /// 将备份数据容器保存为JSON文件
    func saveToFile(at url: URL) throws {
        let data = try toJSONData()
        try data.write(to: url)
    }

    /// 从JSON文件加载备份数据容器
    static func loadFromFile(at url: URL) throws -> BackupDataContainer {
        let data = try Data(contentsOf: url)
        return try fromJSONData(data)
    }
}

// MARK: - Core Data转换扩展

extension BackupProduct {
    /// 从Core Data Product实体创建备份数据
    static func fromCoreData(_ product: Product) -> BackupProduct {
        // 处理图片数据
        var imagesContainer: BinaryDataContainer?
        if let imageData = product.images {
            // 将Binary数据转换为Base64
            let base64String = imageData.base64EncodedString()
            imagesContainer = BinaryDataContainer(
                strategy: .base64,
                data: base64String,
                fileName: "product_images_\(product.id?.uuidString ?? "unknown").data",
                mimeType: "application/octet-stream",
                size: Int64(imageData.count),
                checksum: base64String.sha256
            )
        }

        return BackupProduct(
            id: product.id ?? UUID(),
            lastModified: Date(), // 实际应用中应该从Core Data获取
            name: product.name ?? "",
            brand: product.brand,
            model: product.model,
            price: product.price,
            quantity: Int(product.quantity),
            purchaseDate: product.purchaseDate ?? Date(),
            purchaseChannel: product.purchaseChannelRelation?.name,
            purchaseMotivation: product.purchaseMotivation,
            purchaseNotes: product.purchaseNotes,
            isVirtualProduct: product.isVirtualProduct,
            expectedLifespan: Int(product.expectedLifespan),
            expectedUsageFrequency: product.expectedUsageFrequency,
            valuationMethod: product.valuationMethod ?? "market",
            warrantyEndDate: product.warrantyEndDate,
            warrantyDetails: product.warrantyDetails,
            warrantyImage: product.warrantyImage,
            expiryDate: product.expiryDate,
            initialSatisfaction: Int(product.initialSatisfaction),
            isConsumable: product.isConsumable,
            currentQuantity: product.isConsumable ? product.currentQuantity : nil,
            unitType: product.isConsumable ? product.unitType : nil,
            minStockAlert: product.isConsumable ? product.minStockAlert : nil,
            consumptionRate: product.isConsumable ? product.consumptionRate : nil,
            consumptionUnitType: product.isConsumable ? product.consumptionUnitType : nil,
            unitConversionRatio: product.isConsumable ? product.unitConversionRatio : nil,
            categoryId: product.category?.id,
            tagIds: product.tags?.compactMap { ($0 as? Tag)?.id } ?? [],
            purchaseChannelId: product.purchaseChannelRelation?.id,
            parentProductIds: product.parentProducts?.compactMap { ($0 as? Product)?.id } ?? [],
            relatedProductIds: product.relatedProducts?.compactMap { ($0 as? Product)?.id } ?? [],
            images: imagesContainer
        )
    }
}

extension BackupUsageRecord {
    /// 从Core Data UsageRecord实体创建备份数据
    static func fromCoreData(_ record: UsageRecord) -> BackupUsageRecord {
        // 处理图片数据
        var imagesContainer: BinaryDataContainer?
        if let imageData = record.images {
            let base64String = imageData.base64EncodedString()
            imagesContainer = BinaryDataContainer(
                strategy: .base64,
                data: base64String,
                fileName: "usage_images_\(record.id?.uuidString ?? "unknown").data",
                mimeType: "application/octet-stream",
                size: Int64(imageData.count),
                checksum: base64String.sha256
            )
        }

        // 处理音频数据 - 备份实际的音频文件内容而不是文件名
        var audioContainer: BinaryDataContainer?
        if let audioData = record.audioRecordings {
            print("📦 开始处理录音数据: \(audioData.count) 字节（序列化的文件名数组）")

            do {
                // 尝试解析文件名数组（新格式）
                if let fileNames = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [String] {
                    print("📂 解析到 \(fileNames.count) 个录音文件名: \(fileNames)")

                    // 收集所有音频文件的内容
                    var allAudioData = Data()
                    var audioFileInfos: [[String: Any]] = []
                    let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
                    print("📁 Documents目录: \(documentsDirectory.path)")

                    for fileName in fileNames {
                        let audioURL = documentsDirectory.appendingPathComponent(fileName)
                        print("🔍 检查文件: \(audioURL.path)")

                        if FileManager.default.fileExists(atPath: audioURL.path) {
                            if let fileData = try? Data(contentsOf: audioURL) {
                                let startOffset = allAudioData.count
                                allAudioData.append(fileData)

                                audioFileInfos.append([
                                    "fileName": fileName,
                                    "startOffset": startOffset,
                                    "size": fileData.count
                                ])
                                print("📁 添加音频文件: \(fileName), 大小: \(fileData.count) 字节")
                            } else {
                                print("⚠️ 无法读取音频文件: \(fileName)")
                            }
                        } else {
                            print("⚠️ 音频文件不存在: \(fileName)")
                        }
                    }

                    if !allAudioData.isEmpty {
                        // 创建包含文件信息和数据的容器
                        let audioPackage = [
                            "fileInfos": audioFileInfos,
                            "audioData": allAudioData
                        ] as [String : Any]

                        let packageData = try NSKeyedArchiver.archivedData(withRootObject: audioPackage, requiringSecureCoding: false)
                        let base64String = packageData.base64EncodedString()

                        audioContainer = BinaryDataContainer(
                            strategy: .base64,
                            data: base64String,
                            fileName: "usage_audio_package_\(record.id?.uuidString ?? "unknown").data",
                            mimeType: "application/octet-stream",
                            size: Int64(packageData.count),
                            checksum: base64String.sha256
                        )
                        print("✅ 录音数据包已创建: \(audioFileInfos.count) 个文件, 总大小: \(allAudioData.count) 字节")
                    } else {
                        print("⚠️ 没有找到有效的音频文件")
                    }
                } else {
                    // 尝试解析旧格式（URL数组）
                    if let urls = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [URL] {
                        print("📂 解析到旧格式URL数组: \(urls.count) 个文件")
                        let fileNames = urls.map { $0.lastPathComponent }

                        // 收集所有音频文件的内容
                        var allAudioData = Data()
                        var audioFileInfos: [[String: Any]] = []
                        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]

                        for fileName in fileNames {
                            let audioURL = documentsDirectory.appendingPathComponent(fileName)
                            if FileManager.default.fileExists(atPath: audioURL.path) {
                                if let fileData = try? Data(contentsOf: audioURL) {
                                    let startOffset = allAudioData.count
                                    allAudioData.append(fileData)

                                    audioFileInfos.append([
                                        "fileName": fileName,
                                        "startOffset": startOffset,
                                        "size": fileData.count
                                    ])
                                    print("📁 添加音频文件（旧格式）: \(fileName), 大小: \(fileData.count) 字节")
                                }
                            }
                        }

                        if !allAudioData.isEmpty {
                            let audioPackage = [
                                "fileInfos": audioFileInfos,
                                "audioData": allAudioData
                            ] as [String : Any]

                            let packageData = try NSKeyedArchiver.archivedData(withRootObject: audioPackage, requiringSecureCoding: false)
                            let base64String = packageData.base64EncodedString()

                            audioContainer = BinaryDataContainer(
                                strategy: .base64,
                                data: base64String,
                                fileName: "usage_audio_package_\(record.id?.uuidString ?? "unknown").data",
                                mimeType: "application/octet-stream",
                                size: Int64(packageData.count),
                                checksum: base64String.sha256
                            )
                            print("✅ 旧格式录音数据包已创建: \(audioFileInfos.count) 个文件")
                        }
                    } else {
                        print("⚠️ 无法解析录音数据，数据格式未知")
                        print("📊 原始数据大小: \(audioData.count) 字节")
                        // 尝试直接备份原始数据作为最后手段
                        let base64String = audioData.base64EncodedString()
                        audioContainer = BinaryDataContainer(
                            strategy: .base64,
                            data: base64String,
                            fileName: "usage_audio_raw_\(record.id?.uuidString ?? "unknown").data",
                            mimeType: "application/octet-stream",
                            size: Int64(audioData.count),
                            checksum: base64String.sha256
                        )
                        print("⚠️ 使用原始数据备份作为后备方案")
                    }
                }
            } catch {
                print("❌ 处理录音数据时出错: \(error)")
            }
        } else {
            print("ℹ️ 使用记录没有录音数据")
        }

        return BackupUsageRecord(
            id: record.id ?? UUID(),
            lastModified: Date(),
            date: record.date ?? Date(),
            satisfaction: Int(record.satisfaction),
            emotionalValue: Int(record.emotionalValue),
            duration: record.duration,
            usageType: record.usageType ?? "personal",
            scenario: record.scenario,
            title: record.title,
            notes: record.notes,
            memories: record.memories,
            isLoanedOut: record.isLoanedOut,
            borrowerName: record.borrowerName,
            contactInfo: record.contactInfo,
            dueDate: record.dueDate,
            returnDate: record.returnDate,
            recipient: record.recipient,
            transferPrice: record.transferPrice,
            transferType: record.transferTypeString,
            wearCondition: record.wearCondition,
            isStory: record.isStory,
            consumedQuantity: record.consumedQuantity > 0 ? record.consumedQuantity : nil,
            remainingQuantity: record.remainingQuantity > 0 ? record.remainingQuantity : nil,
            consumptionUnit: record.consumptionUnit,
            productId: record.product?.id ?? UUID(),
            images: imagesContainer,
            audioRecordings: audioContainer
        )
    }
}

extension BackupRelatedExpense {
    /// 从Core Data RelatedExpense实体创建备份数据
    static func fromCoreData(_ expense: RelatedExpense) -> BackupRelatedExpense {
        return BackupRelatedExpense(
            id: expense.id ?? UUID(),
            lastModified: Date(),
            amount: expense.amount,
            date: expense.date ?? Date(),
            notes: expense.notes,
            productId: expense.product?.id ?? UUID(),
            expenseTypeId: expense.type?.id ?? UUID()
        )
    }
}

extension BackupCategory {
    /// 从Core Data Category实体创建备份数据
    static func fromCoreData(_ category: Category) -> BackupCategory {
        return BackupCategory(
            id: category.id ?? UUID(),
            lastModified: Date(),
            name: category.name ?? "",
            icon: category.icon,
            parentCategoryId: category.parentCategory?.id
        )
    }
}

extension BackupTag {
    /// 从Core Data Tag实体创建备份数据
    static func fromCoreData(_ tag: Tag) -> BackupTag {
        return BackupTag(
            id: tag.id ?? UUID(),
            lastModified: Date(),
            name: tag.name ?? "",
            color: tag.color,
            type: tag.type
        )
    }
}

extension BackupLoanRecord {
    /// 从Core Data LoanRecord实体创建备份数据
    static func fromCoreData(_ loan: LoanRecord) -> BackupLoanRecord {
        return BackupLoanRecord(
            id: loan.id ?? UUID(),
            lastModified: Date(),
            borrowerName: loan.borrowerName ?? "",
            contactInfo: loan.contactInfo,
            createdAt: loan.createdAt ?? Date(),
            dueDate: loan.dueDate ?? Date(),
            returnDate: loan.returnDate,
            status: loan.status ?? "active",
            notes: loan.notes,
            isLoanedOut: loan.isLoanedOut,
            productId: loan.product?.id ?? UUID()
        )
    }
}

extension BackupExpenseType {
    /// 从Core Data ExpenseType实体创建备份数据
    static func fromCoreData(_ type: ExpenseType) -> BackupExpenseType {
        return BackupExpenseType(
            id: type.id ?? UUID(),
            lastModified: Date(),
            name: type.name ?? "",
            icon: type.icon
        )
    }
}

extension BackupPurchaseChannel {
    /// 从Core Data PurchaseChannel实体创建备份数据
    static func fromCoreData(_ channel: PurchaseChannel) -> BackupPurchaseChannel {
        return BackupPurchaseChannel(
            id: channel.id ?? UUID(),
            lastModified: Date(),
            name: channel.name ?? "",
            categoryId: channel.category?.id
        )
    }
}

extension BackupPurchaseChannelCategory {
    /// 从Core Data PurchaseChannelCategory实体创建备份数据
    static func fromCoreData(_ category: PurchaseChannelCategory) -> BackupPurchaseChannelCategory {
        return BackupPurchaseChannelCategory(
            id: category.id ?? UUID(),
            lastModified: Date(),
            name: category.name ?? "",
            icon: category.icon
        )
    }
}

extension BackupReminder {
    /// 从Core Data Reminder实体创建备份数据
    static func fromCoreData(_ reminder: Reminder) -> BackupReminder {
        return BackupReminder(
            id: reminder.id ?? UUID(),
            lastModified: Date(),
            date: reminder.date ?? Date(),
            message: reminder.message ?? "",
            reminderType: reminder.reminderType ?? "general",
            isActive: reminder.isActive,
            productIds: reminder.product?.compactMap { ($0 as? Product)?.id } ?? []
        )
    }
}

extension BackupProductLink {
    /// 从Core Data ProductLink实体创建备份数据
    static func fromCoreData(_ link: ProductLink) -> BackupProductLink {
        return BackupProductLink(
            id: link.id ?? UUID(),
            lastModified: Date(),
            relationshipType: link.relationshipType ?? "other",
            notes: link.notes,
            createdAt: link.createdAt ?? Date(),
            strength: Int(link.strength),
            isBidirectional: link.isBidirectional,
            usageCount: Int(link.usageCount),
            lastUsedTogether: link.lastUsedTogether,
            usageScenario: link.usageScenario,
            groupID: link.groupID,
            sourceProductId: link.sourceProduct?.id ?? UUID(),
            targetProductId: link.targetProduct?.id ?? UUID()
        )
    }
}

extension BackupConversation {
    /// 从Core Data Conversation实体创建备份数据
    static func fromCoreData(_ conversation: Conversation) -> BackupConversation {
        return BackupConversation(
            id: conversation.id ?? UUID(),
            lastModified: Date(),
            title: conversation.title ?? "",
            startDate: conversation.startDate ?? Date(),
            lastMessageDate: conversation.lastMessageDate ?? Date()
        )
    }
}

extension BackupMessage {
    /// 从Core Data Message实体创建备份数据
    static func fromCoreData(_ message: Message) -> BackupMessage {
        // 处理分析上下文数据
        var analysisContainer: BinaryDataContainer?
        if let analysisData = message.analysisContextData {
            let base64String = analysisData.base64EncodedString()
            analysisContainer = BinaryDataContainer(
                strategy: .base64,
                data: base64String,
                fileName: "analysis_\(message.id?.uuidString ?? "unknown").data",
                mimeType: "application/json",
                size: Int64(analysisData.count),
                checksum: base64String.sha256
            )
        }

        // 处理附件数据
        var attachmentsContainer: BinaryDataContainer?
        if let attachmentsData = message.attachmentsData {
            let base64String = attachmentsData.base64EncodedString()
            attachmentsContainer = BinaryDataContainer(
                strategy: .base64,
                data: base64String,
                fileName: "attachments_\(message.id?.uuidString ?? "unknown").data",
                mimeType: "application/octet-stream",
                size: Int64(attachmentsData.count),
                checksum: base64String.sha256
            )
        }

        return BackupMessage(
            id: message.id ?? UUID(),
            lastModified: Date(),
            text: message.text ?? "",
            timestamp: message.timestamp ?? Date(),
            isFromUser: message.isFromUser,
            role: message.role ?? "user",
            conversationId: message.conversation?.id ?? UUID(),
            analysisContextData: analysisContainer,
            attachmentsData: attachmentsContainer
        )
    }
}

// MARK: - 批量转换工具

/// 批量转换工具类
struct BackupDataConverter {
    /// 从Core Data上下文批量转换所有数据
    static func convertAllData(from context: NSManagedObjectContext) throws -> BackupDataContainer {
        // 获取所有实体数据
        let products = try fetchAllEntities(Product.self, from: context).map(BackupProduct.fromCoreData)
        let usageRecords = try fetchAllEntities(UsageRecord.self, from: context).map(BackupUsageRecord.fromCoreData)
        let relatedExpenses = try fetchAllEntities(RelatedExpense.self, from: context).map(BackupRelatedExpense.fromCoreData)
        let categories = try fetchAllEntities(Category.self, from: context).map(BackupCategory.fromCoreData)
        let tags = try fetchAllEntities(Tag.self, from: context).map(BackupTag.fromCoreData)
        let loanRecords = try fetchAllEntities(LoanRecord.self, from: context).map(BackupLoanRecord.fromCoreData)
        let expenseTypes = try fetchAllEntities(ExpenseType.self, from: context).map(BackupExpenseType.fromCoreData)
        let purchaseChannels = try fetchAllEntities(PurchaseChannel.self, from: context).map(BackupPurchaseChannel.fromCoreData)
        let purchaseChannelCategories = try fetchAllEntities(PurchaseChannelCategory.self, from: context).map(BackupPurchaseChannelCategory.fromCoreData)
        let reminders = try fetchAllEntities(Reminder.self, from: context).map(BackupReminder.fromCoreData)
        let productLinks = try fetchAllEntities(ProductLink.self, from: context).map(BackupProductLink.fromCoreData)
        let conversations = try fetchAllEntities(Conversation.self, from: context).map(BackupConversation.fromCoreData)
        let messages = try fetchAllEntities(Message.self, from: context).map(BackupMessage.fromCoreData)

        return BackupDataContainer(
            backupType: .full,
            products: products,
            usageRecords: usageRecords,
            relatedExpenses: relatedExpenses,
            categories: categories,
            tags: tags,
            loanRecords: loanRecords,
            expenseTypes: expenseTypes,
            purchaseChannels: purchaseChannels,
            purchaseChannelCategories: purchaseChannelCategories,
            reminders: reminders,
            productLinks: productLinks,
            conversations: conversations,
            messages: messages
        )
    }

    /// 从Core Data上下文获取指定类型的所有实体
    private static func fetchAllEntities<T: NSManagedObject>(_ entityType: T.Type, from context: NSManagedObjectContext) throws -> [T] {
        let request = NSFetchRequest<T>(entityName: String(describing: entityType))
        return try context.fetch(request)
    }
}
