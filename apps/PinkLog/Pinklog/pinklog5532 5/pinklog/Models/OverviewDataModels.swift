import Foundation
import SwiftUI

// MARK: - 基础统计数据模型
// 这些数据结构用于AI助手的基础统计功能，不涉及三曲线分析

/// 基础统计信息
struct BasicStats: Codable {
    let totalProductsCount: Int
    let totalInvestment: Double
    let averageProductPrice: Double
    let totalUsageRecords: Int
    let averageUsagePerProduct: Double
    let oldestProductDate: Date?
    let newestProductDate: Date?
    let ownershipTimespan: TimeInterval // 拥有产品的总时间跨度（秒）

    init(
        totalProductsCount: Int,
        totalInvestment: Double,
        averageProductPrice: Double,
        totalUsageRecords: Int,
        averageUsagePerProduct: Double,
        oldestProductDate: Date?,
        newestProductDate: Date?,
        ownershipTimespan: TimeInterval
    ) {
        self.totalProductsCount = totalProductsCount
        self.totalInvestment = totalInvestment
        self.averageProductPrice = averageProductPrice
        self.totalUsageRecords = totalUsageRecords
        self.averageUsagePerProduct = averageUsagePerProduct
        self.oldestProductDate = oldestProductDate
        self.newestProductDate = newestProductDate
        self.ownershipTimespan = ownershipTimespan
    }
}

/// 类别统计信息
struct CategoryStats: Codable {
    let categoryId: UUID
    let categoryName: String
    let categoryIcon: String?
    let productCount: Int
    let totalValue: Double
    let averagePrice: Double
    let totalUsageCount: Int
    let averageUsagePerProduct: Double
    let averageSatisfaction: Double
    let averageWorthIndex: Double
    let percentageOfTotal: Double // 占总产品数的百分比

    init(
        categoryId: UUID,
        categoryName: String,
        categoryIcon: String?,
        productCount: Int,
        totalValue: Double,
        averagePrice: Double,
        totalUsageCount: Int,
        averageUsagePerProduct: Double,
        averageSatisfaction: Double,
        averageWorthIndex: Double,
        percentageOfTotal: Double
    ) {
        self.categoryId = categoryId
        self.categoryName = categoryName
        self.categoryIcon = categoryIcon
        self.productCount = productCount
        self.totalValue = totalValue
        self.averagePrice = averagePrice
        self.totalUsageCount = totalUsageCount
        self.averageUsagePerProduct = averageUsagePerProduct
        self.averageSatisfaction = averageSatisfaction
        self.averageWorthIndex = averageWorthIndex
        self.percentageOfTotal = percentageOfTotal
    }
}

/// 价格区间枚举
enum PriceRange: String, CaseIterable, Identifiable, Codable {
    case under100 = "100元以下"
    case range100to500 = "100-500元"
    case range500to1000 = "500-1000元"
    case range1000to5000 = "1000-5000元"
    case range5000to10000 = "5000-10000元"
    case over10000 = "10000元以上"

    var id: String { self.rawValue }

    /// 价格区间的最小值
    var minValue: Double {
        switch self {
        case .under100: return 0
        case .range100to500: return 100
        case .range500to1000: return 500
        case .range1000to5000: return 1000
        case .range5000to10000: return 5000
        case .over10000: return 10000
        }
    }

    /// 价格区间的最大值
    var maxValue: Double {
        switch self {
        case .under100: return 100
        case .range100to500: return 500
        case .range500to1000: return 1000
        case .range1000to5000: return 5000
        case .range5000to10000: return 10000
        case .over10000: return Double.infinity
        }
    }

    /// 判断价格是否在此区间内
    func contains(_ price: Double) -> Bool {
        return price >= minValue && price < maxValue
    }
}

/// 价格分布统计
struct PriceDistribution: Codable {
    let distributionByRange: [PriceRange: PriceRangeStats]
    let medianPrice: Double
    let averagePrice: Double
    let priceStandardDeviation: Double
    let mostExpensiveProduct: ProductSummary?
    let cheapestProduct: ProductSummary?

    init(
        distributionByRange: [PriceRange: PriceRangeStats],
        medianPrice: Double,
        averagePrice: Double,
        priceStandardDeviation: Double,
        mostExpensiveProduct: ProductSummary?,
        cheapestProduct: ProductSummary?
    ) {
        self.distributionByRange = distributionByRange
        self.medianPrice = medianPrice
        self.averagePrice = averagePrice
        self.priceStandardDeviation = priceStandardDeviation
        self.mostExpensiveProduct = mostExpensiveProduct
        self.cheapestProduct = cheapestProduct
    }
}

/// 价格区间统计信息
struct PriceRangeStats: Codable {
    let range: PriceRange
    let productCount: Int
    let totalValue: Double
    let averagePrice: Double
    let percentageOfTotal: Double
    let averageUsageCount: Double
    let averageSatisfaction: Double

    init(
        range: PriceRange,
        productCount: Int,
        totalValue: Double,
        averagePrice: Double,
        percentageOfTotal: Double,
        averageUsageCount: Double,
        averageSatisfaction: Double
    ) {
        self.range = range
        self.productCount = productCount
        self.totalValue = totalValue
        self.averagePrice = averagePrice
        self.percentageOfTotal = percentageOfTotal
        self.averageUsageCount = averageUsageCount
        self.averageSatisfaction = averageSatisfaction
    }
}

/// 时间周期枚举
enum TimePeriod: String, CaseIterable, Identifiable, Codable {
    case lastMonth = "最近一个月"
    case lastThreeMonths = "最近三个月"
    case lastSixMonths = "最近六个月"
    case lastYear = "最近一年"
    case overOneYear = "一年以上"

    var id: String { self.rawValue }

    /// 获取时间周期的起始日期
    func startDate(from referenceDate: Date = Date()) -> Date {
        let calendar = Calendar.current
        switch self {
        case .lastMonth:
            return calendar.date(byAdding: .month, value: -1, to: referenceDate) ?? referenceDate
        case .lastThreeMonths:
            return calendar.date(byAdding: .month, value: -3, to: referenceDate) ?? referenceDate
        case .lastSixMonths:
            return calendar.date(byAdding: .month, value: -6, to: referenceDate) ?? referenceDate
        case .lastYear:
            return calendar.date(byAdding: .year, value: -1, to: referenceDate) ?? referenceDate
        case .overOneYear:
            return calendar.date(byAdding: .year, value: -10, to: referenceDate) ?? referenceDate // 10年前作为起始
        }
    }

    /// 判断日期是否在此时间周期内
    func contains(_ date: Date, referenceDate: Date = Date()) -> Bool {
        let startDate = self.startDate(from: referenceDate)
        switch self {
        case .overOneYear:
            return date < Calendar.current.date(byAdding: .year, value: -1, to: referenceDate) ?? referenceDate
        default:
            return date >= startDate && date <= referenceDate
        }
    }
}

/// 时间分布统计
struct TimeDistribution: Codable {
    let distributionByPeriod: [TimePeriod: TimePeriodStats]
    let purchaseFrequencyByMonth: [String: Int] // "YYYY-MM" -> count
    let seasonalTrends: SeasonalTrends
    let averagePurchaseInterval: TimeInterval // 平均购买间隔（秒）

    init(
        distributionByPeriod: [TimePeriod: TimePeriodStats],
        purchaseFrequencyByMonth: [String: Int],
        seasonalTrends: SeasonalTrends,
        averagePurchaseInterval: TimeInterval
    ) {
        self.distributionByPeriod = distributionByPeriod
        self.purchaseFrequencyByMonth = purchaseFrequencyByMonth
        self.seasonalTrends = seasonalTrends
        self.averagePurchaseInterval = averagePurchaseInterval
    }
}

/// 时间周期统计信息
struct TimePeriodStats: Codable {
    let period: TimePeriod
    let productCount: Int
    let totalValue: Double
    let averagePrice: Double
    let percentageOfTotal: Double
    let mostActiveCategory: String?

    init(
        period: TimePeriod,
        productCount: Int,
        totalValue: Double,
        averagePrice: Double,
        percentageOfTotal: Double,
        mostActiveCategory: String?
    ) {
        self.period = period
        self.productCount = productCount
        self.totalValue = totalValue
        self.averagePrice = averagePrice
        self.percentageOfTotal = percentageOfTotal
        self.mostActiveCategory = mostActiveCategory
    }
}

/// 季节性趋势分析
struct SeasonalTrends: Codable {
    let springPurchases: Int    // 春季购买数量 (3-5月)
    let summerPurchases: Int    // 夏季购买数量 (6-8月)
    let autumnPurchases: Int    // 秋季购买数量 (9-11月)
    let winterPurchases: Int    // 冬季购买数量 (12-2月)
    let peakSeason: String      // 购买高峰季节
    let lowSeason: String       // 购买低谷季节

    init(
        springPurchases: Int,
        summerPurchases: Int,
        autumnPurchases: Int,
        winterPurchases: Int,
        peakSeason: String,
        lowSeason: String
    ) {
        self.springPurchases = springPurchases
        self.summerPurchases = summerPurchases
        self.autumnPurchases = autumnPurchases
        self.winterPurchases = winterPurchases
        self.peakSeason = peakSeason
        self.lowSeason = lowSeason
    }

    /// 获取季节购买数量
    func purchasesForSeason(_ season: String) -> Int {
        switch season.lowercased() {
        case "春季", "spring": return springPurchases
        case "夏季", "summer": return summerPurchases
        case "秋季", "autumn", "fall": return autumnPurchases
        case "冬季", "winter": return winterPurchases
        default: return 0
        }
    }
}

/// 产品摘要信息（用于极值展示）
struct ProductSummary: Codable, Identifiable {
    let id: UUID
    let name: String
    let brand: String?
    let price: Double
    let purchaseDate: Date
    let categoryName: String?
    let usageCount: Int
    let averageSatisfaction: Double
    let worthIndex: Double
    let daysSincePurchase: Int

    init(
        id: UUID,
        name: String,
        brand: String?,
        price: Double,
        purchaseDate: Date,
        categoryName: String?,
        usageCount: Int,
        averageSatisfaction: Double,
        worthIndex: Double,
        daysSincePurchase: Int
    ) {
        self.id = id
        self.name = name
        self.brand = brand
        self.price = price
        self.purchaseDate = purchaseDate
        self.categoryName = categoryName
        self.usageCount = usageCount
        self.averageSatisfaction = averageSatisfaction
        self.worthIndex = worthIndex
        self.daysSincePurchase = daysSincePurchase
    }

    /// 格式化的价格显示
    var formattedPrice: String {
        return String(format: "¥%.0f", price)
    }

    /// 产品表现等级
    var performanceGrade: String {
        switch worthIndex {
        case 80...100: return "优秀"
        case 60..<80: return "良好"
        case 40..<60: return "一般"
        case 20..<40: return "较差"
        default: return "很差"
        }
    }

    /// 是否为新购买产品（30天内）
    var isRecentPurchase: Bool {
        return daysSincePurchase <= 30
    }
}

/// 完整的概览度量模型
struct OverviewMetrics: Codable {
    let basicStats: BasicStats
    let categoryDistribution: [UUID: CategoryStats]
    let priceDistribution: PriceDistribution
    let timeDistribution: TimeDistribution
    let generatedAt: Date
    let analysisVersion: String

    init(
        basicStats: BasicStats,
        categoryDistribution: [UUID: CategoryStats],
        priceDistribution: PriceDistribution,
        timeDistribution: TimeDistribution,
        generatedAt: Date = Date(),
        analysisVersion: String = "1.0"
    ) {
        self.basicStats = basicStats
        self.categoryDistribution = categoryDistribution
        self.priceDistribution = priceDistribution
        self.timeDistribution = timeDistribution
        self.generatedAt = generatedAt
        self.analysisVersion = analysisVersion
    }

    /// 获取按产品数量排序的类别列表
    var categoriesByProductCount: [CategoryStats] {
        return categoryDistribution.values.sorted { $0.productCount > $1.productCount }
    }

    /// 获取按总价值排序的类别列表
    var categoriesByTotalValue: [CategoryStats] {
        return categoryDistribution.values.sorted { $0.totalValue > $1.totalValue }
    }

    /// 获取最活跃的类别（使用次数最多）
    var mostActiveCategory: CategoryStats? {
        return categoryDistribution.values.max { $0.totalUsageCount < $1.totalUsageCount }
    }

    /// 获取最有价值的类别（平均值度指数最高）
    var mostValuableCategory: CategoryStats? {
        return categoryDistribution.values.max { $0.averageWorthIndex < $1.averageWorthIndex }
    }

    /// 获取投资最多的价格区间
    var highestInvestmentPriceRange: PriceRange? {
        return priceDistribution.distributionByRange.max { $0.value.totalValue < $1.value.totalValue }?.key
    }

    /// 获取产品数量最多的价格区间
    var mostPopularPriceRange: PriceRange? {
        return priceDistribution.distributionByRange.max { $0.value.productCount < $1.value.productCount }?.key
    }

    /// 获取最活跃的购买时期
    var mostActivePurchasePeriod: TimePeriod? {
        return timeDistribution.distributionByPeriod.max { $0.value.productCount < $1.value.productCount }?.key
    }

    /// 数据是否足够新鲜（1小时内生成）
    var isDataFresh: Bool {
        return Date().timeIntervalSince(generatedAt) < 3600
    }
}
