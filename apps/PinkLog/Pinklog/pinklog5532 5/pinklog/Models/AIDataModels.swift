import Foundation
import SwiftUI

// MARK: - AI数据上下文相关模型

/// 用户数据上下文
/// 为AI助手提供结构化的用户数据摘要
struct UserDataContext: Codable {
    let totalProducts: Int
    let totalInvestment: Double
    let topCategories: [String]
    let topPerformingProducts: [ProductSummary]
    let recentPurchases: [ProductSummary]
    let consumptionTrends: ConsumptionTrends
    let generatedAt: Date
    
    init(
        totalProducts: Int,
        totalInvestment: Double,
        topCategories: [String],
        topPerformingProducts: [ProductSummary],
        recentPurchases: [ProductSummary],
        consumptionTrends: ConsumptionTrends
    ) {
        self.totalProducts = totalProducts
        self.totalInvestment = totalInvestment
        self.topCategories = topCategories
        self.topPerformingProducts = topPerformingProducts
        self.recentPurchases = recentPurchases
        self.consumptionTrends = consumptionTrends
        self.generatedAt = Date()
    }
    
    /// 格式化的投资金额显示
    var formattedInvestment: String {
        return String(format: "¥%.0f", totalInvestment)
    }
    
    /// 平均产品价值
    var averageProductValue: Double {
        return totalProducts > 0 ? totalInvestment / Double(totalProducts) : 0.0
    }
}



/// 消费趋势分析
/// 提供用户消费行为的趋势信息
struct ConsumptionTrends: Codable {
    let monthlySpendingTrend: SpendingTrend
    let categoryPreferences: [CategoryPreference]
    let seasonalPatterns: SeasonalPattern
    let averagePurchaseInterval: TimeInterval
    let spendingVelocity: SpendingVelocity
    
    /// 主要消费类别
    var primaryCategory: String {
        return categoryPreferences.first?.categoryName ?? "未知"
    }
    
    /// 消费频率描述
    var purchaseFrequencyDescription: String {
        let days = averagePurchaseInterval / 86400 // 转换为天数
        switch days {
        case 0..<7: return "频繁购买"
        case 7..<30: return "定期购买"
        case 30..<90: return "偶尔购买"
        default: return "很少购买"
        }
    }
}

/// 支出趋势
enum SpendingTrend: String, Codable, CaseIterable {
    case increasing = "上升"
    case stable = "稳定"
    case decreasing = "下降"
    case volatile = "波动"
    
    var description: String {
        switch self {
        case .increasing: return "消费呈上升趋势"
        case .stable: return "消费保持稳定"
        case .decreasing: return "消费呈下降趋势"
        case .volatile: return "消费波动较大"
        }
    }
    
    var color: Color {
        switch self {
        case .increasing: return .red
        case .stable: return .green
        case .decreasing: return .blue
        case .volatile: return .orange
        }
    }
}

/// 类别偏好
struct CategoryPreference: Codable, Identifiable {
    let id: UUID
    let categoryName: String
    let productCount: Int
    let totalSpending: Double
    let averageWorthIndex: Double
    let preferenceScore: Double // 0-100，综合偏好评分

    init(
        categoryName: String,
        productCount: Int,
        totalSpending: Double,
        averageWorthIndex: Double,
        preferenceScore: Double
    ) {
        self.id = UUID()
        self.categoryName = categoryName
        self.productCount = productCount
        self.totalSpending = totalSpending
        self.averageWorthIndex = averageWorthIndex
        self.preferenceScore = preferenceScore
    }
    
    /// 格式化的支出显示
    var formattedSpending: String {
        return String(format: "¥%.0f", totalSpending)
    }
    
    /// 偏好等级
    var preferenceLevel: String {
        switch preferenceScore {
        case 80...100: return "非常偏好"
        case 60..<80: return "比较偏好"
        case 40..<60: return "一般偏好"
        case 20..<40: return "较少偏好"
        default: return "很少偏好"
        }
    }
}

/// 季节性模式
struct SeasonalPattern: Codable {
    let peakSeason: String
    let lowSeason: String
    let seasonalVariation: Double // 季节性变化程度 0-1
    
    /// 季节性描述
    var description: String {
        let variationLevel = seasonalVariation > 0.5 ? "明显" : "轻微"
        return "\(peakSeason)为购买高峰期，\(lowSeason)为低谷期，季节性变化\(variationLevel)"
    }
}

/// 消费速度
enum SpendingVelocity: String, Codable, CaseIterable {
    case fast = "快速"
    case moderate = "适中"
    case slow = "缓慢"
    
    var description: String {
        switch self {
        case .fast: return "消费节奏较快"
        case .moderate: return "消费节奏适中"
        case .slow: return "消费节奏较慢"
        }
    }
}

/// 相关数据上下文
/// 用于特定问题类型的数据过滤
struct RelevantDataContext: Codable {
    let contextType: DataContextType
    let products: [ProductSummary]
    let metrics: [String: Double]
    let insights: [String]
    let generatedAt: Date
    
    init(
        contextType: DataContextType,
        products: [ProductSummary],
        metrics: [String: Double] = [:],
        insights: [String] = []
    ) {
        self.contextType = contextType
        self.products = products
        self.metrics = metrics
        self.insights = insights
        self.generatedAt = Date()
    }
}

/// 数据上下文类型
enum DataContextType: String, Codable, CaseIterable {
    case productPerformance = "产品表现"
    case consumptionPattern = "消费模式"
    case categoryAnalysis = "类别分析"
    case valueAnalysis = "价值分析"
    case general = "综合分析"

    var description: String {
        return self.rawValue
    }
}

/// 完整的用户数据上下文
/// 包含所有可用数据字段，供AI进行全面分析
struct CompleteUserDataContext: Codable {
    let products: [CompleteProductData]
    let usageRecords: [CompleteUsageRecordData]
    let expenses: [CompleteExpenseData]
    let categories: [CompleteCategoryData]

    // 统计信息
    let totalProducts: Int
    let totalInvestment: Double
    let totalUsageRecords: Int
    let totalExpenses: Double
    let averageWorthIndex: Double
    let averageSatisfaction: Double

    // 元数据
    let generatedAt: Date

    /// 格式化的总投资
    var formattedTotalInvestment: String {
        return String(format: "¥%.2f", totalInvestment)
    }

    /// 格式化的总费用
    var formattedTotalExpenses: String {
        return String(format: "¥%.2f", totalExpenses)
    }

    /// 生成时间字符串
    var generatedAtString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: generatedAt)
    }

    /// 数据完整性评分
    var dataCompletenessScore: Double {
        var score = 0.0
        let maxScore = 100.0

        // 产品数据完整性 (40分)
        if !products.isEmpty {
            score += 20.0
            let productsWithCompleteInfo = products.filter { product in
                !product.name.isEmpty &&
                product.price > 0 &&
                !product.usageRecords.isEmpty
            }
            score += Double(productsWithCompleteInfo.count) / Double(products.count) * 20.0
        }

        // 使用记录完整性 (30分)
        if !usageRecords.isEmpty {
            score += 15.0
            let recordsWithDetails = usageRecords.filter { record in
                record.satisfaction > 0 &&
                !(record.notes?.isEmpty ?? true)
            }
            score += Double(recordsWithDetails.count) / Double(usageRecords.count) * 15.0
        }

        // 费用记录完整性 (20分)
        if !expenses.isEmpty {
            score += 10.0
            let expensesWithDetails = expenses.filter { expense in
                expense.amount > 0 &&
                !(expense.notes?.isEmpty ?? true)
            }
            score += Double(expensesWithDetails.count) / Double(expenses.count) * 10.0
        }

        // 类别信息完整性 (10分)
        if !categories.isEmpty {
            score += 10.0
        }

        return min(score, maxScore)
    }
}
