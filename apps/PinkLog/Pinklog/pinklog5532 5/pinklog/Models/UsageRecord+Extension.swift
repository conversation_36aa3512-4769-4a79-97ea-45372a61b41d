import Foundation
import CoreData
import SwiftUI

extension UsageRecord {
    // 使用类型枚举
    enum UsageType: String, CaseIterable, Identifiable {
        case personal = "个人使用"
        case loaned = "借出"

        var id: String { self.rawValue }

        var icon: String {
            switch self {
            case .personal: return "person.fill"
            case .loaned: return "arrow.up.forward.circle"
            }
        }

        var color: Color {
            switch self {
            case .personal: return .blue
            case .loaned: return .orange
            }
        }
    }

    // 转让类型枚举
    enum TransferType: String, CaseIterable, Identifiable {
        case sold = "出售"
        case gifted = "赠送"

        var id: String { self.rawValue }

        var icon: String {
            switch self {
            case .sold: return "dollarsign.circle"
            case .gifted: return "gift"
            }
        }

        var color: Color {
            switch self {
            case .sold: return .green
            case .gifted: return .purple
            }
        }
    }

    // 借阅状态枚举
    enum LoanStatus: String, CaseIterable, Identifiable {
        case active = "借出中"
        case overdue = "已逾期"
        case returned = "已归还"

        var id: String { self.rawValue }

        var color: Color {
            switch self {
            case .active: return .blue
            case .overdue: return .red
            case .returned: return .green
            }
        }

        var icon: String {
            switch self {
            case .active: return "arrow.up.forward.circle"
            case .overdue: return "exclamationmark.circle"
            case .returned: return "checkmark.circle"
            }
        }
    }

    // 计算属性：使用类型
    var type: UsageType {
        guard let typeString = usageType else {
            return .personal
        }

        switch typeString {
        case "loaned":
            return .loaned
        default:
            return .personal
        }
    }

    // 计算属性：转让类型
    var transferType: TransferType? {
        guard let transferTypeString = transferTypeString else {
            return nil
        }

        return transferTypeString == "sold" ? .sold : .gifted
    }

    // 计算属性：借阅状态（仅适用于借出类型）
    var loanStatus: LoanStatus {
        guard type == .loaned else {
            return .active // 默认值，实际上不应该用于个人使用类型
        }

        if returnDate != nil {
            return .returned
        }

        if let dueDate = dueDate, dueDate < Date() {
            return .overdue
        }

        return .active
    }

    // 计算属性：剩余天数（仅适用于借出类型）
    var daysRemaining: Int? {
        guard type == .loaned, let dueDate = dueDate, returnDate == nil else {
            return nil
        }

        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: dueDate)
        return components.day
    }

    // 计算属性：借阅时长（天）（仅适用于借出类型）
    var loanDuration: Int? {
        guard type == .loaned, let date = date else {
            return nil
        }

        let endDate = returnDate ?? Date()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: date, to: endDate)
        return components.day
    }

    // 计算属性：是否即将到期（7天内）（仅适用于借出类型）
    var isNearDueDate: Bool {
        guard let daysRemaining = daysRemaining else {
            return false
        }
        return daysRemaining >= 0 && daysRemaining <= 7
    }

    // 计算属性：是否逾期（仅适用于借出类型）
    var isOverdue: Bool {
        return loanStatus == .overdue
    }
}

// MARK: - 消耗品相关扩展
extension UsageRecord {
    
    // 消耗单位枚举
    enum ConsumptionUnit: String, CaseIterable, Identifiable {
        case piece = "个"
        case ml = "毫升"
        case gram = "克"
        case tablet = "片"
        case capsule = "粒"
        case drop = "滴"
        case spoon = "勺"
        case cup = "杯"
        case bottle = "瓶"
        case pack = "包"
        case box = "盒"
        case bag = "袋"
        case meter = "米"
        case liter = "升"
        case kilogram = "千克"
        case dose = "剂"
        case serving = "份"
        case sheet = "张"
        case roll = "卷"
        case tube = "管"
        
        var id: String { self.rawValue }
        
        var symbol: String {
            switch self {
            case .piece: return "个"
            case .ml: return "ml"
            case .gram: return "g"
            case .tablet: return "片"
            case .capsule: return "粒"
            case .drop: return "滴"
            case .spoon: return "勺"
            case .cup: return "杯"
            case .bottle: return "瓶"
            case .pack: return "包"
            case .box: return "盒"
            case .bag: return "袋"
            case .meter: return "m"
            case .liter: return "L"
            case .kilogram: return "kg"
            case .dose: return "剂"
            case .serving: return "份"
            case .sheet: return "张"
            case .roll: return "卷"
            case .tube: return "管"
            }
        }
        
        var icon: String {
            switch self {
            case .piece: return "circle"
            case .ml, .liter: return "drop"
            case .gram, .kilogram: return "scalemass"
            case .tablet, .capsule: return "pills"
            case .drop: return "drop.triangle"
            case .spoon: return "fork.knife"
            case .cup: return "cup.and.saucer"
            case .bottle: return "waterbottle"
            case .pack, .box, .bag: return "shippingbox"
            case .meter: return "ruler"
            case .dose: return "cross.vial"
            case .serving: return "fork.knife.circle"
            case .sheet: return "doc"
            case .roll: return "paperclip.circle"
            case .tube: return "testtube.2"
            }
        }
    }
    
    // 计算属性：消耗单位
    var unit: ConsumptionUnit {
        guard let unitString = consumptionUnit else {
            return .piece
        }
        return ConsumptionUnit(rawValue: unitString) ?? .piece
    }
    
    // 计算属性：是否为消耗记录
    var isConsumptionRecord: Bool {
        return consumedQuantity > 0
    }
    
    // 计算属性：消耗量显示文本
    var consumedQuantityText: String {
        guard consumedQuantity > 0 else {
            return "未记录消耗量"
        }
        
        if consumedQuantity == floor(consumedQuantity) {
            return "\(Int(consumedQuantity))\(unit.symbol)"
        } else {
            return String(format: "%.1f%@", consumedQuantity, unit.symbol)
        }
    }
    
    // 计算属性：剩余量显示文本
    var remainingQuantityText: String {
        if remainingQuantity == floor(remainingQuantity) {
            return "剩余 \(Int(remainingQuantity))\(unit.symbol)"
        } else {
            return String(format: "剩余 %.1f%@", remainingQuantity, unit.symbol)
        }
    }
    
    // 计算属性：消耗效率（每分钟消耗量）
    var consumptionRate: Double? {
        guard consumedQuantity > 0,
              duration > 0 else {
            return nil
        }
        
        return consumedQuantity / (duration / 60.0) // 每分钟消耗量
    }
    
    // 计算属性：消耗效率显示文本
    var consumptionRateText: String? {
        guard let rate = consumptionRate else {
            return nil
        }
        
        if rate < 1 {
            return String(format: "%.2f%@/分钟", rate, unit.symbol)
        } else {
            return String(format: "%.1f%@/分钟", rate, unit.symbol)
        }
    }
    
    // 更新消耗量和剩余量
    func updateConsumption(consumed: Double, remaining: Double, unit: ConsumptionUnit) {
        self.consumedQuantity = consumed
        self.remainingQuantity = remaining
        self.consumptionUnit = unit.rawValue
    }
    
    // 验证消耗量数据的一致性
    func validateConsumptionData() -> Bool {
        guard let product = product else {
            return true // 如果没有产品关联，认为是有效的
        }

        // 注意：现在允许负数的consumedQuantity（用于表示库存补充）
        // 所以不再检查consumedQuantity >= 0

        // 检查剩余量是否为非负数
        guard remainingQuantity >= 0 else {
            return false
        }

        // 对于消耗记录（正数），检查是否超过了当时的库存
        if consumedQuantity > 0 {
            // 这是一个消耗记录，检查消耗量是否合理
            // 由于我们现在使用统一的库存计算，这个验证变得复杂
            // 暂时简化为只检查基本的数据有效性
            return consumedQuantity > 0 && remainingQuantity >= 0
        } else if consumedQuantity < 0 {
            // 这是一个补充记录（负数表示补充）
            return remainingQuantity >= 0
        } else {
            // consumedQuantity == 0，可能是其他类型的记录
            return true
        }
    }
}
