//
//  FamilyMember.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  家庭成员管理 - 数据模型
//

import Foundation
import SwiftUI

// MARK: - 家庭成员数据模型
struct FamilyMember: Identifiable, Codable, Hashable {
    let id: UUID
    var name: String
    var nickname: String?
    var avatar: String? // 头像图片名称或URL
    var role: FamilyRole
    var isActive: Bool
    var joinDate: Date
    var notes: String?
    
    // 使用统计
    var totalUsageSessions: Int
    var totalUsageTime: TimeInterval
    var averageSatisfactionRating: Double
    var lastActiveDate: Date?
    
    init(id: UUID = UUID(), name: String, nickname: String? = nil, avatar: String? = nil, role: FamilyRole = .member, isActive: Bool = true, joinDate: Date = Date(), notes: String? = nil) {
        self.id = id
        self.name = name
        self.nickname = nickname
        self.avatar = avatar
        self.role = role
        self.isActive = isActive
        self.joinDate = joinDate
        self.notes = notes
        self.totalUsageSessions = 0
        self.totalUsageTime = 0
        self.averageSatisfactionRating = 0
        self.lastActiveDate = nil
    }
    
    // 计算属性
    var displayName: String {
        return nickname?.isEmpty == false ? nickname! : name
    }
    
    var averageSessionDuration: TimeInterval {
        guard totalUsageSessions > 0 else { return 0 }
        return totalUsageTime / Double(totalUsageSessions)
    }
    
    var daysSinceLastActive: Int {
        guard let lastActive = lastActiveDate else { return Int.max }
        return Calendar.current.dateComponents([.day], from: lastActive, to: Date()).day ?? 0
    }
    
    var isRecentlyActive: Bool {
        return daysSinceLastActive <= 7
    }
}

// MARK: - 家庭角色枚举
enum FamilyRole: String, CaseIterable, Identifiable, Codable {
    case owner = "owner"
    case admin = "admin"
    case member = "member"
    case child = "child"
    case guest = "guest"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .owner: return "家庭主账户"
        case .admin: return "管理员"
        case .member: return "成员"
        case .child: return "儿童"
        case .guest: return "访客"
        }
    }
    
    var icon: String {
        switch self {
        case .owner: return "crown.fill"
        case .admin: return "person.badge.key.fill"
        case .member: return "person.fill"
        case .child: return "figure.child"
        case .guest: return "person.badge.clock"
        }
    }
    
    var color: Color {
        switch self {
        case .owner: return .yellow
        case .admin: return .blue
        case .member: return .green
        case .child: return .purple
        case .guest: return .gray
        }
    }
    
    var permissions: [FamilyPermission] {
        switch self {
        case .owner:
            return FamilyPermission.allCases
        case .admin:
            return [.viewSubscriptions, .editSubscriptions, .addSubscriptions, .viewUsage, .editUsage, .viewReports, .manageMembers]
        case .member:
            return [.viewSubscriptions, .viewUsage, .editUsage]
        case .child:
            return [.viewUsage, .editUsage]
        case .guest:
            return [.viewUsage]
        }
    }
}

// MARK: - 家庭权限枚举
enum FamilyPermission: String, CaseIterable, Identifiable, Codable {
    case viewSubscriptions = "viewSubscriptions"
    case editSubscriptions = "editSubscriptions"
    case addSubscriptions = "addSubscriptions"
    case deleteSubscriptions = "deleteSubscriptions"
    case viewUsage = "viewUsage"
    case editUsage = "editUsage"
    case viewReports = "viewReports"
    case manageMembers = "manageMembers"
    case manageBilling = "manageBilling"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .viewSubscriptions: return "查看订阅"
        case .editSubscriptions: return "编辑订阅"
        case .addSubscriptions: return "添加订阅"
        case .deleteSubscriptions: return "删除订阅"
        case .viewUsage: return "查看使用记录"
        case .editUsage: return "编辑使用记录"
        case .viewReports: return "查看报告"
        case .manageMembers: return "管理成员"
        case .manageBilling: return "管理账单"
        }
    }
}

// MARK: - 共享订阅数据模型
struct SharedSubscription: Identifiable, Codable, Equatable {
    let id: UUID
    var subscriptionId: UUID
    var familyMembers: [UUID] // 家庭成员ID列表
    var costSharingMethod: CostSharingMethod
    var customShares: [UUID: Double]? // 自定义分摊比例 (成员ID -> 比例)
    var isActive: Bool
    var createdDate: Date
    var notes: String?
    
    init(id: UUID = UUID(), subscriptionId: UUID, familyMembers: [UUID], costSharingMethod: CostSharingMethod = .equal, customShares: [UUID: Double]? = nil, isActive: Bool = true, createdDate: Date = Date(), notes: String? = nil) {
        self.id = id
        self.subscriptionId = subscriptionId
        self.familyMembers = familyMembers
        self.costSharingMethod = costSharingMethod
        self.customShares = customShares
        self.isActive = isActive
        self.createdDate = createdDate
        self.notes = notes
    }
    
    // 计算每个成员的分摊成本
    func calculateMemberCost(for memberId: UUID, totalCost: Double) -> Double {
        guard familyMembers.contains(memberId) else { return 0 }
        
        switch costSharingMethod {
        case .equal:
            return totalCost / Double(familyMembers.count)
        case .custom:
            guard let shares = customShares,
                  let memberShare = shares[memberId] else {
                return totalCost / Double(familyMembers.count)
            }
            return totalCost * memberShare
        case .usage:
            // 基于使用量分摊，需要额外的使用数据
            return totalCost / Double(familyMembers.count) // 默认平均分摊
        }
    }
}

// MARK: - 成本分摊方式枚举
enum CostSharingMethod: String, CaseIterable, Identifiable, Codable {
    case equal = "equal"
    case custom = "custom"
    case usage = "usage"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .equal: return "平均分摊"
        case .custom: return "自定义比例"
        case .usage: return "按使用量分摊"
        }
    }
    
    var description: String {
        switch self {
        case .equal: return "所有成员平均分摊费用"
        case .custom: return "根据自定义比例分摊费用"
        case .usage: return "根据实际使用量分摊费用"
        }
    }
    
    var icon: String {
        switch self {
        case .equal: return "equal.square"
        case .custom: return "slider.horizontal.3"
        case .usage: return "chart.bar.fill"
        }
    }
}

// MARK: - 家庭使用统计
struct FamilyUsageStatistics {
    let subscriptionId: UUID
    let totalUsageSessions: Int
    let totalUsageTime: TimeInterval
    let memberUsageDistribution: [UUID: MemberUsageStats]
    let costDistribution: [UUID: Double]
    let period: StatisticsPeriod
    let generatedDate: Date
    
    init(subscriptionId: UUID, totalUsageSessions: Int = 0, totalUsageTime: TimeInterval = 0, memberUsageDistribution: [UUID: MemberUsageStats] = [:], costDistribution: [UUID: Double] = [:], period: StatisticsPeriod = .monthly, generatedDate: Date = Date()) {
        self.subscriptionId = subscriptionId
        self.totalUsageSessions = totalUsageSessions
        self.totalUsageTime = totalUsageTime
        self.memberUsageDistribution = memberUsageDistribution
        self.costDistribution = costDistribution
        self.period = period
        self.generatedDate = generatedDate
    }
}

// MARK: - 成员使用统计
struct MemberUsageStats {
    let memberId: UUID
    let usageSessions: Int
    let usageTime: TimeInterval
    let averageSatisfaction: Double
    let usagePercentage: Double // 占总使用时间的百分比
    let costShare: Double // 分摊的费用
    
    var averageSessionDuration: TimeInterval {
        guard usageSessions > 0 else { return 0 }
        return usageTime / Double(usageSessions)
    }
}

// MARK: - 统计周期枚举
enum StatisticsPeriod: String, CaseIterable, Identifiable, Codable {
    case weekly = "weekly"
    case monthly = "monthly"
    case quarterly = "quarterly"
    case yearly = "yearly"
    case custom = "custom"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .weekly: return "本周"
        case .monthly: return "本月"
        case .quarterly: return "本季度"
        case .yearly: return "本年"
        case .custom: return "自定义"
        }
    }
}