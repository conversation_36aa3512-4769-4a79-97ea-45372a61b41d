import Foundation
import CoreData
import SwiftUI

extension LoanRecord {
    // 借阅状态
    enum LoanStatus: String, CaseIterable, Identifiable {
        case active = "借出中"
        case overdue = "已逾期"
        case returned = "已归还"
        case lost = "已丢失"
        
        var id: String { self.rawValue }
        
        var color: Color {
            switch self {
            case .active: return .blue
            case .overdue: return .red
            case .returned: return .green
            case .lost: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .active: return "arrow.up.forward.circle"
            case .overdue: return "exclamationmark.circle"
            case .returned: return "checkmark.circle"
            case .lost: return "xmark.circle"
            }
        }
    }
    
    // 计算属性：借阅状态
    var loanStatus: LoanStatus {
        if let statusString = status {
            if statusString == "returned" {
                return .returned
            } else if statusString == "lost" {
                return .lost
            }
        }
        
        // 检查是否逾期
        if let dueDate = dueDate, returnDate == nil {
            if dueDate < Date() {
                return .overdue
            }
        }
        
        return .active
    }
    
    // 计算属性：剩余天数
    var daysRemaining: Int? {
        guard let dueDate = dueDate, returnDate == nil else { return nil }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: dueDate)
        return components.day
    }
    
    // 计算属性：借阅时长（天）
    var loanDuration: Int? {
        guard let createdAt = createdAt else { return nil }
        
        let endDate = returnDate ?? Date()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: createdAt, to: endDate)
        return components.day
    }
    
    // 计算属性：是否即将到期（7天内）
    var isNearDueDate: Bool {
        guard let daysRemaining = daysRemaining else { return false }
        return daysRemaining >= 0 && daysRemaining <= 7
    }
    
    // 计算属性：是否逾期
    var isOverdue: Bool {
        return loanStatus == .overdue
    }
}
