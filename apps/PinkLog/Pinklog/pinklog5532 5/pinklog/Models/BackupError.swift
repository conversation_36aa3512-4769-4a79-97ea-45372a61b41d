import Foundation
import CloudKit

// MARK: - 备份错误类型

/// 备份系统统一错误类型
enum BackupError: LocalizedError {
    
    // MARK: - 网络错误
    case networkUnavailable
    case networkTimeout
    case networkConnectionFailed(String)
    case wifiRequired
    case networkTooSlow
    case dataLimitExceeded
    
    // MARK: - CloudKit错误
    case cloudKitAccountUnavailable
    case cloudKitQuotaExceeded
    case cloudKitPermissionDenied
    case cloudKitServerError(Int)
    case cloudKitSyncConflict
    case cloudKitAssetUploadFailed(String)
    case cloudKitRecordNotFound
    case cloudKitZoneNotFound
    case cloudKitUserDeletedZone
    case iCloudDocumentsUnavailable
    
    // MARK: - 数据错误
    case dataCorrupted(String)
    case dataFormatInvalid
    case dataVersionIncompatible(String)
    case dataExportFailed(String)
    case dataImportFailed(String)
    case dataValidationFailed(String)
    case coreDataError(String)
    case binaryDataMissing(String)
    case checksumMismatch
    
    // MARK: - 备份操作错误
    case backupInProgress
    case restoreInProgress
    case operationCancelled
    case operationTimeout
    case insufficientStorage
    case backupVersionNotFound(String)
    case backupVersionCorrupted(String)
    case maxBackupVersionsReached
    
    // MARK: - 系统错误
    case deviceStorageFull
    case lowBattery
    case backgroundTaskExpired
    case permissionDenied(String)
    case systemResourceUnavailable
    case unknownError(Error)
    
    // MARK: - 错误描述
    var errorDescription: String? {
        switch self {
        // 网络错误
        case .networkUnavailable:
            return "网络连接不可用"
        case .networkTimeout:
            return "网络连接超时"
        case .networkConnectionFailed(let details):
            return "网络连接失败: \(details)"
        case .wifiRequired:
            return "需要WiFi连接才能进行备份"
        case .networkTooSlow:
            return "网络速度过慢，无法完成备份"
        case .dataLimitExceeded:
            return "数据传输量超出限制"
            
        // CloudKit错误
        case .cloudKitAccountUnavailable:
            return "iCloud账户不可用，请检查登录状态"
        case .cloudKitQuotaExceeded:
            return "iCloud存储空间不足"
        case .cloudKitPermissionDenied:
            return "iCloud权限被拒绝"
        case .cloudKitServerError(let code):
            return "iCloud服务器错误 (代码: \(code))"
        case .cloudKitSyncConflict:
            return "iCloud同步冲突"
        case .cloudKitAssetUploadFailed(let details):
            return "文件上传失败: \(details)"
        case .cloudKitRecordNotFound:
            return "iCloud记录未找到"
        case .cloudKitZoneNotFound:
            return "iCloud数据区域未找到"
        case .cloudKitUserDeletedZone:
            return "用户已删除iCloud数据区域"
        case .iCloudDocumentsUnavailable:
            return "iCloud Documents不可用"
            
        // 数据错误
        case .dataCorrupted(let details):
            return "数据已损坏: \(details)"
        case .dataFormatInvalid:
            return "数据格式无效"
        case .dataVersionIncompatible(let version):
            return "数据版本不兼容: \(version)"
        case .dataExportFailed(let details):
            return "数据导出失败: \(details)"
        case .dataImportFailed(let details):
            return "数据导入失败: \(details)"
        case .dataValidationFailed(let details):
            return "数据验证失败: \(details)"
        case .coreDataError(let details):
            return "数据库错误: \(details)"
        case .binaryDataMissing(let filename):
            return "文件缺失: \(filename)"
        case .checksumMismatch:
            return "数据校验失败"
            
        // 备份操作错误
        case .backupInProgress:
            return "备份正在进行中"
        case .restoreInProgress:
            return "恢复正在进行中"
        case .operationCancelled:
            return "操作已取消"
        case .operationTimeout:
            return "操作超时"
        case .insufficientStorage:
            return "存储空间不足"
        case .backupVersionNotFound(let versionId):
            return "备份版本未找到: \(versionId)"
        case .backupVersionCorrupted(let versionId):
            return "备份版本已损坏: \(versionId)"
        case .maxBackupVersionsReached:
            return "已达到最大备份版本数限制"
            
        // 系统错误
        case .deviceStorageFull:
            return "设备存储空间已满"
        case .lowBattery:
            return "电量过低，无法执行备份"
        case .backgroundTaskExpired:
            return "后台任务已过期"
        case .permissionDenied(let permission):
            return "权限被拒绝: \(permission)"
        case .systemResourceUnavailable:
            return "系统资源不可用"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 错误恢复建议
    var recoverySuggestion: String? {
        switch self {
        // 网络错误
        case .networkUnavailable:
            return "请检查网络连接并重试"
        case .networkTimeout:
            return "请检查网络稳定性，稍后重试"
        case .networkConnectionFailed:
            return "请检查网络设置或切换网络后重试"
        case .wifiRequired:
            return "请连接WiFi网络后重试"
        case .networkTooSlow:
            return "请等待网络状况改善或切换到更快的网络"
        case .dataLimitExceeded:
            return "请在WiFi环境下重试或联系技术支持"
            
        // CloudKit错误
        case .cloudKitAccountUnavailable:
            return "请在设置中登录iCloud账户"
        case .cloudKitQuotaExceeded:
            return "请清理iCloud存储空间或升级存储计划"
        case .cloudKitPermissionDenied:
            return "请在设置中允许应用访问iCloud"
        case .cloudKitServerError:
            return "iCloud服务暂时不可用，请稍后重试"
        case .cloudKitSyncConflict:
            return "请稍后重试，系统将自动解决冲突"
        case .cloudKitAssetUploadFailed:
            return "请检查网络连接并重试"
        case .cloudKitRecordNotFound, .cloudKitZoneNotFound:
            return "请重新初始化备份设置"
        case .cloudKitUserDeletedZone:
            return "请重新设置iCloud备份"
        case .iCloudDocumentsUnavailable:
            return "请检查iCloud Documents的可用性"
            
        // 数据错误
        case .dataCorrupted, .dataFormatInvalid:
            return "请尝试重新备份或联系技术支持"
        case .dataVersionIncompatible:
            return "请更新应用到最新版本"
        case .dataExportFailed, .dataImportFailed:
            return "请重试或检查数据完整性"
        case .dataValidationFailed:
            return "请检查数据完整性或重新备份"
        case .coreDataError:
            return "请重启应用或联系技术支持"
        case .binaryDataMissing:
            return "请重新备份相关文件"
        case .checksumMismatch:
            return "数据可能已损坏，请重新备份"
            
        // 备份操作错误
        case .backupInProgress, .restoreInProgress:
            return "请等待当前操作完成"
        case .operationCancelled:
            return "操作已取消，可以重新开始"
        case .operationTimeout:
            return "操作超时，请重试"
        case .insufficientStorage:
            return "请清理设备存储空间"
        case .backupVersionNotFound:
            return "请选择其他备份版本"
        case .backupVersionCorrupted:
            return "请选择其他备份版本或重新备份"
        case .maxBackupVersionsReached:
            return "请删除旧的备份版本"
            
        // 系统错误
        case .deviceStorageFull:
            return "请清理设备存储空间"
        case .lowBattery:
            return "请连接充电器或等待电量充足"
        case .backgroundTaskExpired:
            return "请在前台重新执行操作"
        case .permissionDenied:
            return "请在设置中授予相应权限"
        case .systemResourceUnavailable:
            return "请重启应用或设备"
        case .unknownError:
            return "请重试或联系技术支持"
        }
    }
    
    // MARK: - 错误严重程度
    var severity: ErrorSeverity {
        switch self {
        case .networkUnavailable, .networkTimeout, .networkConnectionFailed, .networkTooSlow:
            return .warning
        case .wifiRequired, .dataLimitExceeded:
            return .info
        case .cloudKitAccountUnavailable, .cloudKitQuotaExceeded, .cloudKitPermissionDenied:
            return .error
        case .cloudKitServerError, .cloudKitSyncConflict, .cloudKitAssetUploadFailed:
            return .warning
        case .cloudKitRecordNotFound, .cloudKitZoneNotFound, .cloudKitUserDeletedZone:
            return .error
        case .dataCorrupted, .dataFormatInvalid, .dataVersionIncompatible:
            return .critical
        case .dataExportFailed, .dataImportFailed, .dataValidationFailed:
            return .error
        case .coreDataError, .binaryDataMissing, .checksumMismatch:
            return .critical
        case .backupInProgress, .restoreInProgress, .operationCancelled:
            return .info
        case .operationTimeout, .insufficientStorage:
            return .warning
        case .backupVersionNotFound, .backupVersionCorrupted:
            return .error
        case .maxBackupVersionsReached:
            return .warning
        case .deviceStorageFull, .lowBattery:
            return .warning
        case .backgroundTaskExpired, .permissionDenied:
            return .error
        case .systemResourceUnavailable, .unknownError:
            return .critical
        case .iCloudDocumentsUnavailable:
            return .error
        }
    }
    
    // MARK: - 是否可重试
    var isRetryable: Bool {
        switch self {
        case .networkUnavailable, .networkTimeout, .networkConnectionFailed, .networkTooSlow:
            return true
        case .wifiRequired, .dataLimitExceeded:
            return false
        case .cloudKitServerError, .cloudKitSyncConflict, .cloudKitAssetUploadFailed:
            return true
        case .cloudKitAccountUnavailable, .cloudKitQuotaExceeded, .cloudKitPermissionDenied:
            return false
        case .cloudKitRecordNotFound, .cloudKitZoneNotFound, .cloudKitUserDeletedZone:
            return false
        case .dataExportFailed, .dataImportFailed:
            return true
        case .dataCorrupted, .dataFormatInvalid, .dataVersionIncompatible:
            return false
        case .dataValidationFailed, .coreDataError, .binaryDataMissing, .checksumMismatch:
            return false
        case .operationTimeout, .insufficientStorage:
            return true
        case .backupInProgress, .restoreInProgress, .operationCancelled:
            return false
        case .backupVersionNotFound, .backupVersionCorrupted, .maxBackupVersionsReached:
            return false
        case .lowBattery, .backgroundTaskExpired:
            return true
        case .deviceStorageFull, .permissionDenied, .systemResourceUnavailable:
            return false
        case .unknownError:
            return true
        case .iCloudDocumentsUnavailable:
            return false
        }
    }
}

// MARK: - 错误严重程度

enum ErrorSeverity: Int, CaseIterable {
    case info = 0
    case warning = 1
    case error = 2
    case critical = 3
    
    var displayName: String {
        switch self {
        case .info:
            return "信息"
        case .warning:
            return "警告"
        case .error:
            return "错误"
        case .critical:
            return "严重错误"
        }
    }
    
    var color: String {
        switch self {
        case .info:
            return "blue"
        case .warning:
            return "orange"
        case .error:
            return "red"
        case .critical:
            return "purple"
        }
    }
}
