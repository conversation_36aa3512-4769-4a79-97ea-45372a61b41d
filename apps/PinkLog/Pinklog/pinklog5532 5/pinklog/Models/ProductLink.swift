import Foundation
import CoreData
import SwiftUI

// MARK: - ProductLink 实体
extension ProductLink {
    // 初始化新的 ProductLink
        static func create(sourceProduct: Product, targetProduct: Product, relationshipType: ProductRelationshipType, notes: String? = nil, strength: Int16 = 3, isBidirectional: Bool = false, groupName: String? = nil, groupID: String? = nil, context: NSManagedObjectContext) -> ProductLink {
            let link = ProductLink(context: context)
            link.id = UUID()
            link.sourceProduct = sourceProduct
            link.targetProduct = targetProduct
            link.relationshipType = relationshipType.rawValue
            link.notes = notes
            link.strength = strength 
            link.isBidirectional = isBidirectional
            link.createdAt = Date()
            link.lastUsedTogether = nil
            link.usageCount = 0
            link.groupName = groupName
            link.groupID = groupID
        
            return link
        }
    
    // 删除链接
    func delete() {
        guard let context = self.managedObjectContext else { return }
        context.delete(self)
    }
    
    // 增加共同使用计数
    func incrementUsageCount() {
        // 增加计数
        self.usageCount += 1
        lastUsedTogether = Date()
    }
    
    // 获取反向链接（不论是否标记为双向关系）
    func getReverseLink() -> ProductLink? {
        // 增强防御性检查
        guard let source = sourceProduct, 
              let target = targetProduct, 
              let context = managedObjectContext else { 
            print("Unable to get reverse link: missing required values")
            return nil 
        }
        
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        // 查找方向相反的链接，无论其isBidirectional标记如何
        request.predicate = NSPredicate(format: "sourceProduct == %@ AND targetProduct == %@", target, source)
        request.fetchLimit = 1
        
        do {
            let results = try context.fetch(request)
            return results.first
        } catch {
            print("Error fetching reverse link: \(error)")
            return nil
        }
    }
    
    // 获取关系类型枚举
    var relationshipTypeEnum: ProductRelationshipType {
        get {
            // 安全处理可能为空的relationshipType
            guard let type = relationshipType, !type.isEmpty else {
                return .other
            }
            return ProductRelationshipType(rawValue: type) ?? .other
        }
        set {
            relationshipType = newValue.rawValue
        }
    }
    
    // 获取最后使用的格式化文本
    var lastUsedFormattedText: String {
        guard let date = lastUsedTogether else {
            return "从未一起使用"
        }
        
        // 确保日期有效
        if date > Date() || date.timeIntervalSince1970 < 0 {
            return "使用时间记录有误"
        }
        
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    // 获取使用频率文本
    var usageFrequencyText: String {
        if usageCount == 0 {
            return "从未一起使用"
        } else if usageCount == 1 {
            return "一起使用过一次"
        } else {
            return "一起使用过 \(usageCount) 次"
        }
    }
    
    // 检查产品是否为关联产品的链接
    static func existsLink(sourceProduct: Product, targetProduct: Product, context: NSManagedObjectContext) -> Bool {
        // 防御性检查：确保产品ID存在
        guard let sourceID = sourceProduct.id, let targetID = targetProduct.id else {
            print("Cannot check link existence: product ID missing")
            return false
        }
        
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        // 检查双向关联：任一方向的链接都视为存在
        request.predicate = NSPredicate(format: "(sourceProduct == %@ AND targetProduct == %@) OR (sourceProduct == %@ AND targetProduct == %@)", 
                                        sourceProduct, targetProduct, targetProduct, sourceProduct)
        request.fetchLimit = 1
        
        do {
            // 获取链接而不是计数，以便我们可以检查链接的属性
            let links = try context.fetch(request)
            
            // 只要存在任何链接，就返回true
            // 在处理双向关系时，这防止了重复创建
            if !links.isEmpty {
                return true
            }
            
            return false
        } catch {
            print("Error checking for existing link: \(error)")
            return false
        }
    }
    
    // 查找两个产品之间的链接
    static func findLink(between product1: Product, and product2: Product, context: NSManagedObjectContext) -> ProductLink? {
        // 防御性检查：确保产品ID存在
        guard let id1 = product1.id, let id2 = product2.id else {
            print("Cannot find link: product ID missing")
            return nil
        }
        
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        request.predicate = NSPredicate(format: "(sourceProduct == %@ AND targetProduct == %@) OR (sourceProduct == %@ AND targetProduct == %@)", 
                                       product1, product2, product2, product1)
        // 按照创建时间排序，获取最新的链接
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ProductLink.createdAt, ascending: false)]
        request.fetchLimit = 1
        // 预取产品关系以提高性能
        request.relationshipKeyPathsForPrefetching = ["sourceProduct", "targetProduct"]
        
        do {
            let results = try context.fetch(request)
            return results.first
        } catch {
            print("Error finding link between products: \(error)")
            return nil
        }
    }
    
    // 为产品获取所有链接
    static func getLinks(for product: Product, context: NSManagedObjectContext) -> [ProductLink] {
        // 防御性检查：确保产品ID存在
        guard let productID = product.id else {
            print("Cannot get links: product ID missing")
            return []
        }
        
        let request: NSFetchRequest<ProductLink> = ProductLink.fetchRequest()
        request.predicate = NSPredicate(format: "sourceProduct == %@ OR (targetProduct == %@ AND isBidirectional == YES)", product, product)
        // 预取产品关系以提高性能
        request.relationshipKeyPathsForPrefetching = ["sourceProduct", "targetProduct"]
        
        do {
            let links = try context.fetch(request)
            // 额外安全检查：过滤掉可能的无效链接
            return links.filter { link in
                return link.sourceProduct != nil && link.targetProduct != nil
            }
        } catch {
            print("Error fetching links for product: \(error)")
            return []
        }
    }
}