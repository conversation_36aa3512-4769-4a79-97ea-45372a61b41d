//
//  SubscriptionDataModels.swift
//  pinklog
//
//  Created by AI Assistant on 2025/1/20.
//  虚拟订阅管理 - 数据模型定义
//

import Foundation
import SwiftUI

// MARK: - 订阅计费周期枚举
enum BillingCycle: String, CaseIterable, Identifiable, Codable {
    case monthly = "monthly"
    case quarterly = "quarterly"
    case semiAnnual = "semiAnnual"
    case annual = "annual"
    case custom = "custom"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .monthly: return "月度"
        case .quarterly: return "季度"
        case .semiAnnual: return "半年度"
        case .annual: return "年度"
        case .custom: return "自定义"
        }
    }
    
    var icon: String {
        switch self {
        case .monthly: return "calendar"
        case .quarterly: return "calendar.badge.clock"
        case .semiAnnual: return "calendar.badge.plus"
        case .annual: return "calendar.circle"
        case .custom: return "calendar.badge.exclamationmark"
        }
    }
    
    // 计算下次续订日期
    func nextRenewalDate(from startDate: Date, customDays: Int = 0) -> Date {
        let calendar = Calendar.current
        switch self {
        case .monthly:
            return calendar.date(byAdding: .month, value: 1, to: startDate) ?? startDate
        case .quarterly:
            return calendar.date(byAdding: .month, value: 3, to: startDate) ?? startDate
        case .semiAnnual:
            return calendar.date(byAdding: .month, value: 6, to: startDate) ?? startDate
        case .annual:
            return calendar.date(byAdding: .year, value: 1, to: startDate) ?? startDate
        case .custom:
            return calendar.date(byAdding: .day, value: customDays, to: startDate) ?? startDate
        }
    }
    
    // 计算周期内的天数
    var daysInCycle: Int {
        switch self {
        case .monthly: return 30
        case .quarterly: return 90
        case .semiAnnual: return 180
        case .annual: return 365
        case .custom: return 30 // 默认值，实际使用时会被覆盖
        }
    }
}

// MARK: - 订阅状态枚举
enum SubscriptionStatus: String, CaseIterable, Identifiable, Codable {
    case active = "active"
    case paused = "paused"
    case cancelled = "cancelled"
    case expired = "expired"
    case trial = "trial"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .active: return "活跃"
        case .paused: return "暂停"
        case .cancelled: return "已取消"
        case .expired: return "已过期"
        case .trial: return "试用中"
        }
    }
    
    var color: Color {
        switch self {
        case .active: return .green
        case .paused: return .orange
        case .cancelled: return .red
        case .expired: return .gray
        case .trial: return .blue
        }
    }
    
    var icon: String {
        switch self {
        case .active: return "checkmark.circle.fill"
        case .paused: return "pause.circle.fill"
        case .cancelled: return "xmark.circle.fill"
        case .expired: return "clock.circle.fill"
        case .trial: return "star.circle.fill"
        }
    }
}

// MARK: - 使用会话类型枚举
enum UsageSessionType: String, CaseIterable, Identifiable, Codable {
    case active = "active"
    case background = "background"
    case shared = "shared"
    case family = "family"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .active: return "主动使用"
        case .background: return "后台使用"
        case .shared: return "共享使用"
        case .family: return "家庭使用"
        }
    }
    
    var icon: String {
        switch self {
        case .active: return "person.fill"
        case .background: return "gear.circle"
        case .shared: return "person.2.fill"
        case .family: return "house.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .active: return .blue
        case .background: return .gray
        case .shared: return .green
        case .family: return .purple
        }
    }
}

// MARK: - 订阅数据模型
struct SubscriptionData: Identifiable, Codable {
    let id: UUID
    var name: String
    var serviceProvider: String
    var billingCycle: BillingCycle
    var costPerCycle: Double
    var currency: String
    var startDate: Date
    var nextRenewalDate: Date
    var status: SubscriptionStatus
    var description: String?
    var category: String?
    var customCycleDays: Int?
    var isShared: Bool
    var familyMemberCount: Int
    var notes: String?
    
    // 计算属性
    var monthlyCost: Double {
        switch billingCycle {
        case .monthly:
            return costPerCycle
        case .quarterly:
            return costPerCycle / 3
        case .semiAnnual:
            return costPerCycle / 6
        case .annual:
            return costPerCycle / 12
        case .custom:
            let days = customCycleDays ?? 30
            return costPerCycle * 30 / Double(days)
        }
    }
    
    var annualCost: Double {
        return monthlyCost * 12
    }
    
    var costPerDay: Double {
        let days = customCycleDays ?? billingCycle.daysInCycle
        return costPerCycle / Double(days)
    }
    
    var costPerPerson: Double {
        guard isShared && familyMemberCount > 0 else { return costPerCycle }
        return costPerCycle / Double(familyMemberCount)
    }
    
    var daysUntilRenewal: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: nextRenewalDate)
        return max(0, components.day ?? 0)
    }
    
    var isNearRenewal: Bool {
        return daysUntilRenewal <= 7
    }
    
    init(id: UUID = UUID(), name: String, serviceProvider: String, billingCycle: BillingCycle, costPerCycle: Double, currency: String = "CNY", startDate: Date = Date(), status: SubscriptionStatus = .active, description: String? = nil, category: String? = nil, customCycleDays: Int? = nil, isShared: Bool = false, familyMemberCount: Int = 1, notes: String? = nil) {
        self.id = id
        self.name = name
        self.serviceProvider = serviceProvider
        self.billingCycle = billingCycle
        self.costPerCycle = costPerCycle
        self.currency = currency
        self.startDate = startDate
        self.status = status
        self.description = description
        self.category = category
        self.customCycleDays = customCycleDays
        self.isShared = isShared
        self.familyMemberCount = max(1, familyMemberCount)
        self.notes = notes
        
        // 计算下次续订日期
        self.nextRenewalDate = billingCycle.nextRenewalDate(from: startDate, customDays: customCycleDays ?? 0)
    }
}

// MARK: - 使用会话数据模型
struct UsageSession: Identifiable, Codable {
    let id: UUID
    var subscriptionId: UUID
    var startTime: Date
    var endTime: Date?
    var duration: TimeInterval // 秒
    var sessionType: UsageSessionType
    var activityDescription: String?
    var satisfactionRating: Int // 1-5
    var emotionalValue: Int // 1-5
    var notes: String?
    var familyMember: String? // 家庭成员名称
    
    // 计算属性
    var isActive: Bool {
        return endTime == nil
    }
    
    var durationInMinutes: Double {
        return duration / 60
    }
    
    var durationInHours: Double {
        return duration / 3600
    }
    
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    init(id: UUID = UUID(), subscriptionId: UUID, startTime: Date = Date(), duration: TimeInterval = 0, sessionType: UsageSessionType = .active, activityDescription: String? = nil, satisfactionRating: Int = 3, emotionalValue: Int = 3, notes: String? = nil, familyMember: String? = nil) {
        self.id = id
        self.subscriptionId = subscriptionId
        self.startTime = startTime
        self.endTime = duration > 0 ? startTime.addingTimeInterval(duration) : nil
        self.duration = duration
        self.sessionType = sessionType
        self.activityDescription = activityDescription
        self.satisfactionRating = max(1, min(5, satisfactionRating))
        self.emotionalValue = max(1, min(5, emotionalValue))
        self.notes = notes
        self.familyMember = familyMember
    }
    
    // 结束会话
    mutating func endSession() {
        guard endTime == nil else { return }
        let now = Date()
        self.endTime = now
        self.duration = now.timeIntervalSince(startTime)
    }
}

// MARK: - 计费历史记录
struct BillingHistory: Identifiable, Codable {
    let id: UUID
    var subscriptionId: UUID
    var billingDate: Date
    var amount: Double
    var currency: String
    var billingCycle: BillingCycle
    var paymentMethod: String?
    var transactionId: String?
    var notes: String?
    var isRefunded: Bool
    var refundDate: Date?
    var refundAmount: Double?
    
    init(id: UUID = UUID(), subscriptionId: UUID, billingDate: Date, amount: Double, currency: String = "CNY", billingCycle: BillingCycle, paymentMethod: String? = nil, transactionId: String? = nil, notes: String? = nil, isRefunded: Bool = false, refundDate: Date? = nil, refundAmount: Double? = nil) {
        self.id = id
        self.subscriptionId = subscriptionId
        self.billingDate = billingDate
        self.amount = amount
        self.currency = currency
        self.billingCycle = billingCycle
        self.paymentMethod = paymentMethod
        self.transactionId = transactionId
        self.notes = notes
        self.isRefunded = isRefunded
        self.refundDate = refundDate
        self.refundAmount = refundAmount
    }
}

// MARK: - 订阅分析数据
struct SubscriptionAnalytics {
    let subscriptionId: UUID
    let totalUsageSessions: Int
    let totalUsageTime: TimeInterval
    let averageSessionDuration: TimeInterval
    let averageSatisfactionRating: Double
    let averageEmotionalValue: Double
    let costPerSession: Double
    let costPerHour: Double
    let usageFrequency: Double // 每月使用次数
    let valueScore: Double // 综合价值评分 (0-100)
    let lastUsedDate: Date?
    let daysSinceLastUse: Int
    
    // 使用趋势（最近30天 vs 之前30天）
    let usageTrend: Double // -1.0 到 1.0，正值表示使用增加
    let satisfactionTrend: Double // -1.0 到 1.0，正值表示满意度提升
    
    // 家庭使用统计（如果是共享订阅）
    let familyUsageDistribution: [String: Double]? // 成员名称 -> 使用时长占比
}

// MARK: - 订阅优化建议
struct SubscriptionRecommendation: Identifiable {
    let id: UUID
    let subscriptionId: UUID
    let type: RecommendationType
    let title: String
    let description: String
    let potentialSavings: Double?
    let priority: Priority
    let actionRequired: String
    let createdDate: Date
    let isRead: Bool
    
    enum RecommendationType: String, CaseIterable {
        case underutilized = "underutilized"
        case overpriced = "overpriced"
        case duplicate = "duplicate"
        case upgrade = "upgrade"
        case downgrade = "downgrade"
        case cancel = "cancel"
        case switchProvider = "switchProvider"
        case annualDiscount = "annualDiscount"
        
        var icon: String {
            switch self {
            case .underutilized: return "chart.line.downtrend.xyaxis"
            case .overpriced: return "dollarsign.circle"
            case .duplicate: return "doc.on.doc"
            case .upgrade: return "arrow.up.circle"
            case .downgrade: return "arrow.down.circle"
            case .cancel: return "xmark.circle"
            case .switchProvider: return "arrow.triangle.swap"
            case .annualDiscount: return "percent"
            }
        }
        
        var color: Color {
            switch self {
            case .underutilized: return .orange
            case .overpriced: return .red
            case .duplicate: return .yellow
            case .upgrade: return .green
            case .downgrade: return .blue
            case .cancel: return .red
            case .switchProvider: return .purple
            case .annualDiscount: return .green
            }
        }
    }
    
    enum Priority: String, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case urgent = "urgent"
        
        var color: Color {
            switch self {
            case .low: return .gray
            case .medium: return .blue
            case .high: return .orange
            case .urgent: return .red
            }
        }
        
        var displayName: String {
            switch self {
            case .low: return "低"
            case .medium: return "中"
            case .high: return "高"
            case .urgent: return "紧急"
            }
        }
    }
}