import Foundation
import CoreData
import SwiftUI

// MARK: - 订阅成本分析结构
struct SubscriptionCostAnalysis {
    let monthlyCost: Double
    let costPerUse: Double
    let annualCost: Double
    let recommendation: String
}

extension Product {
    // 产品状态
    enum ProductStatus: String, CaseIterable, Identifiable {
        case core = "核心产品"
        case worthwhile = "值得拥有"
        case potential = "潜力股"
        case reconsider = "需要重新考虑"
        case sellRecommended = "建议出售"
        
        // 虚拟订阅特有状态
        case subscriptionActive = "订阅激活"
        case subscriptionPaused = "订阅暂停"
        case subscriptionCancelled = "订阅取消"
        case subscriptionExpired = "订阅过期"
        case subscriptionTrial = "试用期"

        var id: String { self.rawValue }

        var color: Color {
            switch self {
            case .core: return .green
            case .worthwhile: return .blue
            case .potential: return .orange
            case .reconsider: return .yellow
            case .sellRecommended: return .red
            case .subscriptionActive: return .green
            case .subscriptionPaused: return .orange
            case .subscriptionCancelled: return .red
            case .subscriptionExpired: return .gray
            case .subscriptionTrial: return .purple
            }
        }
        
        var icon: String {
            switch self {
            case .core: return "star.fill"
            case .worthwhile: return "checkmark.circle.fill"
            case .potential: return "arrow.up.circle.fill"
            case .reconsider: return "exclamationmark.triangle.fill"
            case .sellRecommended: return "minus.circle.fill"
            case .subscriptionActive: return "checkmark.circle.fill"
            case .subscriptionPaused: return "pause.circle.fill"
            case .subscriptionCancelled: return "xmark.circle.fill"
            case .subscriptionExpired: return "clock.fill"
            case .subscriptionTrial: return "timer"
            }
        }
        
        var displayName: String {
            return self.rawValue
        }
    }

    // 值度指数计算（简化版本，分析功能已移除）
    func worthItIndex() -> Double {
        // 虚拟订阅的值度指数计算
        if isVirtualProduct {
            return calculateSubscriptionWorthItIndex()
        }
        
        // 简化的值度指数计算
        let usageScore = min(effectiveUsage / 10.0, 10.0) * 10 // 使用频率得分，最高100分
        let costScore = max(0, 100 - (costPerUse / 10.0)) // 成本效率得分，成本越低得分越高
        let satisfactionScore = averageSatisfaction * 20 // 满意度得分，5分制转100分制

        // 简单平均计算
        let totalScore = (usageScore + costScore + satisfactionScore) / 3.0

        return max(0, min(100, totalScore))
    }
    
    // 虚拟订阅产品的值度指数计算
    private func calculateSubscriptionWorthItIndex() -> Double {
        let usageScore = calculateSubscriptionUsageScore()
        let costScore = calculateSubscriptionCostScore()
        let satisfactionScore = averageSatisfaction * 20
        let continuityScore = calculateSubscriptionContinuityScore()
        
        // 订阅产品权重分配：使用频率30%，成本效率25%，满意度30%，连续性15%
        let weightedScore = usageScore * 0.3 + costScore * 0.25 + satisfactionScore * 0.3 + continuityScore * 0.15
        
        return max(0, min(100, weightedScore))
    }
    
    // 订阅产品使用频率评分
    private func calculateSubscriptionUsageScore() -> Double {
        let monthlyUsage = monthlyUsageFrequency
        let expectedUsage = Double(expectedUsageFrequency ?? "0") ?? 0
        
        if expectedUsage > 0 {
            let usageRatio = monthlyUsage / expectedUsage
            return min(100, usageRatio * 100)
        }
        
        // 如果没有预期使用频率，基于实际使用频率评分
        return min(100, monthlyUsage * 10)
    }
    
    // 订阅产品成本效率评分
    private func calculateSubscriptionCostScore() -> Double {
        let monthlyCost = price / 12.0 // 假设价格是年费
        let usageCount = monthlyUsageFrequency
        
        if usageCount > 0 {
            let costPerUse = monthlyCost / usageCount
            // 成本越低分数越高，基于每次使用成本
            return max(0, min(100, 100 - costPerUse))
        }
        
        return 0
    }
    
    // 订阅连续性评分
    private func calculateSubscriptionContinuityScore() -> Double {
        let subscriptionStatus = currentSubscriptionStatus
        
        switch subscriptionStatus {
        case .subscriptionActive:
            return 100
        case .subscriptionTrial:
            return 80
        case .subscriptionPaused:
            return 40
        case .subscriptionCancelled:
            return 20
        case .subscriptionExpired:
            return 0
        default:
            return 50
        }
    }

    // 改进的值度指数计算
    func improvedWorthItIndex(allProducts: [Product] = []) -> Double {
        let lifecycleStage = ProductLifecycleStage(daysSincePurchase: daysSincePurchase)
        let categoryBenchmark = CategoryBenchmark.calculate(for: category, products: allProducts)

        // 1. 标准化各项指标
        let normalizedUsageScore = calculateNormalizedUsageScore(benchmark: categoryBenchmark)
        let normalizedCostScore = calculateNormalizedCostScore(benchmark: categoryBenchmark)
        let normalizedSatisfactionScore = calculateNormalizedSatisfactionScore(benchmark: categoryBenchmark)

        // 2. 应用生命周期权重
        let weights = lifecycleStage.getWeights()

        // 3. 加权计算
        let weightedScore = normalizedUsageScore * weights.usage +
                           normalizedCostScore * weights.cost +
                           normalizedSatisfactionScore * weights.satisfaction

        // 4. 应用置信度调整
        let confidence = ConfidenceMetrics.calculate(for: self)
        let adjustedScore = weightedScore * (0.5 + confidence.factor * 0.5) // 置信度影响50%

        return max(0, min(100, adjustedScore))
    }

    // 产品总使用次数
    var totalUsageCount: Int {
        return usageRecords?.count ?? 0
    }

    // 产品平均满意度
    var averageSatisfaction: Double {
        guard let context = managedObjectContext else {
            return Double(initialSatisfaction)
        }

        var result: Double = Double(initialSatisfaction)
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                result = Double(initialSatisfaction)
                return
            }

            let totalSatisfaction = records.reduce(0) { $0 + Double($1.satisfaction) }
            let average = totalSatisfaction / Double(records.count)

            // 安全检查，防止NaN或无限值
            if average.isNaN || average.isInfinite {
                result = Double(initialSatisfaction)
            } else {
                result = average
            }
        }

        return result
    }

    // 总持有成本
    var totalCostOfOwnership: Double {
        var total = price
        if let expenses = relatedExpenses?.allObjects as? [RelatedExpense] {
            total += expenses.reduce(0) { $0 + $1.amount }
        }
        return total
    }

    // 单次使用成本（根据计算方式返回每次或每天成本）
    var costPerUse: Double {
        let usage = effectiveUsage
        if usage > 0 {
            return totalCostOfOwnership / usage
        }
        return totalCostOfOwnership
    }

    // 产品是否即将过期
    var isNearExpiry: Bool {
        guard let expiryDate = expiryDate else { return false }

        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: expiryDate)

        // 如果在30天内过期，则返回true
        return components.day ?? Int.max <= 30 && components.day ?? Int.min >= 0
    }

    // 产品是否即将过保
    var isNearWarrantyEnd: Bool {
        guard let warrantyEndDate = warrantyEndDate else { return false }

        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: warrantyEndDate)

        // 如果在30天内过保，则返回true
        return components.day ?? Int.max <= 30 && components.day ?? Int.min >= 0
    }

    // 距离购买日期的天数
    var daysSincePurchase: Int {
        guard let purchaseDate = purchaseDate else { return 0 }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: purchaseDate, to: now)
        return max(0, components.day ?? 0)
    }

    // 是否当前被借出
    var isCurrentlyLoaned: Bool {
        return loanStatus == .loanedOut || loanStatus == .overdue
    }

    // 当前借出信息
    var activeLoanInfo: String? {
        if let activeUsageRecord = activeUsageRecordLoan {
            var info = ""
            if let borrower = activeUsageRecord.borrowerName {
                info += "借给：\(borrower)"
            }
            if let dueDate = activeUsageRecord.dueDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                info += info.isEmpty ? "到期：\(formatter.string(from: dueDate))" : "，到期：\(formatter.string(from: dueDate))"
            }
            return info.isEmpty ? nil : info
        }

        if let activeLoan = activeLoanRecord {
            var info = ""
            if let borrower = activeLoan.borrowerName {
                info += "借给：\(borrower)"
            }
            if let dueDate = activeLoan.dueDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                info += info.isEmpty ? "到期：\(formatter.string(from: dueDate))" : "，到期：\(formatter.string(from: dueDate))"
            }
            return info.isEmpty ? nil : info
        }

        return nil
    }

    // 月度使用频率（基于使用记录计算）
     var monthlyUsageFrequency: Double {
         guard let context = managedObjectContext else { return 0.0 }
         
         var result: Double = 0.0
         context.performAndWait {
             let calendar = Calendar.current
             let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()
             
             let recentUsageRecords = usageRecords?.allObjects as? [UsageRecord] ?? []
             let recentUsage = recentUsageRecords.compactMap { record -> UsageRecord? in
                  guard let date = record.date, date >= thirtyDaysAgo else { return nil }
                  return record
              }
             
             result = Double(recentUsage.count)
         }
         
         return result
     }
     
     // 预期使用频率（可以从产品描述或用户设置中获取，这里提供默认值）
     var expectedUsageFrequencyValue: Int {
         // 可以根据产品类型或用户设置来确定预期使用频率
         // 这里提供一个基于产品类别的默认值
         if let categoryName = category?.name {
             switch categoryName.lowercased() {
             case "娱乐", "视频", "音乐":
                 return 20 // 每月20次
             case "工作", "办公", "生产力":
                 return 25 // 每月25次
             case "学习", "教育":
                 return 15 // 每月15次
             case "健康", "健身":
                 return 12 // 每月12次
             default:
                 return 10 // 默认每月10次
             }
         }
         return 10
     }
     
     // 订阅使用效率
     var subscriptionUsageEfficiency: Double {
         guard isVirtualProduct else { return 0 }
         
         let actualUsage = monthlyUsageFrequency
         let expectedUsage = Double(expectedUsageFrequencyValue)
         
         if expectedUsage > 0 {
             return min(1.0, actualUsage / expectedUsage)
         }
         
         return actualUsage > 0 ? 1.0 : 0.0
     }
    
    // 订阅成本分析
    var subscriptionCostAnalysis: SubscriptionCostAnalysis {
        guard isVirtualProduct else {
            return SubscriptionCostAnalysis(monthlyCost: 0, costPerUse: 0, annualCost: 0, recommendation: "非订阅产品")
        }
        
        let monthlyCost = price / 12.0
        let annualCost = price
        let usageCount = monthlyUsageFrequency
        let costPerUse = usageCount > 0 ? monthlyCost / usageCount : monthlyCost
        
        var recommendation = ""
        if costPerUse <= 5 {
            recommendation = "成本效率很高"
        } else if costPerUse <= 15 {
            recommendation = "成本效率适中"
        } else {
            recommendation = "成本效率较低，考虑寻找替代方案"
        }
        
        return SubscriptionCostAnalysis(
            monthlyCost: monthlyCost,
            costPerUse: costPerUse,
            annualCost: annualCost,
            recommendation: recommendation
        )
    }
    
    /// 转换为完整的产品数据模型（供AI使用）
    func toCompleteData() -> CompleteProductData {
        // 获取标签数据
        let tagData = (tags?.allObjects as? [Tag] ?? []).map { tag in
            CompleteTagData(
                id: tag.id ?? UUID(),
                name: tag.name ?? "",
                color: tag.color,
                type: tag.type
            )
        }

        // 获取使用记录数据
        var usageData: [CompleteUsageRecordData] = []
        if let context = managedObjectContext {
            context.performAndWait {
                let records = usageRecords?.allObjects as? [UsageRecord] ?? []
                usageData = records.map { record in
                    CompleteUsageRecordData(
                        id: record.id ?? UUID(),
                        date: record.date ?? Date(),
                        satisfaction: Int(record.satisfaction),
                        emotionalValue: Int(record.emotionalValue),
                        duration: record.duration,
                        usageType: record.usageType ?? "personal",
                        scenario: record.scenario,
                        title: record.title,
                        notes: record.notes,
                        memories: record.memories,
                        isLoanedOut: record.isLoanedOut,
                        borrowerName: record.borrowerName,
                        contactInfo: record.contactInfo,
                        dueDate: record.dueDate,
                        returnDate: record.returnDate,
                        recipient: record.recipient,
                        transferPrice: record.transferPrice,
                        transferType: record.transferTypeString,
                        wearCondition: record.wearCondition,
                        isStory: record.isStory,
                        hasImages: record.images != nil,
                        hasAudioRecordings: record.audioRecordings != nil
                    )
                }.sorted { $0.date > $1.date }
            }
        }

        // 获取费用数据
        let expenseData = (relatedExpenses?.allObjects as? [RelatedExpense] ?? []).map { expense in
            CompleteExpenseData(
                id: expense.id ?? UUID(),
                amount: expense.amount,
                date: expense.date ?? Date(),
                notes: expense.notes,
                typeName: expense.type?.name ?? "其他",
                typeIcon: expense.type?.icon
            )
        }.sorted { $0.date > $1.date }

        // 获取提醒数据
        let reminderData = reminderArray.map { reminder in
            CompleteReminderData(
                id: reminder.id ?? UUID(),
                date: reminder.date ?? Date(),
                message: reminder.message ?? "",
                reminderType: reminder.reminderType ?? "",
                isActive: reminder.isActive
            )
        }

        // 获取关联产品名称
        let relatedNames = (relatedProducts?.allObjects as? [Product] ?? []).map { $0.name ?? "" }
        let parentNames = (parentProducts?.allObjects as? [Product] ?? []).map { $0.name ?? "" }

        return CompleteProductData(
            id: id ?? UUID(),
            name: name ?? "",
            brand: brand,
            model: model,
            price: price,
            quantity: Int(quantity),
            purchaseDate: purchaseDate ?? Date(),
            purchaseChannel: purchaseChannel,
            purchaseMotivation: purchaseMotivation,
            purchaseNotes: purchaseNotes,
            categoryName: category?.name,
            categoryIcon: category?.icon,
            tags: tagData,
            isVirtualProduct: isVirtualProduct,
            expectedLifespan: Int(expectedLifespan),
            expectedUsageFrequency: String(expectedUsageFrequencyValue),
            valuationMethod: valuationMethod ?? "usage",
            warrantyEndDate: warrantyEndDate,
            warrantyDetails: warrantyDetails,
            warrantyImage: warrantyImage,
            expiryDate: expiryDate,
            initialSatisfaction: Int(initialSatisfaction),
            currentAverageSatisfaction: averageSatisfaction,
            totalUsageCount: totalUsageCount,
            effectiveUsage: effectiveUsage,
            costPerUse: costPerUse,
            averageSatisfaction: averageSatisfaction,
            worthItIndex: worthItIndex(),
            totalCostOfOwnership: totalCostOfOwnership,
            totalRelatedExpenses: expenseData.reduce(0) { $0 + $1.amount },
            daysSincePurchase: daysSincePurchase,
            isNearExpiry: isNearExpiry,
            isNearWarrantyEnd: isNearWarrantyEnd,
            productStatus: status.rawValue,
            loanCount: loanCount,
            isCurrentlyLoaned: isCurrentlyLoaned,
            activeLoanInfo: activeLoanInfo,
            usageRecords: usageData,
            relatedExpenses: expenseData,
            reminders: reminderData,
            relatedProductNames: relatedNames,
            parentProductNames: parentNames,
            hasImages: images != nil,
            imageCount: images != nil ? 1 : 0
        )
    }

    // 产品状态（基于值度指数）
    var status: ProductStatus {
        // 虚拟订阅产品使用特殊状态逻辑
        if isVirtualProduct {
            return currentSubscriptionStatus
        }
        
        let worthIndex = worthItIndex()

        if worthIndex >= 80 {
            return .core
        } else if worthIndex >= 60 {
            return .worthwhile
        } else if worthIndex >= 40 {
            return .potential
        } else if worthIndex >= 20 {
            return .reconsider
        } else {
            return .sellRecommended
        }
    }
    
    // 当前订阅状态
    var currentSubscriptionStatus: ProductStatus {
        guard isVirtualProduct else { return .core }
        
        // 检查是否过期
        if let expiryDate = expiryDate, Date() > expiryDate {
            return .subscriptionExpired
        }
        
        // 检查最近使用情况来判断状态
        if let lastUsage = lastUsageDate {
            let daysSinceLastUse = Calendar.current.dateComponents([.day], from: lastUsage, to: Date()).day ?? 0
            
            if daysSinceLastUse <= 7 {
                return .subscriptionActive
            } else if daysSinceLastUse <= 30 {
                return .subscriptionPaused
            } else {
                return .subscriptionCancelled
            }
        }
        
        // 基于购买时间判断试用期
        if daysSincePurchase <= 30 {
            return .subscriptionTrial
        }
        
        return .subscriptionCancelled
    }
    
    // 订阅剩余天数
    var subscriptionDaysRemaining: Int? {
        guard isVirtualProduct, let expiryDate = expiryDate else { return nil }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: expiryDate)
        return max(0, components.day ?? 0)
    }
    
    // 订阅是否即将到期（7天内）
    var isSubscriptionNearExpiry: Bool {
        guard let daysRemaining = subscriptionDaysRemaining else { return false }
        return daysRemaining <= 7 && daysRemaining > 0
    }
    
    // 订阅续费建议
    var subscriptionRenewalRecommendation: String {
        guard isVirtualProduct else { return "" }
        
        let worthIndex = worthItIndex()
        let usageFreq = monthlyUsageFrequency
        
        if worthIndex >= 70 && usageFreq >= 10 {
            return "强烈建议续费 - 高价值高使用频率"
        } else if worthIndex >= 50 && usageFreq >= 5 {
            return "建议续费 - 中等价值和使用频率"
        } else if worthIndex >= 30 {
            return "考虑续费 - 有一定价值但使用频率较低"
        } else {
            return "不建议续费 - 价值较低或很少使用"
        }
    }

    // 最近一次使用日期
    var lastUsageDate: Date? {
        guard let context = managedObjectContext else { return nil }
        
        // 确保在正确的上下文中访问数据
        var result: Date?
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                result = nil
                return
            }
            result = records.compactMap { $0.date }.max()
        }
        return result
    }

    // 距离最近一次使用的天数
    var daysSinceLastUsage: Int? {
        guard let lastDate = lastUsageDate else { return nil }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: lastDate, to: now)
        return components.day
    }



    // 有效使用量（根据计算方式返回次数或天数）
    var effectiveUsage: Double {
        if valuationMethod == "daily" {
            guard let purchaseDate = purchaseDate else { return 0 }
            let days = Calendar.current.dateComponents([.day], from: purchaseDate, to: Date()).day ?? 0
            return Double(max(1, days))
        } else {
            return Double(totalUsageCount)
        }
    }

    // 使用趋势（近期使用频率是否上升）
    var usageTrend: Double {
        guard let context = managedObjectContext else { return 0 }

        var result: Double = 0
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord], records.count >= 10 else {
                result = 0
                return
            }

            // 按日期排序
            let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }

            // 将记录分为前半部分和后半部分
            let midIndex = sortedRecords.count / 2
            let firstHalf = sortedRecords[0..<midIndex]
            let secondHalf = sortedRecords[midIndex..<sortedRecords.count]

            // 计算两个时间段的平均间隔
            func averageInterval(_ records: ArraySlice<UsageRecord>) -> TimeInterval {
                guard records.count >= 2 else { return 0 }

                var totalInterval: TimeInterval = 0
                for i in 1..<records.count {
                    let prev = records[records.startIndex + i - 1].date ?? Date()
                    let curr = records[records.startIndex + i].date ?? Date()
                    totalInterval += curr.timeIntervalSince(prev)
                }

                return totalInterval / Double(records.count - 1)
            }

            let firstHalfInterval = averageInterval(firstHalf)
            let secondHalfInterval = averageInterval(secondHalf)

            // 如果第二个时间段的平均间隔更短，则趋势为正（使用频率上升）
            if firstHalfInterval > 0 && secondHalfInterval > 0 {
                result = (firstHalfInterval - secondHalfInterval) / firstHalfInterval
            } else {
                result = 0
            }
        }

        return result
    }

    // 满意度趋势（近期满意度是否上升）
    var satisfactionTrend: Double {
        guard let context = managedObjectContext else { return 0 }

        var result: Double = 0
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord], records.count >= 5 else {
                result = 0
                return
            }

            // 按日期排序
            let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }

            // 将记录分为前半部分和后半部分
            let midIndex = sortedRecords.count / 2
            let firstHalf = sortedRecords[0..<midIndex]
            let secondHalf = sortedRecords[midIndex..<sortedRecords.count]

            // 计算两个时间段的平均满意度
            let firstHalfSatisfaction = firstHalf.reduce(0) { $0 + Double($1.satisfaction) } / Double(firstHalf.count)
            let secondHalfSatisfaction = secondHalf.reduce(0) { $0 + Double($1.satisfaction) } / Double(secondHalf.count)

            // 返回满意度变化比例
            if firstHalfSatisfaction > 0 {
                result = (secondHalfSatisfaction - firstHalfSatisfaction) / firstHalfSatisfaction
            } else {
                result = secondHalfSatisfaction > 0 ? 1 : 0
            }
        }

        return result
    }

    // MARK: - 借阅相关属性

    // 借阅状态
    enum LoanStatus: String, CaseIterable, Identifiable {
        case available = "可借出"
        case loanedOut = "已借出"
        case overdue = "已逾期"

        var id: String { self.rawValue }

        var color: Color {
            switch self {
            case .available: return .green
            case .loanedOut: return .blue
            case .overdue: return .red
            }
        }

        var icon: String {
            switch self {
            case .available: return "checkmark.circle"
            case .loanedOut: return "arrow.up.forward.circle"
            case .overdue: return "exclamationmark.circle"
            }
        }
    }

    // 当前借阅状态
    var loanStatus: LoanStatus {
        guard let context = managedObjectContext else { return .available }

        var result: LoanStatus = .available
        context.performAndWait {
            // 首先检查UsageRecord中的借出记录
            if let records = usageRecords?.allObjects as? [UsageRecord], !records.isEmpty {
                // 筛选出借出类型的记录
                let loanRecords = records.filter { $0.type == .loaned }

                // 检查是否有活跃的借出记录（未归还）
                let activeLoans = loanRecords.filter { $0.returnDate == nil }

                if !activeLoans.isEmpty {
                    // 检查是否有逾期的借出记录
                    let overdueLoans = activeLoans.filter { $0.isOverdue }

                    if !overdueLoans.isEmpty {
                        result = .overdue
                        return
                    }

                    result = .loanedOut
                    return
                }
            }

            // 向后兼容：检查LoanRecord实体
            guard let loanRecords = loanRecords?.allObjects as? [LoanRecord], !loanRecords.isEmpty else {
                result = .available
                return
            }

            // 检查是否有活跃的借阅记录（未归还且未标记为丢失）
            let activeLoans = loanRecords.filter {
                $0.returnDate == nil && $0.status != "lost" && $0.status != "returned"
            }

            if activeLoans.isEmpty {
                result = .available
                return
            }

            // 检查是否有逾期的借阅记录
            let overdueLoans = activeLoans.filter { loan in
                guard let dueDate = loan.dueDate else { return false }
                return dueDate < Date()
            }

            if !overdueLoans.isEmpty {
                result = .overdue
            } else {
                result = .loanedOut
            }
        }

        return result
    }

    // 当前活跃的借出记录 (UsageRecord)
    var activeUsageRecordLoan: UsageRecord? {
        guard let context = managedObjectContext else { return nil }

        var result: UsageRecord?
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord], !records.isEmpty else {
                result = nil
                return
            }

            // 筛选出借出类型的记录
            let loanRecords = records.filter { $0.type == .loaned }

            // 获取最近的活跃借出记录（未归还）
            let activeLoans = loanRecords.filter { $0.returnDate == nil }

            result = activeLoans.sorted {
                ($0.date ?? Date()) > ($1.date ?? Date())
            }.first
        }

        return result
    }

    // 当前活跃的借阅记录 (LoanRecord - 向后兼容)
    var activeLoanRecord: LoanRecord? {
        guard let loanRecords = loanRecords?.allObjects as? [LoanRecord], !loanRecords.isEmpty else {
            return nil
        }

        // 获取最近的活跃借阅记录
        let activeLoans = loanRecords.filter {
            $0.returnDate == nil && $0.status != "lost" && $0.status != "returned"
        }

        return activeLoans.sorted {
            ($0.createdAt ?? Date()) > ($1.createdAt ?? Date())
        }.first
    }

    // 借阅次数（包括UsageRecord和LoanRecord）
    var loanCount: Int {
        guard let context = managedObjectContext else { return 0 }

        var count = 0
        context.performAndWait {
            // 统计UsageRecord中的借出记录
            if let records = usageRecords?.allObjects as? [UsageRecord] {
                count += records.filter { $0.type == .loaned }.count
            }

            // 统计LoanRecord中的记录（向后兼容）
            count += loanRecords?.count ?? 0
        }

        return count
    }

    // 平均借阅时长（天）
    var averageLoanDuration: Double {
        guard let context = managedObjectContext else { return 0 }

        var totalDuration = 0
        var loanCount = 0

        context.performAndWait {
            // 统计UsageRecord中的借出记录
            if let records = usageRecords?.allObjects as? [UsageRecord] {
                let loanRecords = records.filter { $0.type == .loaned }
                let returnedLoans = loanRecords.filter { $0.returnDate != nil }

                for loan in returnedLoans {
                    if let duration = loan.loanDuration {
                        totalDuration += duration
                        loanCount += 1
                    }
                }
            }

            // 统计LoanRecord中的记录（向后兼容）
            if let loanRecords = loanRecords?.allObjects as? [LoanRecord], !loanRecords.isEmpty {
                // 只计算已归还的借阅记录
                let returnedLoans = loanRecords.filter { $0.returnDate != nil || $0.status == "returned" }

                for loan in returnedLoans {
                    if let duration = loan.loanDuration {
                        totalDuration += duration
                        loanCount += 1
                    }
                }
            }
        }

        return loanCount > 0 ? Double(totalDuration) / Double(loanCount) : 0
    }

    // MARK: - 改进算法的辅助方法

    // 标准化使用频率评分
    private func calculateNormalizedUsageScore(benchmark: CategoryBenchmark) -> Double {
        let rawScore = monthlyUsageFrequency
        let normalizedScore = (rawScore - benchmark.averageUsageFrequency) /
                             max(benchmark.usageStandardDeviation, 1.0)

        // 转换为0-100分制，使用sigmoid函数避免极值
        return sigmoid(normalizedScore) * 100
    }

    // 标准化成本效率评分
    private func calculateNormalizedCostScore(benchmark: CategoryBenchmark) -> Double {
        // 计算相对成本（相对于类别中位价格）
        let relativeCost = costPerUse / max(benchmark.medianPrice / 100, 1.0)

        // 成本效率：成本越低效率越高
        let costEfficiency = max(0, 2.0 - relativeCost)

        // 转换为0-100分制
        return min(100, costEfficiency * 50)
    }

    // 标准化满意度评分
    private func calculateNormalizedSatisfactionScore(benchmark: CategoryBenchmark) -> Double {
        let rawScore = averageSatisfaction
        let categoryAverage = benchmark.averageSatisfaction

        // 相对于类别平均满意度的表现
        let relativeScore = rawScore / max(categoryAverage, 1.0)

        // 转换为0-100分制，满意度5分对应100分
        return min(100, relativeScore * 20)
    }

    // 增强的满意度趋势计算
    var enhancedSatisfactionTrend: Double {
        guard let context = managedObjectContext else { return 0 }

        var result: Double = 0
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord],
                  records.count >= 3 else {
                result = 0
                return
            }

            let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }

            // 使用指数加权移动平均
            var weightedSatisfactions: [Double] = []
            let alpha = 0.3 // 平滑因子

            for (index, record) in sortedRecords.enumerated() {
                let satisfaction = Double(record.satisfaction)
                if index == 0 {
                    weightedSatisfactions.append(satisfaction)
                } else {
                    let ewma = alpha * satisfaction + (1 - alpha) * weightedSatisfactions[index - 1]
                    weightedSatisfactions.append(ewma)
                }
            }

            // 计算趋势斜率
            result = calculateTrendSlope(weightedSatisfactions)
        }

        return result
    }

    // 计算趋势斜率的辅助方法
    private func calculateTrendSlope(_ values: [Double]) -> Double {
        guard values.count >= 2 else { return 0 }

        let n = Double(values.count)
        let sumX = (0..<values.count).reduce(0) { $0 + Double($1) }
        let sumY = values.reduce(0, +)
        let sumXY = values.enumerated().reduce(0) { $0 + Double($1.offset) * $1.element }
        let sumXX = (0..<values.count).reduce(0) { $0 + Double($1) * Double($1) }

        let denominator = n * sumXX - sumX * sumX
        guard denominator != 0 else { return 0 }

        let slope = (n * sumXY - sumX * sumY) / denominator
        return slope
    }

    // Sigmoid函数，用于标准化评分
    private func sigmoid(_ x: Double) -> Double {
        return 1.0 / (1.0 + exp(-x))
    }

    // 获取产品的置信度指标
    var confidenceMetrics: ConfidenceMetrics {
        return ConfidenceMetrics.calculate(for: self)
    }

    // 获取产品的生命周期阶段
    var lifecycleStage: ProductLifecycleStage {
        return ProductLifecycleStage(daysSincePurchase: daysSincePurchase)
    }
    
    // MARK: - 消耗品相关功能
    
    // 消耗型物品生命周期状态枚举
    enum LifecycleStatus: String, CaseIterable, Identifiable {
        case active = "活跃"         // 正常使用中
        case lowStock = "库存不足"    // 库存低但仍可使用
        case critical = "即将耗尽"    // 库存极低，需要立即补充
        case depleted = "已耗尽"      // 库存为0，无法使用
        case expired = "已过期"       // 超过保质期或有效期
        case discontinued = "已停用"  // 用户主动停用
        case archived = "已归档"      // 长期未使用，已归档
        
        var id: String { self.rawValue }
        
        var color: Color {
            switch self {
            case .active: return .green
            case .lowStock: return .orange
            case .critical: return .red
            case .depleted: return .gray
            case .expired: return .purple
            case .discontinued: return .secondary
            case .archived: return .secondary.opacity(0.6)
            }
        }
        
        var icon: String {
            switch self {
            case .active: return "checkmark.circle.fill"
            case .lowStock: return "exclamationmark.triangle.fill"
            case .critical: return "exclamationmark.circle.fill"
            case .depleted: return "xmark.circle.fill"
            case .expired: return "clock.badge.exclamationmark.fill"
            case .discontinued: return "stop.circle.fill"
            case .archived: return "archivebox.fill"
            }
        }
        
        var description: String {
            switch self {
            case .active: return "正常使用中，库存充足"
            case .lowStock: return "库存偏低，建议及时补充"
            case .critical: return "库存极低，请立即补充"
            case .depleted: return "库存已耗尽，无法继续使用"
            case .expired: return "已超过有效期，不建议使用"
            case .discontinued: return "已停止使用此物品"
            case .archived: return "长期未使用，已自动归档"
            }
        }
        
        var priority: Int {
            switch self {
            case .critical: return 1
            case .depleted: return 2
            case .expired: return 3
            case .lowStock: return 4
            case .active: return 5
            case .discontinued: return 6
            case .archived: return 7
            }
        }
    }
    
    // 库存状态枚举（保持向后兼容）
    enum StockStatus: String, CaseIterable, Identifiable {
        case sufficient = "充足"     // >50%
        case moderate = "适中"       // 20-50%
        case low = "不足"           // 5-20%
        case critical = "即将用完"   // <5%
        case empty = "已用完"       // =0
        
        var id: String { self.rawValue }
        
        var color: Color {
            switch self {
            case .sufficient: return .green
            case .moderate: return .blue
            case .low: return .orange
            case .critical: return .red
            case .empty: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .sufficient: return "checkmark.circle.fill"
            case .moderate: return "minus.circle.fill"
            case .low: return "exclamationmark.triangle.fill"
            case .critical: return "exclamationmark.circle.fill"
            case .empty: return "xmark.circle.fill"
            }
        }
    }
    
    // 计算当前实际库存量（基于初始量减去消耗量）
    var actualCurrentQuantity: Double {
        guard isConsumable else { return 0.0 }

        // 基于初始库存量减去总消耗量计算（使用购买单位）
        let initialQuantity = Double(quantity)
        let consumed = totalConsumedQuantityInPurchaseUnit

        return max(0.0, initialQuantity - consumed)
    }
    
    // 当前库存状态（保持向后兼容）
    var stockStatus: StockStatus {
        guard isConsumable else { return .sufficient }
        
        let remaining = actualCurrentQuantity
        
        if remaining <= 0 {
            return .empty
        } else if remaining <= minStockAlert {
            return .critical
        } else if remaining <= minStockAlert * 2 {
            return .low
        } else if remaining <= minStockAlert * 4 {
            return .moderate
        } else {
            return .sufficient
        }
    }
    
    // 生命周期状态计算
    var lifecycleStatus: LifecycleStatus {
        guard isConsumable else { return .active }
        
        // 优先检查是否已归档（通过标签设置）
        if isArchived {
            return .archived
        }
        
        // 检查是否被用户主动停用
        if isDiscontinued {
            return .discontinued
        }
        
        // 检查是否过期
        if isExpired {
            return .expired
        }
        
        // 检查是否长期未使用（超过90天无消耗记录）
        if isLongTermUnused {
            return .archived
        }
        
        // 基于库存状态判断
        let remaining = actualCurrentQuantity
        
        if remaining <= 0 {
            return .depleted
        } else if remaining <= minStockAlert {
            return .critical
        } else if remaining <= minStockAlert * 2 {
            return .lowStock
        } else {
            return .active
        }
    }
    
    // 是否被用户主动停用
    private var isDiscontinued: Bool {
        guard let tags = tags?.allObjects as? [Tag] else { return false }
        return tags.contains { $0.type == "lifecycle" && $0.name == "已停用" }
    }
    
    // 是否已归档
    private var isArchived: Bool {
        guard let tags = tags?.allObjects as? [Tag] else { return false }
        return tags.contains { $0.type == "lifecycle" && $0.name == "已归档" }
    }
    
    // 是否过期
    private var isExpired: Bool {
        guard let expiryDate = expiryDate else { return false }
        return Date() > expiryDate
    }
    
    // 是否长期未使用
    private var isLongTermUnused: Bool {
        guard let context = managedObjectContext else { return false }

        var result: Bool = false
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord],
                  !records.isEmpty else {
                result = false
                return
            }

            let sortedRecords = records.sorted { ($0.date ?? Date()) > ($1.date ?? Date()) }
            guard let lastUsageDate = sortedRecords.first?.date else {
                result = false
                return
            }

            let daysSinceLastUsage = Calendar.current.dateComponents([.day], from: lastUsageDate, to: Date()).day ?? 0
            result = daysSinceLastUsage > 90
        }

        return result
    }
    
    // 库存百分比
    var stockPercentage: Double {
        guard isConsumable else { return 1.0 }

        let remaining = actualCurrentQuantity
        let consumed = totalConsumedQuantityInPurchaseUnit

        // 如果没有消耗记录，返回100%
        if consumed == 0.0 {
            return 1.0
        }

        // 计算初始库存量（使用购买单位）
        let initialQuantity = remaining + consumed
        guard initialQuantity > 0 else { return 0.0 }

        return max(0.0, min(1.0, remaining / initialQuantity))
    }
    
    // 总消耗量（保持原始记录，用于显示）
    var totalConsumedQuantity: Double {
        guard let context = managedObjectContext else { return 0.0 }

        var result: Double = 0.0
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord] else {
                result = 0.0
                return
            }

            result = records.reduce(0.0) { total, record in
                total + record.consumedQuantity
            }
        }

        return result
    }

    // 总消耗量（转换为购买单位，用于库存计算）
    var totalConsumedQuantityInPurchaseUnit: Double {
        guard let context = managedObjectContext else { return 0.0 }

        var result: Double = 0.0
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord] else {
                result = 0.0
                return
            }

            result = records.reduce(0.0) { total, record in
                let consumedInPurchaseUnit: Double

                // 检查消耗记录的单位，如果是消耗单位需要转换为购买单位
                if usesDualUnitSystem && record.consumptionUnit == consumptionUnit {
                    // 消耗记录使用消耗单位，需要转换为购买单位
                    // 例如：消耗了2粒，转换比例是25粒/瓶，则消耗了2/25=0.08瓶
                    consumedInPurchaseUnit = record.consumedQuantity / conversionRatio
                } else {
                    // 消耗记录使用购买单位，直接使用
                    consumedInPurchaseUnit = record.consumedQuantity
                }

                return total + consumedInPurchaseUnit
            }
        }

        return result
    }
    
    // 平均每次消耗量
    var averageConsumptionPerUse: Double {
        let totalUsage = totalUsageCount
        guard totalUsage > 0 else { return 0.0 }
        
        return totalConsumedQuantity / Double(totalUsage)
    }
    
    // 日均消耗量
    var dailyConsumptionRate: Double {
        guard daysSincePurchase > 0 else { return 0.0 }
        
        return totalConsumedQuantity / Double(daysSincePurchase)
    }
    
    // 预计用完时间（天）
    var estimatedDaysUntilEmpty: Int? {
        guard isConsumable, actualCurrentQuantity > 0 else { return nil }
        
        let rate = dailyConsumptionRate
        guard rate > 0 else { return nil }
        
        return Int(ceil(actualCurrentQuantity / rate))
    }
    
    // 预计用完日期
    var estimatedEmptyDate: Date? {
        guard let days = estimatedDaysUntilEmpty else { return nil }
        
        return Calendar.current.date(byAdding: .day, value: days, to: Date())
    }
    
    // 是否需要库存预警
    var needsStockAlert: Bool {
        guard isConsumable else { return false }

        return actualCurrentQuantity <= minStockAlert || stockStatus == .critical || stockStatus == .low
    }

    // MARK: - 双单位系统支持

    // 购买单位（存储单位，如：盒、瓶、箱）
    var purchaseUnit: String {
        get { unitType ?? "个" }
        set { unitType = newValue }
    }

    // 消耗单位（使用单位，如：片、毫升、个）
    var consumptionUnit: String {
        get { consumptionUnitType ?? purchaseUnit }
        set { consumptionUnitType = newValue }
    }

    // 单位转换比例（1购买单位 = X消耗单位）
    var conversionRatio: Double {
        get { unitConversionRatio > 0 ? unitConversionRatio : 1.0 }
        set { unitConversionRatio = max(0.1, newValue) }
    }

    // 是否使用双单位系统
    var usesDualUnitSystem: Bool {
        return consumptionUnitType != nil && unitConversionRatio > 0 && consumptionUnit != purchaseUnit
    }

    // 当前库存（消耗单位）
    var currentQuantityInConsumptionUnit: Double {
        return actualCurrentQuantity * conversionRatio
    }

    // 格式化库存显示（双单位）
    var formattedStockDisplay: String {
        if usesDualUnitSystem {
            let purchaseQty = actualCurrentQuantity
            let consumptionQty = currentQuantityInConsumptionUnit
            return String(format: "%.1f%@ (%.0f%@)", purchaseQty, purchaseUnit, consumptionQty, consumptionUnit)
        } else {
            return String(format: "%.1f%@", actualCurrentQuantity, purchaseUnit)
        }
    }
    
    // 注意：updateCurrentQuantity方法已移除
    // 现在统一使用actualCurrentQuantity计算属性
    // 不再需要手动更新currentQuantity字段
    
    // 消耗趋势分析
    var consumptionTrend: ConsumptionTrend {
        guard let context = managedObjectContext else {
            return ConsumptionTrend(trend: .stable, rate: 0.0, description: "暂无数据")
        }

        var result: ConsumptionTrend = ConsumptionTrend(trend: .stable, rate: 0.0, description: "暂无数据")
        context.performAndWait {
            guard let records = usageRecords?.allObjects as? [UsageRecord],
                  !records.isEmpty else {
                result = ConsumptionTrend(trend: .stable, rate: 0.0, description: "暂无数据")
                return
            }

            let sortedRecords = records.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }

            // 计算最近30天和之前30天的消耗量
            let now = Date()
            let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: now) ?? now
            let sixtyDaysAgo = Calendar.current.date(byAdding: .day, value: -60, to: now) ?? now

            let recentRecords = sortedRecords.filter { ($0.date ?? Date()) >= thirtyDaysAgo }
            let previousRecords = sortedRecords.filter {
                let date = $0.date ?? Date()
                return date >= sixtyDaysAgo && date < thirtyDaysAgo
            }

            let recentConsumption = recentRecords.reduce(0.0) { $0 + $1.consumedQuantity }
            let previousConsumption = previousRecords.reduce(0.0) { $0 + $1.consumedQuantity }

            let trend: ConsumptionTrend.TrendType
            let rate: Double

            if previousConsumption == 0 {
                trend = recentConsumption > 0 ? .increasing : .stable
                rate = recentConsumption
            } else {
                let changeRate = (recentConsumption - previousConsumption) / previousConsumption
                rate = changeRate

                if changeRate > 0.2 {
                    trend = .increasing
                } else if changeRate < -0.2 {
                    trend = .decreasing
                } else {
                    trend = .stable
                }
            }

            result = ConsumptionTrend(trend: trend, rate: rate, description: trend.description)
        }

        return result
    }
    
    // MARK: - 生命周期状态管理
    
    /// 手动设置为停用状态
    func discontinue() {
        guard isConsumable else { return }
        
        // 添加停用标签
        let context = managedObjectContext
        if let discontinuedTag = findOrCreateTag(name: "已停用", type: "lifecycle", color: "gray", in: context) {
            addToTags(discontinuedTag)
        }
        
        // 移除活跃标签
        removeLifecycleTag("活跃")
    }
    
    /// 重新激活消耗品
    func reactivate() {
        guard isConsumable else { return }
        
        // 移除停用和归档标签
        removeLifecycleTag("已停用")
        removeLifecycleTag("已归档")
        
        // 添加活跃标签
        let context = managedObjectContext
        if let activeTag = findOrCreateTag(name: "活跃", type: "lifecycle", color: "green", in: context) {
            addToTags(activeTag)
        }
    }
    
    /// 归档消耗品
    func archive() {
        guard isConsumable else { return }
        
        // 添加归档标签
        let context = managedObjectContext
        if let archivedTag = findOrCreateTag(name: "已归档", type: "lifecycle", color: "secondary", in: context) {
            addToTags(archivedTag)
        }
        
        // 移除其他生命周期标签
        removeLifecycleTag("活跃")
        removeLifecycleTag("已停用")
        
        // 记录归档时间
        if let context = managedObjectContext {
            let archivedAtTag = findOrCreateTag(
                name: "归档于\(DateFormatter.shortDate.string(from: Date()))",
                type: "archive_info",
                color: "gray",
                in: context
            )
            if let tag = archivedAtTag {
                addToTags(tag)
            }
        }
    }
    
    /// 从归档中恢复
    func unarchive() {
        guard isConsumable else { return }
        
        // 移除归档相关标签
        removeLifecycleTag("已归档")
        removeArchiveInfoTags()
        
        // 根据当前状态添加适当的标签
        let context = managedObjectContext
        let currentStatus = lifecycleStatus
        
        let statusTag = findOrCreateTag(
            name: currentStatus.rawValue,
            type: "lifecycle",
            color: currentStatus.color.description,
            in: context
        )
        
        if let tag = statusTag {
            addToTags(tag)
        }
    }
    
    /// 检查是否需要状态更新
    func checkLifecycleStatusUpdate() -> Bool {
        guard isConsumable else { return false }
        
        let currentStatus = lifecycleStatus
        
        // 检查是否需要自动归档
        if currentStatus == .active && isLongTermUnused {
            return true
        }
        
        // 检查是否需要过期标记
        if currentStatus != .expired && isExpired {
            return true
        }
        
        return false
    }
    
    /// 获取状态变更建议
    func getLifecycleStatusSuggestions() -> [LifecycleStatusSuggestion] {
        guard isConsumable else { return [] }
        
        var suggestions: [LifecycleStatusSuggestion] = []
        let currentStatus = lifecycleStatus
        
        switch currentStatus {
        case .depleted:
            suggestions.append(LifecycleStatusSuggestion(
                type: .replenish,
                title: "补充库存",
                description: "库存已耗尽，建议立即补充",
                priority: .high
            ))
            
        case .critical:
            suggestions.append(LifecycleStatusSuggestion(
                type: .replenish,
                title: "紧急补货",
                description: "库存极低，建议尽快补充",
                priority: .high
            ))
            
        case .lowStock:
            suggestions.append(LifecycleStatusSuggestion(
                type: .replenish,
                title: "计划补货",
                description: "库存偏低，建议安排补充",
                priority: .medium
            ))
            
        case .expired:
            suggestions.append(LifecycleStatusSuggestion(
                type: .dispose,
                title: "处理过期物品",
                description: "物品已过期，建议及时处理",
                priority: .high
            ))
            
        case .archived:
            suggestions.append(LifecycleStatusSuggestion(
                type: .reactivate,
                title: "重新激活",
                description: "长期未使用，是否重新激活？",
                priority: .low
            ))
            
        case .discontinued:
            suggestions.append(LifecycleStatusSuggestion(
                type: .reactivate,
                title: "重新启用",
                description: "是否重新启用此物品？",
                priority: .low
            ))
            
        case .active:
            // 活跃状态下检查是否有优化建议
            if let days = estimatedDaysUntilEmpty, days <= 7 {
                suggestions.append(LifecycleStatusSuggestion(
                    type: .replenish,
                    title: "提前备货",
                    description: "预计\(days)天后用完，建议提前准备",
                    priority: .medium
                ))
            }
        }
        
        return suggestions
    }
}

// MARK: - 生命周期状态建议
struct LifecycleStatusSuggestion {
    enum SuggestionType {
        case replenish      // 补充库存
        case dispose        // 处理/丢弃
        case reactivate     // 重新激活
        case archive        // 归档
        case discontinue    // 停用
    }
    
    enum Priority {
        case high
        case medium
        case low
        
        var color: Color {
            switch self {
            case .high: return .red
            case .medium: return .orange
            case .low: return .blue
            }
        }
    }
    
    let type: SuggestionType
    let title: String
    let description: String
    let priority: Priority
}

// MARK: - 消耗趋势数据模型
struct ConsumptionTrend {
    enum TrendType {
        case increasing
        case decreasing
        case stable
        
        var description: String {
            switch self {
            case .increasing: return "消耗量增加"
            case .decreasing: return "消耗量减少"
            case .stable: return "消耗量稳定"
            }
        }
        
        var color: Color {
            switch self {
            case .increasing: return .red
            case .decreasing: return .green
            case .stable: return .blue
            }
        }
        
        var icon: String {
            switch self {
            case .increasing: return "arrow.up.circle"
            case .decreasing: return "arrow.down.circle"
            case .stable: return "minus.circle"
            }
        }
    }
    
    let trend: TrendType
    let rate: Double
    let description: String
}

// MARK: - 标签管理辅助方法

private extension Product {
    
    /// 查找或创建标签
    func findOrCreateTag(name: String, type: String, color: String, in context: NSManagedObjectContext?) -> Tag? {
        guard let context = context else { return nil }
        
        // 首先尝试查找现有标签
        let request: NSFetchRequest<Tag> = Tag.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@ AND type == %@", name, type)
        
        do {
            let existingTags = try context.fetch(request)
            if let existingTag = existingTags.first {
                return existingTag
            }
        } catch {
            print("查找标签失败: \(error)")
        }
        
        // 创建新标签
        let newTag = Tag(context: context)
        newTag.id = UUID()
        newTag.name = name
        newTag.type = type
        newTag.color = color
        
        return newTag
    }
    
    /// 移除生命周期标签
    func removeLifecycleTag(_ tagName: String) {
        guard let tags = tags?.allObjects as? [Tag] else { return }
        
        let lifecycleTags = tags.filter { $0.type == "lifecycle" && $0.name == tagName }
        for tag in lifecycleTags {
            removeFromTags(tag)
        }
    }
    
    /// 移除归档信息标签
    func removeArchiveInfoTags() {
        guard let tags = tags?.allObjects as? [Tag] else { return }
        
        let archiveInfoTags = tags.filter { $0.type == "archive_info" }
        for tag in archiveInfoTags {
            removeFromTags(tag)
        }
    }
}

// MARK: - 辅助结构体和类

// 类别基准数据
struct CategoryBenchmark {
    let averageUsageFrequency: Double
    let usageStandardDeviation: Double
    let medianPrice: Double
    let averageSatisfaction: Double

    static func calculate(for category: Category?, products: [Product]) -> CategoryBenchmark {
        // 如果没有类别或产品数据，返回默认值
        guard let category = category, !products.isEmpty else {
            return CategoryBenchmark(
                averageUsageFrequency: 10.0,
                usageStandardDeviation: 5.0,
                medianPrice: 100.0,
                averageSatisfaction: 3.0
            )
        }

        // 筛选同类别产品
        let categoryProducts = products.filter { $0.category == category }
        guard !categoryProducts.isEmpty else {
            return CategoryBenchmark(
                averageUsageFrequency: 10.0,
                usageStandardDeviation: 5.0,
                medianPrice: 100.0,
                averageSatisfaction: 3.0
            )
        }

        // 计算平均使用频率
        let usageFrequencies = categoryProducts.map { $0.monthlyUsageFrequency }
        let avgUsage = usageFrequencies.reduce(0, +) / Double(usageFrequencies.count)

        // 计算标准差
        let variance = usageFrequencies.map { pow($0 - avgUsage, 2) }.reduce(0, +) / Double(usageFrequencies.count)
        let stdDev = sqrt(variance)

        // 计算中位价格
        let prices = categoryProducts.map { $0.price }.sorted()
        let medianPrice = prices.count % 2 == 0 ?
            (prices[prices.count/2 - 1] + prices[prices.count/2]) / 2 :
            prices[prices.count/2]

        // 计算平均满意度
        let satisfactions = categoryProducts.map { $0.averageSatisfaction }
        let avgSatisfaction = satisfactions.reduce(0, +) / Double(satisfactions.count)

        return CategoryBenchmark(
            averageUsageFrequency: avgUsage,
            usageStandardDeviation: max(stdDev, 1.0),
            medianPrice: max(medianPrice, 1.0),
            averageSatisfaction: avgSatisfaction
        )
    }
}

// 产品生命周期阶段
struct ProductLifecycleStage {
    let stage: Stage

    enum Stage {
        case newProduct     // 0-30天
        case earlyAdoption  // 31-90天
        case mature         // 91-365天
        case longTerm       // 365天以上
    }

    init(daysSincePurchase: Int) {
        switch daysSincePurchase {
        case 0...30:
            self.stage = .newProduct
        case 31...90:
            self.stage = .earlyAdoption
        case 91...365:
            self.stage = .mature
        default:
            self.stage = .longTerm
        }
    }

    func getWeights() -> (usage: Double, cost: Double, satisfaction: Double) {
        switch stage {
        case .newProduct:
            return (0.2, 0.3, 0.5)  // 新产品重视满意度
        case .earlyAdoption:
            return (0.3, 0.3, 0.4)  // 平衡期
        case .mature:
            return (0.4, 0.4, 0.2)  // 成熟期重视使用和成本
        case .longTerm:
            return (0.5, 0.4, 0.1)  // 长期持有重视使用效率
        }
    }
}

// 置信度指标
struct ConfidenceMetrics {
    let factor: Double
    let dataPoints: Int
    let timeSpan: Int

    static func calculate(for product: Product) -> ConfidenceMetrics {
        let usageCount = product.totalUsageCount
        let daysSincePurchase = product.daysSincePurchase

        // 基于数据点数量和时间跨度计算置信度
        let dataPointsFactor = min(1.0, Double(usageCount) / 20.0)  // 20个数据点为满分
        let timeSpanFactor = min(1.0, Double(daysSincePurchase) / 90.0)  // 90天为满分

        let overallFactor = (dataPointsFactor + timeSpanFactor) / 2.0

        return ConfidenceMetrics(
            factor: overallFactor,
            dataPoints: usageCount,
            timeSpan: daysSincePurchase
        )
    }
}

// MARK: - DateFormatter 扩展

private extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        return formatter
    }()
}
