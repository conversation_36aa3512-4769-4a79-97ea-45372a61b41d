import Foundation
import CoreData

// MARK: - Product Extensions
extension Product {
    // 获取提醒列表
    public var reminderArray: [Reminder] {
        let set = reminders as? Set<Reminder> ?? []
        return set.sorted { ($0.date ?? Date()) < ($1.date ?? Date()) }
    }
}

// MARK: - Reminder Extensions
extension Reminder {
    // 获取关联的产品（假设一个提醒只关联一个产品）
    public var productObject: Product? {
        let products = product as? Set<Product> ?? []
        return products.first
    }

    // 获取所有关联的产品
    public var productArray: [Product] {
        let products = product as? Set<Product> ?? []
        return Array(products)
    }
}
