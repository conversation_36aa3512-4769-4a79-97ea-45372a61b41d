<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "180625BC-316F-4B8C-B8F5-C006E2EA60BE"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C0DDE00E-A896-4A5E-98C1-FFE53B1EEEDB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "pinklog/Models/ProductRelationshipType.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "85"
            endingLineNumber = "85"
            landmarkName = "description"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "91748887-DD22-413E-9142-28C16E47BA1B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "pinklog/Models/ProductRelationshipType.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "83"
            endingLineNumber = "83"
            landmarkName = "description"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
