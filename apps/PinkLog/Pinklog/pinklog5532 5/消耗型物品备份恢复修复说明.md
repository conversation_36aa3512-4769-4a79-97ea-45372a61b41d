# 消耗型物品备份恢复修复说明

## 问题描述

用户反馈消耗型物品的功能实现及数据没有持久化，执行备份后再恢复后没有消耗相关的数据了。

## 问题分析

通过代码分析发现以下问题：

### 1. BackupProduct结构体缺失消耗型物品字段
- 缺少 `isConsumable`：标识是否为消耗品
- 缺少 `currentQuantity`：当前库存量
- 缺少 `unitType`：单位类型
- 缺少 `minStockAlert`：最低库存预警值
- 缺少 `consumptionRate`：消耗率
- 缺少 `consumptionUnitType`：消耗单位类型（双单位系统）
- 缺少 `unitConversionRatio`：单位转换比例（双单位系统）

### 2. BackupUsageRecord结构体缺失消耗相关字段
- 缺少 `consumedQuantity`：消耗量
- 缺少 `remainingQuantity`：剩余量
- 缺少 `consumptionUnit`：消耗单位

### 3. 备份和恢复逻辑不完整
- `fromCoreData` 方法没有处理消耗型物品字段
- 恢复逻辑中没有设置消耗相关数据

## 修复方案

### 第一步：修改BackupProduct结构体

在 `BackupDataModels.swift` 中的 `BackupProduct` 结构体添加消耗型物品相关字段：

```swift
// 消耗型物品相关字段
let isConsumable: Bool
let currentQuantity: Double?
let unitType: String?
let minStockAlert: Double?
let consumptionRate: Double?
let consumptionUnitType: String?
let unitConversionRatio: Double?
```

### 第二步：修改BackupUsageRecord结构体

在 `BackupUsageRecord` 结构体添加消耗量相关字段：

```swift
// 消耗量相关字段
let consumedQuantity: Double?
let remainingQuantity: Double?
let consumptionUnit: String?
```

### 第三步：更新数据验证逻辑

在 `validateData` 方法中添加对新字段的验证：

```swift
// 消耗型物品字段验证
if isConsumable {
    let consumableValidation = (currentQuantity ?? 0) >= 0 && 
                             (minStockAlert ?? 0) >= 0 && 
                             (consumptionRate ?? 0) >= 0 &&
                             (unitConversionRatio ?? 1.0) > 0
    return basicValidation && consumableValidation
}
```

### 第四步：更新备份转换逻辑

在 `BackupProduct.fromCoreData` 方法中添加消耗型物品字段的转换：

```swift
isConsumable: product.isConsumable,
currentQuantity: product.isConsumable ? product.currentQuantity : nil,
unitType: product.isConsumable ? product.unitType : nil,
minStockAlert: product.isConsumable ? product.minStockAlert : nil,
consumptionRate: product.isConsumable ? product.consumptionRate : nil,
consumptionUnitType: product.isConsumable ? product.consumptionUnitType : nil,
unitConversionRatio: product.isConsumable ? product.unitConversionRatio : nil,
```

在 `BackupUsageRecord.fromCoreData` 方法中添加消耗量字段的转换：

```swift
consumedQuantity: record.consumedQuantity > 0 ? record.consumedQuantity : nil,
remainingQuantity: record.remainingQuantity > 0 ? record.remainingQuantity : nil,
consumptionUnit: record.consumptionUnit,
```

### 第五步：更新恢复转换逻辑

在 `BackupDataImporter.swift` 的产品导入逻辑中添加消耗型物品字段的恢复：

```swift
// 恢复消耗型物品相关字段
product.isConsumable = backupProduct.isConsumable
if backupProduct.isConsumable {
    product.currentQuantity = backupProduct.currentQuantity ?? 0.0
    product.unitType = backupProduct.unitType
    product.minStockAlert = backupProduct.minStockAlert ?? 0.0
    product.consumptionRate = backupProduct.consumptionRate ?? 0.0
    product.consumptionUnitType = backupProduct.consumptionUnitType
    product.unitConversionRatio = backupProduct.unitConversionRatio ?? 1.0
}
```

在 `updateUsageRecord` 方法中添加消耗量字段的恢复：

```swift
// 恢复消耗量相关字段
record.consumedQuantity = backup.consumedQuantity ?? 0.0
record.remainingQuantity = backup.remainingQuantity ?? 0.0
record.consumptionUnit = backup.consumptionUnit
```

## 修复效果

修复后，消耗型物品的以下数据将能正确备份和恢复：

1. **产品层面**：
   - 消耗品标识
   - 当前库存量
   - 单位信息
   - 库存预警设置
   - 双单位系统配置

2. **使用记录层面**：
   - 每次消耗的数量
   - 消耗后的剩余量
   - 消耗使用的单位

3. **向后兼容性**：
   - 所有新增字段都是可选的
   - 提供合理的默认值
   - 不影响现有非消耗型物品的备份恢复

## 测试建议

1. 创建消耗型物品并设置库存
2. 记录多次消耗
3. 执行备份
4. 清空数据或在新设备上恢复
5. 验证消耗型物品的所有数据是否完整恢复

## 注意事项

- 修复保持了向后兼容性，旧的备份文件仍可正常恢复
- 新字段在恢复时会使用合理的默认值
- CloudKit备份服务应该能自动处理新字段（使用反射机制）
