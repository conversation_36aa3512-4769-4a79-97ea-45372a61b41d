//
//  OptimizedLayoutPreview.swift
//  产品卡片布局优化预览
//
//  展示价格与值度合并到同一行的优化设计
//

import SwiftUI

struct OptimizedLayoutPreview: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 设计理念说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🎨 产品卡片布局优化")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("将价格移至值度左边，形成更紧凑的信息布局，节省空间并提升视觉层次")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 新旧布局对比
                    VStack(spacing: 16) {
                        Text("布局对比")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 旧布局示例
                        VStack(alignment: .leading, spacing: 8) {
                            Text("❌ 旧布局 - 价格单独占行")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                            
                            OldLayoutDemo()
                        }
                        
                        Divider()
                        
                        // 新布局示例
                        VStack(alignment: .leading, spacing: 8) {
                            Text("✅ 新布局 - 价格与值度同行")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                            
                            NewLayoutDemo()
                        }
                    }
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 设计优势说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("✨ 设计优势")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            AdvantageRow(icon: "rectangle.compress.vertical", title: "空间节省", description: "减少垂直空间占用，卡片更紧凑")
                            AdvantageRow(icon: "eye.fill", title: "视觉平衡", description: "信息分布更均匀，视觉层次更清晰")
                            AdvantageRow(icon: "speedometer", title: "阅读效率", description: "相关信息聚合，提升信息获取效率")
                            AdvantageRow(icon: "iphone", title: "移动优化", description: "更适合小屏幕设备的信息密度")
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("布局优化")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// 旧布局演示
struct OldLayoutDemo: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("MacBook Pro")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Apple · M1 Pro")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 价格单独一行
            HStack {
                Text("¥14999")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            // 使用次数和值度分开
            HStack {
                Text("使用25次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Text("值度")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("85")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    Circle()
                        .fill(.green)
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

// 新布局演示
struct NewLayoutDemo: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("MacBook Pro")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Apple · M1 Pro")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 优化后的信息行 - 价格与值度在同一行
            HStack {
                Text("使用25次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 价格、值度和置信度的紧凑布局
                HStack(spacing: 8) {
                    // 价格
                    Text("¥14999")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    // 分隔符
                    Text("·")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // 值度指数和置信度
                    HStack(spacing: 4) {
                        HStack(spacing: 2) {
                            Text("值度")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("85")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                        Circle()
                            .fill(.green)
                            .frame(width: 6, height: 6)
                    }
                }
            }
        }
        .padding()
        .background(Color.green.opacity(0.1))
        .cornerRadius(8)
    }
}

// 优势说明行
struct AdvantageRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    OptimizedLayoutPreview()
}