//
//  ElegantStockColorPreview.swift
//  优雅库存条颜色方案预览
//
//  展示重新设计的消耗型物品库存条颜色效果
//

import SwiftUI

struct ElegantStockColorPreview: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 设计理念说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🎨 优雅库存条颜色方案")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("解决了原有荧光色配白色文字可读性差的问题，采用柔和且专业的颜色搭配")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 颜色方案展示
                    VStack(spacing: 16) {
                        Text("新颜色方案展示")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 不同库存状态的颜色展示
                        ForEach(stockLevels, id: \.percentage) { level in
                            StockColorDemoRow(
                                title: level.title,
                                percentage: level.percentage,
                                description: level.description
                            )
                        }
                    }
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 设计优势说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("✨ 设计优势")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            AdvantageRow(icon: "eye.fill", title: "优秀可读性", description: "所有颜色都经过优化，确保与白色文字有良好对比度")
                            AdvantageRow(icon: "paintbrush.fill", title: "优雅配色", description: "摒弃刺眼荧光色，采用柔和专业的色彩搭配")
                            AdvantageRow(icon: "slider.horizontal.3", title: "渐进过渡", description: "6个库存等级，颜色变化自然流畅")
                            AdvantageRow(icon: "sparkles", title: "现代设计", description: "符合iOS设计规范，提升整体视觉体验")
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("库存条颜色优化")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // 库存等级数据
    private let stockLevels: [(title: String, percentage: Double, description: String)] = [
        ("充足库存", 0.9, "深海绿 - 优雅且专业"),
        ("良好库存", 0.7, "森林绿 - 自然且舒适"),
        ("中等库存", 0.5, "琥珀色 - 温暖且平衡"),
        ("偏低库存", 0.3, "暖橙色 - 提醒但不刺眼"),
        ("低库存", 0.15, "珊瑚红 - 警示但优雅"),
        ("极低库存", 0.02, "深红色 - 强烈但不刺眼")
    ]
}

// 库存颜色演示行
struct StockColorDemoRow: View {
    let title: String
    let percentage: Double
    let description: String
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(Int(percentage * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(stockColor(for: percentage))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(stockColor(for: percentage).opacity(0.12))
                    )
            }
            
            // 库存进度条
            ZStack(alignment: .leading) {
                Capsule()
                    .fill(Color.gray.opacity(0.15))
                    .frame(height: 6)
                
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: stockColor(for: percentage).opacity(0.9), location: 0),
                                .init(color: stockColor(for: percentage), location: 0.5),
                                .init(color: stockColor(for: percentage).opacity(0.8), location: 1)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: UIScreen.main.bounds.width * 0.7 * percentage, height: 6)
                    .shadow(color: stockColor(for: percentage).opacity(0.2), radius: 1, x: 0, y: 0.5)
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.vertical, 4)
    }
    
    // 优雅的库存颜色映射
    private func stockColor(for percentage: Double) -> Color {
        let clampedPercentage = max(0, min(1, percentage))
        
        switch clampedPercentage {
        case 0.8...1.0:
            return Color(red: 0.2, green: 0.7, blue: 0.5)  // 深海绿
        case 0.6..<0.8:
            return Color(red: 0.3, green: 0.75, blue: 0.4) // 森林绿
        case 0.4..<0.6:
            return Color(red: 0.9, green: 0.7, blue: 0.2)  // 琥珀色
        case 0.2..<0.4:
            return Color(red: 0.95, green: 0.6, blue: 0.3) // 暖橙色
        case 0.05..<0.2:
            return Color(red: 0.9, green: 0.45, blue: 0.4) // 珊瑚红
        default:
            return Color(red: 0.8, green: 0.3, blue: 0.3)  // 深红色
        }
    }
}

// 优势说明行
struct AdvantageRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    ElegantStockColorPreview()
}