# 消耗型记录时间线UI设计

## 概述

为了更好地区分和展示消耗品的使用记录，我们在物品详情页的时间线中为消耗型记录设计了特殊的UI样式，让用户能够一眼识别消耗品的使用情况。

## 设计特点

### 1. 新增消耗事件类型

在 `TimelineEventType` 枚举中新增了 `consumption` 类型：
- **图标**: `minus.circle.fill` - 表示消耗减少
- **颜色**: 青色 (Cyan) - 区别于普通使用记录的绿色
- **名称**: "消耗" - 明确标识事件类型

### 2. 特殊的视觉标识

#### 时间线图标设计
- **主图标**: 青色圆形背景 + 白色减号圆形图标
- **消耗标记**: 右下角红色小圆点 + 白色水滴图标
- **双重标识**: 既有主要的消耗图标，又有小型的消耗提示标记

#### 标题区域设计
- **消耗标签**: 青色胶囊形状的"消耗记录"标签
  - 青色背景 + 青色边框
  - 减号图标 + "消耗记录"文字
  - 半透明背景效果
- **标题文字**: "消耗了 [产品名称]"

### 3. 丰富的信息展示

#### 描述信息结构化展示
消耗记录的描述信息采用结构化布局，每个信息项都有对应的图标：

- **消耗量**: 🔴 红色减号圆圈 + "消耗: X单位"
- **剩余量**: 🟠 橙色饼图 + "剩余 X单位" 或 "已用完"
- **消耗效率**: 🔵 蓝色速度计 + "效率: X单位/分钟"
- **满意度**: 🟡 黄色星星 + "满意度: X/5"
- **使用场景**: 🟢 绿色位置 + "场景: XXX"

#### 特殊背景样式
- **背景色**: 青色半透明背景 (opacity: 0.05)
- **边框**: 青色半透明边框 (opacity: 0.2)
- **圆角**: 8px 圆角矩形
- **内边距**: 垂直4px，水平8px

### 4. 智能数据处理

#### TimelineViewModel 增强
- **自动识别**: 通过 `record.isConsumptionRecord` 属性自动识别消耗记录
- **信息聚合**: 自动收集消耗量、剩余量、效率、满意度、场景等信息
- **格式化显示**: 将多个信息项用 " · " 分隔，便于解析和展示

#### 数据验证
- **消耗量验证**: 确保消耗量为正数
- **剩余量验证**: 确保剩余量为非负数
- **一致性检查**: 验证消耗量 + 剩余量的逻辑一致性

## 用户体验优势

### 1. 视觉区分度高
- 独特的青色配色方案
- 双重图标标识系统
- 结构化信息布局

### 2. 信息密度合理
- 关键信息一目了然
- 详细数据有序排列
- 图标辅助理解

### 3. 交互体验一致
- 保持时间线整体风格
- 点击查看详情功能完整
- 过滤器支持消耗类型

## 技术实现

### 文件修改清单

1. **Models/TimelineEvent.swift**
   - 新增 `consumption` 事件类型
   - 配置专属图标和颜色

2. **ViewModels/TimelineViewModel.swift**
   - 增加消耗记录识别逻辑
   - 构建结构化描述信息
   - 创建消耗类型时间线事件

3. **Views/Timeline/TimelineEventView.swift**
   - 新增消耗事件判断逻辑
   - 设计特殊的UI布局
   - 实现信息分类展示

### 核心代码逻辑

```swift
// 消耗记录识别
if record.isConsumptionRecord {
    // 创建消耗类型事件
    allEvents.append(TimelineEvent(
        date: date,
        type: .consumption,
        title: "消耗了 \(product.name ?? "产品")",
        description: structuredDescription,
        sourceId: record.id,
        sourceType: "UsageRecord"
    ))
}
```

## 未来扩展

### 可能的增强功能
1. **消耗趋势图表**: 在时间线中嵌入小型图表
2. **库存预警**: 当剩余量低时显示警告标识
3. **效率对比**: 显示相对于平均效率的对比
4. **场景统计**: 按使用场景分类展示

### 性能优化
1. **懒加载**: 大量消耗记录的分页加载
2. **缓存机制**: 计算结果缓存
3. **动画优化**: 平滑的展开/收起动画

## 总结

通过这套专门为消耗型记录设计的时间线UI，用户可以：
- 快速识别消耗品的使用记录
- 清晰了解每次消耗的详细信息
- 直观感受消耗品的使用模式
- 更好地管理消耗品库存

这种设计既保持了时间线的整体一致性，又突出了消耗记录的特殊性，为用户提供了更好的消耗品管理体验。