---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

Thank you for contributing to MarkdownUI!

Before you submit an issue, please complete the report with all the relevant details for your bug and go through every item in the checklist.

**Describe the bug**
A clear and concise description of what the bug is.

**Checklist**
- [ ] I can reproduce this issue with a vanilla SwiftUI project.
- [ ] I can reproduce this issue using the `main` branch of this package.
- [ ] This bug hasn't been addressed in an [existing GitHub issue](https://github.com/gonzalezreal/swift-markdown-ui/issues).

**Steps to reproduce**
Explanation of how to reproduce the incorrect behavior. This could include an attached project, a link to code, or a Markdown-formatted text exhibiting the issue.
1. ...

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Version information**
- MarkdownUI: [e.g. 2.0.0, or a commit hash]
- OS: [e.g. iOS 15, macOS 13]
- Xcode: [e.g. 14.2.0]

**Additional context**
Add any other context about the problem here.
