# 库存显示修复说明

## 问题描述
用户反映现有库存条与实际库存不符，库存显示存在不一致的问题。

## 问题分析
经过代码分析，发现库存计算存在两套逻辑：
1. **Core Data存储的currentQuantity属性** - 直接存储在数据库中
2. **基于消耗记录计算的库存** - 通过初始数量减去总消耗量计算

这两套逻辑可能导致数据不一致，特别是当消耗记录更新后，存储的currentQuantity没有同步更新。

## 修复方案

### 1. 新增统一的库存计算属性
在 `Product+Extension.swift` 中新增 `actualCurrentQuantity` 计算属性：
```swift
var actualCurrentQuantity: Double {
    guard isConsumable else { return 0.0 }
    
    let initialQuantity = Double(quantity)
    let consumed = totalConsumedQuantity
    return max(0.0, initialQuantity - consumed)
}
```

### 2. 更新库存状态计算
修改以下计算属性使用 `actualCurrentQuantity`：
- `stockStatus` - 库存状态（充足/适中/不足/即将用完/已用完）
- `stockPercentage` - 库存百分比
- `estimatedDaysUntilEmpty` - 预计用完时间
- `needsStockAlert` - 是否需要库存预警

### 3. 更新UI显示
修改以下视图文件中的库存显示：
- **ProductCard.swift** - 产品卡片中的库存信息
- **ConsumableManagementView.swift** - 消耗品管理页面
- **ReplenishStockSheet.swift** - 补充库存页面
- **ConsumptionRecordSheet.swift** - 消耗记录页面

## 修复效果

### 库存计算一致性
- ✅ 所有库存相关计算都基于同一套逻辑
- ✅ 库存显示与实际消耗记录保持同步
- ✅ 消除了数据不一致的问题

### 用户体验改善
- ✅ 库存百分比准确反映实际剩余量
- ✅ 库存状态（颜色和图标）正确显示
- ✅ 预计用完时间基于真实数据计算
- ✅ 库存预警功能更加可靠

### 数据完整性
- ✅ 基于消耗记录的实时计算
- ✅ 避免了手动更新currentQuantity的遗漏
- ✅ 确保库存数据的准确性和可靠性

## 技术细节

### 计算逻辑
```
实际库存 = 初始数量 - 总消耗量
库存百分比 = 实际库存 / 初始数量
```

### 库存状态分级
- **充足** (>50%): 绿色 + 对勾图标
- **适中** (20-50%): 蓝色 + 减号图标
- **不足** (5-20%): 橙色 + 警告三角形
- **即将用完** (<5%): 红色 + 感叹号圆圈
- **已用完** (=0): 灰色 + 叉号圆圈

### 安全保障
- 使用 `max(0.0, ...)` 确保库存不会显示负数
- 使用 `guard` 语句确保只对消耗品进行库存计算
- 空值安全处理，避免崩溃

## 测试验证

1. ✅ 项目构建成功
2. ✅ 应用安装到模拟器
3. ✅ 应用启动正常
4. ✅ 库存显示逻辑统一

## 后续建议

1. **数据迁移**: 考虑清理旧的currentQuantity数据，完全依赖计算属性
2. **性能优化**: 如果消耗记录很多，可以考虑缓存计算结果
3. **单元测试**: 为库存计算逻辑添加单元测试
4. **用户反馈**: 收集用户对修复后库存显示的反馈

## 总结

通过统一库存计算逻辑，解决了库存显示不一致的问题。现在所有库存相关的显示都基于实际的消耗记录计算，确保了数据的准确性和一致性。用户可以更可靠地依赖库存信息进行决策。