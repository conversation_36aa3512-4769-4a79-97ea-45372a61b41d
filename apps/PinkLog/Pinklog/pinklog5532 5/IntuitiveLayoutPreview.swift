//
//  IntuitiveLayoutPreview.swift
//  产品卡片直觉化布局预览
//
//  展示价格紧跟物品名的直觉化设计
//

import SwiftUI

struct IntuitiveLayoutPreview: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 设计理念说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🧠 直觉化布局设计")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("将价格紧跟在物品名下方，符合用户的自然阅读习惯和心理预期")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 布局演进对比
                    VStack(spacing: 16) {
                        Text("布局演进历程")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 第一版布局
                        VStack(alignment: .leading, spacing: 8) {
                            Text("❌ 第一版 - 价格单独占行")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                            
                            FirstVersionDemo()
                        }
                        
                        Divider()
                        
                        // 第二版布局
                        VStack(alignment: .leading, spacing: 8) {
                            Text("⚠️ 第二版 - 价格与值度同行")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.orange)
                            
                            SecondVersionDemo()
                        }
                        
                        Divider()
                        
                        // 第三版布局（当前）
                        VStack(alignment: .leading, spacing: 8) {
                            Text("✅ 第三版 - 价格紧跟物品名")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                            
                            ThirdVersionDemo()
                        }
                    }
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 直觉化设计原理
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🎯 直觉化设计原理")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            PrincipleRow(icon: "eye.fill", title: "视觉扫描路径", description: "从上到下：物品名 → 价格 → 其他信息")
                            PrincipleRow(icon: "brain.head.profile", title: "认知负荷", description: "减少信息搜索时间，提升理解效率")
                            PrincipleRow(icon: "target", title: "用户期望", description: "符合商品展示的通用认知模式")
                            PrincipleRow(icon: "speedometer", title: "决策效率", description: "快速获取核心信息，加速购买决策")
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    // 设计优势总结
                    VStack(alignment: .leading, spacing: 12) {
                        Text("✨ 设计优势")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            AdvantageRow(icon: "arrow.down", title: "自然流畅", description: "信息层次清晰，阅读路径顺畅")
                            AdvantageRow(icon: "clock.fill", title: "快速识别", description: "价格信息位置固定，快速定位")
                            AdvantageRow(icon: "checkmark.circle.fill", title: "符合习惯", description: "遵循电商和购物应用的通用布局")
                            AdvantageRow(icon: "rectangle.stack.fill", title: "信息分层", description: "核心信息优先，次要信息分离")
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("直觉化布局")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// 第一版布局演示
struct FirstVersionDemo: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("MacBook Pro")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Apple · M1 Pro")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 价格单独一行
            HStack {
                Text("¥14999")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            // 使用次数和值度分开
            HStack {
                Text("使用25次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Text("值度")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("85")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    Circle()
                        .fill(.green)
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
    }
}

// 第二版布局演示
struct SecondVersionDemo: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("MacBook Pro")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Apple · M1 Pro")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 使用次数
            HStack {
                Text("使用25次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 价格与值度在同一行
                HStack(spacing: 8) {
                    Text("¥14999")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("·")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        HStack(spacing: 2) {
                            Text("值度")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("85")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                        Circle()
                            .fill(.green)
                            .frame(width: 6, height: 6)
                    }
                }
            }
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
    }
}

// 第三版布局演示（当前版本）
struct ThirdVersionDemo: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("MacBook Pro")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("Apple · M1 Pro")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 价格紧跟物品名
            HStack {
                Text("¥14999")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 值度指数和置信度
                HStack(spacing: 4) {
                    HStack(spacing: 2) {
                        Text("值度")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("85")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                    Circle()
                        .fill(.green)
                        .frame(width: 6, height: 6)
                }
            }
            
            // 使用次数移到下方
            HStack {
                Text("使用25次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
        }
        .padding()
        .background(Color.green.opacity(0.1))
        .cornerRadius(8)
    }
}

// 设计原理行
struct PrincipleRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.purple)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// 优势说明行
struct AdvantageRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    IntuitiveLayoutPreview()
}